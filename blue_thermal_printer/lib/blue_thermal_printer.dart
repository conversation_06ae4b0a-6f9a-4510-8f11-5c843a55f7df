import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import 'package:blue_thermal_printer/network_printer_manager.dart';
import 'package:flutter/services.dart';
import 'package:thermal_printer/thermal_printer.dart';

import 'printer_commands.dart';

class BlueThermalPrinter {
  static const int STATE_OFF = 10;
  static const int STATE_TURNING_ON = 11;
  static const int STATE_ON = 12;
  static const int STATE_TURNING_OFF = 13;
  static const int STATE_BLE_TURNING_ON = 14;
  static const int STATE_BLE_ON = 15;
  static const int STATE_BLE_TURNING_OFF = 16;
  static const int ERROR = -1;
  static const int CONNECTED = 1;
  static const int DISCONNECTED = 0;
  static const int DISCONNECT_REQUESTED = 2;

  static const String namespace = 'blue_thermal_printer';
  // static const MethodChannel _channel1 = const MethodChannel('$namespace');
  static const MethodChannel _channel = const MethodChannel('$namespace/methods');

  static const EventChannel _readChannel = const EventChannel('$namespace/read');

  static const EventChannel _stateChannel = const EventChannel('$namespace/state');

  final StreamController<MethodCall> _methodStreamController = new StreamController.broadcast();

  //Stream<MethodCall> get _methodStream => _methodStreamController.stream;

  BlueThermalPrinter._() {
    _channel.setMethodCallHandler((MethodCall call) async {
      _methodStreamController.add(call);
    });
  }

  static BlueThermalPrinter _instance = new BlueThermalPrinter._();

  static BlueThermalPrinter get instance => _instance;

  /// Variables for network printer
  static Socket? networkPrinterSocket;
  String? networkPrinterIP;
  int networkPrinterPort = 9100;
  static String bluetooth = "bluetooth";
  static String network = "network";
  static String usb = "usb";
  static String printerType = "bluetooth"; // Default Bluetooth

  final usbPrinterConnector = UsbPrinterConnector.instance;
  StreamSubscription<USBStatus>? _subscriptionUsbStatus;

  Stream<USBStatus> get stateUSB => usbPrinterConnector.currentStatus.cast<USBStatus>();
  USBStatus get currentStatusUSB => usbPrinterConnector.status;

  ///onStateChanged()
  Stream<int?> onStateChanged() async* {
    yield await _channel.invokeMethod('state').then((buffer) => buffer);

    yield* _stateChannel.receiveBroadcastStream().map((buffer) => buffer);
  }

  ///onRead()
  Stream<String> onRead() => _readChannel.receiveBroadcastStream().map((buffer) => buffer.toString());

  Future<bool?> get isAvailable async => await _channel.invokeMethod('isAvailable');

  Future<bool?> get isOn async => await _channel.invokeMethod('isOn');

  Future<bool?> get isConnected async {
    if (printerType == bluetooth && !Platform.isWindows) {
      var bluetoothConnected = await _channel.invokeMethod('isConnected');
      return bluetoothConnected;
    } else if (printerType == usb) {
      return true;
    } else {
      if (networkPrinterSocket != null) {
        return true;
      } else {
        return false;
      }
    }
  }

  Future<bool?> get openSettings async => await _channel.invokeMethod('openSettings');

  ///getBondedDevices()
  Future<List<BluetoothDevice>> getBondedDevices({String type = "bluetooth"}) async {
    try {
      if (type == "bluetooth") {
        final List list = await (_channel.invokeMethod('getBondedDevices'));
        if (Platform.isAndroid) {
          return list.map((map) => BluetoothDevice.fromMap(map)).toList();
        } else if (Platform.isIOS) {
          List<BluetoothDevice> items = [];
          await Future.forEach(list, (element) {
            String item = element as String;
            List<String> info = item.split("#");
            String name = info[0];
            String mac = info[1];
            items.add(BluetoothDevice(name, mac));
          });
          return items;
        }
      } else if (type == "usb" && (Platform.isAndroid || Platform.isWindows)) {
        List<BluetoothDevice> items = [];
        final Completer<void> completer = Completer<void>();
        await usbPrinterConnector.discovery().listen((device) {
          items.add(BluetoothDevice(
            device.name,
            "",
            vendorId: device.vendorId,
            productId: device.productId,
            printerType: type,
          ));
        }, onDone: () {
          completer.complete(); // Signal that discovery is complete
        }, onError: (error) {
          completer.completeError(error); // Signal that an error occurred
        });
        await completer.future; // Wait for the discovery to complete
        return items;
      }
      return [];
    } on PlatformException catch (e) {
      print("Fail pairedBluetooths: '${e.message}'.");
      return [];
    }
  }

  ///isDeviceConnected(BluetoothDevice device)
  Future<bool?> isDeviceConnected(BluetoothDevice device) => _channel.invokeMethod('isDeviceConnected', device.toMap());

  ///connect(BluetoothDevice device)
  Future<dynamic> connect(BluetoothDevice device) async {
    try {
      if (printerType == bluetooth) {
        if (Platform.isAndroid) {
          _channel.invokeMethod('connect', device.toMap());
        } else {
          var result = await _channel.invokeMethod('connect', device.address);
          print("result status connect: $result");
        }
      } else if (printerType == usb && (Platform.isAndroid || Platform.isWindows)) {
        try {
          var conn = await usbPrinterConnector.connect(UsbPrinterInput(name: device.name, vendorId: device.vendorId, productId: device.productId));
          return conn;
        } catch (e) {
          throw Exception('model must be type of UsbPrinterInput');
        }
      }
    } catch (e) {
      print("Fail connect printers: '${e}'.");
      throw e;
    }
  }

  ///disconnect()
  Future<dynamic> disconnect() async {
    var isConnectedDevice = await isConnected;
    if (printerType == bluetooth) {
      if (isConnectedDevice == true) {
        return _channel.invokeMethod('disconnect');
      }
    } else {
      if (networkPrinterSocket != null) {
        networkPrinterSocket!.close();
        networkPrinterSocket = null;
      }
    }
  }

  ///write(String message)
  Future<dynamic> write(String message) => _channel.invokeMethod('write', {'message': message});

  ///writeBytes(Uint8List message)
  Future<dynamic> writeBytes(List<int> bytes) async {
    try {
      if (printerType == bluetooth) {
        if (Platform.isAndroid) {
          Uint8List bytesMessage = Uint8List.fromList(bytes);
          _channel.invokeMethod('writeBytes', {'message': bytesMessage});
        } else {
          final bool result = await _channel.invokeMethod('writeBytes', bytes);
          return result;
        }
      } else if (printerType == usb && (Platform.isWindows || Platform.isAndroid)) {
        final bool result = await usbPrinterConnector.send(bytes);
        return result;
      } else {
        if (networkPrinterSocket != null) {
          networkPrinterSocket!.add(bytes);
        }
      }
    } catch (e) {
      print(e);
    }
  }

  ///printCustom(String message, int size, int align,{String? charset})
  Future<dynamic> printCustom(String message, int size, int align, {String? charset}) async {
    try {
      if (printerType == bluetooth) {
        if (Platform.isAndroid) {
          _channel.invokeMethod('printCustom', {'message': message, 'size': size, 'align': align, 'charset': charset});
        } else {
          List<int> commandBytes = NetworkPrinterManager.printCustomBytes(message, size, align, charset);
          final bool result = await _channel.invokeMethod('writeBytes', commandBytes);
          return result;
        }
      } else {
        if (networkPrinterSocket != null) {
          List<int> commandBytes = NetworkPrinterManager.printCustomBytes(message, size, align, charset);
          networkPrinterSocket!.add(commandBytes);
        }
      }
    } catch (e) {
      print(e);
    }
  }

  ///printNewLine()
  Future<dynamic> printNewLine() async {
    if (printerType == bluetooth) {
      _channel.invokeMethod('printNewLine');
    } else {
      if (networkPrinterSocket != null) {
        networkPrinterSocket!.add(PrinterCommands.FEED_LINE);
      }
    }
  }

  ///paperCut()
  Future<dynamic> paperCut() async {
    if (printerType == bluetooth) {
      _channel.invokeMethod('paperCut');
    } else {
      if (networkPrinterSocket != null) {
        networkPrinterSocket!.add(PrinterCommands.FEED_PAPER_AND_CUT);
      }
    }
  }

  ///printImage(String pathImage)
  Future<dynamic> printImage(String pathImage) async {
    if (printerType == bluetooth) {
      if (Platform.isAndroid) {
        _channel.invokeMethod('printImage', {'pathImage': pathImage});
      } else {
        List<int> commandBytes = await NetworkPrinterManager.printImage(pathImage);
        final bool result = await _channel.invokeMethod('writeBytes', commandBytes);
        return result;
      }
    } else {
      if (networkPrinterSocket != null) {
        List<int> commandBytes = await NetworkPrinterManager.printImage(pathImage);
        networkPrinterSocket!.add(commandBytes);
        // List<int> commandBytes1 =
        //     await NetworkPrinterManager.printImage1(pathImage);
        // networkPrinterSocket!.add(commandBytes1);
        // List<int> commandBytes2 =
        //     await NetworkPrinterManager.printImage2(pathImage);
        // networkPrinterSocket!.add(commandBytes2);
      }
    }
  }

  ///printImageBytes(Uint8List bytes)
  Future<dynamic> printImageBytes(Uint8List bytes) => _channel.invokeMethod('printImageBytes', {'bytes': bytes});

  ///printQRcode(String textToQR, int width, int height, int align)
  Future<dynamic> printQRcode(String textToQR, int width, int height, int align) =>
      _channel.invokeMethod('printQRcode', {'textToQR': textToQR, 'width': width, 'height': height, 'align': align});

  ///printLeftRight(String string1, String string2, int size,{String? charset, String? format})
  Future<dynamic> printLeftRight(String string1, String string2, int size, {String? charset, String? format}) =>
      _channel.invokeMethod('printLeftRight', {'string1': string1, 'string2': string2, 'size': size, 'charset': charset, 'format': format});

  ///print3Column(String string1, String string2, String string3, int size,{String? charset, String? format})
  Future<dynamic> print3Column(String string1, String string2, String string3, int size, {String? charset, String? format}) => _channel
      .invokeMethod('print3Column', {'string1': string1, 'string2': string2, 'string3': string3, 'size': size, 'charset': charset, 'format': format});

  ///print4Column(String string1, String string2, String string3,String string4, int size,{String? charset, String? format})
  Future<dynamic> print4Column(String string1, String string2, String string3, String string4, int size, {String? charset, String? format}) =>
      _channel.invokeMethod('print4Column',
          {'string1': string1, 'string2': string2, 'string3': string3, 'string4': string4, 'size': size, 'charset': charset, 'format': format});

  /// WIFI PRINTER
  Future<dynamic> connectNetworkPrinter(String ip, int port) async {
    // await _channel
    //     .invokeMethod('connectNetworkPrinter', {'ip': ip, 'port': port});
    if (networkPrinterSocket == null) {
      networkPrinterSocket = await Socket.connect(ip, port);
    }
  }

  Future<void> setPrinterType(String printerType1) async {
    // await _channel.invokeMethod('setPrinterType', {'printerType': printerType});
    printerType = printerType1;
  }

  Future<void> printNetwork(String message, {String? charset}) async {
    await _channel.invokeMethod('printNetwork', {'message': message, 'charset': charset});
  }
}

class BluetoothDevice {
  String? name;
  String? address;
  int type = 0;
  bool connected = false;
  String? vendorId;
  String? productId;
  String? printerType;

  BluetoothDevice(this.name, this.address, {this.vendorId, this.productId, this.printerType});

  BluetoothDevice.fromMap(Map map)
      : name = map['name'],
        address = map['address'];

  Map<String, dynamic> toMap() => {
        'name': this.name,
        'address': this.address,
        'type': this.type,
        'connected': this.connected,
      };

  operator ==(Object other) {
    return other is BluetoothDevice && other.address == this.address;
  }

  @override
  int get hashCode => address.hashCode;
}

class PrinterDevice {
  String name;
  String operatingSystem = Platform.operatingSystem;
  String? vendorId;
  String? productId;
  String? address;

  PrinterDevice({required this.name, this.address, this.vendorId, this.productId});
}
