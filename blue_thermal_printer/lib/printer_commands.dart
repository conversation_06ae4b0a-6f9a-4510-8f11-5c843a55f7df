// ignore_for_file: non_constant_identifier_names

class PrinterCommands {
  static const int HT = 0x9;
  static const int LF = 0x0A;
  static const int CR = 0x0D;
  static const int ESC = 0x1B;
  static const int DLE = 0x10;
  static const int GS = 0x1D;
  static const int FS = 0x1C;
  static const int STX = 0x02;
  static const int US = 0x1F;
  static const int CAN = 0x18;
  static const int CLR = 0x0C;
  static const int EOT = 0x04;

  static final List<int> INIT = [27, 64];
  static final List<int> FEED_LINE = [10];

  static final List<int> SELECT_FONT_A = [20, 33, 0];

  static final List<int> SET_BAR_CODE_HEIGHT = [29, 104, 100];
  static final List<int> PRINT_BAR_CODE_1 = [29, 107, 2];
  static final List<int> SEND_NULL_BYTE = [0x00];

  static final List<int> SELECT_PRINT_SHEET = [0x1B, 0x63, 0x30, 0x02];
  static final List<int> FEED_PAPER_AND_CUT = [0x1D, 0x56, 66, 0x00];

  static final List<int> SELECT_CYRILLIC_CHARACTER_CODE_TABLE = [
    0x1B,
    0x74,
    0x11
  ];

  static final List<int> SELECT_BIT_IMAGE_MODE = [0x1B, 0x2A, 33, -128, 0];
  static final List<int> SET_LINE_SPACING_24 = [0x1B, 0x33, 24];
  static final List<int> SET_LINE_SPACING_30 = [0x1B, 0x33, 30];

  static final List<int> TRANSMIT_DLE_PRINTER_STATUS = [0x10, 0x04, 0x01];
  static final List<int> TRANSMIT_DLE_OFFLINE_PRINTER_STATUS = [
    0x10,
    0x04,
    0x02
  ];
  static final List<int> TRANSMIT_DLE_ERROR_STATUS = [0x10, 0x04, 0x03];
  static final List<int> TRANSMIT_DLE_ROLL_PAPER_SENSOR_STATUS = [
    0x10,
    0x04,
    0x04
  ];

  static final List<int> ESC_FONT_COLOR_DEFAULT = [0x1B, 0x72, 0x00];
  static final List<int> FS_FONT_ALIGN = [0x1C, 0x21, 1, 0x1B, 0x21, 1];
  static final List<int> ESC_ALIGN_LEFT = [0x1b, 0x61, 0x00];
  static final List<int> ESC_ALIGN_RIGHT = [0x1b, 0x61, 0x02];
  static final List<int> ESC_ALIGN_CENTER = [0x1b, 0x61, 0x01];
  static final List<int> ESC_CANCEL_BOLD = [0x1B, 0x45, 0];

  static final List<int> ESC_HORIZONTAL_CENTERS = [0x1B, 0x44, 20, 28, 00];
  static final List<int> ESC_CANCEL_HORIZONTAL_CENTERS = [0x1B, 0x44, 00];

  static final List<int> ESC_ENTER = [0x1B, 0x4A, 0x40];
  static final List<int> PRINTE_TEST = [0x1D, 0x28, 0x41];
}
