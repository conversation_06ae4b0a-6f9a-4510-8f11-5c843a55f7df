// // ignore_for_file: non_constant_identifier_names

// import 'dart:typed_data';
// import 'package:flutter/services.dart';
// import 'package:image/image.dart';

// class Utils {
//   static final List<int> UNICODE_TEXT = List<int>.from([
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//     0x23,
//   ]);

//   static final String hexStr = "0123456789ABCDEF";
//   static final List<String> binaryArray = [
//     "0000",
//     "0001",
//     "0010",
//     "0011",
//     "0100",
//     "0101",
//     "0110",
//     "0111",
//     "1000",
//     "1001",
//     "1010",
//     "1011",
//     "1100",
//     "1101",
//     "1110",
//     "1111"
//   ];

//   Uint8List decodeBitmap(Uint8List imageBytes) {
//     Image bmp = decodeImage(imageBytes);

//     if (bmp == null) {
//       print("Error decoding image");
//       return Uint8List(0);
//     }

//     int bmpWidth = bmp.width;
//     int bmpHeight = bmp.height;

//     List<String> list = [];
//     StringBuffer sb;

//     int bitLen = bmpWidth ~/ 8;
//     int zeroCount = bmpWidth % 8;

//     String zeroStr = "";
//     if (zeroCount > 0) {
//       bitLen = bmpWidth ~/ 8 + 1;
//       for (int i = 0; i < (8 - zeroCount); i++) {
//         zeroStr = zeroStr + "0";
//       }
//     }

//     for (int i = 0; i < bmpHeight; i++) {
//       sb = StringBuffer();
//       for (int j = 0; j < bmpWidth; j++) {
//         Rgba color = getPixel(j, i);

//         int r = color.r;
//         int g = color.g;
//         int b = color.b;

//         if (r > 160 && g > 160 && b > 160)
//           sb.write("0");
//         else
//           sb.write("1");
//       }
//       if (zeroCount > 0) {
//         sb.write(zeroStr);
//       }
//       list.add(sb.toString());
//     }

//     List<String> bmpHexList = binaryListToHexStringList(list);
//     String commandHexString = "1D763000";
//     String widthHexString =
//         (bmpWidth % 8 == 0 ? bmpWidth ~/ 8 : (bmpWidth ~/ 8 + 1))
//             .toRadixString(16);
//     if (widthHexString.length > 10) {
//       print("decodeBitmap error: width is too large");
//       return Uint8List(0);
//     } else if (widthHexString.length == 1) {
//       widthHexString = "0" + widthHexString;
//     }
//     widthHexString = widthHexString + "00";

//     String heightHexString = bmpHeight.toRadixString(16);
//     if (heightHexString.length > 10) {
//       print("decodeBitmap error: height is too large");
//       return Uint8List(0);
//     } else if (heightHexString.length == 1) {
//       heightHexString = "0" + heightHexString;
//     }
//     heightHexString = heightHexString + "00";

//     List<String> commandList = [];
//     commandList.add(commandHexString + widthHexString + heightHexString);
//     commandList.addAll(bmpHexList);

//     return hexList2Byte(commandList);
//   }

//   static List<String> binaryListToHexStringList(List<String> list) {
//     List<String> hexList = <String>[];
//     for (String binaryStr in list) {
//       StringBuffer sb = StringBuffer();
//       for (int i = 0; i < binaryStr.length; i += 8) {
//         String str = binaryStr.substring(i, i + 8);

//         String hexString = myBinaryStrToHexString(str);
//         sb.write(hexString);
//       }
//       hexList.add(sb.toString());
//     }
//     return hexList;
//   }

//   static String myBinaryStrToHexString(String binaryStr) {
//     String hex = "";
//     String f4 = binaryStr.substring(0, 4);
//     String b4 = binaryStr.substring(4, 8);
//     for (int i = 0; i < binaryArray.length; i++) {
//       if (f4 == binaryArray[i]) hex += hexStr.substring(i, i + 1);
//     }
//     for (int i = 0; i < binaryArray.length; i++) {
//       if (b4 == binaryArray[i]) hex += hexStr.substring(i, i + 1);
//     }
//     return hex;
//   }

//   static Uint8List hexList2Byte(List<String> list) {
//     List<Uint8List> commandList = <Uint8List>[];

//     for (String hexStr in list) {
//       commandList.add(hexStringToBytes(hexStr)!);
//     }
//     Uint8List bytes = sysCopy(commandList);
//     return bytes;
//   }

//   static Uint8List? hexStringToBytes(String hexString) {
//     if (hexString == null || hexString == "") {
//       return null;
//     }
//     hexString = hexString.toUpperCase();
//     int length = hexString.length ~/ 2;
//     List<String> hexChars = hexString.split('');
//     Uint8List d = Uint8List(length);
//     for (int i = 0; i < length; i++) {
//       int pos = i * 2;
//       d[i] = (charToByte(hexChars[pos]) << 4 | charToByte(hexChars[pos + 1]));
//     }
//     return d;
//   }

//   static Uint8List sysCopy(List<Uint8List> srcArrays) {
//     int len = 0;
//     for (Uint8List srcArray in srcArrays) {
//       len += srcArray.length;
//     }
//     Uint8List destArray = Uint8List(len);
//     int destLen = 0;
//     for (Uint8List srcArray in srcArrays) {
//       destArray.setRange(destLen, destLen + srcArray.length, srcArray);
//       destLen += srcArray.length;
//     }
//     return destArray;
//   }

//   static int charToByte(String c) {
//     return hexStr.indexOf(c);
//   }
// }

// class Bitmap {
//   final int width;
//   final int height;
//   final List<List<int>> pixels;

//   Bitmap(this.width, this.height, this.pixels);

//   int getPixel(int x, int y) {
//     if (x >= 0 && x < width && y >= 0 && y < height) {
//       return pixels[y][x];
//     }
//     return 0;
//   }
// }
