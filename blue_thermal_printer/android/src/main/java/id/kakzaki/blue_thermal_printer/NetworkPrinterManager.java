package id.kakzaki.blue_thermal_printer;

import android.os.AsyncTask;
import android.util.Log;

import java.io.OutputStream;
import java.net.Socket;

public class NetworkPrinterManager {
    public String printerIP;
    public int printerPort;
    public Socket socket = null;

    public NetworkPrinterManager(String ip, int port) {
        this.printerIP = ip;
        this.printerPort = port;
    }

    public void sendDataInBackground(byte[] data) {
        sendDataInBackground(data, false); // Default value is set to false
    }

    public void sendDataInBackground(byte[] data, Boolean isLastPrint) {
        new SendDataAsyncTask(isLastPrint).execute(data);
    }

    private class SendDataAsyncTask extends AsyncTask<byte[], Void, Boolean> {
        private Boolean isLastPrint;

        public SendDataAsyncTask(Boolean isLastPrint) {
            this.isLastPrint = isLastPrint;
        }

        @Override
        protected Boolean doInBackground(byte[]... params) {
            byte[] data = params[0];
            try {
                Log.i("NETWORKPRINTERMANAGER", printerIP);
                if (socket == null) {
                    socket = new Socket(printerIP, printerPort);
                }

                OutputStream os = socket.getOutputStream();
                os.write(data);
                os.flush();
                if (isLastPrint) {
                    socket.close();
                }

                return true;
            } catch (Exception e) {
                Log.e("NETWORKPRINTERMANAGER", "e.getMessage()", e);
                e.printStackTrace();
                return false;
            }
        }

        @Override
        protected void onPostExecute(Boolean result) {
            // You can handle the result here if needed
        }
    }
}
