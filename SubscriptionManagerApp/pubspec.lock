# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  args:
    dependency: transitive
    description:
      name: args
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.1"
  async:
    dependency: transitive
    description:
      name: async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.8.1"
  barcode_scan2:
    dependency: "direct main"
    description:
      name: barcode_scan2
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  characters:
    dependency: transitive
    description:
      name: characters
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  charcode:
    dependency: transitive
    description:
      name: charcode
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.1"
  clock:
    dependency: transitive
    description:
      name: clock
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  collection:
    dependency: transitive
    description:
      name: collection
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.15.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.0"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.2"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.0"
  dbus:
    dependency: transitive
    description:
      name: dbus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.8"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.2.3"
  device_info_plus_linux:
    dependency: transitive
    description:
      name: device_info_plus_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  device_info_plus_macos:
    dependency: transitive
    description:
      name: device_info_plus_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.2.3"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.0+1"
  device_info_plus_web:
    dependency: transitive
    description:
      name: device_info_plus_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  device_info_plus_windows:
    dependency: transitive
    description:
      name: device_info_plus_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  ffi:
    dependency: transitive
    description:
      name: ffi
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1"
  file:
    dependency: transitive
    description:
      name: file
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.2"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.1"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      url: "https://pub.dartlang.org"
    source: hosted
    version: "8.0.9"
  http:
    dependency: "direct main"
    description:
      name: http
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.13.4"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  intl:
    dependency: "direct main"
    description:
      name: intl
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.17.0"
  js:
    dependency: transitive
    description:
      name: js
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.6.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.12.10"
  meta:
    dependency: transitive
    description:
      name: meta
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.7.0"
  mime:
    dependency: transitive
    description:
      name: mime
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.0.2"
  mobx:
    dependency: "direct main"
    description:
      name: mobx
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.1+4"
  nb_utils:
    dependency: "direct main"
    description:
      name: nb_utils
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.12"
  nm:
    dependency: transitive
    description:
      name: nm
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.4"
  path:
    dependency: transitive
    description:
      name: path
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.0"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.6"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.6"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.4.0"
  platform:
    dependency: transitive
    description:
      name: platform
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.2"
  process:
    dependency: transitive
    description:
      name: process
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.2.4"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.1"
  qr:
    dependency: transitive
    description:
      name: qr
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      url: "https://pub.dartlang.org"
    source: hosted
    version: "4.0.0"
  share:
    dependency: "direct main"
    description:
      name: share
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.15"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.10"
  shared_preferences_ios:
    dependency: transitive
    description:
      name: shared_preferences_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.9"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  shared_preferences_macos:
    dependency: transitive
    description:
      name: shared_preferences_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.0"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.4"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.8.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.10.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  string_encryption:
    dependency: "direct main"
    description:
      name: string_encryption
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.2.0"
  test_api:
    dependency: transitive
    description:
      name: test_api
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.4.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      url: "https://pub.dartlang.org"
    source: hosted
    version: "1.3.0"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.1.2"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.14"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      url: "https://pub.dartlang.org"
    source: hosted
    version: "6.0.14"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.5"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.0.7"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      url: "https://pub.dartlang.org"
    source: hosted
    version: "3.0.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.1.0"
  win32:
    dependency: transitive
    description:
      name: win32
      url: "https://pub.dartlang.org"
    source: hosted
    version: "2.3.6"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      url: "https://pub.dartlang.org"
    source: hosted
    version: "0.2.0+1"
  xml:
    dependency: transitive
    description:
      name: xml
      url: "https://pub.dartlang.org"
    source: hosted
    version: "5.3.1"
sdks:
  dart: ">=2.14.0 <3.0.0"
  flutter: ">=2.5.0"
