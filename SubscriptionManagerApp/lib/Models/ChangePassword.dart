// To parse this JSON data, do
//
//     final changePassword = changePasswordFromJson(jsonString);

import 'dart:convert';

Passwords changePasswordFromJson(String str) => Passwords.fromJson(json.decode(str));

String changePasswordToMap(Passwords data) => json.encode(data.toMap());

class Passwords {
  Passwords({
    this.oldPassword,
    this.newPassword,
    this.status,
    this.message,
  });

  String oldPassword;
  String newPassword;
  int status;
  String message;


  factory Passwords.fromJson(Map<String, dynamic> json) => Passwords(
    status: json["status"],
    message: json["message"],
  );

  Map<String, dynamic> toMap() => {
    "oldPassword": oldPassword,
    "newPassword": newPassword,
  };
}
