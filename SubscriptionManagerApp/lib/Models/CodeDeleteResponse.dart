// To parse this JSON data, do
//
//     final deleteCodeResponse = deleteCodeResponseFrom<PERSON>son(jsonString);

import 'dart:convert';

DeleteCodeResponse deleteCodeResponseFromJson(String str) =>
    DeleteCodeResponse.fromJson(json.decode(str));

String deleteCodeResponseToJson(DeleteCodeResponse data) =>
    json.encode(data.toJson());

class DeleteCodeResponse {
  DeleteCodeResponse({
    this.status,
    this.message,
  });

  int status;
  String message;

  factory DeleteCodeResponse.fromJson(Map<String, dynamic> json) =>
      DeleteCodeResponse(
        status: json["status"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
      };
}
