// To parse this JSON data, do
//
//     final viewUsers = viewUsersFromJson(jsonString);

import 'dart:convert';

ViewUsers viewUsersFromJson(String str) => ViewUsers.fromJson(json.decode(str));

String viewUsersToJson(ViewUsers data) => json.encode(data.toJson());

class ViewUsers {
  ViewUsers({
    this.status,
    this.response,
  });

  int status;
  String response;

  factory ViewUsers.fromJson(Map<String, dynamic> json) => ViewUsers(
        status: json["status"] == null ? null : json["status"],
        response: json["response"] == null ? null : json["response"],
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "response": response == null ? null : response,
      };
}
