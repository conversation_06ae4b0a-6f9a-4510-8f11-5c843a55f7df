// To parse this JSON data, do
//
//     final getOfflineActivationKeyCode = getOfflineActivationKeyCodeFromJson(jsonString);

import 'dart:convert';

GetOfflineActivationKeyCode getOfflineActivationKeyCodeFromJson(String str) =>
    GetOfflineActivationKeyCode.fromJson(json.decode(str));

String getOfflineActivationKeyCodeToJson(GetOfflineActivationKeyCode data) =>
    json.encode(data.toJson());

class GetOfflineActivationKeyCode {
  GetOfflineActivationKeyCode({this.status, this.result, this.message});

  int status;
  String message;
  Result result;

  factory GetOfflineActivationKeyCode.fromJson(Map<String, dynamic> json) =>
      GetOfflineActivationKeyCode(
        status: json["status"],
        message: json["message"],
        result: Result.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "result": result.toJson(),
      };
}

class Result {
  Result({
    this.id,
    this.code,
  });

  int id;
  String code;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"],
        code: json["code"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "code": code,
      };
}
