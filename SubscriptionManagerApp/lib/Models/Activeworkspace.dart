class Active {
  Active({
    this.status,
    this.message,
    this.result,
  });

  int status;
  String message;
  Extendvalidity result;

  factory Active.fromJson(Map<String, dynamic> json) => Active(
    status: json["status"],
    message: json["message"],
    result: json["result"] == null
        ? null
        : Extendvalidity.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = Map<String, dynamic>();
    data['status'] = status;
    data['message'] = message;
    data['result'] = result;

    return data;
  }
}

class Extendvalidity{

  String workspaceID;
  String message;
  String expiryDate;
  int status;

  Extendvalidity({
    this.workspaceID,
    this.message,
    this.expiryDate,
    this.status,
  });
  factory Extendvalidity.fromJson(Map<String, dynamic> json) => Extendvalidity(
    workspaceID: json["workspaceID"],
      message: json["message"],
      expiryDate: json["expiryDate"],
      status: json["status"]

  );

  Map<String, dynamic> toMap() => {
    "workspaceID": workspaceID,
    "message": message,
    "expiryDate": expiryDate,
    "status": status

  };

}

class ActiveWorkspace{
int applicationID;
String workspaceID;
int validityDays;

ActiveWorkspace({
  this.applicationID,
  this.workspaceID,
  this.validityDays
});
factory ActiveWorkspace.fromJson(Map<String, dynamic> json) => ActiveWorkspace(
  applicationID: json["applicationID"],
  workspaceID: json["workspaceID"],
  validityDays: json["validityDays"],

);

Map<String, dynamic> toMap() => {
  "applicationID": applicationID,
  "workspaceID": workspaceID,
  "validityDays": validityDays,

};

}

