// To parse this JSON data, do
//
//     final offlineActivationCode = offlineActivationCodeFromJson(jsonString);

import 'dart:convert';

OfflineActivationCode offlineActivationCodeFromJson(String str) =>
    OfflineActivationCode.fromJson(json.decode(str));

String offlineActivationCodeToMap(OfflineActivationCode data) =>
    json.encode(data.toMap());

class OfflineActivationCode {
  OfflineActivationCode({
    this.activationKey,
    this.validity,
    this.applicationId,
    this.code,
    this.status,
    this.message,
    this.id,
  });

  String activationKey;
  int validity;
  int applicationId;
  String code;

  int status;
  String message;
  int id;

  factory OfflineActivationCode.fromJson(Map<String, dynamic> json) =>
      OfflineActivationCode(
        activationKey: json["activationKey"],
        validity: json["validity"],
        applicationId: json["applicationID"],
        status: json["status"],
        message: json["message"],
        id: json["id"],
      );

  Map<String, dynamic> toMap() => {
        "activationKey": activationKey,
        "validity": validity,
        "applicationID": applicationId,
        "code": code,
      };
}
