// To parse this JSON data, do
//
//     final getCouponCodes = getCouponCodesFromJson(jsonString);

import 'dart:convert';

GetCouponCodes getCouponCodesFromJson(String str) =>
    GetCouponCodes.fromJson(json.decode(str));

String getCouponCodesToJson(GetCouponCodes data) => json.encode(data.toJson());

class GetCouponCodes {
  GetCouponCodes({
    this.status,
    this.result,
  });

  int status;
  List<Result> result;

  factory GetCouponCodes.fromJson(Map<String, dynamic> json) => GetCouponCodes(
        status: json["status"] == null ? null : json["status"],
        result: json["result"] == null
            ? null
            : List<Result>.from(json["result"].map((x) => Result.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "result": result == null
            ? null
            : List<dynamic>.from(result.map((x) => x.toJson())),
      };
}

class Result {
  Result({
    this.id,
    this.activationCode,
    this.validityDays,
    this.createdBy,
    this.createdOn,
    this.activatedOn,
    this.isActive,
    this.applicationId,
    this.vendorId,
  });

  int id;
  String activationCode;
  int validityDays;
  int createdBy;
  DateTime createdOn;
  DateTime activatedOn;
  bool isActive;
  int applicationId;
  int vendorId;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"] == null ? null : json["id"],
        activationCode:
            json["activationCode"] == null ? null : json["activationCode"],
        validityDays:
            json["validityDays"] == null ? null : json["validityDays"],
        createdBy: json["createdBy"] == null ? null : json["createdBy"],
        createdOn: json["createdOn"] == null
            ? null
            : DateTime.parse(json["createdOn"]),
        activatedOn: json["activatedOn"] == null
            ? null
            : DateTime.parse(json["activatedOn"]),
        isActive: json["isActive"] == null ? null : json["isActive"],
        applicationId:
            json["applicationID"] == null ? null : json["applicationID"],
        vendorId: json["vendorID"] == null ? null : json["vendorID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "activationCode": activationCode == null ? null : activationCode,
        "validityDays": validityDays == null ? null : validityDays,
        "createdBy": createdBy == null ? null : createdBy,
        "createdOn": createdOn == null ? null : createdOn.toIso8601String(),
        "activatedOn":
            activatedOn == null ? null : activatedOn.toIso8601String(),
        "isActive": isActive == null ? null : isActive,
        "applicationID": applicationId == null ? null : applicationId,
        "vendorID": vendorId == null ? null : vendorId,
      };
}
