class Workspacelist {
  int status;
  String message;
  List<WorkspaceIdlist> result;

  Workspacelist({this.status, this.message, this.result});

  factory Workspacelist.fromJson(Map<String, dynamic> json) => Workspacelist(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null ? null : List<WorkspaceIdlist>.from(json["result"].map((x) => WorkspaceIdlist.fromJson(x))),
      );
}

class WorkspaceIdlist {
  String workspaceId;
  String workspaceName;
  String expiryDate;
  int expiryDays;
  bool isActive;

  WorkspaceIdlist({
    this.workspaceId,
    this.workspaceName,
    this.expiryDate,
    this.expiryDays,
    this.isActive,
  });

  factory WorkspaceIdlist.fromJson(Map<String, dynamic> json) => WorkspaceIdlist(
      workspaceId: json["workspaceId"], workspaceName: json["workspaceName"], expiryDays: json["expiryDays"], expiryDate: json["expiryDate"], isActive: json["isActive"]);
}
