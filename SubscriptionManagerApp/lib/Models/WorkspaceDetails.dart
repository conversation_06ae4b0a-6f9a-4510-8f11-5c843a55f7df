class Workspace{
  int status;
  String message;
  WorkspaceDetails result;

  Workspace({this.status, this.message, this.result});

  factory Workspace.fromJson(Map<String, dynamic> json) => Workspace(
    status: json["status"],
    message: json["message"],
    result: json["result"] == null
        ? null
        : WorkspaceDetails.fromJson(json["result"]),
  );

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['result'] = result;

    return data;
  }


}

class WorkspaceDetails{
  String workspaceId;
  int userID;
  String firstName;
  String lastName;
  String mobile;
  String shopName;
  String expiryDate;
  bool  isActive;

  WorkspaceDetails(
      {
        this.workspaceId,
        this.userID,
        this.firstName,
        this.lastName,
        this.mobile,
        this.shopName,
        this.expiryDate,
        this.isActive});



  factory WorkspaceDetails.fromJson(Map<String, dynamic> json) =>
      WorkspaceDetails(
        workspaceId: json["workspaceId"],
        userID: json["userID"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        mobile: json["mobile"],
        shopName: json["shopName"],
        expiryDate: json["expiryDate"],
        isActive: json["isActive"],
      );
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceId'] = workspaceId;
    data['userID'] = userID;
    data['firstName'] = firstName;
    data['lastName'] = lastName;
    data['mobile'] = mobile;
    data['shopName'] = shopName;
    data['expiryDate'] = expiryDate;
    data['isActive'] = isActive;

    return data;
  }
}