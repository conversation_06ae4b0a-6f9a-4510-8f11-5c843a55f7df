// To parse this JSON data, do
//
//     final getOfflineActivationCode = getOfflineActivationCodeFromJson(jsonString);

import 'dart:convert';

GetOfflineActivationCode getOfflineActivationCodeFromJson(String str) =>
    GetOfflineActivationCode.fromJson(json.decode(str));

String getOfflineActivationCodeToJson(GetOfflineActivationCode data) =>
    json.encode(data.toJson());

class GetOfflineActivationCode {
  GetOfflineActivationCode({
    this.status,
    this.result,
  });

  int status;
  List<OfflineCodesResult> result;

  factory GetOfflineActivationCode.fromJson(Map<String, dynamic> json) =>
      GetOfflineActivationCode(
        status: json["status"],
        result: List<OfflineCodesResult>.from(
            json["result"].map((x) => OfflineCodesResult.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "result": List<dynamic>.from(result.map((x) => x.toJson())),
      };
}

class OfflineCodesResult {
  OfflineCodesResult({
    this.id,
    this.activationKey,
    this.validityDays,
    this.createdBy,
    this.createdOn,
    this.activatedOn,
    this.isActive,
    this.applicationId,
    this.vendorId,
    this.deviceId,
  });

  int id;
  String activationKey;
  int validityDays;
  int createdBy;
  DateTime createdOn;
  dynamic activatedOn;
  bool isActive;
  int applicationId;
  int vendorId;
  int deviceId;

  factory OfflineCodesResult.fromJson(Map<String, dynamic> json) =>
      OfflineCodesResult(
        id: json["id"],
        activationKey: json["activationKey"],
        validityDays: json["validityDays"],
        createdBy: json["createdBy"],
        createdOn: DateTime.parse(json["createdOn"]),
        activatedOn: json["activatedOn"],
        isActive: json["isActive"],
        applicationId: json["applicationID"],
        vendorId: json["vendorID"],
        deviceId: json["deviceID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "activationKey": activationKey,
        "validityDays": validityDays,
        "createdBy": createdBy,
        "createdOn": createdOn.toIso8601String(),
        "activatedOn": activatedOn,
        "isActive": isActive,
        "applicationID": applicationId,
        "vendorID": vendorId,
        "deviceID": deviceId,
      };
}
