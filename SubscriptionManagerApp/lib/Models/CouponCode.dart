// To parse this JSON data, do
//
//     final couponCode = couponCodeFromJson(jsonString);

import 'dart:convert';

CouponCode couponCodeFromJson(String str) =>
    CouponCode.fromJson(json.decode(str));

String couponCodeToMap(CouponCode data) => json.encode(data.toMap());

class CouponCode {
  CouponCode({
    this.status,
    this.message,
    this.result,
  });

  int status;
  String message;
  Result result;
  GenerateCouponRequest request;

  factory CouponCode.fromJson(Map<String, dynamic> json) => CouponCode(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
      );

  Map<String, dynamic> toMap() => {
        "request": request == null ? null : request.toMap(),
      };
}

class Result {
  Result({
    this.id,
    this.activationCode,
    this.validityDays,
    this.createdBy,
    this.createdOn,
    this.activatedOn,
    this.isActive,
    this.applicationId,
    this.vendorId,
  });

  int id;
  String activationCode;
  int validityDays;
  int createdBy;
  DateTime createdOn;
  dynamic activatedOn;
  bool isActive;
  int applicationId;
  int vendorId;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"] == null ? null : json["id"],
        activationCode:
            json["activationCode"] == null ? null : json["activationCode"],
        validityDays:
            json["validityDays"] == null ? null : json["validityDays"],
        createdBy: json["createdBy"] == null ? null : json["createdBy"],
        createdOn: json["createdOn"] == null
            ? null
            : DateTime.parse(json["createdOn"]),
        activatedOn: json["activatedOn"],
        isActive: json["isActive"] == null ? null : json["isActive"],
        applicationId:
            json["applicationID"] == null ? null : json["applicationID"],
        vendorId: json["vendorID"] == null ? null : json["vendorID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "activationCode": activationCode == null ? null : activationCode,
        "validityDays": validityDays == null ? null : validityDays,
        "createdBy": createdBy == null ? null : createdBy,
        "createdOn": createdOn == null ? null : createdOn.toIso8601String(),
        "activatedOn": activatedOn,
        "isActive": isActive == null ? null : isActive,
        "applicationID": applicationId == null ? null : applicationId,
        "vendorID": vendorId == null ? null : vendorId,
      };
}

class GenerateCouponRequest {
  GenerateCouponRequest({
    this.validityDays,
    this.applicationId,
  });

  int validityDays;
  int applicationId;

  factory GenerateCouponRequest.fromJson(Map<String, dynamic> json) =>
      GenerateCouponRequest(
        validityDays:
            json["validityDays"] == null ? null : json["validityDays"],
        applicationId:
            json["applicationID"] == null ? null : json["applicationID"],
      );

  Map<String, dynamic> toMap() => {
        "validityDays": validityDays == null ? null : validityDays,
        "applicationID": applicationId == null ? null : applicationId,
      };
}
