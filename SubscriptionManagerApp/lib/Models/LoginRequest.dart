// To parse this JSON data, do
//
//     final loginRequestModel = loginRequestModelFromMap(jsonString);

import 'dart:convert';

LoginRequestModel loginRequestModelFromMap(String str) => LoginRequestModel.fromMap(json.decode(str));

String loginRequestModelToMap(LoginRequestModel data) => json.encode(data.toMap());

class LoginRequestModel {
  LoginRequestModel({
    this.username,
    this.password,
    this.mobileId,
    this.applicationId,
  });

  String username;
  String password;
  String mobileId;
  int applicationId;

  factory LoginRequestModel.fromMap(Map<String, dynamic> json) => LoginRequestModel(
    username: json["username"],
    password: json["password"],
    mobileId: json["mobileID"],
    applicationId: json["applicationID"],
  );

  Map<String, dynamic> toMap() => {
    "username": username,
    "password": password,
    "mobileID": mobileId,
    "applicationID": applicationId,
  };
}
