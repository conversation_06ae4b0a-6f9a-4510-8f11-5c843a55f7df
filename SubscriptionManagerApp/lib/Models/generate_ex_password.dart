// To parse this JSON data, do
//
//     final generateExPassword = generateExPasswordFromJson(jsonString);

import 'dart:convert';

GenerateExPassword generateExPasswordFromJson(String str) =>
    GenerateExPassword.fromJson(json.decode(str));

String generateExPasswordToJson(GenerateExPassword data) =>
    json.encode(data.toJson());

class GenerateExPassword {
  GenerateExPassword({
    this.status,
    this.message,
    this.result,
  });

  int status;
  String message;
  Password result;

  factory GenerateExPassword.fromJson(Map<String, dynamic> json) =>
      GenerateExPassword(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result:
            json["result"] == null ? null : Password.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null ? null : result.toJson(),
      };
}

class Password {
  Password({
    this.id,
    this.keyName,
    this.keyValue,
  });

  int id;
  String keyName;
  String keyValue;

  factory Password.fromJson(Map<String, dynamic> json) => Password(
        id: json["id"] == null ? null : json["id"],
        keyName: json["keyName"] == null ? null : json["keyName"],
        keyValue: json["keyValue"] == null ? null : json["keyValue"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "keyName": keyName == null ? null : keyName,
        "keyValue": keyValue == null ? null : keyValue,
      };
}
