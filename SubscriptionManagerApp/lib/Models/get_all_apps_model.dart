// To parse this JSON data, do
//
//     final getAllApps = getAllAppsFromJson(jsonString);

import 'dart:convert';

GetAllApps getAllAppsFromJson(String str) =>
    GetAllApps.fromJson(json.decode(str));

String getAllAppsToJson(GetAllApps data) => json.encode(data.toJson());

class GetAllApps {
  GetAllApps({
    this.status,
    this.applications,
  });

  int status;
  List<Applications> applications;

  factory GetAllApps.fromJson(Map<String, dynamic> json) => GetAllApps(
        status: json["status"] == null ? null : json["status"],
        applications: json["response"] == null
            ? null
            : List<Applications>.from(
                json["response"].map((x) => Applications.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "response": applications == null
            ? null
            : List<dynamic>.from(applications.map((x) => x.toJson())),
      };
}

class Applications {
  Applications({
    this.id,
    this.applicationName,
  });

  int id;
  String applicationName;

  factory Applications.fromJson(Map<String, dynamic> json) => Applications(
        id: json["id"] == null ? null : json["id"],
        applicationName:
            json["applicationName"] == null ? null : json["applicationName"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "applicationName": applicationName == null ? null : applicationName,
      };
}
