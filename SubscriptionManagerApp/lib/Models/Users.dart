import 'dart:convert';

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToMap(User data) => json.encode(data.toMap());

class User {
  User({
    this.status,
    this.message,
    this.result,
  });

  int status;
  String message;
  Result result;

  factory User.fromJson(Map<String, dynamic> json) => User(
        status: json["status"],
        message: json["message"],
        result: Result.fromJson(json["result"]),
      );

  Map<String, dynamic> toMap() => {
        "status": status,
        "message": message,
        "result": result.toMap(),
      };
}

class Result {
  Result({
    this.id,
    this.username,
    this.firstName,
    this.lastName,
    this.mobile,
    this.token,
  });

  int id;
  String username;
  String firstName;
  String lastName;
  String mobile;
  String token;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"],
        username: json["username"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        mobile: json["mobile"],
        token: json["token"],
      );

  Map<String, dynamic> toMap() => {
        "id": id,
        "username": username,
        "firstName": firstName,
        "lastName": lastName,
        "mobile": mobile,
        "token": token,
      };
}
