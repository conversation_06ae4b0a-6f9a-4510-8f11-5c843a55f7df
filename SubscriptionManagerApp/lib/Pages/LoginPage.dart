import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:subscriptionmanager/Pages/HomePage.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/ValidationUtility.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/repository/UserRepository.dart';
import '../Models/LoginRequest.dart';
import '../Utilities/T5Colors.dart';
import '../main.dart';

class LoginPage extends StatefulWidget {
  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  HelperClass helperClass = new HelperClass();
  Validator validator = new Validator();
  double width;
  double height;
  final userNameController = TextEditingController();
  final passwordController = TextEditingController();
  @override
  void initState() {
    Future.delayed(Duration.zero, () {
      helperClass.getId(context).then((id) {
        setState(() {
          MyApp.deviceId = id;
          //toast(MyApp.deviceId);
        });
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      appBar: AppBar(
        title: Text("Subscription Manager"),
      ),
      body: Container(
        width: width,
        height: height,
        padding: EdgeInsets.all(5.0),
        //color: Colors.blue,
        child: Form(
          key: validator.formkey,
          child: Center(
            child: Container(
              //color: Colors.blue,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  SizedBox(
                    height: 20,
                  ),
                  Container(
                    child: text("Login",
                        textColor: appStore.textPrimaryColor,
                        fontFamily: fontBold,
                        fontSize: 22.0),
                    alignment: Alignment.center,
                  ),
                  SizedBox(height: 40),
                  EditText(
                    text: "Username",
                    isPassword: false,
                    mController: userNameController,
                    inputType: TextInputType.text,
                    validator: validator.validateName,
                  ),
                  SizedBox(height: 16),
                  EditText(
                    text: "Password",
                    isSecure: true,
                    isPassword: true,
                    mController: passwordController,
                    validator: validator.validatePassword,
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            LoginRequestModel loginRequest =
                                new LoginRequestModel();
                            loginRequest.username = userNameController.text;
                            loginRequest.password = passwordController.text;
                            loginRequest.applicationId = MyApp.appId;
                            loginRequest.mobileId = MyApp.deviceId;
                            validator.validate();
                            if (validator.validate()) {
                              helperClass.onLoading(context);
                              login(loginRequest).then((value) async {
                                helperClass.onStopping();
                                if (value.status == 1) {
                                  userNameController.clear();
                                  passwordController.clear();
                                  MyApp.isAuthorized = true;
                                  MyApp.token = value.result.token;
                                  MyApp.userName = value.result.username;
                                  SharedPreferences prefs =
                                      await SharedPreferences.getInstance();
                                  await prefs.setString("vendorDetails",
                                      json.encode(value.toMap()));
                                  //prefs.setString("token", MyApp.token);
                                  Navigator.pop(context);
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => HomePage()));
                                } else {
                                  toast(value.message);
                                }
                              });
                            }
                          },
                          child: Container(
                            //margin: EdgeInsets.only(right: 16),
                            alignment: Alignment.center,
                            height: width / 8,
                            child: text("Sign In",
                                textColor: t5White, isCentered: true),
                            decoration:
                                boxDecoration(bgColor: Colors.green, radius: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
