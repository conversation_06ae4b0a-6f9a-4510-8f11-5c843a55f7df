// ignore_for_file: unnecessary_brace_in_string_interps

import 'dart:math';
import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share/share.dart';
import 'package:string_encryption/string_encryption.dart';
import 'package:subscriptionmanager/Models/OfflineActivationCodes.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/T5Colors.dart';
import 'package:subscriptionmanager/Utilities/ValidationUtility.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/repository/UserRepository.dart';

import '../Models/get_all_apps_model.dart';
import '../main.dart';

class ActivationPage extends StatefulWidget {
  @override
  _ActivationPageState createState() => _ActivationPageState();
}

class _ActivationPageState extends State<ActivationPage> {
  double width;
  double height;
  String generatedKey = "";
  Validator validator = new Validator();
  HelperClass helperClass = new HelperClass();
  var now = DateTime.now();
  final daysController = TextEditingController();
  final deviceController = TextEditingController();
  Applications selectedApplication;
  String scanQrCode = '';
  String appId;
  String encryptionText = '';
  var key = "";
  String encryptedS, decryptedS;
  // PlatformStringCryptor cryptor;
  String keyTempCode;
  int keyId;

  Future scan() async {
    try {
      var barcode = await BarcodeScanner.scan();
      setState(() {
        this.scanQrCode = barcode.rawContent;
        deviceController.text = scanQrCode;
        key = scanQrCode;
      });
    } on PlatformException catch (e) {
      if (e.code == BarcodeScanner.cameraAccessDenied) {
        setState(() {
          toast('The user did not grant the camera permission!');
        });
      } else {
        toast('Unknown error: $e');
      }
    } on FormatException {
      setState(() => toast(
          'null (User returned using the "back"-button before scanning anything. Result)'));
    } catch (e) {
      setState(() => toast('Unknown error: $e'));
    }
  }

  void encrypt() async {
    final cryptor = StringEncryption();
    final salt =
        'UJ9m9IsEDABZBg9u9gtnsu5s48AAaUfRifOnH1FcPcrM65tLv5wqsnQrE/7oX39Xh2sJHa49CDzyPdJejknuTr/H/8R3V8mfSHdngZcTPyGOGlpphMN2C41LUmc8VA2hRV/y+gBQykCU/3B2zWUYKJfLjvELplwf8PVNpx7HQW4='; //await cryptor.generateSalt();
    String device = deviceController.text;
    key = await cryptor.generateKeyFromPassword(device, salt);
    //print('00${salt}');
    //key = await cryptor.fr
    print(key);
    // here pass the password entered by user and the key
    encryptedS = await cryptor.encrypt(encryptionText, key);
    print(encryptedS);
    setState(() {
      encryptionText = "";
      // generatedKey = encryptedS;
      storeOfflineActivationKeyinApi();
    });
  }

  storeOfflineActivationKeyinApi() {
    OfflineActivationCode offlineActivationCode = new OfflineActivationCode();
    offlineActivationCode.applicationId = MyApp.appId;
    offlineActivationCode.activationKey = encryptedS;
    offlineActivationCode.validity = int.parse(daysController.text);
    offlineActivationCode.code = keyTempCode;
    createOfflineActivationKey(offlineActivationCode).then((value) {
      toast(value.message);
      if (MyApp.isAuthorized) {
        helperClass.onStopping();
        setState(() {
          generatedKey = encryptedS;
          keyId = value.id;
        });
      } else {
        helperClass.logout(context);
      }
    });
  }

  generateRandomCode() {
    var rng = new Random();
    keyTempCode = (rng.nextInt(900000) + 100000).toString();
  }

  createAlertDialogGetActivateCode(BuildContext buildContext, String tempCode) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Container(
            height: height / 2,
            child: AlertDialog(
              content: text("$tempCode",
                  textColor: appStore.textPrimaryColor,
                  fontFamily: fontBold,
                  fontSize: textSizeNormal,
                  isCentered: true),
              actions: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: text("Ok", textColor: t5White, isCentered: true),
                    decoration:
                        boxDecoration(bgColor: Colors.green, radius: 8.0),
                  ),
                ),
              ],
            ),
          );
        });
  }

// method to decrypt String Password
  @override
  void initState() {
    setState(() {
      selectedApplication = MyApp.applications[0];
      appId = '${selectedApplication.id}';
    });
    // toast(generateRandomCode().toString());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        title: Text("Offline Activation"),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                daysController.clear();
                deviceController.clear();
                generatedKey = "";
              });
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          width: width,
          height: height,
          child: SingleChildScrollView(
            child: Form(
              key: validator.formkey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text(
                        "Application Name",
                        textColor: t5TextColorPrimary,
                        isCentered: true,
                        fontSize: textSizeMedium,
                      ),
                      //Text("Application Name :"),
                      Container(
                        width: width / 1.8,
                        padding: EdgeInsets.all(5.0),
                        height: 40,
                        decoration: BoxDecoration(
                          // color: const Color(0xff7c94b6),
                          border: Border.all(
                            color: Colors.black,
                            width: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: DropdownButton(
                          value: selectedApplication,
                          icon: Icon(
                            Icons.arrow_downward,
                            color: Colors.green,
                          ),
                          isExpanded: true,
                          iconSize: 24,
                          elevation: 16,
                          style: TextStyle(color: Colors.black, fontSize: 16),
                          underline: Container(),
                          onChanged: (Applications value) {
                            setState(() {
                              appId = "";
                              selectedApplication = value;
                              appId = '${value.id}';
                              print(appId);
                            });
                          },
                          items:
                              MyApp.applications.map((Applications valueItem) {
                            return DropdownMenuItem(
                              value: valueItem,
                              child: Text(valueItem.applicationName),
                            );
                          }).toList(),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text("Validity Days",
                          textColor: t5TextColorPrimary,
                          isCentered: true,
                          fontSize: textSizeMedium),
                      Container(
                          width: width / 1.8,
                          // height: 40,
                          child: TextFormField(
                            controller: daysController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              labelText: "Validity days",
                              isDense: true,
                              contentPadding: EdgeInsets.all(8),
                            ),
                            validator: validator.validateTextField,
                          )),
                    ],
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text("Device",
                          textColor: t5TextColorPrimary,
                          isCentered: true,
                          fontSize: textSizeMedium),
                      Container(
                        width: width / 1.8,
                        // height: 40,
                        child: TextFormField(
                          controller: deviceController,
                          decoration: InputDecoration(
                            enabledBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: const BorderSide(
                                  color: t5ViewColor, width: 0.0),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(4),
                              borderSide: const BorderSide(
                                  color: t5ViewColor, width: 0.0),
                            ),
                            labelText: "Device",
                            suffixIcon: IconButton(
                              icon: Icon(
                                Icons.qr_code_scanner,
                                color: Colors.black,
                                size: 20,
                              ),
                              onPressed: () {
                                scan();
                              },
                            ),
                            isDense: true,
                            contentPadding: EdgeInsets.all(8),
                          ),
                          //maxLines: null,
                          //expands: true,
                          style: TextStyle(color: Colors.black),
                          validator: validator.validateTextField,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 35,
                  ),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            FocusScope.of(context).unfocus();
                            validator.validate();
                            if (validator.validate()) {
                              helperClass.onLoading(context);
                              generateRandomCode();
                              var validityDate = now.add(Duration(days: int.parse(daysController.text)));
                              encryptionText =
                                  "${appId}/${validityDate.toString()}/${keyTempCode}";
                              print(encryptionText);
                              encrypt();
                              Future.delayed(Duration(seconds: 1))
                                  .then((value) {
                                setState(() {
                                  //daysController.clear();
                                  //deviceController.clear();
                                  print(validityDate);
                                });
                              });
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.all(5.0),
                            alignment: Alignment.center,
                            height: width / 8,
                            child: text("Generate Key",
                                textColor: t5White, isCentered: true),
                            decoration:
                                boxDecoration(bgColor: Colors.green, radius: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  if (generatedKey != '')
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            //color: Colors.blue,
                            height: 100,
                            width: width / 1.2,
                            child: Text(generatedKey)),
                      ],
                    ),
                  SizedBox(
                    height: 10,
                  ),
                  if (generatedKey != '')
                    Container(
                      // color: Colors.amberAccent,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Container(
                            width: width / 3,
                            child: QrImage(
                              data: generatedKey,
                            ),
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                height: 70,
                                alignment: Alignment.topCenter,
                                // color: Colors.green,
                                child: IconButton(
                                    icon: Icon(
                                      Icons.share,
                                      size: 30,
                                    ),
                                    onPressed: () {
                                      //Navigator.pop(context);
                                      Share.share(generatedKey);
                                    }),
                              ),
                              Container(
                                height: 60,
                                alignment: Alignment.bottomCenter,
                                //color: Colors.blue,
                                child: IconButton(
                                  //tooltip: "Copied",
                                  icon: Icon(
                                    Icons.copy,
                                    size: 30,
                                  ),
                                  onPressed: () {
                                    Clipboard.setData(
                                        ClipboardData(text: generatedKey));
                                    toast('Copied');
                                  },
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  SizedBox(
                    height: 35,
                  ),
                  if (generatedKey != '')
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: InkWell(
                            onTap: () {
                              viewOfflineActivationKeyCode(keyId).then((value) {
                                if (MyApp.isAuthorized) {
                                  if (value.status == 1) {
                                    createAlertDialogGetActivateCode(
                                        context, value.result.code);
                                  } else {
                                    toast(value.message);
                                  }
                                } else {
                                  helperClass.logout(context);
                                }
                              });
                            },
                            child: Container(
                              margin: EdgeInsets.all(5.0),
                              alignment: Alignment.center,
                              height: width / 8,
                              child: text("Activate Code",
                                  textColor: t5White, isCentered: true),
                              decoration: boxDecoration(
                                  bgColor: Colors.green, radius: 8),
                            ),
                          ),
                        ),
                      ],
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
