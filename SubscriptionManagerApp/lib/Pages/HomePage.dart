// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:share/share.dart';
import 'package:subscriptionmanager/Models/ChangePassword.dart';
import 'package:subscriptionmanager/Pages/GenerateCouponCode.dart';
import 'package:subscriptionmanager/Pages/OfflineActivation.dart';
import 'package:subscriptionmanager/Pages/ViewUserPassword.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/T5Colors.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/repository/UserRepository.dart';
import 'package:subscriptionmanager/repository/app_repository.dart';
import 'package:url_launcher/url_launcher.dart';

import '../Utilities/ValidationUtility.dart';
import '../main.dart';
import 'ActiveWorkspace.dart';
import 'ViewOfflineActivationCodes.dart';
import 'ViewOnlineActivationCodes.dart';
import 'WorkspaceList.dart';

class HomePage extends StatefulWidget {
  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  double width;
  double height;
  Validator validator = new Validator();
  Passwords password = new Passwords();
  HelperClass helperClass = new HelperClass();
  final passwordController = TextEditingController();
  final oldPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final cPasswordController = TextEditingController();
  bool isPasswordNotMatch = false;
  bool isLoading = false;

  createAlertDialogLogout(BuildContext buildContext) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Container(
            height: height / 2,
            child: AlertDialog(
              content: text("Are you sure want to logout?",
                  textColor: appStore.textPrimaryColor,
                  fontFamily: fontBold,
                  fontSize: 15.0),
              actions: [
                GestureDetector(
                  onTap: () {
                    setState(() {
                      helperClass.logout(context);
                    });
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: text("Yes", textColor: t5White, isCentered: true),
                    decoration:
                        boxDecoration(bgColor: Colors.green, radius: 8.0),
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: text("No", textColor: t5White, isCentered: true),
                    decoration:
                        boxDecoration(bgColor: Colors.green, radius: 8.0),
                  ),
                ),
              ],
            ),
          );
        });
  }

  showExPassword(BuildContext buildContext, String exPassword) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return AlertDialog(
            content: Container(
              height: 200,
              child: Column(
                children: [
                  if (exPassword != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 2,
                            child: Text(
                              exPassword,
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.w500),
                            )),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: 60,
                              alignment: Alignment.topCenter,
                              child: IconButton(
                                  icon: Icon(
                                    Icons.share,
                                    size: 30,
                                  ),
                                  onPressed: () {
                                    Share.share(exPassword);
                                  }),
                            ),
                            Container(
                              height: 60,
                              alignment: Alignment.bottomCenter,
                              child: IconButton(
                                icon: Icon(
                                  Icons.copy,
                                  size: 30,
                                ),
                                onPressed: () {
                                  Clipboard.setData(
                                      ClipboardData(text: exPassword));
                                  toast('Copied');
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  SizedBox(
                    height: 10,
                  ),
                ],
              ),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 40,
                  width: width / 5,
                  child: text("Close", textColor: t5White, isCentered: true),
                  decoration: boxDecoration(bgColor: Colors.green, radius: 8.0),
                ),
              ),
            ],
          );
        });
  }

  changePasswordDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Center(
                child: SingleChildScrollView(
                  child: AlertDialog(
                    content: Form(
                      key: validator.formkey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: EditText(
                                  text: "Old Password",
                                  isPassword: true,
                                  mController: oldPasswordController,
                                  validator: validator.validatePassword,
                                  isSecure: true,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: EditText(
                                  text: "New Password",
                                  isPassword: true,
                                  isSecure: true,
                                  mController: newPasswordController,
                                  inputType: TextInputType.text,
                                  validator: validator.validatePassword,
                                  errorText: isPasswordNotMatch
                                      ? "Password Mismatch"
                                      : null,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: EditText(
                                  text: "Confirm Password",
                                  isPassword: true,
                                  mController: cPasswordController,
                                  inputType: TextInputType.text,
                                  validator: validator.validatePassword,
                                  errorText: isPasswordNotMatch
                                      ? "Password Mismatch"
                                      : null,
                                  isSecure: true,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  clearTextField();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: text("Cancel",
                                      textColor: t5White, isCentered: true),
                                  decoration: boxDecoration(
                                      bgColor: Colors.green, radius: 8.0),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  validator.validate();
                                  if (validator.validate()) {
                                    password.oldPassword =
                                        oldPasswordController.text;
                                    password.newPassword =
                                        cPasswordController.text;
                                    if (newPasswordController.text ==
                                        cPasswordController.text) {
                                      changePassword(password).then((value) {
                                        toast(value.message);
                                        //print(value.message);
                                        if (MyApp.isAuthorized) {
                                          if (value.status == 1) {
                                            setState(() {
                                              clearTextField();
                                            });
                                            Navigator.pop(context);
                                          }
                                        } else {
                                          helperClass.logout(context);
                                        }
                                      });
                                    } else {
                                      setState(() {
                                        isPasswordNotMatch = true;
                                      });
                                    }
                                  }

                                  // insert(name, price);
                                  // print('ima${images}');
                                  /* Future.delayed(Duration(milliseconds: 100), () {
                              Navigator.pushReplacement(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => T5Listing()));
                            });*/
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: text("Change",
                                      textColor: t5White, isCentered: true),
                                  decoration: boxDecoration(
                                      bgColor: Colors.green, radius: 8.0),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        });
  }

  changeUserToAdmin(BuildContext context, bool value) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Center(
                child: SingleChildScrollView(
                  child: AlertDialog(
                    content: Form(
                      key: validator.formkey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          SizedBox(height: 10),
                          Container(
                            width: double.maxFinite,
                            // padding: EdgeInsets.only(left: 16),
                            child: Row(
                              children: <Widget>[
                                //CountryCodePicker(onChanged: print, showFlag: true),
                                Expanded(
                                  child: EditText(
                                    text: "Password",
                                    isPassword: true,
                                    mController: passwordController,
                                    validator: validator.validatePassword,
                                    isSecure: true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                  passwordController.clear();
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: text("Cancel",
                                      textColor: t5White, isCentered: true),
                                  decoration: boxDecoration(
                                      bgColor: learner_colorPrimary,
                                      radius: 8.0),
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  validator.validate();
                                  if (validator.validate()) {
                                    Navigator.pop(context);
                                    MyApp.isAdmin = true;
                                    passwordController.clear();
                                    /*  setState(() {
                                      isLoading = true;
                                    });
                                    login(loginRequest).then((value) async {
                                      passwordController.clear();
                                      if (value.token != null) {
                                        setState(() {
                                          isLoading = false;
                                          MyApp.isAdmin = true;
                                          fnUtilities
                                              .setUserType(MyApp.isAdmin);
                                          //toast(value.username);
                                        });
                                        SharedPreferences prefs =
                                        await SharedPreferences
                                            .getInstance();
                                        prefs.setString(
                                            "userDetails", json.encode(value));
                                        MyApp.token = value.token;

                                        ListUtility.subscriptionDetails
                                            .addAll(value.subscriptionDetails);

                                        Navigator.pop(context);
                                      } else {
                                        setState(() {
                                          isLoading = false;
                                        });
                                        toast("Incorrect Password");
                                      }
                                    });*/
                                  }
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: text("OK",
                                      textColor: t5White, isCentered: true),
                                  decoration: boxDecoration(
                                      bgColor: learner_colorPrimary,
                                      radius: 8.0),
                                ),
                              ),
                            ],
                          ),
                          isLoading
                              ? Center(child: CircularProgressIndicator())
                              : Container(),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        });
  }

  clearTextField() {
    oldPasswordController.clear();
    newPasswordController.clear();
    cPasswordController.clear();
  }

  getAllApplications() async {
    setState(() {
      isLoading = true;
    });
    await getAllApps().then((value) {
      if (value.status == 1) {
        setState(() {
          MyApp.applications = value.applications;
          isLoading = false;
        });
      } else {
        setState(() {
          toast("Failed to get Workspace");
          isLoading = false;
          MyApp.applications = [];
        });
      }
    });
  }

  @override
  void initState() {
    getAllApplications();
    Future.delayed(Duration.zero, () {
      helperClass.getId(context).then((id) {
        setState(() {
          MyApp.deviceId = id;
          //toast(MyApp.deviceId);
        });
      });
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        title: Text("Subscription Manager"),
      ),
      drawer: SafeArea(
          child: Drawer(
        child: Column(
          children: [
            /*  InkWell(
              onTap: () {
                Navigator.pop(context);
                createAlertDialogLogout(context);
              },
              child: Container(
                width: width / 1.3,
                color: Colors.green,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Align(
                    alignment: Alignment.centerRight,
                    child: Text(
                      "Logout",
                      //textAlign: TextAlign.right,
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 20.0,
                          fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
            ),*/
            UserAccountsDrawerHeader(
              accountName: Text("Subscription Manager"),
              accountEmail: Text("version 1.0.7"),
              currentAccountPicture: CircleAvatar(
                //backgroundColor: learner_colorGreen,
                child: Center(
                    child: Image(
                  image: AssetImage("assets/icon.png"),
                  width: 80,
                )),
              ),
            ),
            Expanded(
              child: ListView(
                padding: EdgeInsets.zero,
                shrinkWrap: true,
                children: [
                  ListTile(
                    title: Text("Home"),
                    onTap: () {
                      Navigator.pop(context);
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ExpansionTile(
                    title: Text("Activation"),
                    children: [
                      ListTile(
                        title: Text("Offline Activation"),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => ActivationPage()));
                        },
                      ),
                      ListTile(
                        title: Text("Online Activation"),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => GenerateCouponCode()));
                        },
                      ),
                    ],
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ExpansionTile(
                    title: Text("Activation Codes"),
                    children: [
                      ListTile(
                        title: Text("Offline Activation Codes"),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      ViewOfflineActivationCodes()));
                        },
                      ),
                      ListTile(
                        title: Text("Online Activation Codes"),
                        onTap: () {
                          Navigator.pop(context);
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) =>
                                      ViewOnlineActivationCodes()));
                        },
                      ),
                    ],
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ListTile(
                    title: Text("Change Password"),
                    onTap: () {
                      setState(() {
                        isPasswordNotMatch = false;
                      });
                      Navigator.pop(context);
                      changePasswordDialog(context);
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ListTile(
                    title: Text("Generate Exception Password"),
                    onTap: () {
                      Navigator.pop(context);
                      helperClass.onLoading(context);
                      generateExPassword().then((value) {
                        helperClass.onStopping();
                        if (value.status == 1) {
                          showExPassword(context, value.result.keyValue);
                        } else {
                          toast(value.message);
                        }
                      });
                      //changePasswordDialog(context);
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  /*ListTile(
                    title: Text("Admin"),
                    trailing: Switch(
                      value: MyApp.isAdmin,
                      onChanged: (value) {
                        if (MyApp.isAdmin) {
                          setState(() {
                            MyApp.isAdmin = value;
                            //fnUtilities.setUserType(MyApp.isAdmin);
                          });
                        } else {
                          changeUserToAdmin(context, value);
                        }
                      },
                      activeTrackColor: learner_colorPrimary,
                      activeColor: learner_colorPrimary,
                    ),
                    onTap: () {},
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),*/
                  ListTile(
                    title: Text("Rate Us"),
                    onTap: () {
                      Navigator.pop(context);
                      launch(
                          'https://play.google.com/store/apps/details?id=com.cloudstier.subscriptionmanager');
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ListTile(
                    title: Text("Share"),
                    onTap: () {
                      Navigator.pop(context);
                      String link =
                          "https://play.google.com/store/apps/details?id=com.cloudstier.subscriptionmanager";
                      Share.share(link);
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ListTile(
                    title: Text("View User Password"),
                    onTap: () {
                      Navigator.pop(context);
                      Navigator.push(
                          context,
                          MaterialPageRoute(
                              builder: (context) => ViewUserPassword()));
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                  ListTile(
                    title: Text("Logout"),
                    onTap: () {
                      createAlertDialogLogout(context);
                    },
                  ),
                  Divider(
                    height: 1,
                    thickness: 1,
                  ),
                ],
              ),
            ),
          ],
        ),
      )),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          width: width,
          height: height,
          child: Center(
            child: isLoading
                ? Container(
                    height: 50,
                    width: 50,
                    child: CircularProgressIndicator(),
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            ActivationPage()));
                              },
                              child: Container(
                                margin: EdgeInsets.all(5.0),
                                alignment: Alignment.center,
                                height: width / 8,
                                child: text("Offline Activation",
                                    textColor: t5White, isCentered: true),
                                decoration: boxDecoration(
                                    bgColor: Colors.green, radius: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            GenerateCouponCode()));
                              },
                              child: Container(
                                margin: EdgeInsets.all(5.0),
                                alignment: Alignment.center,
                                height: width / 8,
                                child: text("Online Activation",
                                    textColor: t5White, isCentered: true),
                                decoration: boxDecoration(
                                    bgColor: Colors.green, radius: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            Activeworkspace()));
                              },
                              child: Container(
                                margin: EdgeInsets.all(5.0),
                                alignment: Alignment.center,
                                height: width / 8,
                                child: text("Workspace Activation",
                                    textColor: t5White, isCentered: true),
                                decoration: boxDecoration(
                                    bgColor: Colors.green, radius: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      ),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => Workspacelist()));
                              },
                              child: Container(
                                margin: EdgeInsets.all(5.0),
                                alignment: Alignment.center,
                                height: width / 8,
                                child: text("Workspace Details",
                                    textColor: t5White, isCentered: true),
                                decoration: boxDecoration(
                                    bgColor: Colors.green, radius: 8),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }
}
