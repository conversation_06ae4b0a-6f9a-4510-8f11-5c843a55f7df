import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:share/share.dart';
import 'package:subscriptionmanager/Models/get_all_apps_model.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/T5Colors.dart';
import 'package:subscriptionmanager/Utilities/ValidationUtility.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/repository/app_repository.dart';
import '../main.dart';

class ViewUserPassword extends StatefulWidget {
  @override
  _ViewUserPasswordState createState() => _ViewUserPasswordState();
}

class _ViewUserPasswordState extends State<ViewUserPassword> {
  double width;
  double height;
  final mobileController = TextEditingController();
  String viewPassword;
  Validator validator = new Validator();
  HelperClass helperClass = new HelperClass();
  Applications selectedApplication;
  @override
  void initState() {
    selectedApplication = MyApp.applications.length > 0
        ? MyApp.applications[0]
        : new Applications();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        title: Text("View Password"),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                mobileController.clear();
                viewPassword = null;
              });
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          width: width,
          height: height,
          child: SingleChildScrollView(
            child: Form(
              key: validator.formkey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text(
                        "Application Name",
                        textColor: t5TextColorPrimary,
                        isCentered: true,
                        fontSize: textSizeMedium,
                      ),
                      MyApp.applications.length > 0
                          ? Container(
                              width: width / 1.8,
                              padding: EdgeInsets.all(5.0),
                              height: 40,
                              decoration: BoxDecoration(
                                // color: const Color(0xff7c94b6),
                                border: Border.all(
                                  color: Colors.black,
                                  width: 0.2,
                                ),
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: DropdownButton(
                                value: selectedApplication,
                                icon: Icon(
                                  Icons.arrow_downward,
                                  color: Colors.green,
                                ),
                                isExpanded: true,
                                iconSize: 24,
                                elevation: 16,
                                style: TextStyle(
                                    color: Colors.black, fontSize: 16),
                                underline: Container(),
                                onChanged: (Applications value) {
                                  setState(() {
                                    selectedApplication = value;
                                  });
                                },
                                items: MyApp.applications
                                    .map((Applications valueItem) {
                                  return DropdownMenuItem(
                                    value: valueItem,
                                    child: Text(valueItem.applicationName),
                                  );
                                }).toList(),
                              ),
                            )
                          : Container(
                              child: Text('Apps not available'),
                            ),
                    ],
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text("User Name",
                          textColor: t5TextColorPrimary,
                          isCentered: true,
                          fontSize: textSizeMedium),
                      Container(
                          width: width / 1.8,
                          // height: 40,
                          child: TextFormField(
                            controller: mobileController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              labelText: "Username",
                              isDense: true,
                              contentPadding: EdgeInsets.all(8),
                            ),
                            validator: validator.validateTextField,
                          )),
                    ],
                  ),
                  SizedBox(
                    height: 35,
                  ),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            FocusScope.of(context).unfocus();
                            validator.validate();
                            if (validator.validate()) {
                              String userName = mobileController.text;
                              helperClass.onLoading(context);
                              viewUserPassword(selectedApplication.id, userName)
                                  .then((value) {
                                helperClass.onStopping();
                                if (MyApp.isAuthorized) {
                                  if (value.status != 0) {
                                    setState(() {
                                      viewPassword = value.response;
                                    });
                                  } else {
                                    toast("Password not found");
                                  }
                                } else {
                                  helperClass.logout(context);
                                }
                              });
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.all(5.0),
                            alignment: Alignment.center,
                            height: width / 8,
                            child: text("View Password",
                                textColor: t5White, isCentered: true),
                            decoration:
                                boxDecoration(bgColor: Colors.green, radius: 8),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  if (viewPassword != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                            //color: Colors.blue,
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 2,
                            child: Text(
                              viewPassword,
                              style: TextStyle(
                                  fontSize: 20, fontWeight: FontWeight.w500),
                            )),
                        Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: 60,
                              alignment: Alignment.topCenter,
                              // color: Colors.green,
                              child: IconButton(
                                  icon: Icon(
                                    Icons.share,
                                    size: 30,
                                  ),
                                  onPressed: () {
                                    //Navigator.pop(context);
                                    Share.share(viewPassword);
                                  }),
                            ),
                            Container(
                              height: 60,
                              alignment: Alignment.bottomCenter,
                              //color: Colors.blue,
                              child: IconButton(
                                //tooltip: "Copied",
                                icon: Icon(
                                  Icons.copy,
                                  size: 30,
                                ),
                                onPressed: () {
                                  Clipboard.setData(
                                      ClipboardData(text: viewPassword));
                                  toast('Copied');
                                },
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  SizedBox(
                    height: 10,
                  ),
                  if (viewPassword != null)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [],
                    ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
