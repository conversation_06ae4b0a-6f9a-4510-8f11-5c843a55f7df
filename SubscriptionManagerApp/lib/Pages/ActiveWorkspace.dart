import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';

import 'package:subscriptionmanager/Models/Activeworkspace.dart';

import 'package:subscriptionmanager/Models/get_all_apps_model.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/T5Colors.dart';
import 'package:subscriptionmanager/Utilities/ValidationUtility.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/commonWidgets/textwidget.dart';

import 'package:subscriptionmanager/repository/WorkspaceRepository.dart';

import '../main.dart';

class Activeworkspace extends StatefulWidget {
  const Activeworkspace({Key key}) : super(key: key);

  @override
  State<Activeworkspace> createState() => _ActiveworkspaceState();
}

class _ActiveworkspaceState extends State<Activeworkspace> {

  double width;
  double height;
  String generatedCode;
  Validator validator = new Validator();
  var now = DateTime.now();
  final daysController = TextEditingController();
  final workController = TextEditingController();
  Applications selectedApplication;
  HelperClass helperClass = new HelperClass();
  String appId = '';
  @override
  void initState() {
    selectedApplication = MyApp.applications.length > 0
        ? MyApp.applications[0]
        : new Applications();
    super.initState();
  }

  clearTextFields() {
    daysController.clear();
    workController.clear();
  }

  String convertDate(originalDate) {
    var date = DateFormat("dd-MM-yyyy    KK:mm:ss a")
        .format(DateTime.parse("$originalDate"));
    return date;
  }

  activeWork(){
    helperClass.onLoading(context);
    ActiveWorkspace active = ActiveWorkspace();
    active.applicationID = selectedApplication.id;
    active.workspaceID = workController.text;
    active.validityDays = int.parse(daysController.text);

    activeWorkspace(active).then((value) {
       if(value.status == 1){
         helperClass.onStopping();
         toast(value.message);
         clearTextFields();
         if (MyApp.isAuthorized) {
           setState(() {
             generatedCode = value.result.expiryDate;
           });
         }
       }
       else {
         helperClass.onStopping();
         toast(value.message);
       }
      });
  }


  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        title: Text("Workspace Activation"),
        actions: [
          IconButton(
            icon: Icon(
              Icons.refresh,
              color: Colors.white,
            ),
            onPressed: () {
              setState(() {
                daysController.clear();
                workController.clear();
                generatedCode = null;
              });
            },
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Container(
          width: width,
          height: height,
          child: SingleChildScrollView(
            child: Form(
              key: validator.formkey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      text(
                        "Application Name",
                        textColor: t5TextColorPrimary,
                        isCentered: true,
                        fontSize: textSizeMedium,
                      ),
                      MyApp.applications.length > 0
                          ? Container(
                        width: width / 1.8,
                        padding: EdgeInsets.all(5.0),
                        height: 40,
                        decoration: BoxDecoration(
                          // color: const Color(0xff7c94b6),
                          border: Border.all(
                            color: Colors.black,
                            width: 0.2,
                          ),
                          borderRadius: BorderRadius.circular(3),
                        ),
                        child: DropdownButton(
                          value: selectedApplication,
                          icon: Icon(
                            Icons.arrow_downward,
                            color: Colors.green,
                          ),
                          isExpanded: true,
                          iconSize: 24,
                          elevation: 16,
                          style: TextStyle(
                              color: Colors.black, fontSize: 16),
                          underline: Container(),
                          onChanged: (Applications value) {
                            setState(() {
                              appId = "";
                              selectedApplication = value;
                              appId = '${value.id}/';
                              print(appId);
                            });
                          },
                          items: MyApp.applications
                              .map((Applications valueItem) {
                            return DropdownMenuItem(
                              value: valueItem,
                              child: Text(valueItem.applicationName),
                            );
                          }).toList(),
                        ),
                      )
                          : Container(
                        child: Text('Apps not available'),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [

                      Container(
                          width: width / 2.4,
                          // height: 50,
                          child: TextFormField(
                            controller: workController,
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.fromLTRB(10, 27, 10, 0),
                              enabledBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(4),
                                borderSide: const BorderSide(
                                    color: t5ViewColor, width: 0.0),
                              ),
                              labelText: "Workspace ID",
                              isDense: true,
                              // contentPadding: EdgeInsets.all(8),
                            ),
                            validator: validator.validateTextField,  inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9,+,.,-]')),
                          ],

                          )
                      ), Container(
                          width: width / 1.9,
                          // height: 75,
                          child: TextFieldWidget(
                            text: " Validity days",
                            isPassword: false,
                            mController: daysController,
                            validator: (value) {
                              if (value.isEmpty){
                                return 'This field is required';
                              }
                              if (value.isNotEmpty){
                                int plus = "+".allMatches(value).length;
                                int minus = "-".allMatches(value).length;
                                String symbol = value.toString()[0];
                                if(plus >= 1 || minus >= 1){
                                  if(plus > 1 || (plus == 1 && symbol != "+")){
                                    return 'example: 1000';
                                  }
                                  else if (minus > 1 || (minus == 1 && symbol != "-")){
                                    return 'example: -1000';
                                  }
                                }
                                else {
                                  return null;
                                }
                              } else {
                                return null;
                              }
                            },
                            inputFormate: [
                              FilteringTextInputFormatter.allow(RegExp(r'[0-9,+,.,-]')),
                            ],
                            inputType: TextInputType.phone,
                            inputAction: TextInputAction.next,
                          ),
                      ),

                    ],
                  ),
                  SizedBox(
                    height: 35,
                  ),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: InkWell(
                          onTap: () {
                            validator.validate();
                            if (validator.validate()) {
                              activeWork();
                              FocusScope.of(context).unfocus();
                            }
                          },
                          child: Container(
                            margin: EdgeInsets.all(5.0),
                            alignment: Alignment.center,
                            height: width / 8,
                            child: text("Activate",
                                textColor: t5White, isCentered: true),
                            decoration:
                            boxDecoration(bgColor: Colors.green, radius: 8),
                          ),
                        ),
                      ),
                    ],
                  ),

                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
