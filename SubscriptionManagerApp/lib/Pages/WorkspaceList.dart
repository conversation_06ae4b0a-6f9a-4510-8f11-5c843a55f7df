// ignore_for_file: unnecessary_statements

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:subscriptionmanager/Models/WorkpaceList.dart';
import 'package:subscriptionmanager/commonWidgets/constants.dart';
import 'package:subscriptionmanager/repository/WorkspaceRepository.dart';

import 'Workspace_details.dart';

class Workspacelist extends StatefulWidget {
  const Workspacelist({Key key}) : super(key: key);

  @override
  State<Workspacelist> createState() => _WorkspacelistState();
}

class _WorkspacelistState extends State<Workspacelist> {
  List<WorkspaceIdlist> WorkspaceList = [];
  double width = 0.0;
  double height = 0.0;
  bool isLoading = false;

  @override
  initState() {
    workspaceIdlist();
  }

  workspaceIdlist() {
    isLoading = true;
    getallWorkspace().then((value) {
      if (value.status == 1) {
        setState(() {
          if (value.result != null) {
            WorkspaceList = value.result;
            for (WorkspaceIdlist model in WorkspaceList) {
              model.workspaceId;
              model.workspaceName;
              model.expiryDate;
              model.isActive;
            }
          }
        });
      }
      setState(() {
        isLoading = false;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Workspace List"),
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 1.0),
        child: Column(
          children: [
            SizedBox(
              height: 60,
              child: Card(
                color: Colors.black54,
                child: Row(
                  children: const [
                    Expanded(
                      child: Text("Expiry In (Days)", style: TextStyle(color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
                    ),
                    Expanded(
                      child: Text("Workspace ID", style: TextStyle(color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold), textAlign: TextAlign.end),
                    ),
                    Expanded(
                      child: Text("Shope Name", style: TextStyle(color: Colors.white, fontSize: 15, fontWeight: FontWeight.bold), textAlign: TextAlign.right),
                    ),
                    SizedBox(width: 15),
                  ],
                ),
              ),
            ),
            viewList(),
          ],
        ),
      ),
    );
  }

  // String convertDate(originalDate) {
  //   var date = DateFormat("dd-MM-yyyy KK:mm:ss a").format(DateTime.parse("$originalDate"));
  //   return date;
  // }

  viewList() {
    return isLoading
        ? SizedBox(
            height: height / 1.8,
            width: width / 1.1,
            child: const Center(
              child: SizedBox(
                height: 40,
                width: 40,
                child: CircularProgressIndicator(),
              ),
            ),
          )
        : Expanded(
            child: WorkspaceList.isNotEmpty
                ? ListView.builder(
                    // reverse: true,
                    itemCount: WorkspaceList.length,
                    itemBuilder: (context, index) {
                      var item = WorkspaceList[index];
                      return Column(
                        children: [
                          InkWell(
                            onTap: () {
                              Navigator.push(context, MaterialPageRoute(builder: (context) => Workspacedetails(workspaceID: item.workspaceId)));
                            },
                            child: Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 4),
                              child: Column(
                                children: [
                                  Column(
                                    children: [
                                      const SizedBox(
                                        height: 9,
                                      ),
                                      Container(
                                        height: 50,
                                        color: Colors.black12,
                                        child: Row(
                                          children: <Widget>[
                                            SizedBox(
                                              width: 1,
                                            ),
                                            Expanded(
                                              child: Text(item.expiryDays != null ? item.expiryDays.toString() : null,
                                                  style: const TextStyle(
                                                    color: Colors.black87,
                                                    fontSize: 15,
                                                  ),
                                                  textAlign: TextAlign.start),
                                            ),
                                            const SizedBox(
                                              width: 14,
                                            ),
                                            Expanded(
                                              child: Text("${item.workspaceId}",
                                                  style: const TextStyle(
                                                    color: Colors.black87,
                                                    fontSize: 15,
                                                  ),
                                                  textAlign: TextAlign.end),
                                            ),
                                            const SizedBox(
                                              width: 14,
                                            ),
                                            Expanded(
                                              child: Text("${item.workspaceName}",
                                                  style: const TextStyle(
                                                    color: Colors.black87,
                                                    fontSize: 15,
                                                  ),
                                                  textAlign: TextAlign.end),
                                            ),
                                            const SizedBox(
                                              width: 14,
                                            ),
                                          ],
                                        ),
                                      ),
                                      const SizedBox(
                                        height: 1,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      );
                    })
                : const Center(child: Text("Workspace not available")),
          );
  }
}
