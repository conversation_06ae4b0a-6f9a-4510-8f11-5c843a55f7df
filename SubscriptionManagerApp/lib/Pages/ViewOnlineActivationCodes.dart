import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:share/share.dart';
import 'package:subscriptionmanager/Models/GetCouponCodes.dart';
import 'package:subscriptionmanager/Pages/LoginPage.dart';
import 'package:subscriptionmanager/Utilities/AppConstant.dart';
import 'package:subscriptionmanager/Utilities/HelperClass.dart';
import 'package:subscriptionmanager/Utilities/T5Colors.dart';
import 'package:subscriptionmanager/Utilities/WidgetsClass.dart';
import 'package:subscriptionmanager/repository/UserRepository.dart';
import '../main.dart';

class ViewOnlineActivationCodes extends StatefulWidget {
  @override
  _ViewOnlineActivationCodesState createState() =>
      _ViewOnlineActivationCodesState();
}

class _ViewOnlineActivationCodesState extends State<ViewOnlineActivationCodes> {
  double width;
  double height;
  List<Result> viewCouponCodeLists = [];
  HelperClass helperClass = new HelperClass();
  bool isLoading = true;

  alertDialogActivationCode(BuildContext context, String activationCode) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              content: Container(
                height: 300,
                width: width / 1.2,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                            width: 150,
                            height: 150,
                            child: QrImage(data: "activationCode")),
                        Container(
                          height: 150,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            children: [
                              IconButton(
                                  icon: Icon(Icons.share),
                                  onPressed: () {
                                    //Navigator.pop(context);
                                    Share.share(activationCode);
                                  }),
                              IconButton(
                                  icon: Icon(Icons.copy),
                                  onPressed: () {
                                    Clipboard.setData(
                                        ClipboardData(text: activationCode));
                                    toast('Copied');
                                  }),
                            ],
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(
                          //width: width / 2.5,
                          // color: Colors.red,
                          child: text(
                            activationCode,
                            textColor: t5TextColorPrimary,
                            isCentered: true,
                            fontSize: textSizeNormal,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    InkWell(
                      onTap: () {
                        Navigator.pop(context);
                      },
                      child: Container(
                        margin: EdgeInsets.all(5.0),
                        alignment: Alignment.center,
                        height: 50,
                        child:
                            text("Close", textColor: t5White, isCentered: true),
                        decoration:
                            boxDecoration(bgColor: Colors.green, radius: 8),
                      ),
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }

  @override
  void initState() {
    // helperClass.onLoading(context);

    viewCoupenCodes().then((value) {
      if (MyApp.isAuthorized) {
        setState(() {
          isLoading = false;
          viewCouponCodeLists = value.result;
        });
      } else {
        Navigator.pop(context);
        Navigator.pushReplacement(
            context, MaterialPageRoute(builder: (context) => LoginPage()));
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: AppBar(
        title: Text("Online Activation Codes"),
      ),
      body: isLoading
          ? Container(
              height: height,
              // color: Colors.blue,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Center(
                    child: CircularProgressIndicator(),
                  ),
                  Text("Loading..."),
                ],
              ),
            )
          : Container(
              alignment: Alignment.topCenter,
              height: height,
              child: viewCouponCodeLists.length != 0
                  ? Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Container(
                          height: 50,
                          child: Row(
                            children: [
                              Container(
                                //color: Colors.amberAccent,
                                width: width / 3,
                                alignment: Alignment.center,
                                child: text("Vendor Id :",
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeNormal),
                              ),
                              Container(
                                //color: Colors.amberAccent,
                                width: width / 5,
                                alignment: Alignment.centerLeft,
                                child: text(
                                    viewCouponCodeLists[0].vendorId.toString(),
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeNormal),
                              ),
                            ],
                          ),
                        ),
                        Divider(
                          height: 2,
                          thickness: 1,
                        ),
                        Container(
                          width: width,
                          height: 50,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: <Widget>[
                              Container(
                                //color: Colors.amberAccent,
                                width: width / 8,
                                alignment: Alignment.center,
                                child: text("S.No",
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeMedium),
                              ),
                              Container(
                                //color: Colors.amberAccent,
                                width: width / 6,
                                alignment: Alignment.center,
                                child: text("App Id",
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeMedium),
                              ),
                              Container(
                                //color: Colors.amberAccent,
                                width: width / 3.5,
                                alignment: Alignment.center,
                                child: text("Date",
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeMedium),
                              ),
                              Container(
                                width: width / 3,
                                alignment: Alignment.center,
                                child: text("Validity Days",
                                    textColor: appStore.textSecondaryColor,
                                    fontSize: textSizeMedium,
                                    fontFamily: fontSemibold),
                              )
                            ],
                          ),
                        ),
                        Divider(
                          height: 2,
                          thickness: 1,
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: ListView.builder(
                                padding: EdgeInsets.only(right: 0),
                                itemCount: viewCouponCodeLists.isNotEmpty
                                    ? viewCouponCodeLists.length
                                    : null,
                                shrinkWrap: true,
                                //reverse: true,
                                itemBuilder: (context, index) {
                                  var item = viewCouponCodeLists[index];
                                  return InkWell(
                                    onTap: () {
                                      alertDialogActivationCode(
                                          context, item.activationCode);
                                    },
                                    child: Column(
                                      children: <Widget>[
                                        Container(
                                          margin: EdgeInsets.all(5),
                                          height: 60,
                                          color: item.isActive
                                              ? Colors.white12
                                              : Colors.greenAccent,
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Container(
                                                //color: Colors.amberAccent,
                                                width: width / 8,
                                                alignment: Alignment.center,
                                                child: text(
                                                    (index + 1).toString(),
                                                    textColor: appStore
                                                        .textSecondaryColor,
                                                    fontSize: textSizeMedium),
                                              ),
                                              Container(
                                                //color: Colors.amberAccent,
                                                width: width / 6,
                                                alignment: Alignment.center,
                                                child: text(
                                                    item.applicationId
                                                        .toString(),
                                                    textColor: appStore
                                                        .textSecondaryColor,
                                                    fontSize: textSizeMedium),
                                              ),
                                              Container(
                                                //color: Colors.amberAccent,
                                                width: width / 3.5,
                                                alignment: Alignment.center,
                                                child: text(
                                                    DateFormat("dd-MM-yyyy")
                                                        .format(DateTime.parse(
                                                            "${item.createdOn}"))
                                                        .toString(),
                                                    textColor: appStore
                                                        .textSecondaryColor,
                                                    fontSize: textSizeMedium),
                                              ),
                                              Container(
                                                width: width / 3,
                                                alignment: Alignment.center,
                                                child: text(
                                                    "${item.validityDays.toString()} days",
                                                    textColor: appStore
                                                        .textSecondaryColor,
                                                    fontSize: textSizeMedium,
                                                    fontFamily: fontSemibold),
                                              )
                                            ],
                                          ),
                                        ),
                                        Divider(height: 0.5, color: t5ViewColor)
                                      ],
                                    ),
                                  );
                                }),
                          ),
                        )
                      ],
                    )
                  : Container(
                      height: height,
                      child: Center(
                        child: text("No Records!"),
                      ),
                    ),
            ),
    );
  }
}
