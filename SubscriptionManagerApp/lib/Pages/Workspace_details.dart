// ignore_for_file: unnecessary_statements

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:subscriptionmanager/Models/WorkpaceList.dart';
import 'package:subscriptionmanager/Models/WorkspaceDetails.dart';
import 'package:subscriptionmanager/commonWidgets/constants.dart';
import 'package:subscriptionmanager/repository/WorkspaceRepository.dart';

class Workspacedetails extends StatefulWidget {
  String workspaceID;
  Workspacedetails({Key key, this.workspaceID}) : super(key: key);

  @override
  State<Workspacedetails> createState() => _WorkspacedetailsState(workspaceID);
}

class _WorkspacedetailsState extends State<Workspacedetails> {
  WorkspaceDetails workspacdetails;
  double width = 0.0;
  double height = 0.0;
  bool isLoading = false;
  String workspaceID;
  _WorkspacedetailsState(this.workspaceID);

  @override
  initState() {
    getWorkdetails();
  }

  var workID;
  int userID;
  var firstName;
  var lastName;
  String mobile;
  String shopName;
  String expiryDate;
  bool isActive;

  getWorkdetails() {
    workspaceDetails(workspaceID).then((value) {
      if (value.status == 1) {
        setState(() {
          if (value.result != null) {
            workspacdetails = value.result;
            workID = workspacdetails.workspaceId;
            userID = workspacdetails.userID;
            firstName = workspacdetails.firstName;
            lastName = workspacdetails.lastName;
            mobile = workspacdetails.mobile;
            shopName = workspacdetails.shopName;
            expiryDate = workspacdetails.expiryDate;
          }
        });
      }
      setState(() {
        isLoading = false;
      });
    });
  }

  String convertDate(originalDate) {
    var date = DateFormat("dd-MM-yyyy KK:mm:ss a").format(DateTime.parse("$originalDate"));
    return date;
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      // backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text("Workspace Details"),
      ),
      body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 1.0),
          child: Container(
            child: Column(
              children: [
                Text("Workspace ID : $workID"),
                Text("User ID : $userID"),
                Text("Name : $firstName $lastName"),
                Text("Mobile Number : $mobile"),
                Text("ShopeName :$shopName"),
                Text(" ExpiryDate : $expiryDate"),
              ],
            ),
          )),
    );
  }
}
