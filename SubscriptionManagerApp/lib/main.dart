import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:subscriptionmanager/Models/Users.dart';
import 'package:subscriptionmanager/Pages/HomePage.dart';
import 'package:subscriptionmanager/Pages/LoginPage.dart';

import 'Models/get_all_apps_model.dart';
import 'Utilities/store/AppStore.dart';

AppStore appStore = AppStore();
void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  // This widget is the root of your application.
  static String userName;
  static String token;
  static bool isAdmin = true;
  static bool isAuthorized = true;
  static String deviceId = '';
  static int appId = 3;
  static List<Applications> applications = <Applications>[];

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Subscription Manager',
      theme: ThemeData(
        primarySwatch: Colors.green,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      home: NavigationPage(),
    );
  }
}

class NavigationPage extends StatefulWidget {
  @override
  _NavigationPageState createState() => _NavigationPageState();
}

class _NavigationPageState extends State<NavigationPage> {
  navigatePage() async {
    Future.delayed(Duration.zero, () {
      Navigator.pop(context);
      if (!MyApp.token.isEmptyOrNull) {
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => HomePage()));
      } else {
        Navigator.push(
            context, MaterialPageRoute(builder: (context) => LoginPage()));
      }
    });
  }

  @override
  void initState() {
    getUserDetails();
    super.initState();
  }

  getUserDetails() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    User user = new User();
    user = prefs.getString("vendorDetails") != null
        ? User.fromJson(json.decode(prefs.getString("vendorDetails")))
        : null;
    setState(() {
      MyApp.userName = user != null ? user.result.username : null;
      MyApp.token = user != null ? user.result.token : null;
    });
    navigatePage();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold();
  }
}
