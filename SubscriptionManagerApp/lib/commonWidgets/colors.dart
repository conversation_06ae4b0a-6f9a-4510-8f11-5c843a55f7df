// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';

const colorPrimary = Color(0XFF5104D7);
const colorPrimaryDark = Color(0XFF325BF0);
const colorAccent = Color(0XFFD81B60);
const textColorPrimary = Color(0XFF130925);
const textColorSecondary = Color(0XFF888888);
const textColorThird = Color(0XFFBABFB6);
const textColorGrey = Color(0XFFB4BBC2);
const white = Color(0XFFFFFFFF);
const layoutBackgroundWhite = Color(0XFFF6F7FA);
const viewColor = Color(0XFFB4BBC2);
const skyBlue = Color(0XFF1fc9cd);
const darkNavy = Color(0XFF130925);
const cat1 = Color(0XFF45c7db);
const cat2 = Color(0XFF510AD7);
const cat3 = Color(0XFFe43649);
const cat4 = Color(0XFFf4b428);
const ca = Color(0XFF22ce9a);
const cat6 = Color(0XFF203afb);
const shadowColor = Color(0X95E9EBF0);
const darkRed = Color(0XFFF06263);
const colorPrimaryLight = Color(0X505104D7);
const iconColor = Colors.black;
const primaryBlue = Color(0xFF64B5F6);
const teal = Colors.teal;

const learner_colorPrimary = Colors.lightBlue;
//const learner_colorPrimary = Color(0xFF345FFB);
const learner_colorBlue = Color(0xFF4da6ff);
const learner_backgroundColor = Color(0xFFFAFAFA);
const learner_blackColor = Color(0xFF000000);
const learner_greyColor = Color(0xFF808080);
const learner_layout_background = Color(0xFFF6F7FA);
const learner_white = Color(0xFFffffff);
const learner_textColorSecondary = Color(0xFF778390);
