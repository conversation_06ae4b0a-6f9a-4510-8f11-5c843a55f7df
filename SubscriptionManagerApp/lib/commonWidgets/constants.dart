/*fonts*/
import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';

const fontRegular = 'Regular';
const fontMedium = 'Medium';
const fontSemibold = 'Semibold';
const fontBold = 'Bold';
/* font sizes*/
const textSizeSmall = 13.0;
const textSizeSMedium = 14.0;
const textSizeMedium = 16.0;
const textSizeLargeMedium = 18.0;
const textSizeNormal = 20.0;
const textSizeLarge = 24.0;
const textSizeXLarge = 28.0;
const textSizeXXLarge = 30.0;

const Color primaryColor = Color(0xff003d00);
const Color primaryWhiteColor = Colors.white;
const Color primaryBlackColor = Colors.black;
const Color primaryGreyColor = Colors.grey;

const double fixPadding = 10;

TextStyle black20BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 20,
  fontWeight: FontWeight.w700,
);

TextStyle white20BoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 20,
  fontWeight: FontWeight.w700,
);

TextStyle black17BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 17,
  fontWeight: FontWeight.w700,
);

TextStyle white18SemiBoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 18,
  fontWeight: FontWeight.w600,
);

TextStyle primaryColor18BoldTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 18,
  fontWeight: FontWeight.w700,
);

TextStyle black16BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
);

TextStyle white16BoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
);

TextStyle primaryColor16BlackTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 16,
  fontWeight: FontWeight.w900,
);

TextStyle primaryColor16BoldTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
);

TextStyle black16SemiBoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 16,
  fontWeight: FontWeight.w600,
);

TextStyle black15BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 15,
  fontWeight: FontWeight.w700,
);

TextStyle red15BoldTextStyle = const TextStyle(
  color: Colors.red,
  fontSize: 15,
  fontWeight: FontWeight.w700,
);

TextStyle black15SemiBoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 15,
  fontWeight: FontWeight.w600,
);

TextStyle primaryColor15BoldTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 15,
  fontWeight: FontWeight.w700,
);

TextStyle white15BoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 15,
  fontWeight: FontWeight.w700,
);

TextStyle grey15RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 15,
  fontWeight: FontWeight.w400,
);

TextStyle white14BoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle white14SemiBoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle black14BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle blue14BoldTextStyle = const TextStyle(
  color: Colors.blue,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle black14SemiBoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle black14RegularTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 14,
  fontWeight: FontWeight.w400,
);

TextStyle primaryColor14SemiBoldTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle grey14BoldTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle grey14SemiBoldTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle grey14RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 14,
  fontWeight: FontWeight.w400,
);

TextStyle black13BoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 13,
  fontWeight: FontWeight.w700,
);

TextStyle primaryColor13BlackTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 13,
  fontWeight: FontWeight.w900,
);

TextStyle grey13SemiBoldTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 13,
  fontWeight: FontWeight.w600,
);

TextStyle white13SemiBoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 13,
  fontWeight: FontWeight.w600,
);

TextStyle black13SemiBoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 13,
  fontWeight: FontWeight.w600,
);

TextStyle grey13RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 13,
  fontWeight: FontWeight.w400,
);

TextStyle white13RegularTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 13,
  fontWeight: FontWeight.w400,
);

TextStyle black13RegularTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 13,
  fontWeight: FontWeight.w400,
);

TextStyle black13MediumTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 13,
  fontWeight: FontWeight.w500,
);

TextStyle primaryColor13RegularTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 13,
  fontWeight: FontWeight.w400,
);

TextStyle black11SemiBoldTextStyle = const TextStyle(
  color: primaryBlackColor,
  fontSize: 11,
  fontWeight: FontWeight.w600,
);

TextStyle white12RegularTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 12,
  fontWeight: FontWeight.w400,
);

TextStyle grey12RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 12,
  fontWeight: FontWeight.w400,
);

TextStyle grey12SemiBoldTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 12,
  fontWeight: FontWeight.w600,
);

TextStyle white12BoldTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 12,
  fontWeight: FontWeight.w700,
);

TextStyle primaryColor12BlackTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 12,
  fontWeight: FontWeight.w900,
);

TextStyle grey12BlackTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 12,
  fontWeight: FontWeight.w900,
);

TextStyle primaryColor11BlackTextStyle = const TextStyle(
  color: primaryColor,
  fontSize: 11,
  fontWeight: FontWeight.w900,
);

TextStyle grey11SemiBoldTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 11,
  fontWeight: FontWeight.w600,
);

TextStyle grey11RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 11,
  fontWeight: FontWeight.w400,
);

TextStyle white10BlackTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 10,
  fontWeight: FontWeight.w900,
);

TextStyle white9BlackTextStyle = const TextStyle(
  color: primaryWhiteColor,
  fontSize: 9,
  fontWeight: FontWeight.w900,
);

TextStyle grey9RegularTextStyle = const TextStyle(
  color: primaryGreyColor,
  fontSize: 9,
  fontWeight: FontWeight.w400,
);
