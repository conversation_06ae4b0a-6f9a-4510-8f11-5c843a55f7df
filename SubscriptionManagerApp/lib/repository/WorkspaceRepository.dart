
import 'dart:convert';
import 'dart:io';

import 'package:subscriptionmanager/Models/Activeworkspace.dart';
import 'package:subscriptionmanager/Models/WorkpaceList.dart';
import 'package:subscriptionmanager/Models/WorkspaceDetails.dart';

import '../main.dart';
import 'package:http/http.dart' as http;

import 'Api.dart';

Future<Active> activeWorkspace(ActiveWorkspace user) async {
  Active users = new Active();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/extendvalidity";
  final client = new http.Client();
  print(json.encode(user.toMap()));
  final response = await client.post(
    Uri.parse(url),
    headers: {HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.token}",},
    body: json.encode(user.toMap()),
  );


  if (response.statusCode == 200) {
    users = Active.fromJson(json.decode(response.body));
  }

  return users;
}


Future<Workspacelist> getallWorkspace() async {
  Workspacelist couponCode = new Workspacelist();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getallactiveworkspace";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    print(json.decode(response.body));
    couponCode = Workspacelist.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    couponCode.status = 0;
  } else {
    couponCode.status = 0;
  }
  return couponCode;
}

Future<Workspace> workspaceDetails(String workspaceId) async {
  Workspace couponCode = new Workspace();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/activeworkspace?WorkspaceID=$workspaceId";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers:<String, String> {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    print(json.decode(response.body));
    couponCode = Workspace.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    couponCode.status = 0;
  } else {
    couponCode.status = 0;
  }
  return couponCode;
}