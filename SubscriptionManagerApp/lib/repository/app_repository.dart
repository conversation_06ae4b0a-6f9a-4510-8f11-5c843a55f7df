import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';
import 'package:subscriptionmanager/Models/ViewUserPassword.dart';
import 'package:subscriptionmanager/Models/generate_ex_password.dart';
import 'package:subscriptionmanager/Models/get_all_apps_model.dart';
import '../main.dart';
import 'Api.dart';

Future<GetAllApps> getAllApps() async {
  GetAllApps getAllApps = new GetAllApps();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getallappilication";
  final client = new http.Client();
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    getAllApps = GetAllApps.fromJson(json.decode(response.body));
  } else {
    getAllApps.status = 0;
  }
  return getAllApps;
}

Future<ViewUsers> viewUserPassword(int appId, String userName) async {
  ViewUsers viewUserPassword = new ViewUsers();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getuserbyname?ApplicationID=$appId&username=$userName";
  final client = new http.Client();
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    viewUserPassword = ViewUsers.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    viewUserPassword.status = 0;
  } else {
    viewUserPassword.status = 0;
  }
  return viewUserPassword;
}

Future<GenerateExPassword> generateExPassword() async {
  GenerateExPassword generateExPassword = new GenerateExPassword();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/generatepassword";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: '${MyApp.token}'
    },
  );

  if (response.statusCode == 200) {
    generateExPassword =
        GenerateExPassword.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    generateExPassword.message = "Authentication failed";
  } else {
    generateExPassword.status = 0;
    generateExPassword.message = "Failed to generate password";
  }
  return generateExPassword;
}
