import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:nb_utils/nb_utils.dart';
import 'package:subscriptionmanager/Models/Activeworkspace.dart';
import 'package:subscriptionmanager/Models/CodeDeleteResponse.dart';
import 'package:subscriptionmanager/Models/CouponCode.dart';
import 'package:subscriptionmanager/Models/GetCouponCodes.dart';
import 'package:subscriptionmanager/Models/GetOfflineActivationKeyCode.dart';
import 'package:subscriptionmanager/Models/LoginRequest.dart';
import 'package:subscriptionmanager/Models/OfflineActivationCodes.dart';
import 'package:subscriptionmanager/Models/Users.dart';
import 'package:subscriptionmanager/Models/ChangePassword.dart';
import 'package:subscriptionmanager/Models/ViewOfflineActivationCodes.dart';

import '../main.dart';
import 'Api.dart';

Future<User> login(LoginRequestModel user) async {
  User users = new User();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/authenticatevendor";
  final client = new http.Client();
  print(json.encode(user.toMap()));
  final response = await client.post(
    Uri.parse(url),
    headers: {HttpHeaders.contentTypeHeader: 'application/json'},
    body: json.encode(user.toMap()),
  );

  //print(response.statusCode);
  if (response.statusCode == 200) {
    //print(json.decode(response.body));
    users = User.fromJson(json.decode(response.body));
    //print(json.decode(response.body));
    //print("Successfully Registered");
  } else {
    users.message = json.decode(response.body)["message"];
  }
  return users;
}

Future<CouponCode> activationByCoupenCode(GenerateCouponRequest generateCouponRequest) async {
  CouponCode couponCode = new CouponCode();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/generateactivationcode?validity=${generateCouponRequest.validityDays}&applicationId=${generateCouponRequest.applicationId}";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    couponCode = CouponCode.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    couponCode.message = "Authentication failed ";
  } else {
    couponCode.message = "Coupon code generation is failed ";
  }
  return couponCode;
}

Future<GetCouponCodes> viewCoupenCodes() async {
  GetCouponCodes couponCode = new GetCouponCodes();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getactivationcode";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    print(json.decode(response.body));
    couponCode = GetCouponCodes.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    couponCode.status = 0;
  } else {
    couponCode.status = 0;
  }
  return couponCode;
}

Future<GetOfflineActivationCode> viewOfflineCodes() async {
  GetOfflineActivationCode getOfflineActivationCode =
      new GetOfflineActivationCode();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getallactivationkey";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    getOfflineActivationCode =
        GetOfflineActivationCode.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    getOfflineActivationCode.status = 0;
  } else {
    getOfflineActivationCode.status = 0;
  }
  return getOfflineActivationCode;
}

Future<Passwords> changePassword(Passwords passwords) async {
  Passwords password = new Passwords();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/changepasswordvendor";
  final client = new http.Client();
  final response = await client.put(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
    body: json.encode(passwords.toMap()),
  );
  if (response.statusCode == 200) {
    password = Passwords.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    password.message = "Authentication failed";
  } else {
    password.message = jsonDecode(response.body)['message'];
  }
  return password;
}

Future<OfflineActivationCode> createOfflineActivationKey(
    OfflineActivationCode offlineActivationCodeRequest) async {
  OfflineActivationCode offlineActivationCode = new OfflineActivationCode();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/createactivationkey";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
    body: json.encode(offlineActivationCodeRequest.toMap()),
  );
  if (response.statusCode == 200) {
    offlineActivationCode =
        OfflineActivationCode.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    offlineActivationCode.message = "Authentication failed";
  } else {
    offlineActivationCode.message = jsonDecode(response.body)['message'];
  }
  return offlineActivationCode;
}

Future<GetOfflineActivationKeyCode> viewOfflineActivationKeyCode(int id) async {
  GetOfflineActivationKeyCode tempCode = new GetOfflineActivationKeyCode();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/getactivationkeycode?id=$id";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    tempCode = GetOfflineActivationKeyCode.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    tempCode.status = 0;
    tempCode.message = "Authentication failed";
  } else {
    tempCode.status = 0;
    tempCode.message = json.decode(response.body)['message'];
  }
  return tempCode;
}

Future<DeleteCodeResponse> deleteOfflineActivationKeyCode(int id) async {
  DeleteCodeResponse deleteCode = new DeleteCodeResponse();
  final String url =
      "${Api.base_url}/${Api.version}/vendorsmobile/deleteactivationkey?id=$id";
  print(url);
  final client = new http.Client();
  //print(json.encode(activation.toMap()));
  final response = await client.delete(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.token}",
    },
  );
  if (response.statusCode == 200) {
    deleteCode = DeleteCodeResponse.fromJson(json.decode(response.body));
  } else if (response.statusCode == 401) {
    MyApp.isAuthorized = false;
    deleteCode.status = 0;
    deleteCode.message = "Authentication failed";
  } else {
    deleteCode = DeleteCodeResponse.fromJson(json.decode(response.body));
  }
  return deleteCode;
}
