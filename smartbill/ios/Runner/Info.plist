<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>NembooBill</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>We need access to Bluetooth to connect with external devices such as Bluetooth-enabled printers for printing receipts.</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>We need access to Bluetooth to establish connections with nearby devices for seamless communication within our app.</string>
	<key>NSCameraUsageDescription</key>
	<string>We need access to the camera to capture photos of products for use in your receipts and invoices within the app.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Your justification for location access both when in use and in the background</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>Your justification for continuous location access in the background</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>We need access to your location for better user experience.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>We needs access to the microphone for voice chat.</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>We need access to your photo library to allow you to choose photos for certain features in the app.</string>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>processing</string>
		<string>remote-notification</string>
	</array>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>com.apple.developer.siri</key>
	<true/>
	<key>Permitted background task scheduler identifiers</key>
	<array>
    <string>com.cloudstier.autosync</string>
    <string>com.cloudstier.manualsync</string>
	</array>
</dict>
</plist>
