{"status": 1, "message": "Success", "sales": [{"id": 1, "salesID": "sale_001", "invoiceID": "INV001", "invoiceNo": "1", "date": "2024-07-25T10:30:00.000Z", "totalAmount": "547.00", "paymentType": "1", "paymentTypeName": "Cash", "paymentID": "payment_001", "paymentCategoryID": 1, "taxMode": 1, "workspaceID": "ws_001", "isActive": true, "customerName": "<PERSON>", "customerID": "cust_001", "mobileNo": "9876543210", "isActiveSale": 1, "tableID": "table_001", "tableName": "Table 1", "createdBy": "user_001", "discountFormula": "0", "discountPrice": "0.00", "transactionQty": "3", "orderType": 1, "onlinePlatformID": null, "onlineRefNo": null, "paymentInvoice": "PAY001", "salesCategoryId": "cat_001", "salesCatName": "<PERSON>e In", "shiftId": "shift_001", "shiftName": "Morning Shift", "firstName": "<PERSON>", "deliveryDateAndTime": null, "salesStatus": 1, "parcelAmount": "25.00"}, {"id": 2, "salesID": "sale_002", "invoiceID": "INV002", "invoiceNo": "2", "date": "2024-07-25T12:15:00.000Z", "totalAmount": "248.00", "paymentType": "2", "paymentTypeName": "Card", "paymentID": "payment_002", "paymentCategoryID": 2, "taxMode": 1, "workspaceID": "ws_001", "isActive": true, "customerName": "<PERSON>", "customerID": "cust_002", "mobileNo": "9876543211", "isActiveSale": 1, "tableID": "table_002", "tableName": "Table 2", "createdBy": "user_002", "discountFormula": "10", "discountPrice": "20.00", "transactionQty": "2", "orderType": 1, "onlinePlatformID": null, "onlineRefNo": null, "paymentInvoice": "PAY002", "salesCategoryId": "cat_001", "salesCatName": "<PERSON>e In", "shiftId": "shift_001", "shiftName": "Morning Shift", "firstName": "<PERSON>", "deliveryDateAndTime": null, "salesStatus": 1, "parcelAmount": "0.00"}, {"id": 3, "salesID": "sale_003", "invoiceID": "INV003", "invoiceNo": "3", "date": "2024-07-25T14:45:00.000Z", "totalAmount": "178.00", "paymentType": "3", "paymentTypeName": "UPI", "paymentID": "payment_003", "paymentCategoryID": 3, "taxMode": 1, "workspaceID": "ws_001", "isActive": true, "customerName": "<PERSON>", "customerID": "cust_003", "mobileNo": "9876543212", "isActiveSale": 1, "tableID": "table_004", "tableName": "Counter", "createdBy": "user_002", "discountFormula": "0", "discountPrice": "0.00", "transactionQty": "2", "orderType": 2, "onlinePlatformID": null, "onlineRefNo": null, "paymentInvoice": "PAY003", "salesCategoryId": "cat_002", "salesCatName": "Take Away", "shiftId": "shift_001", "shiftName": "Morning Shift", "firstName": "<PERSON>", "deliveryDateAndTime": null, "salesStatus": 1, "parcelAmount": "15.00"}, {"id": 4, "salesID": "sale_004", "invoiceID": "INV004", "invoiceNo": "4", "date": "2024-07-25T16:20:00.000Z", "totalAmount": "398.00", "paymentType": "1", "paymentTypeName": "Cash", "paymentID": "payment_004", "paymentCategoryID": 1, "taxMode": 1, "workspaceID": "ws_001", "isActive": true, "customerName": "<PERSON>", "customerID": "cust_004", "mobileNo": "9876543213", "isActiveSale": 1, "tableID": "table_003", "tableName": "Table 3", "createdBy": "user_003", "discountFormula": "5", "discountPrice": "20.00", "transactionQty": "3", "orderType": 1, "onlinePlatformID": null, "onlineRefNo": null, "paymentInvoice": "PAY004", "salesCategoryId": "cat_001", "salesCatName": "<PERSON>e In", "shiftId": "shift_002", "shiftName": "Evening Shift", "firstName": "<PERSON>", "deliveryDateAndTime": null, "salesStatus": 1, "parcelAmount": "0.00"}]}