{"status": 1, "message": "Success", "transactions": [{"id": 1, "transactionID": "trans_001", "salesID": "sale_001", "productID": "prod_001", "productName": "Margherita Pizza", "price": "299.00", "actualPrice": "299.00", "quantity": "1", "amount": "314.95", "amountWithoutGst": "299.00", "productType": "FOOD", "toppingId": null, "cgst": "2.5", "cGstAmount": "7.48", "sgst": "2.5", "sGstAmount": "7.47", "workspaceID": "ws_001", "isActive": true, "isActiveSale": 1, "tableID": "table_001", "tableName": "Table 1", "categoryID": "cat_001", "discountPrice": "0.00", "discountFormula": "0", "parcelAmount": "10.00", "parcelAmountWithoutGst": "10.00", "notes": "", "priceTypeId": "price_type_001"}, {"id": 2, "transactionID": "trans_002", "salesID": "sale_001", "productID": "prod_002", "productName": "Chicken Burger", "price": "199.00", "actualPrice": "199.00", "quantity": "1", "amount": "224.75", "amountWithoutGst": "214.00", "productType": "FOOD", "toppingId": null, "cgst": "2.5", "cGstAmount": "5.35", "sgst": "2.5", "sGstAmount": "5.35", "workspaceID": "ws_001", "isActive": true, "isActiveSale": 1, "tableID": "table_001", "tableName": "Table 1", "categoryID": "cat_001", "discountPrice": "0.00", "discountFormula": "0", "parcelAmount": "15.00", "parcelAmountWithoutGst": "15.00", "notes": "", "priceTypeId": "price_type_001"}, {"id": 3, "transactionID": "trans_003", "salesID": "sale_001", "productID": "prod_004", "productName": "Coca Cola", "price": "49.00", "actualPrice": "49.00", "quantity": "1", "amount": "54.88", "amountWithoutGst": "49.00", "productType": "BEVERAGE", "toppingId": null, "cgst": "6.0", "cGstAmount": "2.94", "sgst": "6.0", "sGstAmount": "2.94", "workspaceID": "ws_001", "isActive": true, "isActiveSale": 1, "tableID": "table_001", "tableName": "Table 1", "categoryID": "cat_001", "discountPrice": "0.00", "discountFormula": "0", "parcelAmount": "0.00", "parcelAmountWithoutGst": "0.00", "notes": "", "priceTypeId": "price_type_001"}, {"id": 4, "transactionID": "trans_004", "salesID": "sale_002", "productID": "prod_003", "productName": "<PERSON>", "price": "149.00", "actualPrice": "149.00", "quantity": "1", "amount": "156.45", "amountWithoutGst": "149.00", "productType": "FOOD", "toppingId": null, "cgst": "2.5", "cGstAmount": "3.73", "sgst": "2.5", "sGstAmount": "3.72", "workspaceID": "ws_001", "isActive": true, "isActiveSale": 1, "tableID": "table_002", "tableName": "Table 2", "categoryID": "cat_001", "discountPrice": "10.00", "discountFormula": "10", "parcelAmount": "5.00", "parcelAmountWithoutGst": "5.00", "notes": "", "priceTypeId": "price_type_001"}, {"id": 5, "transactionID": "trans_005", "salesID": "sale_002", "productID": "prod_005", "productName": "Chocolate Cake", "price": "129.00", "actualPrice": "129.00", "quantity": "1", "amount": "135.45", "amountWithoutGst": "129.00", "productType": "DESSERT", "toppingId": null, "cgst": "2.5", "cGstAmount": "3.23", "sgst": "2.5", "sGstAmount": "3.22", "workspaceID": "ws_001", "isActive": true, "isActiveSale": 1, "tableID": "table_002", "tableName": "Table 2", "categoryID": "cat_001", "discountPrice": "10.00", "discountFormula": "10", "parcelAmount": "8.00", "parcelAmountWithoutGst": "8.00", "notes": "", "priceTypeId": "price_type_001"}]}