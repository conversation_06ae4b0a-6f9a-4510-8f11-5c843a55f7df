{"status": 1, "message": "Success", "products": [{"id": 1, "productID": "prod_001", "productSalesID": 1, "productName": "Margherita Pizza", "productPrice": "299.00", "mrp": "349.00", "productKey": "pizza", "toppingGroupId": "topping_group_001", "priceType": 1, "cgst": "2.5", "sgst": "2.5", "productImage": "margherita_pizza.jpg", "workspaceID": "ws_001", "isActive": true, "productDate": "2024-01-01T00:00:00.000Z", "dynamicQuantity": 1, "enableInventory": 1, "productKeywordID": "keyword_001", "productFor": "DINE_IN", "parcelAmount": "10.00"}, {"id": 2, "productID": "prod_002", "productSalesID": 2, "productName": "Chicken Burger", "productPrice": "199.00", "mrp": "229.00", "productKey": "burger", "toppingGroupId": "topping_group_002", "priceType": 1, "cgst": "2.5", "sgst": "2.5", "productImage": "chicken_burger.jpg", "workspaceID": "ws_001", "isActive": true, "productDate": "2024-01-01T00:00:00.000Z", "dynamicQuantity": 1, "enableInventory": 1, "productKeywordID": "keyword_002", "productFor": "DINE_IN", "parcelAmount": "15.00"}, {"id": 3, "productID": "prod_003", "productSalesID": 3, "productName": "<PERSON>", "productPrice": "149.00", "mrp": "179.00", "productKey": "salad", "toppingGroupId": null, "priceType": 1, "cgst": "2.5", "sgst": "2.5", "productImage": "caesar_salad.jpg", "workspaceID": "ws_001", "isActive": true, "productDate": "2024-01-01T00:00:00.000Z", "dynamicQuantity": 1, "enableInventory": 1, "productKeywordID": "keyword_003", "productFor": "DINE_IN", "parcelAmount": "5.00"}, {"id": 4, "productID": "prod_004", "productSalesID": 4, "productName": "Coca Cola", "productPrice": "49.00", "mrp": "60.00", "productKey": "beverage", "toppingGroupId": null, "priceType": 1, "cgst": "6.0", "sgst": "6.0", "productImage": "coca_cola.jpg", "workspaceID": "ws_001", "isActive": true, "productDate": "2024-01-01T00:00:00.000Z", "dynamicQuantity": 1, "enableInventory": 1, "productKeywordID": "keyword_004", "productFor": "DINE_IN", "parcelAmount": "0.00"}, {"id": 5, "productID": "prod_005", "productSalesID": 5, "productName": "Chocolate Cake", "productPrice": "129.00", "mrp": "149.00", "productKey": "dessert", "toppingGroupId": null, "priceType": 1, "cgst": "2.5", "sgst": "2.5", "productImage": "chocolate_cake.jpg", "workspaceID": "ws_001", "isActive": true, "productDate": "2024-01-01T00:00:00.000Z", "dynamicQuantity": 1, "enableInventory": 1, "productKeywordID": "keyword_005", "productFor": "DINE_IN", "parcelAmount": "8.00"}], "productKeywords": [{"id": 1, "keywordID": "keyword_001", "keywordName": "pizza", "workspaceID": "ws_001"}, {"id": 2, "keywordID": "keyword_002", "keywordName": "burger", "workspaceID": "ws_001"}, {"id": 3, "keywordID": "keyword_003", "keywordName": "salad", "workspaceID": "ws_001"}, {"id": 4, "keywordID": "keyword_004", "keywordName": "beverage", "workspaceID": "ws_001"}, {"id": 5, "keywordID": "keyword_005", "keywordName": "dessert", "workspaceID": "ws_001"}], "priceTypes": [{"id": 1, "priceTypeID": "price_type_001", "priceTypeName": "Regular", "workspaceID": "ws_001"}, {"id": 2, "priceTypeID": "price_type_002", "priceTypeName": "Large", "workspaceID": "ws_001"}]}