{"status": 1, "message": "Success", "workspaces": [{"id": 1, "workspaceId": "ws_001", "workspaceName": "Demo Restaurant", "userId": "user_001", "userName": "<EMAIL>", "isActive": true, "expiryDate": "2025-12-31T23:59:59.000Z", "roleId": 1, "salesCount": 425, "workspaceSettings": [{"id": 1, "settingKey": "currency", "settingValue": "INR", "workspaceId": "ws_001"}, {"id": 2, "settingKey": "taxMode", "settingValue": "1", "workspaceId": "ws_001"}, {"id": 3, "settingKey": "invoicePrefix", "settingValue": "INV", "workspaceId": "ws_001"}, {"id": 4, "settingKey": "printAfterSale", "settingValue": "true", "workspaceId": "ws_001"}, {"id": 5, "settingKey": "enableInventory", "settingValue": "true", "workspaceId": "ws_001"}], "shopDetails": {"id": 1, "shopName": "Demo Restaurant", "shopAddress": "123 Main Street, City Center", "shopPhone": "9876543210", "shopEmail": "<EMAIL>", "gstNumber": "29ABCDE1234F1Z5", "workspaceId": "ws_001"}, "shopTables": [{"id": 1, "tableId": "table_001", "tableName": "Table 1", "tableCapacity": 4, "isActive": true, "workspaceId": "ws_001"}, {"id": 2, "tableId": "table_002", "tableName": "Table 2", "tableCapacity": 6, "isActive": true, "workspaceId": "ws_001"}, {"id": 3, "tableId": "table_003", "tableName": "Table 3", "tableCapacity": 2, "isActive": true, "workspaceId": "ws_001"}, {"id": 4, "tableId": "table_004", "tableName": "Counter", "tableCapacity": 1, "isActive": true, "workspaceId": "ws_001"}]}]}