{"status": 1, "message": "Success", "generalSettings": [{"id": 1, "settingKey": "currency", "settingValue": "INR", "settingName": "<PERSON><PERSON><PERSON><PERSON>", "settingDescription": "Default currency for the application"}, {"id": 2, "settingKey": "taxMode", "settingValue": "1", "settingName": "Tax Mode", "settingDescription": "Tax calculation mode (1=GST, 2=VAT)"}, {"id": 3, "settingKey": "invoicePrefix", "settingValue": "INV", "settingName": "Invoice Prefix", "settingDescription": "Prefix for invoice numbers"}, {"id": 4, "settingKey": "printAfterSale", "settingValue": "true", "settingName": "Print After Sale", "settingDescription": "Automatically print receipt after sale"}, {"id": 5, "settingKey": "enableInventory", "settingValue": "true", "settingName": "Enable Inventory", "settingDescription": "Enable inventory management"}, {"id": 6, "settingKey": "FileManagerUrl", "settingValue": "LOCAL_FILE_MANAGER", "settingName": "File Manager URL", "settingDescription": "URL for file management service"}, {"id": 7, "settingKey": "BucketName", "settingValue": "local-bucket", "settingName": "Bucket Name", "settingDescription": "Storage bucket name"}], "workspaceSettings": [{"id": 1, "workspaceID": "ws_001", "settingKey": "businessName", "settingValue": "Demo Restaurant", "settingType": "TEXT"}, {"id": 2, "workspaceID": "ws_001", "settingKey": "businessAddress", "settingValue": "123 Main Street, City Center", "settingType": "TEXT"}, {"id": 3, "workspaceID": "ws_001", "settingKey": "businessPhone", "settingValue": "9876543210", "settingType": "TEXT"}, {"id": 4, "workspaceID": "ws_001", "settingKey": "businessEmail", "settingValue": "<EMAIL>", "settingType": "TEXT"}, {"id": 5, "workspaceID": "ws_001", "settingKey": "gstNumber", "settingValue": "29ABCDE1234F1Z5", "settingType": "TEXT"}, {"id": 6, "workspaceID": "ws_001", "settingKey": "defaultTaxRate", "settingValue": "5.0", "settingType": "NUMBER"}, {"id": 7, "workspaceID": "ws_001", "settingKey": "enableTableService", "settingValue": "true", "settingType": "BOOLEAN"}, {"id": 8, "workspaceID": "ws_001", "settingKey": "enableTakeaway", "settingValue": "true", "settingType": "BOOLEAN"}, {"id": 9, "workspaceID": "ws_001", "settingKey": "enableDelivery", "settingValue": "true", "settingType": "BOOLEAN"}]}