{"status": 1, "message": "Success", "users": [{"id": "user_001", "username": "<EMAIL>", "firstName": "Admin", "lastName": "User", "mobile": "9876543210", "email": "<EMAIL>", "token": "local_token_admin_001", "salesCount": 150, "workspaceDetails": [{"id": 1, "workspaceId": "ws_001", "workspaceName": "Demo Restaurant", "userId": "user_001", "userName": "<EMAIL>", "isActive": true, "expiryDate": "2025-12-31T23:59:59.000Z", "roleId": 1, "salesCount": 150, "permissions": [{"id": 1, "permissionName": "Sales Management", "isActive": true}, {"id": 2, "permissionName": "Product Management", "isActive": true}, {"id": 3, "permissionName": "Customer Management", "isActive": true}, {"id": 4, "permissionName": "Reports Access", "isActive": true}, {"id": 5, "permissionName": "Settings Management", "isActive": true}]}]}, {"id": "user_002", "username": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Cashier", "mobile": "9876543211", "email": "<EMAIL>", "token": "local_token_cashier_002", "salesCount": 75, "workspaceDetails": [{"id": 2, "workspaceId": "ws_001", "workspaceName": "Demo Restaurant", "userId": "user_002", "userName": "<EMAIL>", "isActive": true, "expiryDate": "2025-12-31T23:59:59.000Z", "roleId": 2, "salesCount": 75, "permissions": [{"id": 1, "permissionName": "Sales Management", "isActive": true}, {"id": 2, "permissionName": "Product Management", "isActive": false}, {"id": 3, "permissionName": "Customer Management", "isActive": true}, {"id": 4, "permissionName": "Reports Access", "isActive": false}, {"id": 5, "permissionName": "Settings Management", "isActive": false}]}]}, {"id": "user_003", "username": "<EMAIL>", "firstName": "<PERSON>", "lastName": "Manager", "mobile": "9876543212", "email": "<EMAIL>", "token": "local_token_manager_003", "salesCount": 200, "workspaceDetails": [{"id": 3, "workspaceId": "ws_001", "workspaceName": "Demo Restaurant", "userId": "user_003", "userName": "<EMAIL>", "isActive": true, "expiryDate": "2025-12-31T23:59:59.000Z", "roleId": 3, "salesCount": 200, "permissions": [{"id": 1, "permissionName": "Sales Management", "isActive": true}, {"id": 2, "permissionName": "Product Management", "isActive": true}, {"id": 3, "permissionName": "Customer Management", "isActive": true}, {"id": 4, "permissionName": "Reports Access", "isActive": true}, {"id": 5, "permissionName": "Settings Management", "isActive": false}]}]}]}