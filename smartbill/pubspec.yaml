name: smartbill
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.2.0+22

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_svg: ^2.0.10+1
  nb_utils: ^6.0.3
  cached_network_image: ^3.2.3
  image_picker: ^1.0.2
  path_provider: ^2.1.0
  path: ^1.8.2
  geolocator: ^11.0.0
  permission_handler: ^11.0.1
  image: ^4.1.3
  intl: ^0.18.1
  shared_preferences: ^2.2.0
  sqflite: ^2.2.8+4
  http: ^1.2.0
  share: ^2.0.4
  url_launcher: ^6.1.11
  uuid: ^4.1.0
  blue_thermal_printer:
    path: ../blue_thermal_printer
  internet_connection_checker: ^1.0.0+1
  collection: ^1.16.0
  pdf: ^3.8.4
  image_cropper: ^5.0.1
  dio: ^4.0.6
  background_fetch: ^1.2.2
  flutter_local_notifications: ^16.3.3
  open_filex: ^4.4.0
  fluttercontactpicker: ^4.7.0
  dropdown_button2: ^2.1.4
  # razorpay_flutter: ^1.3.6
  flutter_bootstrap: ^2.0.0
  barcode_scan2: ^4.2.4
  # The following for sqlite
  # sqflite: ^2.0.0+4
  sqflite_migration: ^0.3.0
  sqlite_viewer: ^1.0.5
  # The following for sqlite windows
  sqflite_common_ffi: ^2.2.5
  sqlite3: ^2.4.0
  # The following for to get device info
  device_info_plus: ^9.1.2
  android_id: ^0.3.6
  # Pin input field for otp
  pinput: ^4.0.0
  # The following for monthly year picker
  month_year_picker: ^0.3.0+1
  # The following for firebase integration
  firebase_core: ^2.24.2
  # The following for firebase push notification
  firebase_messaging: ^14.7.10
  # The following for firebase dynamic links
  firebase_dynamic_links: ^5.4.8
  #The following find custom notifications
  awesome_notifications: ^0.8.2
  #The following find verion rages
  pub_semver: ^2.1.4
  flutter_widget_from_html: ^0.14.11
  wakelock_plus: ^1.1.0

  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.5
  tuple: ^2.0.2

  

dev_dependencies:
  hive_generator: ^2.0.0
  build_runner: ^2.3.3
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # - images/dashboard/
    # - images/theme5/
    # - images/LikeButton/image/grey.jpg
    # - images/learner/
    # - images/defaultTheme/ic_mastercard.png
    - assets/images/
    - assets/svg/
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
