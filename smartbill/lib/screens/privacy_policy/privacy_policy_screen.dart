import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import '../../model/register_request.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/styles.dart';
enum content {privacypolicy,termsandconditions}
class PrivacyPolicyScreen extends StatefulWidget {
  content? contentType;
  PrivacyPolicyScreen({Key? key,this.contentType}) : super(key: key);

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  double width = 0.0;
  double height = 0.0;
  bool isLoading = false;
  String htmlContent = '';
  RegisterRequest? registerRequest;

  getPrivacyPolicyContent() async {
    setState(() {
      isLoading = true;
    });
    // await getGeneralSettingsByKeys("PrivacyPolicy").then((value) {
    //   if (value.status == 1) {
    //     htmlContent = value.result[0].keyValue;
    //   }
    // });
    if(widget.contentType==content.termsandconditions){

    htmlContent = htmlTermsAndCondictions;
    }else{
    htmlContent = htmlPrivacyPolicy;

    }
    setState(() {
      isLoading = false;
    });
  }

  @override
  void initState() {
    getPrivacyPolicyContent();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Container(
        decoration: const BoxDecoration(
          color: backgroundColor,
        ),
        child: Scaffold(
          backgroundColor: Colors.transparent,
          appBar: AppBar(
            title: Text(widget.contentType==content.termsandconditions?"Terms & Conditions":"Privacy Policy", style: headingTextStyle3),
          ),
          body: isLoading
              ? SizedBox(
                  height: height / 1.8,
                  width: width / 1.1,
                  child: const Center(
                    child: SizedBox(
                      height: 40,
                      width: 40,
                      child: CircularProgressIndicator(),
                    ),
                  ),
                )
              : Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Container(
                          padding: const EdgeInsets.all(10),
                          child: HtmlWidget(
                            htmlContent,
                          ),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
               
                    if (  widget.contentType==null) ...[
                      Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            RoundedButton(
                              width: width * 0.45,
                              title: "Cancel",
                              onPressed: () async {
                                Navigator.pop(context);
                              },
                            ),
                            RoundedButton(
                              width: width * 0.45,
                              title: "Accept",
                              onPressed: () async {
                                Navigator.pop(context, true);
                              },
                            ),
                          ],
                        ),
                      ),
                    ],
                    if(Platform.isIOS)...[
                      SizedBox(
                        height: 25,
                      )
                    ]
                  ],
                ),
        ));
  }
}


String htmlTermsAndCondictions='''

<!DOCTYPE html>
<html lang="en">
<head>
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet prefetch" type="text/css" as="style">
<link rel="stylesheet prefetch" as="style" href="https://vyaparwebfiles.vypcdn.in/css/intelInputCss/intlTelInput.min.css">
<link rel="stylesheet prefetch" as="style" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css">
<link rel="stylesheet prefetch" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />

<link type="text/css" href="https://vyaparapp.in/css/oldApp.css" rel="stylesheet prefetch" as="style">
<link type="text/css" href="https://vyaparapp.in/css/paymentrevamp.css?version=0.24" rel="stylesheet prefetch" as="style">
<link type="text/css" href="https://vyaparapp.in/css/login_panel.css?version=0.7" rel="stylesheet prefetch" as="style">
<link rel="stylesheet prefetch" href="https://vyaparwebfiles.vypcdn.in/css/layout-app/layout-app.css?v=0.1" as="style" />

<link rel="apple-touch-icon" sizes="57x57" href="/favicon/apple-icon-57x57.png?version=0.1">
<link rel="apple-touch-icon" sizes="60x60" href="/favicon/apple-icon-60x60.png?version=0.1">
<link rel="apple-touch-icon" sizes="72x72" href="/favicon/apple-icon-72x72.png?version=0.1">
<link rel="apple-touch-icon" sizes="76x76" href="/favicon/apple-icon-76x76.png?version=0.1">
<link rel="apple-touch-icon" sizes="114x114" href="/favicon/apple-icon-114x114.png?version=0.1">
<link rel="apple-touch-icon" sizes="120x120" href="/favicon/apple-icon-120x120.png?version=0.1">
<link rel="apple-touch-icon" sizes="144x144" href="/favicon/apple-icon-144x144.png?version=0.1">
<link rel="apple-touch-icon" sizes="152x152" href="/favicon/apple-icon-152x152.png?version=0.1">
<link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-icon-180x180.png?version=0.1">
<link rel="icon" type="image/png" sizes="192x192" href="/favicon/android-icon-192x192.png?version=0.1">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon-32x32.png?version=0.1">
<link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png?version=0.1">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png?version=0.1">
<link rel="manifest" href="/favicon/manifest.json">
<meta name="msapplication-TileColor" content="#ffffff">
<meta name="msapplication-TileImage" content="/favicon/ms-icon-144x144.png?version=0.1">
<meta name="theme-color" content="#ffffff">

<meta name="csrf-token" content="xu8Y6BS175FmZFdEtAWGrMjQPEsmimjEgDbYh4YY">

<link rel="canonical" href="https://vyaparapp.in/permission">

<link rel="stylesheet" href="https://vyaparwebfiles.vypcdn.in/css/permissions/permissions.css" />

</head>
<body>
<div class="main-content">
<div class="container">
<div class="row">
<div class="col s12">
<div class="permission-section">
<ul class="terms center-align">
<h1 style="font-size: 1.64rem !important;"><b>NembooBill Terms and Conditions  </b></h1><br><hr>
<p> Simply NembooBill Apps Private Limited (“the Company”) having its registered address #3-80,Kamaraj Nagar,Kasinayakkan Patti,Kunichi Post Tirupattur,Tirupattur District,Tamilnadu,India-635602 recognizes the need for appropriate protection and management of any information shared with the Company or on NembooBill Platform.</p>
<p> By using or accessing the NembooBill platform in any manner or opening an Account, it is acknowledged that the present Policy is accepted and it further consents that the Company will collect, use, disclose, share and transfer information and personal data including but not limited to sensitive Personal Information. This Policy is an electronic record in the form of an electronic contract formed under the Information Technology Act, 2000 and the rules made thereunder. </p>
<p> However, in case the User does not intend to share some of the Information as referred herein below for use by the Company as stated, the User is free to opt-out or not use the NembooBill platform and/or the Products and Services by deleting the Account and in such a case, are not required to give consent as per the present Privacy Policy </p>
<p> In the case in future it is felt that the Information or a part thereof as collected should not be used or should be deleted, consent granted in the present Policy can be withdrawn, by writing to the Company seeking to withdraw the consent so granted or deleting the Account, as the case may be. However, in case the consent for the Company to use the Information as provided in the present Policy is withdrawn, the Products and Services cannot be offered to the User by the Company. </p>
</ul>
<ul class="terms center-align">
<h3>Modification of privacy policy</h3><br><hr>
<p>These policies may be updated from time to time in the future. The User should agree to review our privacy policies regularly by visiting this page. Continued access to or use of the service will mean that the User agrees to the change.
</p>
</ul>
<ul class="terms center-align">
<h3>When this privacy statement applies</h3><br><hr>
<p>
Its liability to the User is limited to those who have downloaded the NembooBill application and logged in by clicking on the “I Agree/Login” button for Terms and Conditions or has explicitly consented to the policy on any other product/service page offered to the User
</p>
</ul>
<ul class="terms center-align">
<h3>How is the information collected, used, disclosed, shared and stored</h3><br><hr>
<p>
1. The Company collects, uses, discloses, shares and transfers Information, including but not limited to User content, other content, communications, information etc. provided by Users when the Users access or use the NembooBill platform for the Products and Services. By use of the NembooBill platform and/or Products and Services, express consent and permission is granted to the Company for having access to, using, processing, disclosing, sharing and transferring of information in accordance with this Policy and the Terms of Use.
</p>
<p>
2. For the purpose of these Terms, the term “Personal Information” shall mean any information provided by the User and shall include, without limitation User content, any and all documents, details, information etc. shared with the Company by the User and the One Time Password (OTP) received by the User from the government authorities/good and service tax authority for authenticating/verifying the goods and service tax registration process; in furtherance to the Terms of Use.
</p>
<p>
For the purpose of these Terms, the term “Non-Personal Information” shall mean information that does not specifically identify an individual but includes information from such as the type of internet browser used, mobile device used, computer or mobile device’s unique device ID, Internet Service Provider (ISP) and Internet Protocol (IP) address. The Company may also collect Non-Personal Information that is provided.
</p>
<p>
Personal and Non-Personal Information together is referred to as “Information”. The Company may collect Information when registration or opening of an Account happens on the NembooBill platform, when the features or the Products and Services are used, when the User shares the User content with the Company and when the User interacts with the customer care team including the details of the message or chats etc.
</p>
<p>
3. The Company may use cookies to monitor usage including, without limitation, to provide useful features to simplify experiences for the User on the NembooBill platform, like remembering login ID and certain Personal Information and to deliver relevant content based on preferences, usage patterns and location.
</p>
<p>
4. There could also arise a requirement of providing credit/debit card when the User is required to payment Government Fees and/or Facilitation Fees as per the Terms of Use. The Company or any third party providing services related to payment may use this information, such as account and authentication information and billing, delivery and contact details. The Company shall in no case be liable for any loss or damage sustained by the User arising out of any disclosure (inadvertent or otherwise) of any information relating to bank account, credit or debit card details in the course of any online transactions or payments made relating to Government Fees and/or Facilitation Fees.
</p>
<p>
5. The Company collects information about the User’s use of the NembooBill platform and/or Products and Service, such as the features used, any activity carried out, the actions that are taken, the time, frequency and duration of the said activities.
</p>
<p>
6. The Company is required to collect information from and about the computers, tablets, phones and other web-connected devices used and the Company then combines this information across different devices that are used.
</p>
<p>
Information that the Company obtains from these devices includes:
</p>
<p>
a. Information including but not limited to the operating system, hardware and software versions, battery level, signal strength, available storage space, browser type, app and file names and types, and plugins, name of mobile operator or ISP, language, time zone, mobile phone number, IP address, connection speed and, in some cases, information about other devices that are nearby or on user network etc.
</p>
<p>
b. Information including but not limited to access to beacons and mobile phone masts, access to GPS location, network, camera or photos, data from cookies stored on user devices, storage space or disk on the device etc.
</p>
<p>
c. Location-related information – such as current location to provide, personalized and improved use of the NembooBill platform and/or Products and Services. Location-related information can be based on things such as precise device location, IP addresses etc.
</p>
</ul>
<ul class="terms center-align">
<h3>Google permissions usage</h3><br><hr>
<p><span class="for">Gmail Mail Send (gmail.send)</a> </span> Allows you to send your invoices and report PDFs to your customers.
</p>
<p><span class="for"></span> Allows you to take backup of your app data on your email.</p>
<p><span class="for">Google Drive file (drive.file) </span> Allow you to take backup of your app data on your google drive.</p>
<p><span class="for"></span> Allows the app to take regular backup of your app data automatically.</p>
<p><span class="for">Google Contacts (contacts.readonly) </span> Allow users to use Google Contacts for adding parties (customers) on the accounting software.
</p>
</ul>
<ul class="terms center-align">
<h3>How NembooBill stores and shares google user data </h3><br><hr>
<p>
NembooBill does not store any Google user data. Whenever NembooBill fetches this data, it gets shown to the user while using the feature. The user chooses the data he wants to use for import and that gets used to create customers/vendors for him in the software. NembooBill will not store this data for any further use. NembooBill will also not share the user's Google authentication data with anyone. Also, NembooBill will not store this data anywhere.
</p>
</ul>
<ul class="terms center-align">
<h3>Collection of financial sms information</h3><br><hr>
<p>
We don’t collect, read or store your personal SMS from your inbox. We collect and monitor only financial SMS sent by 6-digit alphanumeric senders from your inbox which helps us in identifying the various bank accounts that you may be holding, cash flow patterns, description and amount of the transactions undertaken by you as a user to help us perform a credit risk assessment which enables us to determine your risk profile and to provide you with the appropriate credit analysis. This process will enable you to take financial facilities from the regulated financial entities available on the Platform. This Financial SMS data also includes your historical data.
</p>
</ul>
<ul class="terms center-align">
<h3>Collection of device location and device information </h3><br><hr>
<p>
We collect and monitor the information about the location of your device to provide serviceability of your loan application, to reduce the risk associated with your loan application and to provide pre-approved customised loan offers. This also helps us to verify the address, make a better credit risk decision and expedite know your customer (KYC) process. Information the App collects, and its usage depends on how you manage your privacy controls on your device. When you install the App, we store the information we collect with unique identifiers tied to the device you are using. We collect information from the device when you download and install the App and explicitly seek permissions from You to get the required information from the device. The information we collect from your device includes the hardware model, build model, RAM, storage; unique device identifiers like IMEI, serial number, SSAID; SIM information that includes network operator, roaming state, MNC and MCC codes, WIFI information that includes MAC address and mobile network information to uniquely identify the devices and ensure that no unauthorized device acts on your behalf to prevent frauds
</p>
</ul>
<ul class="terms center-align">
<h3>Collection of installed applications </h3><br><hr>
<p>
We collect a list of the installed applications’ metadata information which includes the application name, package name, installed time, updated time, version name and version code of each installed application on your device to assess your credit worthiness and enrich your profile with pre-approved customized loan offers.
</p>
</ul>
<ul class="terms center-align">
<h3>Storage</h3><br><hr>
<p>
We require storage permission so that your KYC and other relevant documents can be securely downloaded and saved on your phone. You can then easily upload the correct KYC related documents for faster loan application details filling and disbursal process. This ensures that you are provided with a seamless experience while using the application.
</p>
</ul>
<ul class="terms center-align">
<h3>Camera</h3><br><hr>
<p>
We require the camera information permission to provide you with an easy/smooth experience and to enable you to click photos of your KYC documents along with other requisite documents and upload the same on the App during your loan application journey.
</p>
</ul>
<ul class="terms center-align">
<h3>Collection of other non-personal information</h3><br><hr>
<p>
We automatically track certain information about you based upon your behaviour on our Platform. We use this information to do internal research on our users’ demographics, interests, and behaviour to better understand, protect and serve our users and improve our services. This information is compiled and analysed on an aggregated basis. We also collect your Internet Protocol (IP) address and the URL used by you to connect your computer to the internet, etc. This information may include the URL that you just came from (whether this URL is on our Website or not), which URL you next go to (whether this URL is on our Website or not), your computer browser information, and your IP address. Cookies are small data files that a Website stores on Your computer. We will use cookies on our Website similar to other lending websites/apps and online marketplace websites/apps. Use of this information helps Us identify You in order to make our Website more user friendly. Most browsers will permit You to decline cookies but if You choose to do this it might affect service on some parts of Our Website. If you choose to make a purchase through the Platform, we collect information about your buying behaviour. We retain this information as necessary to resolve disputes, provide customer support and troubleshoot problems as permitted by law. if you send us personal correspondence, such as emails or letters, or if other users or third parties send us correspondence about your activities or postings on the Website, we collect such information into a file specific to you.
</p>
</ul>
<ul class="terms center-align">
<h3>Link to third-party sdk</h3><br><hr>
<p>
The NembooBill Platform has a link to a registered third party SDK that collects data on our behalf and data is stored on a secured server to perform a credit risk assessment. We ensure that our third-party service provider takes extensive security measures in order to protect your personal information against loss, misuse or alteration of the data. Our third-party service provider employs separation of environments and segregation of duties and has strict role-based access control on a documented, authorized, need to-use basis. The stored data is protected and stored by application-level encryption. They enforce key management services to limit access to data. Furthermore, our registered third party service provider provides hosting security – they use industry leading anti-virus, anti-malware, intrusion prevention systems, intrusion detection systems, file integrity monitoring, and application control solutions
</p>
</ul>
<ul class="terms center-align">
<h3>How NembooBill uses user data </h3><br><hr>
<p>
The Company may use the Information for:
</p>
<p>
1. Providing access to the NembooBill platform and/or use of the Products and Services.
</p>
<p>
2. Manage Account with the Company.
</p>
<p>
3. Authentication by using One Time Password received from the for the Purpose including but not limited to government authorities/good and service tax authority for authenticating/verifying the goods and service tax registration process, Bureau check or any other product/service availed by the user from time to time on NembooBill.
</p>
<p>
4. Send SMS for authenticating transactions via One Time Password (OTP) etc.
</p>
<p>
5. Conduct general survey or analysis of Accounts or otherwise.
</p>
<p>
6. Develop, test and improve the NembooBill platform and/or Products and Services, testing and troubleshooting new products and features, removing any glitches including technical problems, improving the experience of navigating and carrying out transactions and improving the services and content, Products and Service on the NembooBill platform.
</p>
<p>
7. Based on Information, share sponsored content and show ads, including relating to third parties, that the Company thinks will be relevant.
</p>
</ul>
<ul class="terms center-align">
<h3>
Sharing of personal information
</h3><br><hr>
<p>
The Company may disclose or share Personal Information including but not limited to User Content in the following ways:
</p>
<p>
1. Disclosure or sharing with government authorities including but not limited to the Goods and Service Tax Authority, MSME Authority, FSSAI Authority.
Information including Personal Information may be shared with and disclosed to government authorities including but not limited to the Goods and Service Tax Authority, MSME Authority, FSSAI Authority, in order to enable providing the Products and Services.
</p>
<p>
2. Disclosure to Government authorities or as required by law information may be shared with Government and/or statutory authorities or under court orders or other legal processes; to establish or exercise legal rights; to defend against legal claims; or as otherwise required by law. The Company warrants that such information will be disclosed only in accordance with applicable directions, instructions, laws and regulations. </p>
<p>
3. Disclosure to third parties the Company may provide, disclose and share information including Personal Information that is collected to third-party service providers, database service providers, backup and disaster recovery service providers, email service providers and also make it accessible to technology partners, subsidiaries and affiliates, including outside India, to help ensure availability of NembooBill platform and providing the Products and Services
</p>
<p>
4. Disclosure to prospective Acquirers The Company may disclose information including Personal Information to an acquirer, assignee or other successor entity in connection with a sale, merger, or reorganization of all or substantially all of the equity or business of the Company.
</p>
<p>
5. Disclosure to Facilitate Payment on Payment Gateways In order to process payment transactions, the Company may direct to a payment gateway service provider of its own choice, which may have access to the Personal Information provided while making such payment.
</p>
</ul>
<ul class="terms center-align">
<h3>Protection of information</h3><br><hr>
<p>
The Company, on a best effort basis, shall attempt to take adequate measures to protect the security of Personal Information and to ensure that the choices for its intended use are honoured. The Company also takes adequate precautions, on a best effort basis, to protect data from loss, misuse, unauthorized access or disclosure, alteration or destruction.
</p>
<p>
The Company uses industry standards of physical, technical and administrative security measures to keep Information, on best effort basis, secure although is unequivocally understood and agreed that the same cannot be foolproof since no security system is impassable and the Company does not guarantee that the Information may be absolutely free from invasion.
</p>
<p>
Please note that e-mails, messaging and means of communication with the Company may not be encrypted, and therefore the Company advises not to communicate any confidential information through these means.
</p>
<p>
The Company is not responsible for any breach of security or for any actions or inactions of any Users or other third parties including but not limited to government authorities that receive Information.
</p>
<p>
The User is solely responsible for maintaining the confidentiality of its username and password. To ensure the safety of Personal Information, it is advised that the username and password should not be shared with anyone. The Company would not be liable or responsible for any breach of this covenant for any reason.
</p>
<p>
The Company would retain Personal Information only as long as the entity or User to whom the said Personal Information belongs has an active Account or as permitted/required by applicable law. Generally, Personal Information, upon deletion of a related Account, is also deleted unless the same is required to comply with any legal requirements, fraud prevention or assistance in any enquiry, for resolving disputes, to protect interests of the Company, to continue to develop and improve the Products and Services
</p>
</ul>
<ul class="terms center-align">
<h3>Grievance redressal mechanism</h3><br><hr>
<p>
In accordance with the Information Technology Act, 2000 and Rules made thereunder, the contact details for raising grievances or obtaining any further information or enquiry, if any are provided below:
</p>
<p>
<span class="for">Address: </span> #3-80,Kamaraj Nagar,Kasinayakkan Patti,Kunichi Post Tirupattur,Tirupattur District,Tamilnadu,India-635602</p>
<p> <span class="for"> E-mail: </span><EMAIL></span></p>
<p> <span class="for">Contact number: </span>+91 **********</p>
</ul>
</div>
</div>
</div>
</div>
</div></div>

<script type="text/javascript" src="https://vyaparwebfiles.vypcdn.in/js/materialize.min.js"></script>
<script type="text/javascript">

</body>
</html>

''';

String htmlPrivacyPolicy='''

<!DOCTYPE html>
<html lang="en">
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<meta name="description" content="Vyapar is committed to protecting your privacy and this privacy statement describes in greater detail the privacy practices of Vyapar tech Solution Ltd.">
<meta property="og:title" content="Vyapar App Terms &amp; Conditions -  Vyapar App" />
<meta property="og:image" content="https://vyaparwebsiteimages.vypcdn.in/desktop-hero-image.png" />
<meta property="og:image:width" content="300" />
<meta property="og:image:height" content="300" />
<meta property="og:site_name" content="Vyapar App" />
<meta property="og:description" content="Vyapar is committed to protecting your privacy and this privacy statement describes in greater detail the privacy practices of Vyapar tech Solution Ltd." />
<meta name="yandex-verification" content="5559b59e64695360" />
<meta name="p:domain_verify" content="a4daf9a199884bb7abe56019ffc09c32" />
<meta charset="utf-8">
<meta name="csrf-token" content="xu8Y6BS175FmZFdEtAWGrMjQPEsmimjEgDbYh4YY">
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<meta name="msvalidate.01" content="CE7B00AD9AFC15DAD1D261A43F989093" />

<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet prefetch" type="text/css" as="style">
<link rel="stylesheet prefetch" as="style" href="https://vyaparwebfiles.vypcdn.in/css/intelInputCss/intlTelInput.min.css">
<link rel="stylesheet prefetch" as="style" href="https://use.fontawesome.com/releases/v5.8.1/css/all.css">
<link rel="stylesheet prefetch" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css">
<link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Material+Symbols+Outlined:opsz,wght,FILL,GRAD@48,400,0,0" />

<link type="text/css" href="https://vyaparapp.in/css/oldApp.css" rel="stylesheet prefetch" as="style">
<link type="text/css" href="https://vyaparapp.in/css/paymentrevamp.css?version=0.24" rel="stylesheet prefetch" as="style">
<link type="text/css" href="https://vyaparapp.in/css/login_panel.css?version=0.7" rel="stylesheet prefetch" as="style">
<link rel="stylesheet prefetch" href="https://vyaparwebfiles.vypcdn.in/css/layout-app/layout-app.css?v=0.1" as="style" />

<link rel="apple-touch-icon" sizes="57x57" href="/favicon/apple-icon-57x57.png?version=0.1">
<link rel="apple-touch-icon" sizes="60x60" href="/favicon/apple-icon-60x60.png?version=0.1">
<link rel="apple-touch-icon" sizes="72x72" href="/favicon/apple-icon-72x72.png?version=0.1">
<link rel="apple-touch-icon" sizes="76x76" href="/favicon/apple-icon-76x76.png?version=0.1">
<link rel="apple-touch-icon" sizes="114x114" href="/favicon/apple-icon-114x114.png?version=0.1">
<link rel="apple-touch-icon" sizes="120x120" href="/favicon/apple-icon-120x120.png?version=0.1">
<link rel="apple-touch-icon" sizes="144x144" href="/favicon/apple-icon-144x144.png?version=0.1">
<link rel="apple-touch-icon" sizes="152x152" href="/favicon/apple-icon-152x152.png?version=0.1">
<link rel="apple-touch-icon" sizes="180x180" href="/favicon/apple-icon-180x180.png?version=0.1">
<link rel="icon" type="image/png" sizes="192x192" href="/favicon/android-icon-192x192.png?version=0.1">
<link rel="icon" type="image/png" sizes="32x32" href="/favicon/favicon-32x32.png?version=0.1">
<link rel="icon" type="image/png" sizes="96x96" href="/favicon/favicon-96x96.png?version=0.1">
<link rel="icon" type="image/png" sizes="16x16" href="/favicon/favicon-16x16.png?version=0.1">
<link rel="manifest" href="/favicon/manifest.json">
<meta name="msapplication-TileColor" content="#ffffff">
<meta name="msapplication-TileImage" content="/favicon/ms-icon-144x144.png?version=0.1">
<meta name="theme-color" content="#ffffff">

<meta name="csrf-token" content="xu8Y6BS175FmZFdEtAWGrMjQPEsmimjEgDbYh4YY">

<link rel="canonical" href="https://vyaparapp.in/terms">
<link rel="stylesheet" href="https://vyaparwebfiles.vypcdn.in/css/term-and-cond.css?v=0.4">

</head>
<body>
<div class="main-content">
<div class="container">
<div class="row">
<div class="col s12">
<div class style="background: white;">
<ul class="terms center-align">
<p><h1 style="font-size: 1.75rem"><b>NembooBill Apps Privacy Policy</b></h1></p><br><hr>
<p>We are dedicated to safeguarding your privacy. This privacy statement provides a more comprehensive explanation of the privacy practices of Simply NembooBill Apps Private Limited, the company behind the app.
</p>
<p><b>App Name:</b>NembooBill App  ("Your Smart Billing App")</p>
<p><b>Developer Name:</b>Cloudstier Solutions</p>
</ul>
<ul class="terms center-align">
<h3>General</h3><br><hr>
<p>Review these Terms of Service ("Agreement") thoroughly. This Agreement is a legal agreement between the User and NembooBill. By accepting electronically (for example, clicking “I Agree”), installing, accessing or using the Services, User agrees to these terms. If the User does not agree to this Agreement, then he/she may not use the Services. This License Agreement shall be governed by and construed in accordance with the laws of the INDIA.
</p>
</ul>
<ul class="terms center-align">
<h3>Modification of terms & conditions</h3><br><hr>
<p>These terms may be updated from time to time in the future. The User should agree to review our Terms and Conditions regularly by visiting this page. Continued access to or use of the service will mean that the User agrees to the change.
</p>
</ul>
<ul class="terms center-align">
<h3>When This Privacy Statement Applies</h3><br><hr>
<p>
Its liability to the User is limited to those who have downloaded the NembooBill application by clicking on the “I Agree/Login” button for Terms and Conditions.
</p>
</ul>
<ul class="terms center-align">
<h5>Terms and Description of Services</h5>
<p>
We provide an array of services including Inventory management, Invoicing, Reporting tool, Estimate/Order Form creator and so on to serve as a complete business management software ("Service" or "Services"). The User may use the Services for his/her personal and business use or for internal business purpose in the organization that he/she represent. The User may download the application from the Official website/ Google Play Store using the Internet. Once downloaded, the User no longer needs an internet connection for its usage. A User can create and add content to his/her account staying offline. But, the User is responsible for obtaining access to the Internet and the device necessary to use the Services. ( Side Note: NembooBill tries its best to make Businesses compliant to taxation and other laws as much as possible. But, it is solely the User’s responsibility to see that he/she remains compliant with GST and other laws levied by Government. NembooBill is not in any case responsible for the violation of the compliance caused by software) <br>
For the software subscription services purchased on NembooBill, the validity of the license period shall commence on or before 60 days since the date of purchase. NembooBill reserves all rights to increase or reduce this grace period
</p>
</ul>
<ul class="terms center-align">
<h3>Business Data and Privacy</h3><br><hr>
<p>
The User alone is responsible for maintaining the confidentiality of his/her username, password and other sensitive information. He/She is responsible for all activities that occur in his/her user account and he/she agrees to inform us immediately of any unauthorized use of their user account by <NAME_EMAIL> or by calling us on any of the numbers listed on http://NembooBillapp.in/#contact. We are not responsible for any loss or damage to his/her or to any third party incurred as a result of any unauthorized access and/or use of his/her user account, or otherwise. We are neither responsible for any kind of data loss as performing necessary backups on data is solely the User’s responsibility. NembooBill is not responsible for any kind of data discrepancy or any type of loss occured due to data discrepancy/software issue.
</p>
</ul>
<ul class="terms center-align">
<h3>Data Ownership</h3><br><hr>
<p>
The User owns the content created or stored by his/her as we respect his/her right to ownership of content created or stored by them. The Users use of the Services does grant NembooBill the license to use, reproduce, adapt, modify, publish or distribute the content created by them or stored in the user’s account for NembooBill internal purpose. NembooBill tries keeping Users data safe but holds zero responsibility in case of data leaks
</p>
</ul>
<ul id="permissions" class="terms center-align">
<h3>Permissions</h3><br><hr>
<p><span class="for">Camera: </span>To take the picture of the bills etc to be attached with transactions.
</p>
<p>
<span class="for">Contacts: </span>
This app collects contact information like number of contacts on the device with a phone
number and without a phone number. In addition to it, the app collects other non-personal
information to understand the risk profile of the user and enable credit facility.<br>
This app doesn't upload your contacts information like names, phone numbers, emails
addresses and other personal information to our servers.
</p>
<p>
<span class="for">Storage: </span>
This app collects metadata of files on your device like file names, types, created times, last
updated times for credit profile enrichment.
</p>
<p>
<span class="for">Location: </span>
This app collects location details to verify your location & current address, to ensure
serviceability and to identify unusual activity to prevent against any fraud, we do not collect
location when app is in background.
</p>
<p>
<span class="for">SMS: </span>
This app collects SMS data like sender names, SMS body, received time to understand your income,
spending patterns and automatically fetch your expenses, bills, credits. We do not read any personal or OTP messages.
</p>
<p>
<span class="for" style="width: 180px;">Device and Personal info: </span>
This app collects and monitor specific information about users' Phone Number and your device including your hardware model, masked phone numbers, operating system and version, unique device identifiers like IMEI and serial number, user profile information to uniquely identify the devices and ensure that unauthorized devices are not able to act on your behalf to prevent frauds.
</p>
<p>
<span class="for">Accounts: </span>
This app collects and monitor list of account information like account names, account types
in your device for credit profile enrichment.
</p>
<p>
<span class="for">Apps: </span>
This app collects details of your installed applications to understand your profile. This helps
us give quicker approvals and higher credit limits.
</p>
<p>
<span class="for">Call logs: </span>
This app uses call logs permission to validate your phone number using missed call. In
addition to it, details of your aggregated call information is collected to understand your
profile. This helps us give quicker approvals and higher credit limits. We do not read the
phone numbers or any personal information.
</p>
<p>
<span class="for">Calendar: </span>
This app collects event data on your calendar for credit profile enrichment.
</p>
</ul>
<ul class="terms center-align border-5px">
<h4 class="border-3px">Permissions Usage:</h4><br><hr>
</ul>
<ul id="permissionsUsage" class="terms center-align">
<h5>Collection of contact</h5>
<h5>This is how we will use your contact permissions:</h5>
<p>(i) We allow users to sync their contacts from mobile to desktop, the contact permissions will be used to facilitate this.</p>
<p>(ii) We will show parties that are already NembooBill users, to show you your potential buyers and suppliers.</p>
</ul>
<ul class="terms center-align">
<h3>Collection of SMS information<h3><br><hr>
<p>
We will use SMS data like sender names, SMS body, received time to understand your income, spending patterns and automatically fetch your expenses, bills, credits. We do not read any personal or OTP messages. <br>
We collect and monitor SMS sent by 6-digit alphanumeric senders from your inbox to understand your income, spending patterns and automatically fetch your expenses, bills, credits. We collect your historical SMS data as well, as this process will enable you to take financial facilities like loans, credit cards from the regulated financial entities available on the platform. We do not read any personal or OTP messages.
</p>
</ul>
<ul class="terms center-align">
<h3>
Collection of device location, device information and personal information
</h3><br><hr>
<p>
We collect and monitor the information about the location of your device to provide serviceability of your loan application, to reduce the risk associated with your loan application and to provide pre-approved customised loan offers. This also helps us to verify the address, make a better credit risk decision and expedite the know your customer (KYC) process. Information the App collects, and its usage depends on how you manage your privacy controls on your device. When you install the App, we store the information we collect with unique identifiers tied to the device you are using. We collect information from the device when you download and install the App and explicitly seek permissions from You to get the required information from the device.<br>
The information we collect from your device includes the users' phone number, masked phone numbers, hardware model, build model, RAM, storage; unique device identifiers like IMEI, serial number, SSAID; SIM information that includes network operator, roaming state, MNC and MCC codes, WIFI information that includes MAC address and mobile network information to uniquely identify the devices and ensure that no unauthorized device acts on your behalf to prevent frauds.
</p>
</ul>
<ul class="terms center-align">
<h3>
Collection of installed applications
</h3><br><hr>
<p>
We collect a list of the installed applications’ metadata information which includes the application name, package name, installed time, updated time, version name and version code of each installed application on your device to assess your creditworthiness and enrich your profile with pre-approved customized loan offers.
</p>
</ul>
<ul class="terms center-align">
<h3>
Collection of Images
</h3><br><hr>
<p>
The images uploaded by users in NembooBill App are stored on our servers so they can be accessed from multiple devices. We do this to enhance your user experience and make it seamless.
</p>
</ul>
<ul class="terms center-align">
<h3>
Storage
</h3><br><hr>
<p>
We require storage permission so that your KYC and other relevant documents can be securely downloaded and saved on your phone. You can then easily upload the correct KYC related documents for faster loan application details filling and disbursal process. This ensures that you are provided with a seamless experience while using the application.
</p>
</ul>
<ul class="terms center-align">
<h3>
Camera
</h3><br><hr>
<p>
We require the camera information permission to provide you with an easy/smooth experience and to enable you to click photos of your KYC documents along with other requisite documents and upload the same on the App during your loan application journey.
</p>
</ul>
<ul class="terms center-align">
<h3>
Collection of other non-personal information
</h3><br><hr>
<p>
We automatically track certain information about you based upon your behaviour on our Platform. We use this information to do internal research on our users’ demographics, interests, and behaviour to better understand, protect and serve our users and improve our services. This information is compiled and analysed on an aggregated basis. We also collect your Internet Protocol (IP) address and the URL used by you to connect your computer to the internet, etc. This information may include the URL that you just came from (whether this URL is on our Website or not), which URL you next go to (whether this URL is on our Website or not), your computer browser information, and your IP address.<br>
Cookies are small data files that a Website stores on Your computer. We will use cookies on our Website similar to other lending websites/apps and online marketplace websites/apps. Use of this information helps Us identify You in order to make our Website more user friendly. Most browsers will permit You to decline cookies but if You choose to do this it might affect service on some parts of Our Website. If you choose to make a purchase through the Platform, we collect information about your buying behaviour. We retain this information as necessary to resolve disputes, provide customer support and troubleshoot problems as permitted by law.<br>
If you send us personal correspondence, such as emails or letters, or if other users or third parties send us correspondence about your activities or postings on the Website, we collect such information into a file specific to you.
</p>
</ul><ul class="terms center-align">
<h3>
Data Security
</h3><br><hr>
<p>
At NembooBill, we understand the paramount importance of safeguarding your personal and business information. Our dedication to data security is unwavering, and we employ a comprehensive approach to protect your data against unauthorised access, misuse, or loss.
</p>
<p>
<b>Industry-Standard Safeguards:</b> We leverage industry-standard physical, technical, and administrative measures to ensure the security of your information. Our systems are fortified with robust firewalls, advanced encryption methods, and continuous monitoring to thwart potential security breaches. Our team stays abreast of the latest security trends and technologies, ensuring your data is protected by the best defenses available.
</p>
<p>
<b>Continuous Improvement:</b> Recognizing that no security system is impenetrable, we are committed to continually enhancing our security measures. Our efforts are guided by a ‘best effort basis’ principle, focusing on continuous improvement and adaptation to evolving threats.
</p>
<p>
<b>User Responsibility:</b> We emphasize the importance of your role in maintaining the security of your account. It’s crucial to keep your username and password confidential and to exercise caution in their usage. We count on your partnership to uphold this aspect of security.
</p>
<p>
<b>Communication Safety:</b> Please be aware that standard email and messaging communication with NembooBill may not be encrypted. We advise against sharing confidential information through these channels to avoid potential risks.
</p>
</ul><ul class="terms center-align">
<h3>
Link to third-party SDK
</h3><br><hr>
<p>
The NembooBill Platform has a link to a registered third party SDK that collects data on our behalf and data is stored on a secured server to perform a credit risk assessment. We ensure that our third-party service provider takes extensive security measures in order to protect your personal information against loss, misuse or alteration of the data. Our third-party service provider employs separation of environments and segregation of duties and has strict role-based access control on a documented, authorized, need to-use basis.<br>
The stored data is protected and stored by application-level encryption. They enforce key management services to limit access to data. Furthermore, our registered third party service provider provides hosting security – they use industry-leading anti-virus, anti-malware, intrusion prevention systems, intrusion detection systems, file integrity monitoring, and application control solutions
</p>
</ul>
<ul class="terms center-align">
<h3>
Approval of Alerts, Messages and Promotional campaign
</h3><br><hr>
<p>
In addition to the General Conditions of NembooBill service a User agrees to be bound by the following terms and conditions:<br>
(i) The User agrees to receive promotional, transactional and commercial communications from NembooBill through calls, email, SMS or push notifications.<br>
(ii) Under certain conditions, promotional messages are transmitted to the users and that NembooBill shall not bear any further obligation in respect of such promotional messages.
</p>
</ul>
</ul>
<ul class="terms center-align">
<h3>
Delete Account
</h3><br><hr>
<p>
Log in to Your NembooBill Account: Visit the NembooBill website and log in to your account using your credentials.
Navigate to Settings: Once logged in, navigate to the settings or account management section of the application.
Find Account Deletion Option: Look for an option related to deleting or deactivating your account. This option is typically found under the account settings or privacy settings.
Follow Deletion Instructions: Click on the account deletion option and follow the instructions provided. You may need to confirm your decision to delete your account.
Confirmation: After confirming your decision, your NembooBill account and all associated data will be permanently deleted from the system.
If you need further assistance, you can refer to the following link for detailed instructions on how to delete your NembooBill account: <a href="https://drive.google.com/file/d/166qOnO5zBEht2syVLObJWDWVU5xP2GlZ/view">Delete NambooBill Account Instructions</a>.
</p>
</ul>
</ul>
<ul class="terms center-align">
<h3>
END OF TERMS OF SERVICE
</h3>
<p>
If you have any questions or concerns regarding this Agreement, please contact us
<p>Email : <EMAIL></P> 
<p>Phone : +91 **********</p>
</p>
</ul>
</div>
</div>
</div>
</div>
</div></div>
</body>
</html>

''';
 