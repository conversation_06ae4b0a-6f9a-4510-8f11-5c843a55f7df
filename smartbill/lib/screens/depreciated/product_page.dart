// // ignore_for_file: sdk_version_ui_as_code, unused_local_variable, non_constant_identifier_names

// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
// import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
// import 'package:smartbill/model/products.dart';
// import 'package:smartbill/utils/common_function/validation_utility.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// 
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import 'package:smartbill/utils/constants/colors.dart';
// import '../../main.dart';
// import 'add_product_page.dart';
// import 'header_footer.dart';

// //PRODUCT LIST VIEW CLASS
// class ProductPage extends StatefulWidget {
//   static var tag = "/ProductPage";

//   @override
//   T5ListingState createState() => T5ListingState();
// }

// class T5ListingState extends State<ProductPage> {
//   RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
//   DBOperations dbOperations = new DBOperations();
//   Validator validator = new Validator();
//   final dbHelper = DatabaseHelper.instance;
//   final searchController = new TextEditingController();
//   bool enableAddProduct = true;
//   bool taxupdate = false;
//   bool isLock = false;
//   var width;
//   var height;
//   bool isProductIDDesc = false;
//   bool isProductNameDesc = false;
//   bool isProductPriceDesc = false;
//   List<Products> filterProductList = [];
//   List<ProductKey> productKeyList = [];
//   ProductKey selectCategory;

//   //GET ALL PRODUCT DETAILS
//   void _queryAllProductDetails() async {
//     final allRows = await dbHelper.queryallRowsProductDeatails();
//     setState(() {
//       ListUtility.productList.clear();
//       filterProductList.clear();
//       allRows
//           .forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
//       filterProductList.addAll(ListUtility.productList);
//     });
//   }

//   //GET ALL PRODUCT DETAILS
//   queryAllProductDetailsByCategory(String categoryID) async {
//     final allRows =
//         await dbHelper.queryAllRowsProductDetailsByCategory(categoryID);
//     setState(() {
//       ListUtility.productList.clear();
//       filterProductList.clear();
//       allRows
//           .forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
//       filterProductList.addAll(ListUtility.productList);
//     });
//   }

//   void queryAllProductKeys() async {
//     final allRows = await dbHelper.queryallRowsProductKeyTable();
//     setState(() {
//       productKeyList.clear();
//       ProductKey model = ProductKey();
//       model.keywordName = "All";
//       productKeyList.add(model);
//       allRows.forEach((row) => productKeyList.add(ProductKey.fromMap(row)));
//     });
//   }

//   //DELETE PRODUCT DETAILS LIKE INVISIBLE
//   updateWithDelete(String id) async {
//     Products products = Products(productId: id, sync: 0);
//     await dbHelper.deleteUpdateProductsTable(products);
//     setState(() {
//       _queryAllProductDetails();
//     });
//   }

//   sortByProductID() {
//     try {
//       setState(() {
//         if (isProductIDDesc) {
//           isProductIDDesc = false;
//           filterProductList
//               .sort((a, b) => a.productSaleId.compareTo(b.productSaleId));
//         } else {
//           isProductIDDesc = true;
//           filterProductList
//               .sort((a, b) => b.productSaleId.compareTo(a.productSaleId));
//         }
//       });
//     } catch (ex) {}
//   }

//   sortByProductName() {
//     try {
//       setState(() {
//         if (isProductNameDesc) {
//           isProductNameDesc = false;
//           filterProductList.sort(
//               (a, b) => a.name.toLowerCase().compareTo(b.name.toLowerCase()));
//         } else {
//           isProductNameDesc = true;
//           filterProductList.sort(
//               (a, b) => b.name.toLowerCase().compareTo(a.name.toLowerCase()));
//         }
//       });
//     } catch (ex) {}
//   }

//   sortByProductPrice() {
//     try {
//       setState(() {
//         if (isProductPriceDesc) {
//           isProductPriceDesc = false;
//           filterProductList.sort((a, b) => a.price != null && a.price != ""
//               ? double.parse(a.price).compareTo(
//                   b.price != null && b.price != "" ? double.parse(b.price) : 0)
//               : 0.compareTo(b.price != null && b.price != ""
//                   ? double.parse(b.price)
//                   : 0));
//         } else {
//           isProductPriceDesc = true;
//           filterProductList.sort((a, b) => b.price != null && b.price != ""
//               ? double.parse(b.price).compareTo(
//                   a.price != null && a.price != "" ? double.parse(a.price) : 0)
//               : 0.compareTo(a.price != null && a.price != ""
//                   ? double.parse(a.price)
//                   : 0));
//         }
//       });
//     } catch (ex) {}
//   }

//   void filterProductsByName(String name) async {
//     if (name != "") {
//       setState(() {
//         filterProductList.clear();
//         for (Products product in ListUtility.productList) {
//           if (product.name.toLowerCase().contains(name.toLowerCase())) {
//             filterProductList.add(product);
//           }
//         }
//       });
//     } else {
//       setState(() {
//         filterProductList.clear();
//         filterProductList.addAll(ListUtility.productList);
//       });
//     }
//   }

//   //MESSAGE FOR PERMISSION
//   // ignore: missing_return
//   bool showMessage() {
//     showToast("You don't have permission.");
//   }

//   @override
//   void initState() {
//     _queryAllProductDetails();
//     queryAllProductKeys();
//     super.initState();
//   }

//   filterDialog(BuildContext context) async {
//     selectCategory =
//         selectCategory == null ? productKeyList.first : selectCategory;
//     return showDialog(
//         barrierDismissible: true,
//         context: context,
//         builder: (BuildContext context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return AlertDialog(
//               title: Text(
//                 "Filter",
//                 style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
//               ),
//               content: Container(
//                 width: double.maxFinite,
//                 height: height / 4,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: <Widget>[
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         SizedBox(
//                           width: width / 1.5,
//                           child: InputDecorator(
//                             decoration: InputDecoration(
//                               contentPadding: EdgeInsets.symmetric(
//                                   horizontal: 10.0, vertical: 1.0),
//                               labelText: 'Category',
//                               border: OutlineInputBorder(
//                                   borderRadius: BorderRadius.circular(5.0)),
//                             ),
//                             child: DropdownButtonHideUnderline(
//                               child: new DropdownButton<ProductKey>(
//                                 hint: Text("All"),
//                                 value: selectCategory,
//                                 items: productKeyList.map((ProductKey value) {
//                                   return new DropdownMenuItem<ProductKey>(
//                                     value: value,
//                                     child: new Text(value?.keywordName),
//                                   );
//                                 }).toList(),
//                                 onChanged: (ProductKey value) async {
//                                   if (!isLock) {
//                                     isLock = true;
//                                     setState(() {
//                                       selectCategory = value;
//                                     });
//                                     isLock = false;
//                                   }
//                                 },
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           height: 10,
//                         ),
//                       ],
//                     ),
//                     SizedBox(
//                       height: 40,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () async {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               searchController.clear();
//                               selectCategory = productKeyList.first;
//                               await _queryAllProductDetails();
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: text("Reset",
//                                 textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(
//                                 bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               searchController.clear();
//                               if (selectCategory?.keywordName == "All") {
//                                 await _queryAllProductDetails();
//                               } else {
//                                 await queryAllProductDetailsByCategory(
//                                     selectCategory?.productKeyId);
//                               }
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: text("Filter",
//                                 textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(
//                                 bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             );
//           });
//         });
//   }

//   String sortType = "ID";

//   sortDialog(BuildContext context) {
//     return showDialog(
//         context: context,
//         builder: (context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return AlertDialog(
//                 contentPadding: EdgeInsets.all(10),
//                 scrollable: true,
//                 content: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Container(
//                         width: double.maxFinite,
//                         child: Column(
//                           children: [
//                             Row(
//                               children: [
//                                 new Radio(
//                                   value: "ID",
//                                   groupValue: sortType,
//                                   onChanged: (value) {
//                                     setState(() {
//                                       sortType = value;
//                                     });
//                                   },
//                                 ),
//                                 new Text('Sort By ID'),
//                               ],
//                             ),
//                             Row(
//                               children: [
//                                 new Radio(
//                                   value: "Name",
//                                   groupValue: sortType,
//                                   onChanged: (value) {
//                                     setState(() {
//                                       sortType = value;
//                                     });
//                                   },
//                                 ),
//                                 new Text('Sort By Name'),
//                               ],
//                             ),
//                             Row(
//                               children: [
//                                 new Radio(
//                                   value: "Price",
//                                   groupValue: sortType,
//                                   onChanged: (value) {
//                                     setState(() {
//                                       sortType = value;
//                                     });
//                                   },
//                                 ),
//                                 new Text('Sort By Price'),
//                               ],
//                             )
//                           ],
//                         )),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: text("Cancel",
//                                 textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(
//                                 bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               if (sortType == "ID") {
//                                 sortByProductID();
//                               }
//                               if (sortType == "Name") {
//                                 sortByProductName();
//                               }
//                               if (sortType == "Price") {
//                                 sortByProductPrice();
//                               }
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: text("Sort",
//                                 textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(
//                                 bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ));
//           });
//         });
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;
//     

//     return Scaffold(
//       backgroundColor: secondaryTextColor,
//       resizeToAvoidBottomInset: false,
//       appBar: BaseAppBar(
//         title: text("Products",
//             textColor: secondaryTextColor,
//             fontSize: textSizeNormal,
//             fontFamily: fontMedium),
//         appBar: AppBar(),
//         widgets: <Widget>[],
//       ),
//       drawer: BaseDrawer(),
//       body: Column(
//         children: [
//           SizedBox(
//             height: 3,
//           ),
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Container(
//                 padding: const EdgeInsets.only(top: 7, left: 5),
//                 width: width / 1.4,
//                 height: 50,
//                 child: TextField(
//                   controller: searchController,
//                   onChanged: filterProductsByName,
//                   style: TextStyle(color: Colors.black),
//                   autofocus: false,
//                   decoration: InputDecoration(
//                     labelText: 'Search by product name',
//                     border: InputBorder.none,
//                     enabledBorder: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(4),
//                       borderSide: const BorderSide(color: grey, width: 0.0),
//                     ),
//                     focusedBorder: OutlineInputBorder(
//                       borderRadius: BorderRadius.circular(4),
//                       borderSide:
//                           const BorderSide(color: t5ViewColor, width: 0.0),
//                     ),
//                     suffixIcon: InkWell(
//                       onTap: () {
//                         setState(() {
//                           FocusScope.of(context).unfocus();
//                           searchController.clear();
//                           filterProductsByName("");
//                         });
//                       },
//                       child: new Icon(
//                         Icons.clear,
//                         color: appStore.iconColor,
//                       ),
//                     ),
//                   ),
//                 ),
//               ),
//               Column(
//                 children: [
//                   Text("Sort"),
//                   IconButton(
//                       onPressed: () => sortDialog(context),
//                       icon: Icon(Icons.sort)),
//                 ],
//               ),
//               Column(
//                 children: [
//                   Text("Filter"),
//                   IconButton(
//                       onPressed: () => filterDialog(context),
//                       icon: Icon(Icons.filter_alt_outlined)),
//                 ],
//               ),
//             ],
//           ),
//           Expanded(
//             child: SingleChildScrollView(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   Container(
//                     height: height / 1.47,
//                     child: Column(
//                       children: [
//                         // Container(
//                         //   height: 40,
//                         //   child: Card(
//                         //     child: Row(
//                         //       mainAxisAlignment: MainAxisAlignment.spaceAround,
//                         //       children: [
//                         //         Container(
//                         //           width: width / 10,
//                         //           child: text("No",
//                         //               textColor: primaryTextColor,
//                         //               fontFamily: fontBold,
//                         //               fontSize: textSizeNormal,
//                         //               isCentered: true),
//                         //         ),
//                         //         Container(
//                         //           width: width / 2,
//                         //           child: text("Name",
//                         //               textColor: primaryTextColor,
//                         //               fontFamily: fontBold,
//                         //               fontSize: textSizeNormal,
//                         //               isCentered: true),
//                         //         ),
//                         //         Container(
//                         //           alignment: Alignment.center,
//                         //           width: width / 4,
//                         //           child: text("Price(Rs)",
//                         //               textColor: primaryTextColor,
//                         //               fontFamily: fontBold,
//                         //               fontSize: textSizeNormal,
//                         //               isCentered: true),
//                         //         ),
//                         //       ],
//                         //     ),
//                         //   ),
//                         // ),
//                         Expanded(
//                           child: Align(
//                             alignment: Alignment.topCenter,
//                             child: ListView.builder(
//                                 padding: EdgeInsets.symmetric(horizontal: 5),
//                                 scrollDirection: Axis.vertical,
//                                 itemCount: filterProductList.length,
//                                 shrinkWrap: true,
//                                 physics: ScrollPhysics(),
//                                 itemBuilder: (context, index) {
//                                   //DELETE ALERT DIALOG FOR PRODUCTS
//                                   var item = filterProductList[index];
//                                   return GestureDetector(
//                                     onLongPress: () {
//                                       final bool res = rolePermissions
//                                                   .isDeletePermissions(
//                                                       dbOperations
//                                                           .returnPermission(
//                                                               "Products")) ==
//                                               true
//                                           ? showDialog(
//                                               context: context,
//                                               builder: (BuildContext context) {
//                                                 return AlertDialog(
//                                                   content: Text(
//                                                       "Are you sure you want to delete ${item.name}?"),
//                                                   actions: <Widget>[
//                                                     GestureDetector(
//                                                       onTap: () async {
//                                                         if (!isLock) {
//                                                           isLock = true;
//                                                           final syncNo = await dbHelper
//                                                               .queryGetProductSyncNo(
//                                                                   item.productId);
//                                                           if (syncNo == 1) {
//                                                             await updateWithDelete(
//                                                                 item.productId);
//                                                           } else {
//                                                             await dbHelper
//                                                                 .deleteProductsDetailsBasedOnId(
//                                                                     item.productId);
//                                                           }

//                                                           setState(() {
//                                                             _queryAllProductDetails();
//                                                           });
//                                                           Navigator.pop(
//                                                               context);
//                                                           isLock = false;
//                                                         }
//                                                       },
//                                                       child: Container(
//                                                         alignment:
//                                                             Alignment.center,
//                                                         height: 40,
//                                                         width: width / 5,
//                                                         child: text("Yes",
//                                                             textColor: secondaryTextColor,
//                                                             isCentered: true),
//                                                         decoration: boxDecoration(
//                                                             bgColor:
//                                                                 buttonThemeColor,
//                                                             radius: 8.0),
//                                                       ),
//                                                     ),
//                                                     GestureDetector(
//                                                       onTap: () {
//                                                         if (!isLock) {
//                                                           isLock = true;
//                                                           Navigator.pop(
//                                                               context);
//                                                           isLock = false;
//                                                         }
//                                                       },
//                                                       child: Container(
//                                                         alignment:
//                                                             Alignment.center,
//                                                         height: 40,
//                                                         width: width / 5,
//                                                         child: text("Cancel",
//                                                             textColor: secondaryTextColor,
//                                                             isCentered: true),
//                                                         decoration: boxDecoration(
//                                                             bgColor:
//                                                                 buttonThemeColor,
//                                                             radius: 8.0),
//                                                       ),
//                                                     ),
//                                                   ],
//                                                 );
//                                               })
//                                           : showMessage();
//                                       return res;
//                                     },
//                                     child: Card(
//                                       margin: EdgeInsets.all(0.0),
//                                       color: index % 2 == 0
//                                           ? secondaryTextColor
//                                           : Colors.blue[50],
//                                       child: Container(
//                                         child: InkWell(
//                                           onDoubleTap: () async {
//                                             if (rolePermissions
//                                                     .isEditPermissions(
//                                                         dbOperations
//                                                             .returnPermission(
//                                                                 "Products")) ==
//                                                 true) {
//                                               Navigator.push(
//                                                   context,
//                                                   MaterialPageRoute(
//                                                       builder: (context) =>
//                                                           AddProductDialog(
//                                                             id: item.productId,
//                                                           )));
//                                               // await showDialog(
//                                               //     context: context,
//                                               //     builder: (context) {
//                                               //       return AddProductDialoge(
//                                               //         id: item.productId,
//                                               //       );
//                                               //     });
//                                               _queryAllProductDetails();
//                                               await queryAllProductKeys();
//                                             } else {
//                                               showToast(
//                                                   "you don't have a permission");
//                                             }
//                                           },
//                                           child: Column(
//                                             children: <Widget>[
//                                               Container(
//                                                 child: Row(
//                                                   mainAxisAlignment:
//                                                       MainAxisAlignment
//                                                           .spaceBetween,
//                                                   children: <Widget>[
//                                                     Container(
//                                                       width: width / 1.6,
//                                                       alignment:
//                                                           Alignment.centerLeft,
//                                                       child: text(item.name,
//                                                           textColor: appStore
//                                                               .textPrimaryColor,
//                                                           fontSize:
//                                                               textSizeMedium,
//                                                           fontFamily:
//                                                               fontSemibold,
//                                                           isCentered: false,
//                                                           isLongText: true),
//                                                     ),
//                                                     Container(
//                                                       width: width / 3,
//                                                       alignment:
//                                                           Alignment.centerRight,
//                                                       child: text(
//                                                           item.price == "" ||
//                                                                   item.price ==
//                                                                       null
//                                                               ? ""
//                                                               : "\u{20B9}" +
//                                                                   double.parse(item
//                                                                           .price)
//                                                                       .toStringAsFixed(
//                                                                           2),
//                                                           textColor: appStore
//                                                               .textSecondaryColor,
//                                                           fontSize:
//                                                               textSizeMedium,
//                                                           fontFamily:
//                                                               fontSemibold),
//                                                     ),
//                                                   ],
//                                                 ),
//                                               ),
//                                               Container(
//                                                 child: Row(
//                                                   mainAxisAlignment:
//                                                       MainAxisAlignment
//                                                           .spaceBetween,
//                                                   children: <Widget>[
//                                                     Container(
//                                                       width: width / 4,
//                                                       alignment:
//                                                           Alignment.centerLeft,
//                                                       child: text(
//                                                           "No: " +
//                                                               item.productSaleId
//                                                                   .toString(),
//                                                           fontSize:
//                                                               textSizeSMedium,
//                                                           isCentered: true),
//                                                     ),
//                                                     Container(
//                                                       width: width / 1.5,
//                                                       alignment:
//                                                           Alignment.centerLeft,
//                                                       child: text(
//                                                           "Category: ${item.productKeyName != null ? item.productKeyName.toString() : 'None'}",
//                                                           fontSize:
//                                                               textSizeSMedium,
//                                                           isCentered: true),
//                                                     ),
//                                                     // Container(
//                                                     //   width: width / 2.6,
//                                                     //   alignment:
//                                                     //   Alignment.centerRight,
//                                                     //   child: text(
//                                                     //       item.price == "" ||
//                                                     //           item.price ==
//                                                     //               null
//                                                     //           ? ""
//                                                     //           : "\u{20B9}"+double.parse(item
//                                                     //           .price)
//                                                     //           .toStringAsFixed(
//                                                     //           2),
//                                                     //       textColor: appStore
//                                                     //           .textSecondaryColor,
//                                                     //       fontSize:
//                                                     //       textSizeMedium,
//                                                     //       fontFamily:
//                                                     //       fontSemibold),
//                                                     // ),
//                                                   ],
//                                                 ),
//                                               ),
//                                               Divider(
//                                                   height: 0.5,
//                                                   color: t5ViewColor)
//                                             ],
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   );
//                                 }),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ],
//       ),
//       bottomNavigationBar: BottomNavigation(),
//       floatingActionButton: enableAddProduct
//           ? FloatingActionButton(
//               backgroundColor: buttonThemeColor,
//               onPressed: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   if (rolePermissions.isEditPermissions(
//                           dbOperations.returnPermission("Products")) ==
//                       true) {
//                     setState(() {
//                       enableAddProduct = false;
//                     });
//                     Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                                 builder: (context) => AddProductDialog()))
//                         .then((value) {
//                       setState(() {
//                         _queryAllProductDetails();
//                         queryAllProductKeys();
//                       });
//                     });

//                     // await showDialog(
//                     //     context: context,
//                     //     builder: (context) {
//                     //       return AddProductDialoge();
//                     //     });
//                     setState(() {
//                       enableAddProduct = true;
//                     });
//                     _queryAllProductDetails();
//                     await queryAllProductKeys();
//                   } else {
//                     showToast("You don't have a permission");
//                   }
//                   isLock = false;
//                 }
//               },
//               child: Icon(Icons.add),
//             )
//           : Container(
//               width: 0.0,
//               height: 0.0,
//               color: Colors.red,
//             ),
//     );
//   }
// }
