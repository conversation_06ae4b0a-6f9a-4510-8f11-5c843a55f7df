// import 'package:flutter/material.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/model/user_model.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// import 'package:smartbill/utils/common_widgets/widgets.dart';

// import '../../../main.dart';

// class ValidityListView extends StatefulWidget {
//   @override
//   _ValidityListViewState createState() => _ValidityListViewState();
// }

// class _ValidityListViewState extends State<ValidityListView> {
//   @override
//   List<SubscriptionDetail> viewOtherDevice = [];
//   removeCurrentDeviceId() {
//     for (SubscriptionDetail v in ListUtility.subscriptionDetails) {
//       if (v.mobileId != MyApp.deviceId) {
//         viewOtherDevice.add(v);
//       }
//     }
//   }

//   var now = DateTime.now();
//   void initState() {
//     viewOtherDevice.clear();
//     removeCurrentDeviceId();
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     var width = MediaQuery.of(context).size.width;
//     var height = MediaQuery.of(context).size.height;

//     return Container(
//       alignment: Alignment.topCenter,
//       height: height / 1.2,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: <Widget>[
//           viewOtherDevice.length != 0
//               ? Expanded(
//                   child: Align(
//                     alignment: Alignment.topCenter,
//                     child: ListView.builder(
//                         padding: EdgeInsets.only(right: 0),
//                         itemCount: viewOtherDevice.length,
//                         shrinkWrap: true,
//                         reverse: true,
//                         itemBuilder: (context, index) {
//                           var item = viewOtherDevice[index];
//                           return InkWell(
//                             onTap: () {
//                               Navigator.pop(context);
//                               // Navigator.push(
//                               //     context,
//                               //     MaterialPageRoute(
//                               //         builder: (context) => Subscriptions(
//                               //               validity: item,
//                               //             )));
//                             },
//                             child: Column(
//                               children: <Widget>[
//                                 Container(
//                                   margin: EdgeInsets.all(5),
//                                   height: 60,
//                                   child: Row(
//                                     mainAxisAlignment:
//                                         MainAxisAlignment.spaceBetween,
//                                     children: <Widget>[
//                                       Column(
//                                         children: [
//                                           Container(
//                                             //color: Colors.amberAccent,
//                                             width: width / 2,
//                                             alignment: Alignment.centerLeft,
//                                             child: text(
//                                                 item.deviceName.toString(),
//                                                 fontSize: textSizeMedium),
//                                           ),
//                                           Container(
//                                             //color: Colors.amberAccent,
//                                             width: width / 2,
//                                             alignment: Alignment.centerLeft,
//                                             child: text(
//                                                 item.mobileId.toString(),
//                                                 fontSize: textSizeSmall),
//                                           ),
//                                         ],
//                                       ),
//                                       item.activations.length != 0
//                                           ? Container(
//                                               width: width / 3.5,
//                                               alignment: Alignment.centerRight,
//                                               child: text(
//                                                   DateTime.parse(item
//                                                                   .activations[item
//                                                                               .activations
//                                                                               .length !=
//                                                                           0
//                                                                       ? item.activations
//                                                                               .length -
//                                                                           1
//                                                                       : 0]
//                                                                   .expiryDate)
//                                                               .difference(now)
//                                                               .inDays >
//                                                           0
//                                                       ? '${DateTime.parse(item.activations[item.activations.length != 0 ? item.activations.length - 1 : 0].expiryDate).difference(now).inDays.toString()} days'
//                                                       : "Plan Expired",
//                                                   textColor: appStore
//                                                       .textSecondaryColor,
//                                                   fontSize: textSizeSmall,
//                                                   fontFamily: fontSemibold),
//                                             )
//                                           : Container(),
//                                     ],
//                                   ),
//                                 ),
//                                 Divider(height: 0.5, color: t5ViewColor)
//                               ],
//                             ),
//                           );
//                         }),
//                   ),
//                 )
//               : Container(
//                   height: 100,
//                   child: Center(
//                     child: Text("No Other Devices !"),
//                   ),
//                 ),
//         ],
//       ),
//     );
//   }
// }
