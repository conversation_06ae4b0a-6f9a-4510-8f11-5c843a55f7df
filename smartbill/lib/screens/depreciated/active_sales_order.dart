// // ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code, null_aware_before_operator

// import 'dart:io';

// import 'package:blue_thermal_printer/blue_thermal_printer.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:smartbill/model/printer_devices.dart';
// import 'package:smartbill/utils/constants/strings_keys.dart';
// import 'package:smartbill/utils/constants/styles.dart';
// import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/common_function/printer_utilities.dart';
// import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
// import 'package:smartbill/utils/common_function/validation_utility.dart';
// import 'package:smartbill/utils/dbutils/customers_db_script.dart';
// import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
// import 'package:smartbill/utils/common_function/discount_function.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// import 'package:smartbill/model/payment_type.dart';
// import 'package:smartbill/model/reports.dart';
// import 'package:smartbill/model/transaction_model.dart';
// import 'package:smartbill/model/balance_model.dart';
// import 'package:smartbill/model/customers.dart';
// import 'package:smartbill/model/discounts.dart';
// import 'package:smartbill/model/sales_type.dart';
// import 'package:smartbill/utils/common_widgets/widgets.dart';

// import 'package:smartbill/utils/constants/colors.dart';
// import 'package:uuid/uuid.dart';

// import '../../main.dart';
// import '../../model/sales_payment_mapping.dart';
// import '../../utils/common_function/sales_function.dart';
// import '../../utils/common_widgets/header_footer.dart';
// import '../../utils/common_widgets/text_field_box_widget.dart';
// import '../../utils/constants/images.dart';
// import '../../utils/dbutils/sales_details_db_script.dart';
// import '../../utils/dbutils/sales_trans_db_script.dart';
// import '../../utils/dbutils/sales_trans_staging.dart';
// import '../../utils/printer_utilities/sales_print.dart';
// import '../item_layouts/Itempage_layout2.dart';
// import '../item_layouts/ItemsPage.dart';
// import '../item_layouts/sales_main_page.dart';
// import '../reports/reports_view_page.dart';
// import '../customers/customers_page.dart';

// class ActiveSalesOrder extends StatefulWidget {
//   @override
//   ActiveSalesOrderState createState() => ActiveSalesOrderState();
// }

// class ActiveSalesOrderState extends State<ActiveSalesOrder> {
//   final dbHelper = DatabaseHelper.instance;
//   bool isLock = false;
//   bool isCollectBillLock = false;
//   bool isCollectBillPrintLock = false;
//   BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
//   PrinterUtilities printerUtilities = new PrinterUtilities();
//   SalesPrint salesPrint = SalesPrint();
//   FnUtilities fnUtilities = new FnUtilities();
//   Validator validator = new Validator();
//   DBOperations dbOperations = new DBOperations();
//   DiscountFunction discount = DiscountFunction();
//   CustomersDBScript customerDB = CustomersDBScript();
//   SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
//   SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
//   SalesTransDBScript salesTransDBScript = SalesTransDBScript();
//   SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();

//   final customerNameController = new TextEditingController();
//   final givenAmountController = new TextEditingController();
//   final mobileNoController = new TextEditingController();
//   final paymentAmountController = new TextEditingController();

//   bool connected = false;
//   bool multiPrint = false;
//   bool isCustomer = false;
//   Sales reports;
//   PaymentType selectPaymentType;
//   List<Sales> activeTablesList = [];
//   List<Transactions> saleProductList = [];
//   List<Customer> customerList = [];
//   String saleID;
//   List<Transactions> isActiveSaleList = [];
//   bool isPaymentType = false;
//   bool isPaymentTypeIsEmpty = false;
//   bool isPaymentAmountEmpty = false;
//   double returnAmount = 0.0;
//   bool isIGST = false;
//   bool addTax = false;
//   bool isReturnAmount = false;
//   String saleLayout;
//   bool isDiscount = false;
//   String discountType;
//   String pageTitle = "Eat & Pay";
//   bool successDialogOnSales = false;
//   Customer selectCustomer;
//   List<Transactions> salesTransactionList = [];
//   bool isLoading = false;
//   bool isBigLayout = false;
//   double width = 0.0;
//   double height = 0.0;
//   List<SalesPaymentMapping> paymentMappingList = [];
//   int paymentMappingIndex = -1;
//   SalesFunctions salesFun = SalesFunctions();
//   double total = 0.0;
//   bool isPaymentMappingListExist = false;
//   StateSetter saveAlertSetState;
//   // "SO" - SalesOrder

//   /// Get Printer Info
//   PrinterDevices printerForBill;
//   PrinterDevices printerForKOT;

//   getPrinterInfo() {
//     /// Get Printer Informations
//     printerForBill = printerUtilities.getCurrentPrinter(PrinterFor.bill);
//     printerForKOT = printerUtilities.getCurrentPrinter(PrinterFor.kot);
//   }

//   ///---///
//   initFunction() async {
//     setState(() {
//       isLoading = true;
//     });
//     getPrinterInfo();
//     await initSettings();
//     await getAllActiveSales();
//     await checkSalesDelete();
//     await queryGetSalesProducts();
//     await initPrintFunction();
//     await getCustomersDetails();
//     setState(() {
//       isLoading = false;
//     });
//   }

//   initPrintFunction() async {
//     if (!multiPrint) {
//       await printerUtilities.disconnect();
//       await Future.delayed(Duration(seconds: 1));
//       await printerUtilities.initPrint(printerForBill);
//     }
//   }

//   initSettings() async {
//     setState(() {
//       ///workspace settings
//       multiPrint = fnUtilities.workSpaceSetValues(key_multiPrint) == "1";
//       isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
//       discountType = fnUtilities.workSpaceSetValues(key_discountType);
//       isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
//       isCustomer = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
//       addTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
//       isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
//       successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";

//       ///workspace user settings
//       saleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
//     });
//   }

//   @override
//   void initState() {
//     initFunction();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     if (!multiPrint) {
//       printerUtilities.disconnect();
//     }
//     super.dispose();
//   }

//   getAllActiveSales() async {
//     final allRows = await salesDetailsDBScript.queryForActiveSales();
//     setState(() {
//       activeTablesList.clear();
//       allRows.forEach((row) => activeTablesList.add(Sales.fromMap(row)));
//       for (Sales item in activeTablesList) {
//         if (item.discountPrice != null) {
//           item.totalAmount = (double.parse(item.totalAmount) - double.parse(item.discountPrice)).toString();
//         }
//       }
//     });
//   }

//   queryGetSalesDetails(id, flag) async {
//     var allRows;
//     if (flag != "Sale") {
//       if (flag != "Sale" && flag != "KOT") {
//         allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id, tableName: "staging", isActiveSaleNo: int.parse(flag));
//       } else if (flag == "KOT") {
//         allRows = await salestransStagingDBScript.getAllStagingSalesById(id);
//       }
//       setState(() {
//         salesTransactionList.clear();
//         allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
//       });
//     }
//   }

//   getCustomersDetails() async {
//     final allRows = await customerDB.selectAllCustomers();
//     if (this.mounted) {
//       setState(() {
//         customerList.clear();
//         for (var row in allRows) {
//           customerList.add(Customer.fromMap(row));
//         }
//       });
//     }
//   }

//   getAllTransaction(id) async {
//     var allRows;
//     allRows = await salestransStagingDBScript.getAllStagingSalesById(id);
//     setState(() {
//       salesTransactionList.clear();
//       allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
//     });
//   }

//   queryGetSalesProducts() async {
//     final allRows = await salestransStagingDBScript.queryAllSalesTransaction();
//     if (this.mounted) {
//       setState(() {
//         saleProductList.clear();
//         allRows.forEach((row) => saleProductList.add(Transactions.fromMap(row)));
//       });
//     }
//   }

//   //GET A INVOICE ID FUNCTION
//   getMaxInvoiceId() async {
//     String prefix = await dbOperations.queryPrefixName();
//     bool isResetSalesNo = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
//     String prefixMax = await salesDetailsDBScript.queryGetMaxInvoiceId(prefix, isResetSalesNo);
//     if (prefixMax == "0") {
//       return prefix + 1.toString();
//     } else {
//       return prefix + (int.parse(prefixMax.substring(1)) + 1).toString();
//     }
//   }

//   Future<int> startReportPrint(BuildContext context, Sales sales, String flag) async {
//     List<Transactions> printSaleList = [];
//     printSaleList.addAll(salesTransactionList);
//     if (!await printerUtilities.checkPrinterConnection(context)) {
//       return 0;
//     } else {
//       await printerUtilities.getPrinterDetails();
//       if (flag == "Sale") {
//         await onPressSave(sales);
//       }

//       if (flag == "Sale") {
//         if (printerForBill.printerFormat == "Default") {
//           if (printerForBill.paperSize == 58) {
//             await salesPrint.salesPrint(sales, "SO", printSaleList, paymentMappingList);
//           } else {
//             await salesPrint.salesPrint80MM(sales, "SO", printSaleList, paymentMappingList);
//           }
//         } else {
//           if (printerForBill.paperSize == 58) {
//             await salesPrint.salesPrintTvs(sales, "SO", printSaleList, paymentMappingList);
//           } else {
//             await salesPrint.salesPrintTvs80MM(sales, "SO", printSaleList, paymentMappingList);
//           }
//         }
//       } else {
//         if (printerForKOT.printerFormat == "Default") {
//           if (printerForKOT.paperSize == 58) {
//             await salesPrint.salesPrintKOT(sales, sales.tableName, printerForKOT, printSaleList);
//           } else {
//             await salesPrint.salesPrintKOT80MM(sales, sales.tableName, printerForKOT, printSaleList);
//           }
//         } else {
//           if (printerForKOT.paperSize == 58) {
//             await salesPrint.salesPrintKOTTVS(sales, sales.tableName, printerForKOT, printSaleList);
//           } else {
//             await salesPrint.salesPrintKOTTvs80MM(sales, sales.tableName, printerForKOT, printSaleList);
//           }
//         }
//       }
//       return 1;
//     }
//   }

//   Future<int> onPressPrint(Sales model, String flag) async {
//     LoadingAlertWidget.onLoading(context);;
//     int printResult = 0;
//     await queryGetSalesDetails(model.salesId, flag);
//     if (multiPrint == true) {
//       // await printerUtilities.initPrint(printerForBill);
//       printResult = await startReportPrint(context, model, flag);
//       // printerUtilities.disconnect();
//       LoadingAlertWidget.onStopping();
//       return printResult;
//     } else {
//       printResult = await startReportPrint(context, model, flag);
//       LoadingAlertWidget.onStopping();
//       return printResult;
//     }
//   }

//   saveTransactions(id) async {
//     for (Transactions transaction in salesTransactionList) {
//       transaction.isIGST = isIGST;
//       transaction.sync = 0;
//       await salesTransDBScript.insertSalesTransactions(transaction);
//       await salestransStagingDBScript.deleteSalesTransactionStaging(transaction.transactId);
//     }
//   }

//   onPressSave(Sales model) async {
//     try {
//       model.invoiceId = await getMaxInvoiceId();
//       model.isActiveSale = null;
//       model.sync = 0;
//       model.date = DateTime.now().toString();
//       model.paymentType = null;
//       model.customerName = isCustomer
//           ? customerNameController.text != ""
//               ? customerNameController.text
//               : null
//           : model.customerName;
//       model.customerID = selectCustomer?.customerID;
//       model.paymentName = selectPaymentType?.paymentName;
//       model.returnAmount = returnAmount;
//       model.givenAmount = givenAmountController.text != "" ? givenAmountController.text : null;
//       model.mobileNo = mobileNoController.text != "" ? mobileNoController.text : null;
//       model.isIGST = isIGST;
//       await salesDetailsDBScript.updateSalesActiveTable(model);
//       await salesFun.salesPaymentMappingMethod(paymentMappingList, saleId: model.salesId);

//       if (isCustomer && model?.customerID != null) {
//         await insertSalesTypeFunction(model);
//       }
//       await saveTransactions(model.salesId);
//       showToast("Sales Completed");
//       await getAllActiveSales();
//       selectPaymentType = null;
//       mobileNoController.clear();
//       selectCustomer = null;
//       return 1;
//     } on Exception catch (e) {
//       return 0;
//     }
//   }

//   insertSalesTypeFunction(Sales reports) async {
//     var uuid = new Uuid();
//     SalesType model = SalesType();
//     model.salesTypeID = uuid.v4().toString();
//     model.salesID = reports.salesId;
//     model.customerID = reports.customerID;
//     model.createdDate = reports.date;
//     model.salesType = "debit";
//     model.syncStatus = 0;
//     model.rowStatus = 0;
//     model.workspaceID = MyApp.activeWorkspace.workspaceId;
//     model.amount = reports.totalAmount;
//     await salesTypeDB.insertSalesType(model);
//     Balance balance = Balance();
//     balance.customerID = reports?.customerID;
//     balance.balance = reports.totalAmount;
//     await fnUtilities.insertOrUpdateBalance(balance, "debit");
//     if (givenAmountController.text != "0" && givenAmountController.text != "") {
//       model.salesTypeID = uuid.v4().toString();
//       model.salesType = "credit";
//       model.paymentTypeID = null;
//       model.amount = givenAmountController.text;
//       await salesTypeDB.insertSalesType(model);
//       balance.customerID = reports?.customerID;
//       balance.balance = reports.totalAmount;
//       await fnUtilities.insertOrUpdateBalance(balance, "credit");
//     }
//   }

//   checkSalesDelete() async {
//     for (Sales item in activeTablesList) {
//       await queryAllReportsWithProductsByDateToDate(item.salesId);
//       if (isActiveSaleList.isEmpty) {
//         await salesDetailsDBScript.deleteSales(item.salesId);
//       }
//     }
//     await getAllActiveSales();
//   }

//   deleteOrderDialog(BuildContext context, Sales sale, int transactSaleNo) {
//     bool isDeleteLock = false;
//     double width = MediaQuery.of(context).size.width;
//     return showDialog(
//         context: context,
//         builder: (context) {
//           return Center(
//             child: SingleChildScrollView(
//               child: AlertDialog(
//                   content: Container(
//                 //  width: double.maxFinite,
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   mainAxisSize: MainAxisSize.max,
//                   children: [
//                     Container(
//                       child: text("Are you sure want to delete order?", textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
//                     ),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () {
//                             if (!isDeleteLock) {
//                               isDeleteLock = true;
//                               Navigator.pop(context);
//                               isDeleteLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: isBigLayout ? width * 0.18 : width * 0.3,
//                             child: text("Cancel", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         SizedBox(
//                           width: 5,
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             if (!isDeleteLock) {
//                               isDeleteLock = true;
//                               saleID = null;
//                               await salestransStagingDBScript.deleteSalesTransactionStagingBySaleNo(sale.salesId, transactSaleNo);
//                               await queryGetSalesProducts();
//                               await getAllTransaction(sale.salesId);
//                               await findTotal(sale);
//                               sale.sync = 0;
//                               await salesDetailsDBScript.updateSalesActiveTable(sale);
//                               await checkSalesDelete();
//                               Navigator.pop(context);
//                               isDeleteLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: isBigLayout ? width * 0.18 : width * 0.3,
//                             child: text("Delete", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               )),
//             ),
//           );
//         });
//   }

//   //CALCULATE TOTAL AMOUNT
//   findTotal(Sales sales) {
//     double total = 0.0;
//     for (Transactions p in salesTransactionList) {
//       total += double.parse(p.amount);
//     }
//     if (sales?.discountPrice != null) {
//       total = total - double.parse(sales?.discountPrice);
//     }
//     sales.totalAmount = total.toString();
//   }

//   customDiscountDialog(BuildContext context1, Sales sales) async {
//     double width = MediaQuery.of(context1).size.width;
//     double height = MediaQuery.of(context1).size.height;
//     String selectedDiscountOn = "Percentage";
//     bool isDiscountEmpty = false;
//     bool isDiscountLock = false;
//     final discountController = new TextEditingController();
//     return showDialog(
//         barrierDismissible: true,
//         context: context,
//         builder: (BuildContext context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return AlertDialog(
//               title: Text(
//                 "Manual Discount",
//                 style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
//               ),
//               content: Container(
//                 //width: double.maxFinite,
//                 height: height / 3,
//                 child: Column(
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: <Widget>[
//                     Column(
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: [
//                         SizedBox(
//                           width: isBigLayout ? width * 0.4 : width * 0.7,
//                           child: InputDecorator(
//                             decoration: InputDecoration(
//                               contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 1.0),
//                               labelText: 'Payment Type',
//                               border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
//                             ),
//                             child: DropdownButtonHideUnderline(
//                               child: new DropdownButton<String>(
//                                 hint: Text("Discount On"),
//                                 value: selectedDiscountOn,
//                                 items: <String>['Percentage', 'Fixed Amount'].map((String value) {
//                                   return new DropdownMenuItem<String>(
//                                     value: value,
//                                     child: new Text(value),
//                                   );
//                                 }).toList(),
//                                 onChanged: (String value) async {
//                                   if (!isDiscountLock) {
//                                     isDiscountLock = true;
//                                     setState(() {
//                                       selectedDiscountOn = value;
//                                     });
//                                     isDiscountLock = false;
//                                   }
//                                 },
//                               ),
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           height: 10,
//                         ),
//                         selectedDiscountOn != null
//                             ? Column(
//                                 children: [
//                                   EditText(
//                                     text: selectedDiscountOn == "Percentage" ? "Percentage (%)" : "Amount (Rs)",
//                                     isPassword: false,
//                                     mController: discountController,
//                                     inputType: TextInputType.number,
//                                     inputFormate: [
//                                       FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
//                                     ],
//                                     validator: validator.validateTextField,
//                                     inputAction: TextInputAction.next,
//                                   ),
//                                   isDiscountEmpty
//                                       ? Container(
//                                           width: isBigLayout ? width * 0.2 : width * 0.3,
//                                           alignment: Alignment.centerLeft,
//                                           padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
//                                           child: const Text(
//                                             "This field is required",
//                                             style: TextStyle(color: Colors.red, fontSize: 11.5),
//                                           ),
//                                         )
//                                       : Container()
//                                 ],
//                               )
//                             : Container(
//                                 height: 30,
//                               ),
//                       ],
//                     ),
//                     SizedBox(
//                       height: 40,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () {
//                             if (!isDiscountLock) {
//                               isDiscountLock = true;
//                               Navigator.pop(context1);
//                               isDiscountLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: isBigLayout ? width * 0.18 : width * 0.3,
//                             child: text("Cancel", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             if (!isDiscountLock) {
//                               isDiscountLock = true;
//                               if (selectedDiscountOn != null) {
//                                 if (discountController.text != "") {
//                                   setState(() {
//                                     isDiscountEmpty = false;
//                                   });
//                                   double tAmount = 0.0;
//                                   for (Transactions d in salesTransactionList) {
//                                     tAmount += double.parse(d.actualPrice);
//                                     d.discountPrice = null;
//                                     d.discountFormula = null;
//                                   }
//                                   String discountPrice;
//                                   if (selectedDiscountOn == "Percentage") {
//                                     discountPrice = (((tAmount * double.parse(discountController.text)) / 100)).toString();
//                                   } else {
//                                     discountPrice = discountController.text;
//                                   }
//                                   sales.discountFormula = "Manual_Discount";
//                                   sales.discountPrice = discountPrice;
//                                   Navigator.pop(context1);
//                                   await discount.callCalculate(null);
//                                 } else {
//                                   setState(() {
//                                     isDiscountEmpty = true;
//                                   });
//                                 }
//                               }
//                               isDiscountLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: isBigLayout ? width * 0.18 : width * 0.3,
//                             child: text("Apply", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: selectedDiscountOn != null ? buttonThemeColor : Colors.grey, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             );
//           });
//         });
//   }

//   couponDialog(BuildContext context1, Sales sales) async {
//     double width = MediaQuery.of(context1).size.width;
//     double height = MediaQuery.of(context1).size.height;
//     List<Discounts> discountList = await discount.checkValidDateDiscount("C", list: true);
//     bool isDialogLock = false;
//     return showDialog(
//         barrierDismissible: true,
//         context: context1,
//         builder: (BuildContext context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return AlertDialog(
//               title: Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
//                 Container(
//                   child: text("Coupons", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge),
//                 ),
//               ]),
//               content: Container(
//                 //width: double.maxFinite,
//                 height: height / 2.5,
//                 child: Column(
//                   children: [
//                     Container(
//                       width: isBigLayout ? width * 0.4 : width * 0.6,
//                       height: height / 3.2,
//                       child: discountList.length > 0
//                           ? ListView.builder(
//                               shrinkWrap: true,
//                               itemCount: discountList.length,
//                               itemBuilder: (BuildContext context1, int index) {
//                                 var item = discountList[index];
//                                 String type = discountType == "Sales" ? "S" : "P";
//                                 return type == item.type
//                                     ? Container(
//                                         height: 40,
//                                         child: Column(
//                                           children: [
//                                             Padding(
//                                               padding: const EdgeInsets.all(4.0),
//                                               child: Row(
//                                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                                 children: <Widget>[
//                                                   InkWell(
//                                                     onTap: () async {
//                                                       if (!isDialogLock) {
//                                                         isDialogLock = true;
//                                                         for (Transactions d in ListUtility.saleList) {
//                                                           d.discountPrice = null;
//                                                           d.discountFormula = null;
//                                                         }
//                                                         if (discountType == "Product") {
//                                                           await discount.calculateDiscount("C", discount: item);
//                                                         } else {
//                                                           Discounts discountModel = await discount.salesCommonDiscount("C", discount: item, saleID: null, saleNum: null);
//                                                           sales.discountFormula = discountModel?.formula;
//                                                           sales.discountPrice = discountModel?.discountAmount;
//                                                         }
//                                                         Navigator.pop(context1);
//                                                         await discount.callCalculate(null);
//                                                         isDialogLock = false;
//                                                       }
//                                                     },
//                                                     child: Row(
//                                                       children: [
//                                                         Container(
//                                                           width: isBigLayout ? width * 0.28 : width * 0.4,
//                                                           child: text(item.couponName != null ? item.couponName : "",
//                                                               textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
//                                                         ),
//                                                         Container(
//                                                           width: isBigLayout ? width * 0.1 : width * 0.2,
//                                                           child: text("${item.discount} ${item.discountOn == "P" ? "%" : ""}",
//                                                               textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
//                                                         ),
//                                                       ],
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                             divider(),
//                                           ],
//                                         ),
//                                       )
//                                     : Container();
//                               },
//                             )
//                           : Text("Coupon Not Available"),
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.end,
//                       children: [
//                         GestureDetector(
//                           onTap: () {
//                             if (!isDialogLock) {
//                               isDialogLock = true;
//                               Navigator.pop(context1);
//                               isDialogLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 40,
//                             width: isBigLayout ? width * 0.18 : width * 0.3,
//                             child: text("Close", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               ),
//             );
//           });
//         });
//   }

//   commonDiscountFunction(Sales sales) async {
//     await getAllTransaction(sales.salesId);
//     if (isDiscount) {
//       for (Transactions d in salesTransactionList) {
//         d.discountPrice = null;
//         d.discountFormula = null;
//       }
//       if (discountType == "Product") {
//         await discount.calculateDiscount(
//           "A",
//         );
//       } else {
//         Discounts discountModel = await discount.salesCommonDiscount("A", saleID: null, saleNum: null);
//         sales.discountFormula = discountModel?.formula;
//         sales.discountPrice = discountModel?.discountAmount;
//       }
//     }
//     await discount.callCalculate(null);
//     setState(() {
//       findTotal(sales);
//     });
//   }

//   //Payment Type DIALOG
//   saveDialog(BuildContext context, Sales obj, String flag) {
//     double width = MediaQuery.of(context).size.width;
//     bool isSaveDialogLock = false;
//     try {
//       selectCustomer = customerList.firstWhere((element) => element.customerID == obj.customerID);
//     } catch (ex) {
//       selectCustomer = null;
//     }
//     returnAmount = 0.0;
//     customerNameController.text = obj.customerName != null ? obj.customerName.toString() : "";
//     mobileNoController.text = obj?.mobileNo != null ? obj?.mobileNo : "";
//     givenAmountController.clear();
//     isPaymentTypeIsEmpty = false;
//     isPaymentAmountEmpty = false;
//     isPaymentMappingListExist = false;
//     paymentMappingList.clear();
//     paymentAmountController.clear();
//     selectPaymentType = null;
//     bool isSameAmount = false;
//     isIGST = false;
//     int printResult = 0;
//     total = double.parse(obj.totalAmount);
//     paymentMappingList.clear();

//     return showDialog(
//         context: context,
//         barrierDismissible: false,
//         builder: (context) {
//           return StatefulBuilder(builder: (context, setState) {
//             saveAlertSetState = setState;
//             return Center(
//               child: SingleChildScrollView(
//                 child: AlertDialog(
//                     content: Container(
//                   width: isBigLayout ? width * 0.4 : width * 0.8,
//                   child: Form(
//                     key: validator.formkey,
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       mainAxisSize: MainAxisSize.max,
//                       children: [
//                         isDiscount
//                             ? Column(
//                                 mainAxisAlignment: MainAxisAlignment.start,
//                                 children: [
//                                   Container(
//                                     width: width / 1.2,
//                                     alignment: Alignment.centerLeft,
//                                     child: Text("Discount: "),
//                                   ),
//                                   SizedBox(
//                                     height: 3,
//                                   ),
//                                   Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       GestureDetector(
//                                         onTap: () async {
//                                           if (!isLock) {
//                                             isLock = true;
//                                             await customDiscountDialog(context, obj);
//                                             setState(() {
//                                               findTotal(obj);
//                                             });
//                                             isLock = false;
//                                           }
//                                         },
//                                         child: Container(
//                                           alignment: Alignment.center,
//                                           height: 40,
//                                           width: isBigLayout ? width * 0.18 : width * 0.3,
//                                           child: text("Manual", textColor: secondaryTextColor, isCentered: true),
//                                           decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                         ),
//                                       ),
//                                       GestureDetector(
//                                         onTap: () async {
//                                           if (!isLock) {
//                                             isLock = true;
//                                             await couponDialog(context, obj);
//                                             setState(() {
//                                               findTotal(obj);
//                                             });
//                                             isLock = false;
//                                           }
//                                         },
//                                         child: Container(
//                                           alignment: Alignment.center,
//                                           height: 40,
//                                           width: isBigLayout ? width * 0.18 : width * 0.3,
//                                           child: text("Coupon", textColor: secondaryTextColor, isCentered: true),
//                                           decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                         ),
//                                       ),
//                                     ],
//                                   ),
//                                   SizedBox(
//                                     height: 5,
//                                   ),
//                                   Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Text(
//                                         "Total: ",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                       Text(
//                                         "Rs.${(double.parse(obj.totalAmount) + (obj.discountPrice != null ? double.parse(obj.discountPrice) : 0.00)).toStringAsFixed(2)}",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                     ],
//                                   ),
//                                   Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Text(
//                                         "Discount: ",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                       Text(
//                                         "Rs.${obj.discountPrice != null ? double.parse(obj.discountPrice).toStringAsFixed(2) : 0}",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                     ],
//                                   ),
//                                 ],
//                               )
//                             : Container(),
//                         SizedBox(
//                           height: 10,
//                         ),
//                         Container(
//                           alignment: Alignment.centerLeft,
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Text(
//                                 "Choose customer: ",
//                               ),
//                               SizedBox(
//                                 height: 3,
//                               ),
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   GestureDetector(
//                                     onTap: () async {
//                                       if (!isLock) {
//                                         isLock = true;
//                                         setState(() {
//                                           if (obj != null && selectCustomer == null) {
//                                             customerNameController.text = obj?.customerName != null ? obj?.customerName : '';
//                                             mobileNoController.text = obj?.mobileNo != null ? obj?.mobileNo : "";
//                                           } else {
//                                             selectCustomer = null;
//                                             customerNameController.clear();
//                                             mobileNoController.clear();
//                                           }
//                                         });
//                                         isLock = false;
//                                       }
//                                     },
//                                     child: Container(
//                                       alignment: Alignment.center,
//                                       height: 40,
//                                       width: isBigLayout ? width * 0.18 : width * 0.3,
//                                       child: text("Guest", textColor: secondaryTextColor, isCentered: true),
//                                       decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                     ),
//                                   ),
//                                   GestureDetector(
//                                     onTap: () async {
//                                       if (!isLock) {
//                                         isLock = true;
//                                         Navigator.push(
//                                             context,
//                                             MaterialPageRoute(
//                                                 builder: (context) => CustomerPage(
//                                                       pageType: "Sale",
//                                                     ))).then((value) async {
//                                           setState(() {
//                                             try {
//                                               if (value == null) {
//                                                 selectCustomer = null;
//                                                 customerNameController.clear();
//                                                 mobileNoController.clear();
//                                               } else {
//                                                 selectCustomer = value;
//                                                 customerNameController.text = selectCustomer.firstName + " " + selectCustomer.lastName;
//                                                 mobileNoController.text = selectCustomer.mobileNo;
//                                               }
//                                             } catch (e) {
//                                               selectCustomer = null;
//                                             }
//                                           });
//                                         });
//                                         isLock = false;
//                                       }
//                                     },
//                                     child: Container(
//                                       alignment: Alignment.center,
//                                       height: 40,
//                                       width: isBigLayout ? width * 0.18 : width * 0.3,
//                                       child: text("Customers", textColor: secondaryTextColor, isCentered: true),
//                                       decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                               SizedBox(
//                                 height: 10,
//                               ),
//                               Container(
//                                 child: EditText(
//                                   mController: customerNameController,
//                                   inputType: TextInputType.text,
//                                   isPassword: false,
//                                   text: "Customer Name",
//                                   readOnly: selectCustomer == null ? false : true,
//                                 ),
//                               ),
//                               SizedBox(
//                                 height: 10,
//                               ),
//                               Container(
//                                 child: EditText(
//                                   mController: mobileNoController,
//                                   isPassword: false,
//                                   text: "Mobile No",
//                                   inputType: TextInputType.phone,
//                                   inputFormate: [
//                                     FilteringTextInputFormatter.digitsOnly,
//                                   ],
//                                   maxLength: 10,
//                                   readOnly: selectCustomer == null ? false : true,
//                                 ),
//                               ),
//                             ],
//                           ),
//                         ),
//                         SizedBox(
//                           height: 10,
//                         ),
//                         isPaymentType ? paymentInputFieldWidget(context) : SizedBox(),
//                         SizedBox(
//                           height: isPaymentType ? 20 : 0,
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             Text(
//                               "Net Total: ",
//                               style: TextStyle(fontWeight: FontWeight.bold),
//                             ),
//                             Text(
//                               "Rs.${total.toStringAsFixed(2)}",
//                               style: TextStyle(fontWeight: FontWeight.bold),
//                             ),
//                           ],
//                         ),
//                         isReturnAmount && flag != "save"
//                             ? Column(
//                                 mainAxisAlignment: MainAxisAlignment.start,
//                                 children: [
//                                   Row(
//                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                     children: [
//                                       Text(
//                                         "Return Amount: ",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                       Text(
//                                         "Rs.${returnAmount.toStringAsFixed(2)}",
//                                         style: TextStyle(fontWeight: FontWeight.bold),
//                                       ),
//                                     ],
//                                   ),
//                                 ],
//                               )
//                             : SizedBox.shrink(),
//                         addTax
//                             ? new CheckboxListTile(
//                                 dense: false,
//                                 controlAffinity: ListTileControlAffinity.leading,
//                                 contentPadding: EdgeInsets.zero,
//                                 value: isIGST,
//                                 title: text("Other state tax", textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isLongText: true),
//                                 onChanged: (bool newValue) {
//                                   if (!isLock) {
//                                     isLock = true;
//                                     setState(() {
//                                       isIGST = newValue;
//                                     });
//                                     isLock = false;
//                                   }
//                                 })
//                             : Container(),
//                         flag != "save"
//                             ? SizedBox(
//                                 height: 20,
//                               )
//                             : Container(),
//                         Text(flag == "save" ? "Click Ok to collect bill" : "Click Ok to collect bill and print"),
//                         SizedBox(
//                           height: 20,
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           crossAxisAlignment: CrossAxisAlignment.end,
//                           children: <Widget>[
//                             GestureDetector(
//                               onTap: () async {
//                                 if (!isSaveDialogLock) {
//                                   isSaveDialogLock = true;
//                                   if (paymentMappingList.isNotEmpty || !isPaymentType) {
//                                     setState(() {
//                                       isPaymentTypeIsEmpty = false;
//                                       isPaymentMappingListExist = false;
//                                       isPaymentAmountEmpty = false;
//                                     });
//                                     isSaveDialogLock = false;
//                                     if (flag == "save") {
//                                       printResult = await onPressSave(obj);
//                                       if (printResult == 1) {
//                                         Navigator.pop(context);
//                                       }
//                                       isSaveDialogLock = false;
//                                       return printResult;
//                                     } else {
//                                       printResult = await onPressPrint(obj, "Sale");
//                                       if (printResult == 1) {
//                                         Navigator.pop(context);
//                                       }
//                                       isSaveDialogLock = false;
//                                       return printResult;
//                                     }
//                                   } else {
//                                     if (paymentMappingList.isEmpty) {
//                                       setState(() {
//                                         isPaymentMappingListExist = true;
//                                       });
//                                     }
//                                   }
//                                   isSaveDialogLock = false;
//                                 }
//                               },
//                               child: Container(
//                                 alignment: Alignment.center,
//                                 height: 45,
//                                 width: isBigLayout ? width * 0.4 : width * 0.65,
//                                 child: text(flag == "save" ? "Ok" : "Ok & Print", textColor: secondaryTextColor, isCentered: true),
//                                 decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 )),
//               ),
//             );
//           });
//         }).then((val) {
//       setState(() {
//         findTotal(obj);
//       });
//       return printResult;
//     });
//   }

//   queryAllReportsWithProductsByDateToDate(String saleID) async {
//     final allRows = await salestransStagingDBScript.queryActiveTransactionList(saleID);
//     if (this.mounted) {
//       setState(() {
//         isActiveSaleList.clear();
//         allRows.forEach((row) => isActiveSaleList.add(Transactions.fromMap(row)));
//       });
//     }
//   }

//   activeTableUpdateTransaction(Sales sales) async {
//     final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(sales.salesId, tableName: "staging", isActiveSaleNo: sales?.isActiveSale);
//     setState(() {
//       salesTransactionList.clear();
//       allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
//     });
//     return salesTransactionList;
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;
//     isBigLayout = (width > tabletWidth && saleLayout == "DesktopLayout") || Platform.isWindows;

//     return Scaffold(
//       backgroundColor: appThemeColor,
//       resizeToAvoidBottomInset: false,
//       appBar: BaseAppBar(
//         title: text(pageTitle, textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
//         appBar: AppBar(),
//         widgets: <Widget>[
//           InkWell(
//             onTap: () async {
//               if (!isLock) {
//                 isLock = true;
//                 Navigator.pushReplacement(
//                     context,
//                     MaterialPageRoute(
//                         builder: (context) => saleLayout == "SaleLayout2"
//                             ? ItemsPageLayout2(
//                                 saleType: "SO",
//                               )
//                             : saleLayout == "SaleLayout3"
//                                 ? ItemsPage(
//                                     saleType: "SO",
//                                   )
//                                 : SalesMainPage(
//                                     saleType: "SO",
//                                   )));
//                 isLock = false;
//               }
//             },
//             child: Row(
//               children: [
//                 Container(
//                   alignment: Alignment.topRight,
//                   padding: EdgeInsets.only(right: 20),
//                   height: 38,
//                   width: width / 4,
//                   child: text("New", textColor: secondaryTextColor, isCentered: true),
//                   decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                 ),
//               ],
//             ),
//           ),
//         ],
//       ),
//       body: Column(
//         children: [
//           SizedBox(
//             height: 3,
//           ),
//           if (isLoading) ...[
//             Expanded(
//                 child: Center(
//                     child: CircularProgressIndicator(
//               color: secondaryTextColor,
//             )))
//           ] else ...[
//             Expanded(
//                 child: SingleChildScrollView(
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   Padding(
//                     padding: const EdgeInsets.all(10.0),
//                     child: Column(
//                       children: [
//                         activeTablesList.isNotEmpty
//                             ? Align(
//                                 alignment: Alignment.topCenter,
//                                 child: Wrap(
//                                     // padding: EdgeInsets.only(top: 5.0),
//                                     children: List.generate(activeTablesList.length, (index) {
//                                   var item = activeTablesList[index];
//                                   List<Transactions> tempSaleProductList = [];
//                                   if (item != null) {
//                                     tempSaleProductList = saleProductList.where((element) => element.salesId == item.salesId).toList();
//                                   }
//                                   return GestureDetector(
//                                     onLongPress: () {
//                                       showDialog(
//                                           context: context,
//                                           builder: (BuildContext context) {
//                                             return AlertDialog(
//                                               content: Text("Are you sure you want to delete ?"),
//                                               actions: <Widget>[
//                                                 GestureDetector(
//                                                   onTap: () async {
//                                                     if (!isLock) {
//                                                       isLock = true;
//                                                       await salesDetailsDBScript.updateSalesDelete(item.salesId);
//                                                       await salesTransDBScript.updateSalesTransactionDelete(item.salesId);
//                                                       await getAllActiveSales();
//                                                       Navigator.pop(context);
//                                                       isLock = false;
//                                                     }
//                                                   },
//                                                   child: Container(
//                                                     alignment: Alignment.center,
//                                                     height: 40,
//                                                     width: width / 5,
//                                                     child: text("Yes", textColor: secondaryTextColor, isCentered: true),
//                                                     decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                                   ),
//                                                 ),
//                                                 GestureDetector(
//                                                   onTap: () {
//                                                     if (!isLock) {
//                                                       isLock = true;
//                                                       Navigator.pop(context);
//                                                       isLock = false;
//                                                     }
//                                                   },
//                                                   child: Container(
//                                                     alignment: Alignment.center,
//                                                     height: 40,
//                                                     width: width / 5,
//                                                     child: text("Cancel", textColor: secondaryTextColor, isCentered: true),
//                                                     decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                                                   ),
//                                                 ),
//                                               ],
//                                             );
//                                           });
//                                     },
//                                     child: Card(
//                                       child: Container(
//                                         width: width > tabletWidth ? width * 0.3 : width,
//                                         padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
//                                         child: InkWell(
//                                           onTap: () async {
//                                             await queryAllReportsWithProductsByDateToDate(item.salesId);
//                                             setState(() {
//                                               if (saleID == null) {
//                                                 saleID = item.salesId;
//                                               } else if (saleID != item.salesId && saleID != null) {
//                                                 saleID = item.salesId;
//                                               } else {
//                                                 saleID = null;
//                                               }
//                                             });
//                                           },
//                                           child: Column(
//                                             children: <Widget>[
//                                               /// show customer name and product details
//                                               Column(
//                                                 children: [
//                                                   Row(
//                                                     mainAxisAlignment: MainAxisAlignment.start,
//                                                     children: [
//                                                       text(item.customerName != null ? item.customerName : "",
//                                                           textColor: buttonThemeColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: false, isLongText: true),
//                                                     ],
//                                                   ),
//                                                   Row(
//                                                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                                                     children: [
//                                                       text(double.parse(item.totalAmount).toStringAsFixed(2),
//                                                           textColor: buttonThemeColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: false, isLongText: true),
//                                                       Row(
//                                                         children: [
//                                                           IconButton(
//                                                               onPressed: () {
//                                                                 if (!isLock) {
//                                                                   isLock = true;
//                                                                   item.isActiveSale = null;
//                                                                   Navigator.push(
//                                                                       context,
//                                                                       MaterialPageRoute(
//                                                                           builder: (context) => saleLayout == "SaleLayout2"
//                                                                               ? ItemsPageLayout2(sales: item, saleType: "SO")
//                                                                               : saleLayout == "SaleLayout3"
//                                                                                   ? ItemsPage(sales: item, saleType: "SO")
//                                                                                   : SalesMainPage(
//                                                                                       sales: item,
//                                                                                       saleType: "SO",
//                                                                                     ))).then((value) async {
//                                                                     await getAllActiveSales();
//                                                                     await queryGetSalesProducts();
//                                                                   });
//                                                                   isLock = false;
//                                                                 }
//                                                               },
//                                                               icon: Icon(Icons.add)),
//                                                           IconButton(
//                                                               onPressed: () async {
//                                                                 if (!isLock) {
//                                                                   isLock = true;
//                                                                   RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
//                                                                   if (await rolePermissions.isEditPermissions(dbOperations.returnPermission("Reports")) == true) {
//                                                                     Navigator.push(
//                                                                         context,
//                                                                         MaterialPageRoute(
//                                                                             builder: (context) => ReportsViewPage(
//                                                                                   reports: item,
//                                                                                   tableName: "staging",
//                                                                                 )));
//                                                                   } else {
//                                                                     toast("You don't have permission");
//                                                                   }
//                                                                   isLock = false;
//                                                                 }
//                                                               },
//                                                               icon: Icon(Icons.preview)),
//                                                           IconButton(
//                                                               onPressed: () async {
//                                                                 await queryAllReportsWithProductsByDateToDate(item.salesId);
//                                                                 setState(() {
//                                                                   if (saleID == null) {
//                                                                     saleID = item.salesId;
//                                                                   } else if (saleID != item.salesId && saleID != null) {
//                                                                     saleID = item.salesId;
//                                                                   } else {
//                                                                     saleID = null;
//                                                                   }
//                                                                 });
//                                                               },
//                                                               icon: Icon(Icons.expand)),
//                                                         ],
//                                                       ),
//                                                     ],
//                                                   ),
//                                                   Container(
//                                                     width: width / 1.1,
//                                                     alignment: Alignment.centerLeft,
//                                                     child: SingleChildScrollView(
//                                                       scrollDirection: Axis.horizontal,
//                                                       child: Row(
//                                                         children: [
//                                                           for (int i = 0; i < tempSaleProductList.length; i++)
//                                                             (i == tempSaleProductList.length - 1)
//                                                                 ? text(tempSaleProductList[i]?.name + ".",
//                                                                     textColor: primaryTextColor,
//                                                                     fontSize: textSizeSmall,
//                                                                     fontFamily: fontSemibold,
//                                                                     isCentered: false,
//                                                                     isLongText: false)
//                                                                 : text(tempSaleProductList[i]?.name + ", ",
//                                                                     textColor: primaryTextColor,
//                                                                     fontSize: textSizeSmall,
//                                                                     fontFamily: fontSemibold,
//                                                                     isCentered: false,
//                                                                     isLongText: false),
//                                                         ],
//                                                       ),
//                                                     ),
//                                                   ),
//                                                 ],
//                                               ),

//                                               /// show content while
//                                               saleID == item.salesId
//                                                   ? Column(
//                                                       children: [
//                                                         SizedBox(
//                                                           height: 10,
//                                                         ),
//                                                         Divider(
//                                                           thickness: .5,
//                                                         ),
//                                                         for (Transactions order in isActiveSaleList)
//                                                           Container(
//                                                             // height: 40,
//                                                             alignment: Alignment.centerLeft,
//                                                             child: Row(
//                                                               children: [
//                                                                 Expanded(
//                                                                   child: Container(
//                                                                     child: SingleChildScrollView(
//                                                                       scrollDirection: Axis.horizontal,
//                                                                       child: Wrap(
//                                                                         children: [
//                                                                           for (int i = 0; i < tempSaleProductList.length; i++)
//                                                                             if (order?.isActiveSale == tempSaleProductList[i]?.isActiveSale)
//                                                                               (i == tempSaleProductList.length - 1)
//                                                                                   ? text(tempSaleProductList[i]?.name + ".",
//                                                                                       textColor: primaryTextColor,
//                                                                                       fontSize: textSizeSmall,
//                                                                                       fontFamily: fontSemibold,
//                                                                                       isCentered: false,
//                                                                                       isLongText: false)
//                                                                                   : text(tempSaleProductList[i]?.name + ", ",
//                                                                                       textColor: primaryTextColor,
//                                                                                       fontSize: textSizeSmall,
//                                                                                       fontFamily: fontSemibold,
//                                                                                       isCentered: false,
//                                                                                       isLongText: false),
//                                                                         ],
//                                                                       ),
//                                                                     ),
//                                                                   ),
//                                                                 ),
//                                                                 Row(
//                                                                   children: [
//                                                                     Container(
//                                                                       child: IconButton(
//                                                                           onPressed: () async {
//                                                                             if (!isLock) {
//                                                                               isLock = true;
//                                                                               item.isActiveSale = order?.isActiveSale;
//                                                                               List<Transactions> list = await activeTableUpdateTransaction(item);
//                                                                               Navigator.push(
//                                                                                   context,
//                                                                                   MaterialPageRoute(
//                                                                                       builder: (context) => saleLayout == "SaleLayout2"
//                                                                                           ? ItemsPageLayout2(sales: item, saleTransactionList: list, saleType: "SO")
//                                                                                           : saleLayout == "SaleLayout3"
//                                                                                               ? ItemsPage(sales: item, saleTransactionList: list, saleType: "SO")
//                                                                                               : SalesMainPage(
//                                                                                                   sales: item,
//                                                                                                   saleTransactionList: list,
//                                                                                                   saleType: "SO",
//                                                                                                 ))).then((value) async {
//                                                                                 saleID = null;
//                                                                                 await getAllActiveSales();
//                                                                                 await queryGetSalesProducts();
//                                                                                 await checkSalesDelete();
//                                                                               });
//                                                                               isLock = false;
//                                                                             }
//                                                                           },
//                                                                           icon: Icon(
//                                                                             Icons.edit,
//                                                                             size: 20,
//                                                                           )),
//                                                                     ),
//                                                                     Container(
//                                                                       child: IconButton(
//                                                                           onPressed: () async {
//                                                                             if (!isLock) {
//                                                                               isLock = true;
//                                                                               deleteOrderDialog(context, item, order?.isActiveSale);
//                                                                               isLock = false;
//                                                                             }
//                                                                           },
//                                                                           icon: Icon(
//                                                                             Icons.delete,
//                                                                             size: 20,
//                                                                           )),
//                                                                     ),
//                                                                     InkWell(
//                                                                       onTap: () async {
//                                                                         if (!isLock) {
//                                                                           isLock = true;
//                                                                           await onPressPrint(item, order?.isActiveSale.toString());
//                                                                           isLock = false;
//                                                                         }
//                                                                       },
//                                                                       child: Container(
//                                                                         child: Row(
//                                                                           children: [
//                                                                             Text(
//                                                                               "KOT - " + (order?.isActiveSale).toString(),
//                                                                               style: TextStyle(fontSize: 12, color: whiteColor),
//                                                                             ),
//                                                                             SizedBox(
//                                                                               width: 1,
//                                                                             ),
//                                                                             Icon(
//                                                                               Icons.kitesurfing,
//                                                                               color: whiteColor,
//                                                                               size: 20,
//                                                                             ),
//                                                                           ],
//                                                                         ),
//                                                                         padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
//                                                                         decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
//                                                                       ),
//                                                                     ),
//                                                                   ],
//                                                                 ),
//                                                               ],
//                                                             ),
//                                                           ),
//                                                         Divider(
//                                                           thickness: .5,
//                                                         ),
//                                                         Container(
//                                                           width: width / 1.1,
//                                                           height: 50,
//                                                           alignment: Alignment.center,
//                                                           child: SingleChildScrollView(
//                                                             scrollDirection: Axis.horizontal,
//                                                             child: Row(
//                                                               mainAxisAlignment: MainAxisAlignment.center,
//                                                               children: [
//                                                                 InkWell(
//                                                                   onTap: () async {
//                                                                     if (!isLock) {
//                                                                       isLock = true;
//                                                                       if (isActiveSaleList.length > 0) {
//                                                                         await onPressPrint(item, "KOT");
//                                                                       }
//                                                                       isLock = false;
//                                                                     }
//                                                                   },
//                                                                   child: Container(
//                                                                     child: Row(
//                                                                       children: [
//                                                                         Text(
//                                                                           "KOT",
//                                                                           style: TextStyle(fontSize: 12, color: whiteColor),
//                                                                         ),
//                                                                         SizedBox(
//                                                                           width: 2,
//                                                                         ),
//                                                                         Icon(
//                                                                           Icons.kitchen,
//                                                                           color: whiteColor,
//                                                                         ),
//                                                                       ],
//                                                                     ),
//                                                                     padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
//                                                                     decoration: boxDecoration(
//                                                                         radius: 10, showShadow: true, bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
//                                                                   ),
//                                                                 ),
//                                                                 SizedBox(
//                                                                   width: 10,
//                                                                 ),
//                                                                 InkWell(
//                                                                   onTap: () async {
//                                                                     if (!isCollectBillLock) {
//                                                                       isCollectBillLock = true;
//                                                                       if (isActiveSaleList.length > 0) {
//                                                                         await commonDiscountFunction(item);
//                                                                         int result = await saveDialog(context, item, "save");
//                                                                         if (successDialogOnSales && result == 1) {
//                                                                           callSuccessDialog(context, item.salesId);
//                                                                         }
//                                                                       }
//                                                                       isCollectBillLock = false;
//                                                                     }
//                                                                   },
//                                                                   child: Container(
//                                                                     child: Row(
//                                                                       children: [
//                                                                         Text(
//                                                                           "Collect Bill",
//                                                                           style: TextStyle(fontSize: 12, color: whiteColor),
//                                                                         ),
//                                                                         SizedBox(
//                                                                           width: 2,
//                                                                         ),
//                                                                         Icon(
//                                                                           Icons.save,
//                                                                           color: whiteColor,
//                                                                         ),
//                                                                       ],
//                                                                     ),
//                                                                     padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
//                                                                     decoration: boxDecoration(
//                                                                         radius: 10, showShadow: true, bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
//                                                                   ),
//                                                                 ),
//                                                                 SizedBox(
//                                                                   width: 10,
//                                                                 ),
//                                                                 InkWell(
//                                                                   onTap: () async {
//                                                                     if (!isCollectBillPrintLock) {
//                                                                       isCollectBillPrintLock = true;
//                                                                       if (isActiveSaleList.length > 0) {
//                                                                         await commonDiscountFunction(item);
//                                                                         int result = await saveDialog(context, item, "print");
//                                                                         if (successDialogOnSales && result == 1) {
//                                                                           callSuccessDialog(context, item.salesId);
//                                                                         }
//                                                                       }
//                                                                       isCollectBillPrintLock = false;
//                                                                     }
//                                                                   },
//                                                                   child: Container(
//                                                                     child: Row(
//                                                                       children: [
//                                                                         Text(
//                                                                           "Collect Bill &",
//                                                                           style: TextStyle(fontSize: 12, color: whiteColor),
//                                                                         ),
//                                                                         SizedBox(
//                                                                           width: 2,
//                                                                         ),
//                                                                         Icon(
//                                                                           Icons.print,
//                                                                           color: whiteColor,
//                                                                         ),
//                                                                       ],
//                                                                     ),
//                                                                     padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
//                                                                     decoration: boxDecoration(
//                                                                         radius: 10, showShadow: true, bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
//                                                                   ),
//                                                                 )
//                                                               ],
//                                                             ),
//                                                           ),
//                                                         ),
//                                                       ],
//                                                     )
//                                                   : Container(),
//                                               // Divider(height: 0.5, color: t5ViewColor)
//                                             ],
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   );
//                                 })),
//                               )
//                             : Center(
//                                 child: Text(
//                                   "No active order available",
//                                   style: textStyle18,
//                                 ),
//                               ),
//                         SizedBox(
//                           height: 10,
//                         ),
//                       ],
//                     ),
//                   ),
//                 ],
//               ),
//             )),
//           ]
//         ],
//       ),
//     );
//   }

//   callSuccessDialog(BuildContext context, String salesId) async {
//     await fnUtilities.salesSuccessDialog(context, salesId);
//   }

//   /// payment dropdown and amount input field
//   paymentInputFieldWidget(BuildContext context) {
//     return Column(
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: <Widget>[
//             Container(
//               width: isBigLayout ? width * 0.20 : width * 0.35,
//               child: InputDecorator(
//                 decoration: InputDecoration(
//                   contentPadding: EdgeInsets.symmetric(horizontal: 3.0, vertical: 1.0),
//                   labelText: 'Payment Type',
//                   border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
//                 ),
//                 child: ListUtility.paymentTypeList.length != 0
//                     ? DropdownButtonHideUnderline(
//                         child: Container(
//                           height: 40,
//                           width: isBigLayout ? width * 0.20 : width * 0.35,
//                           child: DropdownButton<PaymentType>(
//                             hint: Text(
//                               "Payment Type",
//                               style: TextStyle(fontSize: 14),
//                             ),
//                             value: selectPaymentType,
//                             items: ListUtility.paymentTypeList.map((PaymentType value) {
//                               return DropdownMenuItem<PaymentType>(
//                                 value: value,
//                                 child: Text(value.paymentName),
//                               );
//                             }).toList(),
//                             onChanged: (PaymentType val) async {
//                               saveAlertSetState(() {
//                                 selectPaymentType = val;
//                                 FocusScope.of(context).requestFocus(FocusNode());
//                               });
//                               return val;
//                             },
//                           ),
//                         ),
//                       )
//                     : Container(),
//               ),
//             ),
//             SizedBox(
//               width: 5,
//             ),
//             Expanded(
//               child: TextFieldBoxWidget(
//                 text: "Rs",
//                 isPassword: false,
//                 mController: paymentAmountController,
//                 // validator: validator.validateTextField,
//                 inputFormate: [
//                   FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
//                 ],
//                 inputType: TextInputType.number,
//                 inputAction: TextInputAction.done,
//               ),
//             ),
//             SizedBox(
//               width: 5,
//             ),
//             InkWell(
//               onTap: () {
//                 checkValidationForPayment();
//               },
//               child: SvgPicture.asset(
//                 addIconSVG,
//                 color: black,
//               ),
//             )
//           ],
//         ),
//         SizedBox(
//           height: 10,
//         ),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           children: [
//             isPaymentTypeIsEmpty
//                 ? Container(
//                     width: isBigLayout ? width * 0.20 : width * 0.33,
//                     alignment: Alignment.centerLeft,
//                     padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                     child: const Text(
//                       "This field is required",
//                       style: TextStyle(color: Colors.red, fontSize: 11.5),
//                     ),
//                   )
//                 : SizedBox(
//                     width: isBigLayout ? width * 0.20 : width * 0.33,
//                   ),
//             isPaymentAmountEmpty
//                 ? Expanded(
//                     child: Container(
//                       padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                       child: const Text(
//                         "This field is required",
//                         style: TextStyle(color: Colors.red, fontSize: 11.5),
//                       ),
//                     ),
//                   )
//                 : SizedBox.shrink(),
//           ],
//         ),
//         isPaymentMappingListExist
//             ? Container(
//                 alignment: Alignment.centerLeft,
//                 padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
//                 child: const Text(
//                   "Atleast add one payment type using the add button",
//                   style: TextStyle(color: Colors.red, fontSize: 11.5),
//                 ),
//               )
//             : Container(),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//           crossAxisAlignment: CrossAxisAlignment.start,
//           children: [
//             if (paymentMappingList != null && paymentMappingList.length != 0) ...[
//               SizedBox(
//                 width: isBigLayout ? width * 0.40 : width * 0.633,

//                 ///   units list widget
//                 child: paymentListWidget(context),
//               )
//             ] else ...[
//               SizedBox.shrink(),
//             ]
//           ],
//         ),
//       ],
//     );
//   }

//   // payment list
//   Widget paymentListWidget(BuildContext context) {
//     return SingleChildScrollView(
//       child: ListView.builder(
//           itemCount: paymentMappingList.length,
//           scrollDirection: Axis.vertical,
//           physics: ScrollPhysics(),
//           shrinkWrap: true,
//           itemBuilder: (BuildContext context, int index) {
//             var item = paymentMappingList[index];
//             return paymentMappingList[index].status != 2
//                 ? Container(
//                     height: 40,
//                     decoration: BoxDecoration(
//                       border: Border(
//                         bottom: BorderSide(
//                           color: Colors.grey,
//                           width: 0.5,
//                         ),
//                       ),
//                     ),
//                     child: Row(
//                       children: [
//                         SizedBox(
//                           child: Row(
//                             children: [
//                               Container(
//                                 // color: blueViolet,
//                                 // width: width * 0.1571,
//                                 width: width > tabletWidth ? width * 0.15 : width * 0.22,
//                                 child: text(item.paymentName, textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                               ),
//                               Container(
//                                 width: width > tabletWidth ? width * 0.16 : width * 0.22,
//                                 child: text(item.amount.toString(), textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                               ),
//                               Row(
//                                 children: [
//                                   InkWell(
//                                       onTap: () {
//                                         if (!isLock) {
//                                           isLock = true;

//                                           saveAlertSetState(() {
//                                             for (PaymentType model in ListUtility.paymentTypeList) {
//                                               if (model.paymentTypeID == item.paymentId) {
//                                                 paymentAmountController.text = paymentMappingList[index].amount.toStringAsFixed(0);
//                                                 selectPaymentType = model;
//                                                 paymentMappingIndex = index;
//                                               }
//                                             }
//                                           });
//                                           FocusScope.of(context).requestFocus(FocusNode());

//                                           isLock = false;
//                                         }
//                                       },
//                                       child: Icon(Icons.edit)),
//                                   SizedBox(
//                                     width: isBigLayout ? width * 0.05 : width * 0.05,
//                                   ),
//                                   InkWell(
//                                       onTap: () {
//                                         if (!isLock) {
//                                           isLock = true;
//                                           saveAlertSetState(() {
//                                             paymentMappingIndex = index;
//                                             addPaymentMapping(
//                                               selectPaymentType,
//                                               paymentAmountController.text,
//                                               index: paymentMappingIndex,
//                                               status: 2,
//                                             );
//                                             FocusScope.of(context).requestFocus(FocusNode());
//                                           });

//                                           isLock = false;
//                                         }
//                                       },
//                                       child: Icon(
//                                         Icons.delete,
//                                         color: redColor,
//                                       )),
//                                 ],
//                               )
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   )
//                 : SizedBox.shrink();
//           }),
//     );
//   }

//   void addPaymentMapping(PaymentType paymentType, String paymentAmount, {status = 0, index = 0}) async {
//     var temPaymentMappingList = await salesFun.salesPaymentMapping(paymentType, paymentMappingList, status, index, paymentAmount);
//     if (temPaymentMappingList != null) {
//       paymentMappingList = temPaymentMappingList;
//     }
//     paymentMappingIndex = -1;
//     saveAlertSetState(() {
//       isPaymentMappingListExist = false;
//       isPaymentAmountEmpty = false;
//       isPaymentTypeIsEmpty = false;
//       selectPaymentType = null;
//       paymentAmountController.clear();
//     });
//     calculateTotalAmount();

//     FocusScope.of(context).requestFocus(FocusNode());
//   }

//   calculateTotalAmount() {
//     double totalPayedAmount = 0;
//     if (paymentMappingList != null && paymentMappingList.isNotEmpty) {
//       for (int i = 0; i < paymentMappingList.length; i++) {
//         totalPayedAmount = totalPayedAmount + paymentMappingList[i].amount;
//       }
//       setState(() {
//         returnAmount = totalPayedAmount - total;
//       });
//     }
//     if (paymentMappingList.isEmpty) {
//       setState(() {
//         returnAmount = 0.0;
//       });
//     }
//     FocusScope.of(context).requestFocus(FocusNode());
//   }

//   ///Check Validation for payment
//   checkValidationForPayment() {
//     if (paymentAmountController.text.isNotEmpty || selectPaymentType != null) {
//       if (selectPaymentType != null) {
//         if (paymentAmountController.text.isNotEmpty) {
//           saveAlertSetState(() {
//             isPaymentTypeIsEmpty = false;
//             isPaymentAmountEmpty = false;
//             if (paymentMappingIndex == -1) {
//               addPaymentMapping(selectPaymentType, paymentAmountController.text, index: paymentMappingIndex, status: 0);
//             } else {
//               addPaymentMapping(selectPaymentType, paymentAmountController.text, index: paymentMappingIndex, status: 1);
//             }
//             FocusScope.of(context).requestFocus(FocusNode());
//           });
//         } else {
//           saveAlertSetState(() {
//             isPaymentAmountEmpty = true;
//             isPaymentTypeIsEmpty = false;
//           });
//         }
//       } else {
//         saveAlertSetState(() {
//           isPaymentTypeIsEmpty = true;
//           isPaymentAmountEmpty = false;
//         });
//       }
//     } else {
//       saveAlertSetState(() {
//         isPaymentTypeIsEmpty = true;
//         isPaymentAmountEmpty = true;
//         FocusScope.of(context).requestFocus(FocusNode());
//       });
//     }
//   }
// }
