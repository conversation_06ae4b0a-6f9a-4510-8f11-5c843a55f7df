// // ignore_for_file: non_constant_identifier_names

// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:smartbill/model/t5_models.dart';
// import 'package:smartbill/utils/common_function/data_generator.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// import 'package:smartbill/model/workspace_settings.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// 
// import '../../main.dart';
// import 'active_table.dart';
// import 'header_footer.dart';
// import 'itempage_layout2.dart';
// import 'itemsPage.dart';
// import 'sales_main_page.dart';
// import 'active_sales_order.dart';

// class OrderOptions extends StatefulWidget {
//   @override
//   OrderOptionsState createState() => OrderOptionsState();
// }

// class OrderOptionsState extends State<OrderOptions> {
//   bool isLock = false;
//   FnUtilities fnUtilities = new FnUtilities();
//   bool enableTableOrders = false;
//   bool isEatAndPay = false;
//   List<T5Category> mFavouriteList;
//   String saleLayout;
//   double width = 0.0;
//   double height = 0.0;
//   initSettings() async {
//     var EnableTableOrdersValue =
//         await fnUtilities.getWorkspaceSetValue("EnableTableOrders");
//     var SaleLayout = await fnUtilities.getWorkspaceSetValue("SaleLayout",
//         table: "WorkSpaceUserSettings");
//     var EatAndPay = await fnUtilities.getWorkspaceSetValue("EatAndPay");
//     if (SaleLayout == null) {
//       SaleLayout = await initLayoutInsert();
//     }
//     setState(() {
//       if (EnableTableOrdersValue == "1") {
//         enableTableOrders = true;
//       } else {
//         enableTableOrders = false;
//         mFavouriteList.removeWhere((element) => element.name == tableOrderName);
//       }
//       if (EatAndPay == "1") {
//         isEatAndPay = true;
//       } else {
//         isEatAndPay = false;
//         mFavouriteList.removeWhere((element) => element.name == eatPayName);
//       }
//       if (SaleLayout != null) {
//         saleLayout = SaleLayout;
//       } else {
//         saleLayout = "SaleLayout1";
//       }
//     });
//   }

//   initLayoutInsert() async {
//     WorkspaceSettings model = new WorkspaceSettings();
//     model.settingKey = "SaleLayout";
//     model.settingValue = "SaleLayout1";
//     model.sync = 0;
//     model.workspaceId = MyApp.activeWorkspace.workspaceId;
//     await fnUtilities.updateSettingDetails(model,
//         tableType: "WorkSpaceUserSettings");
//     return model.settingValue;
//   }

//   String takeAwayName = "Take Away";
//   String tableOrderName = "Table Order";
//   String eatPayName = "Eat & Pay";

//   initPageDetails() async {
//     takeAwayName =
//         await fnUtilities.getWorkspaceSetValue("TakeAwayName") ?? "Take Away";
//     eatPayName =
//         await fnUtilities.getWorkspaceSetValue("EatAndPayName") ?? "Eat & Pay";
//     tableOrderName = await fnUtilities.getWorkspaceSetValue("TableOrderName") ??
//         "Table Order";
//     mFavouriteList = getOrderItems(takeAwayName, tableOrderName, eatPayName);
//     await initSettings();
//   }

//   @override
//   void initState() {
//     initPageDetails();
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;
//     width = width - 50;
//     
//     return Scaffold(
//       backgroundColor: secondaryTextColor,
//       appBar: BaseAppBar(
//         title: text("Order Type",
//             textColor: secondaryTextColor,
//             fontSize: textSizeNormal,
//             fontFamily: fontMedium),
//         appBar: AppBar(),
//         widgets: <Widget>[],
//       ),
//       drawer: BaseDrawer(),
//       body: SingleChildScrollView(
//         child: Container(
//           padding: EdgeInsets.only(top: 28),
//           alignment: Alignment.topLeft,
//           height: MediaQuery.of(context).size.height - 150,
//           decoration: BoxDecoration(
//             color: secondaryTextColor,
//           ),
//           child: Column(
//             children: <Widget>[
//               SizedBox(height: 10),
//               Expanded(
//                 child: Padding(
//                   padding: EdgeInsets.all(24.0),
//                   child: GridListing(mFavouriteList, false),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//       bottomNavigationBar: BottomNavigation(),
//     );
//     // return Scaffold(
//     //   backgroundColor: secondaryTextColor,
//     //   appBar: BaseAppBar(
//     //     title: text("Order Type",
//     //         textColor: secondaryTextColor,
//     //         fontSize: textSizeNormal,
//     //         fontFamily: fontMedium),
//     //     appBar: AppBar(),
//     //     widgets: <Widget>[],
//     //   ),
//     //   drawer: BaseDrawer(),
//     //   body: SingleChildScrollView(
//     //     child: Column(
//     //       children: [
//     //         Padding(
//     //           padding: const EdgeInsets.all(8.0),
//     //           child: Container(
//     //             width: width / 1.1,
//     //             height: height / 1.4,
//     //             child: Row(
//     //               crossAxisAlignment: CrossAxisAlignment.center,
//     //               mainAxisAlignment: MainAxisAlignment.center,
//     //               children: [
//     //                 InkWell(
//     //                   onTap: () async {
//     //                     if (!isLock) {
//     //                       isLock = true;
//     //                       if (saleLayout == "SaleLayout2") {
//     //                         Navigator.push(
//     //                             context,
//     //                             MaterialPageRoute(
//     //                                 builder: (context) => ItemsPage()));
//     //                       } else {
//     //                         Navigator.push(
//     //                             context,
//     //                             MaterialPageRoute(
//     //                                 builder: (context) => Cards()));
//     //                       }
//     //                       isLock = false;
//     //                     }
//     //                   },
//     //                   child: SizedBox(
//     //                     height: 180,
//     //                     child: Padding(
//     //                       padding: EdgeInsets.all(5.0),
//     //                       child: Container(
//     //                         alignment: Alignment.center,
//     //                         decoration: boxDecoration(
//     //                             radius: 10,
//     //                             showShadow: true,
//     //                             bgColor: secondaryTextColor),
//     //                         padding: EdgeInsets.symmetric(
//     //                             vertical: 0, horizontal: 30),
//     //                         child: Column(
//     //                           mainAxisAlignment: MainAxisAlignment.center,
//     //                           children: <Widget>[
//     //                             Container(
//     //                                 height: width / 5.0,
//     //                                 width: width / 5.0,
//     //                                 padding: EdgeInsets.all(width / 20),
//     //                                 decoration: boxDecoration(
//     //                                     bgColor: Colors.brown[400], radius: 10),
//     //                                 child: Icon(
//     //                                   Icons.assignment,
//     //                                   color: white,
//     //                                   size: 38,
//     //                                 )),
//     //                             text("Take Away",
//     //                                 textColor: appStore.textSecondaryColor,
//     //                                 fontSize: textSizeMedium)
//     //                           ],
//     //                         ),
//     //                       ),
//     //                     ),
//     //                   ),
//     //                 ),
//     //                 SizedBox(
//     //                   width: enableTableOrders ? 10 : 1,
//     //                 ),
//     //                 enableTableOrders
//     //                     ? InkWell(
//     //                         onTap: () async {
//     //                           if (!isLock) {
//     //                             isLock = true;
//     //                             Navigator.push(
//     //                                 context,
//     //                                 MaterialPageRoute(
//     //                                     builder: (context) => ActiveTables()));
//     //                             isLock = false;
//     //                           }
//     //                         },
//     //                         child: SizedBox(
//     //                           height: 180,
//     //                           child: Padding(
//     //                             padding: EdgeInsets.all(5.0),
//     //                             child: Container(
//     //                               alignment: Alignment.center,
//     //                               decoration: boxDecoration(
//     //                                   radius: 10,
//     //                                   showShadow: true,
//     //                                   bgColor: secondaryTextColor),
//     //                               padding: EdgeInsets.symmetric(
//     //                                   vertical: 0, horizontal: 30),
//     //                               child: Column(
//     //                                 mainAxisAlignment: MainAxisAlignment.center,
//     //                                 children: <Widget>[
//     //                                   Container(
//     //                                       height: width / 5.0,
//     //                                       width: width / 5.0,
//     //                                       padding: EdgeInsets.all(width / 20),
//     //                                       decoration: boxDecoration(
//     //                                           bgColor: Colors.teal, radius: 10),
//     //                                       child: Icon(
//     //                                         Icons.shopping_cart_outlined,
//     //                                         color: white,
//     //                                         size: 38,
//     //                                       )),
//     //                                   text("Table Order",
//     //                                       textColor:
//     //                                           appStore.textSecondaryColor,
//     //                                       fontSize: textSizeMedium)
//     //                                 ],
//     //                               ),
//     //                             ),
//     //                           ),
//     //                         ),
//     //                       )
//     //                     : Container(),
//     //               ],
//     //             ),
//     //           ),
//     //         ),
//     //       ],
//     //     ),
//     //   ),
//     //   bottomNavigationBar: BottomNavigation(),
//     // );
//   }

//   Widget GridListing(mFavouriteList, isScrollable) {
//     return mFavouriteList != null
//         ? Container(
//             alignment: Alignment.center,
//             child: GridView.builder(
//                 scrollDirection: Axis.vertical,
//                 physics: ScrollPhysics(),
//                 itemCount: mFavouriteList.length,
//                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                     crossAxisCount: width < 600 ? 2 : 4,
//                     crossAxisSpacing: 10,
//                     mainAxisSpacing: 10,
//                     childAspectRatio: mFavouriteList.length == 1 ? 1 : .9),
//                 itemBuilder: (BuildContext context, int index) {
//                   var item = mFavouriteList[index];
//                   return GestureDetector(
//                     onTap: () async {
//                       if (item.name == takeAwayName) {
//                         if (!isLock) {
//                           isLock = true;
//                           Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) =>
//                                       saleLayout == "SaleLayout2"
//                                           ? ItemsPageLayout2()
//                                           : saleLayout == "SaleLayout3"
//                                               ? ItemsPage()
//                                               : Cards()));
//                           isLock = false;
//                         }
//                       } else if (item.name == tableOrderName) {
//                         if (!isLock) {
//                           isLock = true;
//                           Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) => ActiveTables()));
//                           isLock = false;
//                         }
//                       } else if (item.name == eatPayName) {
//                         if (!isLock) {
//                           isLock = true;
//                           Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) => ActiveSalesOrder()));
//                           isLock = false;
//                         }
//                       }
//                     },
//                     child: Padding(
//                       padding: EdgeInsets.all(5.0),
//                       child: Container(
//                         alignment: Alignment.center,
//                         decoration: boxDecoration(
//                             radius: 10,
//                             showShadow: true,
//                             bgColor: secondaryTextColor),
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: <Widget>[
//                             Container(
//                               height:
//                                   width < 600 ? (width / 5.0) : (width / 7.0),
//                               width: width / 5.0,
//                               margin: EdgeInsets.only(bottom: 3, top: 6),
//                               padding: EdgeInsets.all(width / 20),
//                               decoration: boxDecoration(
//                                   bgColor: mFavouriteList[index].color,
//                                   radius: 10),
//                               child: SvgPicture.asset(
//                                 mFavouriteList[index].icon,
//                                 color: secondaryTextColor,
//                               ),
//                             ),
//                             text(mFavouriteList[index].name,
//                                 isLongText: true,
//                                 isCentered: true,
//                                 textColor: appStore.textSecondaryColor,
//                                 fontSize: textSizeMedium)
//                           ],
//                         ),
//                       ),
//                     ),
//                   );
//                 }),
//           )
//         : Container();
//   }
// }
