// import 'package:flutter/material.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// import 'package:smartbill/model/products.dart';
// import 'package:smartbill/model/stocks.dart';
// import 'package:smartbill/utils/common_widgets/widgets.dart';

// import '../../../main.dart';
// import '../../utils/constants/colors.dart';
// import '../../utils/constants/strings_keys.dart';
// import '../../utils/constants/styles.dart';
// import '../../utils/dbutils/product_details_db_script.dart';
// import '../../utils/dbutils/stocks_db_script.dart';
// import '../../utils/common_widgets/header_footer.dart';
// import '../expenses/functions/stocks_functions.dart';

// class ManageStocks extends StatefulWidget {
//   static var tag = "/ManageStocks";

//   @override
//   ManageStocksState createState() => ManageStocksState();
// }

// class ManageStocksState extends State<ManageStocks> {
//   RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
//   DBOperations dbOperations = new DBOperations();
//   FnUtilities fnUtilities = new FnUtilities();
//   final dbHelper = DatabaseHelper.instance;
//   StocksDBScript stocksDBScript = StocksDBScript();
//   StocksFunction stocksFunction = StocksFunction();
//   ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
//   bool isLock = false;
//   var width;
//   var height;
//   List<Stocks> stockProductsList = [];
//   List<Stocks> tempStockProductsList = [];
//   List<Stocks> storeStockProductsList = [];
//   String inventoryType;
//   // ignore: deprecated_member_use
//   List<TextEditingController> stockController = new List();
//   final searchController = new TextEditingController();
//   bool isQtyDesc = false;
//   bool isProductIDDesc = false;
//   workspaceSettings() async {
//     if (await fnUtilities.workSpaceSetValues(key_inventoryType) == "All") {
//       inventoryType = "All";
//     } else {
//       inventoryType = "Specific";
//     }
//   }

//   //GET ALL PRODUCT DETAILS
//   void _queryAllProductDetails() async {
//     await workspaceSettings();
//     final allRows = await productDetailsDBScript.queryallRowsProductDeatails();
//     setState(() {
//       ListUtility.productList.clear();
//       allRows.forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
//     });
//     await getAllStackDetails();
//   }

//   //GET ALL PRODUCT DETAILS
//   getAllStackDetails() async {
//     final allRows = await stocksDBScript.getAllStocks();
//     setState(() {
//       tempStockProductsList.clear();
//       allRows.forEach((row) => tempStockProductsList.add(Stocks.fromMap(row)));
//     });
//     if (inventoryType == "All" || inventoryType == "Specific") {
//       await allProductStock();
//     }
//   }

//   allProductStock() async {
//     stockProductsList.clear();
//     for (Products p in ListUtility.productList) {
//       String id;
//       try {
//         id = tempStockProductsList.firstWhere((element) => element.productId == p.productId).productId;
//       } catch (ex) {}
//       if (id == null) {
//         Stocks model = Stocks();
//         model.productId = p.productId;
//         model.stocksSold = "0";
//         model.workspaceID = MyApp.activeWorkspace.workspaceId;
//         model.updatedQty = "0";
//         model.currentQty = "0";
//         model.stocksQty = "0";
//         model.productName = p.name;
//         if (inventoryType == "Specific") {
//           if (p.enableInventory == 1) {
//             stockProductsList.add(model);
//           }
//         } else {
//           stockProductsList.add(model);
//         }
//       } else {
//         Stocks model = Stocks();
//         model.stocksID = tempStockProductsList.firstWhere((element) => element.productId == p.productId).stocksID;
//         model.productId = p.productId;
//         model.stocksSold = tempStockProductsList.firstWhere((element) => element.productId == p.productId).stocksSold;
//         model.workspaceID = MyApp.activeWorkspace.workspaceId;
//         model.updatedQty = "0";
//         model.currentQty = "0";
//         model.stocksQty = tempStockProductsList.firstWhere((element) => element.productId == p.productId).stocksQty;
//         model.productName = p.name;
//         if (inventoryType == "Specific") {
//           if (p.enableInventory == 1) {
//             stockProductsList.add(model);
//           }
//         } else {
//           stockProductsList.add(model);
//         }
//       }
//     }
//     await assignValue();
//   }

//   assignValue() {
//     setState(() {
//       for (Stocks s in stockProductsList) {
//         try {
//           s.currentQty = (int.parse(s.stocksQty) - int.parse(s.stocksSold)).toString();
//           s.productSaleID = ListUtility.productList.firstWhere((element) => element.productId == s.productId, orElse: () => null).productSaleId;
//         } catch (ex) {
//           s.currentQty = "0";
//         }
//       }
//       storeStockProductsList.clear();
//       storeStockProductsList.addAll(stockProductsList);
//       try {
//         stockProductsList.sort((a, b) => int.parse(a.currentQty).compareTo(int.parse(b.currentQty)));
//       } catch (ex) {}
//     });
//   }

//   void filterProductsByName(String name) async {
//     if (name != "") {
//       for (Stocks p in storeStockProductsList) {
//         String updateQty = stockProductsList.firstWhere((element) => element.productId == p.productId, orElse: () => null)?.updatedQty;
//         if (updateQty != null) {
//           p.updatedQty = updateQty;
//         }
//       }
//       stockProductsList.clear();
//       for (Stocks p in storeStockProductsList) {
//         if (p.productName.toLowerCase().contains(name.toLowerCase())) {
//           setState(() {
//             stockProductsList.add(p);
//           });
//         }
//       }
//     } else {
//       stockProductsList.clear();
//       stockProductsList.addAll(storeStockProductsList);
//     }
//   }

//   sortByQty() {
//     try {
//       setState(() {
//         if (isQtyDesc) {
//           isQtyDesc = false;
//           stockProductsList.sort((a, b) => int.parse(a.currentQty).compareTo(int.parse(b.currentQty)));
//         } else {
//           isQtyDesc = true;
//           stockProductsList.sort((a, b) => int.parse(b.currentQty).compareTo(int.parse(a.currentQty)));
//         }
//       });
//     } catch (ex) {}
//   }

//   sortByProductID() {
//     try {
//       setState(() {
//         if (isProductIDDesc) {
//           isProductIDDesc = false;
//           stockProductsList.sort((a, b) => a.productSaleID.compareTo(b.productSaleID));
//         } else {
//           isProductIDDesc = true;
//           stockProductsList.sort((a, b) => b.productSaleID.compareTo(a.productSaleID));
//         }
//       });
//     } catch (ex) {}
//   }

//   @override
//   void initState() {
//     _queryAllProductDetails();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     for (TextEditingController c in stockController) {
//       c.dispose();
//     }
//     super.dispose();
//   }

//   modifyStockQty() async {
//     if (stockProductsList.isNotEmpty) {
//       for (Stocks item in stockProductsList) {
//         if (int.parse(item.updatedQty) != 0) {
//           ModifyStocksDto model = ModifyStocksDto();
//           model.productId = item.productId;
//           model.stocksSold = item.stocksSold;
//           model.stocksQty = (int.parse(item.updatedQty) + int.parse(item.stocksSold)).toString();
//           model.modifiedStocks = item.modifiedStocks;
//           model.stocksID = item.stocksID;
//           await stocksFunction.modifyStocks(model);
//         }
//       }
//       showToast("Stocks are updated successfully");
//       // ignore: await_only_futures
//       searchController.clear();
//       await _queryAllProductDetails();
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;

//     return Scaffold(
//       backgroundColor: secondaryTextColor,
//       resizeToAvoidBottomInset: true,
//       appBar: BaseAppBar(
//         title: text("Stocks", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
//         appBar: AppBar(),
//         widgets: <Widget>[
//           IconButton(
//               onPressed: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   await modifyStockQty();
//                   isLock = false;
//                 }
//               },
//               icon: Icon(Icons.save))
//         ],
//       ),
//       body: SingleChildScrollView(
//         child: Column(
//           children: [
//             SizedBox(
//               height: 10,
//             ),
//             Row(
//               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//               children: [
//                 Container(
//                   padding: const EdgeInsets.only(top: 7, left: 5),
//                   width: width / 1.5,
//                   height: 50,
//                   child: TextField(
//                     controller: searchController,
//                     onChanged: filterProductsByName,
//                     style: TextStyle(color: Colors.black),
//                     autofocus: false,
//                     decoration: InputDecoration(
//                       labelText: 'Search by product name',
//                       border: InputBorder.none,
//                       enabledBorder: OutlineInputBorder(
//                         borderRadius: BorderRadius.circular(4),
//                         borderSide: const BorderSide(color: grey, width: 0.0),
//                       ),
//                       focusedBorder: OutlineInputBorder(
//                         borderRadius: BorderRadius.circular(4),
//                         borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
//                       ),
//                       suffixIcon: InkWell(
//                         onTap: () {
//                           setState(() {
//                             searchController.clear();
//                             filterProductsByName("");
//                           });
//                         },
//                         child: new Icon(
//                           Icons.clear,
//                           color: primaryTextColor,
//                         ),
//                       ),
//                     ),
//                   ),
//                 ),
//                 Column(
//                   children: [
//                     Text("Qty"),
//                     IconButton(onPressed: () => sortByQty(), icon: Icon(!isQtyDesc ? Icons.arrow_upward : Icons.arrow_downward)),
//                   ],
//                 ),
//                 Column(
//                   children: [
//                     Text("ID"),
//                     IconButton(onPressed: () => sortByProductID(), icon: Icon(!isProductIDDesc ? Icons.arrow_upward : Icons.arrow_downward)),
//                   ],
//                 ),
//               ],
//             ),
//             SizedBox(
//               height: 10,
//             ),
//             ListView.builder(
//                 padding: EdgeInsets.only(right: 3),
//                 scrollDirection: Axis.vertical,
//                 itemCount: stockProductsList.length,
//                 shrinkWrap: true,
//                 physics: ScrollPhysics(),
//                 itemBuilder: (context, index) {
//                   var item = stockProductsList[index];
//                   stockController.add(new TextEditingController());
//                   return Card(
//                     margin: EdgeInsets.symmetric(vertical: 0, horizontal: 1),
//                     color: index % 2 == 0 ? secondaryTextColor : Colors.blue[50],
//                     child: Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       children: [
//                         Container(
//                           width: width / 2,
//                           child: Column(
//                             children: [
//                               Row(
//                                 children: [
//                                   Container(
//                                     padding: const EdgeInsets.only(bottom: 2, left: 2),
//                                     width: width / 2,
//                                     child: Text("${item.productSaleID != null ? item.productSaleID : ""}  " + item.productName),
//                                   )
//                                 ],
//                               ),
//                               Row(
//                                 children: [
//                                   Container(
//                                     alignment: Alignment.center,
//                                     width: width / 4,
//                                     child: Text(
//                                       "Current Qty",
//                                       style: TextStyle(fontWeight: FontWeight.bold),
//                                     ),
//                                   ),
//                                   Container(
//                                     alignment: Alignment.center,
//                                     width: width / 4,
//                                     child: Text("Updated Qty", style: TextStyle(fontWeight: FontWeight.bold)),
//                                   )
//                                 ],
//                               ),
//                               Row(
//                                 children: [
//                                   Container(
//                                     width: width / 4,
//                                     alignment: Alignment.center,
//                                     child: Text(item.currentQty),
//                                   ),
//                                   Container(
//                                     alignment: Alignment.center,
//                                     width: width / 4,
//                                     child: Text(item.updatedQty),
//                                   )
//                                 ],
//                               )
//                             ],
//                           ),
//                         ),
//                         Container(
//                           padding: EdgeInsets.only(top: 5),
//                           width: width / 2.2,
//                           child: Column(
//                             children: [
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   Container(
//                                       width: 108,
//                                       height: 50,
//                                       child: EditText(
//                                         mController: stockController[index],
//                                         isPassword: false,
//                                         maxLength: 7,
//                                         inputType: TextInputType.number,
//                                       )),
//                                 ],
//                               ),
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                                 children: [
//                                   Container(
//                                     child: InkWell(
//                                         onTap: () async {
//                                           if (!isLock) {
//                                             isLock = true;
//                                             if (stockController[index].text != "") {
//                                               setState(() {
//                                                 if (item.updatedQty == "0") {
//                                                   stockProductsList[index].updatedQty = (int.parse(item.currentQty) + int.parse(stockController[index].text)).toString();
//                                                 } else {
//                                                   stockProductsList[index].updatedQty = (int.parse(item.updatedQty) + int.parse(stockController[index].text)).toString();
//                                                 }
//                                                 stockProductsList[index].modifiedStocks = stockController[index].text;
//                                                 stockController[index].clear();
//                                               });
//                                             }
//                                             isLock = false;
//                                           }
//                                         },
//                                         child: Padding(
//                                           padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
//                                           child: Icon(
//                                             Icons.add,
//                                             color: Colors.green,
//                                           ),
//                                         )),
//                                   ),
//                                   SizedBox(
//                                     width: 10,
//                                   ),
//                                   Container(
//                                     child: InkWell(
//                                         onTap: () async {
//                                           if (!isLock) {
//                                             isLock = true;
//                                             if (stockController[index].text != "") {
//                                               setState(() {
//                                                 if (item.updatedQty == "0") {
//                                                   stockProductsList[index].updatedQty = (int.parse(item.currentQty) - int.parse(stockController[index].text)).toString();
//                                                 } else {
//                                                   stockProductsList[index].updatedQty = (int.parse(item.updatedQty) - int.parse(stockController[index].text)).toString();
//                                                 }
//                                                 stockProductsList[index].modifiedStocks = stockController[index].text;
//                                                 stockController[index].clear();
//                                               });
//                                             }
//                                             isLock = false;
//                                           }
//                                         },
//                                         child: Padding(
//                                           padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
//                                           child: Icon(
//                                             Icons.remove,
//                                             color: Colors.red,
//                                           ),
//                                         )),
//                                   ),
//                                 ],
//                               ),
//                             ],
//                           ),
//                         ),
//                       ],
//                     ),
//                   );
//                 }),
//           ],
//         ),
//       ),
//     );
//   }
// }
