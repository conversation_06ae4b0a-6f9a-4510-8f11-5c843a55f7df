// import 'package:flutter/material.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/common_function/data_generator.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// 

// import '../../main.dart';

// class PlansListView extends StatefulWidget {
//   @override
//   _PlansListViewState createState() => _PlansListViewState();
// }

// class _PlansListViewState extends State<PlansListView> {
//   @override
//   void initState() {
//     setState(() {
//       ListUtility.viewPlans.clear();
//       ListUtility.viewPlans = getPlans();
//     });
//     super.initState();
//   }

//   @override
//   Widget build(BuildContext context) {
//     var width = MediaQuery.of(context).size.width;
//     var height = MediaQuery.of(context).size.height;
//     
//     return Container(
//       alignment: Alignment.topCenter,
//       height: height / 1.2,
//       child: Column(
//         mainAxisAlignment: MainAxisAlignment.start,
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: <Widget>[
//           Row(
//             mainAxisAlignment: MainAxisAlignment.spaceBetween,
//             children: [
//               Container(
//                 child: text('Amount',
//                     textColor: textSecondaryColor,
//                     fontSize: textSizeMedium,
//                     fontFamily: fontSemibold),
//               ),
//               Container(
//                 child: text('Validity',
//                     textColor: textSecondaryColor,
//                     fontSize: textSizeMedium,
//                     fontFamily: fontSemibold),
//               ),
//             ],
//           ),
//           ListUtility.viewPlans.length != 0
//               ? Expanded(
//                   child: Align(
//                     alignment: Alignment.topCenter,
//                     child: ListView.builder(
//                         scrollDirection: Axis.vertical,
//                         padding: EdgeInsets.only(right: 0),
//                         itemCount: ListUtility.viewPlans.isNotEmpty
//                             ? ListUtility.viewPlans.length
//                             : null,
//                         shrinkWrap: true,
//                         itemBuilder: (context, index) {
//                           var item = ListUtility.viewPlans[index];
//                           return InkWell(
//                             onTap: () {
//                               Navigator.pop(context);
//                             },
//                             child: SingleChildScrollView(
//                               physics: ScrollPhysics(),
//                               child: Column(
//                                 children: <Widget>[
//                                   Container(
//                                     margin: EdgeInsets.all(5),
//                                     height: 60,
//                                     child: Row(
//                                       mainAxisAlignment:
//                                           MainAxisAlignment.spaceBetween,
//                                       children: <Widget>[
//                                         Container(
//                                           //width: width / 3.5,
//                                           alignment: Alignment.centerRight,
//                                           child: text('₹ ${item.amount}',
//                                               textColor: Colors.red,
//                                               fontSize: textSizeLargeMedium,
//                                               fontFamily: fontSemibold),
//                                         ),
//                                         Column(
//                                           children: [
//                                             Container(
//                                               //color: Colors.amberAccent,
//                                               width: width / 2,
//                                               alignment: Alignment.centerRight,
//                                               child: text(
//                                                   '${item.validityDays} days',
//                                                   fontSize: textSizeSmall),
//                                             ),
//                                           ],
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                   Divider(height: 0.5, color: t5ViewColor)
//                                 ],
//                               ),
//                             ),
//                           );
//                         }),
//                   ),
//                 )
//               : Container(
//                   height: 100,
//                   child: Center(
//                     child: Text("No Plans!"),
//                   ),
//                 ),
//         ],
//       ),
//     );
//   }
// }
