// import 'dart:async';
// import 'dart:math';
// import 'package:internet_connection_checker/internet_connection_checker.dart';
// import 'package:device_info/device_info.dart';
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter/rendering.dart';
// import 'package:flutter/widgets.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:intl/intl.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:permission_handler/permission_handler.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:smartbill/model/t5_models.dart';
// import 'package:smartbill/screens/settings/subscription_page.dart';
// import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/common_function/printer_utilities.dart';
// import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
// import 'package:smartbill/utils/common_function/sync_utility.dart';
// import 'package:smartbill/model/workspace.dart';
// import 'package:smartbill/screens/masters_option.dart';
// import 'package:smartbill/screens/workspace_page.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// import 'package:smartbill/utils/common_function/data_generator.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// 
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import 'package:smartbill/utils/constants/colors.dart';
// import 'package:wakelock/wakelock.dart';
// import '../../main.dart';
// import 'add_shop_tables.dart';
// import 'header_footer.dart';
// import 'itempage_layout2.dart';
// import 'itemsPage.dart';
// import 'navigators/home_settings.dart';
// import 'order_options.dart';
// import 'reports_main_page.dart';
// import 'sales_main_page.dart';
// import 'users/sign_in.dart';

// class Dashboard extends StatefulWidget {
//   static var tag = "/Dashboard";
//   @override
//   T5DashboardState createState() => T5DashboardState();
// }

// class T5DashboardState extends State<Dashboard> {
//   SyncUtility syncUtility = new SyncUtility();
//   DBOperations dbOperations = new DBOperations();
//   final fnUtilities = new FnUtilities();
//   final printerUtilities = new PrinterUtilities();
//   final dbHelper = DatabaseHelper.instance;
//   List<T5Category> mFavouriteList;
//   List<T5Slider> mSliderList;
//   bool passwordVisible = false;
//   bool isLock = false;
//   RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
//   double width = 0.0;
//   double height = 0.0;
//   bool isTableOrder = false;
//   bool isEatAndPay = false;
//   String saleLayout = "SaleLayout1";

//   //GET DEVICE ID
//   Future<String> _getId() async {
//     DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
//     if (Theme.of(context).platform == TargetPlatform.iOS) {
//       IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
//       return iosDeviceInfo.identifierForVendor; // unique ID on iOS
//     } else {
//       AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
//       return androidDeviceInfo.androidId; // unique ID on Android
//     }
//   }

//   //GET TOKEN
//   genarateAppTempToken() async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     if (prefs.getString("appTempToken") == null) {
//       var rng = new Random();
//       MyApp.appTempToken = (rng.nextInt(900000) + 100000).toString();
//       prefs.setString("appTempToken", MyApp.appTempToken);
//     } else {
//       MyApp.appTempToken = prefs.getString("appTempToken");
//     }
//   }

//   requestPermission() async {
//     var status = await Permission.storage.status;
//     if (!status.isGranted) {
//       await Permission.storage.request();
//     }
//   }

//   String liveTime;

//   void getTime() {
//     if (this.mounted) {
//       final String formattedDateTime =
//           DateFormat('KK:mm a').format(DateTime.now()).toString();
//       setState(() {
//         liveTime = formattedDateTime;
//       });
//     }
//   }

//   //'dd-MM-yyyy KK:mm:ss a'
//   @override
//   void initState() {
//     getTime();
//     Timer.periodic(Duration(seconds: 10), (Timer t) => getTime());
//     setState(() {
//       printerUtilities.getBlutoothDevice();
//       genarateAppTempToken();
//     });
//     super.initState();
//     passwordVisible = false;
//     mFavouriteList = getCategoryItems();
//     mSliderList = getSliders();
//     Future.delayed(Duration.zero, () {
//       _getId().then((id) {
//         setState(() {
//           MyApp.deviceId = id;
//         });
//       });
//     });
//     initSettings();
//   }

//   var EnableTableOrdersValue; // This field only for check settings exists for re initiate settings on click order page.
//   initSettings() async {
//     EnableTableOrdersValue =
//         await fnUtilities.getWorkspaceSetValue("EnableTableOrders");
//     var SaleLayout = await fnUtilities.getWorkspaceSetValue("SaleLayout",
//         table: "WorkSpaceUserSettings");
//     var EatAndPay = await fnUtilities.getWorkspaceSetValue("EatAndPay");
//     var AlwaysOnDisplay = await fnUtilities.getWorkspaceSetValue(
//         "AlwaysOnDisplay",
//         table: "WorkSpaceUserSettings");
//     setState(() {
//       if (EnableTableOrdersValue == "1") {
//         isTableOrder = true;
//       } else {
//         isTableOrder = false;
//       }
//       if (EatAndPay == "1") {
//         isEatAndPay = true;
//       } else {
//         isEatAndPay = false;
//       }
//       if (SaleLayout != null) {
//         saleLayout = SaleLayout;
//       } else {
//         saleLayout = "SaleLayout1";
//       }
//       if (AlwaysOnDisplay == "1") {
//         Wakelock.toggle(enable: true);
//       } else {
//         Wakelock.toggle(enable: false);
//       }
//     });
//   }

//   //TEMP LOGOUT ALERT DIALOG
//   alertDialogLogout(BuildContext buildContext, WorkSpace item) {
//     var width = MediaQuery.of(context).size.width;
//     var height = MediaQuery.of(context).size.height;
//     return showDialog(
//         context: buildContext,
//         builder: (context) {
//           return Container(
//             height: height / 2,
//             child: AlertDialog(
//               content: Text(
//                 "Are you sure want to logout ?",
//                 style: TextStyle(
//                   color: primaryTextColor,
//                   fontSize: 15.0,
//                 ),
//               ),
//               actions: [
//                 GestureDetector(
//                   onTap: () async {
//                     ListUtility.signedWorkspaceList.removeWhere(
//                         (element) => element.workspaceId == item.workspaceId);
//                     MyApp.activeUser.token = null;
//                     Navigator.pop(context, 'true');
//                     Navigator.pushReplacement(context,
//                         MaterialPageRoute(builder: (context) => SignInPage()));
//                   },
//                   child: Container(
//                     alignment: Alignment.center,
//                     height: 40,
//                     width: width / 5,
//                     child: text("Yes", textColor: secondaryTextColor, isCentered: true),
//                     decoration:
//                         boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                   ),
//                 ),
//                 GestureDetector(
//                   onTap: () {
//                     Navigator.pop(context);
//                   },
//                   child: Container(
//                     alignment: Alignment.center,
//                     height: 40,
//                     width: width / 5,
//                     child: text("No", textColor: secondaryTextColor, isCentered: true),
//                     decoration:
//                         boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                   ),
//                 ),
//               ],
//             ),
//           );
//         });
//   }

//   int colorSync;
//   syncColor() async {
//     int sync = await dbHelper.checkSync(MyApp.activeWorkspace);
//     setState(() {
//       if (sync != 0) {
//         colorSync = 0;
//       } else {
//         colorSync = 1;
//       }
//     });
//   }

//   //SYNC WIDGET
//   Widget syncWidget() {
//     return InkWell(
//       // behavior: HitTestBehavior.translucent,
//       onTap: () async {
//         if (await InternetConnectionChecker().hasConnection) {
//           try {
//             if (colorSync != 2) {
//               setState(() {
//                 colorSync = 2;
//               });
//               await syncUtility.triggerManualSync();
//               syncColor();
//             }
//           } catch (e) {
//             setState(() {
//               colorSync = 0;
//             });
//             showToast("Sync failed");
//           }
//         } else {
//           fnUtilities.internetTurnOnAlertDialog(context);
//         }
//       },
//       child: Padding(
//         padding: EdgeInsets.symmetric(horizontal: 20, vertical: 5),
//         child: Container(
//             child: colorSync == 0
//                 ? Icon(
//                     Icons.sync,
//                     color: Colors.deepOrangeAccent,
//                     size: 30,
//                   )
//                 : colorSync == 2
//                     ? Icon(
//                         Icons.sync,
//                         color: Colors.yellowAccent,
//                         size: 30,
//                       )
//                     : Icon(
//                         Icons.sync,
//                         color: secondaryTextColor,
//                         size: 30,
//                       )),
//       ),
//     );
//   }

//   //GRID WIDGET
//   Widget GridListing(mFavouriteList, isScrollable) {
//     return Container(
//       child: GridView.builder(
//           scrollDirection: Axis.vertical,
//           physics: ScrollPhysics(),
//           itemCount: mFavouriteList.length,
//           gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//             crossAxisCount: width < 600 ? 2 : 4,
//             crossAxisSpacing: 10,
//             mainAxisSpacing: 10,
//             childAspectRatio: 1.0,
//           ),
//           itemBuilder: (BuildContext context, int index) {
//             return GestureDetector(
//               onTap: () async {
//                 if (index == 0) {
//                   if (!isLock) {
//                     isLock = true;
//                     if (EnableTableOrdersValue == null) {
//                       await initSettings();
//                     }
//                     if (MyApp.activeWorkspace.workspaceId != null) {
//                       if (await getValidityDays() > 0) {
//                         Navigator.push(
//                             context,
//                             MaterialPageRoute(
//                                 builder: (context) =>
//                                     isTableOrder || isEatAndPay
//                                         ? OrderOptions()
//                                         : saleLayout == "SaleLayout2"
//                                             ? ItemsPageLayout2()
//                                             : saleLayout == "SaleLayout3"
//                                                 ? ItemsPage()
//                                                 : Cards())).then((value) async {
//                           await syncColor();
//                         });
//                       } else if (await getValidityDays() >= -5) {
//                         validityAlertDialog(context,
//                             "Your plan expired!, you need to activate the subscription for sales",
//                             isGraceTime: true);
//                       } else {
//                         validityAlertDialog(context,
//                             "You should activate the subscription for sales",
//                             isGraceTime: false);
//                       }
//                     } else {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => WorkspacePage()));
//                       showToast("You must sign in shops");
//                     }
//                     isLock = false;
//                   }
//                 }
//                 if (index == 1) {
//                   if (!isLock) {
//                     isLock = true;
//                     if (MyApp.activeWorkspace.workspaceId != null) {
//                       if (await rolePermissions.isViewPermissions(
//                               dbOperations.returnPermission("Sales")) ==
//                           true) {
//                         Navigator.push(
//                                 context,
//                                 MaterialPageRoute(
//                                     builder: (context) => MastersOption()))
//                             .then((value) async {
//                           await syncColor();
//                         });
//                       } else {
//                         showToast("You don't have a permission");
//                       }
//                     } else {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => WorkspacePage()));
//                       showToast("You must sign in shops");
//                     }
//                     isLock = false;
//                   }
//                 }
//                 if (index == 2) {
//                   if (!isLock) {
//                     isLock = true;
//                     if (MyApp.activeWorkspace.workspaceId != null) {
//                       // if (await rolePermissions.isViewPermissions(
//                       //         dbOperations.returnPermission("Reports")) ==
//                       //     true) {
//                       Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) => ReportsMainPage()))
//                           .then((value) async {
//                         await syncColor();
//                       });
//                       // } else {
//                       //   showToast("You don't have a permission");
//                       // }
//                     } else {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => WorkspacePage()));
//                       showToast("You must sign in shops");
//                     }
//                     isLock = false;
//                   }
//                 }
//                 if (index == 3) {
//                   if (!isLock) {
//                     isLock = true;
//                     if (MyApp.activeWorkspace.workspaceId != null) {
//                       Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) => HomeSettings()))
//                           .then((value) async {
//                         await initSettings();
//                         await syncColor();
//                       });
//                     } else {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => WorkspacePage()));
//                       showToast("You must sign in shops");
//                     }
//                     isLock = false;
//                   }
//                 }
//                 if (index == 4) {
//                   if (!isLock) {
//                     isLock = true;
//                     if (MyApp.activeWorkspace.workspaceId != null) {
//                       Navigator.push(
//                               context,
//                               MaterialPageRoute(
//                                   builder: (context) => AddShopTables()))
//                           .then((value) async {
//                         await syncColor();
//                       });
//                     } else {
//                       Navigator.push(
//                           context,
//                           MaterialPageRoute(
//                               builder: (context) => WorkspacePage()));
//                       showToast("You must sign in shops");
//                     }
//                     isLock = false;
//                   }
//                 }
//                 // else {
//                 //   fnUtilities.internetTurnOnAlertDialog(context);
//                 // }
//               },
//               child: Padding(
//                 padding: EdgeInsets.all(5.0),
//                 child: Container(
//                   alignment: Alignment.center,
//                   decoration: boxDecoration(
//                       radius: 10,
//                       showShadow: true,
//                       bgColor: secondaryTextColor),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     children: <Widget>[
//                       Container(
//                         height: width < 600 ? (width / 5.0) : (width / 7.0),
//                         width: width / 5.0,
//                         margin: EdgeInsets.only(bottom: 3, top: 6),
//                         padding: EdgeInsets.all(width / 20),
//                         decoration: boxDecoration(
//                             bgColor: mFavouriteList[index].color, radius: 10),
//                         child: SvgPicture.asset(
//                           mFavouriteList[index].icon,
//                           color: secondaryTextColor,
//                         ),
//                       ),
//                       text(mFavouriteList[index].name,
//                           textColor: appStore.textSecondaryColor,
//                           fontSize: textSizeMedium)
//                     ],
//                   ),
//                 ),
//               ),
//             );
//           }),
//     );
//   }

// //VALIDITY ALERT ALERT DIALOG
//   validityAlertDialog(BuildContext context, String msg,
//       {bool isGraceTime = false}) {
//     showDialog(
//       context: context,
//       barrierDismissible: false,
//       builder: (BuildContext context) {
//         return Container(
//           height: 200,
//           child: AlertDialog(
//             content: Container(
//               child: Text(
//                 msg,
//                 style:
//                     TextStyle(color: primaryTextColor, fontSize: 18.0),
//               ),
//             ),
//             actions: [
//               GestureDetector(
//                 onTap: () {
//                   Navigator.pop(context);
//                   if (isGraceTime) {
//                     Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                             builder: (context) => isTableOrder || isEatAndPay
//                                 ? OrderOptions()
//                                 : saleLayout == "SaleLayout2"
//                                     ? ItemsPageLayout2()
//                                     : saleLayout == "SaleLayout3"
//                                         ? ItemsPage()
//                                         : Cards())).then((value) async {
//                       await syncColor();
//                     });
//                   } else {
//                     Navigator.push(
//                         context,
//                         MaterialPageRoute(
//                             builder: (context) => Subscriptions()));
//                   }
//                 },
//                 child: Container(
//                   alignment: Alignment.center,
//                   height: 40,
//                   width: 40,
//                   child: text("OK",
//                       fontSize: textSizeMedium,
//                       textColor: buttonThemeColor,
//                       isCentered: true),
//                   decoration: boxDecoration(radius: 8.0),
//                 ),
//               ),
//             ],
//           ),
//         );
//       },
//     );
//   }

//   Future<int> getValidityDays() async {
//     try {
//       DateTime now = DateTime.now();
//       int validityDays = DateTime.parse(MyApp.activeWorkspace.expiryDate)
//           .difference(now)
//           .inDays;
//       return validityDays;
//     } catch (e) {
//       return 0;
//     }
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;
//     changeStatusColor(appThemeColor);
//     //var width = MediaQuery.of(context).size.width;
//     width = width - 50;
//     return Scaffold(
//       //backgroundColor: t5DarkNavy,
//       appBar: BaseAppBar(
//         title: text(
//             MyApp.activeWorkspace.workspaceName != null
//                 ? MyApp.activeWorkspace.workspaceName
//                 : appName,
//             textColor: secondaryTextColor,
//             fontSize: textSizeNormal,
//             fontFamily: fontMedium),
//         appBar: AppBar(),
//         widgets: MyApp.activeWorkspace.workspaceId != null
//             ? <Widget>[
//                 syncWidget(),
//               ]
//             : null,
//       ),
//       drawer: BaseDrawer(),
//       body: SafeArea(
//         child: SingleChildScrollView(
//           child: Column(
//             children: <Widget>[
//               SingleChildScrollView(
//                 child: Container(
//                   padding: EdgeInsets.only(top: 5),
//                   alignment: Alignment.topLeft,
//                   height: MediaQuery.of(context).size.height - 150,
//                   decoration: BoxDecoration(
//                     color: secondaryTextColor,
//                   ),
//                   child: Column(
//                     children: <Widget>[
//                       liveTime != null
//                           ? Container(
//                               width: width / 1,
//                               alignment: Alignment.centerRight,
//                               child: text("$liveTime",
//                                   textColor: appThemeColor,
//                                   fontSize: textSizeNormal,
//                                   fontFamily: fontMedium),
//                             )
//                           : Container(),
//                       SizedBox(height: 5),
//                       Expanded(
//                         child: Padding(
//                           padding: EdgeInsets.all(24.0),
//                           child: GridListing(mFavouriteList, false),
//                         ),
//                       ),
//                     ],
//                   ),
//                 ),
//               ),
//             ],
//           ),
//         ),
//       ),
//       bottomNavigationBar: BottomNavigation(),
//     );
//   }
// }
