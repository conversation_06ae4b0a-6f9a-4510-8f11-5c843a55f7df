// // ignore_for_file: non_constant_identifier_names, must_be_immutable, sdk_version_ui_as_code

// import 'package:nb_utils/nb_utils.dart';
// import 'package:flutter/material.dart' hide Image;
// import 'package:flutter/services.dart';
// import 'package:smartbill/model/sale_model.dart';
// import 'package:smartbill/screens/item_layouts/sales_main_page.dart';
// import 'package:smartbill/screens/products/add_product_page.dart';
// import 'package:smartbill/utils/common_function/validation_utility.dart';
// import 'package:smartbill/model/products.dart';
// import 'package:smartbill/model/reports.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/model/stocks.dart';
// import 'package:smartbill/model/toppings_model.dart';
// import 'package:smartbill/model/transaction_model.dart';
// import 'package:smartbill/model/workspace_settings.dart';
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// import 'package:smartbill/utils/common_widgets/widgets.dart';
// import 'package:smartbill/utils/constants/colors.dart';
// import '../../../main.dart';
// import '../../utils/common_widgets/text_widget.dart';
// import '../../utils/constants/strings_keys.dart';
// import '../../utils/constants/styles.dart';
// import '../../utils/dbutils/price_type_db_script.dart';
// import '../../utils/dbutils/product_details_db_script.dart';
// import '../../utils/dbutils/product_key_db_script.dart';
// import '../../utils/dbutils/stocks_db_script.dart';
// import '../../utils/dbutils/toppings_db_script.dart';
// import '../../utils/common_widgets/header_footer.dart';

// class ItemsPage extends StatefulWidget {
//   static var tag = "/ItemsPage";
//   Sales sales;
//   String saleType;
//   List<Transactions> saleTransactionList;
//   ItemsPage({this.sales, this.saleTransactionList, this.saleType});
//   @override
//   ItemsPageState createState() => ItemsPageState(sales, saleTransactionList, saleType);
// }

// class ItemsPageState extends State<ItemsPage> {
//   ItemsPageState(this.sales, this.saleTransactionList, this.saleType);
//   WorkspaceSettings settingsDetail = new WorkspaceSettings();
//   FnUtilities fnUtilities = new FnUtilities();
//   Validator validator = new Validator();
//   StocksDBScript stocksDBScript = StocksDBScript();
//   ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
//   ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
//   PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
//   ToppingsDBScript toppingsDBscript = ToppingsDBScript();
//   final qtyController = new TextEditingController();
//   final priceController = new TextEditingController();
//   final customerNameController = new TextEditingController();
//   final givenAmountController = new TextEditingController();
//   final holdSalesController = new TextEditingController();
//   final searchController = TextEditingController();
//   final singleProdQtyController = TextEditingController();

//   final dbHelper = DatabaseHelper.instance;
//   List<Products> filterProductList = [];
//   List<ProductKey> productKeyList = [];
//   List<PriceUnitType> productUnitList = [];
//   List<Topping> productToppingList = [];
//   List<Transactions> saleTransactionList = [];
//   bool isLock = false;
//   bool isCounterLock = false;
//   double width;
//   double height;
//   String panels;
//   String prodId = '';
//   String prodKey = '';
//   String prodName = '';
//   int productsRowCount = 0;
//   bool isDynamicQuantity = false;
//   double dynamicQuantity = 1;
//   bool useMobileLayout = false;
//   Sales sales;
//   bool showInventory = false;
//   String inventoryType;
//   bool allowNegativeSale = false;
//   double totalQty = 0;
//   String saleType;
//   bool successDialogOnSales = false;
//   bool isProductLoading = false;
//   bool isFavorite = false;

//   //GET PRODUCT DETAILS FORM DB
//   queryAllProductDetails() async {
//     try {
//       setState(() {
//         isProductLoading = true;
//       });
//       var allRows;
//       if (isFavorite) {
//         allRows = await productDetailsDBScript.queryallFavoriteProducts();
//       } else {
//         allRows = await productDetailsDBScript.queryallRowsProductDeatails();
//       }
//       setState(() {
//         ListUtility.productList.clear();
//         filterProductList.clear();
//         allRows.forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
//       });
//       getAllProductKey();
//       ListUtility.productList = await assignFilePath(ListUtility.productList);
//       filterProductList.addAll(ListUtility.productList);
//       filterProductList.add(Products());
//       setState(() {
//         isProductLoading = false;
//       });
//     } catch (e) {
//       setState(() {
//         isProductLoading = false;
//       });
//     }
//   }

//   //GET PRODUCT DETAILS BY Key
//   getAllProductKey() async {
//     if (ListUtility.productKeyList.isNotEmpty) {
//       setState(() {
//         productKeyList.clear();
//         productKeyList.addAll(ListUtility.productKeyList);
//       });
//     }
//   }

//   //Get product unit details from static unit list
//   void getAllProductUnitsDetails(String productId) async {
//     if (ListUtility.unitList.isNotEmpty) {
//       productUnitList.clear();
//       for (PriceUnitType unitType in ListUtility.unitList) {
//         if (unitType.productId == productId) {
//           setState(() {
//             productUnitList.add(unitType);
//           });
//         }
//       }
//     }
//   }

//   //GET PRODUCT Topping DETAILS FROM DB
//   queryAllProductToppingDetails(Products product) async {
//     if (ListUtility.toppingsList.isNotEmpty) {
//       productToppingList.clear();
//       setState(() {
//         for (Topping t in ListUtility.toppingsList) {
//           if (t.toppingGroupId == product.toppingGroupId) {
//             t.isActive = isToppingID(t.toppingId, product);
//             productToppingList.add(t);
//           }
//         }
//       });
//     }
//   }

//   isToppingID(String toppingID, Products product) {
//     String topping;
//     try {
//       topping = saleTransactionList.firstWhere((element) => element.toppingId == toppingID && element.prodId == product?.productId, orElse: () => null).toppingId;
//     } catch (ex) {}
//     if (topping != null) {
//       return true;
//     } else {
//       return false;
//     }
//   }

//   Future<List<Products>> assignFilePath(List<Products> productList) async {
//     for (Products p in productList) {
//       if (!p.image.isEmptyOrNull) {
//         p.imagePath = await fnUtilities.readFileFromLocal(p.image);
//       }
//     }
//     return productList;
//   }

//   initSettings() async {
//     setState(() {
//       //Workspace device settings
//       isFavorite = fnUtilities.workSpaceDeviceSetValue(key_favoriteProduct) == "1";
//       panels = fnUtilities.workSpaceDeviceSetValue(key_panelType);
//       MyApp.isFilterEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductTapOnClear) == "1";
//       MyApp.isListViewEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductListview) == "1";

//       ///WorkSpace Settings
//       isDynamicQuantity = fnUtilities.workSpaceSetValues(key_dynamicQuantity) == "1";
//       showInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1";
//       allowNegativeSale = fnUtilities.workSpaceSetValues(key_allowNegativeSales) == "1";
//       successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
//       inventoryType = fnUtilities.workSpaceSetValues(key_inventoryType);

//       if (inventoryType == "All") {
//         inventoryType = "All";
//       } else {
//         inventoryType = "Specific";
//       }
//     });
//   }

//   //CALCULATE TOTAL Qty
//   findTotal() {
//     if (this.mounted) {
//       setState(() {
//         totalQty = 0.0;
//         for (Transactions p in saleTransactionList) {
//           if (p.toppingId == null) {
//             totalQty += double.parse(p.qty);
//           }
//         }
//       });
//     }
//   }

//   //GET PRODUCT DETAILS BY ID FROM LIST
//   getProductDetailsById(String prodId) {
//     for (Products p in ListUtility.productList) {
//       if (p.productId == prodId) {
//         return p;
//       }
//     }
//   }

//   //SET PRODUCT ID DETAILS
//   setProdId(String id) async {
//     setState(() {
//       prodId += id;
//       filterProductList.clear();
//       filterProductsByID(prodId);
//     });
//     await loopForProductQty(saleTransactionList);
//   }

//   //SET PRODUCT KEY DETAILS
//   setProdKey(String key, String keyName) async {
//     setState(() {
//       filterProductList.clear();
//       filterProductsByKeyword(key, keyName);
//     });
//     await loopForProductQty(saleTransactionList);
//   }

//   //GET FILTER PRODUCT FROM LIST
//   filterProductsByID(String id) async {
//     for (Products p in ListUtility.productList) {
//       if (p.productSaleId != null && p.productSaleId.toString().contains(prodId)) {
//         setState(() {
//           filterProductList.add(p);
//         });
//       }
//     }
//     filterProductList.add(Products());
//     await loopForProductQty(saleTransactionList);
//   }

//   //GET FILTER PRODUCT FROM LIST
//   filterProductsByKeyword(String key, String keyName) async {
//     for (Products p in ListUtility.productList) {
//       if (p.keyID != null && p.keyID.toString().contains(key) || p.productKeyName == keyName) {
//         setState(() {
//           filterProductList.add(p);
//         });
//       }
//     }
//     filterProductList.add(Products());
//     await loopForProductQty(saleTransactionList);
//   }

//   //GET PRODUCT DETAILS BY NAME
//   void filterProductsByName(String name) async {
//     setState(() {
//       prodName = name;
//       filterProductList.clear();
//       for (Products p in ListUtility.productList) {
//         if (p.name != null && p.name.toLowerCase().contains(name.toLowerCase())) {
//           filterProductList.add(p);
//         }
//       }
//     });
//     filterProductList.add(Products());
//     await loopForProductQty(saleTransactionList);
//   }

//   //ADD PRODUCT IN SALES LIST
//   addProductsInSalesList1(Products item, {isRemove = false}) async {
//     double amount;
//     try {
//       for (Topping t in productToppingList) {
//         Transactions transaction = saleTransactionList.firstWhere(
//             (element) => element.toppingId == t.toppingId && double.parse(t.toppingPrice) == double.parse(element.actualPrice) && element.prodId == item.productId,
//             orElse: () => null);
//         if (transaction != null) {
//           if (!isRemove && t.isActive) {
//             amount = (1) * double.parse(t.toppingPrice);
//             transaction.productType = "Topping";
//             transaction.toppingId = t.toppingId;
//             transaction.name = t.toppingName;
//             transaction.price = t.toppingPrice;
//             transaction.actualPrice = t.toppingPrice;
//             transaction.qty = (1).toString();
//             transaction.amount = amount.toString();
//           } else {
//             saleTransactionList.removeWhere(
//                 (element) => element.toppingId == t.toppingId && double.parse(t.toppingPrice) == double.parse(element.actualPrice) && element.prodId == item.productId);
//           }
//         } else if (t.isActive) {
//           saleTransactionList.add(Transactions(
//             prodId: item.productId,
//             productType: "Topping",
//             toppingId: t.toppingId,
//             name: t.toppingName,
//             qty: "1",
//             price: t.toppingPrice,
//             actualPrice: t.toppingPrice,
//             amount: t.toppingPrice,
//             cGst: item.cGst,
//             sGst: item.sGst,
//           ));
//         }
//       }
//     } catch (ex) {
//       //print(ex);
//     }
//     await findTotal();
//     productToppingNewList.clear();
//   }

//   //ADD PRODUCT IN SALES LIST
//   addProductsInSalesList(Products item, PriceUnitType unit, {bool isReduce = false, bool isDynamic = false}) async {
//     int gridIndex = -1;
//     String price;
//     double amount;
//     if (item.priceType == null) {
//       if (item.price == null || item.price == "") {
//         price = priceController.text;
//       } else {
//         price = item.price;
//       }
//     } else {
//       if ((item.priceType == 0 || item.price == "" || item.price == null) && priceController.text != "") {
//         price = priceController.text;
//       } else {
//         price = item.price;
//       }
//     }
//     if (unit != null) {
//       if (unit.measurementPrice == "" || unit.measurementPrice == null) {
//         unit.measurementPrice = priceController.text;
//       }
//     }
//     if (item.priceType != 2) {
//       for (int i = 0; i < saleTransactionList.length; i++) {
//         var transaction = saleTransactionList[i];

//         if (item.productId == transaction.prodId && double.parse(price) == double.parse(transaction.actualPrice) && transaction.toppingId == null) {
//           gridIndex = i;
//           double currentQty = isReduce ? (double.parse(transaction.qty) - dynamicQuantity) : (double.parse(transaction.qty) + dynamicQuantity);
//           if (isDynamic) {
//             currentQty = dynamicQuantity == null ? 0 : dynamicQuantity;
//           }
//           if (currentQty > 0) {
//             amount = currentQty * double.parse(price);
//             saleTransactionList[i] = Transactions(
//                 id: transaction.id,
//                 transactId: transaction.transactId,
//                 salesId: transaction.salesId,
//                 prodId: item.productId,
//                 productType: "Product",
//                 name: item.name,
//                 price: price,
//                 actualPrice: price,
//                 cGst: item.cGst,
//                 sGst: item.sGst,
//                 qty: currentQty.toString(),
//                 amount: amount.toString(),
//                 isActiveSale: transaction.isActiveSale);
//           } else {
//             saleTransactionList.removeAt(i);
//           }
//         }
//       }
//     } else {
//       for (int i = 0; i < saleTransactionList.length; i++) {
//         var transaction = saleTransactionList[i];

//         if (unit.productId == transaction.prodId && double.parse(unit.measurementPrice) == double.parse(transaction.actualPrice)) {
//           gridIndex = i;
//           double currentQty = isReduce ? (double.parse(transaction.qty) - dynamicQuantity) : (double.parse(transaction.qty) + dynamicQuantity);
//           if (isDynamic) {
//             currentQty = dynamicQuantity == null ? 0 : dynamicQuantity;
//           }
//           if (currentQty > 0) {
//             amount = currentQty * double.parse(unit.measurementPrice);
//             String name = item.name + '-' + unit.measurement;
//             saleTransactionList[i] = Transactions(
//                 id: transaction.id,
//                 transactId: transaction.transactId,
//                 salesId: transaction.salesId,
//                 prodId: unit.productId,
//                 productType: "Product",
//                 name: name,
//                 price: unit.measurementPrice,
//                 actualPrice: unit.measurementPrice,
//                 cGst: item.cGst,
//                 sGst: item.sGst,
//                 qty: currentQty.toString(),
//                 amount: amount.toString(),
//                 isActiveSale: transaction.isActiveSale);
//           } else {
//             saleTransactionList.removeAt(i);
//           }
//         }
//       }
//     }

//     if (gridIndex == -1) {
//       if (item.priceType != 2) {
//         saleTransactionList.add(Transactions(
//           prodId: item.productId,
//           productType: "Product",
//           name: item.name,
//           qty: dynamicQuantity.toString(),
//           price: price,
//           actualPrice: price,
//           amount: price,
//           cGst: item.cGst,
//           sGst: item.sGst,
//         ));
//       } else {
//         String name = item.name + '-' + unit.measurement;
//         saleTransactionList.add(Transactions(
//           prodId: item.productId,
//           productType: "Product",
//           name: name,
//           qty: dynamicQuantity.toString(),
//           price: unit.measurementPrice,
//           actualPrice: unit.measurementPrice,
//           amount: unit.measurementPrice,
//           cGst: item.cGst,
//           sGst: item.sGst,
//         ));
//       }
//     }
//     await findTotal();
//     dynamicQuantity = 1;
//   }

//   getInitSalesPage() async {
//     await initSettings();
//     await assignTransactionList();
//     await queryAllProductDetails();
//   }

//   assignTransactionList() async {
//     if (saleTransactionList != null) {
//       await loopForProductQty(saleTransactionList);
//       await findTotal();
//     } else {
//       saleTransactionList = [];
//     }
//   }

//   getAllStockDetails(String productID) async {
//     List<Stocks> tempStockProductsList = [];
//     final allRows = await stocksDBScript.getAllStocks();
//     setState(() {
//       allRows.forEach((row) => tempStockProductsList.add(Stocks.fromMap(row)));
//     });
//     Stocks stockModel;
//     try {
//       stockModel = tempStockProductsList.firstWhere((element) => element.productId == productID);
//     } catch (ex) {}
//     int currentStock = 0;
//     if (stockModel != null) {
//       currentStock = int.parse(stockModel.stocksQty) - int.parse(stockModel.stocksSold);
//     }
//     return currentStock;
//   }

//   // commonOnTapProduct(Products product, int index) async {
//   //   int currentStock = await getAllStockDetails(product.productId);
//   //   if ((showInventory &&
//   //           inventoryType == "All" &&
//   //           !allowNegativeSale &&
//   //           currentStock <= 0) ||
//   //       (showInventory &&
//   //           inventoryType == "Specific" &&
//   //           !allowNegativeSale &&
//   //           currentStock <= 0 &&
//   //           product.enableInventory == 1)) {
//   //     showToast("Stock Empty");
//   //   } else {
//   //     if (product?.dynamicQuantity == 1) {
//   //       if (product?.priceType == 2) {
//   //         getAllProductUnitsDetails(product.productId);
//   //         await unitDialog(context, product, "dynamicQty");
//   //       } else {
//   //         await dynamicQtyDialog(
//   //             context, product?.toppingGroupId, product?.price, product, null);
//   //       }
//   //       await productCountIncrement(product?.productId);
//   //     } else {
//   //       if (product?.priceType == null) {
//   //         if (product?.price == null || product?.price == "") {
//   //           await AmountDialog(context, index);
//   //         } else {
//   //           await addProductsInSalesList(product, null);
//   //           if (product?.toppingGroupId != null) {
//   //             await queryAllProductToppingDetails(product?.toppingGroupId);
//   //             if (productToppingList.length > 0) {
//   //               await toppingDialog(context, product);
//   //             }
//   //           }
//   //         }
//   //       } else {
//   //         if (product?.priceType == 0) {
//   //           await AmountDialog(context, index);
//   //         } else if (product?.priceType == 1) {
//   //           await addProductsInSalesList(product, null);
//   //           if (product?.toppingGroupId != null) {
//   //             await queryAllProductToppingDetails(product?.toppingGroupId);
//   //             if (productToppingList.length > 0) {
//   //               await toppingDialog(context, product);
//   //             }
//   //           }
//   //         } else {
//   //           await getAllProductUnitsDetails(product?.productId);
//   //           await unitDialog(context, product, "normalQty");
//   //         }
//   //       }
//   //       await productCountIncrement(product?.productId);
//   //     }
//   //     if (MyApp.isFilterEnable) {
//   //       await clearFunction();
//   //     }
//   //   }
//   // }

//   commonIncrementOnTapProduct(Products product, int index, {PriceUnitType unitMeasurement, isDynamic = false}) async {
//     int currentStock = await getAllStockDetails(product.productId);
//     if ((showInventory && inventoryType == "All" && !allowNegativeSale && currentStock <= 0) ||
//         (showInventory && inventoryType == "Specific" && !allowNegativeSale && currentStock <= 0 && product.enableInventory == 1)) {
//       showToast("Stock Empty");
//     } else {
//       await addProductsInSalesList(product, unitMeasurement, isDynamic: isDynamic);
//       await productCountIncrement(product?.productId);
//       if (MyApp.isFilterEnable) {
//         await clearFunction();
//       }
//     }
//   }

//   clearFunction() {
//     if (prodKey != "") {
//       prodKey = "";
//       setProdKey("", '');
//     }
//     if (prodId != "") {
//       prodId = "";
//       setProdId("");
//     }
//     if (searchController.text != "") {
//       searchController.text = "";
//       filterProductsByName("");
//     }
//   }

//   @override
//   void initState() {
//     getInitSalesPage();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     //ScaffoldMessenger.of(context).hideCurrentSnackBar();
//     super.dispose();
//   }

//   List<Topping> productToppingNewList = [];

//   // //DYNAMIC Unit ALERT DIALOG
//   // unitDialog(BuildContext context, Products item1, String dFlag) {
//   //   bool isUnitLock = false;
//   //   return showDialog(
//   //       context: context,
//   //       builder: (context) {
//   //         return AlertDialog(
//   //             scrollable: true,
//   //             content: Column(
//   //               mainAxisSize: MainAxisSize.min,
//   //               children: [
//   //                 Container(
//   //                   width: double.maxFinite,
//   //                   child: ListView.builder(
//   //                       itemCount: productUnitList.length,
//   //                       shrinkWrap: true,
//   //                       itemBuilder: (BuildContext context, int index) {
//   //                         var item = productUnitList[index];
//   //                         return InkWell(
//   //                           onTap: () async {
//   //                             if (!isUnitLock) {
//   //                               isUnitLock = true;
//   //                               if ("dynamicQty" == dFlag) {
//   //                                 Navigator.pop(context);
//   //                                 await dynamicQtyDialog(
//   //                                     context,
//   //                                     item1.toppingGroupId,
//   //                                     item.measurementPrice,
//   //                                     item1,
//   //                                     item);
//   //                               } else {
//   //                                 await addProductsInSalesList(item1, item);
//   //                                 Navigator.pop(context);
//   //                                 if (item1.toppingGroupId != null) {
//   //                                   await queryAllProductToppingDetails(
//   //                                       item1.toppingGroupId);
//   //                                   if (productToppingList.length > 0) {
//   //                                     toppingDialog(context, item1);
//   //                                   }
//   //                                 }
//   //                               }
//   //                               isUnitLock = false;
//   //                             }
//   //                           },
//   //                           child: Container(
//   //                             //height: 40,
//   //                             //margin: const EdgeInsets.all(2.0),
//   //                             margin: const EdgeInsets.all(2.0),
//   //                             padding: EdgeInsets.only(
//   //                                 left: 5, right: 5, top: 10, bottom: 10),
//   //                             decoration: boxDecoration(
//   //                                 radius: 10,
//   //                                 showShadow: true,
//   //                                 bgColor: secondaryTextColor),
//   //                             child: Row(
//   //                               mainAxisAlignment:
//   //                                   MainAxisAlignment.spaceAround,
//   //                               crossAxisAlignment: CrossAxisAlignment.start,
//   //                               children: <Widget>[
//   //                                 Container(
//   //                                   width: width / 3,
//   //                                   alignment: Alignment.centerLeft,
//   //                                   child: TextWidget(item.measurement,
//   //                                       textColor: primaryTextColor,
//   //                                       fontSize: textSizeLargeMedium,
//   //                                       fontFamily: fontSemibold,
//   //                                       isLongText: true),
//   //                                 ),
//   //                                 SizedBox(height: 1),
//   //                                 Container(
//   //                                   width: width / 4,
//   //                                   alignment: Alignment.centerRight,
//   //                                   child: item.measurementPrice != "" &&
//   //                                           item.measurementPrice != null
//   //                                       ? TextWidget("Rs.${item.measurementPrice}",
//   //                                           textColor:
//   //                                               primaryTextColor,
//   //                                           fontSize: textSizeLargeMedium,
//   //                                           fontFamily: fontSemibold)
//   //                                       : TextWidget("",
//   //                                           textColor:
//   //                                               primaryTextColor,
//   //                                           fontSize: textSizeLargeMedium,
//   //                                           fontFamily: fontSemibold),
//   //                                 ),
//   //                               ],
//   //                             ),
//   //                           ),
//   //                         );
//   //                       }),
//   //                 ),
//   //               ],
//   //             ));
//   //       });
//   // }
//   //
//   //DYNAMIC Unit ALERT DIALOG
//   toppingDialog(BuildContext context, Products item1) {
//     bool isToppingLock = false;
//     return showDialog(
//         context: context,
//         builder: (context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return AlertDialog(
//                 contentPadding: EdgeInsets.all(10),
//                 scrollable: true,
//                 content: Column(
//                   mainAxisSize: MainAxisSize.min,
//                   children: [
//                     Container(
//                       width: double.maxFinite,
//                       child: ListView.builder(
//                           itemCount: productToppingList.length,
//                           padding: EdgeInsets.zero,
//                           shrinkWrap: true,
//                           itemBuilder: (BuildContext context, int index) {
//                             var item = productToppingList[index];
//                             return Container(
//                               margin: const EdgeInsets.all(0),
//                               padding: EdgeInsets.zero,
//                               decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
//                               child: Row(
//                                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                 children: <Widget>[
//                                   Container(
//                                     width: width / 1.5,
//                                     // alignment: Alignment.topLeft,
//                                     child: new CheckboxListTile(
//                                         dense: false,
//                                         controlAffinity: ListTileControlAffinity.leading,
//                                         contentPadding: EdgeInsets.zero,
//                                         value: item.isActive,
//                                         title: Row(
//                                           children: [
//                                             Container(
//                                               width: width / 3.8,
//                                               alignment: Alignment.centerLeft,
//                                               child:
//                                                   TextWidget(item.toppingName, textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isLongText: true),
//                                             ),
//                                             Container(
//                                               width: width / 5,
//                                               alignment: Alignment.centerRight,
//                                               child: item.toppingPrice != ""
//                                                   ? TextWidget("Rs.${item.toppingPrice}", textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold)
//                                                   : TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                                             ),
//                                           ],
//                                         ),
//                                         onChanged: (bool newValue) {
//                                           if (!isToppingLock) {
//                                             isToppingLock = true;
//                                             setState(() {
//                                               item.isActive = newValue;
//                                               // if (item.isActive == true) {
//                                               //   productToppingNewList.add(item);
//                                               // } else {
//                                               //   productToppingNewList
//                                               //       .removeWhere((element) =>
//                                               //           element.toppingId ==
//                                               //           item.toppingId);
//                                               // }
//                                             });
//                                             isToppingLock = false;
//                                           }
//                                         }),
//                                   ),
//                                 ],
//                               ),
//                             );
//                           }),
//                     ),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () {
//                             if (!isToppingLock) {
//                               isToppingLock = true;
//                               // productToppingNewList.clear();
//                               Navigator.pop(context);
//                               isToppingLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () {
//                             if (!isToppingLock) {
//                               isToppingLock = true;
//                               addProductsInSalesList1(item1);
//                               Navigator.pop(context);
//                               isToppingLock = false;
//                             }
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ));
//           });
//         });
//   }
//   //
//   // //DYNAMIC PRICE ALERT DIALOG
//   // AmountDialog(BuildContext context, int index) {
//   //   bool isAmountLock = false;
//   //   return showDialog(
//   //       context: context,
//   //       builder: (context) {
//   //         var item = filterProductList[index];
//   //         return Center(
//   //           child: SingleChildScrollView(
//   //             child: AlertDialog(
//   //                 content: Container(
//   //               width: double.maxFinite,
//   //               child: Form(
//   //                 key: validator.formkey,
//   //                 child: Column(
//   //                   mainAxisAlignment: MainAxisAlignment.start,
//   //                   crossAxisAlignment: CrossAxisAlignment.center,
//   //                   mainAxisSize: MainAxisSize.max,
//   //                   children: [
//   //                     Container(
//   //                       child: EditText(
//   //                         mController: priceController,
//   //                         inputType: TextInputType.number,
//   //                         inputFormate: [
//   //                           FilteringTextInputFormatter.allow(
//   //                               RegExp(r'[0-9.]')),
//   //                         ],
//   //                         isPassword: false,
//   //                         text: "Price",
//   //                         validator: validator.validateTextField,
//   //                       ),
//   //                     ),
//   //                     SizedBox(
//   //                       height: 20,
//   //                     ),
//   //                     Row(
//   //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   //                       crossAxisAlignment: CrossAxisAlignment.end,
//   //                       children: <Widget>[
//   //                         GestureDetector(
//   //                           onTap: () {
//   //                             if (!isAmountLock) {
//   //                               isAmountLock = true;
//   //                               Navigator.pop(context);
//   //                               isAmountLock = false;
//   //                             }
//   //                           },
//   //                           child: Container(
//   //                             alignment: Alignment.center,
//   //                             height: 50,
//   //                             width: width / 3.5,
//   //                             child: TextWidget("Cancel",
//   //                                 textColor: secondaryTextColor, isCentered: true),
//   //                             decoration: boxDecoration(
//   //                                 bgColor: buttonThemeColor, radius: 8.0),
//   //                           ),
//   //                         ),
//   //                         GestureDetector(
//   //                           onTap: () async {
//   //                             if (!isAmountLock) {
//   //                               isAmountLock = true;
//   //                               validator.validate();
//   //                               if (validator.validate()) {
//   //                                 Navigator.pop(context);
//   //                                 setState(() {
//   //                                   addProductsInSalesList(item, null);
//   //                                 });
//   //                                 if (item.toppingGroupId != null) {
//   //                                   await queryAllProductToppingDetails(
//   //                                       item.toppingGroupId);
//   //                                   if (productToppingList.length > 0) {
//   //                                     toppingDialog(context, item);
//   //                                   }
//   //                                 }
//   //                               }
//   //                               isAmountLock = false;
//   //                             }
//   //                           },
//   //                           child: Container(
//   //                             alignment: Alignment.center,
//   //                             height: 50,
//   //                             width: width / 3.5,
//   //                             child: TextWidget("Ok",
//   //                                 textColor: secondaryTextColor, isCentered: true),
//   //                             decoration: boxDecoration(
//   //                                 bgColor: buttonThemeColor, radius: 8.0),
//   //                           ),
//   //                         ),
//   //                       ],
//   //                     ),
//   //                   ],
//   //                 ),
//   //               ),
//   //             )),
//   //           ),
//   //         );
//   //       });
//   // }
//   //
//   // //DYNAMIC QUANTITY ALERT DIALOG
//   // dynamicQtyDialog(BuildContext context, String tGroupID, String price,
//   //     Products item, PriceUnitType unit) {
//   //   qtyController.text = "1";
//   //   priceController.text = price != null && price != "" ? price : "";
//   //   Validator validator = new Validator();
//   //   return showDialog(
//   //       context: context,
//   //       builder: (context) {
//   //         return Center(
//   //           child: SingleChildScrollView(
//   //             child: AlertDialog(
//   //                 content: Container(
//   //               width: double.maxFinite,
//   //               child: Form(
//   //                 key: validator.formkey,
//   //                 child: Column(
//   //                   mainAxisAlignment: MainAxisAlignment.start,
//   //                   children: [
//   //                     Container(
//   //                       child: EditText(
//   //                         mController: qtyController,
//   //                         inputType: TextInputType.number,
//   //                         inputFormate: [
//   //                           FilteringTextInputFormatter.allow(
//   //                               RegExp(r'[0-9.]')),
//   //                         ],
//   //                         isPassword: false,
//   //                         text: "Qty",
//   //                         validator: validator.validateTextField,
//   //                         autofocus: true,
//   //                       ),
//   //                     ),
//   //                     SizedBox(
//   //                       height: 10,
//   //                     ),
//   //                     priceController.text == ""
//   //                         ? Container(
//   //                             child: EditText(
//   //                               mController: priceController,
//   //                               inputType: TextInputType.number,
//   //                               inputFormate: [
//   //                                 FilteringTextInputFormatter.allow(
//   //                                     RegExp(r'[0-9.]')),
//   //                               ],
//   //                               isPassword: false,
//   //                               text: "Price",
//   //                               validator: validator.validateTextField,
//   //                               autofocus: true,
//   //                             ),
//   //                           )
//   //                         : Container(),
//   //                     SizedBox(
//   //                       height: 20,
//   //                     ),
//   //                     Row(
//   //                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//   //                       children: <Widget>[
//   //                         InkWell(
//   //                           onTap: () {
//   //                             qtyController.clear();
//   //                             priceController.clear();
//   //                             Navigator.pop(context);
//   //                           },
//   //                           child: Container(
//   //                             alignment: Alignment.center,
//   //                             height: 50,
//   //                             width: width / 3.5,
//   //                             child: TextWidget("Cancel",
//   //                                 textColor: secondaryTextColor, isCentered: true),
//   //                             decoration: boxDecoration(
//   //                                 bgColor: buttonThemeColor, radius: 8.0),
//   //                           ),
//   //                         ),
//   //                         InkWell(
//   //                           onTap: () async {
//   //                             validator.validate();
//   //                             if (validator.validate()) {
//   //                               dynamicQuantity =
//   //                                   double.parse(qtyController.text) > 0
//   //                                       ? double.parse(qtyController.text)
//   //                                       : 1;
//   //
//   //                               await addProductsInSalesList(item, unit);
//   //                               print(productToppingList.length);
//   //                               Navigator.pop(context);
//   //                               if (tGroupID != null) {
//   //                                 await queryAllProductToppingDetails(tGroupID);
//   //                                 if (productToppingList.length > 0) {
//   //                                   toppingDialog(context, item);
//   //                                 }
//   //                               }
//   //
//   //                               qtyController.clear();
//   //                               priceController.clear();
//   //                             }
//   //                           },
//   //                           child: Container(
//   //                             alignment: Alignment.center,
//   //                             height: 50,
//   //                             width: width / 3.5,
//   //                             child: TextWidget("Ok",
//   //                                 textColor: secondaryTextColor, isCentered: true),
//   //                             decoration: boxDecoration(
//   //                                 bgColor: buttonThemeColor, radius: 8.0),
//   //                           ),
//   //                         ),
//   //                       ],
//   //                     ),
//   //                   ],
//   //                 ),
//   //               ),
//   //             )),
//   //           ),
//   //         );
//   //       });
//   // }

//   //HOLD SALES VIEW ALERT DIALOG
//   commonDialogAlertDialog(BuildContext context, String showText) {
//     return showDialog(
//         barrierDismissible: false,
//         context: context,
//         builder: (BuildContext context) {
//           return AlertDialog(
//             content: Container(
//               width: 200,
//               height: 20,
//               child: Text(
//                 "$showText",
//                 style: TextStyle(color: primaryTextColor, fontSize: 18.0),
//               ),
//             ),
//             actions: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.end,
//                 children: [
//                   GestureDetector(
//                     onTap: () {
//                       if (!isLock) {
//                         isLock = true;
//                         Navigator.pop(context);
//                         isLock = false;
//                       }
//                     },
//                     child: Container(
//                       alignment: Alignment.center,
//                       height: 40,
//                       width: width / 5,
//                       child: TextWidget("Close", textColor: secondaryTextColor, isCentered: true),
//                       decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                     ),
//                   ),
//                 ],
//               ),
//             ],
//           );
//         });
//   }

//   panelSwitch() {
//     if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Normal") {
//       settingsDetail.settingKey = key_panelType;
//       settingsDetail.settingValue = "Id";
//       fnUtilities.updateDeviceSettingDetails(settingsDetail);
//     } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Id") {
//       settingsDetail.settingKey = key_panelType;
//       settingsDetail.settingValue = "Name";
//       fnUtilities.updateDeviceSettingDetails(settingsDetail);
//     } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Name") {
//       settingsDetail.settingKey = key_panelType;
//       settingsDetail.settingValue = "Key";
//       fnUtilities.updateDeviceSettingDetails(settingsDetail);
//     } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Key") {
//       settingsDetail.settingKey = key_panelType;
//       settingsDetail.settingValue = "Normal";
//       fnUtilities.updateDeviceSettingDetails(settingsDetail);
//     }
//     setState(() {
//       initSettings();
//     });
//   }

//   productCountIncrement(productID) {
//     if (this.mounted) {
//       setState(() {
//         List<Transactions> qtyList = saleTransactionList.where((element) => element.toppingId == null && element.prodId == productID).toList();
//         double qty = 0;
//         for (Transactions qt in qtyList) {
//           if (qt?.qty != null) {
//             qty += double.parse(qt?.qty);
//           }
//         }
//         filterProductList.firstWhere((element) => element.productId == productID, orElse: () => null)?.count =
//             qty != 0 ? double.parse(fnUtilities.checkQtyDoubleValue(qty.toString())) : null;
//       });
//     }
//   }

//   loopForProductQty(List<Transactions> saleTransaction) async {
//     for (Products p in filterProductList) {
//       p.count = null;
//     }
//     for (Transactions t in saleTransaction) {
//       await productCountIncrement(t.prodId);
//     }
//   }

//   Future<bool> conformationDialog(context) {
//     return showDialog(
//         context: context,
//         builder: (context) {
//           return Center(
//             child: SingleChildScrollView(
//               child: AlertDialog(
//                   content: Container(
//                 child: Column(
//                   mainAxisAlignment: MainAxisAlignment.start,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   mainAxisSize: MainAxisSize.max,
//                   children: [
//                     Container(
//                       child: TextWidget("Are you sure want to exit?", textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
//                     ),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                       crossAxisAlignment: CrossAxisAlignment.end,
//                       children: <Widget>[
//                         GestureDetector(
//                           onTap: () {
//                             Navigator.pop(context, false);
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                         SizedBox(
//                           width: 10,
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             Navigator.pop(context, true);
//                           },
//                           child: Container(
//                             alignment: Alignment.center,
//                             height: 50,
//                             width: width / 3.5,
//                             child: TextWidget("Exit", textColor: secondaryTextColor, isCentered: true),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                           ),
//                         ),
//                       ],
//                     ),
//                   ],
//                 ),
//               )),
//             ),
//           );
//         });
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;
//     var shortestSide = MediaQuery.of(context).size.shortestSide;
//     useMobileLayout = shortestSide < 600;

//     return WillPopScope(
//       onWillPop: () async {
//         if (saleTransactionList.length > 0) {
//           return conformationDialog(context);
//         }
//         return true;
//       },
//       child: Scaffold(
//         backgroundColor: secondaryTextColor,
//         appBar: BaseAppBar(
//           automaticallyImplementing: false,
//           appBar: AppBar(),
//           widgets: <Widget>[
//             Row(
//               mainAxisAlignment: MainAxisAlignment.end,
//               children: [
//                 InkWell(
//                   onTap: () async {
//                     WorkspaceSettings settings = WorkspaceSettings();
//                     settings.settingKey = "FavoriteProduct";
//                     setState(() {
//                       if (isFavorite) {
//                         isFavorite = false;
//                         settings.settingValue = "0";
//                         fnUtilities.updateDeviceSettingDetails(settings);
//                       } else {
//                         isFavorite = true;
//                         settings.settingValue = "1";
//                         fnUtilities.updateDeviceSettingDetails(settings);
//                       }
//                     });
//                     if (!isLock) {
//                       isLock = true;
//                       await queryAllProductDetails();
//                       isLock = false;
//                     }
//                   },
//                   child: Container(
//                     alignment: Alignment.center,
//                     padding: EdgeInsets.all(5.0),
//                     width: 40,
//                     height: height / 20,
//                     child: isFavorite
//                         ? Icon(
//                             Icons.favorite_sharp,
//                             color: Colors.blueAccent,
//                           )
//                         : Icon(Icons.favorite_outline),
//                   ),
//                 ),
//                 SizedBox(
//                   width: 2,
//                 ),
//                 panels != "Normal"
//                     ? InkWell(
//                         onTap: () async {
//                           if (!isLock) {
//                             isLock = true;
//                             await clearFunction();
//                             isLock = false;
//                           }
//                         },
//                         child: Container(
//                           alignment: Alignment.center,
//                           padding: EdgeInsets.all(5.0),
//                           width: 40,
//                           height: height / 20,
//                           child: Icon(Icons.cleaning_services),
//                         ),
//                       )
//                     : SizedBox(),
//                 SizedBox(
//                   width: 8,
//                 ),
//                 InkWell(
//                   onTap: () async {
//                     if (!isLock) {
//                       isLock = true;
//                       await clearFunction();
//                       panelSwitch();
//                       isLock = false;
//                     }
//                   },
//                   child: Container(
//                     alignment: Alignment.center,
//                     padding: EdgeInsets.all(5.0),
//                     width: 40,
//                     height: height / 20,
//                     child: Icon(Icons.swipe_outlined),
//                   ),
//                 ),
//               ],
//             ),
//             PopupMenuButton(
//               padding: EdgeInsets.only(top: 10, left: 0.0),
//               itemBuilder: (BuildContext context) {
//                 return <PopupMenuEntry>[
//                   PopupMenuItem(
//                     child: ListTile(
//                       title: Text("New Product"),
//                       leading: Icon(
//                         Icons.add,
//                         size: 20,
//                       ),
//                       onTap: () {
//                         if (!isLock) {
//                           isLock = true;
//                           Navigator.pop(context);
//                           Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog())).then((value) async {
//                             await clearFunction();
//                             await queryAllProductDetails();
//                             await loopForProductQty(saleTransactionList);
//                             await findTotal();
//                           });
//                           isLock = false;
//                         }
//                       },
//                     ),
//                   ),
//                   PopupMenuItem(
//                     child: ListTile(
//                       title: Text("Switch Panel"),
//                       leading: Icon(
//                         Icons.swipe_outlined,
//                         size: 20,
//                       ),
//                       onTap: () async {
//                         if (!isLock) {
//                           isLock = true;
//                           await clearFunction();
//                           panelSwitch();
//                           Navigator.pop(context);
//                           isLock = false;
//                         }
//                       },
//                     ),
//                   ),
//                 ];
//               },
//             ),
//           ],
//         ),
//         body: Column(
//           children: [
//             SizedBox(height: 15),
//             Expanded(
//               child: SingleChildScrollView(
//                 child: Container(
//                     width: width,
//                     height: height / 1.2,
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       crossAxisAlignment: CrossAxisAlignment.start,
//                       children: <Widget>[
//                         Container(
//                           width: width,
//                           color: Colors.white10,
//                           child: Column(
//                             children: [
//                               Row(
//                                 mainAxisAlignment: MainAxisAlignment.center,
//                                 children: [
//                                   Container(
//                                     padding: const EdgeInsets.only(top: 7),
//                                     width: width / 1.1,
//                                     height: 50,
//                                     child: TextField(
//                                       controller: searchController,
//                                       onChanged: filterProductsByName,
//                                       style: TextStyle(color: Colors.black),
//                                       autofocus: false,
//                                       decoration: InputDecoration(
//                                         labelText: 'Search',
//                                         border: InputBorder.none,
//                                         enabledBorder: OutlineInputBorder(
//                                           borderRadius: BorderRadius.circular(4),
//                                           borderSide: const BorderSide(color: grey, width: 0.0),
//                                         ),
//                                         focusedBorder: OutlineInputBorder(
//                                           borderRadius: BorderRadius.circular(4),
//                                           borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
//                                         ),
//                                         suffixIcon: InkWell(
//                                           onTap: () async {
//                                             FocusScope.of(context).unfocus();
//                                             if (searchController.text != "") {
//                                               searchController.text = "";
//                                               filterProductsByName("");
//                                             }
//                                           },
//                                           child: new Icon(
//                                             Icons.clear,
//                                             color: primaryTextColor,
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               ),
//                             ],
//                           ),
//                         ),
//                         GestureDetector(
//                           onTap: () async {
//                             if (!isCounterLock) {
//                               isCounterLock = true;
//                               Navigator.push(
//                                       context, MaterialPageRoute(builder: (context) => SalesMainPage(saleTransactionList: saleTransactionList, sales: sales, saleType: saleType)))
//                                   .then((value) async {
//                                 if (value != null) {
//                                   SaleModel model = value;
//                                   if (model.isCompletedSales) {
//                                     setState(() {
//                                       saleTransactionList = [];
//                                       sales = null;
//                                     });
//                                     await loopForProductQty(saleTransactionList);
//                                     await findTotal();
//                                     if (successDialogOnSales) {
//                                       await callSuccessDialog(context, model.fullSale.salesId);
//                                     }
//                                   } else {
//                                     sales = model?.fullSale;
//                                     saleType = model?.saleType;
//                                     saleTransactionList = model?.transaction;
//                                     await loopForProductQty(saleTransactionList);
//                                     await findTotal();
//                                   }
//                                 }
//                               });

//                               isCounterLock = false;
//                             }
//                           },
//                           child: Container(
//                             width: width / 1.1,
//                             padding: const EdgeInsets.all(5),
//                             alignment: Alignment.center,
//                             margin: EdgeInsets.fromLTRB(15, 5, 15, 0),
//                             decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.center,
//                               children: [
//                                 Container(
//                                   child: TextWidget("COUNTER", textColor: secondaryTextColor, isCentered: true),
//                                 ),
//                                 SizedBox(
//                                   width: 10,
//                                 ),
//                                 Container(
//                                   alignment: Alignment.centerRight,
//                                   child: TextWidget("(${fnUtilities.checkQtyDoubleValue(totalQty.toString())})",
//                                       textColor: whiteColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ),
//                         SizedBox(
//                           height: 15,
//                         ),
//                         Expanded(
//                             child: !isProductLoading
//                                 ? Row(
//                                     crossAxisAlignment: CrossAxisAlignment.start,
//                                     children: [
//                                       panels == "Normal" ? normalPanelType() : Container(),
//                                       panels == "Id" ? filterPanelType() : Container(),
//                                       panels == "Name" ? normalPanelType() : Container(),
//                                       panels == "Key" ? filterKeyPanelType() : Container(),
//                                     ],
//                                   )
//                                 : Center(child: CircularProgressIndicator()))
//                       ],
//                     )),
//               ),
//             ),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget listProdItem(Products item, int index) {
//     return Padding(
//       padding: const EdgeInsets.symmetric(horizontal: 3.0),
//       child: item?.productId != null
//           ? InkWell(
//               onTap: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   if (item?.priceType != null) {
//                     String qty = saleTransactionList.firstWhere((element) => element.prodId == item.productId, orElse: () => null)?.qty;
//                     if (qty.isEmptyOrNull) {
//                       if (item.priceType == 1) {
//                         await addProductsInSalesList(item, null, isReduce: false);
//                       }
//                       setState(() {
//                         qtyController.text = item.priceType == 1 ? "1" : "0";
//                         productCountIncrement(item.productId);
//                       });
//                     } else {
//                       setState(() {
//                         qtyController.text = fnUtilities.checkQtyDoubleValue(qty.toString());
//                       });
//                     }

//                     await productBottomSheet(item, index);
//                     if (MyApp.isFilterEnable) {
//                       await clearFunction();
//                     }
//                   } else {
//                     showToast("Please update this product");
//                   }
//                   isLock = false;
//                 }
//               },
//               child: Container(
//                 margin: const EdgeInsets.all(2.0),
//                 padding: EdgeInsets.only(left: 16, right: 16, top: 2, bottom: 2),
//                 decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.spaceAround,
//                   crossAxisAlignment: CrossAxisAlignment.start,
//                   children: <Widget>[
//                     Container(
//                       width: width / 10,
//                       alignment: Alignment.centerLeft,
//                       child: TextWidget(item.productSaleId.toString(), textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isCentered: true),
//                     ),
//                     SizedBox(height: 1),
//                     Container(
//                       width: width / 2.5,
//                       alignment: Alignment.centerLeft,
//                       child: TextWidget(item.name, textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
//                     ),
//                     SizedBox(height: 1),
//                     Container(
//                       width: width / 4,
//                       alignment: Alignment.centerRight,
//                       child: item.price == "" || item.price == null
//                           ? TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                           : TextWidget("Rs.${item.price}", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                     ),
//                   ],
//                 ),
//               ),
//             )
//           : InkWell(
//               onTap: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                           builder: (context) => AddProductDialog(
//                                 productName: searchController.text,
//                               ))).then((value) async {
//                     await clearFunction();
//                     await queryAllProductDetails();
//                     await loopForProductQty(saleTransactionList);
//                     await findTotal();
//                   });
//                   isLock = false;
//                 }
//               },
//               child: Container(
//                 margin: const EdgeInsets.all(2.0),
//                 padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
//                 decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
//                 child: Row(
//                   mainAxisAlignment: MainAxisAlignment.center,
//                   crossAxisAlignment: CrossAxisAlignment.center,
//                   children: <Widget>[
//                     Icon(Icons.add),
//                     SizedBox(width: 5),
//                     TextWidget("New Product", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
//                   ],
//                 ),
//               ),
//             ),
//     );
//   }

//   List<Products> productListBasedOnAmount = [];
//   productBottomSheet(Products item, int index) async {
//     currentMeasurement = null;
//     currentProduct = null;
//     currentMeasurementPrice = null;
//     productListBasedOnAmount = [];
//     productToppingList = [];
//     measurementAmountList = [];
//     await getAllProductUnitsDetails(item?.productId);
//     if (item?.priceType == 2 && productUnitList.isNotEmpty) {
//       PriceUnitType UnitMeas = productUnitList.first;
//       currentMeasurement = UnitMeas;
//       await getMeasurementDynamicAmountList(UnitMeas, item?.name);
//       qtyController.text = returnMeasurementQty(item, UnitMeas);
//     } else if (item?.priceType == 0) {
//       await getDynamicAmountList(item);
//     }
//     await queryAllProductToppingDetails(item);
//     showModalBottomSheet<void>(
//       context: context,
//       isScrollControlled: true,
//       builder: (BuildContext context) {
//         return StatefulBuilder(builder: (BuildContext context, StateSetter setState) {
//           return Padding(
//             padding: MediaQuery.of(context).viewInsets,
//             child: Container(
//               height: height / 1.8,
//               padding: EdgeInsets.zero,
//               width: width,
//               child: SingleChildScrollView(
//                 child: Column(
//                   children: [
//                     Container(
//                       margin: const EdgeInsets.only(bottom: .1), //Same as `blurRadius` i guess
//                       decoration: BoxDecoration(
//                         borderRadius: BorderRadius.circular(3.0),
//                         color: Colors.white,
//                         boxShadow: [
//                           BoxShadow(
//                             color: Colors.grey,
//                             offset: Offset(0.0, .1), //(x,y)
//                             blurRadius: 3.0,
//                           ),
//                         ],
//                       ),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                         children: [
//                           Row(
//                             mainAxisAlignment: MainAxisAlignment.start,
//                             children: [
//                               Container(
//                                 alignment: Alignment.centerLeft,
//                                 width: width / 4,
//                                 child: GestureDetector(
//                                   onTap: () async {
//                                     if (!isCounterLock) {
//                                       isCounterLock = true;
//                                       Navigator.pop(context);
//                                       Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog())).then((value) async {
//                                         await clearFunction();
//                                         await queryAllProductDetails();
//                                         await loopForProductQty(saleTransactionList);
//                                         await findTotal();
//                                       });
//                                       isCounterLock = false;
//                                     }
//                                   },
//                                   child: Container(
//                                     width: width / 4,
//                                     padding: const EdgeInsets.all(5),
//                                     alignment: Alignment.centerLeft,
//                                     margin: EdgeInsets.fromLTRB(5, 5, 15, 0),
//                                     decoration: boxDecoration(bgColor: buttonThemeColor, radius: 5.0),
//                                     child: Row(
//                                       mainAxisAlignment: MainAxisAlignment.center,
//                                       children: [
//                                         Container(
//                                           child: TextWidget("New", textColor: secondaryTextColor, isCentered: true),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 ),
//                               ),
//                               Container(
//                                 width: width / 1.9,
//                                 alignment: Alignment.center,
//                                 padding: EdgeInsets.only(left: 5),
//                                 child: TextWidget(
//                                     "${item?.priceType == 2 ? currentMeasurementPrice == null ? item.name + '-' + currentMeasurement?.measurement : item.name + '-' + currentMeasurementPrice?.measurement : item.name}",
//                                     textColor: primaryTextColor,
//                                     fontSize: textSizeMedium,
//                                     fontFamily: fontSemibold),
//                               ),
//                             ],
//                           ),
//                           IconButton(
//                             icon: Icon(
//                               Icons.close_outlined,
//                               color: Colors.red,
//                             ),
//                             onPressed: () {
//                               Navigator.pop(context);
//                             },
//                           ),
//                         ],
//                       ),
//                     ),
//                     SizedBox(
//                       height: 10,
//                     ),
//                     (item?.priceType == 0 && currentProduct?.price != null) ||
//                             (item?.priceType == 2 && (currentMeasurement?.measurementPrice != null || currentMeasurementPrice != null) || item?.priceType == 1)
//                         ? Container(
//                             padding: EdgeInsets.symmetric(vertical: 5),
//                             // decoration: BoxDecoration(
//                             //     borderRadius: BorderRadius.circular(4),
//                             //     color: Colors.blue[100],
//                             //     border: Border.all(
//                             //       color: Colors.grey,
//                             //       width: 1,
//                             //     )),
//                             child: Row(
//                               mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                               children: [
//                                 Container(
//                                   padding: EdgeInsets.only(left: 5),
//                                   child: TextWidget(
//                                       "\u{20B9}${item?.priceType == 1 ? item.price : item?.priceType == 0 ? currentProduct?.price : item?.priceType == 2 ? currentMeasurementPrice == null ? currentMeasurement?.measurementPrice : currentMeasurementPrice?.measurementPrice : ""}",
//                                       textColor: primaryTextColor,
//                                       fontSize: textSizeMedium,
//                                       fontFamily: fontSemibold),
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.center,
//                                   children: [
//                                     // IconButton(
//                                     //     onPressed: () async {
//                                     //       if(item?.price != null ||  ((item?.priceType == 2 && currentMeasurement?.measurementPrice != null) || currentProduct != null || currentMeasurementPrice != null)) {
//                                     //         if (double.tryParse(qtyController.text) > 0) {
//                                     //           setState(() {
//                                     //             qtyController.text =
//                                     //                 fnUtilities.checkQtyDoubleValue(
//                                     //                     (double.tryParse(qtyController.text) -
//                                     //                         1)
//                                     //                         .toString());
//                                     //           });
//                                     //           await addProductsInSalesList(
//                                     //               currentProduct == null
//                                     //                   ? item
//                                     //                   : currentProduct, currentMeasurementPrice == null ? currentMeasurement : currentMeasurementPrice,
//                                     //               isReduce: true);
//                                     //           if(item?.priceType == 1 && double.tryParse(qtyController.text) == 0){
//                                     //             for(Topping proTop in productToppingList){
//                                     //               if(proTop.isActive) {
//                                     //                 proTop?.isActive = false;
//                                     //               }
//                                     //             }
//                                     //             addProductsInSalesList1(item,
//                                     //                 isRemove: true);
//                                     //           }
//                                     //           productCountIncrement(item.productId);
//                                     //         } else {
//                                     //           setState(() {
//                                     //             //productCountIncrement(item.productId);
//                                     //           });
//                                     //         }
//                                     //       }
//                                     //       else{
//                                     //         showToast("Price is mandatory");
//                                     //       }
//                                     //     },
//                                     //     icon: Icon(
//                                     //       Icons.remove,
//                                     //       color: Colors.red,
//                                     //       size: 25,
//                                     //     )),
//                                     GestureDetector(
//                                       onTap: () async {
//                                         if (item?.price != null ||
//                                             ((item?.priceType == 2 && currentMeasurement?.measurementPrice != null) || currentProduct != null || currentMeasurementPrice != null)) {
//                                           if (double.tryParse(qtyController.text) > 0) {
//                                             setState(() {
//                                               qtyController.text = fnUtilities.checkQtyDoubleValue((double.tryParse(qtyController.text) - 1).toString());
//                                             });
//                                             await addProductsInSalesList(
//                                                 currentProduct == null ? item : currentProduct, currentMeasurementPrice == null ? currentMeasurement : currentMeasurementPrice,
//                                                 isReduce: true);
//                                             if (item?.priceType == 1 && double.tryParse(qtyController.text) == 0) {
//                                               for (Topping proTop in productToppingList) {
//                                                 if (proTop.isActive) {
//                                                   proTop?.isActive = false;
//                                                 }
//                                               }
//                                               addProductsInSalesList1(item, isRemove: true);
//                                             }
//                                             productCountIncrement(item.productId);
//                                           } else {
//                                             setState(() {
//                                               //productCountIncrement(item.productId);
//                                             });
//                                           }
//                                         } else {
//                                           showToast("Price is mandatory");
//                                         }
//                                       },
//                                       child: Container(
//                                         width: 50,
//                                         padding: const EdgeInsets.all(5),
//                                         alignment: Alignment.centerLeft,
//                                         margin: EdgeInsets.fromLTRB(5, 5, 15, 0),
//                                         decoration: boxDecoration(bgColor: white, radius: 5.0),
//                                         child: Row(
//                                           mainAxisAlignment: MainAxisAlignment.center,
//                                           children: [
//                                             Container(
//                                                 child: Icon(
//                                               Icons.remove,
//                                               color: Colors.red,
//                                               size: 25,
//                                             )),
//                                           ],
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                         width: width / 4.5,
//                                         child: EditText(
//                                           mController: qtyController,
//                                           isPassword: false,
//                                           inputType: TextInputType.number,
//                                           enable: item?.dynamicQuantity == 1 ? true : false,
//                                           textAlign: TextAlign.center,
//                                           onChange: (value) async {
//                                             if (value.isNotEmpty) {
//                                               dynamicQuantity = double.parse(qtyController.text);
//                                               await commonIncrementOnTapProduct(currentProduct == null ? item : currentProduct, index,
//                                                   unitMeasurement: currentMeasurementPrice == null ? currentMeasurement : currentMeasurementPrice, isDynamic: true);
//                                             }
//                                           },
//                                         )),
//                                     GestureDetector(
//                                       onTap: () async {
//                                         if (item?.price != null ||
//                                             ((item?.priceType == 2 && currentMeasurement?.measurementPrice != null) || currentProduct != null || currentMeasurementPrice != null)) {
//                                           setState(() {
//                                             qtyController.text = qtyController.text = fnUtilities.checkQtyDoubleValue((double.tryParse(qtyController.text) + 1).toString());
//                                           });
//                                           await commonIncrementOnTapProduct(currentProduct == null ? item : currentProduct, index,
//                                               unitMeasurement: currentMeasurementPrice == null ? currentMeasurement : currentMeasurementPrice);
//                                           productCountIncrement(item.productId);
//                                         } else {
//                                           showToast("Price is mandatory");
//                                         }
//                                       },
//                                       child: Container(
//                                         width: 50,
//                                         padding: const EdgeInsets.all(5),
//                                         alignment: Alignment.centerLeft,
//                                         margin: EdgeInsets.fromLTRB(5, 5, 15, 0),
//                                         decoration: boxDecoration(bgColor: white, radius: 5.0),
//                                         child: Row(
//                                           mainAxisAlignment: MainAxisAlignment.center,
//                                           children: [
//                                             Container(
//                                                 child: Icon(
//                                               Icons.add,
//                                               color: Colors.green,
//                                               size: 25,
//                                             )),
//                                           ],
//                                         ),
//                                       ),
//                                     ),
//                                     //               IconButton(
//                                     //                   onPressed: () async {
//                                     // if(item?.price != null || ((item?.priceType == 2 && currentMeasurement?.measurementPrice != null) || currentProduct != null || currentMeasurementPrice != null)) {
//                                     //   setState(() {
//                                     //           qtyController.text = qtyController.text =
//                                     //               fnUtilities.checkQtyDoubleValue(
//                                     //                   (double.tryParse(qtyController.text) + 1)
//                                     //                       .toString());
//                                     //   });
//                                     //   await commonIncrementOnTapProduct(
//                                     //             currentProduct == null ? item : currentProduct, index,
//                                     //             unitMeasurement: currentMeasurementPrice == null ? currentMeasurement: currentMeasurementPrice);
//                                     //   productCountIncrement(item.productId);
//                                     // }
//                                     // else{
//                                     //   showToast("Price is mandatory");
//                                     // }
//                                     //                   },
//                                     //                   icon: Icon(
//                                     //                     Icons.add,
//                                     //                     color: Colors.green,
//                                     //                     size: 25,
//                                     //                   ))
//                                   ],
//                                 ),
//                               ],
//                             ),
//                           )
//                         : Container(),
//                     Container(
//                       width: width / 1,
//                       alignment: Alignment.centerLeft,
//                       child: Row(
//                         children: [
//                           productToppingList.isNotEmpty
//                               ? GestureDetector(
//                                   onTap: () async {
//                                     toppingDialog(context, item);
//                                   },
//                                   child: Container(
//                                     width: width / 3,
//                                     padding: const EdgeInsets.all(5),
//                                     alignment: Alignment.centerLeft,
//                                     margin: EdgeInsets.fromLTRB(5, 5, 15, 0),
//                                     decoration: boxDecoration(bgColor: buttonThemeColor, radius: 5.0),
//                                     child: Row(
//                                       mainAxisAlignment: MainAxisAlignment.center,
//                                       children: [
//                                         Container(
//                                           child: TextWidget("Toppings", textColor: secondaryTextColor, isCentered: true),
//                                         ),
//                                       ],
//                                     ),
//                                   ),
//                                 )
//                               : Container(),
//                         ],
//                       ),
//                     ),
//                     Column(
//                       children: [
//                         SizedBox(
//                           height: 10,
//                         ),
//                         item?.priceType == 0 || (item?.priceType == 2 && currentMeasurement?.measurementPrice == null && currentMeasurementPrice == null)
//                             ? (item?.priceType == 0 && currentProduct == null) || item?.priceType == 2
//                                 ? Form(
//                                     key: validator.formkey,
//                                     child: Row(
//                                       mainAxisAlignment: MainAxisAlignment.center,
//                                       children: [
//                                         // IconButton(
//                                         //     onPressed: () async {
//                                         //       setState((){
//                                         //         currentProduct = null;
//                                         //         currentMeasurement = null;
//                                         //         currentMeasurementPrice = null;
//                                         //         priceController.clear();
//                                         //         qtyController.text = "0";
//                                         //       });
//                                         //     },
//                                         //     icon: Icon(
//                                         //       Icons.clear,
//                                         //       color: Colors.red,
//                                         //     )),
//                                         Container(
//                                           width: width / 2,
//                                           child: EditText(
//                                             mController: priceController,
//                                             inputType: TextInputType.number,
//                                             inputFormate: [
//                                               FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
//                                             ],
//                                             isPassword: false,
//                                             text: "Price",
//                                             enable: currentProduct != null || currentMeasurementPrice != null ? false : true,
//                                             validator: validator.validateTextField,
//                                           ),
//                                         ),
//                                         IconButton(
//                                             onPressed: () async {
//                                               validator.validate();
//                                               if (validator.validate()) {
//                                                 if (currentMeasurement != null) {
//                                                   currentMeasurementPrice = measurementValueAssign(currentMeasurement, priceController.text);
//                                                   currentMeasurementPrice.measurementPrice = priceController.text;
//                                                 } else {
//                                                   item.price = priceController.text;
//                                                 }
//                                                 if ((item?.priceType == 2 && currentMeasurement != null) || item?.priceType == 0) {
//                                                   await commonIncrementOnTapProduct(item, index, unitMeasurement: currentMeasurementPrice);
//                                                 } else {
//                                                   showToast("Please select measurement");
//                                                 }
//                                                 if (currentMeasurement != null) {
//                                                   await getMeasurementDynamicAmountList(currentMeasurementPrice, item?.name);
//                                                   currentMeasurement = null;
//                                                 } else {
//                                                   await getDynamicAmountList(item);
//                                                   currentProduct = item;
//                                                 }
//                                                 productCountIncrement(item.productId);
//                                                 setState(() {
//                                                   qtyController.text = "1";
//                                                   priceController.clear();
//                                                 });
//                                               }
//                                             },
//                                             icon: Icon(
//                                               Icons.add,
//                                               color: Colors.green,
//                                             ))
//                                       ],
//                                     ),
//                                   )
//                                 : Container(
//                                     width: width / 1,
//                                     alignment: Alignment.centerLeft,
//                                     child: GestureDetector(
//                                       onTap: () async {
//                                         setState(() {
//                                           currentProduct = null;
//                                           currentMeasurement = null;
//                                           currentMeasurementPrice = null;
//                                           priceController.clear();
//                                           qtyController.text = "0";
//                                         });
//                                       },
//                                       child: Container(
//                                         width: width / 3,
//                                         padding: const EdgeInsets.all(5),
//                                         alignment: Alignment.centerLeft,
//                                         margin: EdgeInsets.fromLTRB(5, 5, 15, 0),
//                                         decoration: boxDecoration(bgColor: buttonThemeColor, radius: 5.0),
//                                         child: Row(
//                                           mainAxisAlignment: MainAxisAlignment.center,
//                                           children: [
//                                             Container(
//                                               child: TextWidget("New Price", textColor: secondaryTextColor, isCentered: true),
//                                             ),
//                                           ],
//                                         ),
//                                       ),
//                                     ),
//                                   )
//                             : Container(),
//                         SizedBox(
//                           height: 10,
//                         ),
//                         productListBasedOnAmount.isNotEmpty || measurementAmountList.isNotEmpty
//                             ? Container(
//                                 width: double.maxFinite,
//                                 child: productListBasedOnAmount.isNotEmpty
//                                     ? ListView.builder(
//                                         itemCount: productListBasedOnAmount.length,
//                                         shrinkWrap: true,
//                                         physics: NeverScrollableScrollPhysics(),
//                                         itemBuilder: (BuildContext context, int index) {
//                                           var item = productListBasedOnAmount[index];
//                                           return InkWell(
//                                             onTap: () async {
//                                               setState(() {
//                                                 currentProduct = item;
//                                                 qtyController.text = returnProductQty(item);
//                                                 priceController.text = item.price;
//                                               });
//                                             },
//                                             child: Container(
//                                               margin: const EdgeInsets.all(2.0),
//                                               padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
//                                               decoration:
//                                                   boxDecoration(radius: 10, showShadow: true, bgColor: currentProduct?.price == item?.price ? Colors.green : secondaryTextColor),
//                                               child: Row(
//                                                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                                 children: <Widget>[
//                                                   Container(
//                                                     width: width / 3,
//                                                     alignment: Alignment.centerLeft,
//                                                     child: TextWidget(item.name,
//                                                         textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
//                                                   ),
//                                                   SizedBox(height: 1),
//                                                   Container(
//                                                     width: width / 4,
//                                                     alignment: Alignment.centerRight,
//                                                     child: item.price != "" && item.price != null
//                                                         ? TextWidget("\u{20B9}${item.price}", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                                                         : TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                           );
//                                         })
//                                     : ListView.builder(
//                                         itemCount: measurementAmountList.length,
//                                         shrinkWrap: true,
//                                         physics: NeverScrollableScrollPhysics(),
//                                         itemBuilder: (BuildContext context, int index) {
//                                           var itemMes = measurementAmountList[index];
//                                           return InkWell(
//                                             onTap: () async {
//                                               setState(() {
//                                                 currentMeasurementPrice = itemMes;
//                                                 qtyController.text = returnMeasurementQty(item, itemMes);
//                                                 priceController.text = itemMes.measurementPrice;
//                                                 currentMeasurement = null;
//                                               });
//                                             },
//                                             child: Container(
//                                               margin: const EdgeInsets.all(2.0),
//                                               padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
//                                               decoration: boxDecoration(
//                                                   radius: 10,
//                                                   showShadow: true,
//                                                   bgColor: currentMeasurementPrice?.measurementPrice == itemMes?.measurementPrice ? Colors.green : secondaryTextColor),
//                                               child: Row(
//                                                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                                                 crossAxisAlignment: CrossAxisAlignment.start,
//                                                 children: <Widget>[
//                                                   Container(
//                                                     width: width / 3,
//                                                     alignment: Alignment.centerLeft,
//                                                     child: TextWidget(item.name + '-' + itemMes.measurement,
//                                                         textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
//                                                   ),
//                                                   SizedBox(height: 1),
//                                                   Container(
//                                                     width: width / 4,
//                                                     alignment: Alignment.centerRight,
//                                                     child: itemMes.measurementPrice != "" && itemMes.measurementPrice != null
//                                                         ? TextWidget("\u{20B9}${itemMes.measurementPrice}",
//                                                             textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                                                         : TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                                                   ),
//                                                 ],
//                                               ),
//                                             ),
//                                           );
//                                         }),
//                               )
//                             : Container()
//                       ],
//                     ),
//                     item?.priceType == 2 && productUnitList.isNotEmpty
//                         ? Column(
//                             children: [
//                               SizedBox(
//                                 height: 10,
//                               ),
//                               Container(
//                                 width: double.maxFinite,
//                                 child: ListView.builder(
//                                     itemCount: productUnitList.length,
//                                     shrinkWrap: true,
//                                     physics: NeverScrollableScrollPhysics(),
//                                     itemBuilder: (BuildContext context, int index) {
//                                       var itemMes = productUnitList[index];
//                                       return InkWell(
//                                         onTap: () async {
//                                           String qty = returnMeasurementQty(item, itemMes);
//                                           PriceUnitType unit = measurementValueAssign(itemMes, itemMes?.measurementPrice);
//                                           currentMeasurement = unit;
//                                           if (qty == "0" && currentMeasurement?.measurementPrice != null) {
//                                             qtyController.text = "1";
//                                             await commonIncrementOnTapProduct(item, index, unitMeasurement: currentMeasurement);
//                                             setState(() {
//                                               priceController.clear();
//                                               currentMeasurementPrice = null;
//                                             });
//                                           } else {
//                                             setState(() {
//                                               PriceUnitType unit = measurementValueAssign(itemMes, itemMes?.measurementPrice);
//                                               currentMeasurement = unit;
//                                               qtyController.text = returnMeasurementQty(item, itemMes);
//                                               priceController.clear();
//                                               currentMeasurementPrice = null;
//                                             });
//                                           }
//                                         },
//                                         child: Container(
//                                           margin: const EdgeInsets.all(2.0),
//                                           padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
//                                           decoration: boxDecoration(
//                                               radius: 10, showShadow: true, bgColor: currentMeasurement?.priceTypeId == itemMes?.priceTypeId ? Colors.green : secondaryTextColor),
//                                           child: Row(
//                                             mainAxisAlignment: MainAxisAlignment.spaceAround,
//                                             crossAxisAlignment: CrossAxisAlignment.start,
//                                             children: <Widget>[
//                                               Container(
//                                                 width: width / 3,
//                                                 alignment: Alignment.centerLeft,
//                                                 child: TextWidget(itemMes.measurement,
//                                                     textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
//                                               ),
//                                               SizedBox(height: 1),
//                                               Container(
//                                                 width: width / 4,
//                                                 alignment: Alignment.centerRight,
//                                                 child: itemMes.measurementPrice != "" && itemMes.measurementPrice != null
//                                                     ? TextWidget("\u{20B9}${itemMes.measurementPrice}",
//                                                         textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                                                     : TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
//                                               ),
//                                             ],
//                                           ),
//                                         ),
//                                       );
//                                     }),
//                               ),
//                             ],
//                           )
//                         : Container(),
//                     // productToppingList.isNotEmpty
//                     //      ? toppingWidget(context, item)
//                     //      : Container(),
//                   ],
//                 ),
//               ),
//             ),
//           );
//         });
//       },
//     );
//   }

//   measurementValueAssign(PriceUnitType measurement, String price) {
//     PriceUnitType items = new PriceUnitType();
//     items.productId = measurement.productId;
//     items.measurementPrice = price;
//     items.id = measurement.id;
//     items.measurement = measurement.measurement;
//     items.priceTypeId = measurement.priceTypeId;
//     return items;
//   }

//   productValueAssign(Products product, String price) {
//     Products items = new Products();
//     items.name = product.name;
//     items.price = price;
//     items.productId = product.productId;
//     items.priceType = product.priceType;
//     items.cGst = product.cGst;
//     items.count = product.count;
//     items.dynamicQuantity = product.dynamicQuantity;
//     items.productSaleId = product.productSaleId;
//     items.sGst = product.sGst;
//     items.toppingGroupId = product.toppingGroupId;
//     return items;
//   }

//   getTransactionListWithSameProduct(String productID) {
//     List<Transactions> sameSaleTransactionList = [];
//     sameSaleTransactionList = saleTransactionList.where((element) => element.prodId == productID && element.toppingId == null).toList();
//     return sameSaleTransactionList;
//   }

//   getDynamicAmountList(Products item) async {
//     List<Transactions> sameSaleTransactionList = [];
//     sameSaleTransactionList = await getTransactionListWithSameProduct(item?.productId);
//     productListBasedOnAmount.clear();
//     setState(() {
//       for (Transactions t in sameSaleTransactionList) {
//         Products product = productValueAssign(item, t?.actualPrice);
//         productListBasedOnAmount.add(product);
//       }
//       qtyController.text = "0";
//       priceController.text = "";
//       productListBasedOnAmount.length;
//     });
//   }

//   List<PriceUnitType> measurementAmountList = [];
//   getMeasurementDynamicAmountList(PriceUnitType item, String productName) async {
//     List<Transactions> sameSaleTransactionList = [];
//     sameSaleTransactionList = await getTransactionListWithSameProduct(item?.productId);
//     measurementAmountList.clear();
//     setState(() {
//       for (Transactions t in sameSaleTransactionList) {
//         String pro = "$productName-${currentMeasurement?.measurement}";
//         if ((t?.name == pro) && (currentMeasurement?.measurementPrice == null)) {
//           PriceUnitType unit = measurementValueAssign(item, t?.actualPrice);
//           measurementAmountList.add(unit);
//         }
//       }
//       qtyController.text = "0";
//       priceController.text = "";
//       measurementAmountList.length;
//     });
//   }

//   // Widget amountWidget(BuildContext context, Products item,int index) {
//   //   return StatefulBuilder(builder:
//   //       (BuildContext context, StateSetter setState /*You can rename this!*/) {
//   //   return Column(
//   //     children: [
//   //       SizedBox(
//   //         height: 10,
//   //       ),
//   //       Form(
//   //         key: validator.formkey,
//   //         child: Row(
//   //           mainAxisAlignment: MainAxisAlignment.center,
//   //           children: [
//   //             IconButton(
//   //                 onPressed: () async {
//   //                   currentProduct = null;
//   //                   currentMeasurement = null;
//   //                   currentMeasurementPrice = null;
//   //                 },
//   //                 icon: Icon(
//   //                   Icons.clear,
//   //                   color: Colors.red,
//   //                 )),
//   //             Container(
//   //               width: width / 2,
//   //               child: EditText(
//   //                 mController: priceController,
//   //                 inputType: TextInputType.number,
//   //                 inputFormate: [
//   //                   FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
//   //                 ],
//   //                 isPassword: false,
//   //                 text: "Price",
//   //                 enable: true,
//   //                 validator: validator.validateTextField,
//   //               ),
//   //             ),
//   //             IconButton(
//   //                 onPressed: () async {
//   //   validator.validate();
//   //   if (validator.validate()) {
//   //     if(currentMeasurement != null){
//   //       currentMeasurementPrice = currentMeasurement;
//   //       currentMeasurementPrice.measurementPrice = priceController.text;
//   //     }else{
//   //       item.price = priceController.text;
//   //     }
//   //     await commonIncrementOnTapProduct(
//   //     item, index, unitMeasurement : currentMeasurementPrice);
//   //         if(currentMeasurement != null){
//   //           getMeasurementDynamicAmountList(currentMeasurementPrice, item?.name);
//   //         }
//   //         else{
//   //           getDynamicAmountList(item);
//   //         }
//   //     setState(() {
//   //       qtyController.text = "0";
//   //       priceController.clear();
//   //     });
//   //   }
//   //                 },
//   //                 icon: Icon(
//   //                   Icons.update,
//   //                   color: Colors.green,
//   //                 )),
//   //           ],
//   //         ),
//   //       ),
//   //      SizedBox(
//   //        height: 10,
//   //      ),
//   //      productListBasedOnAmount.isNotEmpty || measurementAmountList.isNotEmpty ?  Container(
//   //        width: double.maxFinite,
//   //        child: productListBasedOnAmount.isNotEmpty
//   //            ? ListView.builder(
//   //            itemCount: productListBasedOnAmount.length,
//   //            shrinkWrap: true,
//   //            physics: NeverScrollableScrollPhysics(),
//   //            itemBuilder: (BuildContext context, int index) {
//   //              var item = productListBasedOnAmount[index];
//   //              return InkWell(
//   //                onTap: () async {
//   //                  // await addProductsInSalesList(item1, item);
//   //
//   //                  setState(() {
//   //                    currentProduct = item;
//   //                    qtyController.text = returnProductQty(item);
//   //                    priceController.text = item.price;
//   //                  });
//   //                  // if (!isUnitLock) {
//   //                  //   isUnitLock = true;
//   //                  //   if ("dynamicQty" == dFlag) {
//   //                  //     Navigator.pop(context);
//   //                  //     await dynamicQtyDialog(
//   //                  //         context,
//   //                  //         item1.toppingGroupId,
//   //                  //         item.measurementPrice,
//   //                  //         item1,
//   //                  //         item);
//   //                  //   } else {
//   //                  //     await addProductsInSalesList(item1, item);
//   //                  // Navigator.pop(context);
//   //                  // if (item1.toppingGroupId != null) {
//   //                  //   await queryAllProductToppingDetails(
//   //                  //       item1.toppingGroupId);
//   //                  //   if (productToppingList.length > 0) {
//   //                  //     toppingDialog(context, item1);
//   //                  //   }
//   //                  // }
//   //                  // }
//   //                  //   isUnitLock = false;
//   //                  // }
//   //                },
//   //                child: Container(
//   //                  //height: 40,
//   //                  //margin: const EdgeInsets.all(2.0),
//   //                  margin: const EdgeInsets.all(2.0),
//   //                  padding:
//   //                  EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
//   //                  decoration: boxDecoration(
//   //                      radius: 10,
//   //                      showShadow: true,
//   //                      bgColor: currentProduct?.price == item?.price
//   //                          ? Colors.green
//   //                          : secondaryTextColor),
//   //                  child: Row(
//   //                    mainAxisAlignment: MainAxisAlignment.spaceAround,
//   //                    crossAxisAlignment: CrossAxisAlignment.start,
//   //                    children: <Widget>[
//   //                      Container(
//   //                        width: width / 3,
//   //                        alignment: Alignment.centerLeft,
//   //                        child: TextWidget(item.name,
//   //                            textColor: primaryTextColor,
//   //                            fontSize: textSizeLargeMedium,
//   //                            fontFamily: fontSemibold,
//   //                            isLongText: true),
//   //                      ),
//   //                      SizedBox(height: 1),
//   //                      Container(
//   //                        width: width / 4,
//   //                        alignment: Alignment.centerRight,
//   //                        child: item.price != "" &&
//   //                            item.price != null
//   //                            ? TextWidget("Rs.${item.price}",
//   //                            textColor: primaryTextColor,
//   //                            fontSize: textSizeLargeMedium,
//   //                            fontFamily: fontSemibold)
//   //                            : TextWidget("",
//   //                            textColor: primaryTextColor,
//   //                            fontSize: textSizeLargeMedium,
//   //                            fontFamily: fontSemibold),
//   //                      ),
//   //                    ],
//   //                  ),
//   //                ),
//   //              );
//   //            })
//   //            : ListView.builder(
//   //         itemCount: measurementAmountList.length,
//   //         shrinkWrap: true,
//   //         physics: NeverScrollableScrollPhysics(),
//   //         itemBuilder: (BuildContext context, int index) {
//   //         var itemMes = measurementAmountList[index];
//   //         return InkWell(
//   //         onTap: () async {
//   //         // await addProductsInSalesList(item1, item);
//   //
//   //         setState(() {
//   //           currentMeasurementPrice = itemMes;
//   //         qtyController.text = returnMeasurementQty(item, itemMes);
//   //         priceController.text = itemMes.measurementPrice;
//   //         currentMeasurement = null;
//   //         });
//   //         // if (!isUnitLock) {
//   //         //   isUnitLock = true;
//   //         //   if ("dynamicQty" == dFlag) {
//   //         //     Navigator.pop(context);
//   //         //     await dynamicQtyDialog(
//   //         //         context,
//   //         //         item1.toppingGroupId,
//   //         //         item.measurementPrice,
//   //         //         item1,
//   //         //         item);
//   //         //   } else {
//   //         //     await addProductsInSalesList(item1, item);
//   //         // Navigator.pop(context);
//   //         // if (item1.toppingGroupId != null) {
//   //         //   await queryAllProductToppingDetails(
//   //         //       item1.toppingGroupId);
//   //         //   if (productToppingList.length > 0) {
//   //         //     toppingDialog(context, item1);
//   //         //   }
//   //         // }
//   //         // }
//   //         //   isUnitLock = false;
//   //         // }
//   //         },
//   //         child: Container(
//   //         //height: 40,
//   //         //margin: const EdgeInsets.all(2.0),
//   //         margin: const EdgeInsets.all(2.0),
//   //         padding:
//   //         EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
//   //         decoration: boxDecoration(
//   //         radius: 10,
//   //         showShadow: true,
//   //         bgColor: currentMeasurementPrice?.measurementPrice == itemMes?.measurementPrice
//   //         ? Colors.green
//   //             : secondaryTextColor),
//   //         child: Row(
//   //         mainAxisAlignment: MainAxisAlignment.spaceAround,
//   //         crossAxisAlignment: CrossAxisAlignment.start,
//   //         children: <Widget>[
//   //         Container(
//   //         width: width / 3,
//   //         alignment: Alignment.centerLeft,
//   //         child: TextWidget(item.name +'-'+ itemMes.measurement,
//   //         textColor: primaryTextColor,
//   //         fontSize: textSizeLargeMedium,
//   //         fontFamily: fontSemibold,
//   //         isLongText: true),
//   //         ),
//   //         SizedBox(height: 1),
//   //         Container(
//   //         width: width / 4,
//   //         alignment: Alignment.centerRight,
//   //         child: itemMes.measurementPrice != "" &&
//   //             itemMes.measurementPrice != null
//   //         ? TextWidget("Rs.${itemMes.measurementPrice}",
//   //         textColor: primaryTextColor,
//   //         fontSize: textSizeLargeMedium,
//   //         fontFamily: fontSemibold)
//   //             : TextWidget("",
//   //         textColor: primaryTextColor,
//   //         fontSize: textSizeLargeMedium,
//   //         fontFamily: fontSemibold),
//   //         ),
//   //         ],
//   //         ),
//   //         ),
//   //         );
//   //         }),
//   //      ) : Container()
//   //     ],
//   //   );
//   // });}

//   // Widget toppingWidget(BuildContext context, Products product) {
//   //   return StatefulBuilder(builder:
//   //       (BuildContext context, StateSetter setState /*You can rename this!*/) {
//   //     return Column(
//   //       children: [
//   //         SizedBox(
//   //           height: 10,
//   //         ),
//   //         Container(
//   //           width: double.maxFinite,
//   //           child: ListView.builder(
//   //               itemCount: productToppingList.length,
//   //               padding: EdgeInsets.zero,
//   //               shrinkWrap: true,
//   //               physics: NeverScrollableScrollPhysics(),
//   //               itemBuilder: (BuildContext context, int index) {
//   //                 var item = productToppingList[index];
//   //                 return Container(
//   //                   margin: const EdgeInsets.all(0),
//   //                   padding: EdgeInsets.zero,
//   //                   decoration: boxDecoration(
//   //                       radius: 10,
//   //                       showShadow: true,
//   //                       bgColor: secondaryTextColor),
//   //                   child: Row(
//   //                     mainAxisAlignment: MainAxisAlignment.spaceAround,
//   //                     crossAxisAlignment: CrossAxisAlignment.start,
//   //                     children: <Widget>[
//   //                       Container(
//   //                         width: width / 1.1,
//   //                         // alignment: Alignment.topLeft,
//   //                         child: new CheckboxListTile(
//   //                             dense: false,
//   //                             controlAffinity: ListTileControlAffinity.leading,
//   //                             contentPadding: EdgeInsets.zero,
//   //                             value: item.isActive,
//   //                             title: Row(
//   //                               mainAxisAlignment:
//   //                                   MainAxisAlignment.spaceBetween,
//   //                               children: [
//   //                                 Container(
//   //                                   width: width / 2.2,
//   //                                   alignment: Alignment.centerLeft,
//   //                                   child: TextWidget(item.toppingName,
//   //                                       textColor: primaryTextColor,
//   //                                       fontSize: textSizeMedium,
//   //                                       fontFamily: fontSemibold,
//   //                                       isLongText: true),
//   //                                 ),
//   //                                 Container(
//   //                                   width: width / 3.5,
//   //                                   alignment: Alignment.centerRight,
//   //                                   child: item.toppingPrice != ""
//   //                                       ? TextWidget("\u{20B9}${item.toppingPrice}",
//   //                                           textColor:
//   //                                               primaryTextColor,
//   //                                           fontSize: textSizeMedium,
//   //                                           fontFamily: fontSemibold)
//   //                                       : TextWidget("",
//   //                                           textColor:
//   //                                               primaryTextColor,
//   //                                           fontSize: textSizeLargeMedium,
//   //                                           fontFamily: fontSemibold),
//   //                                 ),
//   //                               ],
//   //                             ),
//   //                             onChanged: (bool newValue) {
//   //                               setState(() {
//   //                                 // item.isActive = newValue;
//   //                                 // if (item.isActive == false) {
//   //                                 //   addProductsInSalesList1(product, item,
//   //                                 //       isRemove: true);
//   //                                 // } else {
//   //                                 //   addProductsInSalesList1(product, item);
//   //                                 // }
//   //                               });
//   //                             }),
//   //                       ),
//   //                     ],
//   //                   ),
//   //                 );
//   //               }),
//   //         ),
//   //       ],
//   //     );
//   //   });
//   // }

//   returnMeasurementQty(Products product, PriceUnitType unit) {
//     String qty = saleTransactionList
//         .firstWhere((element) => element.prodId == product?.productId && element.actualPrice == unit?.measurementPrice && element.name == product.name + "-" + unit?.measurement,
//             orElse: () => null)
//         ?.qty;
//     if (qty.isEmptyOrNull) {
//       return "0";
//     } else {
//       return fnUtilities.checkQtyDoubleValue(qty.toString());
//     }
//   }

//   returnProductQty(Products product) {
//     String qty = saleTransactionList.firstWhere((element) => element.prodId == product?.productId && element.actualPrice == product?.price, orElse: () => null)?.qty;
//     if (qty.isEmptyOrNull) {
//       return "0";
//     } else {
//       return fnUtilities.checkQtyDoubleValue(qty.toString());
//     }
//   }

//   PriceUnitType currentMeasurement;
//   PriceUnitType currentMeasurementPrice;
//   Products currentProduct;

//   Widget gridProdItem(Products item, int index, BuildContext gridContext) {
//     //double cardHight = MediaQuery.of(gridContext).size.height;
//     return Container(
//       margin: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 2.0),
//       decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
//       padding: const EdgeInsets.symmetric(horizontal: 1.0, vertical: 1.0),
//       child: item?.productId != null
//           ? InkWell(
//               onTap: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   if (item?.priceType != null) {
//                     String qty = saleTransactionList.firstWhere((element) => element.prodId == item.productId, orElse: () => null)?.qty;
//                     if (qty.isEmptyOrNull) {
//                       if (item.priceType == 1) {
//                         await addProductsInSalesList(item, null, isReduce: false);
//                       }
//                       setState(() {
//                         qtyController.text = item.priceType == 1 ? "1" : "0";
//                         productCountIncrement(item.productId);
//                       });
//                     } else {
//                       setState(() {
//                         qtyController.text = fnUtilities.checkQtyDoubleValue(qty.toString());
//                       });
//                     }

//                     await productBottomSheet(item, index);
//                     if (MyApp.isFilterEnable) {
//                       await clearFunction();
//                     }
//                   } else {
//                     showToast("Please update this product");
//                   }
//                   isLock = false;
//                 }
//               },
//               child: Column(
//                 children: [
//                   Container(
//                     height: panels != "Id"
//                         ? 100
//                         : item.count != null && item.count > 0
//                             ? 50
//                             : 0,
//                     child: Stack(children: [
//                       panels != "Id"
//                           ? Container(
//                               padding: EdgeInsets.only(left: 0, right: 0),
//                               decoration: item.imagePath == null
//                                   ? boxDecoration(radius: 0.0, bgColor: secondaryTextColor)
//                                   : BoxDecoration(
//                                       borderRadius: BorderRadius.all(Radius.circular(0.0)), image: DecorationImage(image: FileImage(item.imagePath), fit: BoxFit.cover)),
//                               alignment: Alignment.topCenter,
//                               child: item.price == "" || item.price == null
//                                   ? TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                                   : textWithStroke("Rs.${item.price}", textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: true),
//                             )
//                           : Container(),
//                       item.count != null && item.count > 0
//                           ? Container(
//                               width: width,
//                               height: height / 2,
//                               decoration: BoxDecoration(color: Colors.black12, borderRadius: BorderRadius.all(Radius.circular(0.0))),
//                               alignment: Alignment.center,
//                               child: TextWidget(fnUtilities.checkQtyDoubleValue(item.count.toString()),
//                                   textColor: whiteColor, fontSize: textSizeLarge, fontFamily: fontSemibold, isCentered: true),
//                             )
//                           : Container()
//                     ]),
//                   ),
//                   Column(
//                     mainAxisAlignment: item.imagePath == null ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: <Widget>[
//                       panels == "Id"
//                           ? Container(
//                               width: width,
//                               padding: EdgeInsets.only(right: 5),
//                               alignment: Alignment.center,
//                               child: TextWidget(item.productSaleId.toString(), textColor: primaryTextColor, fontSize: textSizeNormal, fontFamily: fontBold, isCentered: true),
//                             )
//                           : Container(),
//                       SizedBox(height: 2),
//                       Column(
//                         children: [
//                           Container(
//                               height: height / 18,
//                               child: TextWidget(item.name, textColor: primaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)),
//                           SizedBox(height: 2),
//                           if (panels == "Id")
//                             item.price == "" || item.price == null
//                                 ? TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
//                                 : TextWidget("Rs.${item.price}", textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: true),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ],
//               ),
//             )
//           : InkWell(
//               onTap: () async {
//                 if (!isLock) {
//                   isLock = true;
//                   Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog(productName: searchController.text))).then((value) async {
//                     await clearFunction();
//                     await queryAllProductDetails();
//                     await loopForProductQty(saleTransactionList);
//                     await findTotal();
//                   });
//                   isLock = false;
//                 }
//               },
//               child: Stack(children: [
//                 Container(
//                   padding: EdgeInsets.only(left: 0, right: 0),
//                   decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.center,
//                     crossAxisAlignment: CrossAxisAlignment.center,
//                     children: <Widget>[
//                       Container(
//                         width: width,
//                         padding: EdgeInsets.all(5),
//                         child: Icon(Icons.add),
//                       ),
//                       SizedBox(height: 2),
//                       textWithStroke("New Product", textColor: primaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)
//                     ],
//                   ),
//                 ),
//               ]),
//             ),
//     );
//   }

//   //NORMAL PANEL TYPE WIDGET
//   normalPanelType() {
//     return MyApp.isListViewEnable == true
//         ? Expanded(
//             child: Container(
//               width: width,
//               alignment: Alignment.topCenter,
//               child: ListView.builder(
//                   padding: EdgeInsets.only(top: 5.0, bottom: 20),
//                   scrollDirection: Axis.vertical,
//                   physics: ScrollPhysics(),
//                   itemCount: filterProductList.length,
//                   shrinkWrap: true,
//                   itemBuilder: (BuildContext context, int index) {
//                     var item = filterProductList[index];
//                     return listProdItem(item, index);
//                   }),
//             ),
//           )
//         : Expanded(
//             child: Container(
//               child: GridView.builder(
//                   padding: EdgeInsets.only(bottom: 15),
//                   scrollDirection: Axis.vertical,
//                   physics: ScrollPhysics(),
//                   itemCount: filterProductList.length,
//                   shrinkWrap: true,
//                   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                       crossAxisCount: fnUtilities.getProductsGridCount(width), crossAxisSpacing: 2, mainAxisSpacing: 2, childAspectRatio: 0.8),
//                   itemBuilder: (BuildContext context, int index) {
//                     var item = filterProductList[index];
//                     return gridProdItem(item, index, context);
//                   }),
//             ),
//           );
//   }

//   //NUMBER PANEL TYPE WIDGET
//   filterPanelType() {
//     return MyApp.isListViewEnable == true
//         ? Expanded(
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 3.0),
//               child: Column(
//                 mainAxisAlignment: MainAxisAlignment.start,
//                 crossAxisAlignment: CrossAxisAlignment.center,
//                 children: [
//                   Container(
//                     width: width,
//                     child: Column(
//                       children: [
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                           children: [
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("1");
//                                 },
//                                 child: Text("1"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("2");
//                                 },
//                                 child: Text("2"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("3");
//                                 },
//                                 child: Text("3"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("4");
//                                 },
//                                 child: Text("4"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("5");
//                                 },
//                                 child: Text("5"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                           children: [
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("6");
//                                 },
//                                 child: Text("6"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("7");
//                                 },
//                                 child: Text("7"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("8");
//                                 },
//                                 child: Text("8"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("9");
//                                 },
//                                 child: Text("9"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                             Container(
//                               width: width / 8,
//                               // ignore: deprecated_member_use
//                               child: OutlinedButton(
//                                 onPressed: () {
//                                   setProdId("0");
//                                 },
//                                 child: Text("0"),
//                                 style: OutlinedButton.styleFrom(
//                                   primary: buttonThemeColor, // Sets the button's text color
//                                   shape: RoundedRectangleBorder(
//                                     borderRadius: BorderRadius.circular(5.0),
//                                   ),
//                                 ),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                   Expanded(
//                     child: ListView.builder(
//                         itemCount: filterProductList.length,
//                         scrollDirection: Axis.vertical,
//                         physics: ScrollPhysics(),
//                         shrinkWrap: true,
//                         padding: EdgeInsets.only(top: 5.0),
//                         itemBuilder: (BuildContext context, int index) {
//                           var item = filterProductList[index];
//                           return listProdItem(item, index);
//                         }),
//                   ),
//                 ],
//               ),
//             ),
//           )
//         : Expanded(
//             child: Padding(
//               padding: EdgeInsets.symmetric(horizontal: 3.0),
//               child: Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceAround,
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   Container(
//                     width: width / 3,
//                     height: height / 2.3,
//                     child: SingleChildScrollView(
//                       physics: ScrollPhysics(),
//                       child: Column(
//                         children: [
//                           Container(
//                             child: Column(
//                               children: [
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: 70,
//                                       child: TextWidget("$prodId", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLargeMedium, isCentered: true),
//                                     ),
//                                     prodId.isEmptyOrNull
//                                         ? Container()
//                                         : Container(
//                                             width: width / 8,
//                                             child: IconButton(
//                                               icon: Icon(Icons.close),
//                                               onPressed: () async {
//                                                 if (!isLock) {
//                                                   isLock = true;
//                                                   await clearFunction();
//                                                   isLock = false;
//                                                 }
//                                               },
//                                             ),
//                                           ),
//                                   ],
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("1");
//                                         },
//                                         child: Text("1"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("2");
//                                         },
//                                         child: Text("2"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("3");
//                                         },
//                                         child: Text("3"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("4");
//                                         },
//                                         child: Text("4"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("5");
//                                         },
//                                         child: Text("5"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("6");
//                                         },
//                                         child: Text("6"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("7");
//                                         },
//                                         child: Text("7"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("8");
//                                         },
//                                         child: Text("8"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                                 Row(
//                                   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
//                                   children: [
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("9");
//                                         },
//                                         child: Text("9"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                     Container(
//                                       width: width / 8,
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdId("0");
//                                         },
//                                         child: Text("0"),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ),
//                   ),
//                   Expanded(
//                     child: Padding(
//                       padding: EdgeInsets.only(left: 8.0),
//                       child: Container(
//                         child: (filterProductList.isEmpty)
//                             ? Center(child: Text("Products not available"))
//                             : GridView.builder(
//                                 padding: EdgeInsets.zero,
//                                 scrollDirection: Axis.vertical,
//                                 physics: ScrollPhysics(),
//                                 itemCount: filterProductList.length,
//                                 shrinkWrap: true,
//                                 gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                                     crossAxisCount: fnUtilities.getProductsGridCount(width, sideCardWidth: width / 3),
//                                     crossAxisSpacing: 2,
//                                     mainAxisSpacing: 2,
//                                     childAspectRatio: 0.7),
//                                 itemBuilder: (BuildContext context, int index) {
//                                   var item = filterProductList[index];
//                                   return gridProdItem(item, index, context);
//                                 }),
//                       ),
//                     ),
//                   ),
//                 ],
//               ),
//             ),
//           );
//   }

//   //NAME PANEL TYPE WIDGET
//   filterNamePanelType() {
//     return MyApp.isListViewEnable == true
//         ? Expanded(
//             child: SingleChildScrollView(
//             child: Column(
//               children: [
//                 Padding(
//                   padding: const EdgeInsets.symmetric(horizontal: 3.0),
//                   child: Column(
//                     mainAxisAlignment: MainAxisAlignment.spaceAround,
//                     mainAxisSize: MainAxisSize.max,
//                     children: [
//                       Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         children: [
//                           Container(
//                             padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
//                             width: width / 1.1,
//                             child: TextField(
//                               controller: searchController,
//                               onChanged: filterProductsByName,
//                               style: TextStyle(color: Colors.black),
//                               decoration: InputDecoration(
//                                 labelText: 'Search',
//                               ),
//                             ),
//                           ),
//                         ],
//                       ),
//                       SizedBox(
//                         height: 20,
//                       ),
//                       Row(
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Container(
//                             width: width / 1.1,
//                             child: Column(
//                               children: [
//                                 Container(
//                                   child: ListView.builder(
//                                       padding: EdgeInsets.only(top: 5.0, bottom: 15),
//                                       itemCount: filterProductList.length,
//                                       scrollDirection: Axis.vertical,
//                                       physics: ScrollPhysics(),
//                                       shrinkWrap: true,
//                                       // padding: EdgeInsets.only(top: 5.0),
//                                       itemBuilder: (BuildContext context, int index) {
//                                         var item = filterProductList[index];
//                                         return listProdItem(item, index);
//                                       }),
//                                 ),
//                               ],
//                             ),
//                           ),
//                         ],
//                       ),
//                     ],
//                   ),
//                 ),
//               ],
//             ),
//           ))
//         : Expanded(
//             child: SingleChildScrollView(
//             child: Column(
//               children: [
//                 Column(
//                   mainAxisAlignment: MainAxisAlignment.spaceAround,
//                   mainAxisSize: MainAxisSize.max,
//                   children: [
//                     Row(
//                       mainAxisAlignment: MainAxisAlignment.center,
//                       children: [
//                         Container(
//                           padding: const EdgeInsets.fromLTRB(20, 0, 20, 0),
//                           width: width / 1.1,
//                           child: TextField(
//                             controller: searchController,
//                             onChanged: filterProductsByName,
//                             style: TextStyle(color: Colors.black),
//                             decoration: InputDecoration(
//                               labelText: 'Search',
//                             ),
//                           ),
//                         ),
//                       ],
//                     ),
//                     SizedBox(
//                       height: 20,
//                     ),
//                     Container(
//                       height: height / 1.1,
//                       child: (filterProductList.isEmpty)
//                           ? Center(child: Text("Products not available"))
//                           : Column(
//                               children: [
//                                 Expanded(
//                                   child: GridView.builder(
//                                       padding: EdgeInsets.only(bottom: 15),
//                                       scrollDirection: Axis.vertical,
//                                       physics: ScrollPhysics(),
//                                       itemCount: filterProductList.length,
//                                       shrinkWrap: true,
//                                       gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                                           crossAxisCount: fnUtilities.getProductsGridCount(width), crossAxisSpacing: 2, mainAxisSpacing: 2, childAspectRatio: 0.8),
//                                       itemBuilder: (BuildContext context, int index) {
//                                         var item = filterProductList[index];
//                                         return gridProdItem(item, index, context);
//                                       }),
//                                 ),
//                               ],
//                             ),
//                     ),
//                   ],
//                 ),
//               ],
//             ),
//           ));
//   }

//   //Key PANEL TYPE WIDGET
//   filterKeyPanelType() {
//     return productKeyList.length > 0
//         ? MyApp.isListViewEnable == true
//             ? Expanded(
//                 child: SingleChildScrollView(
//                   child: Column(
//                     children: [
//                       Padding(
//                         padding: EdgeInsets.all(3.0),
//                         child: Column(
//                           mainAxisAlignment: MainAxisAlignment.start,
//                           crossAxisAlignment: CrossAxisAlignment.center,
//                           children: [
//                             SizedBox(
//                               height: 80,
//                               width: width,
//                               child: GridView.builder(
//                                   padding: EdgeInsets.symmetric(horizontal: 15),
//                                   scrollDirection: Axis.horizontal,
//                                   physics: ScrollPhysics(),
//                                   itemCount: productKeyList.length,
//                                   shrinkWrap: true,
//                                   gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(crossAxisCount: 2, crossAxisSpacing: 10, mainAxisSpacing: 16, childAspectRatio: 0.3),
//                                   itemBuilder: (BuildContext context, int index) {
//                                     var item = productKeyList[index];
//                                     return Container(
//                                       width: width,
//                                       // ignore: deprecated_member_use
//                                       child: OutlinedButton(
//                                         onPressed: () {
//                                           setProdKey(item.productKeyId, item.keywordName);
//                                           prodKey = item.productKeyId;
//                                         },
//                                         child: Text(item.keywordName),
//                                         style: OutlinedButton.styleFrom(
//                                           primary: buttonThemeColor, // Sets the button's text color
//                                           shape: RoundedRectangleBorder(
//                                             borderRadius: BorderRadius.circular(5.0),
//                                           ),
//                                         ),
//                                       ),
//                                     );
//                                   }),
//                             ),
//                             Container(
//                               height: height / 1.9,
//                               width: width / 1.1,
//                               child: Column(
//                                 children: [
//                                   Expanded(
//                                     child: ListView.builder(
//                                         itemCount: filterProductList.length,
//                                         scrollDirection: Axis.vertical,
//                                         physics: ScrollPhysics(),
//                                         shrinkWrap: true,
//                                         padding: EdgeInsets.only(top: 5.0),
//                                         itemBuilder: (BuildContext context, int index) {
//                                           var item = filterProductList[index];
//                                           return listProdItem(item, index);
//                                         }),
//                                   ),
//                                 ],
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                       // SizedBox(height: 20),
//                     ],
//                   ),
//                 ),
//               )
//             : Expanded(
//                 child: Padding(
//                   padding: EdgeInsets.symmetric(horizontal: 3.0),
//                   child: Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceAround,
//                     crossAxisAlignment: CrossAxisAlignment.start,
//                     children: [
//                       Container(
//                         width: width / 3.3,
//                         height: height / 1.4,
//                         child: SingleChildScrollView(
//                           child: Column(
//                             mainAxisAlignment: MainAxisAlignment.start,
//                             children: [
//                               SizedBox(
//                                 height: height / 1.4,
//                                 child: GridView.builder(
//                                     padding: EdgeInsets.zero,
//                                     scrollDirection: Axis.vertical,
//                                     physics: ScrollPhysics(),
//                                     itemCount: productKeyList.length,
//                                     shrinkWrap: true,
//                                     gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                                         crossAxisCount: fnUtilities.getProductsGridCount(width / 3.3), crossAxisSpacing: 4, mainAxisSpacing: 13, childAspectRatio: 3),
//                                     itemBuilder: (BuildContext context, int index) {
//                                       var item = productKeyList[index];
//                                       return Container(
//                                         width: width / 3.3,
//                                         child: OutlinedButton(
//                                           onPressed: () {
//                                             setProdKey(item.productKeyId, item.keywordName);
//                                             prodKey = item.productKeyId;
//                                           },
//                                           child: Text(item.keywordName),
//                                           style: OutlinedButton.styleFrom(
//                                             primary: buttonThemeColor, // Sets the button's text color
//                                             shape: RoundedRectangleBorder(
//                                               borderRadius: BorderRadius.circular(5.0),
//                                             ),
//                                           ),
//                                         ),
//                                       );
//                                     }),
//                               ),
//                             ],
//                           ),
//                         ),
//                       ),
//                       Expanded(
//                         child: GridView.builder(
//                             padding: EdgeInsets.zero,
//                             scrollDirection: Axis.vertical,
//                             physics: ScrollPhysics(),
//                             itemCount: filterProductList.length,
//                             shrinkWrap: true,
//                             gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
//                                 crossAxisCount: fnUtilities.getProductsGridCount(width, sideCardWidth: width / 3.3),
//                                 crossAxisSpacing: 2,
//                                 mainAxisSpacing: 2,
//                                 childAspectRatio: 0.8),
//                             itemBuilder: (BuildContext context, int index) {
//                               var item = filterProductList[index];
//                               return gridProdItem(item, index, context);
//                             }),
//                       ),
//                     ],
//                   ),
//                 ),
//               )
//         : Container(
//             height: height / 3,
//             child: Center(child: Text("Categories are not available")),
//           );
//   }

//   callSuccessDialog(BuildContext context, salesIdForReport) async {
//     //if (sales?.salesId == null) {
//     await fnUtilities.salesSuccessDialog(context, salesIdForReport);
//     // } else {
//     //   await fnUtilities.salesSuccessDialog(context, sales?.salesId);
//     //}
//   }
// }
