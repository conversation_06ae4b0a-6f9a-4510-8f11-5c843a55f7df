// // ignore_for_file: sdk_version_ui_as_code, unused_local_variable, non_constant_identifier_names

// import 'dart:io';
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:image_cropper/image_cropper.dart';
// import 'package:image_picker/image_picker.dart';
// import 'package:nb_utils/nb_utils.dart';
// import 'package:path_provider/path_provider.dart';
// import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
// import 'package:smartbill/utils/common_function/fn_utilities.dart';
// import 'package:smartbill/model/products.dart';
// import 'package:smartbill/model/stocks.dart';
// import 'package:smartbill/model/toppings_model.dart';
// import 'package:smartbill/screens/view_product_category.dart';
// import 'package:smartbill/utils/constants/colors.dart';
// import 'package:uuid/uuid.dart';
// import 'package:smartbill/utils/common_function/validation_utility.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import 'package:smartbill/utils/constant/colors.dart';
// import 'package:smartbill/utils/constant/constant.dart';
// 
// 
// import 'package:smartbill/utils/dbutils/database_helper.dart';
// import '../../main.dart';
// import '../header_footer.dart';
// import '../toppings_list.dart';

// //ADD PRODUCT DIALOG CLASS
// // ignore: must_be_immutable
// class AddProductDialog extends StatefulWidget {
//   String id;
//   String productName;
//   AddProductDialog({this.id, this.productName});
//   @override
//   _AddProductDialogState createState() =>
//       _AddProductDialogState(id, productName);
// }

// class _AddProductDialogState extends State<AddProductDialog> {
//   String id;
//   String productName;
//   _AddProductDialogState(this.id, this.productName);

//   final productSaleIdController = TextEditingController();
//   final productController = TextEditingController();
//   final priceController = TextEditingController();
//   final mrpController = TextEditingController();
//   final gstController = TextEditingController();
//   final unitController = TextEditingController();
//   final unitPriceController = TextEditingController();
//   final stocksController = TextEditingController();
//   bool isMeasurementIsEmpty = false;
//   final dbHelper = DatabaseHelper.instance;
//   Validator validator = new Validator();
//   FocusNode measure = FocusNode();
//   FocusNode product = new FocusNode();
//   bool taxupdate = false;
//   bool measurementUpdate = false;
//   bool dynamicQuantity = false;
//   bool isUpdate = false;
//   bool isLock = false;
//   ProductKey selectKeyword;
//   ToppingsGroup selectGroup;
//   String imageId = '';
//   int unitIndex;
//   List<ProductKey> productKeyList = [];
//   List<ToppingsGroup> toppingsGroupList = [];
//   List<PriceUnitType> productUnitList = [];
//   var width;
//   var height = 100.0;
//   bool showDynamicQty = false;
//   bool showMeasurement = false;
//   bool showToppings = false;
//   FnUtilities fnUtilities = new FnUtilities();
//   DBOperations dbUtils = new DBOperations();
//   bool showTax = false;
//   bool showInventory = false;
//   String inventoryType;
//   bool enableInventory = false;
//   bool showParcel = false;
//   bool parcelupdate = false;
//   final parcelController = TextEditingController();

//   //INSERT PRODUCT DETAILS
//   insert(
//       String prodId,
//       String name,
//       String price,
//       String imageId,
//       String sgst,
//       String cgst,
//       String mrp,
//       String key,
//       String groupId,
//       int prodSaleId,
//       int priceType,
//       int dynamicQty,
//       bool enableInventory,
//       String keyID,
//       String parcelAmount) async {
//     Products products = Products(
//         productId: prodId,
//         productSaleId: prodSaleId,
//         name: name,
//         price: price,
//         mrp: mrp,
//         productKeyName: key,
//         toppingGroupId: groupId,
//         priceType: priceType,
//         dynamicQuantity: dynamicQty,
//         enableInventory: enableInventory ? 1 : 0,
//         cGst: cgst,
//         sGst: sgst,
//         image: imageId,
//         workspaceId: MyApp.activeWorkspace.workspaceId,
//         sync: 0,
//         rowStatus: 0,
//         productDate: DateTime.now().toString(),
//         keyID: keyID,
//         parcelAmount: parcelAmount);
//     await dbHelper.insert4(products);
//     if (stocksController.text != "" && enableInventory) {
//       ModifyStocksDto model = new ModifyStocksDto();
//       model.productId = prodId;
//       model.stocksQty = stocksController.text;
//       model.stocksSold = "0";
//       model.modifiedStocks = stocksController.text;
//       await dbUtils.modifyStocks(model);
//     }
//   }

//   //GET PRODUCT DETAILS FROM DB
//   void _queryAllProductDeatails() async {
//     final allRows = await dbHelper.queryallRowsProductDeatails();
//     setState(() {
//       ListUtility.productList.clear();
//       allRows
//           .forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
//     });
//   }

//   //GET PRODUCT UNIT DETAILS FROM DB
//   void queryAllProductUnitsDetails(String productId) async {
//     final allRows = await dbHelper.queryGetAllForUnit(productId);
//     setState(() {
//       productUnitList.clear();
//       allRows.forEach((row) => productUnitList.add(PriceUnitType.fromMap(row)));
//     });
//   }

//   //CLEAR PRODUCT TEXT FILED FUNCTION
//   clearTextField() {
//     Future.delayed(Duration(milliseconds: 50), () {
//       productController.clear();
//       priceController.clear();
//       gstController.clear();
//       parcelController.clear();
//       mrpController.clear();
//       stocksController.clear();
//       unitController.clear();
//       unitPriceController.clear();
//     });
//   }

//   //SET PRODUCT TEXT FILED VALUE
//   setTextFieldValues() async {
//     // ignore: await_only_futures
//     await queryAllProductKeys();
//     // ignore: await_only_futures
//     await queryAllProductToppings();
//     for (Products p in ListUtility.productList) {
//       if (p.productId == id) {
//         // ignore: await_only_futures
//         await queryAllProductUnitsDetails(p.productId);
//         setState(() {
//           productSaleIdController.text = p.productSaleId.toString();
//           productController.text = p.name;
//           priceController.text = p.price;
//           if (p.productKeyName == null && p.keyID == null) {
//             selectKeyword = productKeyList.first;
//           } else {
//             ProductKey selectKey;
//             if (p.productKeyName != null && p.keyID == null) {
//               selectKey = productKeyList.singleWhere(
//                   (item) => item.keywordName == p.productKeyName,
//                   orElse: () => null);
//             } else if (p.keyID != null) {
//               selectKey = productKeyList.singleWhere(
//                   (item) => item.productKeyId == p.keyID,
//                   orElse: () => null);
//             }

//             if (selectKey == null) {
//               selectKeyword = productKeyList.first;
//             } else {
//               selectKeyword = selectKey;
//             }
//           }
//           if (p.toppingGroupId == null) {
//             selectGroup = toppingsGroupList.first;
//           } else {
//             ToppingsGroup group;
//             group = toppingsGroupList.singleWhere(
//                 (item) => item.toppingGroupId == p.toppingGroupId,
//                 orElse: () => null);
//             if (group == null) {
//               selectGroup = toppingsGroupList.first;
//             } else {
//               selectGroup = toppingsGroupList.singleWhere(
//                   (item) => item.toppingGroupId == p.toppingGroupId);
//             }
//           }
//           if (p.priceType == 2) {
//             if (productUnitList.length != 0) {
//               measurementUpdate = true;
//             }
//           }
//           if (p.dynamicQuantity == 1) {
//             dynamicQuantity = true;
//           }

//           if (showTax) {
//             if (p.sGst != null || p.cGst != null) {
//               taxupdate = true;
//               gstController.text =
//                   (double.parse(p.sGst) + double.parse(p.cGst)).toString();
//               // cgstController.text = p.cGst;
//             }
//           }
//           mrpController.text = p.mrp;
//           parcelController.text = p.parcelAmount;
//           if (p.enableInventory == 1) {
//             enableInventory = true;
//           }
//         });
//         if (!p.image.isEmptyOrNull) {
//           imageId = p.image;
//           var filepath = await fnUtilities.readFileFromLocal(imageId);
//           setState(() {
//             imageFile = filepath;
//           });
//         }
//       }
//     }
//   }

//   //UPDATE PRODUCT DETAILS
//   update(
//       String id,
//       String name,
//       String price,
//       String imageId,
//       String sgst,
//       String cgst,
//       String mrp,
//       String key,
//       String groupId,
//       int prodSaleId,
//       int priceType,
//       int dynamicQty,
//       bool enableInventory,
//       String keyID,
//       String parcelAmount) async {
//     final syncNo = await dbHelper.queryGetProductSyncNo(id);
//     Products products = Products(
//         productId: id,
//         productSaleId: prodSaleId,
//         name: name,
//         price: price,
//         mrp: mrp,
//         productKeyName: key,
//         toppingGroupId: groupId,
//         priceType: priceType,
//         dynamicQuantity: dynamicQty,
//         enableInventory: enableInventory ? 1 : 0,
//         cGst: cgst,
//         sGst: sgst,
//         image: imageId,
//         sync: 0,
//         rowStatus: syncNo == 0 ? 0 : 1,
//         productDate: DateTime.now().toString(),
//         keyID: keyID,
//         parcelAmount: parcelAmount);
//     await dbHelper.updateProductsTable(products);
//     setState(() {
//       _queryAllProductDeatails();
//     });
//   }

//   //SET PRODUCT SALES ID
//   setProductSaleId() async {
//     int id = await dbHelper.queryGetMaxProductSaleId();
//     setState(() {
//       productSaleIdController.text = (id + 1).toString();
//     });
//   }

//   //GET PRODUCT DETAILS BY Key
//   void queryAllProductKeys() async {
//     final allRows = await dbHelper.queryallRowsProductKeyTable();
//     setState(() {
//       productKeyList.clear();
//       ProductKey model = ProductKey();
//       model.keywordName = "None";
//       productKeyList.add(model);
//       allRows.forEach((row) => productKeyList.add(ProductKey.fromMap(row)));
//       if (productKeyList.length != 0 && id == null) {
//         selectKeyword = productKeyList.first;
//       }
//     });
//   }

//   //GET PRODUCT DETAILS BY Key
//   void queryAllProductToppings() async {
//     final allRows = await dbHelper.queryallRowsToppingsDetails();
//     setState(() {
//       toppingsGroupList.clear();
//       ToppingsGroup model = ToppingsGroup();
//       model.toppingGroupName = "None";
//       toppingsGroupList.add(model);
//       allRows
//           .forEach((row) => toppingsGroupList.add(ToppingsGroup.fromMap(row)));
//       if (toppingsGroupList.length != 0 && id == null) {
//         selectGroup = toppingsGroupList.first;
//       }
//     });
//   }

// //GET PRODUCT SETTINGS FROM DB
//   initSettings() async {
//     var ShowMeasurementValue =
//         await fnUtilities.getWorkspaceSetValue("ShowMeasurement");
//     var ShowDynamicQtyValue =
//         await fnUtilities.getWorkspaceSetValue("ShowDynamicQty");
//     var ShowToppingsValue =
//         await fnUtilities.getWorkspaceSetValue("ShowToppings");
//     var TaxEnableValue = await fnUtilities.getWorkspaceSetValue("TaxEnable");
//     var EnableInventoryValue =
//         await fnUtilities.getWorkspaceSetValue("EnableInventory");
//     var InventoryTypeValue =
//         await fnUtilities.getWorkspaceSetValue("InventoryType");
//     var ApplyTaxForValue =
//         await fnUtilities.getWorkspaceSetValue("ApplyTaxFor");

//     /// Parcel Settings
//     var ParcelEnableValue =
//         await fnUtilities.getWorkspaceSetValue("ParcelEnable");
//     var ApplyParcelForValue =
//         await fnUtilities.getWorkspaceSetValue("ApplyParcelFor");
//     setState(() {
//       if (ShowMeasurementValue == "1") {
//         showMeasurement = true;
//       } else {
//         showMeasurement = false;
//       }
//       if (ShowDynamicQtyValue == "1") {
//         showDynamicQty = true;
//       } else {
//         showDynamicQty = false;
//       }
//       if (ShowToppingsValue == "1") {
//         showToppings = true;
//       } else {
//         showToppings = false;
//       }
//       if ((TaxEnableValue == "1") &&
//           (ApplyTaxForValue == null || ApplyTaxForValue == 'Specific')) {
//         showTax = true;
//       } else {
//         showTax = false;
//       }

//       /// set parcel settings
//       if ((ParcelEnableValue == "1") &&
//           (ApplyParcelForValue == null || ApplyParcelForValue == 'Specific')) {
//         showParcel = true;
//       } else {
//         showParcel = false;
//       }
//       if (EnableInventoryValue == "1") {
//         showInventory = true;
//       } else {
//         showInventory = false;
//       }
//       if (InventoryTypeValue == "All") {
//         inventoryType = "All";
//         enableInventory = true;
//       } else {
//         inventoryType = "Specific";
//       }
//     });
//   }

//   initPageDetails() async {
//     await initSettings();
//     await queryAllProductKeys();
//     await queryAllProductToppings();
//     if (id != null) {
//       setTextFieldValues();
//     } else {
//       if (productName != null) {
//         productController.text = productName;
//       }
//       isUpdate = true;
//       setProductSaleId();
//     }
//   }

//   @override
//   void initState() {
//     initPageDetails();
//     super.initState();
//   }

//   onSaveProduct(String press) async {
//     if (!isLock) {
//       isLock = true;
//       var uuid = new Uuid();
//       String prodId = uuid.v4().toString();
//       String name = productController.text;
//       String price = priceController.text;
//       String mrp = mrpController.text;
//       int dynamicQty = 0;
//       if (dynamicQuantity == true) {
//         dynamicQty = 1;
//       }
//       String sgst;
//       String cgst;
//       if (gstController.text != '') {
//         sgst = (double.parse(gstController.text) / 2).toString();
//         cgst = (double.parse(gstController.text) / 2).toString();
//       } else {
//         sgst = null;
//         cgst = null;
//       }
//       // if (gstController.text != '') {
//       //   cgst = cgstController.text;
//       // } else {
//       //   cgst = null;
//       // }
//       int prodSaleId = int.parse(productSaleIdController.text);
//       String key;
//       String keyID;
//       if (selectKeyword.keywordName != "None") {
//         key = selectKeyword.keywordName;
//         keyID = selectKeyword.productKeyId;
//       } else {
//         key = null;
//         keyID = null;
//       }
//       String groupId;
//       if (selectGroup.toppingGroupName != "None") {
//         groupId = selectGroup.toppingGroupId;
//       } else {
//         groupId = null;
//       }
//       int priceType;
//       if (priceController.text == '' && !measurementUpdate) {
//         priceType = 0;
//         if (productUnitList.length > 0) {
//           for (PriceUnitType obj in productUnitList) {
//             if (obj.priceTypeId != null) {
//               PriceUnitType model = PriceUnitType();
//               model.priceTypeId = obj.priceTypeId;
//               model.measurement = obj.measurement;
//               model.measurementPrice = obj.measurementPrice;
//               model.workspaceId = MyApp.activeWorkspace.workspaceId;
//               model.rowStatus = 2;
//               model.measurementSync = 0;
//               await dbHelper.updateUnitTable(model);
//             }
//           }
//         }
//       } else if (priceController.text != '' && !measurementUpdate) {
//         priceType = 1;
//         if (productUnitList.length > 0) {
//           for (PriceUnitType obj in productUnitList) {
//             if (obj.priceTypeId != null) {
//               PriceUnitType model = PriceUnitType();
//               model.priceTypeId = obj.priceTypeId;
//               model.measurement = obj.measurement;
//               model.measurementPrice = obj.measurementPrice;
//               model.workspaceId = MyApp.activeWorkspace.workspaceId;
//               model.rowStatus = 2;
//               model.measurementSync = 0;
//               await dbHelper.updateUnitTable(model);
//             }
//           }
//         }
//       } else if (productUnitList.length > 0 && measurementUpdate) {
//         priceType = 2;
//         price = null;
//         mrp = null;
//       }
//       if (priceType == 2) {
//         PriceUnitType model = PriceUnitType();
//         for (PriceUnitType obj in productUnitList) {
//           if (obj.priceTypeId != null) {
//             model.priceTypeId = obj.priceTypeId;
//             model.measurement = obj.measurement;
//             model.measurementPrice = obj.measurementPrice;
//             model.workspaceId = MyApp.activeWorkspace.workspaceId;
//             model.rowStatus = obj.rowStatus;
//             model.measurementSync = 0;
//             await dbHelper.updateUnitTable(model);
//           } else {
//             if (id == null) {
//               model.productId = prodId;
//             } else {
//               model.productId = id;
//             }
//             model.measurement = obj.measurement;
//             model.measurementPrice = obj.measurementPrice;
//             model.workspaceId = MyApp.activeWorkspace.workspaceId;
//             String priceUnitId = uuid.v4().toString();
//             model.priceTypeId = priceUnitId;
//             model.measurementSync = 0;
//             model.rowStatus = obj.rowStatus;
//             await dbHelper.insertUnits(model);
//           }
//         }
//       }
//       imageId = await saveImage();
//       if (id == null) {
//         await insert(
//             prodId,
//             name,
//             price,
//             imageId,
//             sgst,
//             cgst,
//             mrp,
//             key,
//             groupId,
//             prodSaleId,
//             priceType,
//             dynamicQty,
//             enableInventory,
//             keyID,
//             parcelController.text);
//       } else {
//         await update(
//             id,
//             name,
//             price,
//             imageId,
//             sgst,
//             cgst,
//             mrp,
//             key,
//             groupId,
//             prodSaleId,
//             priceType,
//             dynamicQty,
//             enableInventory,
//             keyID,
//             parcelController.text);
//       }
//       // ignore: await_only_futures
//       await _queryAllProductDeatails();
//       setState(() {
//         clearTextField();
//         imageFile = null;
//         imageId = null;
//       });
//       productUnitList.clear();
//       await setProductSaleId();
//       FocusScope.of(context).requestFocus(product);
//       if (press == "add") {
//         Navigator.pop(context);
//       }
//       isLock = false;
//     }
//   }

//   onSubmitUnitMeasurement() async {
//     if (unitController.text != '') {
//       PriceUnitType model = PriceUnitType();
//       model.measurement = unitController.text;
//       model.measurementPrice =
//           unitPriceController.text != "" ? unitPriceController.text : null;
//       model.rowStatus = 0;
//       setState(() {
//         if (productUnitList.length != 0) {
//           if (unitIndex == null) {
//             productUnitList.add(model);
//           } else {
//             if (productUnitList[unitIndex].priceTypeId != null) {
//               productUnitList[unitIndex].rowStatus = 1;
//             }
//             productUnitList[unitIndex].measurement = model.measurement;
//             productUnitList[unitIndex].measurementPrice =
//                 model.measurementPrice;
//             unitIndex = null;
//           }
//         } else {
//           productUnitList.add(model);
//           unitIndex = null;
//         }
//         isMeasurementIsEmpty = false;
//         unitController.clear();
//         unitPriceController.clear();
//         FocusScope.of(context).requestFocus(measure);
//       });
//     } else {
//       setState(() {
//         isMeasurementIsEmpty = unitController.text.isEmpty ? true : false;
//       });
//     }
//   }

//   //EDIT WIDGET
//   Widget editWidget() {
//     return Padding(
//       padding: EdgeInsets.all(5),
//       child: IconButton(
//         iconSize: 25,
//         icon: Icon(Icons.edit),
//         onPressed: () {
//           setState(() {
//             isUpdate = true;
//           });
//         },
//         color: Colors.white,
//       ),
//     );
//   }

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     double height = MediaQuery.of(context).size.height;
//     // height = 200.0;
//     return Scaffold(
//       appBar: BaseAppBar(
//         title: text(
//           id == null ? "Add Product" : "Update Product",
//           textColor: secondaryTextColor,
//           fontSize: textSizeLargeMedium,
//         ),
//         appBar: AppBar(),
//         widgets: id != null ? <Widget>[editWidget()] : null,
//       ),
//       body: Container(
//         height: height,
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
//           child: SingleChildScrollView(
//             child: Form(
//               key: validator.formkey,
//               child: Column(
//                 // crossAxisAlignment: CrossAxisAlignment.start,
//                 children: <Widget>[
//                   SizedBox(height: 10),
//                   Container(
//                     width: double.maxFinite,
//                     child: EditText(
//                       text: "Product Id",
//                       isPassword: false,
//                       mController: productSaleIdController,
//                       inputFormate: [
//                         FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
//                       ],
//                       inputType: TextInputType.number,
//                       validator: (value) {
//                         if (value.isEmpty)
//                           return 'This field is required';
//                         else if (ListUtility.productList.firstWhere(
//                                 (element) =>
//                                     element.productSaleId.toString() == value &&
//                                     id != element.productId.toString(),
//                                 orElse: () => null) !=
//                             null)
//                           return 'This id already used';
//                         else
//                           return null;
//                       },
//                       inputAction: TextInputAction.next,
//                       enable: isUpdate,
//                     ),
//                   ),
//                   SizedBox(height: 10),
//                   Container(
//                     width: double.maxFinite,
//                     child: EditText(
//                       text: "Product Name",
//                       isPassword: false,
//                       mController: productController,
//                       autofocus: true,
//                       focus: product,
//                       maxLength: 40,
//                       validator: (value) {
//                         if (value.isEmpty)
//                           return 'This field is required';
//                         else if (ListUtility.productList.firstWhere(
//                                 (element) =>
//                                     element.name == value &&
//                                     id != element.productId,
//                                 orElse: () => null) !=
//                             null)
//                           return 'This name already used';
//                         else
//                           return null;
//                       },
//                       inputAction: TextInputAction.next,
//                       enable: isUpdate,
//                     ),
//                   ),
//                   if (showDynamicQty) SizedBox(height: 10),
//                   if (showDynamicQty)
//                     Container(
//                       child: CheckboxListTile(
//                         title: Text("Dynamic quantity"),
//                         activeColor: !isUpdate ? grey : null,
//                         contentPadding: EdgeInsets.all(0),
//                         controlAffinity: ListTileControlAffinity.leading,
//                         value: dynamicQuantity,
//                         onChanged: (bool value) {
//                           setState(() {
//                             if (isUpdate) {
//                               dynamicQuantity = value;
//                               FocusScope.of(context).requestFocus(FocusNode());
//                             }
//                           });
//                         },
//                       ),
//                     ),
//                   SizedBox(height: 10),
//                   Container(
//                     child: Row(
//                       children: <Widget>[
//                         productKeyList.length != 0
//                             ? Column(
//                                 mainAxisAlignment: MainAxisAlignment.start,
//                                 children: [
//                                   // Container(
//                                   //   height: 20,
//                                   //   width: width / 1.4,
//                                   //   child: text("keyword",
//                                   //       textColor: textSecondaryColor,
//                                   //       fontSize: textSizeSmall,
//                                   //       fontFamily: fontSemibold),
//                                   // ),
//                                   Container(
//                                     width: width / 1.4,
//                                     // height: 40,
//                                     child: InputDecorator(
//                                       decoration: InputDecoration(
//                                         contentPadding: EdgeInsets.symmetric(
//                                             horizontal: 20.0, vertical: 1.0),
//                                         labelText: 'Category',
//                                         border: OutlineInputBorder(
//                                             borderRadius:
//                                                 BorderRadius.circular(5.0)),
//                                       ),
//                                       child: DropdownButtonHideUnderline(
//                                         child: new DropdownButton<ProductKey>(
//                                           hint: Text("Category"),
//                                           value: selectKeyword,
//                                           items: productKeyList
//                                               .map((ProductKey value) {
//                                             return new DropdownMenuItem<
//                                                 ProductKey>(
//                                               value: value,
//                                               child:
//                                                   new Text(value.keywordName),
//                                             );
//                                           }).toList(),
//                                           onChanged: !isUpdate
//                                               ? null
//                                               : (ProductKey val) async {
//                                                   setState(() {
//                                                     if (isUpdate) {
//                                                       selectKeyword = val;
//                                                       FocusScope.of(context)
//                                                           .requestFocus(
//                                                               FocusNode());
//                                                     }
//                                                   });
//                                                 },
//                                         ),
//                                       ),
//                                     ),
//                                   ),
//                                 ],
//                               )
//                             : Container(),
//                         InkWell(
//                           onTap: () {
//                             if (!isLock) {
//                               isLock = true;
//                               if (isUpdate) {
//                                 Navigator.push(
//                                         context,
//                                         MaterialPageRoute(
//                                             builder: (context) =>
//                                                 ViewProductCategory()))
//                                     .then((value) async {
//                                   setState(() {
//                                     selectKeyword = null;
//                                   });
//                                   if (id == null) {
//                                     // ignore: await_only_futures
//                                     await queryAllProductKeys();
//                                     if (productKeyList.length != 0) {
//                                       selectKeyword = productKeyList.last;
//                                     }
//                                   } else {
//                                     await setTextFieldValues();
//                                   }
//                                 });
//                               }
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                               padding: EdgeInsets.symmetric(
//                                   horizontal: 20, vertical: 13),
//                               // width: 25,
//                               child: Icon(Icons.add)),
//                         )
//                       ],
//                     ),
//                   ),
//                   if (showToppings) SizedBox(height: 10),
//                   if (showToppings)
//                     Container(
//                       child: Row(
//                         children: <Widget>[
//                           toppingsGroupList.length != 0
//                               ? Column(
//                                   mainAxisAlignment: MainAxisAlignment.start,
//                                   children: [
//                                     // Container(
//                                     //   // height: 20,
//                                     //   width: width / 1.4,
//                                     //   child: text("toppings group",
//                                     //       textColor: textSecondaryColor,
//                                     //       fontSize: textSizeSmall,
//                                     //       fontFamily: fontSemibold),
//                                     // ),
//                                     Container(
//                                       width: width / 1.4,
//                                       // height: 40,
//                                       child: InputDecorator(
//                                         decoration: InputDecoration(
//                                           contentPadding: EdgeInsets.symmetric(
//                                               horizontal: 20.0, vertical: 1.0),
//                                           labelText: 'toppings',
//                                           border: OutlineInputBorder(
//                                               borderRadius:
//                                                   BorderRadius.circular(5.0)),
//                                         ),
//                                         child: DropdownButtonHideUnderline(
//                                           child:
//                                               new DropdownButton<ToppingsGroup>(
//                                             hint: Text("toppings"),
//                                             value: selectGroup,
//                                             items: toppingsGroupList
//                                                 .map((ToppingsGroup value) {
//                                               return new DropdownMenuItem<
//                                                   ToppingsGroup>(
//                                                 value: value,
//                                                 child: new Text(
//                                                     value.toppingGroupName),
//                                               );
//                                             }).toList(),
//                                             onChanged: !isUpdate
//                                                 ? null
//                                                 : (ToppingsGroup val) async {
//                                                     setState(() {
//                                                       if (isUpdate) {
//                                                         selectGroup = val;
//                                                         FocusScope.of(context)
//                                                             .requestFocus(
//                                                                 FocusNode());
//                                                       }
//                                                     });
//                                                   },
//                                           ),
//                                         ),
//                                       ),
//                                     ),
//                                   ],
//                                 )
//                               : Container(),
//                           InkWell(
//                             onTap: () {
//                               if (!isLock) {
//                                 isLock = true;
//                                 if (isUpdate) {
//                                   Navigator.push(
//                                           context,
//                                           MaterialPageRoute(
//                                               builder: (context) =>
//                                                   ToppingsList()))
//                                       .then((value) async {
//                                     setState(() {
//                                       selectGroup = null;
//                                     });
//                                     if (id == null) {
//                                       // ignore: await_only_futures
//                                       await queryAllProductToppings();
//                                       if (toppingsGroupList.length != 0) {
//                                         selectGroup = toppingsGroupList.last;
//                                       }
//                                     } else {
//                                       await setTextFieldValues();
//                                     }
//                                   });
//                                 }
//                                 isLock = false;
//                               }
//                             },
//                             child: Container(
//                                 padding: EdgeInsets.symmetric(
//                                     horizontal: 20, vertical: 13),
//                                 // width: 25,
//                                 child: Icon(Icons.add)),
//                           )
//                         ],
//                       ),
//                     ),
//                   if (showMeasurement)
//                     SizedBox(
//                       height: 10,
//                     ),
//                   if (showMeasurement)
//                     Container(
//                       child: CheckboxListTile(
//                         title: Text("Add Measurement"),
//                         activeColor: !isUpdate ? grey : null,
//                         contentPadding: EdgeInsets.all(0),
//                         controlAffinity: ListTileControlAffinity.leading,
//                         value: measurementUpdate,
//                         onChanged: (bool priceUpdateValue) {
//                           setState(() {
//                             if (isUpdate) {
//                               measurementUpdate = priceUpdateValue;
//                               FocusScope.of(context).requestFocus(FocusNode());
//                             }
//                           });
//                         },
//                       ),
//                     ),
//                   // Container(
//                   //   child: EditText(
//                   //     text: "Measurement",
//                   //     isPassword: false,
//                   //     mController: unitController,
//                   //     inputType: TextInputType.text,
//                   //     validator: validator.validateTextField,
//                   //     inputAction: TextInputAction.next,
//                   //   ),
//                   // ),
//                   // SizedBox(
//                   //   height: 10,
//                   // ),
//                   // Container(
//                   //   child: EditText(
//                   //     text: "Price",
//                   //     isPassword: false,
//                   //     mController: unitPriceController,
//                   //     inputType: TextInputType.number,
//                   //     validator: validator.validateTextField,
//                   //     inputAction: TextInputAction.next,
//                   //   ),
//                   // ),
//                   measurementUpdate
//                       ? Column(
//                           children: [
//                             Row(
//                               children: [
//                                 Container(
//                                   padding: EdgeInsets.only(bottom: 5),
//                                   width: width / 2.5,
//                                   child: EditText(
//                                     text: "Measurement",
//                                     isPassword: false,
//                                     mController: unitController,
//                                     errorText: isMeasurementIsEmpty
//                                         ? "This field is required"
//                                         : null,
//                                     focus: measure,
//                                     inputType: TextInputType.text,
//                                     inputAction: TextInputAction.next,
//                                     enable: isUpdate,
//                                   ),
//                                 ),
//                                 SizedBox(
//                                   width: 5,
//                                 ),
//                                 Container(
//                                   padding: EdgeInsets.only(bottom: 5),
//                                   width: width / 3.3,
//                                   child: EditText(
//                                     text: "Price",
//                                     isPassword: false,
//                                     mController: unitPriceController,
//                                     inputType: TextInputType.number,
//                                     inputFormate: [
//                                       FilteringTextInputFormatter.allow(
//                                           RegExp(r'[0-9.]')),
//                                     ],
//                                     inputAction: TextInputAction.done,
//                                     enable: isUpdate,
//                                     submit: (submit) {
//                                       if (isUpdate) {
//                                         onSubmitUnitMeasurement();
//                                       }
//                                     },
//                                   ),
//                                 ),
//                                 SizedBox(
//                                   width: 5,
//                                 ),
//                                 InkWell(
//                                   onTap: () {
//                                     if (isUpdate) {
//                                       onSubmitUnitMeasurement();
//                                     }
//                                   },
//                                   child: Container(
//                                       padding: EdgeInsets.symmetric(
//                                           horizontal: 20, vertical: 13),
//                                       child: unitIndex == null
//                                           ? Icon(Icons.add)
//                                           : Icon(Icons.update)),
//                                 )
//                               ],
//                             ),
//                             Row(
//                               children: [
//                                 productUnitList.length != 0
//                                     ? SizedBox(
//                                         width: width / 1.1,
//                                         child: SingleChildScrollView(
//                                           child: ListView.builder(
//                                               itemCount: productUnitList.length,
//                                               scrollDirection: Axis.vertical,
//                                               physics: ScrollPhysics(),
//                                               shrinkWrap: true,
//                                               itemBuilder:
//                                                   (BuildContext context,
//                                                       int index) {
//                                                 var item =
//                                                     productUnitList[index];
//                                                 return productUnitList[index]
//                                                             .rowStatus !=
//                                                         2
//                                                     ? Container(
//                                                         height: 40,
//                                                         decoration:
//                                                             BoxDecoration(
//                                                           border: Border(
//                                                             bottom: BorderSide(
//                                                               color:
//                                                                   Colors.grey,
//                                                               width: 0.5,
//                                                             ),
//                                                           ),
//                                                         ),
//                                                         child: Row(
//                                                           mainAxisAlignment:
//                                                               MainAxisAlignment
//                                                                   .spaceBetween,
//                                                           children: [
//                                                             SizedBox(
//                                                               width:
//                                                                   width / 1.7,
//                                                               child: Row(
//                                                                 mainAxisAlignment:
//                                                                     MainAxisAlignment
//                                                                         .spaceEvenly,
//                                                                 children: [
//                                                                   Container(
//                                                                     width:
//                                                                         width /
//                                                                             2.8,
//                                                                     alignment:
//                                                                         Alignment
//                                                                             .centerLeft,
//                                                                     child: text(
//                                                                         item
//                                                                             .measurement,
//                                                                         textColor:
//                                                                             appStore
//                                                                                 .textPrimaryColor,
//                                                                         fontSize:
//                                                                             textSizeLargeMedium,
//                                                                         fontFamily:
//                                                                             fontSemibold),
//                                                                   ),
//                                                                   Container(
//                                                                     width:
//                                                                         width /
//                                                                             4.6,
//                                                                     alignment:
//                                                                         Alignment
//                                                                             .centerRight,
//                                                                     child: text(
//                                                                         item.measurementPrice ??
//                                                                             "",
//                                                                         textColor:
//                                                                             appStore
//                                                                                 .textPrimaryColor,
//                                                                         fontSize:
//                                                                             textSizeLargeMedium,
//                                                                         fontFamily:
//                                                                             fontSemibold),
//                                                                   ),
//                                                                 ],
//                                                               ),
//                                                             ),
//                                                             Row(
//                                                               children: [
//                                                                 IconButton(
//                                                                     onPressed:
//                                                                         () {
//                                                                       if (!isLock) {
//                                                                         isLock =
//                                                                             true;
//                                                                         setState(
//                                                                             () {
//                                                                           if (isUpdate) {
//                                                                             unitController.text =
//                                                                                 item.measurement;
//                                                                             unitPriceController.text =
//                                                                                 item.measurementPrice;
//                                                                             unitIndex =
//                                                                                 index;
//                                                                           }
//                                                                         });
//                                                                         isLock =
//                                                                             false;
//                                                                       }
//                                                                     },
//                                                                     icon: Icon(Icons
//                                                                         .edit)),
//                                                                 SizedBox(
//                                                                   width: 5,
//                                                                 ),
//                                                                 IconButton(
//                                                                     onPressed:
//                                                                         () {
//                                                                       if (!isLock) {
//                                                                         isLock =
//                                                                             true;
//                                                                         setState(
//                                                                             () {
//                                                                           if (isUpdate) {
//                                                                             if (productUnitList[index].priceTypeId !=
//                                                                                 null) {
//                                                                               productUnitList[index].rowStatus = 2;
//                                                                             } else {
//                                                                               productUnitList.removeAt(index);
//                                                                             }
//                                                                           }
//                                                                         });
//                                                                         isLock =
//                                                                             false;
//                                                                       }
//                                                                     },
//                                                                     icon: Icon(Icons
//                                                                         .delete))
//                                                               ],
//                                                             ),
//                                                           ],
//                                                         ),
//                                                       )
//                                                     : Container();
//                                               }),
//                                         ),
//                                       )
//                                     : Container(),
//                               ],
//                             ),
//                           ],
//                         )
//                       : Container(),
//                   SizedBox(
//                     height: 10,
//                   ),
//                   !measurementUpdate
//                       ? Container(
//                           child: EditText(
//                             text: "Price",
//                             isPassword: false,
//                             mController: priceController,
//                             inputType: TextInputType.number,
//                             inputFormate: [
//                               FilteringTextInputFormatter.allow(
//                                   RegExp(r'[0-9]')),
//                             ],
//                             inputAction: TextInputAction.next,
//                             enable: isUpdate,
//                           ),
//                         )
//                       : Container(),
//                   SizedBox(
//                     height: 10,
//                   ),
//                   !measurementUpdate
//                       ? Container(
//                           child: EditText(
//                               text: "MRP",
//                               isPassword: false,
//                               mController: mrpController,
//                               inputType: TextInputType.number,
//                               inputFormate: [
//                                 FilteringTextInputFormatter.allow(
//                                     RegExp(r'[0-9]')),
//                               ],
//                               inputAction: taxupdate == true
//                                   ? TextInputAction.next
//                                   : TextInputAction.done,
//                               enable: isUpdate,
//                               submit: (submit) {
//                                 if (validator.validate()) {
//                                   if (isUpdate) {
//                                     onSaveProduct("more");
//                                   }
//                                 }
//                               }),
//                         )
//                       : Container(),
//                   showTax
//                       ? Column(
//                           children: [
//                             SizedBox(
//                               height: 10,
//                             ),
//                             Container(
//                               child: CheckboxListTile(
//                                 title: Text("Add Tax"),
//                                 contentPadding: EdgeInsets.all(0),
//                                 activeColor: !isUpdate ? grey : null,
//                                 controlAffinity:
//                                     ListTileControlAffinity.leading,
//                                 value: taxupdate,
//                                 onChanged: (bool priceupdateValue) {
//                                   setState(() {
//                                     if (isUpdate) {
//                                       taxupdate = priceupdateValue;
//                                       FocusScope.of(context)
//                                           .requestFocus(FocusNode());
//                                     }
//                                   });
//                                 },
//                               ),
//                             ),
//                           ],
//                         )
//                       : Container(),
//                   taxupdate == true
//                       ? Column(children: <Widget>[
//                           // EditText(
//                           //   text: "CGST (%)",
//                           //   isPassword: false,
//                           //   mController: cgstController,
//                           //   inputType: TextInputType.number,
//                           //   inputFormate: [
//                           //     FilteringTextInputFormatter.allow(
//                           //         RegExp(r'[0-9.]')),
//                           //   ],
//                           //   validator: validator.validateTextField,
//                           //   inputAction: TextInputAction.next,
//                           //   enable: isUpdate,
//                           // ),
//                           // SizedBox(height: 10),
//                           EditText(
//                               text: "GST (%)",
//                               isPassword: false,
//                               mController: gstController,
//                               inputType: TextInputType.number,
//                               inputFormate: [
//                                 FilteringTextInputFormatter.allow(
//                                     RegExp(r'[0-9.]')),
//                               ],
//                               validator: validator.validateTextField,
//                               inputAction: TextInputAction.done,
//                               enable: isUpdate,
//                               submit: (submit) {
//                                 if (validator.validate()) {
//                                   if (isUpdate) {
//                                     onSaveProduct("more");
//                                   }
//                                 }
//                               }),
//                           SizedBox(height: 10)
//                         ])
//                       : Container(),
//                   showInventory
//                       ? SizedBox(
//                           height: 10,
//                         )
//                       : Container(),
//                   showInventory
//                       ? Container(
//                           child: CheckboxListTile(
//                             title: Text("Add Stock"),
//                             contentPadding: EdgeInsets.all(0),
//                             activeColor: !isUpdate ? grey : null,
//                             controlAffinity: ListTileControlAffinity.leading,
//                             value: enableInventory,
//                             onChanged: (bool inventory) {
//                               setState(() {
//                                 if (isUpdate) {
//                                   enableInventory = inventory;
//                                   FocusScope.of(context)
//                                       .requestFocus(FocusNode());
//                                 }
//                               });
//                             },
//                           ),
//                         )
//                       : Container(),
//                   enableInventory == true && id == null && showInventory
//                       ? Column(children: <Widget>[
//                           EditText(
//                               text: "Stocks",
//                               isPassword: false,
//                               mController: stocksController,
//                               inputType: TextInputType.number,
//                               inputFormate: [
//                                 FilteringTextInputFormatter.allow(
//                                     RegExp(r'[0-9.]')),
//                               ],
//                               inputAction: TextInputAction.done,
//                               enable: isUpdate,
//                               submit: (submit) {
//                                 if (validator.validate()) {
//                                   if (isUpdate) {
//                                     onSaveProduct("more");
//                                   }
//                                 }
//                               }),
//                           SizedBox(height: 10)
//                         ])
//                       : Container(),

//                   /// parcel details widget
//                   parcelDetailsWidget(),
//                   SizedBox(height: 10),

// //TEMP IMAGE PICKER
//                   Row(
//                     children: [
//                       GestureDetector(
//                         onTap: () {
//                           if (isUpdate) {
//                             pickImage();
//                           }
//                           //pickImageFromGallery(ImageSource.gallery);
//                         },
//                         child: Container(
//                           alignment: Alignment.center,
//                           height: 50,
//                           width: width / 3,
//                           child: text("Image",
//                               textColor: secondaryTextColor, isCentered: true),
//                           decoration: boxDecoration(
//                               bgColor: !isUpdate ? grey : buttonThemeColor,
//                               radius: 8.0),
//                         ),
//                       ),
//                       // imageFromGallery(),
//                       showImage(),
//                       // SizedBox(width: 10),
//                       // id != null && imageFile == null
//                       //     ? Container(
//                       //         //color: Colors.blue,
//                       //         width: 100,
//                       //         height: 100,
//                       //         alignment: Alignment.center,
//                       //         child: image != ""
//                       //             ? Utility.imageFromBase64String(image)
//                       //             : Container(),
//                       //       )
//                       //     : Container(
//                       //         child: Center(child: imageFromGallery()),
//                       //         width: 100,
//                       //         height: 100,
//                       //       ),
//                     ],
//                   ),
//                   SizedBox(height: 24),
//                   Row(
//                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                     children: [
//                       InkWell(
//                         onTap: () {
//                           validator.validate();
//                           if (validator.validate()) {
//                             if (isUpdate) {
//                               onSaveProduct("add");
//                             }
//                           }
//                         },
//                         child: Container(
//                           alignment: Alignment.center,
//                           height: 50,
//                           width: id == null ? width / 3 : width / 1.1,
//                           child: text(id == null ? "Add" : "Update Product ",
//                               textColor: secondaryTextColor, isCentered: true),
//                           decoration: boxDecoration(
//                               bgColor: !isUpdate ? grey : buttonThemeColor,
//                               radius: 8.0),
//                         ),
//                       ),
//                       id == null
//                           ? InkWell(
//                               onTap: () {
//                                 validator.validate();
//                                 if (validator.validate()) {
//                                   if (isUpdate) {
//                                     onSaveProduct("more");
//                                   }
//                                 }
//                               },
//                               child: Container(
//                                 alignment: Alignment.center,
//                                 height: 50,
//                                 width: width / 3,
//                                 child: text("More",
//                                     textColor: secondaryTextColor, isCentered: true),
//                                 decoration: boxDecoration(
//                                     bgColor:
//                                         !isUpdate ? grey : buttonThemeColor,
//                                     radius: 8.0),
//                               ),
//                             )
//                           : Container(),
//                     ],
//                   ),
//                 ],
//               ),
//             ),
//           ),
//         ),
//       ),
//     );
//   }

//   File imageFile;

//   pickImage() async {
//     try {
//       PickedFile pickedFile =
//           await ImagePicker().getImage(source: ImageSource.gallery);
//       if (pickedFile != null) {
//         imageFile = File(pickedFile.path);
//       }
//       var cropFile = await cropImage(imageFile.path);
//       setState(() {
//         imageFile = cropFile;
//       });
//     } catch (ex) {
//       imageFile = null;
//     }
//   }

//   Future<File> cropImage(filePath) async {
//     File croppedImage = await ImageCropper().cropImage(
//         sourcePath: filePath,
//         maxWidth: 150,
//         maxHeight: 150,
//         aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1));
//     return croppedImage;
//   }

//   Future<String> saveImage() async {
//     try {
//       var uuid = new Uuid();
//       String id = uuid.v4().toString();
//       if (imageFile == null) return null;
//       final String path1 = (await getApplicationDocumentsDirectory()).path;
//       //var fileName = path.basename(imageFile.path);
//       final File storedImage = await imageFile.copy('$path1/$id.png');
//       String localImagePath = storedImage.path;
//       return id;
//     } catch (ex) {
//       //print(ex);
//       return null;
//     }
//   }

//   Widget showImage() {
//     return !imageFile.toString().isEmptyOrNull
//         ? Stack(
//             children: [
//               isUpdate
//                   ? Positioned(
//                       top: 5.0,
//                       right: -10.0,
//                       child: IconButton(
//                         onPressed: () {
//                           setState(() {
//                             imageFile = null;
//                             imageId = null;
//                           });
//                         },
//                         icon: Icon(Icons.delete,
//                             size: 25.0, color: Colors.redAccent[400]),
//                       ), //Icon
//                     )
//                   : Container(),
//               SizedBox(
//                 height: 5,
//               ),
//               Container(
//                   width: 200,
//                   height: 200,
//                   alignment: Alignment.center,
//                   child: Image.file(imageFile))
//             ],
//           )
//         : Container(
//             decoration: boxDecoration(radius: 8.0),
//             alignment: Alignment.center,
//             child: Image(image: AssetImage("assets/images/no-image.jpeg")),
//             width: 200,
//             height: 200,
//           );
//   }

//   parcelDetailsWidget() {
//     if (showParcel) {
//       return Column(
//         children: [
//           SizedBox(
//             height: 10,
//           ),
//           // Container(
//           //   child: CheckboxListTile(
//           //     title: Text("Add Parcel"),
//           //     contentPadding: EdgeInsets.all(0),
//           //     activeColor: !isUpdate ? grey : null,
//           //     controlAffinity: ListTileControlAffinity.leading,
//           //     value: parcelupdate,
//           //     onChanged: (bool value) {
//           //       setState(() {
//           //         if (isUpdate) {
//           //           parcelupdate = value;
//           //           FocusScope.of(context).requestFocus(FocusNode());
//           //         }
//           //       });
//           //     },
//           //   ),
//           // ),
//           //if (parcelupdate) ...[
//           Column(children: <Widget>[
//             EditText(
//                 text: "Parcel Amount (₹)",
//                 isPassword: false,
//                 mController: parcelController,
//                 inputType: TextInputType.number,
//                 inputFormate: [
//                   FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
//                 ],
//                 validator: validator.validateTextField,
//                 inputAction: TextInputAction.done,
//                 enable: isUpdate,
//                 submit: (submit) {
//                   if (validator.validate()) {
//                     if (isUpdate) {
//                       onSaveProduct("more");
//                     }
//                   }
//                 }),
//             SizedBox(height: 10)
//           ]),
//           //],
//           SizedBox(
//             height: 10,
//           ),
//         ],
//       );
//     } else {
//       return Container();
//     }
//   }
// }
