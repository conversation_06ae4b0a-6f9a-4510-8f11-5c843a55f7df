// ignore_for_file: no_logic_in_create_state, must_be_immutable, unnecessary_this

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../masters/payment/add_payment_table.dart';
import '../../utils/common_widgets/header_footer.dart';

class CollectBill extends StatefulWidget {
  // ignore: prefer_typing_uninitialized_variables
  SalesType? sales;
  // ignore: prefer_typing_uninitialized_variables
  final customers;
  CollectBill({Key? key, this.sales, this.customers}) : super(key: key);

  @override
  _CollectBillState createState() => _CollectBillState(this.sales, this.customers);
}

class _CollectBillState extends State<CollectBill> {
  Validator validator = Validator();
  final creditController = TextEditingController();
  // final descriptionController = TextEditingController();
  // String selectCustomer;
  bool isLock = false;
  // bool isCustomerIsEmpty = false;
  SalesType? sales;
  Customer customers;
  _CollectBillState(this.sales, this.customers);
  final dbHelper = DatabaseHelper.instance;
  DBOperations funCommonDB = DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  BalanceDBScript balanceDB = BalanceDBScript();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  CommonDB commonDB = CommonDB();
  // List<Sales> salesList = [];
  double balance = 0.0;
  DateTime salesDate = DateTime.now();
  bool isPaymentTypeIsEmpty = false;
  PaymentType? selectPaymentType;
  bool enablePaymentType = false;

  clearTextFields() {
    creditController.clear();
    // descriptionController.clear();
  }

  onBillCollect() async {
   try{
     if (!isLock) {
      isLock = true;
      // try {
      LoadingAlertWidget.onLoading(context);;
      SalesType model = SalesType();
      model.salesType = "credit";
      model.syncStatus = 0;
      model.customerID = customers.customerID;
      model.paymentTypeID = selectPaymentType?.paymentTypeID;
      model.workspaceID = MyApp.activeWorkspace.workspaceId;
      model.amount = creditController.text;
      if (sales == null) {
        var uuid = new Uuid();
        model.salesTypeID = uuid.v4().toString();
        model.rowStatus = 0;
        model.createdDate = DateTime.now().toString();
        await salesTypeDB.insertSalesType(model);
        showToast("Collected successfully");
      } else {
        model.salesTypeID = sales?.salesTypeID;
        model.salesID = sales?.salesID;
        model.createdDate = sales?.createdDate;
        model.rowStatus =
            await commonDB.checkNonSyncCommonFunction("SALESTYPE", "SalesTypeID", sales?.salesTypeID != null ? sales?.salesTypeID.toString() : "", "SyncStatus") == 0 ? 1 : 0;
        await salesTypeDB.updateSalesType(model);
        showToast("Updated successfully");
      }
      // Sales model = Sales();
      // model.customerID = customers.customerID.toString();
      // model.customerName = (customers.firstName.toString() +
      //     " " +
      //     customers.lastName.toString());
      // model.mobileNo = customers?.mobileNo;
      // // ignore: unnecessary_null_in_if_null_operators
      // model.paymentType = selectPaymentType?.paymentTypeID ?? null;
      // model.salesType = "credit";
      // // model.description = descriptionController.text;
      // model.totalAmount = creditController.text;
      // model.workspaceId = MyApp.activeWorkspace.workspaceId.toString();
      // model.date = salesDate.toString();
      // model.createdBy = MyApp.activeUser.username;
      // if (sales == null) {
      //   var uuid = const Uuid();
      //   String salesID = uuid.v4().toString();
      //   model.salesId = salesID;
      //   model.invoiceId = null;
      //   model.rowStatus = 0;
      //   model.sync = 0;
      //   await dbHelper.insertSalesDetails(model);
      //   showToast("Collected successfully");
      // } else {
      //   model.salesId = sales.salesId;
      //   model.rowStatus = await dbHelper.nonSyncSaleByID(
      //               sales.salesId.toString(), model.salesType.toString()) ==
      //           0
      //       ? 1
      //       : 0;
      //   model.sync = 0;
      //   await dbHelper.updateSalesTable(model);
      //   showToast("Updated successfully");
      // }
      Balance balance = Balance();
      balance.customerID = customers.customerID;
      balance.balance = sales == null ? creditController.text : (double.parse(creditController.text) - double.parse(sales?.amount ?? "0.0")).toString();
      await fnUtilities.insertOrUpdateBalance(balance, "credit");
      clearTextFields();
      LoadingAlertWidget.onStopping();
      Navigator.pop(context);
      isLock = false;
    }
   } catch(e){
    LoadingAlertWidget.onStopping();
    ErrorLogsFunction.insertErrorLogs(e, "onBillCollect - CollectBill");
   }
  }

  getSalesList() async {
    await assignValues();
    await calculateBalance();
  }

  calculateBalance() async {
    var keyExists = await balanceDB.getBalanceByCustomerID(customers.customerID!);
    setState(() {
      balance = 0.0;
      balance = keyExists == null ? 0 : double.parse(keyExists['CustomerBalance']);
    });
    // if (customers?.exitBalance != null) {
    //   balance = double.parse(customers.exitBalance.toString());
    // }
    // for (Sales b in salesList) {
    //   setState(() {
    //     if (b.salesType == "credit") {
    //       balance = (balance - double.parse(b.totalAmount.toString()));
    //       b.balance = balance.toStringAsFixed(1);
    //     } else {
    //       balance = (balance + double.parse(b.totalAmount.toString()));
    //       b.balance = balance.toStringAsFixed(1);
    //     }
    //   });
    // }
  }

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        initialDate: salesDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime.now(),
        context: context);
    if (picked != null && picked != salesDate) {
      setState(() => salesDate = picked);
      TimeOfDay? pickedTime = await showTimePicker(
        initialTime: TimeOfDay.now(),
        context: context,
        builder: (BuildContext context, Widget? child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(alwaysUse24HourFormat: false),
            child: child!,
          );
        },
      );
      if (pickedTime != null) {
        setState(() {
          try {
            String hour;
            String minute;
            if (pickedTime.hour.toString().length == 1) {
              hour = "0" + pickedTime.hour.toString();
            } else {
              hour = pickedTime.hour.toString();
            }
            if (pickedTime.minute.toString().length == 1) {
              minute = "0" + pickedTime.minute.toString();
            } else {
              minute = pickedTime.minute.toString();
            }
            var tempSalesDate = DateTime.parse(DateFormat("yyyy-MM-dd ").format(salesDate) + hour + ":" + minute + ":00.000"); // set the value of text field.
            var now = DateTime.now();
            if (tempSalesDate.isAfter(now)) {
              salesDate = now;
              showToast("Future time not allowed");
            } else {
              salesDate = tempSalesDate;
            }
          } catch (ex) {
            // ignore: avoid_print
            // print("format exception");
          }

          // ignore: avoid_print
          // print(salesDate);
        });
      }
    }
  }

  assignValues() {
    if (sales != null) {
      salesDate = DateTime.parse(sales!.createdDate.toString());
      // descriptionController.text = sales.description!;
      creditController.text = sales!.amount ?? "";
      try {
        selectPaymentType = ListUtility.paymentTypeList.firstWhere((element) => element.paymentTypeID == sales?.paymentTypeID);
      } catch (ex) {}
    }
  }

  initSettings() async {
    var PaymentTypeValue = await fnUtilities.workSpaceSetValues(key_paymentType);
    setState(() {
      if (PaymentTypeValue == "1") {
        enablePaymentType = true;
      } else {
        enablePaymentType = false;
      }
    });
  }

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    await initSettings();
    await getSalesList();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(sales == null ? 'Collect Bill' : 'Update Bill', textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        // widgets: customers != null ? <Widget>[editWidget()] : null,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Container(
          width: width,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), color: backgroundColor),
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 15.0),
              child: Form(
                key: validator.formkey,
                child: Column(
                  children: [
                    const SizedBox(
                      height: 15,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: width / 5,
                          child: Text(
                            'Name',
                            textAlign: TextAlign.left,
                            style: black15BoldTextStyle,
                          ),
                        ),
                        Expanded(
                          // width: width / 1.5,
                          child: Text(
                            ': ' + customers.firstName.toString() + ' ' + customers.lastName.toString() + ' A/C',
                            textAlign: TextAlign.left,
                            style: black15BoldTextStyle,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        SizedBox(
                          width: width / 5,
                          child: Text(
                            'Balance',
                            textAlign: TextAlign.left,
                            style: black15BoldTextStyle,
                          ),
                        ),
                        Expanded(
                        //width: width / 1.5,
                          child: Text(
                            ': Rs.${balance.toStringAsFixed(2)}',
                            textAlign: TextAlign.left,
                            style: black15BoldTextStyle,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(vertical: 5),
                          width: width / 1.5,
                          child: Text(
                            DateFormat("dd-MM-yyyy hh:mm a").format(salesDate),
                            textAlign: TextAlign.left,
                            style: black15BoldTextStyle,
                          ),
                        ),
                        IconButton(
                            onPressed: () {
                              selectDate(context);
                            },
                            icon: const Icon(Icons.calendar_today))
                      ],
                    ),
                    const SizedBox(
                      height: 20,
                    ),
                    enablePaymentType
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Column(
                                children: [
                                  SizedBox(
                                    // padding: const EdgeInsets.symmetric(
                                    //     horizontal: 10, vertical: 12),
                                    width: width * 0.7,
                                    // decoration: BoxDecoration(
                                    //   border: Border.all(color: primaryBlackColor),
                                    //   borderRadius: BorderRadius.circular(5),
                                    // ),
                                    child: InputDecorator(
                                      decoration: InputDecoration(
                                        contentPadding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
                                        labelText: 'Payment Type',
                                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                      ),
                                      child: DropdownButtonHideUnderline(
                                        child: Theme(
                                          data: Theme.of(context).copyWith(canvasColor: Colors.blue.shade50),
                                          child: DropdownButton(
                                            // decoration: const InputDecoration(
                                            //     border: InputBorder.none,
                                            //     contentPadding: EdgeInsets.all(0)),
                                            isExpanded: true,
                                            elevation: 0,
                                            isDense: true,
                                            // validator: (value) =>
                                            //     value == null ? 'Area is required' : null,
                                            hint: Text(
                                              'Please Select...',
                                              style: black13RegularTextStyle,
                                            ),
                                            icon: Icon(
                                              Icons.keyboard_arrow_down,
                                              color: primaryArrowColor,
                                              size: 20,
                                            ),
                                            value: selectPaymentType,
                                            style: black13RegularTextStyle,
                                            onChanged: (PaymentType? newValue) {
                                              setState(() {
                                                FocusScope.of(context).requestFocus(FocusNode());
                                                selectPaymentType = newValue;
                                              });
                                            },
                                            items: ListUtility.paymentTypeList.map<DropdownMenuItem<PaymentType>>((PaymentType value) {
                                              return DropdownMenuItem<PaymentType>(
                                                value: value,
                                                child: Text(value.paymentName.toString()),
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                  isPaymentTypeIsEmpty
                                      ? Container(
                                          width: width * 0.7,
                                          alignment: Alignment.centerLeft,
                                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 15),
                                          child: const Text(
                                            "This field is required",
                                            style: TextStyle(color: Colors.red, fontSize: 11.5),
                                          ),
                                        )
                                      : Container()
                                ],
                              ),
                              IconButton(
                                  onPressed: () {
                                    if (PermissionFunctions.checkPermission(49)) {
                                    if (!isLock) {
                                      isLock = true;
                                      Navigator.push(context, MaterialPageRoute(builder: (context) => AddPaymentType())).then((value) async {
                                        await fnUtilities.getAllPaymentType();
                                        setState(() {
                                          selectPaymentType = null;
                                          if (sales != null) {
                                            try {
                                              selectPaymentType = ListUtility.paymentTypeList.singleWhere((item) => item.paymentTypeID == sales?.paymentTypeID);
                                            } catch (e) {
                                              selectPaymentType = null;
                                            }
                                          } else {
                                            selectPaymentType = null;
                                          }
                                        });
                                      });
                                      isLock = false;
                                    }
                                    }
                                  },
                                  icon: const Icon(Icons.add))
                            ],
                          )
                        : Container(),
                    enablePaymentType
                        ? const SizedBox(
                            height: 15,
                          )
                        : Container(),
                    EditText(
                      text: "Credit Amount",
                      isPassword: false,
                      mController: creditController,
                      validator: validator.validateTextField,
                      inputType: TextInputType.number,
                      inputAction: TextInputAction.next,
                      inputFormate: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                      ],
                    ),
                    const SizedBox(
                      height: 15,
                    ),
                    // EditText(
                    //   text: "Description",
                    //   isPassword: false,
                    //   maxLine: 2,
                    //   mController: descriptionController,
                    //   inputType: TextInputType.text,
                    //   inputAction: TextInputAction.done,
                    // ),
                    // const SizedBox(
                    //   height: 15,
                    // ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 20),
                      child: InkWell(
                        onTap: () {
                          if (PermissionFunctions.checkPermission(50)) {
                          if (selectPaymentType != null || !enablePaymentType) {
                            setState(() {
                              isPaymentTypeIsEmpty = false;
                            });
                            validator.validate();
                            if (validator.validate() ?? false) {
                              onBillCollect();
                            }
                          } else {
                            setState(() {
                              isPaymentTypeIsEmpty = true;
                            });
                          }
                          }
                        },
                        child: Container(
                            height: 45,
                             width: width ,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(20),
                              color: buttonThemeColor,
                            ),
                            child: Padding(
                              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                              child: Text(
                                sales == null ? 'Collect Bill' : 'Update Bill',
                                textAlign: TextAlign.center,
                                style: const TextStyle(color: secondaryTextColor, fontSize: textSizeNormal),
                              ),
                            )),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
