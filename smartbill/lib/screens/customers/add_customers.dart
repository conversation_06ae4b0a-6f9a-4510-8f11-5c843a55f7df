// ignore_for_file: prefer_typing_uninitialized_variables, unnecessary_this, no_logic_in_create_state

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:fluttercontactpicker/fluttercontactpicker.dart';
import 'package:nb_utils/nb_utils.dart'; 
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/dbutils/customers_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/screens/customers/view_area.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../Repositories/image_repository.dart';
import '../../utils/common_widgets/header_footer.dart';

class AddCustomer extends StatefulWidget {
  Customer? customers;
   AddCustomer({Key? key, this.customers}) : super(key: key);

  @override
  _AddCustomerState createState() => _AddCustomerState(this.customers);
}

class _AddCustomerState extends State<AddCustomer> {
  Customer? customers;
  _AddCustomerState(this.customers);
  Validator validator = Validator();
  CommonDB commonDB = CommonDB();
  DBOperations funCommonDB = DBOperations();
  final dbHelper = DatabaseHelper.instance;
  CustomersDBScript customerDB = CustomersDBScript();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  final mobileNoController = TextEditingController();
  final address1Controller = TextEditingController();
  final address2Controller = TextEditingController();
  final balanceController = TextEditingController();
  final gstNoController = TextEditingController();
  bool isLock = false;
  bool isAreaIsEmpty = false;
  Area? selectArea;
  bool isWhatsApp = false;
  bool isFullDetails = false;
  List<Area> areaList = [];

  clearTextFields() async {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
    mobileNoController.clear();
    address1Controller.clear();
    address2Controller.clear();
    balanceController.clear();
    gstNoController.clear();
    await getAreaList();
  }

  onCustomerRegister() async {
    try {
      if (!isLock) {
        isLock = true;
        LoadingAlertWidget.onLoading(context);;
        int? isExists = await commonDB.checkValueExists(
            "CUSTOMERS", "MobileNo", mobileNoController.text, "CustomerID", customers?.customerID != null ? customers?.customerID ?? "" : "");
        if (isExists != 0) {
          LoadingAlertWidget.onStopping();
          toast("Customer already exists");
        } else {
          Customer model = Customer();
          model.firstName = "${firstNameController.text} ${lastNameController.text}";
          model.lastName = "";
          model.mobileNo = mobileNoController.text;
          model.email = emailController.text != "" ? emailController.text.toLowerCase() : null;
          model.address1 = address1Controller.text != "" ? address1Controller.text : null;
          model.address2 = address2Controller.text != "" ? address2Controller.text : null;
          model.fkAreaID = selectArea?.areaID;
          model.syncStatus = 0;
          model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();
          model.isWhatsApp = isWhatsApp ? 1 : 0;
          model.gstNo = gstNoController.text != "" ? gstNoController.text.toUpperCase() : null;
          model.exitBalance = balanceController.text != "" ? balanceController.text : "0.0";
          if (customers == null) {
            var uuid = const Uuid();
            String customerID = uuid.v4().toString();
            model.customerID = customerID;
            model.createdDate = DateTime.now().toString();
            model.rowStatus = 0;
            await customerDB.insertCustomer(model);
            toast("Customer added successfully");
          } else {
            model.customerID = customers?.customerID;
            model.rowStatus = await commonDB.checkNonSyncCommonFunction(
                        "AREAS", "AreaID", customers?.customerID != null ? customers?.customerID.toString() : "", "SyncStatus") ==
                    0
                ? 1
                : 0;
            await customerDB.updateCustomer(model);
            selectArea = null;
            toast("Customer updated successfully");
          }
          if (model.exitBalance != null && model.exitBalance != "0.0") {
            Balance balance = Balance();
            balance.customerID = model.customerID;
            String type = model.exitBalance![0] == "-" ? "credit" : "debit";
            balance.balance = model.exitBalance![0] == "-" ? model.exitBalance?.substring(1) : model.exitBalance;
            balance.balance = customers != null ? (double.parse(balance.balance ?? "0.0") - double.parse(customers?.exitBalance ?? "0.0")).toString() : balance.balance;
            await fnUtilities.insertOrUpdateBalance(balance, type);
          }
          await clearTextFields();
        }
        LoadingAlertWidget.onStopping();
        Navigator.pop(context);
        isLock = false;
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "onCustomerRegister - AddCustomer");
    }
  }

  getAreaList() async {
    List<Area> tempAreaList = await funCommonDB.getAllAreaFromDB();
    areaList.clear();
    Area model = Area();
    model.areaName = "None";
    areaList.add(model);
    setState(() {
      areaList.addAll(tempAreaList);
    });
    if (areaList.isNotEmpty && customers == null) {
      selectArea = areaList.first;
    }
  }

  assignValues() async {
    firstNameController.text = customers?.firstName ?? "";
    lastNameController.text = customers?.lastName ?? "";
    emailController.text = customers?.email ?? "";
    mobileNoController.text = customers?.mobileNo ?? "";
    address1Controller.text = customers?.address1 ?? "";
    address2Controller.text = customers?.address2 ?? "";
    gstNoController.text = customers?.gstNo ?? "";
    balanceController.text = customers?.exitBalance ?? "";
    try {
      selectArea = areaList.firstWhereOrNull((item) => item.areaID == customers?.fkAreaID,);
    } catch (e) {
      selectArea = null;
    }
    isWhatsApp = customers?.isWhatsApp == 1 ? true : false;
    if (customers?.email != null ||
        customers?.address1 != null ||
        customers?.address2 != null ||
        customers?.gstNo != null ||
        customers?.exitBalance != "0.0" ||
        customers?.fkAreaID != null) {
      isFullDetails = true;
    }
  }

  checkAddOrUpdate() async {
    await getAreaList();
    if (customers != null) {
      await assignValues();
    }
  }

  @override
  void initState() {
    checkAddOrUpdate();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(customers == null ? "Add Customer" : "Update Customer",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(0.0),
            child: Container(
              height: height,
              // width: width > tabletWidth ? width * 0.48 : width * 0.85,
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
              child: Padding(
                padding: const EdgeInsets.all(10.0),
                child: SingleChildScrollView(
                  child: Form(
                    key: validator.formkey,
                    child: Column(
                      children: <Widget>[
                        BootstrapRow(
                          children: [
                            BootstrapCol(
                                sizes: 'col-md-6 col-sm-12',
                                child: Column(
                                  children: [
                                    EditText(
                                      text: "First Name",
                                      isPassword: false,
                                      mController: firstNameController,
                                      inputType: TextInputType.text,
                                      inputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    EditText(
                                      text: "Last Name",
                                      isPassword: false,
                                      mController: lastNameController,
                                      inputType: TextInputType.text,
                                      inputAction: TextInputAction.next,
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Checkbox(
                                          value: isWhatsApp,
                                          onChanged: (value) {
                                            setState(() {
                                              isWhatsApp = value ?? false;
                                            });
                                          },
                                        ),
                                        const Text("Is same number used for whatsapp"),
                                      ],
                                    ),
                                    SizedBox(
                                      height: 5,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Expanded(
                                          child: SizedBox(
                                            width: width * 0.76923,
                                            child: EditText(
                                              text: "Mobile No",
                                              isPassword: false,
                                              mController: mobileNoController,
                                              validator: validator.validateMobile,
                                              inputType: TextInputType.phone,
                                              inputFormate: [
                                                FilteringTextInputFormatter.digitsOnly,
                                              ],
                                              inputAction: TextInputAction.next,
                                              maxLength: 10,
                                            ),
                                          ),
                                        ),
                                        Padding(
                                          padding: const EdgeInsets.only(bottom: 20),
                                          child: IconButton(
                                              onPressed: () async {
                                                if (!isLock) {
                                                  isLock = true;
                                                  try {
                                                    final PhoneContact contact = await FlutterContactPicker.pickPhoneContact();
                                                    String mobNo = contact.phoneNumber!.number.toString();
                                                    String mobileNo = mobNo.substring(0, 1) == "+" ? mobNo.substring(3) : mobNo;
                                                    mobileNo = mobileNo.replaceAll(' ', '');
                                                    if (mobileNo.length == 11) {
                                                      if (mobileNo[0] == '0') {
                                                        mobileNo = mobileNo.substring(1);
                                                      }
                                                    }
                                                    setState(() {
                                                      mobileNoController.text = mobileNo;
                                                    });
                                                  } catch (ex) {
                                                    // ignore: avoid_print
                                                    // print("Contact not selected");
                                                  }

                                                  isLock = false;
                                                }
                                              },
                                              icon: const Icon(Icons.contact_page)),
                                        )
                                      ],
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Checkbox(
                                          value: isFullDetails,
                                          onChanged: (value) {
                                            setState(() {
                                              isFullDetails = value ?? false;
                                            });
                                          },
                                        ),
                                        const Text("More details"),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 10,
                                    ),
                                    isFullDetails
                                        ? Column(
                                            children: [
                                              emailTextFieldWidget(),
                                              const SizedBox(
                                                height: 20,
                                              ),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  Column(
                                                    children: [
                                                      SizedBox(
                                                        width: width >= tabletWidth ? width * 0.4 : width * 0.74,
                                                        // ),
                                                        child: areaDropdownWidget(),
                                                      ),
                                                      // isAreaIsEmpty
                                                      //     ? Container(
                                                      //   width: width / 1.4,
                                                      //   alignment: Alignment.centerLeft,
                                                      //   padding: const EdgeInsets.symmetric(
                                                      //       vertical: 4, horizontal: 15),
                                                      //   child: const Text(
                                                      //     "This field is required",
                                                      //     style: TextStyle(
                                                      //         color: Colors.red, fontSize: 11.5),
                                                      //   ),
                                                      // )
                                                      //     : Container()
                                                    ],
                                                  ),
                                                  IconButton(
                                                      onPressed: () {
                                                        if (!isLock) {
                                                          isLock = true;
                                                          Navigator.push(context, MaterialPageRoute(builder: (context) => ViewArea()))
                                                              .then((value) async {
                                                            setState(() {
                                                              selectArea = null;
                                                            });
                                                            await checkAddOrUpdate();
                                                            if (areaList.isNotEmpty && customers == null) {
                                                              selectArea = areaList.last;
                                                            }
                                                          });
                                                          isLock = false;
                                                        }
                                                      },
                                                      icon: const Icon(Icons.add))
                                                ],
                                              ),
                                              const SizedBox(
                                                height: 10,
                                              ),
                                            ],
                                          )
                                        : SizedBox.shrink()
                                  ],
                                )),
                            BootstrapCol(
                                sizes: 'col-md-6 col-sm-12',
                                child: Padding(
                                  padding: const EdgeInsets.only(left: 5.0),
                                  child: Column(
                                    children: [
                                      isFullDetails
                                          ? Column(
                                              children: [
                                                EditText(
                                                  text: "Address 1",
                                                  isPassword: false,
                                                  //maxLine: 2,
                                                  mController: address1Controller,
                                                  // validator: validator.validateTextField,
                                                  inputType: TextInputType.text,
                                                  inputAction: TextInputAction.next,
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                EditText(
                                                  text: "Address 2",
                                                  isPassword: false,
                                                  // maxLine: 2,
                                                  mController: address2Controller,
                                                  inputType: TextInputType.text,
                                                  inputAction: TextInputAction.next,
                                                ),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                balanceTextFieldWidget(),
                                                const SizedBox(
                                                  height: 10,
                                                ),
                                                EditText(
                                                  text: "GST No",
                                                  isPassword: false,
                                                  mController: gstNoController,
                                                  maxLength: 15,
                                                  validator: (value) {
                                                    if (value.isNotEmpty && value.length != 15) {
                                                      return 'gst no 15 digit';
                                                    } else {
                                                      return null;
                                                    }
                                                  },
                                                  inputType: TextInputType.text,
                                                  inputAction: TextInputAction.done,
                                                ),
                                              ],
                                            )
                                          : SizedBox.shrink(),
                                      width < tabletWidth
                                          ? RoundedButton(
                                              width: width > tabletWidth ? width * 0.48 : width * 0.85,
                                              onPressed: () {
                                                validator.validate();
                                                if (validator.validate()) {
                                                  onCustomerRegister();
                                                }
                                              },
                                              title: customers == null ? 'Add Customer' : 'Update Customer',
                                            )
                                          : SizedBox.shrink()
                                    ],
                                  ),
                                ))
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Positioned(
              child: width > tabletWidth
                  ? Padding(
                      padding: const EdgeInsets.symmetric(vertical: 25, horizontal: 25),
                      child: Align(
                        alignment: Alignment.bottomRight,
                        child: RoundedButton(
                          width: width > tabletWidth ? width * 0.47 : width * 0.85,
                          onPressed: () {
                            validator.validate();
                            if (validator.validate()) {
                              onCustomerRegister();
                            }
                          },
                          title: customers == null ? 'Add Customer' : 'Update Customer',
                        ),
                      ),
                    )
                  : SizedBox.shrink())
        ],
      ),
    );
  }

  /// email text field widget

  emailTextFieldWidget() {
    return EditText(
      text: "Email",
      isPassword: false,
      mController: emailController,
      validator: (value) {
        String pattern =
            r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
        RegExp regex = RegExp(pattern);
        if (!regex.hasMatch(value) && (value.isNotEmpty)) {
          return 'Enter valid email';
        } else {
          return null;
        }
      },
      inputType: TextInputType.text,
      inputAction: TextInputAction.next,
    );
  }

  /// area dropdown widget

  areaDropdownWidget() {
    return InputDecorator(
      decoration: InputDecoration(
        contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 1.0),
        labelText: 'Area',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
      ),
      child: DropdownButtonHideUnderline(
        child: Theme(
          data: Theme.of(context).copyWith(canvasColor: Colors.blue.shade50),
          child: DropdownButton(
            // decoration: const InputDecoration(
            //     border: InputBorder.none,
            //     contentPadding: EdgeInsets.all(0)),
            isExpanded: true,
            elevation: 0,
            isDense: true,
            // validator: (value) =>
            //     value == null ? 'Area is required' : null,
            hint: Text(
              'Please Select...',
              style: black13RegularTextStyle,
            ),
            icon: Icon(
              Icons.keyboard_arrow_down,
              color: primaryTextColor,
              size: 20,
            ),
            value: selectArea,
            style: black13RegularTextStyle,
            onChanged: (Area? newValue) {
              setState(() {
                FocusScope.of(context).requestFocus(FocusNode());
                selectArea = newValue;
              });
            },
            items: areaList.map<DropdownMenuItem<Area>>((Area value) {
              return DropdownMenuItem<Area>(
                value: value,
                child: Text(value.areaName.toString()),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }

  /// balence text field widget
  balanceTextFieldWidget() {
    return EditText(
      text: "Balance",
      isPassword: false,
      mController: balanceController,
      validator: (value) {
        if (value.isNotEmpty) {
          int plus = "+".allMatches(value).length;
          int minus = "-".allMatches(value).length;
          String symbol = value.toString()[0];
          if (plus >= 1 || minus >= 1) {
            if (plus > 1 || (plus == 1 && symbol != "+")) {
              return 'example: 1000';
            } else if (minus > 1 || (minus == 1 && symbol != "-")) {
              return 'example: -1000';
            }
          } else {
            return null;
          }
        } else {
          return null;
        }
      },
      inputFormate: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9,+,.,-]')),
      ],
      inputType: TextInputType.phone,
      inputAction: TextInputAction.next,
    );
  }
}
