import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/dbutils/area_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';

class AddArea extends StatefulWidget {
  // ignore: prefer_typing_uninitialized_variables
  Area? area;
   AddArea({Key? key, this.area}) : super(key: key);

  @override
  // ignore: no_logic_in_create_state
  _AddAreaState createState() => _AddAreaState(area);
}

class _AddAreaState extends State<AddArea> {
  Validator validator = Validator();
  final dbHelper = DatabaseHelper.instance;
  // LoadingAlertDialog alertLoading = LoadingAlertDialog();
  AreaDBScript areaDB = AreaDBScript();
  CommonDB commonDB = CommonDB();
  // CustomersDBScript customersDB = CustomersDBScript();
  // CommonDBFunction funCommonDB = CommonDBFunction();
  final areaController = TextEditingController();
  Area? area;
  _AddAreaState(this.area);
  bool isLock = false;

  clearTextFields() {
    areaController.clear();
  }

  onAreaRegister() async {
    try{
      if (!isLock) {
      isLock = true;
      LoadingAlertWidget.onLoading(context);;
      int? isExists = await commonDB.checkValueExists("AREAS", "AreaName", areaController.text, "AreaID", area?.areaID != null ? area?.areaID ?? "" : "");
      if (isExists != 0) {
        LoadingAlertWidget.onStopping();
        showToast("Payment type already exists");
      } else {
        Area model = Area();
        model.areaName = areaController.text;
        model.syncStatus = 0;
        model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();
        if (area?.areaID == null) {
          var uuid = const Uuid();
          DateTime currentDate = DateTime.now();
          String areaID = uuid.v4().toString();
          model.areaID = areaID;
          model.createdDate = currentDate.toString();
          model.rowStatus = 0;
          await areaDB.insertArea(model);
          showToast("Area added successfully");
        } else {
          model.areaID = area?.areaID;
          model.rowStatus = await commonDB.checkNonSyncCommonFunction("AREAS", "AreaID", area?.areaID != null ? area?.areaID.toString() : "", "SyncStatus") == 0 ? 1 : 0;
          await areaDB.updateArea(model);
          showToast("Area updated successfully");
          area?.areaID = null;
        }
        clearTextFields();
        LoadingAlertWidget.onStopping();
        Navigator.pop(context);
      }
      isLock = false;
    }
    } catch(e){
       LoadingAlertWidget.onStopping();
       ErrorLogsFunction.insertErrorLogs(e, "onAreaRegister - AddAreaState");
    }
  }

  assignValue() {
    //if (area != null) {
      areaController.text = area?.areaName ?? "";
    //}
  }

  @override
  void initState() {
    assignValue();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(area?.areaID == null ? 'Add Area' : 'Update Area', textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Form(
          key: validator.formkey,
          child: Column(
            children: [
              const SizedBox(
                height: 20,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: width / 1.1,
                    child: EditText(
                      text: "Area Name",
                      isPassword: false,
                      mController: areaController,
                      validator: validator.validateTextField,
                      inputType: TextInputType.text,
                      inputAction: TextInputAction.next,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 15,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    InkWell(
                      onTap: () {
                        validator.validate();
                        if (validator.validate()) {
                          onAreaRegister();
                        }
                      },
                      child: Container(
                          height: 45,
                          width: width / 1.1,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: buttonThemeColor,
                          ),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                            child: Text(
                              area?.areaID == null ? 'Add Area' : 'Update Area',
                              textAlign: TextAlign.center,
                              style: const TextStyle(color: secondaryTextColor, fontSize: textSizeNormal),
                            ),
                          )),
                    )
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
