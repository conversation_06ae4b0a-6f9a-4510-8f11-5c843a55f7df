// ignore_for_file: unused_local_variable

import 'dart:core';

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/area_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../../main.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';
import 'add_area.dart';

class ViewArea extends StatefulWidget {
  const ViewArea({Key? key}) : super(key: key);

  @override
  _ViewAreaState createState() => _ViewAreaState();
}

class _ViewAreaState extends State<ViewArea> {
  // CommonDBFunction dbFunction = CommonDBFunction();
  AreaDBScript areaDB = AreaDBScript();
  CommonDB commonDB = CommonDB();
  DBOperations funCommonDB = DBOperations();
  // PermissionFunction permission = PermissionFunction();
  // WorkspaceDBScript workspaceDB = WorkspaceDBScript();
  // ignore: non_constant_identifier_names
  List<Area> AreaList = [];

  @override
  void initState() {
    getAreaList();
    super.initState();
  }

  getAreaList() async {
    List<Area> tempAreaList = await funCommonDB.getAllAreaFromDB();
    setState(() {
      AreaList = tempAreaList;
    });
  }

  deleteDialog(BuildContext buildContext, Area area) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return showDialog(
        context: buildContext,
        builder: (context) {
          return SizedBox(
            height: height / 2,
            child: AlertDialog(
              content: Text("Are your sure, you want to delete " + area.areaName.toString()),
              actions: [
                InkWell(
                  onTap: () async {
                    try {
                      if (await commonDB.checkNonSyncCommonFunction("AREAS", "AreaID", area.areaID != null ? area.areaID.toString() : "", "SyncStatus") == 0) {
                        Area model = Area();
                        model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();
                        model.syncStatus = 0;
                        model.rowStatus = 2;
                        model.areaID = area.areaID;
                        await areaDB.deleteWithUpdateArea(model);
                      } else {
                        await commonDB.permanentDeleteCommonFunction("AREAS", "AreaID", area.areaID.toString());
                      }
                      await getAreaList();
                      Navigator.pop(context);
                    } catch (ex) {
                      //print("delete issue");
                    }
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: Text(
                      "Yes",
                      style: white14BoldTextStyle,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: buttonThemeColor,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: Text(
                      "No",
                      style: white14BoldTextStyle,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5),
                      color: buttonThemeColor,
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: BaseAppBar(
        title: text("View Area", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          IconButton(
            onPressed: () async {
              // if(permission.editPermissions(await workspaceDB.getPermissionKey("Products"))){
              Navigator.push(context, MaterialPageRoute(builder: (context) =>  AddArea())).then((value) => {getAreaList()});
              //    }
              //  else{
              //      showToast("You don't have a permission");
              //      }
            },
            icon: const Icon(
              Icons.add,
              color: whiteColor,
              size: 30.0,
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            areaListWidget()
          ],
        ),
      ),
    );
  }

  areaListWidget() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: AreaList.isNotEmpty
          ? ListView.builder(
              itemCount: AreaList.length,
              itemBuilder: (context, index) {
                var item = AreaList[index];
                return Column(
                  children: [
                    InkWell(
                      onDoubleTap: () async {
                        // if(permission.editPermissions(await workspaceDB.getPermissionKey("Products"))){
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => AddArea(
                                      area: item,
                                    ))).then((value) => {getAreaList()});
                        // }
                        // else{
                        //   showToast("You don't have a permission");
                        // }
                      },
                      onLongPress: () async {
                        //   if(permission.deletePermissions(await workspaceDB.getPermissionKey("Products"))){
                        //     if (await areaDB
                        //         .checkAreaExists(item.areaID.toString())) {
                        deleteDialog(context, item);
                        //     } else {
                        //       showToast(
                        //           "Area used in Customer, so update that Customer then delete");
                        //     }
                        //    }
                        //    else{
                        //  showToast("You don't have a permission");
                        //  }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(width: width / 1.5, child: Text(item.areaName ?? "")),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      thickness: 1,
                    ),
                  ],
                );
              })
          : const Center(child: Text("No Area available")),
    );
  }
}
