import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/dbutils/sales_details_db_script.dart';
import 'package:uuid/uuid.dart';

import '../../main.dart';
import '../../model/customers.dart';
import '../../utils/dbutils/customers_db_script.dart';

class CustomerDetailsHandler {
  // CustomerDetailsHandler();
  CommonDB commonDB = CommonDB();
  SyncUtility syncUtility = SyncUtility();
  CustomersDBScript customerDB = CustomersDBScript();
  Customer? customers;

  Future<Map<String,dynamic>> addCustomerDetails(String customerName, String mobileNumber) async {
    Customer model = Customer();
    model.firstName = customerName;
    model.syncStatus = 0;
    model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();

    int? isExists = await syncUtility.checkValueExists(
      "CUSTOMERS",
      cName: "MobileNo",
      cValue: mobileNumber,
    );

    if (mobileNumber.isNotEmpty) {
      if (isExists == 0) {
        var uuid = const Uuid();
        String customerID = uuid.v4().toString();
        model.customerID = customerID;
        model.lastName = "";
        model.mobileNo = mobileNumber;
        model.createdDate = DateTime.now().toString();
        model.rowStatus = 0;
        await customerDB.insertCustomer(model);
        return {"customerId" : model.customerID,"customerName" : model.firstName,"customerMobile" : model.mobileNo};
      } else {
        SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
        var rowItem = await commonDB.getSingleDataBasedOneColumn("CUSTOMERS", "MobileNo", mobileNumber);
        Customer customer = Customer.fromMap(rowItem!);
        model.lastName = customer.lastName;
        model.mobileNo = customer.mobileNo;
        model.address1 = customer.address1;
        model.address2 = customer.address2;
        model.fkAreaID = customer.fkAreaID;
        model.isWhatsApp = customer.isWhatsApp;
        model.email = customer.email;
        model.gstNo = customer.gstNo;
        model.exitBalance = customer.exitBalance;
        model.customerID = customer.customerID;
        model.rowStatus = 1;
        await customerDB.updateCustomer(model);
        await salesDetailsDBScript.updateCustomerNameInSalesDetails(model.customerID!,model.firstName!);
        return {"customerId" : model.customerID,"customerName" : model.firstName,"customerMobile" : model.mobileNo};
      }
    }
    return {};
  }
}
