// ignore_for_file: prefer_const_constructors, sized_box_for_whitespace, prefer_typing_uninitialized_variables, no_logic_in_create_state, unnecessary_this

// ignore: implementation_imports
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:smartbill/model/printer_devices.dart'; 
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../Repositories/reports_repository.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/sales_details_db.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/shop_tables_db_script.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../item_layouts/Itempage_layout2.dart';
import '../item_layouts/sales_main_page.dart';
import 'collect_bill.dart';

class CustomerAccount extends StatefulWidget {
  final customers;
  const CustomerAccount({Key? key, this.customers}) : super(key: key);

  @override
  _CustomerAccountState createState() => _CustomerAccountState(this.customers);
}

class _CustomerAccountState extends State<CustomerAccount> {
  Customer customers;
  _CustomerAccountState(this.customers);
  final searchController = TextEditingController();
  String? selectArea;
  String? option;
  DBOperations funCommonDB = DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  BalanceDBScript balanceDB = BalanceDBScript();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  CommonDB commonDB = CommonDB();
  final dbHelper = DatabaseHelper.instance;
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  SalesDetailsDB salesDetailsDB = SalesDetailsDB();

  // CommonDBFunction common = CommonDBFunction();
  List<SalesType> salesList = [];
  // SalesDBScript salesDB = SalesDBScript();
  String? salesID;
  Sales? sales;
  List<Transactions> salesTransactionList = [];
  bool isLock = false;
  bool isExpanded = false;
  List<SalesType> salesNavigationList = [];
  int pageNext = 10;
  int pageNo = 1;
  int maxPageCount = 10;
  bool isAll = true;
  bool isDebit = false;
  bool isCredit = false;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool isCustom = false;
  bool isToday = false;
  bool isWeekly = false;
  bool isMonthly = false;
  List<SalesType> filterSalesList = [];
  String? layout;
  List<Sales> allSalesList = [];
  bool multiPrint = false;
  bool isResetInvoice = false;
  PrinterDevices? reportPrinter;

  getSalesList() async {
    await getTableDetails();
    await getAllSalesList();
    salesList.clear();
    salesList = await salesTypeByCustomerID();
    await calculateBalance();
    await pageNavigation();
  }

  getAllSalesList() async {
    List<Sales> tempSalesList = [];
    tempSalesList = await funCommonDB.selectSalesCustomerByID(customers.customerID.toString());
    for (Sales sale in tempSalesList) {
      if (sale.fkTableID != null) {
        sale.tableName = await shopTableAssign(sale.fkTableID);
      }
      if (sale.paymentType != null) {
        sale.paymentName = await paymentTypeAssign(sale.paymentType);
      }
    }
    setState(() {
      allSalesList.clear();
      allSalesList.addAll(tempSalesList);
    });
  }

  salesTypeByCustomerID() async {
    final allRows = await salesTypeDB.selectAllSalesByCustomerID(customers.customerID!);
    List<SalesType> salesList = [];
    allRows?.forEach((row) => salesList.add(SalesType.fromMap(row)));
    return salesList;
  }

  double balance = 0.0;
  calculateBalance() async {
    balance = 0.0;
    double subBalance = 0.0;
    var keyExists = await balanceDB.getBalanceByCustomerID(customers.customerID!);
    balance = keyExists == null ? 0 : double.parse(keyExists['CustomerBalance']);
    if (customers.exitBalance != null) {
      subBalance = double.parse(customers.exitBalance.toString());
    }
    int index = salesList.length;
    for (SalesType b in salesList) {
      b.id = index;
      index--;
      setState(() {
        if (b.salesType == "credit") {
          subBalance = (subBalance - double.parse(b.amount.toString()));
          b.balance = subBalance.toStringAsFixed(1);
        } else {
          subBalance = (subBalance + double.parse(b.amount.toString()));
          b.balance = subBalance.toStringAsFixed(1);
        }
      });
    }
  }

  List<ShopTable> shopTablesList = [];
  queryGetSalesDetails(id) async {
    final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id);
    salesTransactionList.clear();
    for (var row in allRows) {
      salesTransactionList.add(Transactions.fromMap(row));
    }
  }

  shopTableAssign(String? tableID) async {
    String? tableName;
    if (tableID != null) {
      try {
        tableName = shopTablesList.firstWhere((element) => element.shopTableId == tableID).shopTableName ?? "";
      } catch (ex) {
        tableName = "";
      }
    }
    return tableName;
  }

  paymentTypeAssign(String? paymentID) async {
    String? paymentName;
    if (paymentID != null) {
      try {
        paymentName = ListUtility.paymentTypeList.firstWhere((element) => element.paymentTypeID == paymentID).paymentName;
      } catch (ex) {
        paymentName = "";
      }
    }
    return paymentName;
  }

  getTableDetails() async {
    if (ListUtility.shopTableList.isNotEmpty) {
      setState(() {
        shopTablesList.clear();
        shopTablesList.add(new ShopTable(shopTableId: "All", shopTableName: "All"));
        shopTablesList.addAll(ListUtility.shopTableList);
      });
    }
  }

  bool isLoading = false;
  queryAllSalesTransactionForExpand(String? salesID) async {
  if(salesID != null){
      setState(() {
      isLoading = true;
    });
    // await getTransactionDetails(salesID);
    await getAllSalesList();
    final allRows = await salesTransDBScript.queryAllSalesTransactionByCustomer(customers.customerID.toString());
    setState(() {
      salesTransactionList.clear();
      for (var row in allRows) {
        salesTransactionList.add(Transactions.fromMap(row));
      }
      isLoading = false;
    });
  }
  }

  salesDetailsBySalesID(String? salesID) async {
   if(salesID != null){
     Map<String, dynamic>? allRows = await salesDetailsDBScript.getSalesDetailsBySalesID(salesID);
    if (allRows != null) {
      Sales sales = Sales.fromMap(allRows);
      sales.tableName = await shopTableAssign(sales.fkTableID);
      sales.paymentName = await paymentTypeAssign(sales.paymentType);
      return sales;
    }
   }
    return null;
  }

  getTransactionDetails(String salesID) async {
    SyncUtility syncUtility = SyncUtility();
    await getTransactionBySalesID(salesID).then((value) async {
      if (value.result?.transactionList?.length != 0) {
        await syncUtility.syncTransactionsFromApi(value.result?.transactionList ?? []);
      }
    });
  }

  pageNavigation() async {
    int id = (pageNo - 1) * maxPageCount;
    if (filterSalesList.isEmpty) {
      await calculateBalance();
    }
    setState(() {
      salesNavigationList.clear();

      if (filterSalesList.isEmpty) {
        salesNavigationList.addAll(salesList.where((element) => element.id! > id && element.id! <= id + maxPageCount));
      } else {
        salesNavigationList.addAll(filterSalesList.where((element) => element.id! > id && element.id! <= id + maxPageCount));
      }
    });
  }

  selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null && picked != fromDate && dateType == 'fromDate') {
      setState(() {
        fromDate = picked;
      });
    }
    if (picked != null && picked != toDate && dateType == 'toDate') {
      setState(() {
        toDate = picked;
      });
    }
  }

  queryAllReportsByMonth() async {
    var now = DateTime.now();
    fromDate = DateTime.parse('${now.year}-${now.month.toString().padLeft(2, '0')}-01');
    // Find the last day of the month.
    var beginningNextMonth = (now.month < 12) ? DateTime(now.year, now.month + 1, 1) : DateTime(now.year + 1, 1, 1);
    var lastDay = beginningNextMonth.subtract(const Duration(days: 1)).day;
    toDate = DateTime.parse('${now.year}-${now.month.toString().padLeft(2, '0')}-$lastDay');
    toDate = toDate.add(const Duration(days: 1));
  }

  queryAllReportsByWeek() async {
    var now = DateTime.now();
    fromDate = now.subtract(Duration(days: now.weekday));
    toDate = now.add(Duration(days: (DateTime.daysPerWeek - now.weekday) - 1));
  }

  todayFunction() {
    setState(() {
      isToday = true;
      isWeekly = false;
      isMonthly = false;
      isCustom = false;
      fromDate = DateTime.now();
      toDate = DateTime.now();

      var now = DateTime.now();
      fromDate = DateTime.parse('${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}');
      toDate = fromDate;
    });
  }

//   //ACCOUNT PRINTER BUILD START--------------------------------------------------------------------

  printLineBuildFunction(Sales reports, List<Transactions> salesTransaction) async {
    String listData = "";
    var groupList = salesTransaction.groupListsBy((element) => element.salesId);
    if (salesTransaction.isNotEmpty) {
      for (var i in groupList.keys) {
        // ignore: prefer_is_empty
        if (groupList[i]?.length != 0) {
          if (reports.fkTableID != null) {
            listData += '''
   {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.paymentType != null) {
            listData += '''
   {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.discountPrice != null) {
            listData += '''
   {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
          }
          for (int list = 0; list < groupList[i]!.length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 9);
            String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(10) + " " + qty.toString().padLeft(9) + " " + double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(8) + " " + double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(12)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(10) + "  " + " ".padLeft(5) + "  " + ' '.padLeft(5) + "       " + " ".padLeft(10)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Sales", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Ref No: ${reports.invoiceId}", "size": 1, "alignment": 0},
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "Customer Name: ${reports.customerName}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "Product    Qty    Rate    Amount", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "Total RS. ${double.parse(reports.totalAmount.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray, develop: "Yes");
  }

  printLineBuildFunction80MM(Sales reports, List<Transactions> salesTransaction) async {
    String listData = "";
    var groupList = salesTransaction.groupListsBy((element) => element.salesId);
    if (salesTransaction.isNotEmpty) {
      for (var i in groupList.keys) {
        // ignore: prefer_is_empty
        if (groupList[i]?.length != 0) {
          if (reports.fkTableID != null) {
            listData += '''
   {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.paymentType != null) {
            listData += '''
   {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.discountPrice != null) {
            listData += '''
   {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
          }
          for (int list = 0; list < groupList[i]!.length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 20);
            String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(21) + " " + qty.toString().padLeft(10) + " " + double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(13) + " " + double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + " ".padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Sales", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Ref No: ${reports.invoiceId}", "size": 1, "alignment": 0},
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "Customer Name: ${reports.customerName}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total RS. ${double.parse(reports.totalAmount.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray, develop: "Yes80MM");
  }

  printLineBuildFunctionTVS(Sales reports, List<Transactions> salesTransaction) async {
    String listData = "";
    var groupList = salesTransaction.groupListsBy((element) => element.salesId);
    if (salesTransaction.isNotEmpty) {
      for (var i in groupList.keys) {
        // ignore: prefer_is_empty
        if (groupList[i]?.length != 0) {
          if (reports.fkTableID != null) {
            listData += '''
   {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.paymentType != null) {
            listData += '''
   {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.discountPrice != null) {
            listData += '''
   {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
          }
          for (int list = 0; list < groupList[i]!.length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 10);
            String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(11) + " " + qty.toString().padLeft(3) + " " + double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(8) + " " + double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(9)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(11) + " ".padLeft(3) + " ".padLeft(8) + " ".padLeft(9)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Sales", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Ref No: ${reports.invoiceId}", "size": 1, "alignment": 0},
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "Customer Name: ${reports.customerName}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  {"text": "Product  Qty Rate  Amount(RS)", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  {"text": "Total RS. ${double.parse(reports.totalAmount.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray, develop: "Yes");
  }

  printLineBuildFunctionTVS80MM(Sales reports, List<Transactions> salesTransaction) async {
    String listData = "";
    var groupList = salesTransaction.groupListsBy((element) => element.salesId);
    if (salesTransaction.isNotEmpty) {
      for (var i in groupList.keys) {
        // ignore: prefer_is_empty
        if (groupList[i]?.length != 0) {
          if (reports.fkTableID != null) {
            listData += '''
   {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.paymentType != null) {
            listData += '''
   {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
          }
          if (reports.discountPrice != null) {
            listData += '''
   {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
          }
          for (int list = 0; list < groupList[i]!.length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 20);
            String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(21) + " " + qty.toString().padLeft(10) + " " + double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(13) + " " + double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + " ".padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Sales", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Ref No: ${reports.invoiceId}", "size": 1, "alignment": 0},
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "Customer Name: ${reports.customerName}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total RS. ${double.parse(reports.totalAmount.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray, develop: "Yes80MM");
  }

  printLineAccountShortBuildFunction() async {
    double totCredit = 0.0;
    double totDebit = 0.0;
    String listData = "";
    int sno = 0;
    for (SalesType obj in salesList) {
      sno++;
      if (obj.salesType == "debit") {
        listData +=
            '''{"text": "${sno.toString().padRight(5) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(11) + " " + "-".padLeft(11) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(12)}", "size": 0, "alignment": 0},''';
        totDebit += double.parse(obj.amount.toString());
      } else {
        listData +=
            '''{"text": "${sno.toString().padRight(5) + " " + "-".padLeft(11) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(11) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(12)}", "size": 0, "alignment": 0},''';
        totCredit += double.parse(obj.amount.toString());
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Short Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "No    Debit    Credit   Balance", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountShortBuildFunction80MM() async {
    double totCredit = 0.0;
    double totDebit = 0.0;
    String listData = "";
    int sno = 0;
    for (SalesType obj in salesList) {
      sno++;
      if (obj.salesType == "debit") {
        listData +=
            '''{"text": "${sno.toString().padRight(4) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(18) + " " + "-".padLeft(18) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(20)}", "size": 0, "alignment": 0},''';
        totDebit += double.parse(obj.amount.toString());
      } else {
        listData +=
            '''{"text": "${sno.toString().padRight(4) + " " + "-".padLeft(18) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(18) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(20)}", "size": 0, "alignment": 0},''';
        totCredit += double.parse(obj.amount.toString());
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Short Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "No        Debit(RS)   Credit(RS)     Balance(RS)", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountShortBuildFunctionTVS() async {
    double totCredit = 0.0;
    double totDebit = 0.0;
    String listData = "";
    int sno = 0;
    for (SalesType obj in salesList) {
      sno++;
      if (obj.salesType == "debit") {
        listData +=
            '''{"text": "${sno.toString().padRight(5) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(10) + " " + "-".padLeft(10) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(10)}", "size": 0, "alignment": 0},''';
        totDebit += double.parse(obj.amount.toString());
      } else {
        listData +=
            '''{"text": "${sno.toString().padRight(5) + " " + "-".padLeft(10) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(11) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(10)}", "size": 0, "alignment": 0},''';
        totCredit += double.parse(obj.amount.toString());
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Short Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  {"text": "No    Debit   Credit  Balance", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountShortBuildFunctionTVS80MM() async {
    double totCredit = 0.0;
    double totDebit = 0.0;
    String listData = "";
    int sno = 0;
    for (SalesType obj in salesList) {
      sno++;
      if (obj.salesType == "debit") {
        listData +=
            '''{"text": "${sno.toString().padRight(4) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(18) + " " + "-".padLeft(18) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(20)}", "size": 0, "alignment": 0},''';
        totDebit += double.parse(obj.amount.toString());
      } else {
        listData +=
            '''{"text": "${sno.toString().padRight(4) + " " + "-".padLeft(18) + " " + double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(18) + " " + double.parse(obj.balance.toString()).toStringAsFixed(2).padLeft(20)}", "size": 0, "alignment": 0},''';
        totCredit += double.parse(obj.amount.toString());
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Short Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "No        Debit(RS)   Credit(RS)     Balance(RS)", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountFullBuildFunction() async {
    double totDebit = 0.0;
    double totCredit = 0.0;
    String listData = "";
    for (SalesType obj in salesList) {
      List<Transactions> tempTransactionList = salesTransactionList.where((element) => element.salesId == obj.salesID).toList();
      Sales reports = await salesDetailsBySalesID(obj.salesID);
      var groupList = tempTransactionList.groupListsBy((element) => element.salesId);
      if (obj.salesType == "credit") {
        totCredit += double.parse(obj.amount.toString());
        listData += '''
         {"text": "Ref No: ${reports.invoiceId != null ? reports.invoiceId.toString() : ''}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Credit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": " ", "size": 0, "alignment": 0},''';
      } else {
        totDebit += double.parse(obj.amount.toString());
        if (tempTransactionList.isNotEmpty) {
          listData += '''
         {"text": "Ref No: ${reports.invoiceId.toString()}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Debit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
         {"text": "Product    Qty    Rate    Amount", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0}, ''';
          for (var i in groupList.keys) {
            // ignore: prefer_is_empty
            if (groupList[i]?.length != 0) {
              if (reports.fkTableID != null) {
                listData += '''
         {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.paymentType != null) {
                listData += '''
         {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.discountPrice != null) {
                listData += '''
         {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
              }
              for (int list = 0; list < groupList[i]!.length; list++) {
                String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
                var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 9);
                for (int p = 0; p < productSplit.length; p++) {
                  if (p == 0) {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(10)} ${qty.padLeft(9)} ${double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(12)}", "size": 0, "alignment": 0},''';
                  } else {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(10) + "  " + " ".padLeft(5) + "  " + ' '.padLeft(5) + "       " + " ".padLeft(10)}", "size": 0, "alignment": 0},''';
                  }
                }
              }
            }
          }
          listData += '''
            {"text": "Total: ${double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},
            {"text": " ", "size": 0, "alignment": 0},
             ''';
        }
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Full Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "Total Items RS. ${salesTransactionList.length.toString().padLeft(10)}", "size": 1, "alignment": 2},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}

  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountFullBuildFunction80MM() async {
    double totDebit = 0.0;
    double totCredit = 0.0;
    String listData = "";
    for (SalesType obj in salesList) {
      Sales reports = await salesDetailsBySalesID(obj.salesID);
      List<Transactions> tempTransactionList = salesTransactionList.where((element) => element.salesId == obj.salesID).toList();
      var groupList = tempTransactionList.groupListsBy((element) => element.salesId);
      if (obj.salesType == "credit") {
        totCredit += double.parse(obj.amount.toString());
        listData += '''
         {"text": "Ref No: ${reports.invoiceId != null ? reports.invoiceId.toString() : ''}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Credit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": " ", "size": 0, "alignment": 0},''';
      } else {
        totDebit += double.parse(obj.amount.toString());
        if (tempTransactionList.isNotEmpty) {
          listData += '''
         {"text": "Ref No: ${reports.invoiceId.toString()}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Debit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
         {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0}, ''';
          for (var i in groupList.keys) {
            // ignore: prefer_is_empty
            if (groupList[i]?.length != 0) {
              if (reports.fkTableID != null) {
                listData += '''
         {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.paymentType != null) {
                listData += '''
         {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.discountPrice != null) {
                listData += '''
         {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
              }
              for (int list = 0; list < groupList[i]!.length; list++) {
                String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
                var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 20);
                for (int p = 0; p < productSplit.length; p++) {
                  if (p == 0) {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(21)} ${qty.padLeft(10)} ${double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(13)} ${double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
                  } else {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + " ".padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
                  }
                }
              }
            }
          }
          listData += '''
            {"text": "Total: ${double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
            {"text": " ", "size": 0, "alignment": 0},
             ''';
        }
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Full Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total Items RS. ${salesTransactionList.length.toString().padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}

  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountFullBuildFunctionTVS() async {
    double totDebit = 0.0;
    double totCredit = 0.0;
    String listData = "";
    for (SalesType obj in salesList) {
      Sales reports = await salesDetailsBySalesID(obj.salesID);
      List<Transactions> tempTransactionList = salesTransactionList.where((element) => element.salesId == obj.salesID).toList();
      var groupList = tempTransactionList.groupListsBy((element) => element.salesId);
      if (obj.salesType == "credit") {
        totCredit += double.parse(obj.amount.toString());
        listData += '''
         {"text": "Ref No: ${reports.invoiceId != null ? reports.invoiceId.toString() : ''}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Credit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": " ", "size": 0, "alignment": 0},''';
      } else {
        totDebit += double.parse(obj.amount.toString());
        if (tempTransactionList.isNotEmpty) {
          listData += '''
         {"text": "Ref No: ${reports.invoiceId.toString()}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Debit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
         {"text": "Product  Qty Rate  Amount(RS)", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0}, ''';
          for (var i in groupList.keys) {
            // ignore: prefer_is_empty
            if (groupList[i]?.length != 0) {
              if (reports.fkTableID != null) {
                listData += '''
         {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.paymentType != null) {
                listData += '''
         {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.discountPrice != null) {
                listData += '''
         {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
              }
              for (int list = 0; list < groupList[i]!.length; list++) {
                String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
                var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 10);
                for (int p = 0; p < productSplit.length; p++) {
                  if (p == 0) {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(11)} ${qty.padLeft(3)} ${double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(9)}", "size": 0, "alignment": 0},''';
                  } else {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(11) + " ".padLeft(3) + " ".padLeft(8) + " ".padLeft(9)}", "size": 0, "alignment": 0},''';
                  }
                }
              }
            }
          }
          listData += '''
            {"text": "Total: ${double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},
            {"text": " ", "size": 0, "alignment": 0},
             ''';
        }
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Full Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
  {"text": "Total Items RS. ${salesTransactionList.length.toString().padLeft(8)}", "size": 1, "alignment": 2},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}

  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  printLineAccountFullBuildFunctionTVS80MM() async {
    double totDebit = 0.0;
    double totCredit = 0.0;
    String listData = "";
    for (SalesType obj in salesList) {
      Sales reports = await salesDetailsBySalesID(obj.salesID);
      List<Transactions> tempTransactionList = salesTransactionList.where((element) => element.salesId == obj.salesID).toList();
      var groupList = tempTransactionList.groupListsBy((element) => element.salesId);
      if (obj.salesType == "credit") {
        totCredit += double.parse(obj.amount.toString());
        listData += '''
         {"text": "Ref No: ${reports.invoiceId != null ? reports.invoiceId.toString() : ''}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Credit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": " ", "size": 0, "alignment": 0},''';
      } else {
        totDebit += double.parse(obj.amount.toString());
        if (tempTransactionList.isNotEmpty) {
          listData += '''
         {"text": "Ref No: ${reports.invoiceId.toString()}", "size": 1, "alignment": 0},
         {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(obj.createdDate.toString()))}", "size": 1, "alignment": 0},
         {"text": "Debit: ${double.parse(obj.amount.toString()).toStringAsFixed(1)}", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
         {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
         {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0}, ''';
          for (var i in groupList.keys) {
            // ignore: prefer_is_empty
            if (groupList[i]?.length != 0) {
              if (reports.fkTableID != null) {
                listData += '''
         {"text": "Table: ${reports.tableName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.paymentType != null) {
                listData += '''
         {"text": "Payment: ${reports.paymentName.toString()}", "size": 0, "alignment": 0},''';
              }
              if (reports.discountPrice != null) {
                listData += '''
         {"text": "Discount: ${reports.discountPrice.toString()}", "size": 0, "alignment": 0},''';
              }
              for (int list = 0; list < groupList[i]!.length; list++) {
                String qty = fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString());
                var productSplit = printerUtilities.splitStringByLength(groupList[i]![list].name.toString(), 20);
                for (int p = 0; p < productSplit.length; p++) {
                  if (p == 0) {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(21)} ${qty.padLeft(10)} ${double.parse(groupList[i]![list].price.toString()).toStringAsFixed(1).padLeft(13)} ${double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
                  } else {
                    listData +=
                        '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + " ".padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
                  }
                }
              }
            }
          }
          listData += '''
            {"text": "Total: ${double.parse(obj.amount.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
            {"text": " ", "size": 0, "alignment": 0},
             ''';
        }
      }
    }
    String gstNo = "";
    if (customers.gstNo != null) {
      gstNo = '''{"text": "GST No: ${customers.gstNo}", "size": 1, "alignment": 0},''';
    }
    String printArray = '''
  [
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Account Full Report", "size": 1, "alignment": 1},
  {"text": " ", "size": 0, "alignment": 0},
  {"text": "Name: ${customers.firstName} ${customers.lastName} A/C", "size": 1, "alignment": 0},
  {"text": "Mobile No: ${customers.mobileNo}", "size": 1, "alignment": 0},
  $gstNo
  {"text": "Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()))}", "size": 1, "alignment": 0},
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  $listData
  {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
  {"text": "Total Items RS. ${salesTransactionList.length.toString().padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Debit RS. ${double.parse(totDebit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Total Credit RS. ${double.parse(totCredit.toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},
  {"text": "Available Balance RS. ${double.parse((totDebit - totCredit).toString()).toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}

  ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

//   //ACCOUNT PRINTER BUILD END-----------------------------------------------------------------------

//   //ACCOUNT PDF BUILD START ------------------------------------------------------------------------
  pw.Widget pdfShortAccountBodyBuildFunction(String report) {
    double totCredit = 0.0;
    double totDebit = 0.0;
    for (int item = 0; item < salesList.length; item++) {
      if (salesList[item].salesType == "debit") {
        totDebit += double.parse(salesList[item].amount.toString());
      } else {
        totCredit += double.parse(salesList[item].amount.toString());
      }
    }
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Account " + report + " Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Text("${customers.firstName} ${customers.lastName} A/C,", style: pw.TextStyle(fontSize: 15)),
                  (customers.address1 != null && customers.address1 != "") || (customers.address2 != null && customers.address2 != "")
                      ? pw.Text("Address :", style: pw.TextStyle(fontSize: 15))
                      : pw.Container(),
                  customers.address1 != null && customers.address1 != ""
                      ? pw.Text("${customers.address1},", style: pw.TextStyle(fontSize: 15))
                      : pw.Container(),
                  customers.address2 != null && customers.address2 != ""
                      ? pw.Text("${customers.address2}.", style: pw.TextStyle(fontSize: 15))
                      : pw.Container(),
                  pw.Text("+91 ${customers.mobileNo}", style: pw.TextStyle(fontSize: 15)),
                  customers.gstNo != null && customers.gstNo != ""
                      ? pw.Text("GST No : ${customers.gstNo}", style: pw.TextStyle(fontSize: 15))
                      : pw.Container(),
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 150, child: pw.Text("Date", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 100, child: pw.Text("Debit", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 100, child: pw.Text("Credit", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 90, child: pw.Text("Balance", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (salesList.isNotEmpty)
            for (int item = 0; item < salesList.length; item++)
              // ignore: unnecessary_null_comparison
              item != null
                  ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
                      pw.Container(width: 40, child: pw.Text((item + 1).toString(), style: pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                      pw.Container(
                        width: 150,
                        child: salesList[item].createdDate != null
                            ? pw.Text(DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(salesList[item].createdDate.toString())),
                                style: pw.TextStyle(fontSize: 10))
                            : pw.Container(),
                      ),
                      pw.Container(
                          width: 100,
                          child: salesList[item].salesType != null
                              ? pw.Text(
                                  salesList[item].salesType == "debit" && salesList[item].amount != null ? salesList[item].amount.toString() : "-",
                                  style: pw.TextStyle(fontSize: 10),
                                  textAlign: pw.TextAlign.right)
                              : pw.Container()),
                      pw.Container(
                          width: 100,
                          child: salesList[item].salesType != null
                              ? pw.Text(
                                  salesList[item].salesType != "debit" && salesList[item].amount != null ? salesList[item].amount.toString() : "-",
                                  style: pw.TextStyle(fontSize: 10),
                                  textAlign: pw.TextAlign.right)
                              : pw.Container()),
                      pw.Container(
                        width: 90,
                        child: pw.Text(
                            // ignore: unnecessary_null_comparison
                            (salesList[item].balance.toString() != null
                                ? double.parse(salesList[item].balance.toString()).toStringAsFixed(1)
                                : "0.0"),
                            textAlign: pw.TextAlign.right,
                            style: pw.TextStyle(fontSize: 10)),
                      )
                    ])
                  : pw.Container(),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 100,
              child: pw.Text(totDebit.toStringAsFixed(1),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 100,
              child: pw.Text(totCredit.toStringAsFixed(1),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 90,
              child: pw.Text((totDebit - totCredit).toStringAsFixed(1),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 100,
              child: pw.Text("Balance :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(double.parse(balance.toString()).toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfShortBuildFunction(String? currentDate) {
    String fileName = "Account_Short_Report";
    if (currentDate != null) {
      fileName = "${customers.firstName}_${customers.lastName}_" + fileName;
    }
    sharePDF.savePDFFormat(pdfShortAccountBodyBuildFunction("Short"), fileName, currentDate!);
  }

  pw.Widget pdfFullAccountBodyBuildFunction() {
    double finalAmount = 0.0;
    for (SalesType sales in salesList) {
      if (sales.salesType == "debit") {
        finalAmount += double.parse(sales.amount.toString());
      }
    }
    return pw.Column(children: [
      pdfShortAccountBodyBuildFunction("Full"),
      pw.SizedBox(height: 20),
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [pw.Text("Items Full Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
      ),
      pw.SizedBox(height: 10),
      pw.Divider(thickness: 2),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
        pw.Container(width: 40, child: pw.Text("S.No", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        pw.Container(width: 130, child: pw.Text("Product", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        pw.Container(width: 100, child: pw.Text("Qty", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
        pw.Container(width: 80, child: pw.Text("Price", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
        pw.Container(width: 80, child: pw.Text("Amount", style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
      ]),
      pw.Divider(thickness: 2),
      // ignore: sdk_version_ui_as_code
      for (SalesType saleType in salesList) saleType.salesType == "debit" ? pdfFullTransactionBodyBuildFunction(saleType) : pw.Container(),
      pw.Divider(thickness: 2),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 150,
          child: pw.Text("Total Items :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 150,
          child: pw.Text(salesTransactionList.length.toString(),
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 150,
          child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 150,
          child: pw.Text(finalAmount.toStringAsFixed(2),
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
    ]);
  }

  pw.Widget pdfFullTransactionBodyBuildFunction(SalesType saleType) {
    Sales sale = allSalesList.firstWhere((element) => element.salesId == saleType.salesID,);
    var groupList;
    List<Transactions> reportTransactionList = [];
    reportTransactionList = salesTransactionList.where((element) => element.salesId == saleType.salesID).toList();
    groupList = reportTransactionList.groupListsBy((element) => element.salesId);
    return pw.Column(children: [
      // ignore: sdk_version_ui_as_code
      for (var i in groupList.keys)
        pw.Column(children: [
          // pw.SizedBox(height: 20),
          pw.Row(children: [
            pw.Text(
              "Ref No : " + sale.invoiceId!,
              style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
            ),
          ]),
          pw.Row(children: [
            pw.Text("Date : " + DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(saleType.createdDate.toString())),
                style: pw.TextStyle(fontSize: 13))
          ]),
          pw.SizedBox(height: 5),
          sale.fkTableID != null
              ? pw.Padding(
                  padding: pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Table : " + sale.tableName.toString(), style: pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          sale.paymentType != null
              ? pw.Padding(
                  padding: pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Payment : " + sale.paymentName.toString(), style: pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          sale.discountPrice != null
              ? pw.Padding(
                  padding: pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Discount : " + sale.discountPrice.toString(), style: pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          for (int list = 0; list < groupList[i].length; list++)
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
              pw.Container(width: 40, child: pw.Text((list + 1).toString(), style: pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 130, child: pw.Text(groupList[i][list].name.toString(), style: pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 100,
                  child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i][list].qty.toString()),
                      textAlign: pw.TextAlign.center, style: pw.TextStyle(fontSize: 10))),
              pw.Container(
                  width: 80,
                  child: pw.Text(double.parse(groupList[i][list].price.toString()).toStringAsFixed(1),
                      textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10))),
              pw.Container(
                  width: 80,
                  child: pw.Text(double.parse(groupList[i][list].amount.toString()).toStringAsFixed(1),
                      textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 10)))
            ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 70,
              child: pw.Text("Total :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 100,
              child: pw.Text(double.parse(saleType.amount.toString()).toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
        ]),
    ]);
  }

  pdfFullBuildFunction(String? currentDate) {
    String fileName = "Account_Full_Report";
    if (currentDate != null) {
      fileName = "${customers.firstName}_${customers.lastName}_" + fileName;
    }
    sharePDF.savePDFFormat(pdfFullAccountBodyBuildFunction(), fileName, currentDate!);
  }

  pw.Widget salesPDFBuildFunction(Sales reports, List<Transactions> salesTransaction) {
    var groupList = salesTransaction.groupListsBy((element) => element.salesId);
    if (salesTransaction.isNotEmpty) {
      var invoiceExtension = reports.invoiceExtension != null ? "/" + reports.invoiceExtension! : "";
      return pw.Column(
        children: [
          pw.Container(
              alignment: pw.Alignment.center,
              padding: pw.EdgeInsets.symmetric(vertical: 10),
              child: pw.Text("Sales",
                  style: pw.TextStyle(
                    fontSize: 20,
                    decoration: pw.TextDecoration.underline,
                  ))),
          // pw.Row(
          //   mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          //   children: [
          //     reports.invoiceNo != null && !isResetInvoice
          //         ? pw.Text("Bill No: ${reports.invoiceNo}", style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold))
          //         : pw.Container(),
          //     pw.Text("Ref No: ${reports.invoiceId}", style: pw.TextStyle(fontSize: 13, fontWeight: pw.FontWeight.normal)),
          //   ],
          // ),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              reports.paymentInvoice != null && !isResetInvoice
                  ? pw.Text("Bill No: ${reports.paymentInvoice! + invoiceExtension}",
                      style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold))
                  : pw.Container(),
              pw.Text("Ref No: ${reports.invoiceId}", style: pw.TextStyle(fontSize: 13, fontWeight: pw.FontWeight.normal)),
            ],
          ),
          pw.SizedBox(
              width: 500,
              child: pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                pw.Text("Name: " + reports.customerName.toString(), style: pw.TextStyle(fontSize: 18)),
                pw.Text("Date: " + DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(reports.date.toString())),
                    style: pw.TextStyle(fontSize: 18)),
                reports.fkTableID != null ? pw.Text("Table: " + reports.tableName.toString(), style: pw.TextStyle(fontSize: 18)) : pw.Container(),
                reports.paymentType != null
                    ? pw.Text("Payment: " + reports.paymentName.toString(), style: pw.TextStyle(fontSize: 18))
                    : pw.Container(),
                reports.discountPrice != null
                    ? pw.Text("Discount: " + reports.discountPrice.toString(), style: pw.TextStyle(fontSize: 18))
                    : pw.Container()
              ])),
          pw.SizedBox(
            height: 10,
          ),
          pw.Column(mainAxisAlignment: pw.MainAxisAlignment.center, children: [
            pw.Divider(thickness: 2),
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
              pw.Container(width: 180, child: pw.Text("Product", style: pw.TextStyle(fontSize: 20))),
              pw.Container(width: 80, child: pw.Text("Qty", style: pw.TextStyle(fontSize: 20))),
              pw.Container(width: 100, child: pw.Text("Rate", style: pw.TextStyle(fontSize: 20), textAlign: pw.TextAlign.center)),
              pw.Container(width: 100, child: pw.Text("Amount", style: pw.TextStyle(fontSize: 20), textAlign: pw.TextAlign.center))
            ]),
            pw.Divider(thickness: 2),
            // ignore: sdk_version_ui_as_code
            for (var i in groupList.keys)
              // ignore: prefer_is_empty
              if (groupList[i]?.length != 0)
                pw.Column(children: [
                  for (int list = 0; list < groupList[i]!.length; list++)
                    pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
                      pw.Container(width: 180, child: pw.Text(groupList[i]![list].name.toString(), style: pw.TextStyle(fontSize: 15))),
                      pw.Container(
                          width: 80,
                          child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i]![list].qty.toString()), style: pw.TextStyle(fontSize: 15))),
                      pw.Container(
                          width: 100,
                          child: pw.Text(double.parse(groupList[i]![list].price.toString()).toStringAsFixed(2),
                              style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
                      pw.Container(
                          width: 100,
                          child: pw.Text(double.parse(groupList[i]![list].amount.toString()).toStringAsFixed(2),
                              style: pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
                    ]),
                ]),
            pw.Divider(thickness: 2),
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
              pw.Text("Total Rs:", style: pw.TextStyle(fontSize: 20)),
              pw.Text(double.parse(reports.totalAmount.toString()).toStringAsFixed(2), style: pw.TextStyle(fontSize: 20))
            ]),
            pw.SizedBox(
              height: 10,
            )
          ])
        ],
      );
    } else {
      return pw.Container();
    }
  }

  pdfBuildFunction(Sales reports, List<Transactions> salesTransaction, String? currentDate) {
    String fileName = "Sales_Report";
    if (currentDate != null) {
      fileName = "${customers.firstName}_${customers.lastName}_" + fileName;
    }
    sharePDF.savePDFFormat(salesPDFBuildFunction(reports, salesTransaction), fileName, currentDate!);
  }

  //ACCOUNT PDF BUILD END----------------------------------------------------------------------

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  initFunction() async {
    await initSettings();
    await getSalesList();
    await todayFunction();
  }

  initSettings() async {
    var SaleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
    var MultiPrintValue = fnUtilities.workSpaceUserSetValue(key_multiPrint);
    setState(() {
      if (SaleLayout != null) {
        layout = SaleLayout;
      } else {
        layout = "SaleLayout1";
      }
      if (MultiPrintValue == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
        reportPrinter = printerUtilities.getReportPrinter();
        printerUtilities.initPrint(reportPrinter!);
      }
    });
  }

  filterDialog(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
              child: SingleChildScrollView(
                child: AlertDialog(
                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    content: SizedBox(
                      width: double.maxFinite,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.max,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                width: width / 4.8,
                                child: CheckboxListTile(
                                    dense: false,
                                    controlAffinity: ListTileControlAffinity.leading,
                                    contentPadding: EdgeInsets.zero,
                                    value: isAll,
                                    title: Text("All"),
                                    onChanged: (bool? newValue) {
                                      if (!isLock) {
                                        isLock = true;
                                        setState(() {
                                          isAll = newValue ?? false;
                                          if (isAll) {
                                            isDebit = false;
                                            isCredit = false;
                                          }
                                        });
                                        isLock = false;
                                      }
                                    }),
                              ),
                            ],
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              SizedBox(
                                width: width / 3,
                                child: CheckboxListTile(
                                    dense: false,
                                    controlAffinity: ListTileControlAffinity.leading,
                                    contentPadding: EdgeInsets.zero,
                                    value: isDebit,
                                    title: Text("Debit"),
                                    onChanged: (bool? newValue) {
                                      if (!isLock) {
                                        isLock = true;
                                        setState(() {
                                          isDebit = newValue ?? false;
                                          if (isDebit) {
                                            isAll = false;
                                            isCredit = false;
                                          }
                                          if (!isDebit && !isCredit) {
                                            isAll = true;
                                          }
                                        });
                                        isLock = false;
                                      }
                                    }),
                              ),
                              SizedBox(
                                width: width / 3,
                                child: CheckboxListTile(
                                    dense: false,
                                    controlAffinity: ListTileControlAffinity.leading,
                                    contentPadding: EdgeInsets.zero,
                                    value: isCredit,
                                    title: Text("Credit"),
                                    onChanged: (bool? newValue) {
                                      if (!isLock) {
                                        isLock = true;
                                        setState(() {
                                          isCredit = newValue ?? false;
                                          if (isCredit) {
                                            isAll = false;
                                            isDebit = false;
                                          }
                                          if (!isDebit && !isCredit) {
                                            isAll = true;
                                          }
                                        });
                                        isLock = false;
                                      }
                                    }),
                              ),
                            ],
                          ),
                          Divider(
                            thickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              InkWell(
                                onTap: () {
                                  if (!isLock) {
                                    isLock = true;

                                    setState(() {
                                      todayFunction();
                                    });
                                    isLock = false;
                                  }
                                },
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.calendar_view_day,
                                      color: isToday ? Colors.blue : Colors.black,
                                    ),
                                    Text(
                                      'Today',
                                      style: TextStyle(
                                        fontSize: isToday ? 14 : 12.0,
                                      ),
                                    )
                                  ],
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  if (!isLock) {
                                    isLock = true;
                                    setState(() {
                                      isWeekly = true;
                                      isToday = false;
                                      isMonthly = false;
                                      isCustom = false;
                                      queryAllReportsByWeek();
                                    });
                                    isLock = false;
                                  }
                                },
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.calendar_view_week,
                                      color: isWeekly ? Colors.blue : Colors.black,
                                    ),
                                    Text(
                                      'Weekly',
                                      style: TextStyle(fontSize: isWeekly ? 14 : 12.0),
                                    )
                                  ],
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  if (!isLock) {
                                    isLock = true;
                                    setState(() {
                                      isMonthly = true;
                                      isWeekly = false;
                                      isToday = false;
                                      isCustom = false;
                                      queryAllReportsByMonth();
                                    });
                                    isLock = false;
                                  }
                                },
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.calendar_view_month,
                                      color: isMonthly ? Colors.blue : Colors.black,
                                    ),
                                    Text(
                                      'Monthly',
                                      style: TextStyle(fontSize: isMonthly ? 14 : 12.0),
                                    )
                                  ],
                                ),
                              ),
                              InkWell(
                                onTap: () {
                                  if (!isLock) {
                                    isLock = true;
                                    setState(() {
                                      isCustom = true;
                                      isMonthly = false;
                                      isWeekly = false;
                                      isToday = false;
                                    });
                                    isLock = false;
                                  }
                                },
                                child: Column(
                                  children: [
                                    Icon(
                                      Icons.date_range,
                                      color: isCustom ? Colors.blue : Colors.black,
                                    ),
                                    Text(
                                      'Customize',
                                      style: TextStyle(fontSize: isCustom ? 14 : 12.0),
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                          Divider(
                            thickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  Text(DateFormat("dd-MM-yyyy").format(fromDate)),
                                  isCustom
                                      ? IconButton(
                                          onPressed: () async {
                                            if (!isLock) {
                                              isLock = true;
                                              final DateTime? picked = await showDatePicker(
                                                  helpText: 'Select date',
                                                  cancelText: 'Cancel',
                                                  confirmText: "Ok",
                                                  fieldLabelText: 'Selected Date',
                                                  fieldHintText: 'Month/Date/Year',
                                                  errorFormatText: 'Enter valid date',
                                                  errorInvalidText: 'Enter date in valid range',
                                                  context: context,
                                                  initialDate: fromDate,
                                                  firstDate: DateTime(2015, 8),
                                                  lastDate: DateTime(2101));
                                              if (picked != null && picked != fromDate) {
                                                setState(() {
                                                  fromDate = picked;
                                                });
                                              }
                                              isLock = false;
                                            }
                                          },
                                          icon: Icon(Icons.calendar_today))
                                      : Container(),
                                ],
                              ),
                              Row(
                                children: [
                                  Text(DateFormat("dd-MM-yyyy").format(toDate)),
                                  isCustom
                                      ? IconButton(
                                          onPressed: () async {
                                            if (!isLock) {
                                              isLock = true;
                                              final DateTime? picked = await showDatePicker(
                                                  helpText: 'Select date',
                                                  cancelText: 'Cancel',
                                                  confirmText: "Ok",
                                                  fieldLabelText: 'Selected Date',
                                                  fieldHintText: 'Month/Date/Year',
                                                  errorFormatText: 'Enter valid date',
                                                  errorInvalidText: 'Enter date in valid range',
                                                  context: context,
                                                  initialDate: toDate,
                                                  firstDate: DateTime(2015, 8),
                                                  lastDate: DateTime(2101));
                                              if (picked != null && picked != toDate) {
                                                setState(() {
                                                  toDate = picked;
                                                });
                                              }
                                              isLock = false;
                                            }
                                          },
                                          icon: Icon(Icons.calendar_today))
                                      : Container(),
                                ],
                              )
                            ],
                          ),
                          Divider(
                            thickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: <Widget>[
                              InkWell(
                                onTap: () async {
                                  if (!isLock) {
                                    isLock = true;
                                    isAll = true;
                                    isDebit = false;
                                    isCredit = false;
                                    isCustom = false;
                                    pageNo = 1;
                                    await todayFunction();
                                    filterSalesList.clear();
                                    await pageNavigation();
                                    Navigator.pop(context);
                                    isLock = false;
                                  }
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 3.5,
                                  child: Text(
                                    "Reset",
                                    style: white14BoldTextStyle,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    color: buttonThemeColor,
                                  ),
                                ),
                              ),
                              InkWell(
                                onTap: () async {
                                  if (!isLock) {
                                    // isLock = true;
                                    // AccountFilter model = AccountFilter();
                                    // model.customerID = customers!.customerID;
                                    // model.isAll = isAll;
                                    // model.isCredit = isCredit;
                                    // model.isDebit = isDebit;
                                    // model.fromDate = fromDate.toString();
                                    // model.toDate = toDate.toString();
                                    // pageNo = 1;
                                    // await filterFunction(model);
                                    // Navigator.pop(context);
                                    // isLock = false;
                                  }
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 3.5,
                                  child: Text(
                                    "Filter",
                                    style: white14BoldTextStyle,
                                  ),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(5),
                                    color: buttonThemeColor,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )),
              ),
            );
          });
        });
  }

  iconTopWidget() {
    return Row(
      children: [
        PopupMenuButton(
          icon: Icon(Icons.share),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(41)) {
                          if (!isLock) {
                            isLock = true;
                            Navigator.pop(context);
                            await pdfShortBuildFunction(null);
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Short PDF Report"),
                      ))),
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(41)) {
                          if (!isLock) {
                            isLock = true;
                            await queryAllSalesTransactionForExpand(null);
                            Navigator.pop(context);
                            await pdfFullBuildFunction(null);
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Full PDF Report"),
                      ))),
            ];
          },
        ),
        PopupMenuButton(
          icon: Icon(Icons.download),
          itemBuilder: (BuildContext context) {
            isLock = false;
            return <PopupMenuEntry>[
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(42)) {
                          if (!isLock) {
                            isLock = true;
                            Navigator.pop(context);
                            await pdfShortBuildFunction(DateTime.now().toString());
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Short PDF Report"),
                      ))),
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(42)) {
                          if (!isLock) {
                            isLock = true;
                            await queryAllSalesTransactionForExpand(null);
                            Navigator.pop(context);
                            await pdfFullBuildFunction(DateTime.now().toString());
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Full PDF Report"),
                      ))),
            ];
          },
        ),
        PopupMenuButton(
          icon: Icon(Icons.print),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(43)) {
                          if (!isLock) {
                            isLock = true;
                            Navigator.pop(context);
                            if (fnUtilities.workSpaceDeviceSetValue(key_printFormat) == "Default") {
                              if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                                await printLineAccountShortBuildFunction();
                              } else {
                                await printLineAccountShortBuildFunction80MM();
                              }
                            } else {
                              if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                                await printLineAccountShortBuildFunctionTVS();
                              } else {
                                await printLineAccountShortBuildFunctionTVS80MM();
                              }
                            }
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Short Report"),
                      ))),
              PopupMenuItem(
                  padding: EdgeInsets.zero,
                  child: InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(43)) {
                          if (!isLock) {
                            isLock = true;
                            await queryAllSalesTransactionForExpand(null);
                            Navigator.pop(context);
                            if (fnUtilities.workSpaceDeviceSetValue(key_printFormat) == "Default") {
                              if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                                await printLineAccountFullBuildFunction();
                              } else {
                                await printLineAccountFullBuildFunction80MM();
                              }
                            } else {
                              if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                                await printLineAccountFullBuildFunctionTVS();
                              } else {
                                await printLineAccountFullBuildFunctionTVS80MM();
                              }
                            }
                            isLock = false;
                          }
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.fromLTRB(20, 10, 50, 10),
                        child: Text("Full Report"),
                      ))),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    //double height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        appBar: AppBar(),
        title: text(
          "Account",
          textColor: secondaryTextColor,
          fontSize: 20.0,
        ),
        widgets: [salesNavigationList.isNotEmpty ? iconTopWidget() : Container()],
      ),
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: Container(
          width: width,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
          child: Padding(
            padding: const EdgeInsets.only(top: 10.0, left: 10, right: 10, bottom: 10),
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        children: [
                          SizedBox(
                            width: width / 2.5,
                            child: Text(
                              "${customers.firstName ?? ""} ${customers.lastName ?? ""} A/C",
                              style: black14BoldTextStyle,
                            ),
                          ),
                          Container(
                            width: width / 2.5,
                            child: Text(
                              "${customers.address1 != null && customers.address1 != "" ? customers.address1! + "," : ""} ${customers.address2 != null && customers.address2 != "" ? customers.address2 : ""}",
                            ),
                          ),
                          const SizedBox(
                            height: 5,
                          ),
                          Container(
                            width: width / 2.5,
                            child: Text(
                              "${customers.mobileNo ?? ""}",
                            ),
                          ),
                          customers.gstNo != null
                              ? Container(
                                  width: width / 2.5,
                                  child: Text(
                                    "${customers.gstNo?.toUpperCase()}",
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                      Column(
                        children: [
                          SizedBox(
                            child: Text(
                              "Available Balance ",
                              textAlign: TextAlign.right,
                              style: black14BoldTextStyle,
                            ),
                            width: width / 2.5,
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Container(
                            child: Text(
                              ("₹ ${double.parse(balance.toString()).toStringAsFixed(2)}"),
                              textAlign: TextAlign.right,
                              style: black14RegularTextStyle,
                            ),
                            width: width / 2.5,
                          ),
                        ],
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      // InkWell(
                      //   onTap: () {
                      //     // Navigator.push(
                      //     //     context,
                      //     //     MaterialPageRoute(
                      //     //         builder: (context) => Sales(
                      //     //               customers: customers,
                      //     //             ))).then((value) async {
                      //     //   await getSalesList();
                      //     //   setState(() {
                      //     //     isExpanded = false;
                      //     //     salesID = null;
                      //     //   });
                      //     // });
                      //   },
                      //   child: Container(
                      //     decoration: BoxDecoration(
                      //       borderRadius: BorderRadius.circular(5),
                      //       color: buttonThemeColor,
                      //     ),
                      //     padding:
                      //         EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                      //     child: Row(
                      //       children: [
                      //         Text(
                      //           "New",
                      //           style: white14BoldTextStyle,
                      //         ),
                      //         Icon(
                      //           Icons.add,
                      //           color: whiteColor,
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                      // const SizedBox(
                      //   width: 10,
                      // ),
                      InkWell(
                        onTap: () {
                          if (PermissionFunctions.checkPermission(44)) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => CollectBill(
                                          customers: customers,
                                        ))).then((value) async {
                              await getSalesList();
                            });
                          }
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: buttonThemeColor,
                          ),
                          padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                          child: Row(
                            children: [
                              Text(
                                "Collect",
                                style: white14BoldTextStyle,
                              ),
                              Icon(
                                Icons.check,
                                color: whiteColor,
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 3,
                  ),
                  //reportHeader(),
                  salesList.isNotEmpty
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            // IconButton(
                            //     onPressed: () async {
                            //       if (!isLock) {
                            //         isLock = true;
                            //         await queryAllSalesTransactionForExpand(null);
                            //         setState(() {
                            //           salesID = null;
                            //           sales = null;
                            //           if (isExpanded == false) {
                            //             isExpanded = true;
                            //           } else {
                            //             isExpanded = false;
                            //           }
                            //         });
                            //         isLock = false;
                            //       }
                            //     },
                            //     icon: Icon(Icons.expand_outlined)),
                            // IconButton(
                            //     onPressed: () {
                            //       filterDialog(context);
                            //     },
                            //     icon: Icon(Icons.filter_alt_outlined)),
                            IconButton(
                                onPressed: () async {
                                  if (pageNo > 1) {
                                    setState(() {
                                      pageNo = pageNo - 1;
                                      pageNext = pageNext - 10;
                                    });
                                    await pageNavigation();
                                  }
                                },
                                icon: Icon(Icons.chevron_left)),
                            Text(pageNo.toString()),
                            IconButton(
                                onPressed: () async {
                                  int pageCount;
                                  if (filterSalesList.isEmpty) {
                                    pageCount = salesList.length;
                                  } else {
                                    pageCount = filterSalesList.length;
                                  }
                                  if (pageNext < pageCount) {
                                    setState(() {
                                      pageNo = pageNo + 1;
                                      pageNext = pageNext + 10;
                                    });
                                    await pageNavigation();
                                  }
                                },
                                icon: Icon(Icons.chevron_right))
                          ],
                        )
                      : Container(),
                  const SizedBox(
                    height: 3,
                  ),
                  reportList()
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget reportHeader() {
    double width = MediaQuery.of(context).size.width;
    return InkWell(
      onLongPress: () {},
      child: Card(
        child: Container(
          height: 40,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              Container(
                child: Text(
                  "Date",
                  textAlign: TextAlign.start,
                  style: black14BoldTextStyle,
                ),
                width: width / 4.5,
              ),
              Container(
                child: Text(
                  "Notes",
                  textAlign: TextAlign.start,
                  style: black14BoldTextStyle,
                ),
                width: width / 4.5,
              ),
              Container(
                child: Text(
                  "Debit",
                  textAlign: TextAlign.start,
                  style: black14BoldTextStyle,
                ),
                width: width / 9.5,
              ),
              Container(
                child: Text(
                  "Credit",
                  textAlign: TextAlign.start,
                  style: black14BoldTextStyle,
                ),
                width: width / 9.5,
              ),
              Container(
                child: Text(
                  "Balance",
                  textAlign: TextAlign.start,
                  style: black14BoldTextStyle,
                ),
                width: width / 7,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget reportList() {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return Container(
      padding: EdgeInsets.only(bottom: 5),
      height: height / 1.5,
      child: salesNavigationList.isNotEmpty
          ? ListView.builder(
              scrollDirection: Axis.vertical,
              itemCount: salesNavigationList.length,
              shrinkWrap: true,
              physics: const ScrollPhysics(),
              padding: EdgeInsets.only(right: 0),
              reverse: true,
              itemBuilder: (context, index) {
                var item = salesNavigationList[index];
                return InkWell(
                  onDoubleTap: () async {
                    if (await PermissionFunctions.checkPermission(45)) {
                      if (item.salesType == "credit") {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => CollectBill(
                                      customers: customers,
                                      sales: item,
                                    ))).then((value) async {
                          await getSalesList();
                        });
                      } else {
                        Sales sale = await salesDetailsBySalesID(item.salesID);
                        await queryAllSalesTransactionForExpand(item.salesID);
                        List<Transactions> reportTransactionList = [];
                        reportTransactionList = salesTransactionList.where((element) => element.salesId == item.salesID).toList();
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => layout == "SaleLayout2"
                                    ? ItemsPageLayout2(sales: sale, saleTransactionList: reportTransactionList)
                                    : SalesMainPage(sales: sale, saleTransactionList: reportTransactionList))).then((value) async {
                          await getSalesList();
                          setState(() {
                            isExpanded = false;
                            salesID = null;
                            sales = null;
                          });
                        });
                      }
                    } 
                  },
                  onTap: () async {
                    if (!isLock) {
                      isLock = true;
                      Sales sale = await salesDetailsBySalesID(item.salesID);
                      if (salesID == null && item.salesType == 'debit') {
                        await queryAllSalesTransactionForExpand(item.salesID);
                        setState(() {
                          salesID = item.salesID;
                          sales = sale;
                          isExpanded = false;
                        });
                      } else if (salesID != item.salesID && item.salesType == 'debit' && salesID != null) {
                        await queryAllSalesTransactionForExpand(item.salesID);
                        setState(() {
                          salesID = item.salesID;
                          sales = sale;
                          isExpanded = false;
                        });
                      } else {
                        setState(() {
                          salesID = null;
                          sales = null;
                          isExpanded = false;
                        });
                      }
                      isLock = false;
                    }
                  },
                  onLongPress: () async {
                    if (PermissionFunctions.checkPermission(46)) {
                      Sales sale = await salesDetailsBySalesID(item.salesID);
                      deleteDialog(context, "Are you sure, you want to delete?", sale, item);
                    }
                  },
                  child: Card(
                    child: Container(
                      padding: EdgeInsets.symmetric(horizontal: 2, vertical: 8),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                child: Text(
                                  DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(item.createdDate.toString())),
                                  textAlign: TextAlign.left,
                                  style: black14BoldTextStyle,
                                ),
                                width: width / 2.3,
                              ),
                              Row(
                                children: [
                                  Container(
                                    child: Text(
                                      "${double.parse(item.amount.toString()).toStringAsFixed(1)} ",
                                      textAlign: TextAlign.right,
                                      style: black16BoldTextStyle,
                                    ),
                                    //width: width / 2.7,
                                  ),
                                  item.salesType == "credit"
                                      ? Icon(
                                          Icons.call_received_sharp,
                                          color: Colors.green,
                                        )
                                      : Icon(
                                          Icons.call_made_sharp,
                                          color: Colors.red,
                                        )
                                ],
                              )
                            ],
                          ),
                          SizedBox(
                            height: 5,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Container(
                                child: Text(
                                  "",
                                  // item.description != null
                                  //     ? item.description.toString()
                                  //     : "",
                                  textAlign: TextAlign.left,
                                  style: black14RegularTextStyle,
                                ),
                                width: width / 2.5,
                              ),
                              Container(
                                child: Text(
                                  "Balance: " +
                                      // ignore: unnecessary_null_comparison
                                      ("₹ " + item.balance.toString() != null ? double.parse(item.balance.toString()).toStringAsFixed(1) : "0.0"),
                                  textAlign: TextAlign.right,
                                  style: black14RegularTextStyle,
                                ),
                                width: width / 2.5,
                              ),
                            ],
                          ),
                          (salesID == item.salesID && item.salesType == 'debit') || (isExpanded && item.salesType == 'debit')
                              ? Column(
                                  children: [
                                    sales?.fkTableID != null
                                        ? Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              if(sales?.tableName != null)...[
                                                TextWidget("Table :  ", textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              TextWidget("${sales!.tableName}", textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              ]
                                            ],
                                          )
                                        : Container(),
                                    sales?.paymentType != null
                                        ? Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              if(sales?.paymentName != null)...[
                                                TextWidget("Payment :  ", textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              TextWidget("${sales!.paymentName}", textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              ]
                                            ],
                                          )
                                        : Container(),
                                    sales?.discountPrice != null
                                        ? Row(
                                            mainAxisAlignment: MainAxisAlignment.start,
                                            children: [
                                              if(sales?.discountPrice != null)...[
                                                TextWidget("Discount :  ", textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              TextWidget("\u{20B9}${sales!.discountPrice}",
                                                  textColor: primaryTextColor, fontSize: 13.0, isCentered: true),
                                              ]
                                            ],
                                          )
                                        : Container(),
                                    SizedBox(
                                      height: 10,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          child: Text(
                                            "Product",
                                            textAlign: TextAlign.left,
                                            style: black16BoldTextStyle,
                                          ),
                                          width: width / 3,
                                        ),
                                        Container(
                                          child: Text(
                                            "Qty",
                                            textAlign: TextAlign.left,
                                            style: black16BoldTextStyle,
                                          ),
                                          width: width / 5,
                                        ),
                                        Container(
                                          child: Text(
                                            "Amount(Rs)",
                                            textAlign: TextAlign.right,
                                            style: black16BoldTextStyle,
                                          ),
                                          width: width / 4,
                                        ),
                                      ],
                                    ),
                                    !isLoading ? transactionReportList(item) : CircularProgressIndicator(),
                                  ],
                                )
                              : Container(),
                        ],
                      ),
                    ),
                  ),
                );
              },
            )
          : Center(
              child: Text("No Transaction "),
            ),
    );
  }

  Widget transactionReportList(SalesType item) {
    List<Transactions> reportTransactionList = [];
    reportTransactionList = salesTransactionList.where((element) => element.salesId == item.salesID).toList();
    var groupList = reportTransactionList.groupListsBy((element) => item.salesID);
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
    return reportTransactionList.isNotEmpty
        ? Column(
            children: [
              // ignore: sdk_version_ui_as_code
              if (reportTransactionList.isNotEmpty)
                for (var i in groupList.keys)
                  // ignore: prefer_is_empty
                  groupList[i]?.length != 0
                      ? Column(
                          children: [
                            // groupList[i]?[0].fkVehicleID != null
                            //     ? Padding(
                            //         padding:
                            //             const EdgeInsets.symmetric(vertical: 8),
                            //         child: Container(
                            //           alignment: Alignment.centerLeft,
                            //           child: Text(
                            //             groupList[i]![0]
                            //                 .salesVehicle
                            //                 .toString(),
                            //             style: black14BoldTextStyle,
                            //           ),
                            //         ),
                            //       )
                            //     : Container(),
                            ListView.builder(
                                scrollDirection: Axis.vertical,
                                itemCount: groupList[i]?.length,
                                shrinkWrap: true,
                                physics: const ScrollPhysics(),
                                padding: EdgeInsets.all(5),
                                reverse: true,
                                itemBuilder: (context, index) {
                                  var item1 = groupList[i]![index];
                                  return Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Container(
                                        child: Text(
                                          item1.name.toString(),
                                          textAlign: TextAlign.left,
                                          style: black14RegularTextStyle,
                                        ),
                                        width: width / 3,
                                        // height: 20,
                                      ),
                                      Container(
                                        child: Text(
                                          fnUtilities.checkQtyDoubleValue(item1.qty.toString()),
                                          textAlign: TextAlign.left,
                                          style: black14RegularTextStyle,
                                        ),
                                        width: width / 5,
                                      ),
                                      Container(
                                        child: Text(
                                          double.parse(item1.amount.toString()).toStringAsFixed(1),
                                          textAlign: TextAlign.right,
                                          style: black14RegularTextStyle,
                                        ),
                                        width: width / 4,
                                      ),
                                    ],
                                  );
                                }),
                            SizedBox(
                              height: 1,
                            ),
                          ],
                        )
                      : Container(),
              Container(
                height: 30,
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(20), color: whiteColor, boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.2),
                    blurRadius: 10.0,
                  ),
                ]),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.print,
                        color: Colors.blue,
                        size: 20,
                      ),
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;
                          if (fnUtilities.workSpaceDeviceSetValue(key_printFormat) == "Default") {
                            if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                              await printLineBuildFunction(sales!, reportTransactionList);
                            } else {
                              await printLineBuildFunction80MM(sales!, reportTransactionList);
                            }
                          } else {
                            if (fnUtilities.workSpaceDeviceSetValue(key_printerPaperSize) == "58") {
                              await printLineBuildFunctionTVS(sales!, reportTransactionList);
                            } else {
                              await printLineBuildFunctionTVS80MM(sales!, reportTransactionList);
                            }
                          }
                          isLock = false;
                        }
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.share,
                        color: Colors.brown,
                        size: 20,
                      ),
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;
                          await pdfBuildFunction(sales!, reportTransactionList, null);
                          isLock = false;
                        }
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.download,
                        color: Colors.green,
                        size: 20,
                      ),
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;
                          await pdfBuildFunction(sales!, reportTransactionList, DateTime.now().toString());
                          isLock = false;
                        }
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.edit,
                        color: Colors.blue,
                        size: 20,
                      ),
                      onPressed: () async {
                        if (await PermissionFunctions.checkPermission(41)) {
                          Sales sale = await salesDetailsBySalesID(salesID);
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => layout == "SaleLayout2"
                                      ? ItemsPageLayout2(sales: sale, saleTransactionList: reportTransactionList)
                                      : SalesMainPage(sales: sale, saleTransactionList: reportTransactionList))).then((value) async {
                            await getSalesList();
                            setState(() {
                              isExpanded = false;
                              salesID = null;
                            });
                          });
                        } 
                      },
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.delete,
                        color: Colors.red,
                        size: 20,
                      ),
                      onPressed: () async {
                        if (await await PermissionFunctions.checkPermission(46)) {
                          Sales sale = await salesDetailsBySalesID(salesID);
                          deleteDialog(context, "Are you sure, you want to delete?", sale, item);
                        } else {
                          showToast("You don't have a permission");
                        }
                      },
                    )
                  ],
                ),
              ),
            ],
          )
        : Container();
  }

  deleteFunction(Sales item, SalesType salesType) async {
    //final syncNo = await salesDetailsDB.queryGetSalesSynccount(item.salesId, salesType.salesType!);
    if (salesType.salesType == 'debit') {
      await salesDetailsDBScript.updateSalesDelete(salesType.salesID!);
      await salesTransDBScript.updateSalesTransactionDelete(salesType.salesID!);
    }
    salesType.rowStatus = 2;
    salesType.syncStatus = 0;
    await salesTypeDB.updateSalesTypeSync(salesType);
    // }
  }

  deleteDialog(BuildContext buildContext, String msgText, Sales item, SalesType salesType) {
    double height = MediaQuery.of(context).size.height;
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Container(
            height: height / 2,
            child: AlertDialog(
              content: Text(
                msgText,
              ),
              actions: [
                InkWell(
                  onTap: () async {
                    Balance balance = Balance();
                    balance.customerID = salesType.customerID;
                    balance.balance = "-${salesType.amount}";
                    await deleteFunction(item, salesType);
                    await fnUtilities.insertOrUpdateBalance(balance, salesType.salesType!);
                    await getSalesList();
                    Navigator.pop(context, 'true');
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: Text("Yes"),
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: Text("No"),
                  ),
                ),
              ],
            ),
          );
        });
  }
}
