// ignore_for_file: no_logic_in_create_state, prefer_typing_uninitialized_variables

import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:share/share.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/dbutils/customers_db_script.dart';
import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/screens/customers/add_customers.dart';
import 'package:smartbill/utils/common_widgets/custom_dropdown.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import 'customer_account.dart';

class CustomerPage extends StatefulWidget {
  final pageType;
  const CustomerPage({Key? key, this.pageType}) : super(key: key);

  @override
  _CustomerPageState createState() => _CustomerPageState(pageType);
}

class _CustomerPageState extends State<CustomerPage> {
  CustomersDBScript customerDB = CustomersDBScript();
  DBOperations funCommonDB = DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  SyncUtility syncUtility = new SyncUtility();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  CommonDB commonDB = CommonDB();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  // CommonDBFunction funCommonDB = CommonDBFunction();
  // PermissionFunction permission = PermissionFunction();
  // WorkspaceDBScript workspaceDB = WorkspaceDBScript();
  BalanceDBScript balanceDB = BalanceDBScript();
  final searchController = TextEditingController();
  DropDown? selectArea;
  List<Customer> customersList = [];
  List<Customer> storeCustomersList = [];
  List<Area> areaList = [];
  FocusNode? myFocusNode;
  String? pageType;
  double width = 0.0;
  _CustomerPageState(this.pageType);

  // getSalesList(Customer customers) async {
  //   double balance = 0.0;
  //   if (customers.exitBalance.toString()[0] == '-' ||
  //       customers.exitBalance.toString()[0] == '+') {
  //     balance = double.parse(customers.exitBalance.toString());
  //   } else if (customers.exitBalance != null) {
  //     balance = double.parse(customers.exitBalance.toString());
  //   }
  //   List<Sales> tempSalesList = [];
  //   tempSalesList = await funCommonDB
  //       .selectSalesCustomerByID(customers.customerID.toString());
  //   for (Sales b in tempSalesList) {
  //     if (b.salesType == "credit") {
  //       balance = (balance - double.parse(b.totalAmount.toString()));
  //     } else {
  //       balance = (balance + double.parse(b.totalAmount.toString()));
  //     }
  //   }
  //   return balance.toString();
  // }

  setStateForBalanceFunction() {
    setState(() {
      storeCustomersList.clear();
      for (Customer obj in customersList) {
        obj.balance = obj.balance;
      }
      storeCustomersList.addAll(customersList);
    });
  }

  getCustomersDetails() async {
    await selectBalanceDetails();
    final allRows = await customerDB.selectAllCustomers();
    setState(() {
      customersList.clear();
      for (var row in allRows) {
        customersList.add(Customer.fromMap(row));
      }
      for (Customer obj in customersList) {
        try {
          obj.areaName = areaList.singleWhere((item) => item.areaID == obj.fkAreaID, orElse: () => Area()).areaName ?? "";
        } catch (e) {
          obj.areaName = '';
        }
      }
    });
    finalBalance = 0.0;
    for (Customer obj in customersList) {
      obj.balance = balanceList.isNotEmpty
          ? balanceList.firstWhere((element) => element.customerID == obj.customerID, orElse: () => Balance()).balance ?? "0.0"
          : "0.0";
      if (obj.balance != null) {
        finalBalance += double.parse(obj.balance.toString());
      } else {
        obj.balance = "0.0";
      }
    }
    await setStateForBalanceFunction();
  }

  double finalBalance = 0.0;

  getCustomersDetailsByArea(String? area) async {
    final allRows = await customerDB.selectAllCustomersByArea(area ?? "");
    setState(() {
      customersList.clear();
      for (var row in allRows) {
        customersList.add(Customer.fromMap(row));
      }
      for (Customer obj in customersList) {
        try {
          obj.areaName = areaList.singleWhere((item) => item.areaID == obj.fkAreaID, orElse: () => Area()).areaName ?? "";
        } catch (e) {
          obj.areaName = '';
        }
      }
    });
    for (Customer obj in customersList) {
      obj.balance = balanceList.isNotEmpty ? balanceList.firstWhere((element) => element.customerID == obj.customerID).balance : "0.0";
      if (obj.balance == null) {
        obj.balance = "0.0";
      }
    }
    await setStateForBalanceFunction();
  }

  List<Balance> balanceList = [];
  selectBalanceDetails() async {
    final allRows = await balanceDB.selectAllBalance();
    balanceList.clear();
    for (var row in allRows) {
      balanceList.add(Balance.fromMap(row));
    }
  }

  getAreaList() async {
    List<Area> tempAreaList = [];
    tempAreaList = await funCommonDB.getAllAreaFromDB();
    areaList.clear();
    setState(() {
      Area model = Area();
      model.areaName = "All";
      tempAreaList.add(model);
      areaList = tempAreaList;
    });
    await areaConvertFunction();
    // await optionConvertFunction();
  }

  void filterFunction(String name) async {
    if (name != "") {
      setState(() {
        customersList.clear();
        for (Customer p in storeCustomersList) {
          if ((p.firstName! + p.lastName.toString()).toLowerCase().contains(name.toLowerCase()) ||
              (p.firstName! + ' ' + p.lastName.toString()).toLowerCase().contains(name.toLowerCase()) ||
              p.mobileNo!.toLowerCase().contains(name.toLowerCase())) {
            customersList.add(p);
          }
        }
      });
    } else {
      setState(() {
        customersList.clear();
        customersList.addAll(storeCustomersList);
      });
    }
  }

  // void customerSearchFunction(String search) async {
  //   final allRows =
  //       await customerDB.selectCustomerByOptionSearch(option?.id, search);
  //   setState(() {
  //     customersList.clear();
  //     for (var row in allRows) {
  //       customersList.add(Customer.fromMap(row));
  //     }
  //     for (Customer obj in customersList) {
  //       try {
  //         obj.areaName = areaList
  //             .singleWhere((item) => item.areaID == obj.fkAreaID,
  //                 orElse: () => null as Area)
  //             .areaName;
  //       } catch (e) {
  //         obj.areaName = '';
  //       }
  //     }
  //   });
  //   for (Customer obj in customersList) {
  //     obj.balance = await getSalesList(obj);
  //   }
  //   await setStateForBalanceFunction();
  // }

  @override
  void initState() {
    initFunction();
    myFocusNode = FocusNode();
    super.initState();
  }

  initFunction() async {
    // await syncFunction();
    await getAreaList();
    await getCustomersDetails();
  }

  bool isLoading = false;
  syncFunction() async {
    if (pageType != "Sale") {
      setState(() {
        isLoading = true;
      });
      if (await InternetConnectionChecker().hasConnection) {
        await syncUtility.triggerManualSync();
      } else {
        fnUtilities.internetTurnOnAlertDialog(context);
      }
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    // Clean up the focus node when the Form is disposed.
    //myFocusNode.dispose();

    super.dispose();
  }

  deleteDialog(BuildContext buildContext, Customer customer) {
    double height = MediaQuery.of(context).size.height;
    return showDialog(
        context: buildContext,
        builder: (context) {
          return SizedBox(
            height: height / 2,
            child: AlertDialog(
              content: Text("Are your sure, you want to delete " + customer.firstName.toString()),
              actions: [
                RoundedButton(
                    title: "Yes",
                    onPressed: () async {
                      // try {
                      if (await commonDB.checkNonSyncCommonFunction(
                              "CUSTOMERS", "CustomerID", customer.customerID != null ? customer.customerID.toString() : "", "SyncStatus") ==
                          0) {
                        customer.rowStatus = 2;
                        customer.syncStatus = 0;
                        await customerDB.deleteWithUpdateCustomer(customer);
                      } else {
                        await commonDB.permanentDeleteCommonFunction("CUSTOMERS", "CustomerID", customer.customerID.toString());
                      }
                      await getCustomersDetails();
                      Navigator.pop(context);
                    }),
                SizedBox(
                  height: height * 0.01,
                ),
                RoundedButton(
                  title: "No",
                  onPressed: () {
                    Navigator.pop(context);
                  },
                )
              ],
            ),
          );
        });
  }

  List<DropDown> dropdownList = [];
  areaConvertFunction() {
    dropdownList.clear();
    for (Area obj in areaList) {
      DropDown model = DropDown();
      model.id = obj.areaID;
      model.name = obj.areaName;
      dropdownList.add(model);
    }
  }

  // List<DropDown> dropdownOptionList = [];
  // optionConvertFunction() {
  //   dropdownOptionList.clear();
  //   DropDown model = DropDown();
  //   model.id = "Mobile No";
  //   model.name = "Mobile No";
  //   dropdownOptionList.add(model);
  //   DropDown model1 = DropDown();
  //   model1.id = "Name";
  //   model1.name = "Name";
  //   dropdownOptionList.add(model1);
  // }

  filterDialog(BuildContext context) {
    selectArea = selectArea;
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
              child: SingleChildScrollView(
                child: AlertDialog(
                    contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                    content: SizedBox(
                      // width: double.maxFinite,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        // crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Text(
                                'Select',
                                style: black15BoldTextStyle,
                              ),
                              SizedBox(
                                width: width > tabletWidth ? width * 0.20 : width * 0.45,
                                child: CommonDropDown(
                                  hintText: 'Area',
                                  dropDownList: dropdownList,
                                  dropDown: selectArea,
                                  isExpanded: true,
                                  isDense: true,
                                  onChanged: (DropDown? newValue) async {
                                    setState(() {
                                      FocusScope.of(context).requestFocus(FocusNode());
                                      selectArea = newValue;
                                    });
                                  },
                                ),
                              )
                            ],
                          ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     SizedBox(
                          //       width: width / 4.8,
                          //       child: CheckboxListTile(
                          //           dense: false,
                          //           controlAffinity:
                          //           ListTileControlAffinity.leading,
                          //           contentPadding: EdgeInsets.zero,
                          //           value: isAll,
                          //           title: Text("All"),
                          //           onChanged: (bool? newValue) {
                          //             if (!isLock) {
                          //               isLock = true;
                          //               setState(() {
                          //                 isAll = newValue!;
                          //                 if (isAll) {
                          //                   isDebit = false;
                          //                   isCredit = false;
                          //                 }
                          //               });
                          //               isLock = false;
                          //             }
                          //           }),
                          //     ),
                          //   ],
                          // ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     SizedBox(
                          //       width: width / 3,
                          //       child: CheckboxListTile(
                          //           dense: false,
                          //           controlAffinity:
                          //           ListTileControlAffinity.leading,
                          //           contentPadding: EdgeInsets.zero,
                          //           value: isDebit,
                          //           title: Text("Debit"),
                          //           onChanged: (bool? newValue) {
                          //             if (!isLock) {
                          //               isLock = true;
                          //               setState(() {
                          //                 isDebit = newValue!;
                          //                 if (isDebit) {
                          //                   isAll = false;
                          //                   isCredit = false;
                          //                 }
                          //                 if (!isDebit && !isCredit) {
                          //                   isAll = true;
                          //                 }
                          //               });
                          //               isLock = false;
                          //             }
                          //           }),
                          //     ),
                          //     SizedBox(
                          //       width: width / 3,
                          //       child: CheckboxListTile(
                          //           dense: false,
                          //           controlAffinity:
                          //           ListTileControlAffinity.leading,
                          //           contentPadding: EdgeInsets.zero,
                          //           value: isCredit,
                          //           title: Text("Credit"),
                          //           onChanged: (bool? newValue) {
                          //             if (!isLock) {
                          //               isLock = true;
                          //               setState(() {
                          //                 isCredit = newValue!;
                          //                 if (isCredit) {
                          //                   isAll = false;
                          //                   isDebit = false;
                          //                 }
                          //                 if (!isDebit && !isCredit) {
                          //                   isAll = true;
                          //                 }
                          //               });
                          //               isLock = false;
                          //             }
                          //           }),
                          //     ),
                          //   ],
                          // ),
                          // Divider(
                          //   thickness: 1,
                          // ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     InkWell(
                          //       onTap: () {
                          //         if (!isLock) {
                          //           isLock = true;
                          //
                          //           setState(() {
                          //             todayFunction();
                          //           });
                          //           isLock = false;
                          //         }
                          //       },
                          //       child: Column(
                          //         children: [
                          //           Icon(
                          //             Icons.calendar_view_day,
                          //             color:
                          //             isToday ? Colors.blue : Colors.black,
                          //           ),
                          //           Text(
                          //             'Today',
                          //             style: TextStyle(
                          //               fontSize: isToday ? 14 : 12.0,
                          //             ),
                          //           )
                          //         ],
                          //       ),
                          //     ),
                          //     InkWell(
                          //       onTap: () {
                          //         if (!isLock) {
                          //           isLock = true;
                          //           setState(() {
                          //             isWeekly = true;
                          //             isToday = false;
                          //             isMonthly = false;
                          //             isCustom = false;
                          //             queryAllReportsByWeek();
                          //           });
                          //           isLock = false;
                          //         }
                          //       },
                          //       child: Column(
                          //         children: [
                          //           Icon(
                          //             Icons.calendar_view_week,
                          //             color:
                          //             isWeekly ? Colors.blue : Colors.black,
                          //           ),
                          //           Text(
                          //             'Weekly',
                          //             style: TextStyle(
                          //                 fontSize: isWeekly ? 14 : 12.0),
                          //           )
                          //         ],
                          //       ),
                          //     ),
                          //     InkWell(
                          //       onTap: () {
                          //         if (!isLock) {
                          //           isLock = true;
                          //           setState(() {
                          //             isMonthly = true;
                          //             isWeekly = false;
                          //             isToday = false;
                          //             isCustom = false;
                          //             queryAllReportsByMonth();
                          //           });
                          //           isLock = false;
                          //         }
                          //       },
                          //       child: Column(
                          //         children: [
                          //           Icon(
                          //             Icons.calendar_view_month,
                          //             color: isMonthly
                          //                 ? Colors.blue
                          //                 : Colors.black,
                          //           ),
                          //           Text(
                          //             'Monthly',
                          //             style: TextStyle(
                          //                 fontSize: isMonthly ? 14 : 12.0),
                          //           )
                          //         ],
                          //       ),
                          //     ),
                          //     InkWell(
                          //       onTap: () {
                          //         if (!isLock) {
                          //           isLock = true;
                          //           setState(() {
                          //             isCustom = true;
                          //             isMonthly = false;
                          //             isWeekly = false;
                          //             isToday = false;
                          //           });
                          //           isLock = false;
                          //         }
                          //       },
                          //       child: Column(
                          //         children: [
                          //           Icon(
                          //             Icons.date_range,
                          //             color:
                          //             isCustom ? Colors.blue : Colors.black,
                          //           ),
                          //           Text(
                          //             'Customize',
                          //             style: TextStyle(
                          //                 fontSize: isCustom ? 14 : 12.0),
                          //           )
                          //         ],
                          //       ),
                          //     ),
                          //   ],
                          // ),
                          // Divider(
                          //   thickness: 1,
                          // ),
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          //   children: [
                          //     Row(
                          //       children: [
                          //         Text(DateFormat("dd-MM-yyyy")
                          //             .format(fromDate)),
                          //         isCustom
                          //             ? IconButton(
                          //             onPressed: () async {
                          //               if (!isLock) {
                          //                 isLock = true;
                          //                 final DateTime? picked =
                          //                 await showDatePicker(
                          //                     helpText: 'Select date',
                          //                     cancelText: 'Cancel',
                          //                     confirmText: "Ok",
                          //                     fieldLabelText:
                          //                     'Selected Date',
                          //                     fieldHintText:
                          //                     'Month/Date/Year',
                          //                     errorFormatText:
                          //                     'Enter valid date',
                          //                     errorInvalidText:
                          //                     'Enter date in valid range',
                          //                     context: context,
                          //                     initialDate: fromDate,
                          //                     firstDate:
                          //                     DateTime(2015, 8),
                          //                     lastDate: DateTime(2101));
                          //                 if (picked != null &&
                          //                     picked != fromDate) {
                          //                   setState(() {
                          //                     fromDate = picked;
                          //                   });
                          //                 }
                          //                 isLock = false;
                          //               }
                          //             },
                          //             icon: Icon(Icons.calendar_today))
                          //             : Container(),
                          //       ],
                          //     ),
                          //     Row(
                          //       children: [
                          //         Text(DateFormat("dd-MM-yyyy").format(toDate)),
                          //         isCustom
                          //             ? IconButton(
                          //             onPressed: () async {
                          //               if (!isLock) {
                          //                 isLock = true;
                          //                 final DateTime? picked =
                          //                 await showDatePicker(
                          //                     helpText: 'Select date',
                          //                     cancelText: 'Cancel',
                          //                     confirmText: "Ok",
                          //                     fieldLabelText:
                          //                     'Selected Date',
                          //                     fieldHintText:
                          //                     'Month/Date/Year',
                          //                     errorFormatText:
                          //                     'Enter valid date',
                          //                     errorInvalidText:
                          //                     'Enter date in valid range',
                          //                     context: context,
                          //                     initialDate: toDate,
                          //                     firstDate:
                          //                     DateTime(2015, 8),
                          //                     lastDate: DateTime(2101));
                          //                 if (picked != null &&
                          //                     picked != toDate) {
                          //                   setState(() {
                          //                     toDate = picked;
                          //                   });
                          //                 }
                          //                 isLock = false;
                          //               }
                          //             },
                          //             icon: Icon(Icons.calendar_today))
                          //             : Container(),
                          //       ],
                          //     )
                          //   ],
                          // ),
                          Divider(
                            thickness: 1,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              RoundedButton(
                                title: "Reset",
                                width: width > tabletWidth ? width * 0.150 : width * 0.30,
                                onPressed: () async {
                                  Navigator.pop(context);
                                  searchController.clear();
                                  selectArea = dropdownList.last;
                                  await getCustomersDetails();
                                },
                              ),
                              SizedBox(
                                width: width * 0.01,
                              ),
                              RoundedButton(
                                width: width > tabletWidth ? width * 0.157 : width * 0.30,
                                title: "Filter",
                                onPressed: () async {
                                  Navigator.pop(context);
                                  searchController.clear();
                                  if (selectArea?.name == "All") {
                                    await getCustomersDetails();
                                  } else {
                                    await getCustomersDetailsByArea(selectArea?.id);
                                  }
                                },
                              )
                            ],
                          ),
                        ],
                      ),
                    )),
              ),
            );
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(
          "Customers",
          textColor: secondaryTextColor,
          fontSize: 20.0,
        ),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: customersAddButton(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: !isLoading
            ? Container(
                decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
                child: Padding(
                  padding: const EdgeInsets.all(10.0),
                  child: Column(
                    children: [
                      SizedBox(
                        height: 0,
                      ),
                      searchFilterFunction(),
                      const SizedBox(
                        height: 10,
                      ),
                      chooseCustomers(),
                      // Container(
                      //   height: 50,
                      //   alignment: Alignment.centerLeft,
                      //   child: Text("Total Balance: +finalBalance".toString(),
                      //       style: black15BoldTextStyle),
                      // )
                    ],
                  ),
                ),
              )
            : Center(child: CircularProgressIndicator()),
      ),
    );
  }

  List<DropdownMenuItem<String>> filteroptionDropdown(List<String> items) {
    List<DropdownMenuItem<String>> _menuItems = [];
    for (var item in items) {
      _menuItems.addAll(
        [
          DropdownMenuItem<String>(
            value: item,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 5.0),
              child: Text(
                item,
                style: const TextStyle(
                  fontSize: 14,
                ),
              ),
            ),
          ),
          //If it's last item, we will not add Divider after it.
          // ignore: sdk_version_ui_as_code
          if (item != items.last)
            const DropdownMenuItem<String>(
              enabled: false,
              child: Divider(
                thickness: 1,
              ),
            ),
        ],
      );
    }
    return _menuItems;
  }

  chooseCustomers() {
    return customersList.isNotEmpty
        ? Expanded(
            child: SingleChildScrollView(
              child: Wrap(
                spacing: 10,
                runSpacing: 10,
                children: List.generate(customersList.length, (index) {
                  var item = customersList[index];
                  return Padding(
                    padding: const EdgeInsets.only(top: 5, bottom: 5, right: 5, left: 5),
                    child: Container(
                      decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 16),
                      width: width > tabletWidth ? width * 0.4 : width,
                      child: InkWell(
                        onTap: () async {
                          if (pageType == "Sale") {
                            Navigator.pop(context, item);
                          } else {
                            if (PermissionFunctions.checkPermission(40)) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => CustomerAccount(
                                            customers: item,
                                          ))).then((value) async {
                                setState(() {
                                  selectArea = null;
                                });
                                await getAreaList();
                                await getCustomersDetails();
                              });
                            }
                          }
                        },
                        onDoubleTap: () async {
                          if (PermissionFunctions.checkPermission(34) == true) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => AddCustomer(
                                          customers: item,
                                        ))).then((value) async {
                              setState(() {
                                selectArea = null;
                              });
                              await getAreaList();
                              await getCustomersDetails();
                            });
                          }
                        },
                        onLongPress: () async {
                          if (PermissionFunctions.checkPermission(36)) {
                            deleteDialog(context, item);
                          }
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: 35,
                                  width: 60,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(3),
                                    image: const DecorationImage(
                                      image: AssetImage('assets/images/profile.png'),
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                                const SizedBox(
                                  width: 2,
                                ),
                                SizedBox(
                                  // width: width  *,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        item.firstName.toString() + ' ' + item.lastName.toString(),
                                        style: black14SemiBoldTextStyle,
                                      ),
                                      Text(
                                        'Customer',
                                        style: grey12RegularTextStyle,
                                      ),
                                      Text(
                                        item.mobileNo.toString(),
                                        style: black15BoldTextStyle,
                                      ),
                                      Text(
                                        item.fkAreaID != null ? item.areaName.toString() : "",
                                        style: grey12RegularTextStyle,
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                pageType != "Sale"
                                    ? Column(
                                        crossAxisAlignment: CrossAxisAlignment.end,
                                        children: [
                                          SizedBox(
                                            child: Text(
                                              item.balance != null
                                                  ? "Balance: " + double.parse(item.balance.toString()).toStringAsFixed(1).toString()
                                                  : "Balance: 0.0",
                                              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            ),
                                          ),
                                          const SizedBox(
                                            height: 5,
                                          ),
                                          Row(
                                            mainAxisAlignment: MainAxisAlignment.end,
                                            children: [
                                              Container(
                                                child: IconButton(
                                                  icon: Icon(
                                                    Icons.call,
                                                    color: greenColor,
                                                    size: 20.0,
                                                  ),
                                                  onPressed: () {
                                                    if (PermissionFunctions.checkPermission(37)) {
                                                      makingPhoneCall(item.mobileNo);
                                                    }
                                                  },
                                                ),
                                              ),

                                              const SizedBox(
                                                width: 2,
                                              ),
                                              Container(
                                                child: IconButton(
                                                  icon: Icon(
                                                    Icons.share,
                                                    color: buttonThemeColor,
                                                    size: 20.0,
                                                  ),
                                                  onPressed: () {
                                                    if (PermissionFunctions.checkPermission(38)) {
                                                      shareTransactionReport(item);
                                                    }
                                                  },
                                                ),
                                              ),
                                              const SizedBox(
                                                width: 2,
                                              ),
                                              // ignore: sdk_version_ui_as_code
                                              if (item.isWhatsApp == 1)
                                                Container(
                                                  child: IconButton(
                                                    icon: Image.asset(
                                                      'assets/images/whatsapp.png',
                                                      height: 20,
                                                      width: 20,
                                                      color: Colors.green,
                                                    ),
                                                    onPressed: () {
                                                      if (PermissionFunctions.checkPermission(39)) {
                                                        shareTransactionReport(item, isWhatsapp: true);
                                                      }
                                                    },
                                                  ),
                                                ),
                                            ],
                                          ),
                                        ],
                                      )
                                    : Container(),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
          )
        : const Center(
            child: Text("No Customers Available"),
          );
  }

  makingPhoneCall(mobNo) async {
    var url = 'tel:$mobNo';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  makingWhatsappMessage(mobNo, message) async {
    String mobileNo = mobNo.substring(0, 1) == "+" ? mobNo : '+91' + mobNo;
    launch('http://api.whatsapp.com/send?phone=$mobileNo&text=${Uri.encodeFull(message)}');
  }

  shopDetailsFunction() async {
    await dbOperations.queryAllShopDeatails();
    ShopDeatails? shop;
    for (ShopDeatails r in ListUtility.shopDetailsList) {
      shop = r;
      shop.name = r.name != null ? r.name : appName;
    }
    return shop;
  }

  shareTransactionReport(Customer model, {isWhatsapp = false}) async {
    DateFormat dateFormat = DateFormat("dd-MM-yyyy");
    // ShopDetails shopDetails = ShopDetails();
    String data = "";
    ShopDeatails shopDetails = await shopDetailsFunction();
    if (shopDetails.name != "" && shopDetails.name != null) {
      data += "\n${shopDetails.name?.toUpperCase()}";
    } else {
      data += appName;
    }
    if (shopDetails.address1 != "" && shopDetails.address1 != null) {
      data += "\n${shopDetails.address1}";
    }
    if (shopDetails.address2 != "" && shopDetails.address2 != null) {
      data += "\n${shopDetails.address2}";
    }
    if (shopDetails.address3 != "" && shopDetails.address3 != null) {
      data += "\n${shopDetails.address3}";
    }
    data += "\n";
    data += "\nDate : ${dateFormat.format(DateTime.now())}";
    data += "\nAccount : ${model.firstName}${model.lastName} A/C";
    data += "\nMobile : ${model.mobileNo}";
    data += "\nLast 5 transactions";
    data += await generateReport(model);

    if (isWhatsapp) {
      makingWhatsappMessage(model.mobileNo, data);
    } else {
      Share.share(data);
    }
  }

  salesTypeByCustomerID(Customer model) async {
    final allRows = await salesTypeDB.selectAllSalesByCustomerID(model.customerID!);
    List<SalesType> salesList = [];
    allRows?.forEach((row) => salesList.add(SalesType.fromMap(row)));
    return salesList;
  }

  Future<String> generateReport(Customer model) async {
    DateFormat dateFormat = DateFormat("dd-MM-yyyy");
    String data = "";
    List<SalesType> salesList = await salesTypeByCustomerID(model);
    double totalBalance = 0.0;

    data += '\n---------------------------------------------';
    data += '\n${'Date'.padRight(10)} ${'Credit'.padLeft(18)} ${'Debit'.padLeft(10)}';
    if (salesList.isNotEmpty) {
      for (int i = salesList.length - 1; i >= salesList.length - 5; i--) {
        if (i >= 0) {
          var item = salesList[i];
          if (item.salesType == "debit") {
            totalBalance = (totalBalance + double.parse(item.amount.toString()));
          } else {
            totalBalance = (totalBalance - double.parse(item.amount.toString()));
          }

          data +=
              '\n${dateFormat.format(DateTime.parse(item.createdDate.toString())).padLeft(10)} ${double.parse(item.salesType == "debit" ? item.amount.toString() : '0').toStringAsFixed(1).padLeft(12)} ${double.parse(item.salesType == "credit" ? item.amount.toString() : '0').toStringAsFixed(1).padLeft(12)}';
        }
      }
    } else {
      data += '\nTransaction Empty';
    }
    data += '\n---------------------------------------------';
    data += "\nTotal Balance : ${totalBalance.toStringAsFixed(2)}";
    data += "\n    ";
    return data;
  }

  Widget customersAddButton() {
    return InkWell(
        onTap: () async {
          if (PermissionFunctions.checkPermission(33)) {
            FocusScope.of(context).unfocus();
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddCustomer())).then((value) async {
              setState(() {
                selectArea = null;
              });
              await getAreaList();
              await getCustomersDetails();
            });
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }

  Widget searchFilterFunction() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(top: 0, left: 5),
            height: 40,
            child: TextField(
              onTapOutside: (event) {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              controller: searchController,
              onChanged: filterFunction,
              style: TextStyle(color: Colors.black),
              autofocus: false,
              decoration: InputDecoration(
                labelText: 'Search by name or mobile no',
                border: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: grey, width: 0.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                ),
                suffixIcon: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    InkWell(
                      onTap: () {
                        setState(() {
                          filterFunction(searchController.text);
                        });
                        FocusScope.of(context).unfocus();
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.asset(
                          searchIconSVG,
                          color: greyColor,
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        setState(() {
                          searchController.clear();
                        });
                        FocusScope.of(context).unfocus();
                        filterFunction(searchController.text);
                      },
                      child: Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: Icon(
                          Icons.clear,
                          color: greyColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        dropdownList.length > 1
            ? Container(
                padding: const EdgeInsets.only(top: 7, left: 10, right: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    InkWell(
                      onTap: () => filterDialog(context),
                      child: SvgPicture.asset(
                        filterIconSVG,
                        color: greyColor,
                      ),
                    ),
                    // Padding(
                    //   padding: const EdgeInsets.only(left: 5),
                    //   child: InkWell(
                    //     onTap: () => sortDialog(context),
                    //     child: SvgPicture.asset(
                    //       sortIconSVG,
                    //       color: greyColor,
                    //     ),
                    //   ),
                    // ),
                  ],
                ),
              )
            : SizedBox.shrink()
      ],
    );
  }
}
