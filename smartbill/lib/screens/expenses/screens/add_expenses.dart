import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/screens/expenses/screens/expense_category.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import '../model/expense_category_model.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/common_function/validation_utility.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/text_field_box_widget.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/images.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_strings.dart';

//GlobalKey<_AddExpensesState> myWidgetKey = GlobalKey<_AddExpensesState>();

class AddExpenses extends StatefulWidget {
  AddExpenses({Key? key, this.id, this.expenseName}) : super(key: key);
  String? id;
  String? expenseName;

  @override
  State<AddExpenses> createState() => _AddExpensesState();
}

class _AddExpensesState extends State<AddExpenses> {
  Validator validator = new Validator();
  List<ExpensesCategoryModel> expensesCategoryList = [];
  ExpensesCategoryModel? selectKeyword;
  TextEditingController expenseNameController = TextEditingController();
  TextEditingController expensePriceController = TextEditingController();
  FnUtilities fnUtilities = new FnUtilities();
  FocusNode expense = new FocusNode();

  double width = 0.0;
  bool isUpdate = false;
  bool isLock = false;

  initPageDetails() async {
    await getAllExpenseCategory();

    if (widget.id != null) {
      await setFormData();
    } else {
      if (widget.expenseName != null) {
        expenseNameController.text = widget.expenseName ?? '';
      }
      setState(() {
        isUpdate = true;
      });
    }
  }

  getAllExpenseCategory() {
    if (ListUtility.expensesCategoryList.isNotEmpty) {
      setState(() {
        expensesCategoryList.clear();
        ExpensesCategoryModel model = ExpensesCategoryModel();
        model.expCategoryName = "None";
        expensesCategoryList.add(model);
        expensesCategoryList.addAll(ListUtility.expensesCategoryList);
        if (expensesCategoryList.length != 0 && widget.id == null) {
          selectKeyword = expensesCategoryList.first;
        }
      });
    }
  }

  setFormData() {
    for (ExpensesProduct model in ListUtility.expensesItemList) {
      if (model.expProductID == widget.id) {
        setState(() {
          expenseNameController.text = model.expProductName ?? "";
          expensePriceController.text = model.expProductPrice != null ? fnUtilities.checkQtyDoubleValue(model.expProductPrice.toString()) : "";
          if (model.expCategoryID != null) {
            selectKeyword = ListUtility.expensesCategoryList.firstWhere((element) => element.expCategoryID == model.expCategoryID);
          }
        });
      }
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  onSaveExpense() async {
   try{
     if (!isLock) {
      isLock = true;
      LoadingAlertWidget.onLoading(context);;
      var uuid = new Uuid();
      ExpensesProduct model = ExpensesProduct();
      model.expProductID = uuid.v4().toString();
      model.expProductName = expenseNameController.text;
      model.expProductPrice = expensePriceController.text;
      model.createdDate = DateTime.now().toString();
      model.sync = 0;
      model.status = 0;
      if (selectKeyword != null) {
        model.expCategoryID = selectKeyword?.expCategoryID;
      }
      if (widget.id == null) {
        await expenseCategoriesOperation(model, "insert");
      } else {
        model.expProductID = widget.id;
        model.status = 1;
        await expenseCategoriesOperation(model, "update");
      }
    }

    setState(() {
      expenseNameController.clear();
      selectKeyword = null;
      expensePriceController.clear();
    });
    LoadingAlertWidget.onStopping();
    Navigator.of(context).pop();
    isLock = false;
   } catch(e){
    ErrorLogsFunction.insertErrorLogs(e, "onSaveExpense - AddExpenses");
   }
  }

  expenseCategoriesOperation(ExpensesProduct expItem, String operations) async {
    var expenseItem;
    DBFuctionsScript dbScript = DBFuctionsScript();
    if (operations == "insert") {
      expenseItem = {
        db_expProductID: expItem.expProductID,
        db_expProductName: expItem.expProductName,
        db_createdDate: expItem.createdDate,
        db_workspaceID: MyApp.activeWorkspace.workspaceId,
        db_expProductPrice: expItem.expProductPrice.toString(),
        db_expenseCategoryName: expItem.expCategoryName,
        db_expCategoryID: expItem.expCategoryID,
        db_expProductSync: expItem.sync,
        db_status: expItem.status,
      };
      await dbScript.dbInsert(db_expensesProduct, expenseItem);
    } else {
      expenseItem = {
        db_expProductName: expItem.expProductName,
        db_createdDate: expItem.createdDate,
        db_workspaceID: MyApp.activeWorkspace.workspaceId,
        db_expProductPrice: expItem.expProductPrice.toString(),
        db_expenseCategoryName: expItem.expCategoryName,
        db_expCategoryID: expItem.expCategoryID,
        db_expProductSync: expItem.sync,
        db_status: expItem.status,
      };
      await dbScript.updateModel(db_expensesProduct, expenseItem, db_expProductID, expItem.expProductID);
    }
    await fnUtilities.getAllExpenses();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(
          widget.id == null ? "Add Expenses" : "Update Expenses",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        widgets: <Widget>[
          widget.id != null
              ? Padding(
                  padding: const EdgeInsets.only(right: 20.0),
                  child: editWidget(),
                )
              : SizedBox.shrink(),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Center(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  width: width > mobileWidth ? width * 0.4 : width,
                  padding: EdgeInsets.all(16.0),
                  height: 350,
                  decoration: BoxDecoration(borderRadius: BorderRadius.circular(15), color: backgroundColor),
                  child: Form(
                      key: validator.formkey,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'Expenses',
                            style: headingFontStyle2,
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          TextFieldBoxWidget(
                            text: "Expenses Name",
                            isPassword: false,
                            mController: expenseNameController,
                            validator: validator.validateTextField,
                            inputFormate: [LengthLimitingTextInputFormatter(200)],
                            inputAction: TextInputAction.next,
                            enable: isUpdate,
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[categoryDropDownWidget(), addCategoryIcon()],
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          TextFieldBoxWidget(
                            text: "Price",
                            isPassword: false,
                            mController: expensePriceController,
                            inputType: TextInputType.number,
                            inputFormate: [
                              FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                            ],
                            inputAction: TextInputAction.done,
                            enable: isUpdate,
                          ),
                          SizedBox(
                            height: 20,
                          ),
                          Align(
                            alignment: Alignment.centerRight,
                            child: RoundedButton(
                                width: width > mobileWidth ? width * 0.4 : width,
                                title: widget.id == null ? "Add Expenses" : "Update Expense",
                                onPressed: () {
                                  if (PermissionFunctions.checkPermission(61)) {
                                    validator.validate();
                                    if (validator.validate()) {
                                      if (isUpdate) {
                                        onSaveExpense();
                                      }
                                    }
                                  }
                                }),
                          ),
                        ],
                      )),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  //EDIT WIDGET
  Widget editWidget() {
    return InkWell(
      onTap: () {
        setState(() {
          isUpdate = true;
        });
      },
      child: SvgPicture.asset(
        editSVG,
        color: Colors.white,
      ),
    );
  }

  categoryDropDownWidget() {
    return Expanded(
      child: InputDecorator(
        decoration: InputDecoration(
          contentPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
          labelText: 'Category',
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
        ),
        child: DropdownButtonHideUnderline(
          child: new DropdownButton<ExpensesCategoryModel>(
            menuMaxHeight: 250,
            hint: Text("None"),
            value: selectKeyword,
            items: expensesCategoryList.map((ExpensesCategoryModel value) {
              return new DropdownMenuItem<ExpensesCategoryModel>(
                value: value,
                child: new Text(value.expCategoryName ?? ''),
              );
            }).toList(),
            onChanged: !isUpdate
                ? null
                : (ExpensesCategoryModel? val) async {
                    setState(() {
                      if (isUpdate) {
                        selectKeyword = val;
                        FocusScope.of(context).requestFocus(FocusNode());
                      }
                    });
                  },
          ),
        ),
      ),
    );
  }

  addCategoryIcon() {
    return Padding(
      padding: const EdgeInsets.only(right: 10, left: 10),
      child: InkWell(
        onTap: () {
          if (PermissionFunctions.checkPermission(60)) {
            if (!isLock) {
              isLock = true;
              if (isUpdate) {
                Navigator.push(context, MaterialPageRoute(builder: (context) => ExpensesCategory())).then((value) async {
                  await fnUtilities.getAllExpensesCategoryList();
                  getAllExpenseCategory();
                });
              }

              isLock = false;
            }
          }
        },
        child: Container(
            padding: EdgeInsets.symmetric(vertical: 13),
            // width: 25,
            child: Icon(Icons.add)),
      ),
    );
  }
}
