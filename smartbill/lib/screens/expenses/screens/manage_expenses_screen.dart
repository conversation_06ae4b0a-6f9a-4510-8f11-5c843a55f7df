// ignore_for_file: sdk_version_ui_as_code, sdk_version_set_literal, missing_return
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:smartbill/model/card_models/card_model.dart';
import 'package:smartbill/model/card_models/category_card_model.dart';
import 'package:smartbill/model/card_models/transaction_card_model.dart';
import 'package:smartbill/model/reports/get_expenses_report.dart';
import 'package:smartbill/model/reports/get_purchase_reports.dart';
import 'package:smartbill/screens/expenses/db_script/purchase_db_script.dart';
import 'package:smartbill/screens/expenses/functions/expense_functions.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/screens/expenses/model/expenses_model.dart';
import 'package:smartbill/screens/expenses/model/get_purchase_transaction_byId.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/screens/expenses/widgets/overview_dialog.dart';
import 'package:smartbill/screens/expenses/widgets/save_expense_dialog.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/transaction_panel_widget.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/expenses_db_script.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../../model/products.dart';
import '../../../model/workspace_settings.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/common_widgets/common_panel_widgets/category_panel_widget.dart';
import '../../../utils/common_widgets/common_panel_widgets/normal_panel_widget.dart';
import '../../../utils/common_widgets/common_panel_widgets/number_panel_widget.dart';
import '../../../utils/common_widgets/text_field_box_widget.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_operations_utility.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import '../../products/add_product_page.dart';
import '../functions/purchase_functions.dart';
import '../model/expense_category_model.dart';
import '../model/expenses_product_model.dart';
import '../model/get_expense_transaction_byId.dart';
import '../model/purchase_transactions_model.dart';
import 'add_expenses.dart';

class ManageExpensesScreen extends StatefulWidget {
  Expenses? expenses;
  List<ExpenseTransactions>? expTransactionList = [];
  PurchaseTransactionReport? purchase;
  List<PurchaseTransactions>? purchaseTransList = [];
  String? expDate;
  String? purchaseDate;
  ManageExpensesScreen(
      {Key? key, this.flag, this.expenses, this.expTransactionList, this.purchase, this.purchaseTransList, this.expDate, this.purchaseDate})
      : super(key: key);

  String? flag;

  @override
  State<ManageExpensesScreen> createState() =>
      _ManageExpensesScreenState(purchase, purchaseTransList, expenses, expTransactionList, expDate, purchaseDate);
}

class _ManageExpensesScreenState extends State<ManageExpensesScreen> {
  double width = 0.0;
  double height = 0.0;
  double quantity = 1;
  double totalAmount = 0.0;
  double totalQty = 0.0;
  bool isBigLayout = false;
  bool isLock = false;
  bool isProductLoading = false;
  // String saleLayout;
  String? expenseName = '';
  String itemId = '';
  String expenseCategoryKey = '';
  String? invoiceAdd;
  String? expDate;
  String? purchaseDate;
  TextEditingController searchController = TextEditingController();
  FnUtilities fnUtilities = FnUtilities();
  DBOperations dbOperations = new DBOperations();
  ExpensesDBScript expensesDBScript = new ExpensesDBScript();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  PurchaseTransactionReport? purchase;
  Expenses? expenses;

  DBFuctionsScript dbScript = DBFuctionsScript();
  List<ExpensesProduct> filterExpItemList = [];
  List<ExpensesCategoryModel> expensesCategoryList = [];
  List<ExpenseTransactionModel> expenseTransactionList = [];
  List<CardModel> cardListItems = [];
  List<CategoryCardModel> categoryItemList = [];
  List<TransactionCardModel> transactionCardList = [];
  List<PurchaseTransactions>? purchaseTransList;
  List<ExpenseTransactions>? expTransactionList;
  String? expensesName;
  DateTime date = DateTime.now();
  final expenseSearchController = TextEditingController();

  final amountController = new TextEditingController();
  final descriptionController = new TextEditingController();

  List<Products> filterProductList = [];
  List<Products> productsToAdd = [];
  List<ProductKey> productKeyList = [];
  List<PurchaseTransactionsModel> purchaseTransactionlist = [];
  PurchaseModel? purchaseProduct;

  bool showImages = true;
  PurchaseFunction purchaseFunction = PurchaseFunction();
  ExpenseFunction expenseFunction = ExpenseFunction();
  // PanelFunction panelFunction = PanelFunction();

  String? panels;

  _ManageExpensesScreenState(this.purchase, this.purchaseTransList, this.expenses, this.expTransactionList, this.expDate, this.purchaseDate);

  initPageDetails() async {
    await initSettings();
    if (widget.flag == expense) {
      getExpensesItem();
      getExpensesCategoryList();
      assignExpenseTransactionList();
    } else {
      getPurchaseItem();
      getpurchaseCategoryList();
      assignPurchaseTransactionList();
    }
  }

  initSettings() async {
    setState(() {
      //Workspace device settings
      MyApp.isListViewEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductListview) == "1";
      MyApp.isFilterEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductTapOnClear) == "1";
      showImages = fnUtilities.workSpaceSetValues(key_showproductImages) == "1";
      panels = fnUtilities.workSpaceDeviceSetValue(key_panelType);
      updateNamePanelType(panels ?? "");
    });
  }

  assignExpenseTransactionList() {
    expenseTransactionList.clear();
    if (expTransactionList != null) {
      for (ExpenseTransactions model in expTransactionList ?? []) {
        expenseTransactionList.add(ExpenseTransactionModel(
            expID: model.expID,
            expTransactionID: model.expTransactionID,
            expProductID: model.expProductID,
            quantity: model.quantity.toString(),
            expProductPrice: model.expProductPrice.toString(),
            description: model.description,
            createdDate: model.createdDate,
            amount: model.amount.toString(),
            expProductName: model.productName,
            workspaceID: model.workspaceID));
      }
      if (expenses?.expId != null) {
        expenseFunction.expTransList = expenseTransactionList;
      }
      transactionCardShow(expenseTransactionList);
    }
  }

  assignPurchaseTransactionList() {
    purchaseTransactionlist.clear();
    if (purchaseTransList != null) {
      for (PurchaseTransactions model in purchaseTransList ?? []) {
        purchaseTransactionlist.add(PurchaseTransactionsModel(
          purchaseID: model.purchaseID,
          purchaseTransactionID: model.purchaseTransactionID,
          purchaseProductID: model.purchaseProductID,
          productName: model.productName,
          purchaseProductPrice: model.purchaseProductPrice.toString(),
          amount: model.amount.toString(),
          createdDate: model.createdDate,
          description: model.description,
          quantity: model.quantity.toString(),
          workspaceID: model.workspaceID,
        ));
      }
      if (purchase?.purchaseId != null) {
        purchaseFunction.purchaseTransactionList = purchaseTransactionlist;
      }
      productTransactionCardShow(purchaseTransactionlist);
    }
  }

  updateNamePanelType(String panelType) async {
    /// this function written for due to name panel depriciation.
    if (panelType == "Name") {
      panels = "Normal";
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = panels;
      fnUtilities.updateDeviceSettingDetails(settingsDetail);
    }
  }

  ///Get purchase items
  getPurchaseItem() async {
    if (ListUtility.productList.isNotEmpty) {
      setState(() {
        isProductLoading = true;
      });
      productsToAdd.clear();
      for (Products p in ListUtility.productList) {
        if (p.productFor != null && (p.productFor?.name == "purchase" || p.productFor?.name == "both")) {
          productsToAdd.add(p);
        }
      }
      List<Products> productsWithFilePaths = [];
      productsWithFilePaths.clear();
      productsWithFilePaths = await fnUtilities.assignFilePath(productsToAdd);
      filterProductList.clear();
      filterProductList.addAll(productsWithFilePaths);
    }

    filterProductList.add(Products());
    panelCardShow(productItemList: filterProductList);
    setState(() {
      isProductLoading = false;
    });
  }

  ///Get expense item
  getExpensesItem() async {
    filterExpItemList.clear();
    if (ListUtility.expensesItemList.isNotEmpty) {
      setState(() {
        isProductLoading = true;
      });
      filterExpItemList.addAll(ListUtility.expensesItemList);
    }
    filterExpItemList.add(ExpensesProduct());
    panelCardShow(expItemList: filterExpItemList);
    setState(() {
      isProductLoading = false;
    });
  }

  ///To assign list of expenses item into card list
  panelCardShow({List<ExpensesProduct>? expItemList, List<Products>? productItemList}) {
    if (widget.flag == expense) {
      cardListItems.clear();
      for (ExpensesProduct model in expItemList ?? []) {
        setState(() {
          cardListItems.add(
            CardModel(
                id: model.id,
                guidId: model.expProductID,
                name: model.expProductName,
                isShowImage: false,
                price: model.expProductPrice,
                keyWordName: model.expCategoryName),
          );
        });
      }
    } else {
      cardListItems.clear();
      for (Products model in filterProductList) {
        setState(() {
          cardListItems.add(CardModel(
              id: model.productSaleId,
              guidId: model.productId,
              name: model.name,
              imagePath: model.imagePath,
              isShowImage: showImages,
              price: model.price,
              keyWordName: model.productKeyName));
        });
      }
    }
  }

  ///Get expense category list
  getExpensesCategoryList() {
    if (ListUtility.expensesCategoryList.isNotEmpty) {
      expensesCategoryList.clear();
      expensesCategoryList.add(ExpensesCategoryModel(expCategoryName: "All"));
      expensesCategoryList.addAll(ListUtility.expensesCategoryList);
      categoryPanelCardShow(expenseCategoryList: expensesCategoryList);
    }
  }

  ///GET purchase CATEGORY LIST
  getpurchaseCategoryList() {
    if (ListUtility.productKeyList.isNotEmpty) {
      productKeyList.clear();
      productKeyList.add(ProductKey(keywordName: "All"));
      productKeyList.addAll(ListUtility.productKeyList);
      categoryPanelCardShow(productKeyList: productKeyList);
    }
  }

  categoryPanelCardShow({List<ExpensesCategoryModel>? expenseCategoryList, List<ProductKey>? productKeyList}) {
    if (widget.flag == expense) {
      categoryItemList.clear();
      for (ExpensesCategoryModel model in expenseCategoryList ?? []) {
        categoryItemList.add(CategoryCardModel(id: model.expCatID, guidId: model.expCategoryID, categoryName: model.expCategoryName));
      }
    } else {
      for (ProductKey model in productKeyList ?? []) {
        categoryItemList.add(CategoryCardModel(id: model.id, guidId: model.productKeyId, categoryName: model.keywordName));
      }
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  ///Panel switch function
  panelSwitch() {
    FocusScope.of(context).unfocus();
    if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Normal") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Id";
      fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Id") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Key";
      fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Key") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Normal";
      fnUtilities.updateDeviceSettingDetails(settingsDetail);
    }
    setState(() {
      initSettings();
    });
  }

  ///On tab add new expense item function
  onTabAddNewExpenses() {
    if (!isLock) {
      isLock = true;
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => AddExpenses(
                    expenseName: expenseSearchController.text,
                  ))).then((value) async {
        await fnUtilities.getAllExpensesCategoryList();
        await fnUtilities.getAllExpenses();
        getExpensesItem();
        getExpensesCategoryList();
        isLock = false;
      });
    }
  }

  ///On tab add new product item function
  onTabAddNewProduct() {
    if (!isLock) {
      isLock = true;
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => AddProductDialog(
                    productName: searchController.text,
                    screenFrom: "Purchase",
                  ))).then((value) async {
        //if (value) {
          await fnUtilities.getAllProductDetails();
          await getPurchaseItem();
        //}
      });
      isLock = false;
    }
  }

  ///On tab expense click function
  commonOnTapExpenses(ExpensesProduct expenseItem) async {
    if (expenseItem.expProductPrice == null || expenseItem.expProductPrice == "") {
      expenseTransactionList = await expenseFunction.amountDialog(context, expenseItem) ?? [];
      transactionCardShow(expenseTransactionList);
    } else {
      String expenseProductPrice = fnUtilities.checkQtyDoubleValue(expenseItem.expProductPrice ?? '0');
      expenseTransactionList = await expenseFunction.addExpenseTransaction(
        expenseItem,
        expenseProductPrice,
      );
      transactionCardShow(expenseTransactionList);
    }
    }

  commonOnTabProduct(Products productsModel) async {
    if (productsModel.dynamicQuantity == 1) {
      if (productsModel.priceType == 2) {
        await purchaseFunction.getAllProductUnitsDetails(productsModel.productId);
        purchaseTransactionlist = await purchaseFunction.unitDialog(context, productsModel, isBigLayout, "dynamicQty") ?? [];
        productTransactionCardShow(purchaseTransactionlist);
      } else {
        purchaseTransactionlist = await purchaseFunction.dynamicQtyDialog(context, productsModel.price ?? '0', productsModel, null) ?? [];
        productTransactionCardShow(purchaseTransactionlist);
      }
    } else {
      if (productsModel.priceType == null) {
        if (productsModel.price == null || productsModel.price == "") {
          purchaseTransactionlist = await purchaseFunction.amountDialogForPurchase(context, productsModel) ?? [];
          productTransactionCardShow(purchaseTransactionlist);
        } else {
          purchaseTransactionlist = await purchaseFunction.addProductsInSalesList(productsModel, null, purchaseFunction.priceController.text);
          productTransactionCardShow(purchaseTransactionlist);
        }
      } else {
        if (productsModel.priceType == 0) {
          purchaseTransactionlist = await purchaseFunction.amountDialogForPurchase(context, productsModel) ?? [];
          productTransactionCardShow(purchaseTransactionlist);
        } else if (productsModel.priceType == 1) {
          purchaseTransactionlist = await purchaseFunction.addProductsInSalesList(productsModel, null, purchaseFunction.priceController.text);
          productTransactionCardShow(purchaseTransactionlist);
        } else {
          await purchaseFunction.getAllProductUnitsDetails(productsModel.productId);
          purchaseTransactionlist = await purchaseFunction.unitDialog(context, productsModel, isBigLayout, "normalQty") ?? [];
          productTransactionCardShow(purchaseTransactionlist);
        }
      }
    }
    }

  /// To be used for common transaction panel widget
  transactionCardShow(List<ExpenseTransactionModel> expenseTransactionList) {
    if (expenseTransactionList.isNotEmpty) {
      transactionCardList.clear();
      for (ExpenseTransactionModel model in expenseTransactionList) {
        setState(() {
          transactionCardList.add(TransactionCardModel(
              guidId: model.expProductID,
              name: model.expProductName,
              qty: model.quantity,
              itemPrice: model.expProductPrice.toString(),
              totalAmount: model.amount.toString()));
        });
      }
    }
  }

  /// To be used for common transaction panel widget
  productTransactionCardShow(List<PurchaseTransactionsModel> purchaseTransactionlist) {
    transactionCardList.clear();
    for (PurchaseTransactionsModel model in purchaseTransactionlist) {
      setState(() {
        transactionCardList.add(TransactionCardModel(
            guidId: model.purchaseProductID,
            name: model.productName,
            qty: model.quantity,
            itemPrice: model.purchaseProductPrice,
            totalAmount: model.amount));
      });
    }
  }

  onSaveExpense(String date) async {
    calculateTotalAmount();
    ExpensesModel expensesModel = ExpensesModel();
    expensesModel.totalAmount = fnUtilities.checkQtyDoubleValue(totalAmount.toString());
    expensesModel.workspaceID = MyApp.activeWorkspace.workspaceId;
    expensesModel.sync = 0;
    if (widget.expenses == null) {
      invoiceAdd = await getMaxInvoiceId();
      var uuid = new Uuid();
      expensesModel.expID = uuid.v4().toString();
      expensesModel.invoiceID = invoiceAdd;
      expensesModel.expDate = date;
      expensesModel.createdDate = DateTime.now().toString();
      expensesModel.status = 0;
      await expenseFunction.upSetExpenses(expensesModel, "insert", expenseTransactionList);
    } else {
      expensesModel.expID = expenses?.expId;
      expensesModel.invoiceID = expenses?.invoiceId;
      expensesModel.expDate = date == expDate ? expDate : date;
      expensesModel.createdDate = expenses?.createdDate;
      expensesModel.status = 1;
      expensesModel.invoiceNo = expenses?.invoiceNo;
      await expenseFunction.upSetExpenses(expensesModel, "update", expenseTransactionList);
    }

    setState(() {
      totalAmount = 0.0;
      expenseTransactionList.clear();
      transactionCardList.clear();
    });
  }

  onSavePurchase(String date) async {
    calculateTotalAmount();
    PurchaseModel model = PurchaseModel();
    model.totalAmount = totalAmount.toString();

    model.workspaceID = MyApp.activeWorkspace.workspaceId;
    model.sync = 0;
    if (purchase == null) {
      invoiceAdd = await purchaseFunction.getMaxInvoiceId();
      var uuid = new Uuid();
      model.purchaseID = uuid.v4().toString();
      model.invoiceID = invoiceAdd;
      model.purchaseDate = date;
      model.createdDate = DateTime.now().toString();
      model.status = 0;
      await purchaseFunction.upsertPurchase(model, "insert", purchaseTransactionlist);
    } else {
      model.purchaseID = purchase?.purchaseId;
      model.invoiceID = purchase?.invoiceId;
      model.purchaseDate = date == purchaseDate ? purchaseDate : date;
      model.invoiceNo = purchase?.invoiceNo.toString();
      model.createdDate = purchase?.createdDate;
      model.status = 1;
      await purchaseFunction.upsertPurchase(model, "update", purchaseTransactionlist);
    }
    setState(() {
      totalAmount = 0.0;
      purchaseTransactionlist.clear();
      transactionCardList.clear();
    });
  }

  ///Calculate the total expense amount
  calculateTotalAmount() {
    totalAmount = 0.0;
    totalQty = 0.0;
    setState(() {
      if (widget.flag == expense) {
        for (ExpenseTransactionModel model in expenseTransactionList) {
          totalAmount = totalAmount + double.parse(model.amount ?? '0');
          totalQty = totalQty + double.parse(model.quantity ?? '0');
        }
      } else {
        for (PurchaseTransactionsModel model in purchaseTransactionlist) {
          totalAmount = totalAmount + double.parse(model.amount ?? '0');
          totalQty = totalQty + double.parse(model.quantity ?? '0');
        }
      }
    });
  }

  //GET A INVOICE ID FUNCTION
  getMaxInvoiceId() async {
    String prefix = await dbOperations.queryPrefixName();
    bool isResetSalesNo = await await fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1" ? true : false;
    String prefixMax = await expensesDBScript.queryGetMaxInvoiceId(prefix, isResetSalesNo);
    if (prefixMax == "0") {
      return prefix + 1.toString();
    } else {
      return prefix + (int.parse(prefixMax.substring(prefix.length)) + 1).toString();
    }
  }

  //Get expense details by name
  void filterExpensesByName(String name) async {
    if (widget.flag == expense) {
      setState(() {
        expensesName = name;
        filterExpItemList.clear();
        for (ExpensesProduct p in ListUtility.expensesItemList) {
          if (p.expProductName != null && p.expProductName!.toLowerCase().contains(name.toLowerCase())) {
            filterExpItemList.add(p);
          }
        }
      });

      filterExpItemList.add(ExpensesProduct());
      panelCardShow(expItemList: filterExpItemList);
    } else {
      setState(() {
        expensesName = name;
        List<Products> tempProductList = [];
        tempProductList.addAll(filterProductList);
        filterProductList.clear();
        for (Products p in tempProductList) {
          if (p.name != null && p.name!.toLowerCase().contains(name.toLowerCase())) {
            filterProductList.add(p);
          }
        }
      });
      filterProductList.add(Products());
      panelCardShow(productItemList: filterProductList);
    }
  }

  //Filter Expense by id
  filterItemById(String id) {
    if (widget.flag == expense) {
      setState(() {
        itemId += id;
        filterExpItemList.clear();
        getfilterExpenseslistById(itemId);
      });
    } else {
      setState(() {
        itemId += id;
        filterProductList.clear();
        getfilterProductlistById(itemId);
      });
    }
  }

  //Get Expense Item based on id from Expense item list
  getfilterExpenseslistById(String id) async {
    if (id == "") {
      setState(() {
        filterExpItemList.addAll(ListUtility.expensesItemList);
        panelCardShow(expItemList: filterExpItemList);
      });
    } else {
      for (ExpensesProduct expItemModel in ListUtility.expensesItemList) {
        if (expItemModel.id != null && expItemModel.id.toString().contains(id)) {
          setState(() {
            filterExpItemList.add(expItemModel);
            panelCardShow(expItemList: filterExpItemList);
          });
        }
      }
    }
    setState(() {
      filterExpItemList.add(ExpensesProduct());
      panelCardShow(expItemList: filterExpItemList);
    });
  }

  //Get Expense Item based on id from product item list
  getfilterProductlistById(String id) async {
    if (id == "") {
      setState(() {
        filterProductList.addAll(productsToAdd);
        panelCardShow(productItemList: filterProductList);
      });
    } else {
      for (Products productModel in productsToAdd) {
        if (productModel.productSaleId != null && productModel.productSaleId.toString().contains(id)) {
          setState(() {
            filterProductList.add(productModel);
            panelCardShow(productItemList: filterProductList);
          });
        }
      }
    }
    setState(() {
      filterProductList.add(Products());
      panelCardShow(productItemList: filterProductList);
    });
  }

  //SET PRODUCT KEY DETAILS
  filterExpensesByCategory(String key, String keyName) {
    if (widget.flag == expense) {
      setState(() {
        filterExpItemList.clear();
        getfilterExpenseslistByKey(key, keyName);
      });
    } else {
      setState(() {
        filterProductList.clear();
        getfilterProductlistByKey(key, keyName);
      });
    }
  }

  //GET FILTER PRODUCT FROM LIST
  getfilterExpenseslistByKey(String key, String keyName) async {
    if (keyName.toLowerCase() == "all") {
      setState(() {
        filterExpItemList.addAll(ListUtility.expensesItemList);
        panelCardShow(expItemList: filterExpItemList);
      });
    } else {
      for (ExpensesProduct expItem in ListUtility.expensesItemList) {
        if (expItem.expCategoryID == key || expItem.expCategoryName == keyName) {
          setState(() {
            filterExpItemList.add(expItem);
            panelCardShow(expItemList: filterExpItemList);
          });
        }
      }
    }
    setState(() {
      filterExpItemList.add(ExpensesProduct());
      panelCardShow(expItemList: filterExpItemList);
    });
  }

  //GET FILTER PRODUCT FROM LIST
  getfilterProductlistByKey(String key, String keyName) async {
    if (keyName.toLowerCase() == "all") {
      setState(() {
        filterProductList.addAll(productsToAdd);
        panelCardShow(productItemList: filterProductList);
      });
    } else {
      for (Products p in productsToAdd) {
        if (p.keyID == key || p.productKeyName == keyName) {
          p.imagePath = await fnUtilities.readFileFromLocal(p.image);
          setState(() {
            filterProductList.add(p);
            panelCardShow(productItemList: filterProductList);
          });
        }
      }
    }
    setState(() {
      filterProductList.add(Products());
      panelCardShow(productItemList: filterProductList);
    });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBigLayout = (width > tabletWidth) || Platform.isWindows;

    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Flexible(
            child: BootstrapRow(children: [
              BootstrapCol(
                sizes: isBigLayout ? 'col-md-6 col-sm-12' : 'col-md-12 col-sm-12',
                child: billSection(),
              ),
              BootstrapCol(
                sizes: isBigLayout ? 'col-md-6 col-sm-12' : 'col-md-12 col-sm-12',
                child: expensesItemSection(),
              ),
            ]),
          ),
        ],
      ),
    );
  }

  /// Bill Section
  billSection() {
    return Container(
        height: (isBigLayout) ? height : height * 0.5,
        padding: EdgeInsets.all(10.0),
        child: Column(
          children: [
            Container(
              height: 45,
              color: isBigLayout ? appThemeColor : secondaryTextColor,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  isBigLayout
                      ? InkWell(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: EdgeInsets.all(5.0),
                            width: 35,
                            child: Icon(Icons.arrow_back, color: secondaryTextColor),
                          ),
                        )
                      : SizedBox.shrink(),
                ],
              ),
            ),
            TransactionPanelWidget(
              transactionFor: widget.flag == expense ? "Expense" : "Purchase",
              transactionCardList: transactionCardList,
              onLongPress: (value) {
                removeItemDialog(value);
              },
              onTapPress: (item, index) {
                if (item != null) {
                  if (widget.flag == expense) {
                    for (ExpenseTransactionModel model in expenseTransactionList) {
                      if (model.expProductID == item.guidId && model.expProductPrice.toString() == item.itemPrice) {
                        saveDialog(context, index, expTransactionList: expenseTransactionList);
                      }
                    }
                  } else {
                    for (PurchaseTransactionsModel model in purchaseTransactionlist) {
                      if (model.purchaseProductID == item.guidId && model.purchaseProductPrice == item.itemPrice) {
                        saveDialog(context, index, pTransactionList: purchaseTransactionlist);
                      }
                    }
                  }
                }
              },
              isBigLayout: isBigLayout,
            ),

            /// Buttons row
            Padding(
              padding: const EdgeInsets.only(top: 5.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GestureDetector(
                    onTap: () {
                      setState(() {
                        expenseTransactionList.clear();
                        transactionCardList.clear();
                        purchaseTransactionlist.clear();
                      });
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 40,
                      width: isBigLayout ? width * 0.10 : 100,
                      child: TextWidget("Reset", textColor: secondaryTextColor, isCentered: true),
                      decoration: boxDecoration(
                          bgColor: expenseTransactionList.isEmpty && purchaseTransactionlist.isEmpty ? Colors.grey : buttonThemeColor, radius: 8.0),
                    ),
                  ),
                  GestureDetector(
                    onTap: () {
                      calculateTotalAmount();
                      if (widget.flag == expense) {
                        if (PermissionFunctions.checkPermission(70)) {
                          expenseTransactionList.isNotEmpty ? overViewDialogWidget(context, totalQty, "Expense Overview", expDate ?? "") : null;
                        }
                      } else {
                        if (PermissionFunctions.checkPermission(72)) {
                          purchaseTransactionlist.isNotEmpty ? overViewDialogWidget(context, totalQty, "Purchase Overview", purchaseDate ?? "") : null;
                        }
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 40,
                      width: isBigLayout ? width * 0.15 : 100,
                      child: TextWidget("Save", textColor: secondaryTextColor, isCentered: true),
                      decoration: boxDecoration(
                          bgColor: expenseTransactionList.isEmpty && purchaseTransactionlist.isEmpty ? Colors.grey : buttonThemeColor, radius: 8.0),
                    ),
                  ),
                ],
              ),
            ),
            if (Platform.isWindows) ...[
              transactionOverview(context),
            ]
          ],
        ));
  }

  ///Expenses Item Section
  expensesItemSection() {
    return Container(
        padding: EdgeInsets.all(10.0),
        height: isBigLayout ? height : height * 0.5,
        child: Column(
          children: [
            Container(
              height: 40,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Expanded(
                    child: TextFieldBoxWidget(
                      text: "Search here..",
                      mController: widget.flag == expense ? expenseSearchController : searchController,
                      onChange: filterExpensesByName,
                      isPassword: false,
                      suffix: true,
                      onTabClear: () {
                        if (widget.flag == expense) {
                          expenseSearchController.clear();
                          filterExpItemList.clear();
                          getExpensesItem();
                        } else {
                          searchController.clear();
                          filterProductList.clear();
                          getPurchaseItem();
                        }
                      },
                    ),
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      panelSwitchIconWidget(),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 5,
            ),
            SizedBox(
              height: 5,
            ),
            // if (saleLayout != "SaleLayout2") ...[
            Expanded(
                child: !isProductLoading
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          panels == "Normal" ? normalPanelWidget() : Container(),
                          panels == "Id" ? numberPanelWidget() : Container(),
                          panels == "Key" ? categoryPanelWidget() : Container(),
                        ],
                      )
                    : Center(child: CircularProgressIndicator()))
            // ]
          ],
        ));
  }

  /// panel swith widget
  panelSwitchIconWidget() {
    return InkWell(
      onTap: () {
        if (!isLock) {
          isLock = true;
          panelSwitch();
          isLock = false;
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(5.0),
        width: 40,
        child: Icon(Icons.swipe_outlined),
      ),
    );
  }

  //NORMAL PANEL TYPE WIDGET
  normalPanelWidget() {
    return NormalPanelWidget(
      isBigLayout: isBigLayout,
      cardItemList: cardListItems,
      isSaleLayoutTwo: false,
      onTapItem: ((value1, value2) {
        if (widget.flag == expense) {
          for (ExpensesProduct model in filterExpItemList) {
            if (model.expProductID == value1.guidId) {
              commonOnTapExpenses(model);
            }
          }
        } else {
          for (Products model in filterProductList) {
            if (model.productId == value1.guidId) {
              commonOnTabProduct(model);
            }
          }
        }
      }),
      onTabAddNewItem: () {
        if (widget.flag == expense) {
          if (PermissionFunctions.checkPermission(71)) {
            onTabAddNewExpenses();
          }
        } else {
          if (PermissionFunctions.checkPermission(73)) {
            onTabAddNewProduct();
          }
        }
      },
    );
  }

  //NUMBER PANEL TYPE WIDGET
  numberPanelWidget() {
    return NumberPanelListWidget(
      isBigLayout: isBigLayout,
      itemId: itemId,
      cardItemList: cardListItems,
      isSaleLayoutTwo: false,
      onTabItemId: (value) {
        filterItemById(value.toString());
      },
      ontabCancel: () {
        if (!isLock) {
          setState(() {
            itemId = "";
            filterItemById(itemId);
          });
        }
      },
      onTapItem: ((value1, value2) {
        if (widget.flag == expense) {
          for (ExpensesProduct model in filterExpItemList) {
            if (model.expProductID == value1.guidId) {
              commonOnTapExpenses(model);
            }
          }
        } else {
          for (Products model in filterProductList) {
            if (model.productId == value1.guidId) {
              commonOnTabProduct(model);
            }
          }
        }
      }),
      onTabAddNewItem: () {
        if (widget.flag == expense) {
          if (PermissionFunctions.checkPermission(71)) {
            onTabAddNewExpenses();
          }
        } else {
          if (PermissionFunctions.checkPermission(73)) {
            onTabAddNewProduct();
          }
        }
      },
    );
  }

  categoryPanelWidget() {
    return CategoryPanelWidget(
      isBigLayout: isBigLayout,
      cardItemList: cardListItems,
      categoryListItems: categoryItemList,
      isSaleLayoutTwo: false,
      categoryBoxHeight: isBigLayout ? 0.0 : height * 0.4,
      onPressCategory: ((value) {
        if (widget.flag == expense) {
          for (ExpensesCategoryModel model in expensesCategoryList) {
            if (model.expCategoryID == value.guidId) {
              filterExpensesByCategory(value.guidId, value.categoryName);
              expenseCategoryKey = value.guidId;
            }
          }
        } else {
          for (ProductKey model in productKeyList) {
            if (model.productKeyId == value.guidId) {
              filterExpensesByCategory(value.guidId, value.categoryName);
              expenseCategoryKey = value.guidId;
            }
          }
        }
      }),
      onTapItem: (value1, value2) {
        if (widget.flag == expense) {
          for (ExpensesProduct model in filterExpItemList) {
            if (model.expProductID == value1.guidId) {
              commonOnTapExpenses(model);
            }
          }
        } else {
          for (Products model in filterProductList) {
            if (model.productId == value1.guidId) {
              commonOnTabProduct(model);
            }
          }
        }
      },
      onTabListAddItems: () {
        if (widget.flag == expense) {
          if (PermissionFunctions.checkPermission(71)) {
            onTabAddNewExpenses();
          }
        } else {
          if (PermissionFunctions.checkPermission(73)) {
            onTabAddNewProduct();
          }
        }
      },
    );
  }

  ///Save Expense Dialog
  saveDialog(BuildContext context, int index,
      {List<ExpenseTransactionModel>? expTransactionList, List<PurchaseTransactionsModel>? pTransactionList}) {
    purchaseFunction.qtyController.text = widget.flag == expense
        ? fnUtilities.checkQtyDoubleValue(expTransactionList?[index].quantity ?? "")
        : fnUtilities.checkQtyDoubleValue(pTransactionList?[index].quantity ?? "");
    purchaseFunction.priceController.text = widget.flag == expense
        ? fnUtilities
            .checkQtyDoubleValue(expTransactionList?[index].expProductPrice != null ? expTransactionList![index].expProductPrice ?? '0' : "0")
        : fnUtilities.checkQtyDoubleValue(
            pTransactionList?[index].purchaseProductPrice != null ? pTransactionList![index].purchaseProductPrice ?? '0' : "0");
    amountController.text = widget.flag == expense ? expTransactionList![index].amount ?? '' : pTransactionList![index].amount.toString();
    descriptionController.text = widget.flag == expense ? expTransactionList![index].description ?? "" : pTransactionList![index].description ?? "";
    return showDialog(
        context: context,
        builder: (context) {
          return ExpenseSaveDialog(
              qtyController: purchaseFunction.qtyController,
              priceController: purchaseFunction.priceController,
              descriptionController: descriptionController,
              onPressUpdate: (quantity, expProductPrice, description, validator) async {
                if (!isLock) {
                  if (validator.validate()) {
                    if (widget.flag == expense) {
                      updateExpenceTransactionList(index, expTransactionList ?? [], quantity, expProductPrice, description);
                    } else {
                      updatePurchaseTransactionList(index, pTransactionList ?? [], quantity, expProductPrice, description);
                    }
                    Navigator.pop(context);
                    purchaseFunction.priceController.clear();
                    descriptionController.clear();
                  }
                }
              });
        });
  }

  updateExpenceTransactionList(
      int index, List<ExpenseTransactionModel> expTransactionList, String quantity, String expProductPrice, String description) {
    setState(() {
      double amount = double.parse(quantity) * double.parse(expProductPrice);
      expTransactionList[index].quantity = quantity;
      expTransactionList[index].expProductPrice = expProductPrice;
      expTransactionList[index].description = description;
      expTransactionList[index].amount = fnUtilities.checkQtyDoubleValue(amount.toString());
      transactionCardShow(expTransactionList);
    });
  }

  ///update purchase transaction list items
  updatePurchaseTransactionList(
      int index, List<PurchaseTransactionsModel> purchaseTransactionList, String quantity, String expProductPrice, String description) {
    setState(() {
      double amount = double.parse(quantity) * double.parse(expProductPrice);
      purchaseTransactionList[index].quantity = quantity;
      purchaseTransactionList[index].purchaseProductPrice = expProductPrice;
      purchaseTransactionList[index].description = description;
      purchaseTransactionList[index].amount = amount.toString();
      productTransactionCardShow(purchaseTransactionList);
    });
  }

  overViewDialogWidget(BuildContext context, double totalQty, String titleText, String date) {
    return showDialog(
      context: context,
      builder: (context) {
        return OverViewDialog(
          date: date,
          title: titleText,
          totalFor: widget.flag == expense ? "Total Expenses" : "Total Purchases",
          totalItems: widget.flag == expense ? expenseTransactionList.length.toString() : purchaseTransactionlist.length.toString(),
          totalQty: fnUtilities.checkQtyDoubleValue(totalQty.toString()),
          totalAmount: fnUtilities.checkQtyDoubleValue(totalAmount.toString()),
          onPress: (dateValue) async {
            var isComplete = false;
            if (!isComplete) {
              isComplete = true;
              Navigator.pop(context);
              date = dateValue ?? DateTime.now().toString();
              widget.flag == expense ? onSaveExpense(date.toString()) : onSavePurchase(date.toString());
              isComplete = false;
            }
          },
        );
      },
    );
  }

  ///Alert dialog to remove a element in transaction list
  removeItemDialog(String itemToRemove) {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: "Are you sure to remove this item ?",
                buttonNameOne: "No",
                ontabButtonOne: () {
                  Navigator.pop(context);
                },
                buttonNameTwo: "Yes",
                ontabButtonTwo: () async {
                  if (widget.flag == expense) {
                    int expenseFindOut = expenseTransactionList.indexWhere((element) => element.expProductID == itemToRemove);
                    if (expenseFindOut != -1) {
                      await expensesDBScript.updateExpenseProdDelete(itemToRemove);
                    }
                    setState(() {
                      // Use the `itemToRemove` value to identify and remove the item from the list.
                      expenseTransactionList.removeWhere((model) => model.expProductID == itemToRemove);
                      transactionCardList.removeWhere((element) => element.guidId == itemToRemove);
                    });
                  } else {
                    PurchaseDBScript purchaseDBScript = PurchaseDBScript();
                    int productFindOut = purchaseTransactionlist.indexWhere((element) => element.purchaseProductID == itemToRemove);
                    if (productFindOut != -1) {
                      await purchaseDBScript.updatePurchaseTransProdDelete(itemToRemove);
                    }
                    setState(() {
                      // Use the `itemToRemove` value to identify and remove the item from the list.

                      purchaseTransactionlist.removeWhere((model) => model.purchaseProductID == itemToRemove);
                      transactionCardList.removeWhere((element) => element.guidId == itemToRemove);
                    });
                  }
                  Navigator.pop(context);
                },
              ),
            ),
          );
        });
  }

  /// Transactions Overview
  transactionOverview(BuildContext context) {
    calculateTotalAmount();
    return Padding(
      padding: const EdgeInsets.only(top: 18.0, bottom: 14.0),
      child: Column(
        children: [
          buildRowWidget(
            widget.flag == expense ? "Total Expenses" : "Total Purchase",
            widget.flag == expense ? expenseTransactionList.length.toString() : purchaseTransactionlist.length.toString(),
          ),
          buildRowWidget(
            "Total Quantity",
            fnUtilities.checkQtyDoubleValue(totalQty.toString()),
          ),
          buildRowWidget("Total Amount", fnUtilities.checkQtyDoubleValue(totalAmount.toString())),
        ],
      ),
    );
  }

  buildRowWidget(String title, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (value != null) ...[
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
            Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ]
        ],
      ),
    );
  }
}
