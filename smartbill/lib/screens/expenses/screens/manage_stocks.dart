// // ignore_for_file: sdk_version_ui_as_code

// import 'package:flutter/material.dart';
// import 'package:smartbill/screens/expenses/functions/stocks_functions.dart';

// import '../../../main.dart';
// import '../../../model/products.dart';
// import '../../../model/stocks.dart';
// import '../../../utils/common_function/fn_utilities.dart';
// import '../../../utils/common_function/list_class.dart';
// import '../../../utils/common_function/role_permissions_utilities.dart';
// import '../../../utils/common_widgets/header_footer.dart';
// import '../../../utils/common_widgets/widgets.dart';
// import '../../../utils/constants/colors.dart';
// import '../../../utils/constants/strings_keys.dart';
// import '../../../utils/constants/styles.dart';
// import '../../../utils/dbutils/db_operations_utility.dart';
// import '../../../utils/dbutils/product_details_db_script.dart';
// import '../../../utils/dbutils/stocks_db_script.dart';

// class ManageStocks extends StatefulWidget {
//   static var tag = "/ManageStocks";

//   @override
//   ManageStocksState createState() => ManageStocksState();
// }

// class ManageStocksState extends State<ManageStocks> {
//   var width;
//   var height;

//   // Initialize your utility classes and variables here
//   RolePermissionsUtilities rolePermissions = RolePermissionsUtilities();
//   DBOperations dbOperations = DBOperations();
//   FnUtilities fnUtilities = FnUtilities();
//   StocksDBScript stocksDBScript = StocksDBScript();
//   StocksFunction stocksFunction = StocksFunction();
//   ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();

//   bool isLock = false;
//   List<Stocks> stockProductsList = [];
//   List<Stocks> tempStockProductsList = [];
//   List<Stocks> storeStockProductsList = [];
//   String inventoryType;

//   // Initialize your text controllers
//   List<TextEditingController> stockControllerList = [];
//   final searchController = TextEditingController();

//   bool isQtyDesc = false;
//   bool isProductIDDesc = false;

//   @override
//   void initState() {
//     initPageDetails();
//     super.initState();
//   }

//   @override
//   void dispose() {
//     for (TextEditingController c in stockControllerList) {
//       c.dispose();
//     }
//     super.dispose();
//   }

//   Future<void> workspaceSettings() async {
//     final inventoryTypeSetting = await fnUtilities.workSpaceSetValues(key_inventoryType);
//     setState(() {
//       inventoryType = inventoryTypeSetting == "All" ? "All" : "Specific";
//     });
//   }

//   Future<void> initPageDetails() async {
//     await workspaceSettings();
//     await getAllStockDetails();
//   }

//   Future<void> getAllStockDetails() async {
//     final allRows = await stocksDBScript.getAllStocks();

//     setState(() {
//       tempStockProductsList.clear();
//       tempStockProductsList.addAll(allRows.map((row) => Stocks.fromMap(row)));
//     });

//     await allProductStock();
//   }

//   Future<void> allProductStock() async {
//     stockProductsList.clear();

//     for (Products p in ListUtility.productList) {
//       Stocks stockItem = tempStockProductsList.firstWhere(
//         (element) => element.productId == p.productId,
//         orElse: () => null,
//       );

//       Stocks model = Stocks();
//       model.productId = p.productId;
//       model.workspaceID = MyApp.activeWorkspace.workspaceId;
//       model.updatedQty = "0";
//       model.currentQty = "0";
//       model.productName = p.name;

//       if (stockItem != null) {
//         model.stocksID = stockItem.stocksID;
//         model.stocksSold = stockItem.stocksSold;
//         model.stocksQty = stockItem.stocksQty;
//       } else {
//         model.stocksSold = "0";
//         model.stocksQty = "0";
//       }

//       if (inventoryType == "Specific" && p.enableInventory == 1) {
//         stockProductsList.add(model);
//       } else if (inventoryType == "All") {
//         stockProductsList.add(model);
//       }
//     }
//     await assignValue();
//   }

//   void assignValue() {
//     setState(() {
//       for (Stocks s in stockProductsList) {
//         try {
//           s.currentQty = (int.parse(s.stocksQty) - int.parse(s.stocksSold)).toString();
//           s.productSaleID = ListUtility.productList.firstWhere((element) => element.productId == s.productId, orElse: () => null).productSaleId;
//         } catch (ex) {
//           s.currentQty = "0";
//         }
//       }

//       storeStockProductsList.clear();
//       storeStockProductsList.addAll(stockProductsList);

//       try {
//         stockProductsList.sort((a, b) => int.parse(a.currentQty).compareTo(int.parse(b.currentQty)));
//       } catch (ex) {}
//     });
//   }

//   void filterProductsByName(String name) {
//     if (name.isNotEmpty) {
//       final updatedProductQtyMap = {
//         for (var p in storeStockProductsList) p.productId: p.updatedQty,
//       };

//       setState(() {
//         stockProductsList.clear();
//         for (var p in storeStockProductsList) {
//           if (p.productName.toLowerCase().contains(name.toLowerCase())) {
//             p.updatedQty = updatedProductQtyMap[p.productId] ?? p.updatedQty;
//             stockProductsList.add(p);
//           }
//         }
//       });
//     } else {
//       setState(() {
//         stockProductsList.clear();
//         stockProductsList.addAll(storeStockProductsList);
//       });
//     }
//   }

//   void sortByQty() {
//     try {
//       setState(() {
//         isQtyDesc = !isQtyDesc;
//         stockProductsList.sort((a, b) => isQtyDesc ? int.parse(b.currentQty).compareTo(int.parse(a.currentQty)) : int.parse(a.currentQty).compareTo(int.parse(b.currentQty)));
//       });
//     } catch (ex) {}
//   }

//   void sortByProductID() {
//     try {
//       setState(() {
//         isProductIDDesc = !isProductIDDesc;
//         stockProductsList.sort((a, b) => isProductIDDesc ? b.productSaleID.compareTo(a.productSaleID) : a.productSaleID.compareTo(b.productSaleID));
//       });
//     } catch (ex) {}
//   }

//   void updateStockFunc(bool isAdd, int index) {
//     setState(() {
//       final item = stockProductsList[index];
//       final currentQty = int.parse(item.updatedQty == "0" ? item.currentQty : item.updatedQty);
//       final stockChange = isAdd ? int.parse(stockControllerList[index].text) : -int.parse(stockControllerList[index].text);
//       final updatedQty = (currentQty + stockChange).toString();
//       item.updatedQty = updatedQty;
//       item.modifiedStocks = stockControllerList[index].text;
//       stockControllerList[index].clear();
//     });
//   }

//   Future<void> modifyStockQty() async {
//     final updatedStockItems = stockProductsList.where((item) => int.parse(item.updatedQty) != 0).toList();

//     if (updatedStockItems.isNotEmpty) {
//       for (Stocks item in updatedStockItems) {
//         final updatedQty = int.parse(item.updatedQty);
//         final stocksQty = int.parse(item.stocksQty);
//         final newStockQty = updatedQty + stocksQty;

//         ModifyStocksDto model = ModifyStocksDto(
//           productId: item.productId,
//           stocksSold: item.stocksSold,
//           stocksQty: newStockQty.toString(),
//           modifiedStocks: item.modifiedStocks,
//           stocksID: item.stocksID,
//         );

//         await stocksFunction.modifyStocks(model);
//       }

//       showToast("Stocks are updated successfully");
//       searchController.clear();
//       await initPageDetails();
//     }
//   }
//   // Rest of your code remains the same

//   @override
//   Widget build(BuildContext context) {
//     width = MediaQuery.of(context).size.width;
//     height = MediaQuery.of(context).size.height;

//     return Scaffold(
//       backgroundColor: secondaryTextColor,
//       resizeToAvoidBottomInset: true,
//       appBar: buildAppBar(),
//       body: SingleChildScrollView(
//         child: Column(
//           children: [
//             SizedBox(height: 10),
//             buildSearchBar(),
//             SizedBox(height: 10),
//             buildStockList(),
//           ],
//         ),
//       ),
//     );
//   }

//   Widget buildAppBar() {
//     return BaseAppBar(
//       title: text("Stocks", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
//       appBar: AppBar(),
//       widgets: <Widget>[
//         IconButton(
//           onPressed: () async {
//             if (!isLock) {
//               isLock = true;
//               await modifyStockQty();
//               isLock = false;
//             }
//           },
//           icon: Icon(Icons.save),
//         ),
//       ],
//     );
//   }

//   Widget buildSearchBar() {
//     return Row(
//       mainAxisAlignment: MainAxisAlignment.spaceBetween,
//       children: [
//         Container(
//           padding: const EdgeInsets.only(top: 7, left: 5),
//           width: width / 1.5,
//           height: 50,
//           child: TextField(
//             controller: searchController,
//             onChanged: filterProductsByName,
//             style: TextStyle(color: Colors.black),
//             autofocus: false,
//             decoration: InputDecoration(
//               labelText: 'Search by product name',
//               border: InputBorder.none,
//               enabledBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(4),
//                 borderSide: BorderSide(color: greyColor, width: 0.0),
//               ),
//               focusedBorder: OutlineInputBorder(
//                 borderRadius: BorderRadius.circular(4),
//                 borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
//               ),
//               suffixIcon: InkWell(
//                 onTap: () {
//                   setState(() {
//                     searchController.clear();
//                     filterProductsByName("");
//                   });
//                 },
//                 child: Icon(Icons.clear, color: primaryTextColor),
//               ),
//             ),
//           ),
//         ),
//         buildColumnWithIconButton("Qty", () => sortByQty(), !isQtyDesc ? Icons.arrow_upward : Icons.arrow_downward),
//         buildColumnWithIconButton("ID", () => sortByProductID(), !isProductIDDesc ? Icons.arrow_upward : Icons.arrow_downward),
//       ],
//     );
//   }

//   Widget buildColumnWithIconButton(String text, Function onPressed, IconData icon) {
//     return Column(
//       children: [
//         Text(text),
//         IconButton(onPressed: onPressed, icon: Icon(icon)),
//       ],
//     );
//   }

//   Widget buildStockList() {
//     return ListView.builder(
//       padding: EdgeInsets.only(right: 3),
//       scrollDirection: Axis.vertical,
//       itemCount: stockProductsList.length,
//       shrinkWrap: true,
//       physics: ScrollPhysics(),
//       itemBuilder: (context, index) {
//         stockControllerList.add(TextEditingController());
//         return buildStockItem(index);
//       },
//     );
//   }

//   Widget buildStockItem(int index) {
//     final item = stockProductsList[index];

//     return Card(
//       margin: EdgeInsets.symmetric(vertical: 0, horizontal: 1),
//       color: index.isEven ? secondaryTextColor : Colors.blue[50],
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceBetween,
//         children: [
//           Container(
//             width: width / 2,
//             child: buildStockItemDetails(item),
//           ),
//           Container(
//             padding: EdgeInsets.only(top: 5),
//             width: width / 2.2,
//             child: buildStockItemInput(index),
//           ),
//         ],
//       ),
//     );
//   }

//   Widget buildStockItemDetails(Stocks item) {
//     return Column(
//       children: [
//         Row(
//           children: [
//             Container(
//               padding: const EdgeInsets.only(bottom: 2, left: 2),
//               width: width / 2,
//               child: Text("${item.productSaleID != null ? item.productSaleID : ""}  " + item.productName),
//             )
//           ],
//         ),
//         Row(
//           children: [
//             buildContainerWithText("Current Qty", FontWeight.bold),
//             buildContainerWithText("Updated Qty", FontWeight.bold),
//           ],
//         ),
//         Row(
//           children: [
//             buildContainerWithText(item.currentQty, null),
//             buildContainerWithText(item.updatedQty, null),
//           ],
//         ),
//       ],
//     );
//   }

//   Widget buildContainerWithText(String text, FontWeight fontWeight) {
//     return Container(
//       alignment: Alignment.center,
//       width: width / 4,
//       child: Text(text, style: TextStyle(fontWeight: fontWeight)),
//     );
//   }

//   Widget buildStockItemInput(int index) {
//     return Column(
//       children: [
//         Row(
//           mainAxisAlignment: MainAxisAlignment.center,
//           children: [
//             Container(
//               width: 108,
//               height: 50,
//               child: TextField(
//                 controller: stockControllerList[index],
//                 keyboardType: TextInputType.number,
//                 maxLength: 7,
//               ),
//             ),
//           ],
//         ),
//         Row(
//           mainAxisAlignment: MainAxisAlignment.spaceAround,
//           children: [
//             buildIconButtonWidget(index, Icons.add, Colors.green),
//             SizedBox(width: 10),
//             buildIconButtonWidget(index, Icons.remove, Colors.red),
//           ],
//         ),
//       ],
//     );
//   }

//   // Define a function to simplify the code for the InkWell widget with an icon
//   InkWell buildIconButtonWidget(int index, IconData icon, Color color) {
//     return InkWell(
//       onTap: () async {
//         if (!isLock) {
//           isLock = true;
//           if (stockControllerList[index].text != "") {
//             updateStockFunc(icon == Icons.add, index);
//           }
//           isLock = false;
//         }
//       },
//       child: Padding(
//         padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 25),
//         child: Icon(
//           icon,
//           color: color,
//         ),
//       ),
//     );
//   }
// }
