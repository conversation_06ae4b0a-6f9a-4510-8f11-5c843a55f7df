// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:flutter_svg/svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/screens/expenses/screens/manage_expenses_screen.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/value_notifier.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import '../../../Repositories/reports_repository.dart';
import '../../../model/expense_dashboard_model.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/images.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/roles_permissions/permission_functions.dart';

class ExpensesDashboard extends StatefulWidget {
  const ExpensesDashboard({Key? key}) : super(key: key);

  @override
  State<ExpensesDashboard> createState() => _ExpensesDashboardState();
}

class _ExpensesDashboardState extends State<ExpensesDashboard> {
  FnUtilities fnUtilities = new FnUtilities();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  double width = 0.0;
  double height = 0.0;
  bool isLock = false;
  bool isBiglayout = false;
  double profit = 0.0;
  bool isLoading = false;

  bool enableExpenses = false;
  bool enablePurchase = false;

  //ExpensesDashboardDetails expensesDashboardDetails;
  DashboardInfo? dashboardInfo;
  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  getDashboardDetails(fromDate, toDate) async {
    setState(() {
      isLoading = true;
    });
    await getExpenseDashboardReport(fromDate, toDate).then(
      (value) {
        if (value.status == 1) {
          dashboardInfo = value.result;
          profit = dashboardInfo?.profit ?? 0.0;
        }
      },
    );
    setState(() {
      isLoading = false;
    });
  }

  initPageDetails() async {
    setState(() {
      enableExpenses = fnUtilities.workSpaceSetValues(key_expense) == "1";
      enablePurchase = fnUtilities.workSpaceSetValues(key_purchase) == "1";
    });
    getDashboardDetails(fromDate, toDate);
  }

  @override
  void initState() {
    super.initState();
    initPageDetails();
  }


  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBiglayout = width > tabletWidth ? true : false;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: AppBar(
          backgroundColor: appThemeColor,
          elevation: 0,
        //Expenses Dashboard
         title: TextWidget("Expenses Dashboard",
              textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          actions: MyApp.activeWorkspace.workspaceId != null
              ? <Widget>[
                  syncIconWidget(),
                ]
              : null,
      ),
      body: Padding(
        padding: const EdgeInsets.all(4.0),
        child: isLoading
            ? Center(
                child: CircularProgressIndicator(
                  valueColor: new AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : SingleChildScrollView(
                child: BootstrapRow(
                  children: [
                    /// Expenses Dashboard contet
                    BootstrapCol(
                      sizes: 'col-md-6 col-sm-12',
                      child: Column(
                        children: [
                          BootstrapRow(children: [
                            BootstrapCol(
                              sizes: 'col-md-8 col-sm-12',

                              /// Date picker to select from date and to date
                              child: Padding(
                                padding: const EdgeInsets.all(4.0),
                                child: Row(
                                  mainAxisAlignment: isBiglayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                  children: [
                                    ///To pick from date
                                    InkWell(
                                      onTap: () {
                                        selectDate(context, 'fromDate');
                                      },
                                      child: Card(
                                        elevation: 4,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "${fnUtilities.convertDate(fromDate.toString())}",
                                                style: secondaryTextStyle(),
                                              ),
                                              SizedBox(
                                                width: 15,
                                              ),
                                              Icon(Icons.calendar_today)
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),

                                    ///To pick to date
                                    InkWell(
                                      onTap: () {
                                        selectDate(context, 'toDate');
                                      },
                                      child: Card(
                                        elevation: 4,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                fnUtilities.convertDate(toDate.toString()),
                                                style: secondaryTextStyle(),
                                              ),
                                              SizedBox(
                                                width: 15,
                                              ),
                                              Icon(Icons.calendar_today)
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            BootstrapCol(
                              sizes: 'col-md-4 col-sm-12',

                              ///Submit button
                              child: Padding(
                                padding: const EdgeInsets.only(top: 6.0),
                                child: Row(
                                  mainAxisAlignment: isBiglayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        if (PermissionFunctions.checkPermission(67)) {
                                          setState(() {
                                            initPageDetails();
                                          });
                                        }
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 45,
                                        width: isBiglayout ? width * 0.1 : width * 0.95,
                                        child: Text("Submit",
                                            style: TextStyle(
                                              color: appThemeColor,
                                              fontSize: 16,
                                              fontWeight: FontWeight.w700,
                                            )),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(5),
                                          color: whiteColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ]),
                          SizedBox(
                            height: 10,
                          ),
                          // if (isLoading) ...[
                          //   CircularProgressIndicator(
                          //     valueColor: new AlwaysStoppedAnimation<Color>(Colors.white),
                          //   ),
                          // ] else ...[
                          Container(
                            // height: 320,
                            width: width,
                            margin: EdgeInsets.all(8),
                            decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 20),
                            padding: EdgeInsets.all(10),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Column(
                                  children: [
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        SizedBox(
                                          width: isBiglayout ? width * 0.1 : width * 0.25,
                                          child: TextWidget(
                                            "",
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(
                                          width: isBiglayout ? width * 0.08 : width * 0.18,
                                          child: TextWidget(
                                            "Item",
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(
                                          width: isBiglayout ? width * 0.08 : width * 0.18,
                                          child: TextWidget(
                                            "Quantity",
                                            fontSize: 16,
                                            fontWeight: FontWeight.bold,
                                          ),
                                        ),
                                        SizedBox(
                                          width: isBiglayout ? width * 0.12 : width * 0.23,
                                          child: Align(
                                            alignment: Alignment.topRight,
                                            child: TextWidget(
                                              "Amount",
                                              fontSize: 16,
                                              fontWeight: FontWeight.bold,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),

                                    /// For Expenses
                                    rowTextWidget("Expenses", dashboardInfo?.totalExpense ?? 0, dashboardInfo?.totalExpenseQuantity ?? 0,
                                        dashboardInfo?.totalExpenseAmount ?? 0),

                                    /// For Purchase
                                    rowTextWidget("Purchase", dashboardInfo?.totalPurchase ?? 0, dashboardInfo?.totalPurchaseQuantity ?? 0,
                                        dashboardInfo?.totalPurchaseAmount ?? 0),

                                    /// For Sales
                                    rowTextWidget("Sales", dashboardInfo?.totalSales ?? 0, dashboardInfo?.totalSalesQuantity ?? 0,
                                        dashboardInfo?.totalSalesAmount ?? 0),

                                    SizedBox(
                                      height: 20,
                                    ),
                                    Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      crossAxisAlignment: CrossAxisAlignment.end,
                                      children: [
                                        TextWidget(
                                          'Profit',
                                          fontWeight: FontWeight.bold,
                                        ),
                                        TextWidget(
                                          fnUtilities.checkQtyDoubleValue(profit.toString()),
                                          // expensesDashboardDetails.profit,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    /// Navigations to expenses and purchase screen
                    BootstrapCol(
                        sizes: 'col-md-6 col-sm-12',
                        child: Container(
                          alignment: Alignment.center,
                          width: width,
                          padding: EdgeInsets.all(8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              /// Navigate to Manage expenses
                              if (enableExpenses) ...[
                                InkWell(
                                  onTap: () {
                                    if (PermissionFunctions.checkPermission(68)) {
                                      if (!isLock) {
                                        isLock = true;
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) => ManageExpensesScreen(
                                                      flag: expense,
                                                    ))).then((value) async {
                                                      SyncUtility syncUtility = SyncUtility();
                                                      await syncUtility.checkSyncStatus();
                                                    });;
                                        isLock = false;
                                      }
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(12),
                                    decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: <Widget>[
                                        Container(
                                          height: isBiglayout ? (width * 0.08) : (width * 0.2),
                                          width: isBiglayout ? (width * 0.10) : (width * 0.25),
                                          padding: EdgeInsets.all(10),
                                          decoration: boxDecoration(bgColor: Colors.blueAccent, radius: 10),
                                          child: SvgPicture.asset(
                                            wallet,
                                            color: secondaryTextColor,
                                          ),
                                        ),
                                        TextWidget("Manage Expenses", isLongText: true, isCentered: true, textColor: primaryTextColor, fontSize: 16.0)
                                      ],
                                    ),
                                  ),
                                ),
                              ],

                              SizedBox(
                                width: isBiglayout ? 50 : 20,
                              ),

                              /// Navigate to Manage purchase
                              if (enablePurchase) ...[
                                InkWell(
                                  onTap: () {
                                    if (PermissionFunctions.checkPermission(69)) {
                                      if (!isLock) {
                                        isLock = true;
                                        Navigator.push(
                                            context,
                                            MaterialPageRoute(
                                                builder: (context) => ManageExpensesScreen(
                                                      flag: purchase,
                                                    ))).then((value) async {
                                                      SyncUtility syncUtility = SyncUtility();
                                                      await syncUtility.checkSyncStatus();
                                                    });
                                        isLock = false;
                                      }
                                    }
                                  },
                                  child: Container(
                                    padding: EdgeInsets.all(12),
                                    decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: <Widget>[
                                        Container(
                                          height: isBiglayout ? (width * 0.08) : (width * 0.2),
                                          width: isBiglayout ? (width * 0.10) : (width * 0.25),
                                          padding: EdgeInsets.all(10),
                                          decoration: boxDecoration(bgColor: Colors.redAccent, radius: 10),
                                          child: SvgPicture.asset(
                                            wallet,
                                            color: secondaryTextColor,
                                          ),
                                        ),
                                        TextWidget("Manage Purchases",
                                            isLongText: true, isCentered: true, textColor: primaryTextColor, fontSize: 16.0)
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ))
                  ],
                ),
              ),
      ),
    );
  }

  /// Row widget for dashboard
  rowTextWidget(
    String text1,
    int text2,
    int text3,
    double text4,
  ) {
    return Padding(
      padding: const EdgeInsets.only(top: 4.0, bottom: 4.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          SizedBox(
            width: isBiglayout ? width * 0.1 : width * 0.25,
            child: TextWidget(
              text1,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(
            width: isBiglayout ? width * 0.08 : width * 0.18,
            child: TextWidget(
              text2.toString(),
              fontSize: 16,
            ),
          ),
          SizedBox(
            width: isBiglayout ? width * 0.08 : width * 0.18,
            child: TextWidget(
              text3.toString(),
              isCentered: true,
              fontSize: 16,
            ),
          ),
          SizedBox(
            width: isBiglayout ? width * 0.12 : width * 0.23,
            child: Align(
              alignment: Alignment.topRight,
              child: TextWidget(
                fnUtilities.checkQtyDoubleValue(text4.toString()),
                fontSize: 16,
              ),
            ),
          ),
        ],
      ),
    );
  }

   Widget syncIconWidget() {
    return ValueListenableBuilder<SyncStatusInfo>(
      valueListenable: ValueNotifiers.instance.syncStatusNotifier,
      builder: (BuildContext context, SyncStatusInfo? value, Widget? child) {
        Color syncIconColor = value?.syncStatus == 0
            ? Colors.deepOrangeAccent
            : value?.syncStatus == 2
                ? Colors.yellowAccent
                : secondaryTextColor;
        return InkWell(
          onTap: () async {
            SyncUtility syncUtility = new SyncUtility();
            if (await InternetConnectionChecker().hasConnection) {
              try {
                //if (value?.syncStatus != 2) {
                  await syncUtility.triggerManualSync(isPullSync: true);
                  syncUtility.checkSyncStatus();
                  await getDashboardDetails(fromDate, toDate);
               // }
              } catch (e) {
                ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(0, 0);
                showToast("Sync failed");
              }
            } else {
              fnUtilities.internetTurnOnAlertDialog(context);
            }
          },
          child: value?.syncStatus == 2
              ? buildCircularLoadingIndicator(value?.syncPercentage)
              : Icon(
                  Icons.sync,
                  color: syncIconColor,
                  size: 30,
                ),
        );
      },
    );
  }
   Widget buildCircularLoadingIndicator(int? syncPercentage) {
    return Padding(
      padding: const EdgeInsets.only(right: 5),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: 25,
            width: 25,
            child: CircularProgressIndicator(
              // value: syncPercentage.toDouble(),
              // backgroundColor: Colors.yellowAccent,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.yellowAccent),
              strokeWidth: 3.0,
            ),
          ),
          Text(
            fnUtilities.checkQtyDoubleValue(syncPercentage.toString()) ?? "",
            style: TextStyle(fontSize: 10, color: secondaryTextColor), // Customize the text style
          ),
        ],
      ),
    );
  }
}
