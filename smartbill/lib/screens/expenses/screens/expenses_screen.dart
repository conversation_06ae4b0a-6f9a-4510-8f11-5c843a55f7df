// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/screens/expenses/screens/add_expenses.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';
import '../../../utils/common_widgets/rounded_button.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import '../model/expense_category_model.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/images.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/expenses_db_script.dart';

class ExpensesScreen extends StatefulWidget {
  const ExpensesScreen({Key? key}) : super(key: key);

  @override
  State<ExpensesScreen> createState() => _ExpensesScreenState();
}

class _ExpensesScreenState extends State<ExpensesScreen> {
  double width = 0.0;
  double height = 0.0;
  final searchController = new TextEditingController();
  List<ExpensesCategoryModel> expensesCategoryList = [];
  List<ExpensesProduct> filterExpenseItemList = [];
  ExpensesDBScript expensesDBScript = ExpensesDBScript();
  bool isLoading = false;
  bool isLock = false;
  bool isProductIDDesc = false;
  bool isProductNameDesc = false;
  bool isProductPriceDesc = false;
  String sortType = "ID";
  ExpensesCategoryModel? selectedCategory;
  initPageDetails() async {
    await assignExpenseList();
    await getAllExpenseCategoryList();
  }

  assignExpenseList() {
    if (ListUtility.expensesItemList.isNotEmpty) {
      filterExpenseItemList.clear();
      setState(() {
        filterExpenseItemList.addAll(ListUtility.expensesItemList);
      });
    } else {
      setState(() {
        filterExpenseItemList.clear();
      });
    }
  }

  getAllExpenseCategoryList() {
    if (ListUtility.expensesCategoryList.isNotEmpty) {
      setState(() {
        expensesCategoryList.clear();
        ExpensesCategoryModel model = ExpensesCategoryModel();
        model.expCategoryName = "All";
        expensesCategoryList.add(model);
        expensesCategoryList.addAll(ListUtility.expensesCategoryList);
      });
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  void filterExpenseByName(String name) async {
    if (name != "") {
      setState(() {
        filterExpenseItemList.clear();
        for (ExpensesProduct expItem in ListUtility.expensesItemList) {
          if (expItem.expProductName?.toLowerCase().contains(name.toLowerCase()) ?? false) {
            filterExpenseItemList.add(expItem);
          }
        }
      });
    } else {
      setState(() {
        filterExpenseItemList.clear();
        filterExpenseItemList.addAll(ListUtility.expensesItemList);
      });
    }
  }

  filterExpenseByCategoryName(String name) async {
    if (name != "") {
      setState(() {
        filterExpenseItemList.clear();
        for (ExpensesProduct expItem in ListUtility.expensesItemList) {
          if (expItem.expCategoryName != null) {
            if (expItem.expCategoryName?.toLowerCase().contains(name.toLowerCase()) ?? false) {
              filterExpenseItemList.add(expItem);
            }
          }
        }
      });
    } else {
      setState(() {
        filterExpenseItemList.clear();
        filterExpenseItemList.addAll(ListUtility.expensesItemList);
      });
    }
  }

  updateWithDelete(String? id) async {
    ExpensesProduct expensesItem = ExpensesProduct(expProductID: id, sync: 0);
    await expensesDBScript.deleteUpdateExpenseItemTable(expensesItem);
    setState(() {
      assignExpenseList();
    });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(
          "Expenses",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: expenseAddButton(),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                SizedBox(
                  height: 3,
                ),
                searchFilterSortFunction(),
                productListFunction(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget expenseAddButton() {
    return InkWell(
        onTap: () {
          if (PermissionFunctions.checkPermission(55)) {
            FocusScope.of(context).unfocus();
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddExpenses())).then(
              (value) async {
                await fnUtilities.getAllExpenses();
                assignExpenseList();
                getAllExpenseCategoryList();
              },
            );
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }

  Widget productListFunction() {
    return Expanded(
      child: Align(
        alignment: Alignment.topCenter,
        child: filterExpenseItemList.isNotEmpty
            ? ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 5),
                scrollDirection: Axis.vertical,
                itemCount: filterExpenseItemList.length,
                shrinkWrap: true,
                physics: ScrollPhysics(),
                itemBuilder: (context, index) {
                  //DELETE ALERT DIALOG FOR PRODUCTS
                  var item = filterExpenseItemList[index];
                  return GestureDetector(
                    onLongPress: () {
                      if (PermissionFunctions.checkPermission(58)) {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return DeleteDialog(
                              message: "Are you sure you want to delete ${item.expProductName}?",
                              buttonNameOne: "Cancel",
                              onPressOne: () {
                                Navigator.pop(context);
                              },
                              buttonNameTwo: "Yes",
                              onPressTwo: () async {
                                DBFuctionsScript dbScript = DBFuctionsScript();
                                CommonDB commonDB = CommonDB();
                                final syncNo = await commonDB.checkNonSyncCommonFunction(
                                    db_expensesProduct, db_expProductID, item.expProductID, db_expProductSync);
                                if (syncNo == 1) {
                                  await updateWithDelete(item.expProductID);
                                } else {
                                  await dbScript.deleteFunction(db_expensesProduct, db_expProductID, item.expProductID ?? '');
                                }
                                await fnUtilities.getAllExpenses();
                                await assignExpenseList();
                                Navigator.pop(context);
                              },
                            );
                          },
                        );
                      }
                    },
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 10),
                          // padding: EdgeInsets.symmetric(
                          //     horizontal: 5),
                          child: InkWell(
                            onTap: () async {
                              if (PermissionFunctions.checkPermission(56)) {
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => AddExpenses(
                                              id: item.expProductID,
                                            ))).then((value) async {
                                  await fnUtilities.getAllExpenses();
                                  assignExpenseList();
                                });
                              }
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                /// serial number
                                Column(
                                  children: [
                                    Container(
                                      width: width * 0.15,
                                      alignment: Alignment.center,
                                      margin: EdgeInsets.only(right: 5),
                                      child: TextWidget(item.id.toString(), fontSize: textSizeLargeMedium, isCentered: true),
                                    ),
                                  ],
                                ),

                                /// product name , expProductPrice , Category
                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      if (item.expProductName != null) ...[
                                        Padding(
                                          padding: const EdgeInsets.only(right: 8),
                                          child: Container(
                                            child: TextWidget(item.expProductName ?? '',
                                                textColor: primaryTextColor,
                                                fontSize: textSizeMedium,
                                                fontWeight: FontWeight.bold,
                                                isCentered: false,
                                                isLongText: true),
                                          ),
                                        ),
                                      ],
                                      Row(mainAxisAlignment: MainAxisAlignment.start, children: <Widget>[
                                        Container(
                                          width: width * 0.25,
                                          child: TextWidget(
                                              item.expProductPrice == "" || item.expProductPrice == null ? "" : "\u{20B9}" + item.expProductPrice!,
                                              textColor: primaryTextColor,
                                              fontSize: textSizeMedium,
                                              fontFamily: fontSemibold),
                                        ),
                                        // SizedBox(
                                        //   width: width * 0.1,
                                        // ),

                                        Flexible(
                                          child: Container(
                                            child: TextWidget("${item.expCategoryName != null ? item.expCategoryName : 'None'}",
                                                fontSize: textSizeMedium, textColor: greyTextColor, isCentered: true, maxLine: 2),
                                          ),
                                        ),
                                      ]),
                                    ],
                                  ),
                                ),

                                /// right arrow
                                Column(
                                  children: [
                                    InkWell(
                                      child: SvgPicture.asset(
                                        leftArrowSVG,
                                        color: primaryTextColor,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Divider(height: 1.0, color: t5ViewColor)
                      ],
                    ),
                  );
                })
            : Center(
                child: Text("Expense Not Found"),
              ),
      ),
    );
  }

  filterDialog(BuildContext context) async {
    selectedCategory = selectedCategory == null ? expensesCategoryList.first : selectedCategory;
    return showDialog(
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              title: Text(
                "Filter",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
              ),
              content: Container(
                // width: double.maxFinite,
                height: height / 4,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          // width: width / 1.5,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 1.0),
                              labelText: 'Category',
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: new DropdownButton<ExpensesCategoryModel>(
                                hint: Text("All"),
                                value: selectedCategory,
                                items: expensesCategoryList.map((ExpensesCategoryModel value) {
                                  return new DropdownMenuItem<ExpensesCategoryModel>(
                                    value: value,
                                    child: new Text(value.expCategoryName ?? ''),
                                  );
                                }).toList(),
                                onChanged: (ExpensesCategoryModel? value) async {
                                  if (!isLock) {
                                    isLock = true;
                                    setState(() {
                                      selectedCategory = value;
                                    });
                                    isLock = false;
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        RoundedButton(
                          width: 100,
                          title: "Reset",
                          onPressed: () async {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              searchController.clear();
                              selectedCategory = expensesCategoryList.first;
                              await assignExpenseList();
                              isLock = false;
                            }
                          },
                        ),
                        RoundedButton(
                          width: 100,
                          title: "Filter",
                          onPressed: () async {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);

                              if (selectedCategory?.expCategoryName == "All") {
                                await assignExpenseList();
                              } else {
                                await filterExpenseByCategoryName(selectedCategory?.expCategoryName ?? '');
                              }
                              isLock = false;
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }

  sortDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
                contentPadding: EdgeInsets.all(10),
                scrollable: true,
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                        // width: double.maxFinite,
                        child: Column(
                      children: [
                        Row(
                          children: [
                            new Radio(
                              value: "ID",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value ?? '';
                                });
                              },
                            ),
                            new Text('Sort By ID'),
                          ],
                        ),
                        Row(
                          children: [
                            new Radio(
                              value: "Name",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value ?? '';
                                });
                              },
                            ),
                            new Text('Sort By Name'),
                          ],
                        ),
                        Row(
                          children: [
                            new Radio(
                              value: "Price",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value ?? '';
                                });
                              },
                            ),
                            new Text('Sort By Price'),
                          ],
                        )
                      ],
                    )),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        RoundedButton(
                          width: 100,
                          title: "Cancel",
                          onPressed: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                        ),
                        RoundedButton(
                          width: 100,
                          title: "Sort",
                          onPressed: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              if (sortType == "ID") {
                                sortByProductID();
                              }
                              if (sortType == "Name") {
                                sortByProductName();
                              }
                              if (sortType == "Price") {
                                sortByProductPrice();
                              }
                              isLock = false;
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ));
          });
        });
  }

  sortByProductID() {
    try {
      setState(() {
        if (isProductIDDesc) {
          isProductIDDesc = false;
          filterExpenseItemList.sort((a, b) => a.id!.compareTo(b.id!));
        } else {
          isProductIDDesc = true;
          filterExpenseItemList.sort((a, b) => b.id!.compareTo(a.id!));
        }
      });
    } catch (ex) {}
  }

  sortByProductName() {
    try {
      setState(() {
        if (isProductNameDesc) {
          isProductNameDesc = false;
          filterExpenseItemList.sort((a, b) => (a.expProductName?.toLowerCase() ?? '').compareTo(b.expProductName?.toLowerCase() ?? ''));
          // filterExpenseItemList.sort((a, b) => a.expProductName.toLowerCase().compareTo(b.expProductName!.toLowerCase()));
        } else {
          isProductNameDesc = true;
          filterExpenseItemList.sort((a, b) => (b.expProductName?.toLowerCase() ?? '').compareTo(a.expProductName?.toLowerCase() ?? ''));

          // filterExpenseItemList.sort((a, b) => b.expProductName?.toLowerCase() ?? ''.compareTo(a.expProductName?.toLowerCase() ?? ''));
        }
      });
    } catch (ex) {}
  }

  sortByProductPrice() {
    try {
      setState(() {
        // if (isProductPriceDesc) {
        //   isProductPriceDesc = false;

        //   filterExpenseItemList.sort((a, b) => a.expProductPrice != null && a.expProductPrice != ""
        //       ? a.expProductPrice.compareTo(b.expProductPrice != null && b.expProductPrice != "" ? b.expProductPrice : 0)
        //       : 0.compareTo(b.expProductPrice != null && b.expProductPrice != "" ? b.expProductPrice : 0));
        // } else {
        //   isProductPriceDesc = true;
        //   filterExpenseItemList.sort((a, b) => b.expProductPrice != null && b.expProductPrice != ""
        //       ? b.expProductPrice.compareTo(a.expProductPrice != null && a.expProductPrice != "" ? a.expProductPrice : 0)
        //       : 0.compareTo(a.expProductPrice != null && a.expProductPrice != "" ? a.expProductPrice : 0));
        // }

        if (isProductPriceDesc) {
          isProductPriceDesc = false;
          filterExpenseItemList.sort((a, b) {
            if (a.expProductPrice is Comparable && b.expProductPrice is Comparable) {
              return (a.expProductPrice as Comparable).compareTo(b.expProductPrice);
            } else {
              return 0;
            }
          });
        } else {
          isProductPriceDesc = true;
          filterExpenseItemList.sort((a, b) {
            if (a.expProductPrice is Comparable && b.expProductPrice is Comparable) {
              return (b.expProductPrice as Comparable).compareTo(a.expProductPrice);
            } else {
              return 0;
            }
          });
        }
      });
    } catch (ex) {}
  }

  Widget searchFilterSortFunction() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(top: 7, left: 5),
            height: 50,
            child: TextField(
              controller: searchController,
              onChanged: filterExpenseByName,
              style: TextStyle(color: Colors.black),
              autofocus: false,
              decoration: InputDecoration(
                labelText: 'Search ',
                border: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: grey, width: 0.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                ),
                suffixIcon: InkWell(
                  onTap: () {
                    setState(() {
                      searchController.clear();
                      filterExpenseByName("");
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(
                      searchIconSVG,
                      color: greyColor,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.only(top: 7, left: 10, right: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () {
                  FocusScope.of(context).unfocus();
                  filterDialog(context);
                },
                child: SvgPicture.asset(
                  filterIconSVG,
                  color: greyColor,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 5),
                child: InkWell(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    sortDialog(context);
                  },
                  child: SvgPicture.asset(
                    sortIconSVG,
                    color: greyColor,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }
}
