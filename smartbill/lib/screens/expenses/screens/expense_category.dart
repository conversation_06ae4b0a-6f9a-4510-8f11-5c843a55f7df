import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/expenses/screens/add_expense_category.dart';

import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import '../model/expense_category_model.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/expenses_db_script.dart';

class ExpensesCategory extends StatefulWidget {
  const ExpensesCategory({Key? key}) : super(key: key);

  @override
  State<ExpensesCategory> createState() => _ExpensesCategoryState();
}

class _ExpensesCategoryState extends State<ExpensesCategory> {
  ExpensesDBScript expensesDBScript = ExpensesDBScript();
  List<ExpensesCategoryModel> filterCategoryList = [];
  bool isLock = false;

  void initState() {
    super.initState();
    getAllExpensesCategoryList();
  }

  getAllExpensesCategoryList() {
    if (ListUtility.expensesCategoryList.isNotEmpty) {
      setState(() {
        filterCategoryList.clear();
        filterCategoryList.addAll(ListUtility.expensesCategoryList);
      });
    }
  }

  void deleteExpenseByName(id) async {
    if (!isLock) {
      isLock = true;
      ExpensesCategoryModel model = ExpensesCategoryModel();
      model.expCategoryID = id;
      model.status = 2;
      model.workspaceID = MyApp.activeWorkspace.workspaceId;
      model.expCategorySync = 0;
      await expensesDBScript.updateProductsKeyDelete(model);
      await fnUtilities.getAllExpensesCategoryList();
      getAllExpensesCategoryList();
      Navigator.pop(context);
      Navigator.pop(context);
      isLock = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: text(" Expenses Category ", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          IconButton(
            onPressed: () async {
              if (PermissionFunctions.checkPermission(62)) {
                Navigator.push(context, MaterialPageRoute(builder: (context) => AddExpenseCategory()))
                    .then((value) => {fnUtilities.getAllExpensesCategoryList(), getAllExpensesCategoryList()});
              }
            },
            icon: const Icon(
              Icons.add,
              color: whiteColor,
              size: 30.0,
            ),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            productListWidget()
          ],
        ),
      ),
    );
  }

  productListWidget() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: filterCategoryList.isNotEmpty
          ? ListView.builder(
              itemCount: filterCategoryList.length,
              itemBuilder: (context, index) {
                var item = filterCategoryList[index];
                return Column(
                  children: [
                    InkWell(
                      onDoubleTap: () async {
                        if (PermissionFunctions.checkPermission(63)) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => AddExpenseCategory(
                                        expensesCatgerory: item,
                                      ))).then((value) => {fnUtilities.getAllExpensesCategoryList(), getAllExpensesCategoryList()});
                        }
                      },
                      onLongPress: () async {
                        if (PermissionFunctions.checkPermission(65)) {
                          keywordDeleteDialog(context, item.expCategoryID);
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(width: width / 1.5, child: Text(item.expCategoryName ?? "")),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      thickness: 1,
                    ),
                  ],
                );
              })
          : const Center(child: Text("Categories not available")),
    );
  }

  ///Delete category alert dialog
  keywordDeleteDialog(BuildContext context, String? id) {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
                child: ConfirmationAlertDialog(
              content: "Are you sure want to delete?",
              buttonNameOne: "Cancel",
              ontabButtonOne: () {
                if (!isLock) {
                  isLock = true;
                  Navigator.pop(context);
                  isLock = false;
                }
              },
              buttonNameTwo: "Delete",
              ontabButtonTwo: () async {
                // deleteConfirmationExpenseByName(context, id);
                if (!isLock) {
                  isLock = true;
                  ExpensesCategoryModel model = ExpensesCategoryModel();
                  model.expCategoryID = id;
                  model.status = 2;
                  model.workspaceID = MyApp.activeWorkspace.workspaceId;
                  model.expCategorySync = 0;
                  await expensesDBScript.updateProductsKeyDelete(model);
                  await fnUtilities.getAllExpensesCategoryList();
                  getAllExpensesCategoryList();
                  Navigator.pop(context);
                  isLock = false;
                }
              },
            )),
          );
        });
  }

  void deleteConfirmationExpenseByName(BuildContext context, id) async {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: "After delete the category if product is there in that categorey name change to None",
                buttonNameOne: "No",
                ontabButtonOne: () {
                  if (!isLock) {
                    isLock = true;
                    Navigator.pop(context);
                    Navigator.pop(context);
                    isLock = false;
                  }
                },
                buttonNameTwo: "Conform",
                ontabButtonTwo: () {
                  deleteExpenseByName(id);
                },
              ),
            ),
          );
        });
  }
}
