import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import '../model/expense_category_model.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:uuid/uuid.dart';

import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/common_function/validation_utility.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_strings.dart';

class AddExpenseCategory extends StatefulWidget {
  AddExpenseCategory({
    Key? key,
    this.expensesCatgerory,
  }) : super(key: key);

  ExpensesCategoryModel? expensesCatgerory;

  @override
  State<AddExpenseCategory> createState() => _AddExpenseCategoryState();
}

class _AddExpenseCategoryState extends State<AddExpenseCategory> {
  DBFuctionsScript dbScript = DBFuctionsScript();
  CommonDB commonDB = CommonDB();
  Validator validator = Validator();
  TextEditingController categoryNameController = TextEditingController();
  bool isLock = false;

  @override
  void initState() {
    super.initState();
    assignValue();
  }

  assignValue() {
    if (widget.expensesCatgerory != null) {
      setState(() {
        categoryNameController.text = widget.expensesCatgerory?.expCategoryName.toString() ?? '';
      });
    }
  }

  expenseKeyWordOperation(ExpensesCategoryModel model, String operation) async {
    var productKey;
    if (operation == "insert") {
      productKey = {
        db_expensesCategory_expCategoryID: model.expCategoryID,
        db_expensesCategory_expCategoryName: model.expCategoryName,
        db_createdDate: model.createdDate,
        db_expensesCategory_workspaceID: model.workspaceID,
        db_expensesCategory_expCategorySync: model.expCategorySync,
        db_status: model.status,
      };
      await dbScript.dbInsert(db_expensesCategory, productKey);
      print("Data inserted");
    } else {
      productKey = {
        db_expensesCategory_expCategoryName: model.expCategoryName,
        db_createdDate: model.createdDate,
        db_expensesCategory_workspaceID: model.workspaceID,
        db_expensesCategory_expCategorySync: model.expCategorySync,
        db_status: model.status,
      };
      await dbScript.updateModel(db_expensesCategory, productKey, db_expensesCategory_expCategoryID, model.expCategoryID);
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(widget.expensesCatgerory == null ? "Add Expense Category" : "Update Category",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Container(
        padding: EdgeInsets.all(20),
        width: width,
        child: Column(
          children: [
            Form(
              key: validator.formkey,
              child: Column(
                children: [
                  EditText(
                    mController: categoryNameController,
                    inputType: TextInputType.text,
                    isPassword: false,
                    text: "Category",
                    maxLength: 20,
                    validator: (value) {
                      if (value.isEmpty)
                        return 'This field is required';
                      else {
                        try {
                          ListUtility.expensesCategoryList.firstWhere(
                            (element) => element.expCategoryName == value,
                          );
                          return 'This name is already used';
                        } catch (e) {
                          return null;
                        }
                      }
                    },
                  ),
                  SizedBox(
                    height: 20,
                  ),
                  RoundedButton(
                      title: widget.expensesCatgerory == null ? "Add Expense Category" : "Update Category",
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;
                          if (validator.validate()) {
                            ExpensesCategoryModel model = ExpensesCategoryModel();
                            model.expCategoryName = categoryNameController.text;
                            model.workspaceID = MyApp.activeWorkspace.workspaceId;

                            if (widget.expensesCatgerory == null) {
                              var uuid = new Uuid();
                              String expCategoryID = uuid.v4().toString();
                              model.expCategoryID = expCategoryID;
                              model.createdDate = DateTime.now().toString();
                              model.expCategorySync = 0;
                              model.status = 0;
                              expenseKeyWordOperation(model, "insert");
                            } else {
                              model.expCategoryID = widget.expensesCatgerory?.expCategoryID;
                              model.createdDate = DateTime.now().toString();
                              model.expCategorySync = 0;
                              model.status = await commonDB.checkNonSyncCommonFunction(
                                          db_expensesCategory,
                                          db_expensesCategory_expCategoryID,
                                          widget.expensesCatgerory?.expCategoryID != null ? widget.expensesCatgerory?.expCategoryID : "",
                                          db_expensesCategory_expCategorySync) ==
                                      0
                                  ? 1
                                  : 0;
                              expenseKeyWordOperation(model, "update");
                              setState(() {
                                widget.expensesCatgerory = null;
                              });
                            }
                            await fnUtilities.getAllExpensesCategoryList();
                            Navigator.pop(context);
                            categoryNameController.clear();
                          }
                          isLock = false;
                        }
                      }),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
