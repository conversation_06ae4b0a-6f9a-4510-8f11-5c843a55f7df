import 'package:smartbill/screens/expenses/functions/stocks_functions.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../../main.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/dbutils/database_helper.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/stocks_db_script.dart';
import '../model/purchase_transactions_model.dart';

class PurchaseDBScript {
  FnUtilities fnUtilities = FnUtilities();
  DBFuctionsScript dbScript = DBFuctionsScript();
  StocksDBScript stocksDBScript = StocksDBScript();
  StocksFunction stocksFunction = StocksFunction();

  Future<String> queryGetMaxInvoiceId(prefixName, bool resetSalesNo) async {
    Database db = await DatabaseHelper.instance.database;
    var count;
    if (resetSalesNo) {
      count = Sqflite.firstIntValue(await db.rawQuery(
          "SELECT COUNT($db_purchaseID) FROM $db_purchase WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND date($db_createdDate) = date('${DateTime.now().toString()}') AND $db_invoiceId LIKE '$prefixName%'"));
    } else {
      count = Sqflite.firstIntValue(await db.rawQuery(
          "SELECT COUNT($db_purchaseID) FROM $db_purchase WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_invoiceId LIKE '$prefixName%'"));
    }
    if (count == 0) {
      return "0";
    } else {
      var result = await db.rawQuery(
          "SELECT $db_invoiceId FROM $db_purchase WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_invoiceId LIKE '$prefixName%' ORDER BY $id");
      return result.last["invoiceID"].toString();
    }
  }

  ///Update Purchase with delete function from report
  Future<int> updatePurchaseDelete(String? id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_purchase 
    SET $db_status = ${2},
    $db_purchaseSync = ${0}
    WHERE $db_purchaseID = ?
    ''', [id]);
    return result;
  }

  ///Update Purchase Transaction with delete function from report
  Future<int> updatePurchaseTransactionDelete(String? purchaseId) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_purchaseTransaction 
    SET $db_status = ${2},
    $db_purchaseTransSync = ${0}
    WHERE $db_purchaseID = ?
    ''', [purchaseId]);
    if (result != 0) {
      var transactionRows = await queryAllTransactionBasedOnPurchaseId(purchaseId);
      List<PurchaseTransactionsModel> transactList = [];

      for (Map<String, dynamic> row in transactionRows) {
        transactList.add(PurchaseTransactionsModel.fromJson(row));
        String purchaseProductId = row[db_purchaseProductID];
        int? oldQty = int.tryParse(fnUtilities.checkQtyDoubleValue(row[db_quantity] ?? '0')) ?? 0;
        int? stockQty = int.tryParse(fnUtilities
                .checkQtyDoubleValue(await stocksDBScript.getColumnFromStocksByProductID(purchaseProductId, db_stocks_stocksQty) ?? '0')) ??
            0;
        int? updatedQty = stockQty - oldQty;
        await stocksFunction.updateStockFromPurchaseEntry(purchaseProductId, updatedQty.toString());
      }
    }
    return result;
  }

  Future<int> updatePurchaseTransProdDelete(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_purchaseTransaction 
    SET $db_status = ${2},
    $db_purchaseTransSync = ${0}
    WHERE $db_purchaseProductID = ?
    ''', [id]);
    return result;
  }

  ///Insert or update purchase items
  upsetPurchase(PurchaseModel purchaseModel, String operations) async {
    var purchasesMap;
    if (operations == "insert") {
      purchasesMap = {
        db_purchaseID: purchaseModel.purchaseID,
        db_totalAmount: purchaseModel.totalAmount,
        db_invoiceId: purchaseModel.invoiceID,
        db_invoiceNo: purchaseModel.invoiceNo,
        db_purchaseDate: purchaseModel.purchaseDate,
        db_createdDate: purchaseModel.createdDate,
        db_workspaceID: purchaseModel.workspaceID,
        db_status: purchaseModel.status,
        db_purchaseSync: 1,
      };
      await dbScript.dbInsert(db_purchase, purchasesMap);
    } else {
      purchasesMap = {
        db_totalAmount: purchaseModel.totalAmount,
        db_invoiceId: purchaseModel.invoiceID,
        db_invoiceNo: purchaseModel.invoiceNo,
        db_purchaseDate: purchaseModel.purchaseDate,
        db_createdDate: purchaseModel.createdDate,
        db_workspaceID: purchaseModel.workspaceID,
        db_status: purchaseModel.status,
        db_purchaseSync: 1,
      };
      await dbScript.updateModel(db_purchase, purchasesMap, db_purchaseID, purchaseModel.purchaseID);
    }
  }

// Insert or updates Purchase transactions
  upsetPurchaseTransaction(PurchaseTransactionsModel purchaseTransactionsModel, String operations) async {
    var purchaseTransactionMap;
    if (operations == "insert") {
      purchaseTransactionMap = {
        db_purchaseTransactionID: purchaseTransactionsModel.purchaseTransactionID,
        db_purchaseID: purchaseTransactionsModel.purchaseID,
        db_purchaseProductID: purchaseTransactionsModel.purchaseProductID,
        db_quantity: purchaseTransactionsModel.quantity,
        db_purchaseProductPrice: purchaseTransactionsModel.purchaseProductPrice,
        db_amount: purchaseTransactionsModel.amount,
        db_description: purchaseTransactionsModel.description,
        db_createdDate: purchaseTransactionsModel.createdDate,
        db_workspaceID: purchaseTransactionsModel.workspaceID,
        db_purchaseTransSync: 1,
        db_status: purchaseTransactionsModel.status,
      };
      await dbScript.dbInsert(db_purchaseTransaction, purchaseTransactionMap);
    } else {
      purchaseTransactionMap = {
        db_purchaseID: purchaseTransactionsModel.purchaseID,
        db_purchaseProductID: purchaseTransactionsModel.purchaseProductID,
        db_quantity: purchaseTransactionsModel.quantity,
        db_purchaseProductPrice: purchaseTransactionsModel.purchaseProductPrice,
        db_amount: purchaseTransactionsModel.amount,
        db_description: purchaseTransactionsModel.description,
        db_createdDate: purchaseTransactionsModel.createdDate,
        db_workspaceID: purchaseTransactionsModel.workspaceID,
        db_purchaseTransSync: 1,
        db_status: purchaseTransactionsModel.status,
      };
      await dbScript.updateModel(
          db_purchaseTransaction, purchaseTransactionMap, db_purchaseTransactionID, purchaseTransactionsModel.purchaseTransactionID);
    }
  }

  Future<List<Map<String, dynamic>>> queryAllTransactionBasedOnPurchaseId(purchaseID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.query(db_purchaseTransaction, where: '$db_purchaseID = ?', whereArgs: [purchaseID]);
    return result;
  }
}
