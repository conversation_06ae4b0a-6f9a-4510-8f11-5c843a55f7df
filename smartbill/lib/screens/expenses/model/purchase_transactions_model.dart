import 'package:smartbill/Repositories/image_repository.dart';

class PurchaseTransactionsModel {
  int? id;
  String? purchaseTransactionID;
  String? purchaseID;
  String? purchaseProductID;
  String? productName;
  String? quantity;
  String? purchaseProductPrice;
  String? amount;
  String? description;
  String? createdDate;
  String? workspaceID;
  int? status;
  int? sync;
  bool? isActive;

  PurchaseTransactionsModel(
      {this.id,
      this.purchaseTransactionID,
      this.purchaseID,
      this.purchaseProductID,
      this.productName,
      this.quantity,
      this.purchaseProductPrice,
      this.amount,
      this.description,
      this.createdDate,
      this.workspaceID,
      this.status,
      this.sync,
      this.isActive});

  PurchaseTransactionsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    purchaseTransactionID = json['purchaseTransactionID'];
    purchaseID = json['purchaseID'];
    purchaseProductID = json['purchaseProductID'];
    quantity = json['quantity'].toString();
    purchaseProductPrice = json['purchaseProductPrice'].toString();
    amount = json['amount'].toString();
    description = json['description'];
    createdDate = json['createdDate'];
    workspaceID = json['workspaceID'];
    isActive = json['isActive'];
    status = json['status'];
    sync = json['sync'];
    productName = json['productName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['purchaseID'] = this.purchaseID;
    data['purchaseTransactionID'] = this.purchaseTransactionID;
    data['purchaseProductID'] = this.purchaseProductID;
    data['quantity'] = int.parse(fnUtilities.checkQtyDoubleValue(this.quantity ?? '0'));
    data['purchaseProductPrice'] = double.parse(this.purchaseProductPrice ?? '0');
    data['amount'] = double.parse(this.amount ?? '0');
    data['description'] = this.description;
    data['createdDate'] = this.createdDate;
    data['workspaceID'] = this.workspaceID;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['productName'] = this.productName;
    data['isActive'] = this.isActive;
    return data;
  }
}
