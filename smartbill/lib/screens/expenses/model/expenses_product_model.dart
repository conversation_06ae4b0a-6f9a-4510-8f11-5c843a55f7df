class ExpensesProduct {
  int? id;
  String? expProductID;
  String? expProductName;
  String? expCategoryID;
  String? expCategoryName;
  String? expProductPrice;
  String? createdDate;
  String? workspaceID;
  int? sync;
  int? status;
  bool? isActive;

  ExpensesProduct(
      {this.id,
      this.expProductID,
      this.expProductName,
      this.expCategoryID,
      this.createdDate,
      this.workspaceID,
      this.expProductPrice,
      this.expCategoryName,
      this.sync,
      this.status,
      this.isActive});

  ExpensesProduct.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    expProductID = json['expProductID'];
    expProductName = json['expProductName'];
    expCategoryID = json['expCategoryID'];
    createdDate = json['createdDate'];
    workspaceID = json['workspaceID'];
    expProductPrice = json['expProductPrice'] == null ? null : json['expProductPrice'].toString();
    expCategoryName = json['expCategoryName'];
    sync = json['sync'];
    status = json['status'];
    isActive = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['expProductID'] = this.expProductID;
    data['expProductName'] = this.expProductName;
    data['expCategoryID'] = this.expCategoryID;
    data['createdDate'] = this.createdDate;
    data['workspaceID'] = this.workspaceID;
    data['expProductPrice'] = this.expProductPrice;
    data['expCategoryName'] = this.expCategoryName;
    data['sync'] = this.sync;
    data['status'] = this.status;
    data['isActive'] = this.isActive;
    return data;
  }
}
