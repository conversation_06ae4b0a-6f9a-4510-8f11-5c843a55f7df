class GetExpenseTransactionById {
  int? status;
  String? message;
  Result? result;

  GetExpenseTransactionById({this.status, this.message, this.result});

  GetExpenseTransactionById.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? new Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.toJson();
    }
    return data;
  }
}

class Result {
  int? totalExpenseTransaction;
  int? totalQuantity;
  double? totalAmount;
  String? expDate;
  List<ExpenseTransactions>? expenseTransactions;

  Result({this.totalExpenseTransaction, this.totalQuantity, this.totalAmount, this.expenseTransactions, this.expDate});

  Result.fromJson(Map<String, dynamic> json) {
    totalExpenseTransaction = json['totalExpenseTransaction'];
    totalQuantity = json['totalQuantity'];
    totalAmount = json['totalAmount'];
    expDate = json['expDate'];
    if (json['expenseTransactions'] != null) {
      expenseTransactions = <ExpenseTransactions>[];
      json['expenseTransactions'].forEach((v) {
        expenseTransactions?.add(new ExpenseTransactions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalExpenseTransaction'] = this.totalExpenseTransaction;
    data['totalQuantity'] = this.totalQuantity;
    data['totalAmount'] = this.totalAmount;
    data['expDate'] = this.expDate;
    if (this.expenseTransactions != null) {
      data['expenseTransactions'] = this.expenseTransactions?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ExpenseTransactions {
  int? id;
  String? expTransactionID;
  String? expID;
  String? expProductID;
  double? expProductPrice;
  int? quantity;
  double? amount;
  String? description;
  String? createdDate;
  String? workspaceID;
  String? productName;

  ExpenseTransactions({
    this.id,
    this.expTransactionID,
    this.expID,
    this.expProductID,
    this.expProductPrice,
    this.quantity,
    this.amount,
    this.description,
    this.createdDate,
    this.workspaceID,
    this.productName,
  });

  ExpenseTransactions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    expTransactionID = json['expTransactionID'];
    expID = json['expID'];
    expProductID = json['expProductID'];
    expProductPrice = json['expProductPrice'];
    quantity = json['quantity'];
    amount = json['amount'];
    description = json['description'];
    createdDate = json['createdDate'];
    workspaceID = json['workspaceID'];
    productName = json['productName'] ?? "Test";
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['expTransactionID'] = this.expTransactionID;
    data['expID'] = this.expID;
    data['expProductID'] = this.expProductID;
    data['expProductPrice'] = this.expProductPrice;
    data['quantity'] = this.quantity;
    data['amount'] = this.amount;
    data['description'] = this.description;
    data['createdDate'] = this.createdDate;
    data['workspaceID'] = this.workspaceID;
    data['productName'] = this.productName;
    return data;
  }
}
