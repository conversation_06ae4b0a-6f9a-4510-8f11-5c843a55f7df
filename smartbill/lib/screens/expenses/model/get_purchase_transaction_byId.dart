class GetPurchaseTransactionById {
  int? status;
  String? message;
  Result? result;

  GetPurchaseTransactionById({this.status, this.message, this.result});

  GetPurchaseTransactionById.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? new Result.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.toJson();
    }
    return data;
  }
}

class Result {
  int? totalPurchaseTransaction;
  int? totalQuantity;
  double? totalAmount;
  String? purchaseDate;
  List<PurchaseTransactions>? purchaseTransactions;

  Result({this.totalPurchaseTransaction, this.totalQuantity, this.totalAmount, this.purchaseTransactions, this.purchaseDate});

  Result.fromJson(Map<String, dynamic> json) {
    totalPurchaseTransaction = json['totalPurchaseTransaction'];
    purchaseDate = json['purchaseDate'];
    totalQuantity = json['totalQuantity'];
    totalAmount = json['totalAmount'];
    if (json['purchaseTransactions'] != null) {
      purchaseTransactions = <PurchaseTransactions>[];
      json['purchaseTransactions'].forEach((v) {
        purchaseTransactions?.add(new PurchaseTransactions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalPurchaseTransaction'] = this.totalPurchaseTransaction;
    data['totalQuantity'] = this.totalQuantity;
    data['totalAmount'] = this.totalAmount;
    data['purchaseDate'] = this.purchaseDate;
    if (this.purchaseTransactions != null) {
      data['purchaseTransactions'] = this.purchaseTransactions?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PurchaseTransactions {
  int? id;
  String? purchaseTransactionID;
  String? purchaseID;
  String? purchaseProductID;
  double? purchaseProductPrice;
  int? quantity;
  double? amount;
  String? description;
  String? createdDate;
  String? workspaceID;
  String? productName;

  PurchaseTransactions(
      {this.id,
      this.purchaseTransactionID,
      this.purchaseID,
      this.purchaseProductID,
      this.purchaseProductPrice,
      this.quantity,
      this.amount,
      this.description,
      this.createdDate,
      this.productName,
      this.workspaceID});

  PurchaseTransactions.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    purchaseTransactionID = json['purchaseTransactionID'];
    purchaseID = json['purchaseID'];
    purchaseProductID = json['purchaseProductID'];
    purchaseProductPrice = json['purchaseProductPrice'];
    quantity = json['quantity'];
    amount = json['amount'];
    description = json['description'];
    createdDate = json['createdDate'];
    workspaceID = json['workspaceID'];
    productName = json['productName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['purchaseTransactionID'] = this.purchaseTransactionID;
    data['purchaseID'] = this.purchaseID;
    data['purchaseProductID'] = this.purchaseProductID;
    data['purchaseProductPrice'] = this.purchaseProductPrice;
    data['quantity'] = this.quantity;
    data['amount'] = this.amount;
    data['description'] = this.description;
    data['createdDate'] = this.createdDate;
    data['workspaceID'] = this.workspaceID;
    data['productName'] = this.productName;
    return data;
  }
}
