class ExpensesModel {
  int? id;
  String? expID;
  String? totalAmount;
  String? invoiceID;
  String? invoiceNo;
  String? createdDate;
  String? expDate;
  String? workspaceID;
  String? invoiceExtension;
  int? status;
  int? sync;
  bool? isActive;

  ExpensesModel(
      {this.id,
      this.expID,
      this.totalAmount,
      this.invoiceID,
      this.invoiceNo,
      this.createdDate,
      this.expDate,
      this.workspaceID,
      this.status,
      this.sync,
      this.isActive});

  ExpensesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    expID = json['expID'];
    totalAmount = json['totalAmount'].toString() ?? null;
    invoiceID = json['invoiceID'];
    invoiceNo = json['invoiceNo'].toString();
    invoiceExtension = json['invoiceExtension'];
    createdDate = json['createdDate'];
    expDate = json['expDate'];
    workspaceID = json['workspaceID'];
    status = json['status'];
    sync = json['sync'];
    isActive = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['expID'] = this.expID;
    data['totalAmount'] = this.totalAmount;
    data['invoiceID'] = this.invoiceID;
    data['invoiceNo'] = this.invoiceNo;
    data['createdDate'] = this.createdDate;
    data['expDate'] = this.expDate;
    data['workspaceID'] = this.workspaceID;
    data['invoiceExtension'] = this.invoiceExtension;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['isActive'] = this.isActive;
    return data;
  }
}
