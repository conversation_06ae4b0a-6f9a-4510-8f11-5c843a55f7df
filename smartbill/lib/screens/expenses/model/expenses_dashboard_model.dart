class ExpensesDashboardDetails {
  String? expensesItem;
  String? expensesQuantity;
  String? expensesAmount;
  String? purchaseItem;
  String? purchaseQuantity;
  String? purchaseAmount;
  String? salesItem;
  String? salesQuantity;
  String? salesAmount;
  String? profit;

  ExpensesDashboardDetails(
      {this.expensesItem,
      this.expensesQuantity,
      this.expensesAmount,
      this.purchaseItem,
      this.purchaseQuantity,
      this.purchaseAmount,
      this.salesItem,
      this.salesQuantity,
      this.salesAmount,
      this.profit});

  ExpensesDashboardDetails.fromJson(Map<String, dynamic> json) {
    expensesItem = json['expensesItem'];
    expensesQuantity = json['expensesQuantity'];
    expensesAmount = json['expensesAmount'];
    purchaseItem = json['purchaseItem'];
    purchaseQuantity = json['purchaseQuantity'];
    purchaseAmount = json['purchaseAmount'];
    salesItem = json['salesItem'];
    salesQuantity = json['salesQuantity'];
    salesAmount = json['salesAmount'];
    profit = json['profit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['expensesItem'] = this.expensesItem;
    data['expensesQuantity'] = this.expensesQuantity;
    data['expensesAmount'] = this.expensesAmount;
    data['purchaseItem'] = this.purchaseItem;
    data['purchaseQuantity'] = this.purchaseQuantity;
    data['purchaseAmount'] = this.purchaseAmount;
    data['salesItem'] = this.salesItem;
    data['salesQuantity'] = this.salesQuantity;
    data['salesAmount'] = this.salesAmount;
    data['profit'] = this.profit;
    return data;
  }
}
