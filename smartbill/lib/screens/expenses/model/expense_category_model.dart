class ExpensesCategoryModel {
  int? expCatID;
  String? expCategoryID;
  String? expCategoryName;
  String? createdDate;
  String? workspaceID;
  int? expCategorySync;
  int? status;
  bool? isActive;

  ExpensesCategoryModel(
      {this.expCatID,
      this.expCategoryID,
      this.expCategoryName,
      this.createdDate,
      this.workspaceID,
      this.expCategorySync,
      this.status,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      'expCatID': expCatID,
      'expCategoryID': expCategoryID,
      'expCategoryName': expCategoryName,
      'createdDate': createdDate,
      'workspaceID': workspaceID,
      'expCategorySync': expCategorySync,
      'status': status,
      'isActive': isActive,
    };
  }

  ExpensesCategoryModel.fromMap(Map<String, dynamic> map) {
    expCatID = map['expCatID'];
    expCategoryID = map['expCategoryID'];
    expCategoryName = map['expCategoryName'];
    createdDate = map['createdDate'];
    workspaceID = map['workspaceID'];
    expCategorySync = map['expCategorySync'];
    status = map['status'];
    isActive = map['isActive'];
  }

  factory ExpensesCategoryModel.fromJson(Map<String, dynamic> json) => ExpensesCategoryModel(
      expCatID: json["expCatID"] == null ? null : json["expCatID"],
      expCategoryID: json["expCategoryID"] == null ? null : json["expCategoryID"],
      expCategoryName: json["expCategoryName"] == null ? null : json["expCategoryName"],
      createdDate: json["createdDate"] == null ? null : json["createdDate"],
      workspaceID: json["workspaceID"] == null ? null : json["workspaceID"],
      expCategorySync: json["expCategorySync"] == null ? null : json["expCategorySync"],
      status: json["status"] == null ? null : json["status"],
      isActive: json["isActive"] == null ? null : json["isActive"]);

  Map<String, dynamic> toJson() => {
        "expCatID": expCatID == null ? null : expCatID,
        "expCategoryID": expCategoryID == null ? null : expCategoryID,
        "expCategoryName": expCategoryName == null ? null : expCategoryName,
        "createdDate": createdDate == null ? null : createdDate,
        "workspaceID": workspaceID == null ? null : workspaceID,
        "expCategorySync": expCategorySync == null ? null : expCategorySync,
        "status": status == null ? null : status,
      };
}
