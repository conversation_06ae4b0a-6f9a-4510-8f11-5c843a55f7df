class ExpenseTransactionModel {
  int? id;
  String? expTransactionID;
  String? expID;
  String? expProductID;
  String? expProductName;
  String? quantity;
  String? expProductPrice;
  String? description;
  String? createdDate;
  String? amount;
  String? workspaceID;
  bool? isActive;
  int? status;
  int? sync;

  ExpenseTransactionModel(
      {this.id,
      this.expTransactionID,
      this.expID,
      this.expProductID,
      this.expProductName,
      this.quantity,
      this.expProductPrice,
      this.description,
      this.createdDate,
      this.amount,
      this.workspaceID,
      this.isActive,
      this.status,
      this.sync});

  ExpenseTransactionModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    expTransactionID = json['expTransactionID'];
    expID = json['expID'];
    expProductID = json['expProductID'];
    expProductName = json['expProductName'];
    quantity = json['quantity'].toString();
    expProductPrice = json['expProductPrice'].toString();
    description = json['description'];
    createdDate = json['createdDate'];
    amount = json['amount'].toString();
    workspaceID = json['workspaceID'];
    isActive = json['isActive'];
    status = json['status'];
    sync = json['sync'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['expTransactionID'] = this.expTransactionID;
    data['expID'] = this.expID;
    data['expProductID'] = this.expProductID;
    data['expProductName'] = this.expProductName;
    data['quantity'] = this.quantity;
    data['expProductPrice'] = this.expProductPrice;
    data['description'] = this.description;
    data['createdDate'] = this.createdDate;
    data['amount'] = this.amount;
    data['workspaceID'] = this.workspaceID;
    data['isActive'] = this.isActive;
    data['status'] = this.status;
    data['sync'] = this.sync;
    return data;
  }
}
