class PurchaseModel {
  int? id;
  String? purchaseID;
  String? totalAmount;
  String? invoiceID;
  String? invoiceNo;
  String? purchaseDate;
  String? createdDate;
  String? workspaceID;
  int? status;
  int? sync;
  bool? isActive;
  PurchaseModel(
      {this.id,
      this.purchaseID,
      this.totalAmount,
      this.invoiceID,
      this.invoiceNo,
      this.purchaseDate,
      this.createdDate,
      this.workspaceID,
      this.status,
      this.sync,
      this.isActive});
  PurchaseModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    purchaseID = json['purchaseID'];
    totalAmount = json['totalAmount'].toString();
    invoiceID = json['invoiceID'].toString();
    invoiceNo = json['invoiceNo'].toString();
    purchaseDate = json['purchaseDate'];
    createdDate = json['createdDate'];
    workspaceID = json['workspaceID'];
    status = json['status'];
    sync = json['sync'];
    isActive = json['isActive'];
  }
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['purchaseID'] = this.purchaseID;
    data['totalAmount'] = this.totalAmount;
    data['invoiceID'] = this.invoiceID;
    data['invoiceNo'] = this.invoiceNo;
    data['purchaseDate'] = this.purchaseDate;
    data['createdDate'] = this.createdDate;
    data['workspaceID'] = this.workspaceID;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['isActive'] = this.isActive;
    return data;
  }
}
