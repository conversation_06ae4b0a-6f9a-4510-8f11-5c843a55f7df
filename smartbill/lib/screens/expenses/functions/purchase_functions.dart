import 'package:flutter/material.dart';
import 'package:smartbill/screens/expenses/db_script/purchase_db_script.dart';
import 'package:smartbill/screens/expenses/functions/stocks_functions.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_transactions_model.dart';
import 'package:smartbill/utils/dbutils/stocks_db_script.dart';
import 'package:uuid/uuid.dart';
import '../../../Repositories/image_repository.dart';
import '../../../model/products.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/common_widgets/common_alert_dialogue/amount_dialog.dart';
import '../../../utils/common_widgets/common_alert_dialogue/dynamic_quantity_dialog.dart';
import '../../../utils/common_widgets/common_alert_dialogue/unit_measurement_dialog.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_operations_utility.dart';
import '../../../utils/dbutils/db_strings.dart';

class PurchaseFunction {
  List<PurchaseTransactionsModel> purchaseTransactionList = [];
  PurchaseDBScript purchaseDBScript = PurchaseDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  StocksFunction stocksFunction = StocksFunction();
  StocksDBScript stocksDBScript = StocksDBScript();
  CommonDB commonDBScript = CommonDB();
  String? invoiceAdd;
  double dynamicQuantity = 1;
  bool isLock = false;

  final qtyController = new TextEditingController();
  final priceController = new TextEditingController();

  List<PriceUnitType> productUnitList = [];

  //Get product unit details from static unit list
  getAllProductUnitsDetails(String? productId) async {
    if (ListUtility.unitList.isNotEmpty) {
      productUnitList.clear();
      for (PriceUnitType unitType in ListUtility.unitList) {
        if (unitType.productId == productId) {
          productUnitList.add(unitType);
        }
      }
    }
  }

  Future<List<PurchaseTransactionsModel>> addProductsInSalesList(Products? item, PriceUnitType? unit, String? itemPrice) async {
    String? price = item?.priceType == null || item?.priceType == 0 || item?.priceType == 2 ? itemPrice : item?.price;
    double? amount = double.parse(price ?? '0') * dynamicQuantity;
    String? productName = unit != null ? '${item?.name}-${unit.measurement}' : item?.name;

    int gridIndex = purchaseTransactionList.indexWhere((transaction) =>
        (item?.priceType != 2 ? item?.productId == transaction.purchaseProductID : unit?.productId == transaction.purchaseProductID) &&
        double.parse(price ?? '0') == double.parse(transaction.purchaseProductPrice ?? '0'));

    if (gridIndex != -1) {
      amount = (double.parse(purchaseTransactionList[gridIndex].quantity ?? '0') + dynamicQuantity) * double.parse(price ?? '0');
      purchaseTransactionList[gridIndex].purchaseProductPrice = price ?? '0';
      purchaseTransactionList[gridIndex].quantity = (double.parse(purchaseTransactionList[gridIndex].quantity ?? '0') + dynamicQuantity).toString();
      purchaseTransactionList[gridIndex].amount = fnUtilities.checkQtyDoubleValue(amount.toString());
    } else {
      purchaseTransactionList.add(PurchaseTransactionsModel(
        purchaseProductID: item?.productId,
        productName: productName,
        quantity: dynamicQuantity.toString(),
        purchaseProductPrice: price,
        amount: fnUtilities.checkQtyDoubleValue(amount.toString()),
        workspaceID: item?.workspaceId,
      ));
    }

    dynamicQuantity = 1;
    return purchaseTransactionList;
  }

  //GET A INVOICE ID FUNCTION
  getMaxInvoiceId() async {
    DBOperations dbOperations = new DBOperations();

    String prefix = await dbOperations.queryPrefixName();
    bool isResetSalesNo = await await fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1" ? true : false;
    String prefixMax = await purchaseDBScript.queryGetMaxInvoiceId(prefix, isResetSalesNo);
    if (prefixMax == "0") {
      return prefix + 1.toString();
    } else {
      return prefix + (int.parse(prefixMax.substring(prefix.length)) + 1).toString();
    }
  }

  ///Insert and Update Purchase in DB
  upsertPurchase(PurchaseModel model, String operations, List<PurchaseTransactionsModel> pTransactionList) async {
    final purchaseMapItem = {
      db_totalAmount: model.totalAmount,
      db_purchaseDate: model.purchaseDate,
      db_invoiceId: model.invoiceID,
      db_createdDate: model.createdDate,
      db_workspaceID: model.workspaceID,
      db_purchaseSync: model.sync,
      db_status: model.status,
    };
    if (operations == "insert") {
      purchaseMapItem[db_purchaseID] = model.purchaseID;
      await dbScript.dbInsert(db_purchase, purchaseMapItem);
      await insertPurchaseTransactions(model.purchaseID, pTransactionList);
    } else {
      await dbScript.updateModel(db_purchase, purchaseMapItem, db_purchaseID, model.purchaseID);
      await updatePurchaseTransactions(model.purchaseID, pTransactionList);
    }
  }

  /// Insert PurchaseTransaction in database
  insertPurchaseTransactions(String? purchaseId, List<PurchaseTransactionsModel> pTransactionList) async {
    var purchaseTransaMapItem;
    var uuid = new Uuid();
    for (PurchaseTransactionsModel model in pTransactionList) {
      model.purchaseTransactionID = uuid.v4().toString();
      purchaseTransaMapItem = {
        db_purchaseTransactionID: model.purchaseTransactionID,
        db_purchaseID: purchaseId ?? "",
        db_purchaseProductID: model.purchaseProductID,
        db_quantity: model.quantity,
        db_purchaseProductPrice: model.purchaseProductPrice,
        db_amount: model.amount,
        db_description: model.description,
        db_createdDate: DateTime.now().toString(),
        db_workspaceID: model.workspaceID,
        db_purchaseTransSync: 0,
        db_status: 0
      };
      int status = await dbScript.dbInsert(db_purchaseTransaction, purchaseTransaMapItem);
      if (status != 0) {
        await stocksFunction.updateStockFromPurchaseEntry(
            model.purchaseProductID ?? '0', int.tryParse(fnUtilities.checkQtyDoubleValue(model.quantity ?? '0')).toString(),
            isAddExitsStock: true);
      }
    }
  }

  updatePurchaseTransactions(String? purchaseId, List<PurchaseTransactionsModel> pTransactionList) async {
    var purchaseTransaMapItem;
    List<PurchaseTransactionsModel> addPurchaseTransaction = [];
    for (PurchaseTransactionsModel model in pTransactionList) {
      if (model.purchaseTransactionID != null) {
        purchaseTransaMapItem = {
          db_purchaseID: purchaseId ?? "",
          db_purchaseProductID: model.purchaseProductID,
          db_quantity: model.quantity,
          db_purchaseProductPrice: model.purchaseProductPrice,
          db_amount: model.amount,
          db_description: model.description,
          db_createdDate: model.createdDate,
          db_workspaceID: model.workspaceID,
          db_purchaseTransSync: 0,
          db_status: 1
        };
        int? newQty = int.tryParse(fnUtilities.checkQtyDoubleValue(model.quantity ?? '0')) ?? 0;
        int? oldQty = int.tryParse(fnUtilities.checkQtyDoubleValue(await commonDBScript.getSingleColumnBasedOnOneColumn(
                    db_purchaseTransaction, db_purchaseTransactionID, model.purchaseTransactionID ?? '0', db_quantity))) ??
            0;
        int? status = await dbScript.updateModel(db_purchaseTransaction, purchaseTransaMapItem, db_purchaseTransactionID, model.purchaseTransactionID);
        if (status != 0 && newQty != oldQty) {
          int? balanceQty = newQty - oldQty;
          int? stockQty = int.tryParse(fnUtilities.checkQtyDoubleValue(
                  await stocksDBScript.getColumnFromStocksByProductID(model.purchaseProductID ?? '', db_stocks_stocksQty) ?? '0')) ??
              0;
          int updatedQty = stockQty + balanceQty;
          await stocksFunction.updateStockFromPurchaseEntry(model.purchaseProductID, updatedQty.toString());
        }
      } else {
        addPurchaseTransaction.add(model);
        int status = await insertPurchaseTransactions(purchaseId, addPurchaseTransaction);
        if (status != 0) {
          await stocksFunction.updateStockFromPurchaseEntry(
              model.purchaseProductID, int.tryParse(fnUtilities.checkQtyDoubleValue(model.quantity ?? '0')).toString(),
              isAddExitsStock: true);
        }
      }
    }
  }

  // //DYNAMIC PRICE ALERT DIALOG
  amountDialogForPurchase(BuildContext context, Products item, {PriceUnitType? unit}) {
    final priceController = new TextEditingController();
    return showDialog(
        context: context,
        builder: (context) {
          return AmountAlertDialog(
            priceController: priceController,
            onPresOk: (value) async {
              value.validate();
              if (value.validate()) {
                if (unit != null) {
                  purchaseTransactionList = await addProductsInSalesList(item, unit, priceController.text);
                } else {
                  purchaseTransactionList = await addProductsInSalesList(item, null, priceController.text);
                }
                priceController.clear();
                Navigator.pop(context, purchaseTransactionList);

                // return purchaseTransactionList = await addProductsInSalesList(item, null, priceController.text);
              }
            },
          );
        }).then((value) {
      if (value != null) {
        return value;
      }
    });
  }

  // Unit ALERT DIALOG
  unitDialog(BuildContext context, Products item, bool isBiglayout, String dFlag) {
    return showDialog(
        context: context,
        builder: (context) {
          return UnitMeasurementDialog(
            isBigLayout: isBiglayout,
            isSaleLayoutTwo: false,
            productUnitList: productUnitList,
            onTabItem: (value) async {
              var isButtonLock = false;
              if (!isButtonLock) {
                isButtonLock = true;
                if ("dynamicQty" == dFlag) {
                  purchaseTransactionList = await dynamicQtyDialog(context, value.measurementPrice, item, value);
                  Navigator.pop(context, purchaseTransactionList);
                } else if (value.measurementPrice == null || value.measurementPrice == "") {
                  purchaseTransactionList = await amountDialogForPurchase(context, item, unit: value);
                  Navigator.pop(context, purchaseTransactionList);
                } else {
                  purchaseTransactionList = await addProductsInSalesList(
                    item,
                    value,
                    value.measurementPrice,
                  );
                  Navigator.pop(context, purchaseTransactionList);
                }
                isButtonLock = false;
              }
            },
          );
        }).then((value) {
      if (value != null) {
        return value;
      }
    });
  }

//DYNAMIC QUANTITY ALERT DIALOG
  Future dynamicQtyDialog(BuildContext context, String price, Products item, PriceUnitType? unit) {
    qtyController.text = "1";
    priceController.text = price != "" ? price : "";
    return showDialog(
        context: context,
        builder: (context) {
          return DynamicQtyDialog(
            qtyController: qtyController,
            priceController: priceController,
            onTabOK: ((value1, value2) async {
              dynamicQuantity = double.parse(value1) > 0 ? double.parse(value1) : 1;
              purchaseTransactionList = await addProductsInSalesList(item, unit, value2);
              Navigator.pop(context, purchaseTransactionList);
            }),
          );
        }).then((value) {
      if (value != null) {
        return value;
      }
    });
  }
}
