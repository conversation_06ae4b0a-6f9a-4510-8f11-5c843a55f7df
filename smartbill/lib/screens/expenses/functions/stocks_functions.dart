import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:uuid/uuid.dart';

import '../../../main.dart';
import '../../../model/products.dart';
import '../../../model/stocks.dart';
import '../../../model/transaction_model.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_operations_utility.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/sales_trans_db_script.dart';
import '../../../utils/dbutils/stocks_db_script.dart';
import '../../products/add_product_page.dart';
import '../model/purchase_transactions_model.dart';

class StocksFunction {
  List<PurchaseTransactionsModel> purchaseTransactionList = [];
  StocksDBScript stocksDBScript = StocksDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  CommonDB commonDBScript = CommonDB();
  DBOperations dbOperations = DBOperations();
  FnUtilities fnUtilities = FnUtilities();

  modifyStocks(ModifyStocksDto manageStocks) async {
    Stocks stock = Stocks();
    var uuid = Uuid();
    String stockId = uuid.v4().toString();
    stock.productId = manageStocks.productId;
    stock.stocksQty = manageStocks.stocksQty;
    stock.stocksSold = manageStocks.stocksSold;
    stock.workspaceID = MyApp.activeWorkspace.workspaceId;
    stock.syncStatus = 0;

    int? isExists = manageStocks.stocksID.toString().isEmptyOrNull
        ? 0
        : await commonDBScript.checkActiveValueExistsOneColumn(db_stocks, db_stocks_stocksId, manageStocks.stocksID ?? '');

    if (isExists == 0) {
      stock.stocksID = stockId;
      stock.rowStatus = 0;
      await stocksDBScript.insertStocks(stock);
    } else {
      stock.stocksID = manageStocks.stocksID;
      stock.rowStatus = 1;
      await stocksDBScript.updateStocks(stock);
    }

    var uuid1 = new Uuid();
    StockLogs stockLog = new StockLogs();
    stockLog.stockLogsID = uuid1.v4().toString();
    stockLog.stocksID = isExists == 0 ? stockId : manageStocks.stocksID;
    stockLog.modifiedStocks = manageStocks.modifiedStocks;
    stockLog.createdDate = DateTime.now().toString();
    stockLog.createdBy = MyApp.activeUser.username;
    stockLog.workspaceID = MyApp.activeWorkspace.workspaceId;
    stockLog.rowStatus = 0;
    stockLog.syncStatus = 0;
    await stocksDBScript.insertStockLogs(stockLog);
  }

  Future<void> updateStockForNewSales(List<Transactions> transactionList) async {
    for (Transactions t in transactionList) {
      String? currentSold = await stocksDBScript.getColumnFromStocksByProductID(t.prodId, "stocksSold");
      if (currentSold != null) {
        int soldToAdd = (int.parse(currentSold) + int.parse(double.parse(t.qty ?? '0').toStringAsFixed(0))).toInt();
        await updateStock(t.prodId, soldToAdd, 0);
      }
    }
  }

  Future<void> updateStockForUpdateSales(String qty, String prodId, int status) async {
    String? currentSold = await stocksDBScript.getColumnFromStocksByProductID(prodId, "stocksSold");
    if (currentSold != null) {
      await updateStock(prodId, int.parse(qty), status);
    }
  }

  Future<void> updateStockForDeleteSales(String saleId) async {
    List<Transactions> transactionList = await dbOperations.getAllTransactionDetailsBySalesID(saleId);
    for (Transactions t in transactionList) {
      if (t.toppingId == null) {
        String? currentSold = await stocksDBScript.getColumnFromStocksByProductID(t.prodId, "stocksSold");
        if (currentSold != null) {
          int soldToSubtract = (int.parse(currentSold) - int.parse(double.parse(t.qty ?? '0').toStringAsFixed(0))).toInt();
          int rowStatus = await commonDBScript.checkNonSyncCommonFunction("Stocks", "productId", t.prodId, "stocksSync") == 0 ? 1 : 0;
          await updateStock(t.prodId, soldToSubtract, rowStatus);
        }
      }
    }
  }

  Future<void> updateStock(String? prodId, int? stocksSold, int? rowStatus) async {
    var productFor = ListUtility.productList.firstWhere((element) => element.productId == prodId).productFor;
    if (productFor == ProductFor.both) {
      Stocks model = Stocks(
        productId: prodId,
        stocksSold: stocksSold.toString(),
        rowStatus: rowStatus,
        syncStatus: 0,
        workspaceID: MyApp.activeWorkspace.workspaceId,
      );

      await stocksDBScript.updateStocksByProductID(model);
    }
  }

  Future<void> updateStockFromPurchaseEntry(String? productId, String stockQty, {bool isAddExitsStock = false}) async {
    var stockMap = await commonDBScript.getSingleDataBasedOneColumn(db_stocks, db_stocks_stocks_fkProdId, productId);
    var modifyStocksDto = ModifyStocksDto(
      productId: productId,
      stocksQty: stockMap != null && isAddExitsStock
          ? ((int.tryParse(Stocks.fromMap(stockMap).stocksQty ?? '0') ?? 0) + (int.tryParse(fnUtilities.checkQtyDoubleValue(stockQty) ?? '0') ?? 0))
              .toString()
          : stockQty,
      stocksSold: stockMap != null ? Stocks.fromMap(stockMap).stocksSold : "0",
      stocksID: stockMap != null ? Stocks.fromMap(stockMap).stocksID : null,
    );

    await modifyStocks(modifyStocksDto);
  }

  Future<bool> checkIsStockAvailable(Products product, List<Transactions>? transactionList, int saleQty) async {
    if (product.productFor == ProductFor.both) {
      if (fnUtilities.workSpaceSetValues(key_allowNegativeSales) == "1") {
        return true;
      } else {
        var stockMap = await commonDBScript.getSingleDataBasedOneColumn(db_stocks, db_stocks_stocks_fkProdId, product.productId);
        Stocks? stockModel = stockMap != null ? Stocks.fromMap(stockMap) : null;
        if (stockModel?.stocksID != null) {
          var transactItem = transactionList?.isNotEmpty == true
              ? transactionList?.firstWhere((element) => element.prodId == product.productId, orElse: () => Transactions())
              : null;
          if (transactItem?.qty != null) {
            saleQty = (int.tryParse(fnUtilities.checkQtyDoubleValue(double.parse(transactItem?.qty ?? '0').toStringAsFixed(0))) ?? 0) + saleQty;
          }

          int? currentStock = (int.tryParse(stockModel?.stocksQty ?? '0') ?? 0) - (int.tryParse(stockModel?.stocksSold ?? '0') ?? 0);

          if (currentStock >= saleQty) {
            return true;
          }
          showToast("Stocks not available");
          return false;
        }
        showToast("Stocks not available");
        return false;
      }
    }
    return true;
  }

  Future<String?> getUpdateStockQtyFun(Transactions p, String flag) async {
    if (p.transactId == null) return null;

    String oldQty = fnUtilities.checkQtyDoubleValue(await salesTransDBScript.getAllQtyByTransactionID(p.transactId ?? ''));
    String? currentSold = await stocksDBScript.getColumnFromStocksByProductID(p.prodId, "stocksSold");
    String transactQty = fnUtilities.checkQtyDoubleValue(p.qty ?? '0');

    if (flag == "Update") {
      if (currentSold != null) {
        int pQty = int.parse(transactQty).toInt();
        int oldQtyInt = int.parse(oldQty).toInt();

        if (pQty > oldQtyInt) {
          int count = pQty - oldQtyInt;
          return (int.parse(currentSold) + count).toString();
        } else if (pQty < oldQtyInt) {
          int count = oldQtyInt - pQty;
          return (int.parse(currentSold) - count).toString();
        } else if (pQty == oldQtyInt) {
          return currentSold;
        }
      }
    } else if (flag == "Insert") {
      if (currentSold != null) {
        int pQty = int.parse(transactQty).toInt();
        return (int.parse(currentSold) + pQty).toString();
      }
    }
    return null;
  }
}
