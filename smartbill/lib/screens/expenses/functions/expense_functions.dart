import 'package:flutter/material.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:uuid/uuid.dart';

import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../utils/common_widgets/common_alert_dialogue/amount_dialog.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../model/expenses_model.dart';
import '../model/expenses_product_model.dart';

class ExpenseFunction {
  double quantity = 1;
  List<ExpenseTransactionModel>? expTransList = [];
  DBFuctionsScript dbScript = DBFuctionsScript();

  ///To add the expense transaction item in a list.
  addExpenseTransaction(ExpensesProduct expenseItem, String expProductPrice) async {
    int? indexToUpdate;
    indexToUpdate = expTransList?.indexWhere(
        (item) => item.expProductID == expenseItem.expProductID && double.tryParse(item.expProductPrice ?? '0') == double.tryParse(expProductPrice));
    if (indexToUpdate != -1) {
      expTransList = await updateExpenseTransList(expenseItem.expProductID, double.parse(expProductPrice), indexToUpdate);
    } else {
      expTransList?.add(ExpenseTransactionModel(
          expProductID: expenseItem.expProductID,
          expProductName: expenseItem.expProductName,
          expProductPrice: fnUtilities.checkQtyDoubleValue(expProductPrice.toString()),
          amount: fnUtilities.checkQtyDoubleValue((quantity * double.parse(expProductPrice)).toString()),
          workspaceID: MyApp.activeWorkspace.workspaceId,
          createdDate: DateTime.now().toString(),
          quantity: fnUtilities.checkQtyDoubleValue(quantity.toString())));
    }
    return expTransList;
  }

  ///update expense transaction list
  updateExpenseTransList(String? expItemId, double? expProductPrice, int? indexToUpdate) {
    expTransList?.asMap().forEach((index, item) {
      if (index == indexToUpdate) {
        double? qty = double.tryParse(item.quantity ?? '0')! + quantity;
        double? amount = qty * expProductPrice!;
        expTransList?[index].amount = fnUtilities.checkQtyDoubleValue(amount.toString());
        expTransList?[index].expProductPrice = fnUtilities.checkQtyDoubleValue(item.expProductPrice ?? '0');
        expTransList?[index].quantity = fnUtilities.checkQtyDoubleValue(qty.toString());
      }
    });
    return expTransList;
  }

  Future<void> upSetExpenses(ExpensesModel model, String operations, List<ExpenseTransactionModel> expenseTransactionList) async {
    final expensesMapItem = {
      db_totalAmount: model.totalAmount,
      db_invoiceId: model.invoiceID,
      db_expenseDate: model.expDate,
      db_createdDate: model.createdDate,
      db_workspaceID: model.workspaceID,
      db_expSync: model.sync,
      db_status: model.status,
    };

    if (operations == "insert") {
      expensesMapItem[db_expID] = model.expID;
      await dbScript.dbInsert(db_expenses, expensesMapItem);
      await insertExpenseTransactions(model.expID, expenseTransactionList);
    } else {
      await dbScript.updateModel(db_expenses, expensesMapItem, db_expID, model.expID);
      await updateExpenseTransactions(model.expID, expenseTransactionList);
    }
  }

  /// Insert ExpenseTransactions in database
  insertExpenseTransactions(String? expenseId, List<ExpenseTransactionModel> expTransList) async {
    if (expTransList.isNotEmpty) {
      var expenseTransMapItem;
      var uuid = new Uuid();
      for (ExpenseTransactionModel model in expTransList) {
        model.expTransactionID = uuid.v4().toString();
        expenseTransMapItem = {
          db_expTransactionId: model.expTransactionID,
          db_expId: expenseId,
          db_expProductID: model.expProductID,
          db_quantity: model.quantity,
          db_expProductPrice: model.expProductPrice,
          db_amount: model.amount,
          db_description: model.description,
          db_createdDate: model.createdDate,
          db_workspaceID: model.workspaceID,
          db_expTransSync: 0,
          db_status: 0
        };
        await dbScript.dbInsert(db_expenseTransactions, expenseTransMapItem);
      }
    }
  }

  updateExpenseTransactions(String? expenseId, List<ExpenseTransactionModel> expTransList) async {
    if (expTransList.isNotEmpty) {
      var expenseTransMapItem;
      List<ExpenseTransactionModel> addExpTransactionList = [];
      for (ExpenseTransactionModel model in expTransList) {
        if (model.expTransactionID != null) {
          expenseTransMapItem = {
            db_expId: expenseId,
            db_expProductID: model.expProductID,
            db_quantity: model.quantity,
            db_expProductPrice: model.expProductPrice,
            db_amount: model.amount,
            db_description: model.description,
            db_createdDate: model.createdDate,
            db_workspaceID: model.workspaceID,
            db_expTransSync: 0,
            db_status: 1
          };
          await dbScript.updateModel(
            db_expenseTransactions,
            expenseTransMapItem,
            db_expTransactionId,
            model.expTransactionID,
          );
        } else {
          addExpTransactionList.add(model);
          await insertExpenseTransactions(expenseId, addExpTransactionList);
        }
      }
    }
  }

  //DYNAMIC PRICE ALERT DIALOG
  amountDialog(BuildContext context, ExpensesProduct item) {
    final priceController = new TextEditingController();
    return showDialog(
        context: context,
        builder: (context) {
          return AmountAlertDialog(
            priceController: priceController,
            onPresOk: (value) async {
              value.validate();
              if (value.validate()) {
                Navigator.pop(context, expTransList);
                expTransList = await addExpenseTransaction(item, priceController.text);
                priceController.clear();
              }
            },
          );
        }).then((value) {
      if (value != null) {
        return value;
      }
    });
  }
}
