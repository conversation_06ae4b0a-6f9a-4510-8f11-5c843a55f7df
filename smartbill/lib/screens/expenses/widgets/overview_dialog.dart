// ignore_for_file: sdk_version_ui_as_code
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import '../../../main.dart';
import '../../../utils/common_function/fn_utilities.dart';
import '../../../utils/common_widgets/rounded_button.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/styles.dart';

class OverViewDialog extends StatefulWidget {
  OverViewDialog({this.totalAmount, this.totalItems, this.totalQty, this.onPress, this.title, this.totalFor, this.date});
  String? title;
  String? totalAmount;
  String? totalItems;
  String? totalQty;
  String? totalFor;
  String? date;
  FunctionCallbackParam? onPress;

  @override
  State<OverViewDialog> createState() => OverViewDialogState();
}

class OverViewDialogState extends State<OverViewDialog> {
  double width = 0.0;
  double height = 0.0;
  FnUtilities fnUtilities = new FnUtilities();
  DateTime? fromDate = DateTime.now();
  String? date;

  @override
  void initState() {
    if (widget.date != null && widget.date != "") {
      fromDate = DateTime.parse(widget.date ?? '');
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Center(
      child: SingleChildScrollView(
        child: AlertDialog(
          content: Container(
            width: width > tabletWidth ? width * 0.4 : width * 0.8,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                Container(
                  child: Text(
                    widget.title ?? '',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(
                  height: 15.0,
                ),
                buildRowWidget(widget.totalFor, widget.totalItems),
                buildRowWidget("Total Amount", widget.totalAmount),
                buildRowWidget("Total Quantity", widget.totalQty),
                Row(
                  children: [
                    Text(
                      "Select Date",
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w400,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    Spacer(),
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0),
                      child: InkWell(
                        onTap: () {
                          selectDate(context, 'fromDate');
                        },
                        child: Card(
                          elevation: 4,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 3),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  "${fnUtilities.convertDate(fromDate.toString())}",
                                  style: secondaryTextStyle(),
                                ),
                                SizedBox(
                                  width: 15,
                                ),
                                Icon(Icons.calendar_today)
                              ],
                            ),
                          ),
                        ),
                      ),
                    )
                  ],
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8.0,
                  ),
                  child: RoundedButton(
                    title: "Complete",
                    width: width > tabletWidth ? width * 0.18 : width * 0.3,
                    onPressed: () async {
                      widget.onPress!(fromDate.toString());
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  buildRowWidget(String? title, String? value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (value != null) ...[
            Text(
              title ?? '',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
              textAlign: TextAlign.center,
            ),
            Spacer(),
            Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w400,
              ),
            ),
          ]
        ],
      ),
    );
  }

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : null,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));

    if (picked != null) {
      if (dateType == 'fromDate') {
        setState(() {
          fromDate = picked;
        });
      }
    }
  }
}
