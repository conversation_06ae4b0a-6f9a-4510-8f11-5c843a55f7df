import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import '../../../main.dart';
import '../../../utils/common_function/validation_utility.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/styles.dart';

class ExpenseSaveDialog extends StatefulWidget {
  ExpenseSaveDialog({
    Key? key,
    this.productName,
    this.qtyController,
    this.priceController,
    this.descriptionController,
    this.onPressUpdate,
  }) : super(key: key);

  String? productName;
  TextEditingController? qtyController = TextEditingController();
  TextEditingController? priceController = TextEditingController();
  TextEditingController? descriptionController = TextEditingController();
  FunctionCallback4Param? onPressUpdate;

  @override
  State<ExpenseSaveDialog> createState() => _ExpenseSaveDialogState();
}

class _ExpenseSaveDialogState extends State<ExpenseSaveDialog> {
  Validator validator = new Validator();
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Center(
      child: SingleChildScrollView(
        child: AlertDialog(
          content: Container(
            width: width > tabletWidth ? width * 0.4 : width * 0.8,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                Container(
                  child: Text(
                    widget.productName ?? "",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.0),
                Form(
                  key: validator.formkey,
                  child: Column(
                    children: [
                      Container(
                        child: EditText(
                          mController: widget.qtyController,
                          inputType: TextInputType.number,
                          inputFormate: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                          ],
                          validator: validator.validateQuantity,
                          isPassword: false,
                          text: "Quantity",
                        ),
                      ),
                      SizedBox(height: 20.0),
                      Container(
                        child: EditText(
                          mController: widget.priceController,
                          inputType: TextInputType.number,
                          inputFormate: [
                            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                          ],
                          isPassword: false,
                          text: "Price",
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Container(
                        child: EditText(
                          mController: widget.descriptionController,
                          isPassword: false,
                          text: "Description",
                          maxLine: 3,
                          inputType: TextInputType.multiline,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 40,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    RoundedButton(
                      title: "Cancel",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () {
                        if (!isLock) {
                          isLock = true;
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                    ),
                    RoundedButton(
                      title: "Update",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () async {
                        widget.onPressUpdate!(
                          widget.qtyController?.text,
                          widget.priceController?.text,
                          widget.descriptionController?.text,
                          validator,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
