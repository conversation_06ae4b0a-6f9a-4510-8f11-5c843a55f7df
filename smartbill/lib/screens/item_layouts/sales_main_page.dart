// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code, sdk_version_set_literal

import 'dart:io';
import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:flutter/material.dart' hide Image;
import 'package:flutter/services.dart';
import 'package:smartbill/model/card_models/category_card_model.dart';
import 'package:smartbill/model/card_models/transaction_card_model.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/screens/item_layouts/widgets/main_page_alert_widget.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import 'package:smartbill/screens/products/add_product_page.dart';
import 'package:smartbill/screens/salescategory/model/sales_category_model.dart';
import 'package:smartbill/utils/common_function/common_sales_function.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/hold_sales_name_dialog.dart';
import 'package:smartbill/utils/common_widgets/custom_dropdown.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/sales_bottom_sheet_widgets/Rounded_button_with_icon.dart';
import 'package:smartbill/utils/common_widgets/text_field_box_widget.dart';
import 'package:smartbill/utils/common_widgets/transaction_panel_widget.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/dbutils/customers_db_script.dart';
import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
import 'package:smartbill/utils/common_function/discount_function.dart';
import 'package:smartbill/utils/common_function/sales_function.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/sales_tax_calculate.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/model/sale_model.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/payment.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../main.dart';
import '../../model/advance_order_note_model.dart';
import '../../model/card_models/card_model.dart';
import '../../model/sales_payment_mapping.dart';
import '../../utils/common_widgets/advance_order_widget.dart';
import '../../utils/common_widgets/common_alert_dialogue/amount_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/dynamic_quantity_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/hold_sales_alert_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/information_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/sales_update_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/topping_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/unit_measurement_dialog.dart';
import '../../utils/common_widgets/common_panel_widgets/category_panel_widget.dart';
import '../../utils/common_widgets/common_panel_widgets/common_billing_card.dart';
import '../../utils/common_widgets/common_panel_widgets/normal_panel_widget.dart';
import '../../utils/common_widgets/common_panel_widgets/number_panel_widget.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/bottom_sheet_widget_controllers/common_billing_card_controller.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/bottom_sheet_widget_controllers/cusromer_widget_controller.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/common_errorvalidation.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/coupon_discount_widget.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/common_tab_bar.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/customer_widget.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/discount_card.dart';
import '../../utils/common_widgets/sales_bottom_sheet_widgets/payment_alert_dialog.dart';
import '../../utils/dbutils/advance_order_db_scripts.dart';
import '../../utils/dbutils/discount_db_script.dart';
import '../../utils/dbutils/price_type_db_script.dart';
import '../../utils/dbutils/product_details_db_script.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/dbutils/sales_details_db.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_hold_sales_db_script.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/sales_trans_staging.dart';
import '../../utils/dbutils/shop_tables_db_script.dart';
import '../../utils/dbutils/stocks_db_script.dart';
import '../../utils/dbutils/toppings_db_script.dart';
import '../../utils/printer_utilities/sales_print.dart';
import '../customers/customer_details_handler.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../customers/customers_page.dart';
import '../expenses/functions/stocks_functions.dart';
import 'table_and_eatandpay.dart';

class SalesMainPage extends StatefulWidget {
  /// orderType  1- take Away, 2 - Online order, 3 - Eat & Pay, 4 - Table Order, 5 - Complementory
  int? orderType = 1;

  Sales? sales;
  List<Transactions>? saleTransactionList;
  List<SalesPaymentMapping>? paymentMapList;
  String? saleType;
  String? orderNotes;
  SalesMainPage({this.orderType, this.sales, this.saleTransactionList, this.saleType, this.paymentMapList, this.orderNotes});
  @override
  SalesMainPageState createState() => SalesMainPageState(orderType, sales, saleTransactionList, saleType, paymentMapList, orderNotes);
}

class SalesMainPageState extends State<SalesMainPage> {
  SalesMainPageState(this.orderType, this.sales, this.saleTransactionList, this.saleType, this.paymentMapList, this.orderNotes);
  int? orderType;
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  PrinterUtilities printerUtilities = PrinterUtilities();
  SalesPrint salesprint = SalesPrint();
  DBOperations dbOperations = new DBOperations();
  DiscountDB discountDB = DiscountDB();
  CommonDB commonDB = CommonDB();
  FnUtilities fnUtilities = FnUtilities();
  Validator validator = new Validator();
  SalesFunctions salesFun = SalesFunctions();
  BalanceDBScript balanceDB = BalanceDBScript();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  DiscountFunction discount = DiscountFunction();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
  ToppingsDBScript toppingsDBscript = ToppingsDBScript();
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  SalesHoldSalesDetailsDBScript salesHoldSalesDetailsDBScript = SalesHoldSalesDetailsDBScript();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();
  SalesDetailsDB salesDetailsDB = SalesDetailsDB();
  StocksFunction stocksFunction = StocksFunction();
  StocksDBScript stocksDBScript = StocksDBScript();

  final qtyController = new TextEditingController();
  final priceController = new TextEditingController();
  final notesController = new TextEditingController();
  final customerNameController = new TextEditingController();
  final mobileNoController = new TextEditingController();
  final givenAmountController = new TextEditingController();
  final holdSalesController = new TextEditingController();
  final productSearchController = TextEditingController();
  final paymentAmountController = new TextEditingController();
  final dbHelper = DatabaseHelper.instance;
  List<Products> filterProductList = [];
  List<ProductKey> productKeyList = [];
  List<Customer> customerList = [];
  List<PriceUnitType> productUnitList = [];
  List<Topping> productToppingList = [];
  bool addTax = false;
  bool? isIndividualKOT;
  bool customerNameEnable = false;
  bool isSalesBillPadEnable = false;
  bool isStoreCustomerDetails = false;
  bool multiPrint = false;
  bool isLock = false;
  double width = 0.0;
  double height = 0.0;
  double total = 0.0;
  double totalQty = 0.0;
  double totalSGST = 0.0;
  double totalWithoutGST = 0.0;
  double totalCGST = 0.0;
  double totalDiscount = 0.0;
  double returnAmount = 0.0;
  double paidAmount = 0.0;
  String? panels;
  String prodId = '';
  String prodKey = '';
  String prodName = '';
  String? invoiceAdd;
  String? insertSalesId;
  int productsRowCount = 0;
  int? taxMode;
  bool isDynamicQuantity = false;
  bool isPaymentType = false;
  PaymentType? selectPaymentType;
  Customer? selectCustomer;
  double dynamicQuantity = 1;
  bool isPaymentTypeIsEmpty = false;
  bool isPaymentAmountEmpty = false;
  Sales? sales;
  // bool showInventory = false;
  // String inventoryType;
  bool allowNegativeSale = false;
  bool isIGST = false;
  bool isReturnAmount = false;
  String? saleDiscountFormula;
  String? saleDiscountPrice;
  bool isDiscount = false;
  String? discountType;
  String? saleLayout;
  List<Transactions>? saleTransactionList;
  String? saleType;
  bool? successDialogOnSales = false;
  String? salesIdForReport;
  CustomersDBScript customerDB = CustomersDBScript();
  bool isProductLoading = false;
  bool isFavorite = false;
  bool additionalKOTButton = false;
  bool showImages = true;
  bool isBigLayout = false;
  List<SalesPaymentMapping> paymentMappingList = [];
  List<SalesPaymentMapping> temPaymentMappingList = [];
  int paymentMappingIndex = -1;
  FocusNode measure = FocusNode();
  bool isUpdate = false;
  bool isPaymentMappingListExist = false;
  List<SalesPaymentMapping>? paymentMapList;
  bool enableOnlineOrder = false;
  List<OnlinePlatformModel> onlinePlatformListList = [];
  OnlinePlatformModel? selectedOnlinePlatform;
  bool isOnlinePlatformIsEmpty = false;
  final onlineRefNoController = new TextEditingController();
  bool isOnlineRefNoIsEmpty = false;
  StateSetter? saveAlertSetState;
  Customer? customers;
  bool isEnableAdvanceOrder = false;
  var deliveryDateController = TextEditingController();
  var deliveryTimeController = TextEditingController();

  var orderNoteController = TextEditingController();
  bool isExpandAdvanceOrderWidget = false;
  bool isEnableDuplicationPayment = false;
  SizedBox sizedBoxHeight5 = SizedBox(
    height: 5,
  );

  List<Products> productsToAdd = [];
  List<CardModel> cardItemList = [];
  List<CategoryCardModel> categoryListItem = [];
  List<TransactionCardModel> transactionCardList = [];
  CustomerDetailsHandler customerDetailsHandler = CustomerDetailsHandler();
  List<PaymentType> paymentTypeList = [];

  bool customerNameMandatory = false;
  List<SalesCategoryModel> salesCategoryList = [];
  List<DropDown> dropDownList = [];
  DropDown? selectedSalesCategory;
  String? shiftId;
  bool isSalesCategoryEnabled = false;
  bool isSalesCategorEmpty = false;
  bool isEnableShiftWiseSales = false;
  int? paymentCategoryID;
  bool splitInvoiceByPayment = false;
  bool isComplementory = false;
  AdvanceOrdersDBScripts advanceOrder = AdvanceOrdersDBScripts();
  String? customerId;
  String? customerName;
  String? orderNotes;
  DateTime? deliveryDateAndTime;
  bool isEnableCustomerSection = false;
  bool isEnableDiscountSection = false;
  bool isEnableAdvanceSection = false;
  int? selectedCouponID;
  final discountController = TextEditingController();
  bool isDiscountEmpty = false;
  bool isAdvancePaymentEnabled = false;
  bool noAdvancePaymentEnabled = false;

  bool enableValidationMessage = false;
  int? discountSelectedIndex = 0;
  PaymentType? selectedRadioPaymentType;
  String validationText = '';
  final CustomerController _customerController = CustomerController();
  BillingCardController billingCardController = BillingCardController();

  FocusNode customerFocusNode = FocusNode();
  FocusNode customerMobileNumberFocusNode = FocusNode();
  bool enableCustomerDetailsFocuse = false;

  double balanceAmount = 0.0;
  AlertWidgets alertWidget = AlertWidgets();
  String? selectedDiscountOn;
  // "T" - Table
  // "SO" - SalesOrder

  getCustomersDetails() async {
    try {
      final allRows = await customerDB.selectAllCustomers();
      setState(() {
        customerList.clear();
        for (var row in allRows) {
          customerList.add(Customer.fromMap(row));
        }
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getCustomersDetails - SalesMainPage");
    }
  }

  assignProductList() async {
    try {
      if (ListUtility.productList.isNotEmpty) {
        setState(() {
          isProductLoading = true;
        });
        productsToAdd.clear();
        for (Products p in ListUtility.productList) {
          if (p.productFor == null || p.productFor?.name == "sale" || p.productFor?.name == "both") {
            productsToAdd.add(p);
          }
        }
        List<Products> productsWithFilePaths = [];
        productsWithFilePaths.clear();
        productsWithFilePaths = await fnUtilities.assignFilePath(productsToAdd);
        // ListUtility.productList.addAll(productsWithFilePaths);
        filterProductList.clear();
        filterProductList.addAll(productsWithFilePaths);
        filterProductList.add(Products());
        paymentTypeList.clear();
        paymentTypeList.addAll(ListUtility.paymentTypeList);
        assignCardList(filterProductList);

        await getAllProductKeys();
        await getCustomersDetails();

        setState(() {
          isProductLoading = false;
        });
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "assignProductList - SalesMainPage");
    }
  }

  assignCardList(List<Products> itemList) {
    try {
      if (itemList.isNotEmpty) {
        cardItemList.clear();
        for (Products model in itemList) {
          cardItemList.add(CardModel(
              id: model.productSaleId,
              guidId: model.productId,
              name: model.name,
              price: model.price ?? "",
              imagePath: model.imagePath,
              count: model.count,
              isShowImage: showImages));
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "assignCardList - SalesMainPage");
    }
  }

  //GET PRODUCT DETAILS BY Key
  getAllProductKeys() async {
    try {
      if (ListUtility.productKeyList.isNotEmpty) {
        setState(() {
          productKeyList.clear();
          productKeyList.addAll(ListUtility.productKeyList);
          productKeyList.insert(0, ProductKey(id: 0, keywordName: "All"));
          assignCategoryList(productKeyList);
        });
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getAllProductKeys - SalesMainPage");
    }
  }

  assignCategoryList(List<ProductKey> keyListItem) {
    try {
      if (keyListItem.isNotEmpty) {
        categoryListItem.clear();
        for (ProductKey model in keyListItem) {
          categoryListItem.add(CategoryCardModel(id: model.id, guidId: model.productKeyId, categoryName: model.keywordName));
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "assignCategoryList - SalesMainPage");
    }
  }

  //Get product unit details from static unit list
  getAllProductUnitsDetails(String productId) async {
    try {
      if (ListUtility.unitList.isNotEmpty) {
        productUnitList.clear();
        for (PriceUnitType unitType in ListUtility.unitList) {
          if (unitType.productId == productId) {
            setState(() {
              productUnitList.add(unitType);
            });
          }
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getAllProductUnitsDetails - SalesMainPage");
    }
  }

  //GET PRODUCT Topping DETAILS FROM DB
  queryAllProductToppingDetails(String toppingId) async {
    try {
      if (ListUtility.toppingsList.isNotEmpty) {
        productToppingList.clear();

        setState(() {
          for (Topping t in ListUtility.toppingsList) {
            if (t.toppingGroupId == toppingId) {
              t.isActive = false;
              productToppingList.add(t);
            }
          }
        });
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "queryAllProductToppingDetails - SalesMainPage");
    }
  }

  getWorkSpaceSettingValue() async {
    setState(() {
      ///workspace settings
      customerNameEnable = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
      customerNameMandatory = fnUtilities.workSpaceSetValues(key_isCustomerNameMandatory) == "1";
      addTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
      if (addTax) {
        isIGST = sales?.isIGST ?? false;
      }
      isIndividualKOT = fnUtilities.workSpaceSetValues(key_kotIndividualEnable) == "1";

      isDynamicQuantity = fnUtilities.workSpaceSetValues(dynamicQuantity) == "1";
      isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      // showInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1" ? true : false;
      // inventoryType = fnUtilities.workSpaceSetValues(inventoryType) == "All" ? "All" : "Specific";
      allowNegativeSale = fnUtilities.workSpaceSetValues(key_allowNegativeSales) == "1";
      isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
      isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
      discountType = fnUtilities.workSpaceSetValues(key_discountType);
      successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
      showImages = fnUtilities.workSpaceSetValues(key_showproductImages) == "1";
      additionalKOTButton = fnUtilities.workSpaceSetValues(key_additionalKOTButton) == "1";
      isSalesBillPadEnable = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";
      isStoreCustomerDetails = fnUtilities.workSpaceSetValues(key_store_customerDetails) == "1";
      isSalesCategoryEnabled = fnUtilities.workSpaceSetValues(key_sales_category) == "1";
      isEnableShiftWiseSales = fnUtilities.workSpaceSetValues(key_shiftBasedSales) == "1";
      splitInvoiceByPayment = fnUtilities.workSpaceSetValues(splitInvoiceByPaymentKey) == "1";
      isEnableAdvanceOrder = fnUtilities.workSpaceSetValues(key_advanceOrder) == "1";

      ///workspace user settings
      saleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
      if (saleLayout == "SaleLayout2" || saleLayout == "SaleLayout3") {
        saleLayout = "SaleLayout2";
      } else if (saleLayout == "DesktopLayout") {
        saleLayout = "DesktopLayout";
      } else {
        saleLayout = "SaleLayout1";
      }
    });
  }

  initSettings() async {
    await getWorkSpaceSettingValue();
    setState(() {
      //Workspace device settings
      MyApp.isListViewEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductListview) == "1";
      MyApp.isFilterEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductTapOnClear) == "1";
      isFavorite = fnUtilities.workSpaceDeviceSetValue(key_favoriteProduct) == "1";
      panels = fnUtilities.workSpaceDeviceSetValue(key_panelType);
      multiPrint = fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1";
      orderType = sales?.salesId != null ? sales?.orderType ?? 1 : orderType;
      enableOnlineOrder = orderType == 2;
      updateNamePanelType(panels ?? "");
      isExpandAdvanceOrderWidget = sales?.deliveryDateAndTime != null ? true : false;

      if (sales?.deliveryDateAndTime != null && sales?.deliveryDateAndTime != "") {
        deliveryDateAndTime = DateTime.tryParse(sales?.deliveryDateAndTime ?? "");
        List<String> deliveryDate = AdvanceOrderWidget.splitDeliveryDateTime(sales?.deliveryDateAndTime ?? '');
        deliveryDateController.text = deliveryDate[0];
        deliveryTimeController.text = "${deliveryDate[1]} ${deliveryDate[2]}";
      }
      if (orderNotes != null || orderNotes != "") {
        orderNoteController.text = orderNotes ?? '';
      }
    });

    if (!multiPrint) {
      multiPrint = false;
      printerUtilities.initPrint(printerForBill);
    }
    if (enableOnlineOrder) {
      await getOnlinePlatformList();
    }
    await assignProductList();
  }

  /// Get Printer Info
  PrinterDevices? printerForBill;
  PrinterDevices? printerForKOT;

  getPrinterInfo() {
    /// Get Printer Informations
    printerForBill = printerUtilities.getCurrentPrinter(PrinterFor.bill);
    printerForKOT = printerUtilities.getCurrentPrinter(PrinterFor.kot);
  }

  ///---///

  //CALCULATE TOTAL AMOUNT
  findTotal() {
    try {
      setState(() {
        total = 0;
        totalQty = 0;
        totalCGST = 0;
        totalSGST = 0;
        totalWithoutGST = 0;
        totalDiscount = 0.0;
        for (Transactions p in ListUtility.saleList) {
          if (p.amountWithoutGst != null) {
            totalWithoutGST += double.parse(p.amountWithoutGst!);
          }
          if (isComplementory) {
            total += double.parse(p.amountWithoutGst ?? "0");
          } else {
            total += double.parse(p.amount ?? "0");
          }
          // total += double.parse(p.amount ?? "0");
          totalQty += double.parse(p.qty ?? "0");
          if (p.cGstAmount != null && p.cGstAmount != "null") {
            totalCGST += double.parse(p.cGstAmount!).round();
            totalSGST += double.parse(p.sGstAmount!).round();
          }
          if (p.discountPrice != null) {
            totalDiscount += double.parse(p.discountPrice!);
          }
        }
        if (saleDiscountPrice != null) {
          totalDiscount = double.parse(saleDiscountPrice!);
        }
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "findTotal - SalesMainPage");
    }
  }

  //START INSERT FUNCTION
  Future startInsert(List<Transactions> saleList, paymentType, salesType, customerName, [String? salesName]) async {
    try {
      if (customerName == "") {
        customerName = null;
      }
      await insertSales(paymentType, salesType, customerName, salesName);
      for (Transactions t in saleList) {
        await insertSalesTransactionFun(t, insertSalesId ?? "", salesType, tranIsActiveSaleNo: 0);
      }
      // if (showInventory) {
      await stocksFunction.updateStockForNewSales(saleList);
      // }
      salesIdForReport = insertSalesId;
      return 1;
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "startInsert - SalesMainPage");
    }
  }

  // START UPDATE FUNCTION

  startUpdate(String salesType) async {
    try {
      await updateSales(salesType);
      await getTotalAmountFromTable(sales?.salesId ?? "").then((value) async {
        await updateSalesTable(value);
      });
      salesIdForReport = sales?.salesId;
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "startUpdate - SalesMainPage");
    }
  }

  //GET PRODUCT DETAILS BY ID FROM LISTNew
  Products? getProductInfoById(String prodId) {
    for (Products p in ListUtility.productList) {
      if (p.productId == prodId) {
        return p;
      }
    }
    return null;
  }

  //CALCULATE TAX FUNCTION
  callCalculate() async {
    try {
      SalesCalculateRequest calculateRequest = new SalesCalculateRequest();
      if (await fnUtilities.workSpaceSetValues(key_taxEnable) == "1") {
        calculateRequest.withTax = true;
        if (await fnUtilities.workSpaceSetValues(key_taxMode) == "1") {
          taxMode = 2;
          calculateRequest.includedTax = true;
        } else {
          taxMode = 3;
          calculateRequest.includedTax = false;
        }
      } else {
        taxMode = 1;
        calculateRequest.withTax = false;
      }
      var overallTax = await fnUtilities.workSpaceSetValues(key_overAllTax) ?? "0";
      if (sales?.salesId != null) {
        //For Update we need to find old transaction tax mode so we
        final reportDetail = ListUtility.reportList.firstWhere((element) => element.salesId == sales?.salesId, orElse: () {
          return Sales();
        });
        if (reportDetail.taxMode != null) {
          if (reportDetail.taxMode == 1) {
            calculateRequest.withTax = false;
            taxMode = 1;
          } else if (reportDetail.taxMode == 2) {
            taxMode = 2;
            calculateRequest.withTax = true;
            calculateRequest.includedTax = true;
          } else if (reportDetail.taxMode == 3) {
            taxMode = 3;
            calculateRequest.withTax = true;
            calculateRequest.includedTax = false;
          }
        }
      }

      /// Parcel Amount Settings
      bool isEnableParcel = false;
      double parcelPerProduct = 0.0;
      bool applyParcelForAll = false;
      if (await fnUtilities.workSpaceSetValues(key_parcelEnable) == "1" && (saleType != "SO" && saleType != "T")) {
        isEnableParcel = true;
        if (await fnUtilities.workSpaceSetValues(key_applyParcelFor) == "All") {
          applyParcelForAll = true;
          var overAllParcelCharge = await fnUtilities.workSpaceSetValues(key_overAllParcelCharge);
          parcelPerProduct = double.tryParse(overAllParcelCharge ?? "0") ?? 0;
        }
      }
      double totAmount = 0.0;
      ListUtility.saleList.forEach((element) {
        totAmount +=
            element.actualPrice != null ? (double.tryParse(element.actualPrice ?? "0") ?? 0.0) * (double.tryParse(element.qty ?? "0") ?? 0.0) : 0.0;
      });

      for (Transactions transactions in ListUtility.saleList) {
        Products? products = await getProductInfoById(transactions.prodId ?? '');
        calculateRequest.qty = transactions.qty.toDouble();
        calculateRequest.rate = double.parse(transactions.actualPrice ?? '0');

        if (saleDiscountPrice != null) {
          double pricePerRupee = double.parse(saleDiscountPrice ?? '0') / totAmount;
          double discountPerProduct = double.parse(transactions.actualPrice ?? '0') * pricePerRupee;
          calculateRequest.discountAmount = discountPerProduct;
        }

        if (transactions.discountPrice != null) {
          calculateRequest.discountAmount = double.parse(transactions.discountPrice ?? '0');
        }

        if (await fnUtilities.workSpaceSetValues(key_applyTaxFor) == "All") {
          calculateRequest.cgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
          calculateRequest.sgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
        } else {
          calculateRequest.cgst = products?.cGst == "" || products?.cGst == null ? 0 : double.parse(products?.cGst ?? "0");
          calculateRequest.sgst = products?.sGst == "" || products?.sGst == null ? 0 : double.parse(products?.sGst ?? "0");
        }

        /// Get Parcel Amount per product
        if (isEnableParcel) {
          if (!applyParcelForAll) {
            parcelPerProduct = products!.parcelAmount.isEmptyOrNull ? 0 : double.tryParse(products.parcelAmount ?? "0.0") ?? 0.0;
          }
        }
        calculateRequest.parcelPerProduct = parcelPerProduct;

        SalesCalculateResponse calculateResponse = salesFun.calculateTax(calculateRequest);
        transactions.price = calculateResponse.rate.toString();
        transactions.discountPrice = calculateResponse.discountAmount.toString();
        transactions.cGst = calculateResponse.cgst.toString();
        transactions.cGstAmount = calculateResponse.cgstAmt.toString();
        transactions.sGst = calculateResponse.sgst.toString();
        transactions.sGstAmount = calculateResponse.sgstAmt.toString();
        transactions.amountWithoutGst = calculateResponse.totalAmtWithoutGst.toString();
        transactions.amount = calculateResponse.totalAmt.toString();
        transactions.parcelAmountWithoutGst = calculateResponse.parcelAmountWithoutGst.toString();
        transactions.parcelAmount = calculateResponse.parcelAmount.toString();
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "callCalculate - SalesMainPage");
    }
  }

  //INSERT SALES DETAILS FOR REPORTS PAGE
  insertSalesTransactionFun(Transactions transaction, String salesId, String tableName, {int? tranIsActiveSaleNo}) async {
    try {
      var uuid = new Uuid();
      String transactId = uuid.v4().toString();
      // int isActivaeSaleNo = sales?.salesId == null
      //     ? 0
      //     : await dbHelper.queryActiveTransactionRowCount(sales?.salesId);

      int? transactSaleNo;
      if (saleType == "SO" || saleType == "T") {
        if (sales?.isActiveSale != null) {
          transactSaleNo = tranIsActiveSaleNo ?? 0;
        } else {
          transactSaleNo = tranIsActiveSaleNo! + 1;
        }
      } else {
        transactSaleNo = null;
      }

      transaction.transactId = transactId;
      transaction.salesId = salesId;
      transaction.sync = 0;
      transaction.workspaceId = MyApp.activeWorkspace.workspaceId;
      transaction.rowStatus = 0;
      transaction.isActiveSale = transactSaleNo;
      transaction.isIGST = isIGST;
      if (tableName == "SALES") {
        await salesTransDBScript.insertSalesTransactions(transaction, transactionType: saleType == "SO" || saleType == "T" ? "staging" : "");
      } else {
        await salesHoldSalesDetailsDBScript.insertHoldTransactions(transaction);
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "insertSalesTransactionFun - SalesMainPage");
    }
  }

  //GET A INVOICE ID FUNCTION
  getMaxInvoiceId() async {
    try {
      String prefix = await dbOperations.queryPrefixName();
      bool isResetSalesNo = await await fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1" ? true : false;
      String prefixMax = await salesDetailsDBScript.queryGetMaxInvoiceId(prefix, isResetSalesNo);
      if (prefixMax == "0") {
        return prefix + 1.toString();
      } else {
        return prefix + (int.parse(prefixMax.substring(prefix.length)) + 1).toString();
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getMaxInvoiceId - SalesMainPage");
    }
  }

  isCustomerGiveAmount(String? customerID) {
    if (customerNameEnable &&
        givenAmountController.text != "0" &&
        givenAmountController.text != "" &&
        saleType != "SO" &&
        saleType != "T" &&
        customerID != null) {
      return true;
    }
    return false;
  }

  shiftWiseSalesFunction() async {
    try {
      List<ShiftwiseSalesModel> shiftSalesList = [];
      String shiftID;
      shiftSalesList.clear();
      shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2 && element.isActive == true));

      DateTime currentTime = DateTime.now();
      // DateTime currentTimeForm = DateTime(currentTime.year, currentTime.month, currentTime.day, currentTime.hour, currentTime.minute);
      for (ShiftwiseSalesModel shift in shiftSalesList) {
        int fromTimeInMinutes = _timeStringToMinutes(shift.fromTime ?? "");
        int toTimeInMinutes = _timeStringToMinutes(shift.toTime ?? "");
        int currentTimeInMinutes = currentTime.hour * 60 + currentTime.minute;

        if (toTimeInMinutes < fromTimeInMinutes) {
          // If toTime is earlier in the day than fromTime, it means the shift spans midnight
          // Adjust toTime to be the next day
          toTimeInMinutes += 24 * 60; // Add 24 hours in minutes
        }

        if (currentTimeInMinutes >= fromTimeInMinutes && currentTimeInMinutes < toTimeInMinutes) {
          shiftID = shift.shiftId ?? "";
          print(shift.id);
          return shiftID;
        }
      }
      return null; // Return null if no shift is found
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "shiftWiseSalesFunction - SalesMainPage");
    }
  }

  void mergeDateAndTime() {
    DateTime date = DateTime.parse(deliveryDateController.text);

    List<String> timeComponents = deliveryTimeController.text.split(':');
    int hour = int.parse(timeComponents[0]);
    int minute = int.parse(timeComponents[1].split(' ')[0]);
    String amPm = timeComponents[1].split(' ')[1];

    if (amPm == 'PM' && hour != 12) {
      hour += 12;
    } else if (amPm == 'AM' && hour == 12) {
      hour = 0;
    }

    deliveryDateAndTime = DateTime(date.year, date.month, date.day, hour, minute);
  }

  int _timeStringToMinutes(String timeString) {
    List<String> parts = timeString.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1].split(' ')[0]);
    String period = parts[1].split(' ')[1];

    if (period.toLowerCase() == 'pm' && hours != 12) {
      hours += 12;
    } else if (period.toLowerCase() == 'am' && hours == 12) {
      hours = 0;
    }

    return hours * 60 + minutes;
  }

  //INSERT SALES DETAIL
  insertSales(paymentType, salesType, customerName, salesName) async {
    try {
      if (isEnableShiftWiseSales) {
        shiftId = await shiftWiseSalesFunction();
      }
      var uuid = new Uuid();
      //if (saleType != "SO" && saleType != "T") {
      invoiceAdd = await getMaxInvoiceId();
      // }
      String salesId = uuid.v4().toString();
      insertSalesId = salesId;

      Sales report = Sales();
      report.holdSalesName = salesName;
      report.salesId = salesId;
      report.invoiceId = invoiceAdd;
      report.customerName = customerName;
      report.date = DateTime.now().toString();
      report.totalAmount = total.toString();
      report.paymentType = paymentType;
      report.sync = 0;
      report.taxMode = taxMode;
      report.workspaceId = MyApp.activeWorkspace.workspaceId;
      report.rowStatus = 0;
      report.isActiveSale = saleType == "SO" || saleType == "T" ? 1 : null;
      report.fkTableID = sales?.fkTableID;
      report.createdBy = MyApp.activeUser.username;
      report.discountFormula = saleDiscountFormula;
      report.discountPrice = saleDiscountPrice;
      report.customerID = selectCustomer?.customerID ?? customerId;
      report.mobileNo = mobileNoController.text != "" ? mobileNoController.text : null;
      report.orderType = orderType;
      report.onlinePlatformID = selectedOnlinePlatform != null ? selectedOnlinePlatform?.onlinePlatformId : "";
      report.onlineRefNo = onlineRefNoController.text != "" ? onlineRefNoController.text : null;
      report.salesStatus = isExpandAdvanceOrderWidget
          ? 3
          : (saleType == "SO" || saleType == "T")
              ? null
              : 1;
      report.shiftId = shiftId != null ? shiftId : null;
      report.paymentCategoryID =
          (saleType == "SO" || saleType == "T") ? null : salesFun.setPaymentCategoryId(paymentMappingList, splitInvoiceByPayment);
      if (selectedSalesCategory != null) {
        report.salesCategoryId = (saleType != "SO" && saleType != "T") && isSalesCategoryEnabled ? selectedSalesCategory?.id : null;
      }
      if (isExpandAdvanceOrderWidget) {
        isEnableDuplicationPayment = true;
        report.deliveryDateAndTime = deliveryDateAndTime.toString();
      }
      if (salesType == "SALES") {
        await salesDetailsDBScript.insertSalesDetails(report);
        await salesFun.salesPaymentMappingMethod(paymentMappingList, saleId: salesId, enableDuplicationPayment: isEnableDuplicationPayment);
        if (customerNameEnable && saleType != "SO" && saleType != "T" && selectCustomer?.customerID != null) {
          await insertSalesTypeFunction(report);
        }
        if (isExpandAdvanceOrderWidget) {
          AdvanceOrderNoteModel? advanceOrderNoteModel = AdvanceOrderNoteModel();
          advanceOrderNoteModel.workspaceId = MyApp.activeWorkspace.workspaceId;
          advanceOrderNoteModel.salesId = salesId;
          advanceOrderNoteModel.note = orderNoteController.text;
          advanceOrderNoteModel.createdDate = DateTime.now().toString();
          advanceOrderNoteModel.updatedDate = null;
          advanceOrderNoteModel.status = 0;
          advanceOrderNoteModel.sync = 0;
          advanceOrder.insertOrderNote(advanceOrderNoteModel);
        }
        showToast("Saved successfully");
      } else {
        await salesHoldSalesDetailsDBScript.insertHoldSalesDetails(report);
        showToast("Holded successfully");
      }
      saleDiscountFormula = null;
      saleDiscountPrice = null;
      selectCustomer = null;
      isExpandAdvanceOrderWidget = false;
      orderNoteController.clear();
      deliveryTimeController.clear();
      deliveryDateController.clear();
      mobileNoController.clear();
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "insertSales - SalesMainPage");
    }
  }

  insertSalesTypeFunction(Sales reports) async {
    try {
      var uuid = new Uuid();
      SalesType model = SalesType();
      model.salesTypeID = uuid.v4().toString();
      model.salesID = reports.salesId;
      model.customerID = reports.customerID;
      model.createdDate = reports.date;
      model.salesType = "debit";
      model.syncStatus = 0;
      model.rowStatus = 0;
      model.workspaceID = MyApp.activeWorkspace.workspaceId;
      model.amount = reports.totalAmount;
      await salesTypeDB.insertSalesType(model);
      Balance balance = Balance();
      balance.customerID = reports.customerID;
      balance.balance = reports.totalAmount;
      await fnUtilities.insertOrUpdateBalance(balance, "debit");
      if (await isCustomerGiveAmount(selectCustomer?.customerID)) {
        model.salesTypeID = uuid.v4().toString();
        model.paymentTypeID = null;
        model.salesType = "credit";
        model.amount = givenAmountController.text;
        await salesTypeDB.insertSalesType(model);
        balance.customerID = reports.customerID;
        balance.balance = reports.totalAmount;
        await fnUtilities.insertOrUpdateBalance(balance, "credit");
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "insertSalesTypeFunction - SalesMainPage");
    }
  }

  //GET TOTAL AMOUNT FROM DB
  Future<String> getTotalAmountFromTable(String id) async {
    var allrows = saleType != "SO" && saleType != "T"
        ? await salesTransDBScript.calculateTotalAmount(id)
        : await salesTransDBScript.calculateTotalAmount(id, tableName: "staging");
    String amount = allrows[0]['amount'] != null ? allrows[0]['amount'].toString() : "0";
    // if (saleDiscountPrice != null && saleType != "SO" && saleType != "T") {
    //   amount = (double.parse(amount) - double.parse(saleDiscountPrice!)).toString();
    // }
    return amount;
  }

  //UPDATE SALES DETAILS
  updateSalesTable(String amount) async {
    double parseAmount = double.tryParse(amount) ?? 0;
    try {
      if (isEnableShiftWiseSales) {
        shiftId = await shiftWiseSalesFunction();
      }
      final syncNo = await salesDetailsDBScript.queryGetSalesSyncNo(sales?.salesId);
      Sales sale = Sales(
          salesId: sales?.salesId,
          date: DateTime.now().toString(),
          totalAmount: parseAmount > 0 ? parseAmount.toString() : sales?.totalAmount,
          taxMode: taxMode,
          customerName: customerNameController.text == '' ? null : customerNameController.text,
          paymentType: null,
          sync: 0,
          rowStatus: parseAmount == 0
              ? 2
              : syncNo == 0
                  ? 0
                  : 1,
          fkTableID: sales?.fkTableID,
          invoiceId: sales?.invoiceId,
          isActiveSale: saleType == "SO" || saleType == "T" ? 1 : null,
          discountFormula: saleDiscountFormula,
          discountPrice: saleDiscountPrice,
          invoiceNo: sales?.invoiceNo,
          customerID: selectCustomer?.customerID,
          salesStatus: sales!.salesStatus ?? 1,
          mobileNo: mobileNoController.text != "" ? mobileNoController.text : null,
          orderType: orderType,
          onlinePlatformID: selectedOnlinePlatform != null ? selectedOnlinePlatform?.onlinePlatformId : '',
          shiftId: shiftId != null ? shiftId : null,
          salesCategoryId: selectedSalesCategory != null ? selectedSalesCategory?.id : sales?.salesCategoryId,
          onlineRefNo: onlineRefNoController.text != "" ? onlineRefNoController.text : null,
          paymentCategoryID: salesFun.setPaymentCategoryId(
            paymentMappingList,
            splitInvoiceByPayment,
          ),
          deliveryDateAndTime: isExpandAdvanceOrderWidget ? deliveryDateAndTime.toString() : null);

      await salesDetailsDBScript.updateSalesTable(sale);
      if (customerNameEnable && saleType != "SO" && saleType != "T" && selectCustomer?.customerID != null) {
        SalesType model = SalesType();
        model.salesID = sale.salesId;
        model.customerID = sale.customerID;
        model.createdDate = sale.date;
        model.salesType = "debit";
        model.syncStatus = 0;
        model.rowStatus = await commonDB.checkNonSyncCommonFunction(
                    "SALESTYPE", "FK_SalesID", sales?.salesId != null ? sales?.salesId.toString() : "", "SyncStatus") ==
                0
            ? 1
            : 0;
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.amount = sale.totalAmount;
        await salesTypeDB.updateSalesType(model);
        Balance balance = Balance();
        balance.customerID = sale.customerID;
        balance.balance = (double.parse(sale.totalAmount ?? "0") - double.parse(sales?.totalAmount ?? "0")).toString();
        await fnUtilities.insertOrUpdateBalance(balance, "debit", isUpdate: true);
        if (await isCustomerGiveAmount(selectCustomer?.customerID)) {
          model.salesType = "credit";
          model.paymentTypeID = sale.customerID;
          model.amount = givenAmountController.text;
          await salesTypeDB.updateSalesType(model);
          balance.customerID = sale.customerID;
          String difference = (double.parse(givenAmountController.text) - double.parse(creditAmount ?? "0")).toString();
          balance.balance = difference;
          await fnUtilities.insertOrUpdateBalance(balance, "credit", isUpdate: true);
        }
      }
      // advance notes update section
      if (isExpandAdvanceOrderWidget) {
        AdvanceOrderNoteModel? advanceOrderNoteModel = AdvanceOrderNoteModel();
        advanceOrderNoteModel.workspaceId = MyApp.activeWorkspace.workspaceId;
        advanceOrderNoteModel.salesId = sales?.salesId;
        advanceOrderNoteModel.note = orderNoteController.text;
        advanceOrderNoteModel.createdDate = null;
        advanceOrderNoteModel.updatedDate = DateTime.now().toString();
        advanceOrderNoteModel.status = 1;
        advanceOrderNoteModel.sync = 0;
        advanceOrder.updateOrderNote(advanceOrderNoteModel);
      }
      selectPaymentType = null;
      selectedOnlinePlatform = null;
      saleDiscountPrice = null;
      saleDiscountFormula = null;
      mobileNoController.clear();
      orderNoteController.clear();
      isExpandAdvanceOrderWidget = false;
      selectCustomer = null;
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "updateSalesTable - SalesMainPage");
    }
  }

  //GET ALL SALES BY SALES ID
  Future<List?> queryAllTransactIdBySalesId(salesId) async {
    try {
      final allRows = saleType != "SO" && saleType != "T"
          ? await salesTransDBScript.queryAllTransactIdBasedOnSalesId(salesId)
          : await salesTransDBScript.queryAllTransactIdBasedOnSalesId(salesId, tableName: "staging");
      var transactId = [];
      allRows.forEach((row) => transactId.add(row["transactId"]));
      return transactId;
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "queryAllTransactIdBySalesId - SalesMainPage");
    }
    return null;
  }

  tableResetDeleteFunction() async {
    try {
      if ((saleType == "SO" || saleType == "T") && sales?.isActiveSale != null) {
        List<Transactions> tempDeleteStagingList = [];
        final allRows =
            await salesTransDBScript.queryAllRowsBasedOnSalesId(sales?.salesId, tableName: "staging", isActiveSaleNo: sales?.isActiveSale ?? -1);
        allRows.forEach((row) => tempDeleteStagingList.add(Transactions.fromMap(row)));
        for (Transactions tran in tempDeleteStagingList) {
          String? id =
              ListUtility.saleList.firstWhere((element) => element.transactId == tran.transactId, orElse: () => null ?? Transactions()).transactId ??
                  null;
          if (id == null) {
            await salestransStagingDBScript.deleteProductsBasedSalesIdAndProductIdAndToppingIdInStaging(
                sales?.salesId, tran.prodId, tran.toppingId, tran.price, sales?.isActiveSale, null);
          }
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "tableResetDeleteFunction - SalesMainPage");
    }
  }

  //INSERT OR UPDATE SALES TRANSACTIONS
  updateSales(String salesType) async {
    try {
      int? tempIsActiveSaleNo =
          sales?.isActiveSale == null ? await salestransStagingDBScript.queryActiveTransactionRowCount(sales?.salesId) : sales?.isActiveSale;
      tempIsActiveSaleNo = tempIsActiveSaleNo != null ? tempIsActiveSaleNo : 0;
      List transactIdList = await queryAllTransactIdBySalesId(sales?.salesId) ?? [];
      await tableResetDeleteFunction();
      for (Transactions transaction in ListUtility.saleList) {
        final syncNo = transactIdList.length != 0 ? await salesTransDBScript.queryGetTransactionSyncNo(transactIdList[0]) : 0;
        if (transactIdList.contains(transaction.transactId)) {
          // if (showInventory) {
          if (transaction.toppingId != null) {
            String? qty = await stocksFunction.getUpdateStockQtyFun(transaction, "Update");
            if (qty != null) {
              int status = syncNo == 0 ? 0 : 1;
              await stocksFunction.updateStockForUpdateSales(qty, transaction.prodId ?? "", status);
            }
          }
          // }
          transaction.salesId = sales?.salesId;
          transaction.sync = 0;
          transaction.rowStatus = syncNo == 0 ? 0 : 1;
          transaction.isIGST = isIGST;

          await salesTransDBScript.updateTransactionsTable(transaction, transactionType: saleType == "SO" || saleType == "T" ? "staging" : "");
        } else {
          // if (showInventory) {
          if (transaction.toppingId == null) {
            String? qty = await stocksFunction.getUpdateStockQtyFun(transaction, "Insert");
            if (qty != null) {
              await stocksFunction.updateStockForUpdateSales(qty, transaction.prodId ?? "", 0);
            }
          }
          // }
          insertSalesTransactionFun(transaction, sales?.salesId ?? "", salesType, tranIsActiveSaleNo: tempIsActiveSaleNo);
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "updateSales - SalesMainPage");
    }
  }

  //DELETE A SALES BASED ON SALES ID
  void _delete(salesId, id, toppingId, Transactions saleItem) async {
    try {
      // Assuming that the number of rows is the id for the last row.
      var transact;
      try {
        transact = ListUtility.saleList.firstWhere((item) => item.prodId == id);
      } catch (ex) {
        transact = null;
      }
      String? fullTopping;
      if (toppingId == null) {
        try {
          fullTopping = ListUtility.saleList.firstWhere((item) => item.prodId == id && item.toppingId != null).toppingId;
        } catch (ex) {
          fullTopping = null;
        }
      }
      int? syncNo;
      if (transact?.transactId != null) {
        syncNo = await salesTransDBScript.queryGetTransactionSyncNo(transact.transactId);
      }
      if (saleType == "SO" || saleType == "T") {
        await salestransStagingDBScript.deleteProductsBasedSalesIdAndProductIdAndToppingIdInStaging(
            salesId, id, toppingId, saleItem.price, sales?.isActiveSale, fullTopping);
      }
      String? qty;
      //if (showInventory) {
      String? currentSold = await stocksDBScript.getColumnFromStocksByProductID(id, "stocksSold");
      if (currentSold != null) {
        String oldQty = ListUtility.saleList.firstWhere((item) => item.prodId == id).qty ?? "";
        qty = (int.parse(currentSold) - int.parse(double.parse(oldQty).toStringAsFixed(0))).toString();
      }
      //}
      if (syncNo == 0) {
        //if (showInventory) {
        if (qty != null) {
          await stocksFunction.updateStockForUpdateSales(qty, id, 0);
        }
        //}
        if (toppingId == null) {
          await salesTransDBScript.deleteProductsBasedSalesIdAndProductId(saleItem, fullTopping ?? "");
        } else {
          await salesTransDBScript.deleteProductsBasedSalesIdAndProductIdAndToppingId(saleItem);
        }
      } else if (syncNo != null) {
        //if (showInventory) {
        if (qty != null) {
          await stocksFunction.updateStockForUpdateSales(qty, id, 1);
        }
        //}
        if (toppingId == null) {
          await salesTransDBScript.updateProductsBasedSalesIdAndProductId(saleItem, fullTopping ?? "");
        } else {
          await salesTransDBScript.updateProductsBasedSalesIdAndProductIdAndTopping(saleItem);
        }
      }
      if (toppingId == null) {
        if (fullTopping == null) {
          ListUtility.saleList.removeWhere((item) => item.prodId == id && item.price == saleItem.price);
          transactionCardShow(ListUtility.saleList);
        } else {
          ListUtility.saleList.removeWhere((item) => item.prodId == id);
          transactionCardShow(ListUtility.saleList);
        }
      } else {
        ListUtility.saleList.removeWhere((item) => item.prodId == id && item.toppingId == toppingId && item.price == saleItem.price);
        transactionCardShow(ListUtility.saleList);
      }
      await getTotalAmountFromTable(salesId).then((value) {
        updateSalesTable(value);
      });
      findTotal();
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "_delete - SalesMainPage");
    }
  }

  String? tableName;
  getAllShopTableDetails() async {
    final allRows = await shopTableDbScript.queryGetShopTables();
    if (this.mounted) {
      setState(() {
        shopTableList.clear();
        allRows.forEach((row) => shopTableList.add(ShopTable.fromMap(row)));
      });
    }
  }

  List<ShopTable> shopTableList = [];
  //GET REPORT DETAILS FORM DB
  _queryAllReports() async {
    try {
      final allRows = await salesDetailsDBScript.queryallRowsSalesDetails();
      setState(() {
        ListUtility.reportList.clear();
        allRows.forEach((row) => ListUtility.reportList.add(Sales.fromMap(row)));
      });
      if (sales?.fkTableID != null) {
        await getAllShopTableDetails();
        tableName = shopTableList.firstWhere((element) => element.shopTableId == sales?.fkTableID, orElse: () => ShopTable()).shopTableName;
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "_queryAllReports - SalesMainPage");
    }
  }

  commonSalesPrintValue() {
    invoiceAdd = sales?.invoiceId != null ? sales?.invoiceId : invoiceAdd;
    Sales s = Sales();
    s.invoiceId = invoiceAdd;
    s.customerName = customerNameController.text;
    s.fkTableID = sales?.fkTableID;
    s.tableName = tableName;
    s.paymentType = null;
    s.paymentName = null;
    s.returnAmount = returnAmount;
    s.givenAmount = givenAmountController.text != "" ? givenAmountController.text : "0";
    s.totalAmount = total.toString();
    s.isIGST = isIGST;
    s.discountPrice = totalDiscount.toString();
    s.orderType = orderType;
    s.onlinePlatformID = selectedOnlinePlatform != null ? selectedOnlinePlatform?.onlinePlatformId : '';
    s.onlineRefNo = onlineRefNoController.text;
    s.deliveryDateAndTime = deliveryDateAndTime.toString();
    return s;
  }

  //SALES PRINTER DEVICE FUNCTION
  Future<int> salesPrint(BuildContext context1, paymentType, {int printType = 0}) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      if (!await printerUtilities.checkPrinterConnection(context)) {
        LoadingAlertWidget.onStopping();
        return 0;
      } else {
        List<Transactions> printSaleList = [];
        printSaleList.addAll(ListUtility.saleList);
        Sales s = await commonSalesPrintValue();
        if (sales?.salesId == null) {
          await startInsert(ListUtility.saleList, paymentType, "SALES", customerNameController.text);
          await _queryAllReports();
          s.invoiceId = invoiceAdd;
          if (printerForBill?.printerFormat == "Default") {
            if (saleType != "SO" && saleType != "T") {
              if (printType == 1) {
                if (printerForKOT?.paperSize == 58) {
                  await salesprint.salesPrintKOT(s, tableName, printerForKOT, printSaleList);
                } else {
                  await salesprint.salesPrintKOT80MM(s, tableName ?? "", printerForKOT, printSaleList);
                }
              } else {
                if (printerForBill?.paperSize == 58) {
                  await salesprint.salesPrint(s, saleType, printSaleList, paymentMappingList,
                      salesStatus: isExpandAdvanceOrderWidget ? SalesStatus.advanceOrder : null,
                      isComplementory: isComplementory,
                      isAdvanceOrder: isEnableAdvanceOrder);
                } else {
                  await salesprint.salesPrint80MM(
                    s,
                    saleType,
                    printSaleList,
                    paymentMappingList,
                    isComplementory: isComplementory,
                    isAdvanceOrder: isEnableAdvanceOrder,
                    salesStatus: isExpandAdvanceOrderWidget ? SalesStatus.advanceOrder : null,
                  );
                }
                customerNameController.clear();
                selectPaymentType = null;
                selectedOnlinePlatform = null;
                if (multiPrint == true) {
                  printerUtilities.disconnect();
                }
                if (isIndividualKOT ?? false) {
                  await individualConformationDialog(context, s);
                }
              }
            } else {
              if (printerForKOT?.paperSize == 58) {
                await salesprint.salesPrintKOT(s, tableName, printerForKOT, printSaleList);
              } else {
                await salesprint.salesPrintKOT80MM(s, tableName, printerForKOT, printSaleList);
              }
            }
            if (multiPrint == true) {
              printerUtilities.disconnect();
            }
          } else {
            if (saleType != "SO" && saleType != "T") {
              if (printerForBill?.paperSize == 58) {
                await salesprint.salesPrintTvs(
                  s,
                  saleType,
                  printSaleList,
                  paymentMappingList,
                  isComplementory: isComplementory,
                  isAdvanceOrder: isEnableAdvanceOrder,
                  salesStatus: isExpandAdvanceOrderWidget ? SalesStatus.advanceOrder : null,
                );
              } else {
                await salesprint.salesPrintTvs80MM(
                  s,
                  saleType,
                  printSaleList,
                  paymentMappingList,
                  isComplementory: isComplementory,
                  isAdvanceOrder: isEnableAdvanceOrder,
                  salesStatus: isExpandAdvanceOrderWidget ? SalesStatus.advanceOrder : null,
                );
              }
              if (isIndividualKOT ?? false) {
                await individualConformationDialog(context, s);
              }
            } else {
              if (printerForKOT?.paperSize == 58) {
                await salesprint.salesPrintKOTTVS(s, tableName, printerForKOT, printSaleList);
              } else {
                await salesprint.salesPrintKOTTvs80MM(s, tableName, printerForKOT, printSaleList);
              }
            }
            customerNameController.clear();
            selectPaymentType = null;
            selectedOnlinePlatform = null;
            selectedSalesCategory = null;
            if (multiPrint == true) {
              printerUtilities.disconnect();
            }
          }
          if (!isIndividualKOT!) {
            setState(() {
              ListUtility.saleList.clear();
              transactionCardList.clear();
              findTotal();
            });
          }
        } else {
          await startUpdate("SALES");
          await salesFun.salesPaymentMappingMethod(paymentMappingList, saleId: sales?.salesId);
          s.invoiceId = invoiceAdd;
          if (sales?.salesId != null && (saleType == "SO" || saleType == "T")) {
            List<Transactions> tempSaleList = [];
            tempSaleList = ListUtility.saleList.where((element) => element.isActiveSale == null).toList();
            ListUtility.saleList.clear();
            transactionCardList.clear();
            ListUtility.saleList = tempSaleList;
          }
          showToast("Updated successfully");
          if (printerForBill?.printerFormat == "Default") {
            if (saleType != "SO" && saleType != "T") {
              if (printType == 1) {
                if (printerForKOT?.paperSize == 58) {
                  await salesprint.salesPrintKOT(s, tableName, printerForKOT, printSaleList);
                } else {
                  await salesprint.salesPrintKOT80MM(s, tableName, printerForKOT, printSaleList);
                }
              } else {
                if (printerForBill?.paperSize == 58) {
                  await salesprint.salesPrint(s, saleType, printSaleList, paymentMappingList);
                } else {
                  await salesprint.salesPrint80MM(s, saleType, printSaleList, paymentMapList ?? []);
                }
                if (isIndividualKOT ?? false) {
                  await individualConformationDialog(context, s);
                }
              }
            } else {
              if (printerForKOT?.paperSize == 58) {
                await salesprint.salesPrintKOT(s, tableName, printerForKOT, printSaleList);
              } else {
                await salesprint.salesPrintKOT80MM(s, tableName, printerForKOT, printSaleList);
              }
            }
            customerNameController.clear();
            selectPaymentType = null;
            selectedOnlinePlatform = null;
            selectedSalesCategory = null;
            if (multiPrint == true) {
              printerUtilities.disconnect();
            }
          } else {
            if (saleType != "SO" && saleType != "T") {
              if (printerForBill?.paperSize == 58) {
                await salesprint.salesPrintTvs(s, saleType, printSaleList, paymentMappingList);
              } else {
                await salesprint.salesPrintTvs80MM(s, saleType, printSaleList, paymentMappingList);
              }
              if (isIndividualKOT ?? false) {
                await individualConformationDialog(context, s);
              }
            } else {
              if (printerForKOT?.paperSize == 58) {
                await salesprint.salesPrintKOTTVS(s, tableName, printerForKOT, printSaleList);
              } else {
                await salesprint.salesPrintKOTTvs80MM(s, tableName, printerForKOT, printSaleList);
              }
            }
            customerNameController.clear();
            selectPaymentType = null;
            selectedOnlinePlatform = null;
            selectedSalesCategory = null;
          }
          if (!isIndividualKOT!) {
            setState(() {
              ListUtility.saleList.clear();
              transactionCardList.clear();
              findTotal();
              if (saleLayout == "SaleLayout1") {
                sales = null;
              }
              selectPaymentType = null;
              selectedOnlinePlatform = null;
              selectedSalesCategory = null;
            });
            if (saleLayout == "SaleLayout2" && (saleType != "SO" && saleType != "T")) {
              sales = null;
            }
          }
        }
        temPaymentMappingList.clear();
        paymentMappingList.clear();
        Navigator.pop(context, true);
        LoadingAlertWidget.onStopping();
        return 1;
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "salesPrint - SalesMainPage");
      return -1;
    }
  }

  //SET PRODUCT ID DETAILS
  filterProductsById(String id) {
    setState(() {
      prodId += id;
      filterProductList.clear();
      getfilterProductlistById(prodId);
    });
  }

  //SET PRODUCT KEY DETAILS
  filterProductsByCategory(String key, String keyName) {
    setState(() {
      filterProductList.clear();
      getfilterProductlistByKey(key, keyName);
    });
  }

  //GET FILTER PRODUCT FROM LIST
  getfilterProductlistById(String id) async {
    if (id == "") {
      setState(() {
        filterProductList.addAll(productsToAdd);
        assignCardList(filterProductList);
      });
    } else {
      for (Products p in productsToAdd) {
        if (p.productSaleId != null && p.productSaleId.toString().contains(prodId)) {
          p.imagePath = await fnUtilities.readFileFromLocal(p.image);
          setState(() {
            filterProductList.add(p);
            assignCardList(filterProductList);
          });
        }
      }
    }
    setState(() {
      filterProductList.add(Products());
      assignCardList(filterProductList);
    });
  }

  //GET FILTER PRODUCT FROM LIST
  getfilterProductlistByKey(String key, String keyName) async {
    if (keyName.toLowerCase() == "all") {
      setState(() {
        filterProductList.addAll(productsToAdd);
        assignCardList(filterProductList);
      });
    } else {
      for (Products p in productsToAdd) {
        if (p.keyID == key || p.productKeyName == keyName) {
          p.imagePath = await fnUtilities.readFileFromLocal(p.image);
          setState(() {
            filterProductList.add(p);
            assignCardList(filterProductList);
          });
        }
      }
    }

    setState(() {
      filterProductList.add(Products());
      assignCardList(filterProductList);
    });
  }

  //GET PRODUCT DETAILS BY NAME
  void filterProductsByName(String name) async {
    setState(() {
      prodName = name;
      filterProductList.clear();
      for (Products p in ListUtility.productList) {
        if (p.name != null && p.name!.toLowerCase().contains(name.toLowerCase())) {
          filterProductList.add(p);
        }
      }
    });
    filterProductList.add(Products());
    assignCardList(filterProductList);
  }

  //ADD PRODUCT IN SALES LIST
  addProductsInSalesList1(Products item) async {
    int gridIndex = -1;
    double amount;
    for (int i = 0; i < ListUtility.saleList.length; i++) {
      for (Topping t in productToppingNewList) {
        if (t.toppingId == ListUtility.saleList[i].toppingId &&
            double.parse(t.toppingPrice ?? "0") == double.parse(ListUtility.saleList[i].actualPrice ?? "0")) {
          gridIndex = i;
          amount = (double.parse(ListUtility.saleList[i].qty ?? "0") + 1) * double.parse(t.toppingPrice ?? "0");

          ListUtility.saleList[i] = Transactions(
            id: ListUtility.saleList[i].id,
            transactId: ListUtility.saleList[i].transactId,
            salesId: ListUtility.saleList[i].salesId,
            prodId: ListUtility.saleList[i].prodId,
            productType: "Topping",
            toppingId: t.toppingId,
            name: t.toppingName,
            price: t.toppingPrice,
            actualPrice: t.toppingPrice,
            cGst: ListUtility.saleList[i].cGst,
            sGst: ListUtility.saleList[i].sGst,
            qty: (double.parse(ListUtility.saleList[i].qty ?? "0") + 1).toString(),
            amount: amount.toString(),
          );
        }
      }
    }

    if (gridIndex == -1) {
      if (productToppingNewList.length != 0) {
        for (Topping t in productToppingNewList) {
          ListUtility.saleList.add(Transactions(
            prodId: item.productId,
            productType: "Topping",
            toppingId: t.toppingId,
            name: t.toppingName,
            qty: "1",
            price: t.toppingPrice,
            actualPrice: t.toppingPrice,
            amount: t.toppingPrice,
            cGst: item.cGst,
            sGst: item.sGst,
          ));
        }
      }
    }
    await callCalculate();
    await findTotal();
    productToppingNewList.clear();
    transactionCardShow(ListUtility.saleList);
  }

  //ADD PRODUCT IN SALES LIST
  addProductsInSalesList(Products item, PriceUnitType? unitItem) async {
    if (await stocksFunction.checkIsStockAvailable(item, ListUtility.saleList, dynamicQuantity.round())) {
      String price;
      PriceUnitType? unit;

      if (item.priceType == null || (item.priceType == 0 && (item.price == "" || item.price == null))) {
        price = priceController.text;
      } else {
        price = item.price ?? "0";
      }

      if (unitItem != null) {
        unit = PriceUnitType(
            id: unitItem.id,
            priceTypeId: unitItem.priceTypeId,
            productId: unitItem.productId,
            measurement: unitItem.measurement,
            measurementPrice: unitItem.measurementPrice,
            isActive: unitItem.isActive,
            workspaceId: unitItem.workspaceId,
            measurementSync: unitItem.measurementSync,
            rowStatus: unitItem.rowStatus);
        if (unit.measurementPrice == "" || unit.measurementPrice == null) {
          unit.measurementPrice = priceController.text;
          // price = priceController.text;
        }
        price = unit.measurementPrice ?? "0";
      }

      int gridIndex = -1;
      double amount;

      for (int i = 0; i < ListUtility.saleList.length; i++) {
        final saleItem = ListUtility.saleList[i];
        if ((item.priceType != 2 && item.productId == saleItem.prodId) ||
            (item.priceType == 2 && unit?.productId == saleItem.prodId && unit?.priceTypeId == saleItem.priceTypeId)) {
          if (double.parse(price != "" ? price : '0') == double.parse(saleItem.actualPrice != '' ? saleItem.actualPrice ?? "0" : '0') &&
              (saleItem.toppingId == null || item.priceType == 2)) {
            gridIndex = i;
            final newQty = double.parse(saleItem.qty ?? "0") + dynamicQuantity;
            amount = newQty * double.parse(price != "" ? price : "0");
            final name = item.priceType != 2 ? item.name : '${item.name}-${unit?.measurement}';
            ListUtility.saleList[i] = Transactions(
              id: saleItem.id,
              transactId: saleItem.transactId,
              salesId: saleItem.salesId,
              prodId: saleItem.prodId,
              productType: "Product",
              name: name,
              price: price,
              actualPrice: price,
              cGst: item.cGst,
              sGst: item.sGst,
              qty: newQty.toString(),
              amount: amount.toString(),
              amountWithoutGst: amount.toString(),
              isActiveSale: saleItem.isActiveSale,
              priceTypeId: saleItem.priceTypeId,
            );
            break;
          }
        }
      }

      if (gridIndex == -1) {
        final name = item.priceType != 2 ? item.name : '${item.name}-${unit?.measurement}';
        ListUtility.saleList.add(Transactions(
          prodId: item.productId,
          productType: "Product",
          name: name,
          qty: dynamicQuantity.toString(),
          price: price,
          actualPrice: price,
          amount: price,
          amountWithoutGst: price,
          cGst: item.cGst,
          sGst: item.sGst,
          priceTypeId: unit?.priceTypeId,
        ));
      }

      await discountCommonFunction();
      priceController.clear();
      await callCalculate();
      await findTotal();
      transactionCardShow(ListUtility.saleList);
      dynamicQuantity = 1;
    }
  }

  transactionCardShow(List<Transactions> saleList) {
    transactionCardList.clear();
    for (Transactions model in saleList) {
      setState(() {
        transactionCardList.add(TransactionCardModel(
            guidId: model.prodId, name: model.name, qty: model.qty, itemPrice: model.actualPrice.toString(), totalAmount: model.amount));
      });
    }
  }

  //TEMP PAYMENT FUNCTION
  List<Payment> paymentList = [];
  getPaymentList() {
    dbOperations.queryAllPaymentDetailsBasedOnActivated().then((value) {
      setState(() {
        paymentList = value;
      });
    });
  }

  //REPEAT PRINTER FUNCTION
  _queryAllForRepeatPrinter() async {
    if (sales?.fkTableID != null) {
      List<Sales> tempReportList = [];
      tempReportList = ListUtility.reportList.where((element) => element.fkTableID != null && element.isActiveSale == 0).toList();
      ListUtility.reportList.clear();
      ListUtility.reportList = tempReportList;
    }
    if (ListUtility.reportList.length <= 0) {
      showToast("Sales not available");
    } else {
      sales = ListUtility.reportList.last;
      customerNameController.text = ListUtility.reportList.last.customerName ?? "";
      final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(sales?.salesId);
      setState(() {
        sales?.invoiceId = ListUtility.reportList.last.invoiceId;
        ListUtility.saleList.clear();
        transactionCardList.clear();
        allRows.forEach((row) => ListUtility.saleList.add(Transactions.fromMap(row)));
        transactionCardShow(ListUtility.saleList);
        for (Transactions t in ListUtility.saleList) {
          sales?.isIGST = t.isIGST != null ? t.isIGST : false;
        }
        callCalculate();
        findTotal();
      });
    }
  }

  //PRINT COUNT FUNCTION
  int printCount = 0;
  int salesLimit = 0;
  getPrinterCount() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    salesLimit = prefs.getInt("salesCount") ?? 5;
    if (prefs.get("printDate") == null) {
      prefs.setString("printDate", new DateFormat.yMd().format(DateTime.now()).toString());
    }
    if (prefs.get("printerCount") == null || prefs.get("printDate") != new DateFormat.yMd().format(DateTime.now()).toString()) {
      prefs.setInt("printerCount", 0);
    } else {
      printCount = prefs.getInt("printerCount") ?? 0;
    }
  }

  void _disconnect() {
    try {
      bluetooth.disconnect();
    } catch (e) {}
  }

  getOnlinePlatformList() async {
    List<OnlinePlatformModel> temponlinePlatformList = await dbOperations.getAllOnlinePlatformsFromDB();
    setState(() {
      onlinePlatformListList = temponlinePlatformList;
    });
  }

  getInitSalesPage() async {
    getPrinterInfo();
    getPrinterCount();
    await initSettings();

    if (saleLayout == "SaleLayout1" && sales?.salesId == null) {
      ListUtility.saleList.clear();
      transactionCardList.clear();
    }
    await assignTransactionList();
    await assignSalesPaymentMappingList();
    if (saleLayout == "SaleLayout2") {
      await discountCommonFunction();
      await callCalculate();
    }

    await findTotal();
    // sales?.salesId != null ? _queryAllLabel(sales?.salesId) : null;
  }

  commonOnTapProduct(Products product, int index) async {
    if (product.dynamicQuantity == 1) {
      if (product.priceType == 2) {
        await getAllProductUnitsDetails(product.productId ?? "");
        unitDialog(context, product, "dynamicQty");
      } else {
        await dynamicQtyDialog(context, product.toppingGroupId, product.price, product, null);
      }
    } else {
      if (product.priceType == null) {
        if (product.price == null || product.price == "") {
          AmountDialog(context, product);
        } else {
          addProductsInSalesList(product, null);
          if (product.toppingGroupId != null) {
            await queryAllProductToppingDetails(product.toppingGroupId ?? "");
            if (productToppingList.length > 0) {
              toppingDialog(context, product);
            }
          }
        }
      } else {
        if (product.priceType == 0) {
          AmountDialog(context, product);
        } else if (product.priceType == 1) {
          addProductsInSalesList(product, null);
          if (product.toppingGroupId != null) {
            await queryAllProductToppingDetails(product.toppingGroupId ?? "");
            if (productToppingList.length > 0) {
              toppingDialog(context, product);
            }
          }
        } else {
          await getAllProductUnitsDetails(product.productId ?? "");
          unitDialog(context, product, "normalQty");
        }
      }
    }
    if (MyApp.isFilterEnable) {
      prodName = "";
      productSearchController.text = "";
      prodId = "";
      filterProductList.clear();
      assignProductList();
    }
  }

  assignTransactionList() async {
    setState(() {
      ListUtility.saleList.clear();
      transactionCardList.clear();
      if (saleTransactionList != null) {
        ListUtility.saleList = List.from(saleTransactionList ?? []);
      }
    });

    await discountCommonFunction();
    await callCalculate();
    await findTotal();
    transactionCardShow(ListUtility.saleList);
  }

  assignSalesPaymentMappingList() async {
    setState(() {
      Map<String, dynamic> salesPaymentMapingValue = salesFun.assignSalesPaymentMappingList(paymentMapList ?? []);
      temPaymentMappingList.clear();
      paymentMappingList.clear();
      if (salesPaymentMapingValue.containsKey('paymentMappingList')) {
        paymentMappingList = List<SalesPaymentMapping>.from(salesPaymentMapingValue['paymentMappingList']);
        temPaymentMappingList = List<SalesPaymentMapping>.from(salesPaymentMapingValue['paymentMappingList']);
      }
      if (salesPaymentMapingValue['isComplementory'] == true) {
        isComplementory = true;
        findTotal();
      } else {
        isComplementory = false;
      }
    });
    await calculateTotalAmount();
  }

  getSalesCategory() {
    if (ListUtility.salesCategoryList.isNotEmpty) {
      salesCategoryList.clear();
      salesCategoryList.addAll(ListUtility.salesCategoryList.where((element) => element.status != 2 && element.salesCatName != null));
      dropDownList = salesCategoryList.map((e) => DropDown(id: '${e.salesCategoryId}', name: e.salesCatName)).toList();
    }
  }

  assignSalesCategory() {
    if (sales != null) {
      selectedSalesCategory = dropDownList.firstWhereOrNull(
        (element) => element.id == sales?.salesCategoryId,
      );
    }
  }

  checkComplementaryExist() {
    if (temPaymentMappingList.isNotEmpty) {
      for (var e in temPaymentMappingList) {
        e.paymentCatId = ListUtility.paymentTypeList
            .firstWhereOrNull((element) => element.paymentTypeID == e.paymentId && element.rowStatus != 2)
            ?.paymentCategoryID;
      }
      if (temPaymentMappingList[0].paymentCatId == 4) {
        return true;
      } else {
        return false;
      }
    }
  }

  @override
  void initState() {
    try {
      getInitSalesPage();
      _queryAllReports();
      getCreditDetails();
      getSalesCategory();
      assignSalesCategory();
    } on Exception catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "SalesMainPage initState()");
    }
    super.initState();
  }

  String? mainBalance;
  String? creditAmount = "0";

  getCreditDetails() async {
    if (sales?.customerID != null && saleType != 'SO' && saleType != 'T') {
      var keyExists = await balanceDB.getBalanceByCustomerID(sales?.customerID ?? "");
      var credit = await salesDetailsDB.returnCreditAmount(sales?.customerID ?? "", sales?.salesId ?? "");
      if (credit != null) {
        givenAmountController.text = credit.toString();
        creditAmount = credit.toString();
      }
      mainBalance = keyExists == null ? "0" : keyExists['CustomerBalance'];
    }
  }

  @override
  void dispose() {
    if (multiPrint == false && saleType != "SO" && saleType != "T") {
      printerUtilities.disconnect();
    }

    super.dispose();
  }

  commonSales(BuildContext context) async {
    try {
      SharedPreferences prefs = await SharedPreferences.getInstance();
      printCount++;
      prefs.setInt("printerCount", printCount);
      if (ListUtility.saleList.isNotEmpty || ((saleType == "SO" || saleType == "T") && sales?.salesId != null)) {
        if (sales?.salesId == null) {
          await startInsert(ListUtility.saleList, selectPaymentType?.paymentTypeID, "SALES", customerNameController.text);
          salesIdForReport = insertSalesId;
        } else {
          await startUpdate("SALES");
          salesIdForReport = sales?.salesId ?? "";
          await salesFun.salesPaymentMappingMethod(paymentMappingList, saleId: salesIdForReport);
          showToast("Updated successfully");
        }
        setState(() {
          ListUtility.saleList.clear();
          transactionCardList.clear();
          findTotal();
          _queryAllReports();
          customerNameController.clear();
          paymentAmountController.clear();
          selectPaymentType = null;
          selectedOnlinePlatform = null;
          selectedSalesCategory = null;
          if (saleType != "SO" && saleType != "T") {
            sales = null;
          }
        });
        temPaymentMappingList.clear();
        paymentMappingList.clear();
      }
    } catch (e) {
      throw e;
    }
  }

  Future<int?> commonSalesPrint(BuildContext context, {int printType = 0}) async {
    try {
      int? printResult;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      printCount++;
      prefs.setInt("printerCount", printCount);
      if (ListUtility.saleList.isNotEmpty) {
        await salesPrint(context, selectPaymentType?.paymentTypeID, printType: printType).then((value) async {
          if (value == 1) {
            if (printType == 1) {
              setState(() {
                ListUtility.saleList.clear();
                transactionCardList.clear();
                findTotal();
              });
            }
            printResult = value;
          }
        });
        //return result;
      }

      // } else {
      //   await salesPrint(context, selectPaymentType?.paymentTypeID)
      //       .then((value) async {
      //     if (value != null && value == 1) {
      //       Navigator.pop(context);
      //       if (successDialogOnSales && saleType != "SO" && saleType != "T") {
      //         await callSuccessDialog(context);
      //       }
      //       return value;
      //     }
      //   });
      // }
      return printResult;
    } on Exception catch (e) {
      // return 0;
      throw e;
    }
  }

  void hideValidation() {
    Future.delayed(Duration(seconds: 5), () {
      if (enableValidationMessage) {
        saveAlertSetState!(() {
          enableValidationMessage = false;
          validationText = '';
        });
      }
    });
  }

  double bswidth = 0.0;
  savePrintDialog(BuildContext context) {
    bswidth = width > tabletWidth ? width * 0.1 : width;
    returnAmount = 0.0;
    givenAmountController.clear();
    givenAmountController.text = (creditAmount != "0" ? creditAmount : "")!;
    selectPaymentType = null;
    try {
      selectCustomer = customerList.firstWhereOrNull(
        (element) => element.customerID == sales?.customerID,
      );
    } catch (ex) {
      // print(ex);
    }
    try {
      selectedOnlinePlatform = onlinePlatformListList.firstWhereOrNull((element) => element.onlinePlatformId == sales?.onlinePlatformID);
    } catch (ex) {}
    customerNameController.text = sales?.customerName ?? '';
    mobileNoController.text = sales?.mobileNo ?? "";
    onlineRefNoController.text = sales?.onlineRefNo ?? "";
    isPaymentTypeIsEmpty = false;
    isPaymentMappingListExist = false;
    isPaymentAmountEmpty = false;
    if (paymentTypeList.isNotEmpty) {
      selectPaymentType = selectPaymentType ?? paymentTypeList.first;
    }
    isIGST = sales?.isIGST ?? false;
    isOnlinePlatformIsEmpty = false;
    isSalesCategorEmpty = false;
    selectedSalesCategory = null;
    isEnableCustomerSection = false;
    isEnableDiscountSection = false;
    isEnableAdvanceSection = false;
    isOnlineRefNoIsEmpty = false;
    isAdvancePaymentEnabled = false;
    noAdvancePaymentEnabled = false;
    balanceAmount = 0.0;
    if (temPaymentMappingList.isEmpty) {
      isComplementory = false;
      findTotal();
      paymentAmountController.text = total.round().toString();
    }
    if (sales?.deliveryDateAndTime == null && sales?.deliveryDateAndTime == null) {
      deliveryDateController.clear();
      deliveryTimeController.clear();
      orderNoteController.clear();
    }

    calculateTotalAmount();
    List<Map<String, dynamic>> cardList = [
      if (isEnableAdvanceOrder && orderType == 1 && saleType != "T" && saleType != "SO") ...[
        {"name": "Pre Order", "img": preOrderSvg}
      ],
      if ((saleType != "SO" && saleType != "T") && customerNameEnable && !enableOnlineOrder) ...[
        {"name": "Customers", "img": customersSvg}
      ],
      if ((saleType != "SO" && saleType != "T") && isDiscount && !enableOnlineOrder) ...[
        {"name": "Discount", "img": discountPercentageSvg}
      ],
    ];

    return showModalBottomSheet(
        isScrollControlled: true,
        isDismissible: false,
        context: context,
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            saveAlertSetState = setState;
            return Padding(
              padding: EdgeInsets.only(
                  left: width > tabletWidth ? width * 0.1 : 0.0,
                  right: width > tabletWidth ? width * 0.1 : 0.0,
                  top: 8.0,
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    if (enableValidationMessage) ...[
                      CommonErrorValidation(
                        message: validationText,
                        onPress: () {
                          saveAlertSetState!(() {
                            enableValidationMessage = false;
                          });
                        },
                      ),
                    ],
                    Container(
                      padding: EdgeInsets.symmetric(vertical: 5.0),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16.0),
                          topRight: Radius.circular(16.0),
                        ),
                      ),
                      child: SingleChildScrollView(
                          child: Form(
                        key: validator.formkey,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 5.0),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              CommonBillingCard(
                                width: width > tabletWidth ? bswidth * 7.5 : width * 0.90,
                                cardList: cardList,
                                controller: billingCardController,
                                onTap: (index) {
                                  setState(() {
                                    if (index == 'Pre Order') {
                                      print("pre order");
                                      isEnableCustomerSection = false;
                                      isEnableDiscountSection = false;

                                      isEnableAdvanceSection = !isEnableAdvanceSection;
                                    } else if (index == 'Customers') {
                                      print("customer");
                                      isEnableDiscountSection = false;
                                      isEnableCustomerSection = !isEnableCustomerSection;
                                      isEnableAdvanceSection = false;
                                    } else if (index == 'Discount') {
                                      print("discount");
                                      isEnableDiscountSection = !isEnableDiscountSection;
                                      isEnableCustomerSection = false;
                                      isEnableAdvanceSection = false;
                                    }
                                  });
                                },
                              ),
                              if (isEnableAdvanceOrder && orderType == 1 && saleType != "T" && isEnableAdvanceSection) ...[
                                AdvanceOrderWidget(
                                    onTabCheckBox: (date, time, note) {
                                      saveAlertSetState!(() {
                                        deliveryDateController.text = date;
                                        deliveryTimeController.text = time;
                                        orderNoteController.text = note;
                                      });
                                    },
                                    dateController: deliveryDateController,
                                    timeController: deliveryTimeController,
                                    noteController: orderNoteController,
                                    isAdvanceSelected: isAdvancePaymentEnabled,
                                    paymentTypeEnabled: isPaymentType,
                                    dateOnChange: (deliveryDate) {
                                      saveAlertSetState!(() {
                                        deliveryDateController.text = deliveryDate.toString();
                                        mergeDateAndTime();
                                      });
                                    },
                                    timeOnChange: (deliveryTime) {
                                      saveAlertSetState!(() {
                                        deliveryTimeController.text = deliveryTime.toString();
                                        mergeDateAndTime();
                                      });
                                    },
                                    noteOnChange: (deliveryNote) {
                                      orderNoteController.text = deliveryNote;
                                    },
                                    isAdvancePayment: (value) {
                                      saveAlertSetState!(() {
                                        isAdvancePaymentEnabled = value;
                                      });
                                    },
                                    noAdvanceOnTab: (value) {
                                      if (value) {
                                        saveAlertSetState!(() {
                                          isAdvancePaymentEnabled = value;
                                          noAdvancePaymentEnabled = true;
                                          paymentMappingList = [];
                                          temPaymentMappingList.clear();
                                          returnAmount = 0.0;
                                          balanceAmount = 0.0;
                                          paidAmount = 0.0;
                                          returnAmount = 0;
                                        });
                                      }
                                    })
                              ] else ...[
                                const SizedBox.shrink()
                              ],
                              if (isEnableCustomerSection) ...[
                                customerSectionForBill(context),
                              ] else ...[
                                const SizedBox.shrink()
                              ],
                              if (isEnableDiscountSection) ...[
                                discountSectionForBill(),
                              ] else ...[
                                SizedBox.shrink()
                              ],
                              if ((saleType != "SO" && saleType != "T") &&
                                  isSalesCategoryEnabled &&
                                  salesCategoryList.isNotEmpty &&
                                  !enableOnlineOrder) ...[
                                SizedBox(height: 10),
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 3.0),
                                  child: salesCategoryWidget(),
                                ),
                                sizedBoxHeight5,
                                if (isSalesCategorEmpty) ...[
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                                    child: const Text(
                                      "This field is required",
                                      style: TextStyle(color: Colors.red, fontSize: 11.5),
                                    ),
                                  ),
                                  sizedBoxHeight5
                                ]
                              ],
                              if (!isAdvancePaymentEnabled) paymentSectionForBill(),
                              if (!enableOnlineOrder) ...[
                                salesDetailsSection(sales),
                                otherStateTaxSectionForBill(),
                              ],
                              SizedBox(
                                height: (saleType != "SO" && saleType != "T") && addTax && !enableOnlineOrder && !isComplementory ? 0 : 10,
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 3.0),
                                child: Row(
                                  mainAxisAlignment: saleType == "T" || saleType == "SO"
                                      ? additionalKOTButton
                                          ? MainAxisAlignment.spaceBetween
                                          : MainAxisAlignment.center
                                      : MainAxisAlignment.spaceBetween,
                                  children: [
                                    RoundedButtonWithIcon(
                                        width: saleType == "T" || saleType == "SO"
                                            ? additionalKOTButton
                                                ? width > tabletWidth
                                                    ? bswidth * 2.0
                                                    : width * 0.30
                                                : width > tabletWidth
                                                    ? bswidth * 7.5
                                                    : width * 0.93
                                            : width > tabletWidth
                                                ? bswidth * 1.75
                                                : width * 0.25,
                                        height: 70,
                                        isEnableBorder: true,
                                        buttonIcon: saveSvg,
                                        iconColor: appThemeColor,
                                        title: sales?.salesId == null ? 'Save' : "Update",
                                        onPressed: () async {
                                          okButtonOnTap();
                                        }),
                                    SizedBox(
                                      width: 5,
                                    ),
                                    if (additionalKOTButton) ...[
                                      RoundedButtonWithIcon(
                                          width: saleType == "T" || saleType == "SO"
                                              ? width > tabletWidth
                                                  ? bswidth * 5.6
                                                  : width * 0.618
                                              : width > tabletWidth
                                                  ? bswidth * 1.70
                                                  : width * 0.25,
                                          height: 70,
                                          isEnableBorder: true,
                                          title: "KOT",
                                          buttonIcon: printerSVG,
                                          iconColor: appThemeColor,
                                          onPressed: () async {
                                            kotButtonOnTap();
                                          }),
                                      SizedBox(
                                        width: saleType == "SO" || saleType == "T" ? null : 5,
                                      ),
                                    ],
                                    if (saleType != "SO" && saleType != "T")
                                      RoundedButtonWithIcon(
                                        width: additionalKOTButton
                                            ? width > tabletWidth
                                                ? bswidth * 4.20
                                                : width * 0.43
                                            : width > tabletWidth
                                                ? bswidth * 5.70
                                                : width * 0.68,
                                        height: 70,
                                        title: sales?.salesId == null ? 'Print' : "Update",
                                        buttonIcon: printerSVG,
                                        onPressed: () {
                                          okPrintButtonOnTap();
                                        },
                                      ),
                                  ],
                                ),
                              ),
                              SizedBox(
                                height: Platform.isIOS ? 30 : 15,
                              )
                            ],
                          ),
                        ),
                      )),
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }

  salesDetailsSection(Sales? sales) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 3.0),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 5.0),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.5),
              spreadRadius: 0.5,
            ),
          ],
          color: secondaryTextColor,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                if (addTax)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      if (totalCGST > 0) ...[
                        Text(
                          'CGST : ${fnUtilities.checkQtyDoubleValue(totalCGST.toStringAsFixed(2))}',
                          style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                        ),
                      ],
                      if (totalSGST > 0) ...[
                        Text(
                          'SGST : ${fnUtilities.checkQtyDoubleValue(totalSGST.toStringAsFixed(2))}',
                          style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                        )
                      ],
                    ],
                  ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Total Product : ${ListUtility.saleList.length.toString()}',
                      style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                    ),
                    Text(
                      'Total Qty : ${fnUtilities.checkQtyDoubleValue(totalQty.toString())}',
                      style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                    )
                  ],
                )
              ],
            ),
            SizedBox(
              height: 10.0,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  "Total Amount : ",
                  style: headingFontStyle2.copyWith(fontWeight: FontWeight.w500),
                ),
                Text(
                  "₹ ${fnUtilities.checkQtyDoubleValue(total.round().toString())}",
                  style: headingFontStyle2.copyWith(fontWeight: FontWeight.w500),
                ),
              ],
            ),
            saleType == "T" || saleType == "SO"
                ? SizedBox.shrink()
                : SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        if (paidAmount > 0) ...[
                          Text(
                            "Paid : ",
                            style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            "₹ ${fnUtilities.checkQtyDoubleValue(paidAmount.toStringAsFixed(2))}",
                            style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            width: 5,
                          ),
                        ],
                        if (balanceAmount > 0) ...[
                          Text(
                            "Outstanding Balance : ",
                            style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                          ),
                          Text(
                            "₹ ${fnUtilities.checkQtyDoubleValue(balanceAmount.round().toStringAsFixed(2))}",
                            style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            width: 5,
                          )
                        ],
                        if ((saleType != "SO" && saleType != "T") && isDiscount && !enableOnlineOrder) ...[
                          Row(
                            // mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (totalDiscount > 0) ...[
                                Text(
                                  "Discount : ",
                                  style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                                ),
                                Text(
                                  "₹ ${fnUtilities.checkQtyDoubleValue(totalDiscount.round().toString())}",
                                  style: black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500),
                                ),
                                SizedBox(
                                  width: 5,
                                )
                              ]
                            ],
                          ),
                        ],
                        if ((saleType != "SO" && saleType != "T") &&
                            (customerNameEnable || isReturnAmount) &&
                            !enableOnlineOrder &&
                            !isComplementory) ...[
                          isReturnAmount && returnAmount >= 1
                              ? Row(
                                  children: [
                                    Text(
                                      "Return : ",
                                      style: TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                    Text(
                                      "₹ ${fnUtilities.checkQtyDoubleValue(returnAmount.round().toString())}",
                                      style: TextStyle(fontWeight: FontWeight.bold),
                                    ),
                                  ],
                                )
                              : SizedBox.shrink()
                        ]
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  ///ALERT DIALOGUES

  //DYNAMIC Unit ALERT DIALOG
  unitDialog(BuildContext context, Products item1, String dFlag) {
    return showDialog(
        context: context,
        builder: (context) {
          return UnitMeasurementDialog(
            isBigLayout: isBigLayout,
            isSaleLayoutTwo: false,
            productUnitList: productUnitList,
            onTabItem: (value) async {
              if (!isLock) {
                isLock = true;
                if ("dynamicQty" == dFlag) {
                  Navigator.pop(context);
                  await dynamicQtyDialog(context, item1.toppingGroupId, value.measurementPrice, item1, value);
                } else if (value.measurementPrice == null || value.measurementPrice == "") {
                  Navigator.pop(context);
                  await AmountDialog(context, item1, unit: value);
                } else {
                  await addProductsInSalesList(item1, value);
                  Navigator.pop(context);
                  if (item1.toppingGroupId != null) {
                    await queryAllProductToppingDetails(item1.toppingGroupId ?? "");
                    if (productToppingList.length > 0) {
                      toppingDialog(context, item1);
                    }
                  }
                }
                isLock = false;
              }
            },
          );
        });
  }

  List<Topping> productToppingNewList = [];
  //TOPPING ALERT DIALOG
  toppingDialog(BuildContext context, Products item1) {
    return showDialog(
        context: context,
        builder: (context) {
          return ToppingDialog(
            productToppingList: productToppingList,
            productToppingNewList: productToppingNewList,
            isBigLayout: isBigLayout,
            onTabok: () {
              if (!isLock) {
                isLock = true;
                setState(() {
                  addProductsInSalesList1(item1);
                });
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  //DYNAMIC PRICE ALERT DIALOG
  AmountDialog(BuildContext context, Products item, {PriceUnitType? unit}) {
    return showDialog(
        context: context,
        builder: (context) {
          return AmountAlertDialog(
            priceController: priceController,
            onPresOk: (value) async {
              value.validate();
              if (value.validate()) {
                Navigator.pop(context);
                if (unit != null) {
                  setState(() {
                    addProductsInSalesList(item, unit);
                  });
                } else {
                  setState(() {
                    addProductsInSalesList(item, null);
                  });
                }
                if (item.toppingGroupId != null) {
                  await queryAllProductToppingDetails(item.toppingGroupId ?? "");
                  if (productToppingList.length > 0) {
                    toppingDialog(context, item);
                  }
                }
              }
            },
          );
        });
  }

  //DYNAMIC QUANTITY ALERT DIALOG
  dynamicQtyDialog(BuildContext context, String? tGroupID, String? price, Products? item, PriceUnitType? unit) {
    qtyController.text = "1";
    priceController.text = price != null && price != "" ? price : "";
    return showDialog(
        context: context,
        builder: (context) {
          return DynamicQtyDialog(
            qtyController: qtyController,
            priceController: priceController,
            onTabOK: ((value1, value2) async {
              dynamicQuantity = double.parse(value1) > 0 ? double.parse(value1) : 1;
              await addProductsInSalesList(item!, unit);
              Navigator.pop(context);
              if (tGroupID != null) {
                await queryAllProductToppingDetails(tGroupID);
                if (productToppingList.length > 0) {
                  toppingDialog(context, item);
                }
              }
              qtyController.clear();
              priceController.clear();
            }),
          );
        });
  }

  individualConformationDialog(BuildContext context1, Sales sale) {
    return showDialog(
        context: context1,
        builder: (context1) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: 'Do you want KOT ?',
                buttonNameOne: 'No',
                ontabButtonOne: () {
                  Navigator.pop(context1);
                },
                buttonNameTwo: 'Yes',
                ontabButtonTwo: () async {
                  List<Transactions> printSaleList = [];
                  printSaleList.addAll(ListUtility.saleList);
                  if (printerForKOT?.printerFormat == "Default") {
                    if (printerForKOT?.paperSize == 58) {
                      await salesprint.salesPrintKOT(sale, tableName, printerForKOT, printSaleList);
                    } else {
                      await salesprint.salesPrintKOT80MM(sale, tableName, printerForKOT, printSaleList);
                    }
                  } else {
                    if (printerForKOT?.paperSize == 58) {
                      await salesprint.salesPrintKOTTVS(sale, tableName, printerForKOT, printSaleList);
                    } else {
                      await salesprint.salesPrintKOTTvs80MM(sale, tableName, printerForKOT, printSaleList);
                    }
                  }
                  Navigator.pop(context1);
                },
              ),
            ),
          );
        }).then((value) {
      setState(() {
        ListUtility.saleList.clear();
        transactionCardList.clear();
        findTotal();
        sales = null;
      });
    });
  }

  //SALES UPDATE ALERT DIALOG
  saleUpdateDialog(BuildContext context, Transactions item, int index) {
    qtyController.text = fnUtilities.checkQtyDoubleValue(item.qty.toString());
    priceController.text = fnUtilities.checkQtyDoubleValue(item.actualPrice != "" ? item.actualPrice ?? "0" : "0");
    notesController.text = item.notes ?? "";
    return showDialog(
        context: context,
        builder: (context) {
          return SaleUpdateDialog(
            item: item,
            qtyController: qtyController,
            priceController: priceController,
            notesController: notesController,
            onPressUpdate: (item, quantity, price, notes, validator) async {
              if (!isLock) {
                isLock = true;
                if (validator.validate()) {
                  if (item.prodId == ListUtility.saleList[index].prodId &&
                      item.productType == "Product" &&
                      ListUtility.saleList[index].toppingId == null) {
                    setState(() {
                      double amount = double.parse(quantity) * double.parse(price);

                      ListUtility.saleList[index] = Transactions(
                          id: item.id,
                          transactId: item.transactId,
                          salesId: item.salesId,
                          prodId: item.prodId,
                          productType: "Product",
                          name: item.name,
                          price: price,
                          actualPrice: price,
                          qty: quantity,
                          amount: amount.toString(),
                          notes: notes,
                          isActiveSale: ListUtility.saleList[index].isActiveSale);
                      ;
                    });

                    notesController.clear();
                  } else if (item.toppingId == ListUtility.saleList[index].toppingId &&
                      item.productType == "Topping" &&
                      item.prodId == ListUtility.saleList[index].prodId) {
                    setState(() {
                      double amount = double.parse(quantity) * double.parse(price);
                      ListUtility.saleList[index] = Transactions(
                          id: item.id,
                          transactId: item.transactId,
                          salesId: item.salesId,
                          prodId: item.prodId,
                          productType: "Topping",
                          toppingId: item.toppingId,
                          name: item.name,
                          price: price,
                          actualPrice: price,
                          qty: quantity,
                          amount: amount.toString(),
                          isActiveSale: ListUtility.saleList[index].isActiveSale);
                    });
                  }
                  await discountCommonFunction();
                  await callCalculate();
                  await findTotal();
                  var saleListItem = ListUtility.saleList;
                  transactionCardShow(saleListItem);
                  Navigator.pop(context);
                  FocusScopeNode currentFocus = FocusScope.of(context);
                  if (!currentFocus.hasPrimaryFocus) {
                    currentFocus.unfocus();
                  }
                }
                isLock = false;
              }
            },
          );
        });
  }

  //SALES RESET ALERT DIALOG
  CreateAlertDialogReset(BuildContext buildContext, String msgText, String flag, String val, String? val2, String? valType, Transactions? saleItem) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: msgText,
                buttonNameOne: "No",
                ontabButtonOne: () {
                  Navigator.pop(context);
                },
                buttonNameTwo: "Yes",
                ontabButtonTwo: () async {
                  setState(() {
                    if (flag == 'Reset') {
                      ListUtility.saleList.clear();
                      transactionCardList.clear();
                      saleDiscountFormula = null;
                      saleDiscountPrice = null;
                      findTotal();
                      if (saleType != "SO" && saleType != "T") {
                        sales = null;
                      }
                      customerNameController.clear();
                      temPaymentMappingList.clear();
                      paymentMappingList.clear();
                      selectPaymentType = null;
                      selectedOnlinePlatform = null;
                    }
                    if (flag == 'Delete') {
                      setState(() {
                        if (valType == "Product") {
                          if (sales?.salesId != null) {
                            _delete(sales?.salesId, val, null, saleItem!);
                          } else {
                            String? fullTopping;
                            try {
                              fullTopping = ListUtility.saleList.firstWhere((item) => item.prodId == val && item.toppingId != null).toppingId;
                              transactionCardShow(ListUtility.saleList);
                            } catch (ex) {
                              fullTopping = null;
                            }
                            if (val2 == null) {
                              if (fullTopping == null) {
                                if (saleItem?.priceTypeId != null) {
                                  ListUtility.saleList.removeWhere((item) => item.priceTypeId == saleItem?.priceTypeId);
                                } else {
                                  ListUtility.saleList.removeWhere((item) => item.prodId == val && item.price == saleItem?.price);
                                }
                                transactionCardShow(ListUtility.saleList);
                              } else {
                                ListUtility.saleList.removeWhere((item) => item.prodId == val);
                                transactionCardShow(ListUtility.saleList);
                              }
                            } else {
                              ListUtility.saleList
                                  .removeWhere((item) => item.prodId == val && item.toppingId == val2 && item.price == saleItem?.price);
                              transactionCardShow(ListUtility.saleList);
                            }
                          }
                        } else {
                          if (sales?.salesId != null) {
                            _delete(sales?.salesId, val, val2, saleItem!);
                          } else {
                            if (val2 == null) {
                              String? fullTopping;
                              try {
                                fullTopping = ListUtility.saleList.firstWhere((item) => item.prodId == val && item.toppingId != null).toppingId;
                                transactionCardShow(ListUtility.saleList);
                              } catch (ex) {
                                fullTopping = null;
                              }
                              if (fullTopping == null) {
                                ListUtility.saleList.removeWhere((item) => item.prodId == val && item.price == saleItem?.price);
                                transactionCardShow(ListUtility.saleList);
                              } else {
                                ListUtility.saleList.removeWhere((item) => item.prodId == val);
                                transactionCardShow(ListUtility.saleList);
                              }
                            } else {
                              ListUtility.saleList
                                  .removeWhere((item) => item.prodId == val && item.toppingId == val2 && item.price == saleItem?.price);
                              transactionCardShow(ListUtility.saleList);
                            }
                          }
                        }
                      });
                    }
                  });
                  await discountCommonFunction();
                  await callCalculate();
                  await findTotal();
                  Navigator.pop(context, 'true');
                },
              ),
            ),
          );
        });
  }

  //HOLD SALES ALERT DIALOG
  holdSalesAlertDialog(BuildContext context) async {
    return showDialog(
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return HoldSalesAlertDialog(
            onTabDeleteAllIcon: () {
              if (!isLock) {
                isLock = true;
                if (ListUtility.holdReportList.length != 0) {
                  deleteHoldSalesAlertDialog(context, "Are you sure want to delete hold sales ?", "DeleteAll");
                }
                isLock = false;
              }
            },
            onTabDeleteIcon: (item) {
              if (!isLock) {
                isLock = true;
                deleteHoldSalesAlertDialog(context, "Are you sure want to delete hold sales ?", "Delete", item.salesId);
                isLock = false;
              }
            },
            onPressItem: (item) async {
              if (!isLock) {
                isLock = true;
                var salesList = await dbOperations.queryGetHoldSalesDetails(item.salesId);
                await dbOperations.deleteHoldSales(item.salesId);
                setState(() {
                  ListUtility.saleList = salesList;
                  transactionCardShow(ListUtility.saleList);
                  for (Transactions t in ListUtility.saleList) {
                    // if (t.discountPrice != null) {
                    //   t.tempPrice = (double.parse(t
                    //               .amountWithoutGst) -
                    //           double.parse(
                    //               t.discountPrice))
                    //       .toString();
                    // } else {
                    //   t.tempPrice =
                    //       t.amountWithoutGst;
                    // }
                  }
                  if (saleLayout == "SaleLayout2") {
                    saleTransactionList = ListUtility.saleList;
                  }
                  findTotal();
                });
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  //HOLD SALES NAME ALERT DIALOG
  holdSalesNameAlertDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return HoldSalesNameDialog(
            holdSalesController: holdSalesController,
            onPressOk: ((value) async {
              value.validate();
              if (value.validate()) {
                Navigator.pop(context);
                await startInsert(ListUtility.saleList, "", "HOLD", null, holdSalesController.text);
                setState(() {
                  ListUtility.saleList.clear();
                  transactionCardList.clear();
                  findTotal();
                  holdSalesController.clear();
                  customerNameController.clear();
                  sales = null;
                });
              }
            }),
          );
        });
  }

  //HOLD SALES DELETE ALERT DIALOG
  deleteHoldSalesAlertDialog(BuildContext buildContext, String msgText, String flag, [String? salesId]) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: msgText,
                buttonNameOne: "Yes",
                ontabButtonOne: () async {
                  if (!isLock) {
                    isLock = true;
                    if (flag == 'DeleteAll') {
                      setState(() {
                        ListUtility.holdReportList.clear();
                      });
                      await dbOperations.deleteAllHoldSales();
                    }
                    if (flag == 'Delete') {
                      if (salesId != null) {
                        await dbOperations.deleteHoldSales(salesId);
                        setState(() {
                          ListUtility.holdReportList.removeWhere((item) => item.salesId == salesId);
                        });
                      }
                    }
                    Navigator.pop(context, 'true');
                    Navigator.pop(context, 'true');
                    holdSalesAlertDialog(context);
                    isLock = false;
                  }
                },
                buttonNameTwo: "No",
                ontabButtonTwo: () {
                  if (!isLock) {
                    isLock = true;
                    Navigator.pop(context);
                    isLock = false;
                  }
                },
              ),
            ),
          );
        });
  }

  //HOLD SALES VIEW ALERT DIALOG
  commonDialogAlertDialog(BuildContext context, String showText) {
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (BuildContext context) {
          return InformationDialog(
            content: showText,
          );
        });
  }

  Widget discountCardWidget(BuildContext context) {
    selectedDiscountOn = selectedDiscountOn ?? "Percentage";
    // bool isDiscountEmpty = false;

    return DiscountCard(
      selectedDiscountOn: selectedDiscountOn,
      isDiscountEmpty: isDiscountEmpty,
      discountController: discountController,
      updateDiscountOption: (value) {
        if (value != null) {
          selectedDiscountOn = value;
        }
      },
      onPressItem: ((value, value1) async {
        if (!isLock) {
          //selectedDiscountOn = value1;
          isLock = true;
          if (discountController.text != "") {
            saveAlertSetState!(() {
              isDiscountEmpty = false;
            });
            double tAmount = 0.0;
            for (Transactions d in ListUtility.saleList) {
              tAmount += (double.parse(d.actualPrice ?? "0") * double.parse(d.qty ?? "0"));
              d.discountPrice = null;
              d.discountFormula = null;
            }
            String discountPrice;
            if (selectedDiscountOn == "Percentage") {
              discountPrice = (((tAmount * double.parse(value)) / 100)).toString();
            } else {
              discountPrice = value;
            }
            saleDiscountFormula = "Manual_Discount";
            saleDiscountPrice = discountPrice;
            // Navigator.pop(context);

            await callCalculate();
            await findTotal();
            saveAlertSetState!(() {
              isEnableDiscountSection = false;
            });
            billingCardController.selectDiscountCard();
          } else {
            saveAlertSetState!(() {
              isDiscountEmpty = true;
            });
          }
          isLock = false;
        }
      }),
    );
  }

  discountCouponCard(BuildContext context) async {
    List<Discounts> discountList = await discount.checkValidDateDiscount("C", list: true);

    return CouponDiscountCard(
      selectedCoupon: selectedCouponID,
      discountList: discountList,
      discountType: discountType,
      onPressItem: (item) async {
        if (!isLock) {
          isLock = true;
          for (Transactions d in ListUtility.saleList) {
            d.discountPrice = null;
            d.discountFormula = null;
          }
          if (discountType == "Product") {
            await discount.calculateDiscount("C", discount: item);
          } else {
            Discounts? discountModel = await discount.salesCommonDiscount("C", discount: item, saleID: sales?.salesId, saleNum: sales?.isActiveSale);
            saleDiscountFormula = discountModel?.formula;
            saleDiscountPrice = discountModel?.discountAmount;
          }
          selectedCouponID = item?.id ?? -1;
          // Navigator.pop(context);
          await callCalculate();
          await findTotal();
          saveAlertSetState!(() {
            isEnableDiscountSection = false;
          });
          isLock = false;
        }
      },
    );
  }

  discountCommonFunction() async {
    if (isDiscount) {
      if (saleType == "T" || saleType == "SO" || sales?.salesId == null) {
        for (Transactions d in ListUtility.saleList) {
          d.discountPrice = null;
          d.discountFormula = null;
        }
        if (discountType == "Product") {
          await discount.calculateDiscount("A");
        } else {
          Discounts? discountModel = await discount.salesCommonDiscount("A", saleID: sales?.salesId, saleNum: sales?.isActiveSale);
          saleDiscountFormula = discountModel?.formula;
          saleDiscountPrice = discountModel?.discountAmount;
        }
      } else {
        saleDiscountFormula = sales?.discountFormula;
        saleDiscountPrice = sales?.discountPrice;
      }
    }
  }

  panelSwitch() async {
    FocusScope.of(context).unfocus();
    if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Normal") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Id";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Id") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Key";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Key") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Normal";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    }
    setState(() {
      initSettings();
    });
  }

  updateNamePanelType(String panelType) async {
    /// this function written for due to name panel depriciation.
    if (panelType == "Name") {
      panels = "Normal";
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = panels;
      fnUtilities.updateDeviceSettingDetails(settingsDetail);
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    //isBigLayout = width> tabletWidth;
    isBigLayout = (width > tabletWidth && saleLayout == "DesktopLayout") || Platform.isWindows;

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        SaleModel model = SaleModel();
        model.transaction = List.from(ListUtility.saleList);
        model.fullSale = sales;
        model.saleType = saleType;
        Navigator.pop(context, model);
        return true;
      },
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        // appBar: Platform.isWindows
        //     ? BaseAppBar(
        //         title: text("",
        //             textColor: secondaryTextColor,
        //             fontSize: textSizeNormal,
        //             fontFamily: fontMedium),
        //         appBar: AppBar(),
        //         widgets: <Widget>[],
        //       )
        //     : null,
        body: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            if (Platform.isIOS) ...[
              SizedBox(
                height: 30,
              )
            ],
            Flexible(
              child: BootstrapRow(children: [
                BootstrapCol(
                  sizes: isBigLayout ? 'col-md-6 col-sm-12' : 'col-md-12 col-sm-12',
                  child: billSection(),
                ),
                BootstrapCol(
                  sizes: isBigLayout ? 'col-md-6 col-sm-12' : 'col-md-12 col-sm-12',
                  child: productSection(),
                ),
              ]),
            ),
          ],
        ),
      ),
    );
  }

  //NORMAL PANEL TYPE WIDGET
  productsWithNormalPanel() {
    return NormalPanelWidget(
      isBigLayout: isBigLayout,
      cardItemList: cardItemList,
      isSaleLayoutTwo: false,
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
      }),
      onTabAddNewItem: () {
        onTabAddNewProduct();
      },
    );
  }

  //NUMBER PANEL TYPE WIDGET
  productsWithNumberPanel() {
    return NumberPanelListWidget(
      isBigLayout: isBigLayout,
      cardItemList: cardItemList,
      isSaleLayoutTwo: false,
      itemId: prodId,
      onTabItemId: (value) {
        filterProductsById(value.toString());
      },
      ontabCancel: () {
        if (!isLock) {
          setState(() {
            prodId = "";
            filterProductsById(prodId);
          });
        }
      },
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
      }),
      onTabAddNewItem: () {
        onTabAddNewProduct();
      },
    );
  }

  //Key PANEL TYPE WIDGET
  productsWithCategoryPanel() {
    return CategoryPanelWidget(
      isBigLayout: isBigLayout,
      categoryListItems: categoryListItem,
      cardItemList: cardItemList,
      isShowImages: showImages,
      isSaleLayoutTwo: false,
      categoryBoxHeight: isBigLayout ? null : height * 0.4,
      prodKey: prodKey,
      onPressCategory: (value) {
        if (value.guidId != null) {
          ProductKey? model = productKeyList.firstWhereOrNull((model) => model.productKeyId == value.guidId);
          if (model != null) {
            filterProductsByCategory(value.guidId!, value.categoryName);
            prodKey = value.guidId!;
          }
        } else {
          filterProductsByCategory(value.guidId ?? "", value.categoryName);
        }
      },
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
      }),
      onTabListAddItems: () {
        onTabAddNewProduct();
      },
    );
  }

  Future<List<Products>> assignFilePath(List<Products> productList) async {
    for (Products p in productList) {
      if (!p.image.isEmptyOrNull) {
        p.imagePath = await fnUtilities.readFileFromLocal(p.image);
      }
    }
    return productList;
  }

  callSuccessDialog(BuildContext context, String salesId) async {
    await fnUtilities.salesSuccessDialog(context, salesId);
  }

  onTabAddNewProduct() {
    if (!isLock) {
      isLock = true;
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => AddProductDialog(
                    productName: productSearchController.text,
                  ))).then((value) async {
        await fnUtilities.getAllProductDetails();
        await assignProductList();
      });
      isLock = false;
    }
  }

  ///Bootstrap row bill section
  billSection() {
    return Container(
      height: (isBigLayout || saleLayout == "SaleLayout2") ? height : height * 0.5,
      padding: EdgeInsets.all(10.0),
      child: Column(children: [
        if (!isBigLayout) ...[
          SizedBox(
            height: 16,
          )
        ],
        Container(
          height: 50,
          color: isBigLayout ? appThemeColor : secondaryTextColor,
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  //if (isBigLayout) ...{
                  InkWell(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      padding: EdgeInsets.all(5.0),
                      width: 35,
                      child: Icon(Icons.arrow_back, color: blackColor),
                    ),
                  ),
                  //},
                  sales?.invoiceId != null
                      ? Row(
                          children: [
                            text("Ref No ",
                                textColor: isBigLayout ? secondaryTextColor : primaryTextColor,
                                fontFamily: fontBold,
                                fontSize: textSizeSMedium,
                                isCentered: true),
                            text("${sales?.invoiceId.toString()}    ",
                                textColor: isBigLayout ? secondaryTextColor : primaryTextColor,
                                fontFamily: fontBold,
                                fontSize: textSizeMedium,
                                isCentered: true),
                          ],
                        )
                      : Container(),
                  if (!isBigLayout) ...{
                    Row(
                      children: [
                        text(
                          "Total: ",
                          textColor: isBigLayout ? secondaryTextColor : primaryTextColor,
                          fontFamily: fontBold,
                          fontSize: textSizeSMedium,
                          isCentered: false,
                        ),
                        text(
                            (saleLayout == "SaleLayout1" && isDiscount && discountType == "Sales" && saleType != "T")
                                ? "Rs.${total.toStringAsFixed(2)}(${totalDiscount.toStringAsFixed(1)})"
                                : "Rs.${total.toStringAsFixed(2)}",
                            textColor: isBigLayout ? secondaryTextColor : primaryTextColor,
                            fontFamily: fontBold,
                            isCentered: true),
                      ],
                    ),
                    popupMenuButtonWidget(),
                  }
                ],
              ),
              // SizedBox(
              //   height: 5,
              // ),
            ],
          ),
        ),
        TransactionPanelWidget(
          transactionFor: "Product",
          transactionCardList: transactionCardList,
          onLongPress: (value) {
            for (Transactions model in ListUtility.saleList) {
              if (model.prodId == value) {
                CreateAlertDialogReset(
                    context, "Are you sure want to delete?", "Delete", model.prodId ?? "", model.toppingId, model.productType ?? "", model);
                break;
              }
            }
          },
          onTapPress: (item, index) {
            if (item != null) {
              for (Transactions model in ListUtility.saleList) {
                if (model.prodId == item.guidId && model.actualPrice == item.itemPrice) {
                  saleUpdateDialog(context, model, index);
                }
              }
            }
          },
          isBigLayout: isBigLayout,
        ),

        /// Buttons row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                if (!isLock) {
                  isLock = true;
                  if (ListUtility.saleList.isNotEmpty) {
                    setState(() {
                      CreateAlertDialogReset(context, "Are you sure want to reset?", 'Reset', "0", null, null, null);
                    });
                  }
                  isLock = false;
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: width >= tabletWidth ? 50 : 45,
                width: isBigLayout ? width * 0.10 : 100,
                child: text("Reset", textColor: secondaryTextColor, isCentered: true),
                decoration: boxDecoration(bgColor: ListUtility.saleList.length != 0 ? buttonThemeColor : Colors.grey, radius: 8.0),
              ),
            ),
            saleType != "SO" && saleType != "T"
                ? GestureDetector(
                    onTap: () {
                      if (!isLock) {
                        isLock = true;
                        if (ListUtility.saleList.isNotEmpty) {
                          holdSalesNameAlertDialog(context);
                        }
                        isLock = false;
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: width >= tabletWidth ? 50 : 45,
                      width: isBigLayout ? width * 0.10 : 100,
                      child: text("Hold", textColor: secondaryTextColor, isCentered: true),
                      decoration: boxDecoration(bgColor: ListUtility.saleList.length != 0 ? buttonThemeColor : Colors.grey, radius: 8.0),
                    ),
                  )
                : Container(),
            GestureDetector(
              onTap: () async {
                if (!isLock) {
                  isLock = true;
                  if ((ListUtility.saleList.length != 0) ||
                      ((saleType == "SO" || saleType == "T") && sales?.salesId != null && sales?.isActiveSale != null)) {
                    //await initSettings();
                    savePrintDialog(context);
                  }
                  isLock = false;
                }
              },
              child: Container(
                alignment: Alignment.center,
                height: width >= tabletWidth ? 50 : 45,
                width: isBigLayout ? width * 0.15 : 100,
                child: sales?.salesId == null
                    ? text((saleType == "SO" || saleType == "T") ? "Create Order" : "Create Bill", textColor: secondaryTextColor, isCentered: true)
                    : text((saleType == "SO" || saleType == "T") ? "Update Order" : "Update Bill", textColor: secondaryTextColor, isCentered: true),
                decoration: boxDecoration(
                    bgColor: (ListUtility.saleList.length != 0) ||
                            ((saleType == "SO" || saleType == "T") && sales?.salesId != null && sales?.isActiveSale != null)
                        ? buttonThemeColor
                        : Colors.grey,
                    radius: 8.0),
              ),
            ),
          ],
        ),
        individualBillInfo(),
      ]),
    );
  }

  ///Bootstrap row bill section
  productSection() {
    return Container(
      padding: EdgeInsets.all(10.0),
      height: isBigLayout ? height : height * 0.5,
      child: Column(children: [
        saleLayout != "SaleLayout2"
            ? Container(
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TextFieldBoxWidget(
                        onTapOutside: (event) {
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                        text: "Search Products",
                        mController: productSearchController,
                        onChange: filterProductsByName,
                        isPassword: false,
                        suffix: true,
                        onTabClear: () {
                          productSearchController.clear();
                          filterProductList.clear();
                          assignProductList();
                        },
                      ),
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        favouriteProductIconWidget(),
                        SizedBox(
                          width: 8,
                        ),
                        panelSwitchIconWidget(),
                        if (isBigLayout) ...{
                          SizedBox(
                            width: 8,
                          ),
                          Container(
                            alignment: Alignment.center,
                            child: popupMenuButtonWidget(),
                          ),
                        }
                      ],
                    ),
                  ],
                ),
              )
            : Container(),
        SizedBox(
          height: saleLayout != "SaleLayout2" ? 5 : 0,
        ),
        saleLayout != "SaleLayout2"
            ? Expanded(
                child: !isProductLoading
                    ? Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          panels == "Normal" ? productsWithNormalPanel() : Container(),
                          panels == "Id" ? productsWithNumberPanel() : Container(),
                          panels == "Key" ? productsWithCategoryPanel() : Container(),
                        ],
                      )
                    : Center(child: CircularProgressIndicator()))
            : Container(),
      ]),
    );
  }

  /// individual bill info

  individualBillInfo() {
    if (saleLayout == "SaleLayout2" || isBigLayout) {
      return Padding(
        padding: const EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
        child: Container(
          width: width > tabletWidth ? width * 0.48 : width * 0.92,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            mainAxisSize: MainAxisSize.min,
            children: [
              Column(
                children: [
                  if (!isBigLayout) ...{
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        InkWell(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              SaleModel model = SaleModel();
                              model.transaction = List.from(ListUtility.saleList);
                              model.fullSale = sales;
                              model.saleType = saleType;
                              Navigator.pop(context, model);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.add,
                                  color: secondaryTextColor,
                                ),
                                text("New Items", textColor: secondaryTextColor, isCentered: true),
                              ],
                            ),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  },
                  Column(
                    children: [
                      /// Total Qty and Total Item
                      if (width > tabletWidth) ...{
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              child: totalQtyWidget(),
                            ),
                            Container(child: totalItemsWidget()),
                          ],
                        )
                      } else ...{
                        Container(
                          child: totalQtyWidget(),
                        ),
                        Container(child: totalItemsWidget()),
                      },
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          text("Total Rs", textColor: textPrimaryColor, fontFamily: fontBold, fontSize: textSizeMedium),
                          text(totalWithoutGST.toStringAsFixed(2), textColor: textPrimaryColor, fontFamily: fontBold, fontSize: textSizeMedium),
                        ],
                      ),

                      isDiscount && saleType != "T"
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                text("Total Discount", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                text(totalDiscount.toStringAsFixed(2), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                              ],
                            )
                          : Container(),
                      addTax
                          ? Container(
                              child: Column(
                                children: [
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      text("Total CGST", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                      text(totalCGST.toStringAsFixed(2), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ],
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      text("Total SGST", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                      text(totalSGST.toStringAsFixed(2), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ],
                                  ),
                                ],
                              ),
                            )
                          : Container(),
                      Container(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            text("Grand Total", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge),
                            SizedBox(
                              width: 10,
                            ),
                            text(total.toStringAsFixed(2), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge),
                          ],
                        ),
                      ),
                    ],
                  )
                ],
              ),
              SizedBox(
                height: height * 0.2,
              ),
            ],
          ),
        ),
      );
    } else {
      return SizedBox();
    }
  }

  getPopupMenuList() {
    List<PopupMenuEntry<dynamic>> items = [];
    if (saleLayout != "SaleLayout2") {
      if (PermissionFunctions.checkPermission(104, viewToast: false)) {
        items.add(PopupMenuItem(
          child: ListTile(
            title: Text("New Product"),
            leading: Icon(Icons.add, size: 20),
            onTap: () {
              if (!isLock) {
                isLock = true;
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog())).then((value) async {
                  if (value ?? false) {
                    await fnUtilities.getAllProductDetails();
                    await assignProductList();
                  }
                });
                isLock = false;
              }
            },
          ),
        ));
      }
      items.add(PopupMenuItem(
        child: ListTile(
          title: Text("Switch Panel"),
          leading: Icon(
            Icons.swipe_outlined,
            size: 20,
          ),
          onTap: () async {
            if (!isLock) {
              isLock = true;
              panelSwitch();
              Navigator.pop(context);
              isLock = false;
            }
          },
        ),
      ));
    }
    if (saleType != "SO" && saleType != "T") {
      items.add(PopupMenuItem(
        child: ListTile(
          title: Text("Repeat Printer"),
          leading: Icon(
            Icons.repeat,
            size: 20,
          ),
          onTap: () async {
            if (!isLock) {
              isLock = true;
              Navigator.pop(context);
              await _queryAllForRepeatPrinter();
              isLock = false;
            }
          },
        ),
      ));

      items.add(PopupMenuItem(
        child: ListTile(
          title: Text("Hold Sales"),
          leading: Icon(
            Icons.pause_sharp,
            size: 20,
          ),
          onTap: () async {
            if (!isLock) {
              isLock = true;
              Navigator.pop(context);
              ListUtility.holdReportList = await dbOperations.queryAllHoldReports();
              await dbOperations.queryAllHoldTransactions();
              if (ListUtility.holdReportList.length != 0) {
                holdSalesAlertDialog(context);
              } else {
                commonDialogAlertDialog(context, "There is no hold sales available!");
              }
              isLock = false;
            }
          },
        ),
      ));
    }
    return items;
  }

  /// popup menu button widget
  popupMenuButtonWidget() {
    if (!(saleLayout == "SaleLayout2" && (saleType == "SO" || saleType == "T"))) {
      return PopupMenuButton(
        // padding: EdgeInsets.only(top: 20, bottom: 10, left: 0.0),
        color: secondaryTextColor,
        itemBuilder: (BuildContext context) {
          return getPopupMenuList();
        },
      );
    } else {
      return SizedBox();
    }
  }

  /// favourite product icon widget
  favouriteProductIconWidget() {
    return InkWell(
      onTap: () async {
        WorkspaceSettings settings = WorkspaceSettings();
        settings.settingKey = "FavoriteProduct";
        setState(() {
          if (isFavorite) {
            isFavorite = false;
            settings.settingValue = "0";
            fnUtilities.updateDeviceSettingDetails(settings);
          } else {
            isFavorite = true;
            settings.settingValue = "1";
            fnUtilities.updateDeviceSettingDetails(settings);
          }
        });
        if (!isLock) {
          isLock = true;
          await fnUtilities.getAllProductDetails();
          assignProductList();
          // filterProductList.clear();
          // filterProductList.addAll(ListUtility.productList);
          // filterProductList.add(Products());
          isLock = false;
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(5.0),
        width: 40,
        child: isFavorite
            ? Icon(
                Icons.favorite_sharp,
                color: buttonThemeColor,
              )
            : Icon(Icons.favorite_outline),
      ),
    );
  }

  /// panel swith widget
  panelSwitchIconWidget() {
    return InkWell(
      onTap: () {
        if (!isLock) {
          isLock = true;
          panelSwitch();
          isLock = false;
        }
      },
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.all(5.0),
        width: 40,
        child: Icon(Icons.swipe_outlined),
      ),
    );
  }

  /// total items widget
  totalItemsWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        text("Total Items : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
        text(ListUtility.saleList.length.toString(), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
      ],
    );
  }

  totalQtyWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        text("Total Qty : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
        text(fnUtilities.checkQtyDoubleValue(totalQty.toString()), textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
      ],
    );
  }

  Widget discountSectionForBill() {
    Widget _discountCouponWidget(BuildContext context) {
      return FutureBuilder(
        future: discountCouponCard(context),
        builder: (BuildContext context, AsyncSnapshot<dynamic> snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            // Check if the future completed successfully
            if (snapshot.hasData) {
              // Return the widget built by the future
              return snapshot.data;
            } else {
              // Handle the case when future returns no data
              return Container(); // or any other default widget
            }
          } else {
            // Handle the case when future is still loading
            return CircularProgressIndicator();
          }
        },
      );
    }

    if ((saleType != "SO" && saleType != "T") && isDiscount && !enableOnlineOrder) {
      return CommonTabBarSection(
        height: discountSelectedIndex != 1 ? 185 : 190,
        tabOneName: "Manual",
        tabTwoName: "Coupon",
        initialTabIndex: discountSelectedIndex,
        onChangeTabEvent: (index) {
          saveAlertSetState!(() {
            discountSelectedIndex = index;
          });
        },
        tab1: discountCardWidget(context),
        tab2: _discountCouponWidget(context),
      );
    } else {
      return Container();
    }
  }

  Widget customerSectionForBill(BuildContext context) {
    bool isGuestEnable = false;

    _chooseCustomerFunction() async {
      if (!isLock) {
        isLock = true;
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => CustomerPage(
                      pageType: "Sale",
                    ))).then((value) async {
          await getCustomersDetails();
          saveAlertSetState!(() {
            try {
              if (value.customerID == null) {
                if (sales != null) {
                  selectCustomer = customerList.singleWhere((item) => item.customerID == sales?.customerID, orElse: () => Customer());
                  customerNameController.text = selectCustomer!.firstName! + " " + selectCustomer!.lastName!;
                  mobileNoController.text = selectCustomer!.mobileNo!;
                }
              } else {
                selectCustomer = value;
                customerNameController.text = selectCustomer!.firstName! + " " + selectCustomer!.lastName!;
                mobileNoController.text = selectCustomer!.mobileNo!;
                isGuestEnable = true;
              }
            } catch (e) {
              selectCustomer = null;
            }
          });
        });
        isLock = false;
      }
    }

    if ((saleType != "SO" && saleType != "T") && customerNameEnable && !enableOnlineOrder) {
      return CustomerWidget(
        mobileNoController: mobileNoController,
        customerNameController: customerNameController,
        controller: _customerController,
        customerFocusNode: customerFocusNode,
        mobileNumberFocusNode: customerMobileNumberFocusNode,
        enableMobileNumberFocuse: enableCustomerDetailsFocuse,
        onChageNumber: (number) {
          mobileNoController.text = number;
        },
        onChageName: (name) {
          customerNameController.text = name;
        },
        customerList: customerList,
        onTabSelectCustomer: (value) {
          saveAlertSetState!(() {
            try {
              if (value.customerID == null) {
                if (sales != null) {
                  selectCustomer = customerList.singleWhere((item) => item.customerID == sales?.customerID, orElse: () => Customer());
                  customerNameController.text = selectCustomer!.firstName! + " " + selectCustomer!.lastName!;
                  mobileNoController.text = selectCustomer!.mobileNo!;
                }
              } else {
                selectCustomer = value;
                customerNameController.text = selectCustomer!.firstName! + " " + selectCustomer!.lastName!;
                mobileNoController.text = selectCustomer!.mobileNo!;
                isGuestEnable = true;
              }
            } catch (e) {
              selectCustomer = null;
            }
          });
        },
        onPressedChooseCustomer: () async {
          await _chooseCustomerFunction();
        },
      );
    } else {
      return Container();
    }
  }

  ///Sales category widget
  Widget salesCategoryWidget() {
    return CommonDropDown(
      hintText: 'Select sales category',
      dropDownList: dropDownList,
      borderColor: Colors.grey.withOpacity(0.5),
      dropDown: selectedSalesCategory,
      isExpanded: true,
      isDense: true,
      canvasColor: Colors.white,
      onChanged: (DropDown? newValue) {
        saveAlertSetState!(() {
          FocusScope.of(context).requestFocus(FocusNode());
          selectedSalesCategory = newValue;
          isSalesCategorEmpty = false;
        });
      },
    );
  }

  _showPaymentTypeAlertDialog(BuildContext context) {
    // selectPaymentType = selectPaymentType ?? paymentTypeList[0];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return PaymentTypeAlertDialog(
          paymentTypeList: paymentTypeList,
          selectedRadioPaymentType: selectPaymentType,
        );
      },
    ).then((val) {
      if (val != null) {
        selectPaymentType = val;
        if (val?.paymentCategoryID == 4 && temPaymentMappingList.length > 0) {
          showToast("Remove other payments");
        } else {
          saveAlertSetState!(() {
            var isComplementoryExist = checkComplementaryExist();
            if (isComplementoryExist ?? false) {
              showToast("Remove complementary payments");
            } else {
              if (val?.paymentCategoryID == 4) {
                isComplementory = true;
                selectPaymentType = val;
                findTotal();
                paymentAmountController.text = total.round().toString();
              } else {
                isComplementory = false;
                isPaymentTypeIsEmpty = false;
                FocusScope.of(context).requestFocus(FocusNode());
                selectPaymentType = val;
                findTotal();
              }
            }
          });
        }
      }
    });
  }

  Widget paymentSectionForBill() {
    isPaymentMappingListExist = false;
    bswidth = width > tabletWidth ? width * 0.1 : width;

    if (saleType != "SO" && saleType != "T") {
      if (enableOnlineOrder) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            sizedBoxHeight5,
            Container(
              // width: width * 0.7,
              child: InputDecorator(
                decoration: InputDecoration(
                  contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 1.0),
                  labelText: 'Order From',
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                ),
                child: onlinePlatformListList.length != 0
                    ? DropdownButtonHideUnderline(
                        child: DropdownButton<OnlinePlatformModel?>(
                          hint: Text("Order From"),
                          value: selectedOnlinePlatform,
                          items: onlinePlatformListList.map((OnlinePlatformModel? value) {
                            return DropdownMenuItem<OnlinePlatformModel?>(
                              value: value,
                              child: Text(value?.onlinePlatformName ?? ""),
                            );
                          }).toList(),
                          onChanged: (OnlinePlatformModel? val) async {
                            if (val != null)
                              saveAlertSetState!(() {
                                selectedOnlinePlatform = val;
                                FocusScope.of(context).requestFocus(FocusNode());
                              });
                          },
                        ),
                      )
                    : Container(),
              ),
            ),
            isOnlinePlatformIsEmpty
                ? Container(
                    // width: isBigLayout ? width * 0.2 : width * 0.3,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: const Text(
                      "This field is required",
                      style: TextStyle(color: Colors.red, fontSize: 11.5),
                    ),
                  )
                : Container(),
            SizedBox(
              height: 10,
            ),
            Container(
              // width: width * 0.8,
              child: EditText(
                mController: onlineRefNoController,
                inputType: TextInputType.text,
                isPassword: false,
                text: "Reference Number",
              ),
            ),
            isOnlineRefNoIsEmpty
                ? Container(
                    // width: isBigLayout ? width * 0.2 : width * 0.3,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: const Text(
                      "This field is required",
                      style: TextStyle(color: Colors.red, fontSize: 11.5),
                    ),
                  )
                : Container(),
            SizedBox(
              height: 10,
            ),
          ],
        );
      } else if (isPaymentType) {
        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 3.0),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              sizedBoxHeight5,
              sizedBoxHeight5,
              Row(
                mainAxisAlignment: selectPaymentType?.paymentCategoryID == 4 ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  if (!isComplementory) ...[
                    Expanded(
                      child: TextFieldBoxWidget(
                        text: "₹ ${selectPaymentType?.paymentName ?? ''}",
                        isPassword: false,
                        mController: paymentAmountController,
                        onTapOutside: (event) {
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                        inputFormate: [
                          FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                        ],
                        inputType: TextInputType.number,
                        inputAction: TextInputAction.done,
                      ),
                    ),
                    SizedBox(
                      width: 5,
                    )
                  ],
                  SizedBox(
                    height: 50.0,
                    child: RoundedButton(
                      width: selectPaymentType?.paymentCategoryID == 4
                          ? width > tabletWidth
                              ? bswidth * 7.75
                              : width * 0.945
                          : width > tabletWidth
                              ? bswidth * 2.50
                              : width * 0.3,
                      title: ' Payment Type ',
                      onPressed: () {
                        _showPaymentTypeAlertDialog(context);
                      },
                      buttonTextStyle: width > tabletWidth ? headingTextStyle3 : black16w500.copyWith(color: secondaryTextColor),
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  if (!isComplementory) ...[
                    RoundedButton(
                      height: 50.0,
                      width: width > tabletWidth ? width * 0.15 : bswidth * 0.18,
                      onPressed: () {
                        checkValidationForPayment();
                      },
                      title: 'Split',
                      buttonTextStyle: width > tabletWidth ? headingTextStyle3 : black16w500.copyWith(color: secondaryTextColor),
                    )
                  ]
                ],
              ),
              SizedBox(
                height: 8.0,
              ),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  isPaymentAmountEmpty
                      ? Container(
                          width: width > tabletWidth ? bswidth * 3.5 : width * 0.45,
                          alignment: Alignment.centerLeft,
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                          child: const Text(
                            "This field is required",
                            style: TextStyle(color: Colors.red, fontSize: 11.5),
                          ),
                        )
                      : SizedBox(
                          width: isBigLayout ? width * 0.20 : width * 0.40,
                        ),
                  isPaymentTypeIsEmpty
                      ? Container(
                          width: width > tabletWidth ? bswidth * 2.30 : width * 0.35,
                          padding: const EdgeInsets.symmetric(
                            vertical: 4,
                          ),
                          child: const Text(
                            "This field is required",
                            style: TextStyle(color: Colors.red, fontSize: 11.5),
                          ),
                        )
                      : SizedBox.shrink(),
                  SizedBox(
                    width: width > tabletWidth
                        ? bswidth * 1.40
                        : isPaymentTypeIsEmpty
                            ? width * 0.08
                            : width * 0.1,
                  )
                ],
              ),
              isPaymentAmountEmpty || isPaymentTypeIsEmpty ? sizedBoxHeight5 : SizedBox.shrink(),
              isPaymentMappingListExist
                  ? Container(
                      alignment: Alignment.centerLeft,
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                      child: const Text(
                        "Atleast add one payment type using the add button",
                        style: TextStyle(color: Colors.red, fontSize: 11.5),
                      ),
                    )
                  : SizedBox.shrink(),
              Row(
                // mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (temPaymentMappingList.length != 0) ...[
                    Expanded(
                      // width: isBigLayout ? width * 0.40 : width * 0.633,

                      ///   units list widget
                      child: paymentListWidget(context),
                    ),
                  ] else ...[
                    SizedBox.shrink(),
                  ]
                ],
              ),
              // Text(selectPaymentType?.paymentName ?? ''),
            ],
          ),
        );
      } else {
        return Container();
      }
    } else {
      return Container();
    }
  }

  Widget returnAmountSectionForBill() {
    if ((saleType != "SO" && saleType != "T") && (customerNameEnable || isReturnAmount) && !enableOnlineOrder && !isComplementory) {
      return isReturnAmount && returnAmount > 0
          ? Row(
              children: [
                Text(
                  "Return : ",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  "₹ ${returnAmount.toStringAsFixed(2)}",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            )
          : SizedBox.shrink();
    } else {
      return SizedBox();
    }
  }

  Widget otherStateTaxSectionForBill() {
    if ((saleType != "SO" && saleType != "T") && addTax && !enableOnlineOrder && !isComplementory) {
      return CheckboxListTile(
          dense: true,
          controlAffinity: ListTileControlAffinity.leading,
          contentPadding: EdgeInsets.zero,
          value: isIGST,
          title: text("Other state tax", textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isLongText: true),
          onChanged: (bool? newValue) {
            if (!isLock) {
              isLock = true;
              saveAlertSetState!(() {
                isIGST = newValue ?? false;
              });
              isLock = false;
            }
          });
    } else {
      return SizedBox();
    }
  }

  ///check sales type
  bool checkSalesType() {
    if ((saleType == "SO" || saleType == "T")) {
      return true;
    }
    return true;
  }

  ///check payment type
  Future<bool> checkPaymentType() async {
    if (isPaymentType && !enableOnlineOrder && (saleType != "SO" && saleType != "T")) {
      if (selectPaymentType != null && paymentAmountController.text.isNotEmpty && !noAdvancePaymentEnabled) {
        await addPaymentMapping(selectPaymentType, paymentAmountController.text, index: paymentMappingIndex, status: 0);
        saveAlertSetState!(() {
          if (paymentTypeList.isNotEmpty) {
            selectPaymentType = paymentTypeList.first;
          }
        });
        return true;
      } else if (temPaymentMappingList.isNotEmpty) {
        return true;
      } else {
        if (isExpandAdvanceOrderWidget) {
          return true;
        } else {
          return false;
        }
      }
    } else {
      return true;
    }
  }

  ///check online order
  bool checkOnlineOrder() {
    if (enableOnlineOrder) {
      if (selectedOnlinePlatform != null) {
        if (onlineRefNoController.text != "") {
          return true;
        } else {
          return false;
        }
      } else {
        return false;
      }
    } else {
      return true;
    }
  }

  bool checkSalesCategory() {
    if (!isSalesCategoryEnabled ||
        selectedSalesCategory != null ||
        (saleType == "SO" || saleType == "T") ||
        salesCategoryList.isEmpty ||
        enableOnlineOrder) {
      return true;
    } else {
      return false;
    }
  }

  String? phoneNumberValidation;
// Call setOnFocusMobileNumberField with a function that focuses on the mobile number field

  bool checkCustomerMandetory() {
    enableCustomerDetailsFocuse = false;
    String? validateMobile(String? values) {
      // Indian Mobile number are of 10 digits only
      String pattern = r'^[0-9]{10}$';
      RegExp regExp = RegExp(pattern);
      if (values == null || values.isEmpty) {
        return 'Please enter the mobile number';
      } else if (values.length != 10) {
        return 'Mobile number must be of 10 digits';
      } else if (!regExp.hasMatch(values)) {
        return 'Enter valid mobile number';
      } else {
        return null;
      }
    }

    if (isExpandAdvanceOrderWidget && isEnableAdvanceOrder) {
      customerNameMandatory = true;
    }

    if (customerNameMandatory) {
      String? mobileValidationResult = validateMobile(mobileNoController.text);

      if (customerNameController.text.isEmpty) {
        saveAlertSetState!(() {
          isEnableDiscountSection = false;
          isEnableCustomerSection = true;
          isEnableAdvanceSection = false;
          billingCardController.selectCard();

          _customerController.focusCustomerField();
          phoneNumberValidation = 'Customer name is required';
        });
        return false;
      } else if (mobileValidationResult != null) {
        saveAlertSetState!(() {
          isEnableDiscountSection = false;
          isEnableCustomerSection = true;
          isEnableAdvanceSection = false;
          enableCustomerDetailsFocuse = true;
          billingCardController.selectCard();

          _customerController.focusMobileNumberField();
          phoneNumberValidation = mobileValidationResult;
        });
        return false;
      } else {
        phoneNumberValidation = null;
        return true;
      }
    } else {
      return true;
    }
  }

  checkValidationForPreOrder() {
    if (isEnableAdvanceOrder) {
      if (isExpandAdvanceOrderWidget) {
        if (deliveryDateController.text.isEmpty) {
          isEnableDiscountSection = false;
          isEnableCustomerSection = false;
          isEnableAdvanceSection = true;
          billingCardController.selectPreOrderCard();

          return false;
        } else if (deliveryTimeController.text.isEmpty) {
          isEnableDiscountSection = false;
          isEnableCustomerSection = false;
          isEnableAdvanceSection = true;
          billingCardController.selectPreOrderCard();
          return false;
        } else {
          return true;
        }
      } else {
        return true;
      }
    } else {
      return true;
    }
  }

  Future<bool> checkPayments() async {
    if (temPaymentMappingList.isEmpty) {
      return true;
    } else if (isEnableAdvanceOrder) {
      return true;
    } else {
      int totalPaidAmount;
      totalPaidAmount = temPaymentMappingList.fold(0, (total, element) => (total + element.amount!).round());
      if (totalPaidAmount >= total.round()) {
        return true;
      } else {
        bool paymetAlertValue =
            await alertWidget.paymentAlertWidgets(context, "Sales bill payment not completed . Do you want to continue?") ?? false;
        return paymetAlertValue;
      }
    }
  }

  Future<bool> checkBillStartCondition() async {
    validator.validate();
    if (deliveryDateController.text.isNotEmpty) {
      isExpandAdvanceOrderWidget = true;
    } else {
      isExpandAdvanceOrderWidget = false;
    }
    if (deliveryTimeController.text.isNotEmpty) {
      isExpandAdvanceOrderWidget = true;
    } else {
      isExpandAdvanceOrderWidget = false;
    }
    var isSalesType = checkSalesType();
    var isPayment = await checkPaymentType();
    var isSalesCategory = checkSalesCategory();
    var isOnlineOrder = checkOnlineOrder();
    var isPaymentValid = await checkPayments();
    var customerMandetory = saleType != "SO" && saleType != "T" && !enableOnlineOrder ? checkCustomerMandetory() : true;
    var checkPreOrder = saleType != "SO" && saleType != "T" && !enableOnlineOrder ? checkValidationForPreOrder() : true;

    if (isSalesCategory && isPayment && isOnlineOrder && isSalesType && customerMandetory && checkPreOrder && isPaymentValid) {
      return true;
    } else {
      if (!isSalesCategory) {
        saveAlertSetState!(() {
          isSalesCategorEmpty = true;
        });
      }
      if (!isPayment) {
        saveAlertSetState!(() {
          isPaymentTypeIsEmpty = true;
        });
      }
      if (!isOnlineOrder) {
        saveAlertSetState!(() {
          isOnlinePlatformIsEmpty = selectedOnlinePlatform == null ? true : false;
          isOnlineRefNoIsEmpty = onlineRefNoController.text == "" ? true : false;
        });
      }
      if (!customerMandetory) {
        saveAlertSetState!(() {
          enableValidationMessage = true;
          validationText = phoneNumberValidation ?? '';

          hideValidation();
          // phoneNumberValidation = false;
        });
      }
      if (!checkPreOrder) {
        saveAlertSetState!(() {
          enableValidationMessage = true;
          validationText = 'Please enter pre order details';
          hideValidation();
        });
      }
      return false;
    }
  }

  Widget paymentListWidget(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 5.0),
      child: SingleChildScrollView(
        child: ListView.builder(
            itemCount: temPaymentMappingList.length,
            scrollDirection: Axis.vertical,
            physics: ScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (BuildContext context, int index) {
              var item = temPaymentMappingList[index];
              return temPaymentMappingList[index].status != 2
                  ? Container(
                      height: 40,
                      decoration: BoxDecoration(
                        border: Border(
                          bottom: BorderSide(
                            color: Colors.grey,
                            width: 0.5,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          Expanded(
                            // color: blueViolet,
                            // width: width * 0.1571,
                            // width: width > tabletWidth ? width * 0.15 : width * 0.22,
                            child: text(item.paymentName ?? "", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                          ),
                          Expanded(
                            // width: width > tabletWidth ? width * 0.16 : width * 0.22,
                            child: text('₹' + fnUtilities.checkQtyDoubleValue(item.amount.toString()),
                                textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                          ),
                          Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  if (!isLock) {
                                    isLock = true;

                                    saveAlertSetState!(() {
                                      for (PaymentType model in paymentTypeList) {
                                        if (model.paymentTypeID == item.paymentId) {
                                          paymentAmountController.text = temPaymentMappingList[index].amount!.toStringAsFixed(0);
                                          selectPaymentType = model;
                                          paymentMappingIndex = index;
                                          isUpdate = true;
                                        }
                                      }
                                    });
                                    FocusScope.of(context).requestFocus(FocusNode());

                                    isLock = false;
                                  }
                                },
                                child: SvgPicture.asset(
                                  paymentEditIcon,
                                  height: 25,
                                ),
                              ),
                              SizedBox(
                                width: 20,
                              ),
                              InkWell(
                                  onTap: () {
                                    saveAlertSetState!(() {
                                      if (!isLock) {
                                        isLock = true;
                                        paymentMappingIndex = index;
                                        addPaymentMapping(
                                          selectPaymentType,
                                          paymentAmountController.text,
                                          index: paymentMappingIndex,
                                          status: 2,
                                        );
                                        FocusScope.of(context).requestFocus(FocusNode());

                                        isLock = false;
                                      }
                                    });
                                  },
                                  child: SvgPicture.asset(
                                    paymentDeleteicon,
                                    height: 25,
                                  )),
                            ],
                          )
                        ],
                      ),
                    )
                  : SizedBox.shrink();
            }),
      ),
    );
  }

  addPaymentMapping(PaymentType? paymentType, String paymentAmount, {status = 0, index = 0}) async {
    paymentMappingList = await salesFun.salesPaymentMapping(paymentType, paymentMappingList, status, index, paymentAmount);
    saveAlertSetState!(() {
      temPaymentMappingList = paymentMappingList.where((element) => element.status != 2).toList();
    });
    paymentMappingIndex = -1;
    saveAlertSetState!(() {
      isPaymentMappingListExist = false;
      isPaymentAmountEmpty = false;
      isPaymentTypeIsEmpty = false;
      paymentAmountController.clear();
    });
    if (paymentTypeList.isNotEmpty) {
      selectPaymentType = paymentTypeList.first;
    }
    calculateTotalAmount();
    FocusScope.of(context).requestFocus(FocusNode());
  }

  calculateTotalAmount() {
    double totalPayedAmount = 0;
    if (temPaymentMappingList.isNotEmpty) {
      for (int i = 0; i < temPaymentMappingList.length; i++) {
        totalPayedAmount = totalPayedAmount + temPaymentMappingList[i].amount!;
      }
      setState(() {
        returnAmount = totalPayedAmount - total;
        paidAmount = totalPayedAmount;
        balanceAmount = total - totalPayedAmount;
      });
    }
    if (temPaymentMappingList.isEmpty) {
      setState(() {
        returnAmount = 0.0;
        balanceAmount = 0.0;
        paidAmount = 0.0;
      });
    }
    FocusScope.of(context).requestFocus(FocusNode());
  }

  checkValidationForPayment() {
    if (paymentAmountController.text.isNotEmpty || selectPaymentType != null) {
      if (selectPaymentType?.paymentTypeID != null) {
        if (paymentAmountController.text.isNotEmpty) {
          saveAlertSetState!(() {
            isPaymentTypeIsEmpty = false;
            isPaymentAmountEmpty = false;
            if (paymentMappingIndex == -1) {
              addPaymentMapping(selectPaymentType!, paymentAmountController.text, index: paymentMappingIndex, status: 0);
              paymentAmountController.clear();
            } else {
              addPaymentMapping(selectPaymentType!, paymentAmountController.text, index: paymentMappingIndex, status: 1);
            }
            FocusScope.of(context).requestFocus(FocusNode());
          });
        } else {
          saveAlertSetState!(() {
            isPaymentAmountEmpty = true;
            isPaymentTypeIsEmpty = false;
          });
        }
      } else {
        saveAlertSetState!(() {
          isPaymentTypeIsEmpty = true;
          isPaymentAmountEmpty = false;
        });
      }
    } else {
      saveAlertSetState!(() {
        isPaymentTypeIsEmpty = true;
        isPaymentAmountEmpty = true;
        FocusScope.of(context).requestFocus(FocusNode());
      });
    }
  }

  okButtonOnTap() async {
    try {
      if ((ListUtility.saleList.length != 0) || ((saleType == "SO" || saleType == "T") && sales?.salesId != null)) {
        if (await checkBillStartCondition()) {
          if (isStoreCustomerDetails && selectCustomer == null) {
            var customerMapValue = await customerDetailsHandler.addCustomerDetails(customerNameController.text.trim(), mobileNoController.text);
            if (customerMapValue.isNotEmpty) {
              customerId = customerMapValue['customerId'];
              customerName = customerMapValue['customerName'];
              mobileNoController.text = customerMapValue['customerMobile'];
            }
          }
          if (validator.validate()) {
            if (!isLock) {
              isLock = true;

              if (printCount < salesLimit) {
                await commonSales(context);
              } else if (MyApp.expiryDate != null) {
                if (MyApp.expiryDate!.difference(DateTime.now()).inDays > 0) {
                  await commonSales(context);
                } else {
                  showToast("You have limited print, you need to activate the subscription for more print");
                }
              }
              Navigator.pop(context);
              if (saleLayout == "SaleLayout1" && saleType != "SO" && saleType != "T" && successDialogOnSales!) {
                await callSuccessDialog(context, salesIdForReport ?? "");
              }
              if (((saleLayout == "SaleLayout2" && saleType != "SO" && saleType != "T"))) {
                SaleModel model = SaleModel();
                model.isCompletedSales = true;
                model.fullSale = Sales();
                model.fullSale?.salesId = salesIdForReport;
                Navigator.pop(context, model);
                //Navigator.pop(context);
              }
              if (((saleLayout == "SaleLayout2") && (saleType == "SO" || saleType == "T"))) {
                Navigator.pop(context);
                paymentAmountController.clear();
              }
              if (saleType == "SO" || saleType == "T") {
                if (saleType == "T" || (saleType == "SO" && sales?.salesId != null)) {
                  Navigator.pop(context);
                  paymentAmountController.clear();
                }
                Navigator.pushReplacement(
                    context, MaterialPageRoute(builder: (context) => saleType == "SO" ? TableOrderPage("SO") : TableOrderPage("T")));
              }
              paymentAmountController.clear();
              isLock = false;
            }
          }
        }
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "okButtonOnTap() - SalesMainPage");
      throw ex;
    }
  }

  okPrintButtonOnTap() async {
    try {
      if (await checkBillStartCondition()) {
        // validator.validate();
        if (validator.validate()) {
          if (isStoreCustomerDetails && selectCustomer == null) {
            var customerMapValue = await customerDetailsHandler.addCustomerDetails(customerNameController.text, mobileNoController.text);
            if (customerMapValue.isNotEmpty) {
              customerId = customerMapValue['customerId'];
              customerName = customerMapValue['customerName'];
              mobileNoController.text = customerMapValue['customerMobile'];
            }
          }
          if (!isLock) {
            isLock = true;
            if (printCount < salesLimit) {
              await commonSalesPrint(context).then((value) async {
                if (value != null) {
                  // Navigator.pop(context);
                  if (saleLayout == "SaleLayout1" && saleType != "SO" && saleType != "T" && successDialogOnSales!) {
                    await callSuccessDialog(context, salesIdForReport!);
                  }
                  if (((saleLayout == "SaleLayout2" && saleType != "SO" && saleType != "T"))) {
                    SaleModel model = SaleModel();
                    model.isCompletedSales = true;
                    model.fullSale = Sales();
                    model.fullSale?.salesId = salesIdForReport;
                    Navigator.pop(context, model);
                    //Navigator.pop(context);
                  }
                  if (((saleLayout == "SaleLayout2") && (saleType == "SO" || saleType == "T"))) {
                    Navigator.pop(context);
                  }
                  if (saleType == "SO" || saleType == "T") {
                    if (saleType == "T" || (saleType == "SO" && sales?.salesId != null)) {
                      Navigator.pop(context);
                    }
                    Navigator.pushReplacement(
                        context, MaterialPageRoute(builder: (context) => saleType == "SO" ? TableOrderPage("SO") : TableOrderPage("T")));
                  }
                }
              });
            } else if (MyApp.expiryDate != null) {
              if (MyApp.expiryDate!.difference(DateTime.now()).inDays > 0) {
                await commonSalesPrint(context).then((value) async {
                  if (value != null) {
                    // Navigator.pop(context);
                    if (saleLayout == "SaleLayout1" && saleType != "SO" && saleType != "T" && successDialogOnSales!) {
                      await callSuccessDialog(context, salesIdForReport!);
                    }
                    if (((saleLayout == "SaleLayout2" && saleType != "SO" && saleType != "T"))) {
                      SaleModel model = SaleModel();
                      model.isCompletedSales = true;
                      model.fullSale = Sales();
                      model.fullSale?.salesId = salesIdForReport;
                      Navigator.pop(context, model);
                      //Navigator.pop(context);
                    }
                    if (((saleLayout == "SaleLayout2") && (saleType == "SO" || saleType == "T"))) {
                      Navigator.pop(context);
                    }
                    if (saleType == "SO" || saleType == "T") {
                      if (saleType == "T" || (saleType == "SO" && sales?.salesId != null)) {
                        Navigator.pop(context);
                      }
                      Navigator.pushReplacement(
                          context, MaterialPageRoute(builder: (context) => saleType == "SO" ? TableOrderPage("SO") : TableOrderPage("T")));
                    }
                  }
                });
              } else {
                Navigator.pop(context);
                showToast("You have limited print, you need to activate the subscription for more print");
              }
            }
            isLock = false;
          }
        }
        isLock = false;
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "okPrintButtonOnTap() - SalesMainPage");
    }
  }

  kotButtonOnTap() async {
    try {
      if (await checkBillStartCondition()) {
        // validator.validate();
        if (validator.validate()) {
          if (!isLock) {
            isLock = true;
            if (printCount < salesLimit) {
              await commonSalesPrint(context, printType: 1).then((value) async {
                if (value != null) {
                  // Navigator.pop(context);
                  if (saleLayout == "SaleLayout1" && saleType != "SO" && saleType != "T" && successDialogOnSales!) {
                    await callSuccessDialog(context, salesIdForReport!);
                  }
                  if (((saleLayout == "SaleLayout2" && saleType != "SO" && saleType != "T"))) {
                    SaleModel model = SaleModel();
                    model.isCompletedSales = true;
                    model.fullSale = Sales();
                    model.fullSale?.salesId = salesIdForReport;
                    Navigator.pop(context, model);
                    //Navigator.pop(context);
                  }
                  if (((saleLayout == "SaleLayout2") && (saleType == "SO" || saleType == "T"))) {
                    Navigator.pop(context);
                  }
                  if (saleType == "SO" || saleType == "T") {
                    if (saleType == "T" || (saleType == "SO" && sales?.salesId != null)) {
                      Navigator.pop(context);
                    }
                    Navigator.pushReplacement(
                        context, MaterialPageRoute(builder: (context) => saleType == "SO" ? TableOrderPage("SO") : TableOrderPage("T")));
                  }
                }
              });
            } else if (MyApp.expiryDate != null) {
              if (MyApp.expiryDate!.difference(DateTime.now()).inDays > 0) {
                await commonSalesPrint(context, printType: 1).then((value) async {
                  if (value != null) {
                    // Navigator.pop(context);
                    if (saleLayout == "SaleLayout1" && saleType != "SO" && saleType != "T" && successDialogOnSales!) {
                      await callSuccessDialog(context, salesIdForReport!);
                    }
                    if (((saleLayout == "SaleLayout2" && saleType != "SO" && saleType != "T"))) {
                      SaleModel model = SaleModel();
                      model.isCompletedSales = true;
                      model.fullSale = Sales();
                      model.fullSale?.salesId = salesIdForReport;
                      Navigator.pop(context, model);
                      //Navigator.pop(context);
                    }
                    if (((saleLayout == "SaleLayout2") && (saleType == "SO" || saleType == "T"))) {
                      Navigator.pop(context);
                    }
                    if (saleType == "SO" || saleType == "T") {
                      if (saleType == "T" || (saleType == "SO" && sales?.salesId != null)) {
                        Navigator.pop(context);
                      }
                      Navigator.pushReplacement(
                          context, MaterialPageRoute(builder: (context) => saleType == "SO" ? TableOrderPage("SO") : TableOrderPage("T")));
                    }
                  }
                });
              } else {
                Navigator.pop(context);
                showToast("You have limited print, you need to activate the subscription for more print");
              }
            }
            isLock = false;
          }
        }
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "kotButtonOnTap() - SalesMainPage");
    }
  }
}
