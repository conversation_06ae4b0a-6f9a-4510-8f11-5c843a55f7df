import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';

class AlertWidgets {
 
  Future<bool?> paymentAlertWidgets(
  BuildContext buildContext,
  String msgText,
) {
  return showDialog<bool>(
    barrierDismissible: false,
    context: buildContext,
    builder: (context) {
      return ConfirmationAlertDialog(
        content: msgText,
        buttonNameOne: "No",
        ontabButtonOne: () {
          Navigator.of(context).pop(false); // Return true when "No" is pressed
        },
        buttonNameTwo: "Yes",
        ontabButtonTwo: () {
          bool isLock = false;
          if (!isLock) {
            isLock = true;
            Navigator.of(context).pop(true); // Return false when "YES" is pressed
            isLock = false;
          }
        },
      );
    },
  ).then((value) {
    if (value != null) {
      return value;
    }
    return false; // Default return value if none is specified
  });
}

}
