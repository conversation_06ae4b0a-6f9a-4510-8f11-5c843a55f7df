// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code, null_aware_before_operator
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/screens/item_layouts/sales_main_page.dart';
import 'package:smartbill/screens/item_layouts/sales_not_complete_screen.dart';
import 'package:smartbill/screens/reports/reports_view_page.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import '../../model/discounts.dart';
import '../../model/reports.dart';
import '../../model/transaction_model.dart';
import '../../utils/common_function/common_sales_function.dart';
import '../../utils/common_function/discount_function.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_function/printer_utilities.dart';
import '../../utils/common_function/sales_function.dart';
import '../../utils/common_function/validation_utility.dart';
import '../../utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/widgets.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_operations_utility.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/sales_trans_staging.dart';
import '../masters/tables/tables_list.dart';
import 'Itempage_layout2.dart';

class TableOrderPage extends StatefulWidget {
  String flag;
  TableOrderPage(this.flag);

  @override
  State<TableOrderPage> createState() => _TableOrderPageState();
}

class _TableOrderPageState extends State<TableOrderPage> {
  double width = 0.0;
  double height = 0.0;
  double paidAmount = 0.0;
  bool isBigLayout = false;
  bool multiPrint = false;
  bool isDiscount = false;
  bool isPaymentType = false;
  bool isCustomer = false;
  bool isReturnAmount = false;
  bool successDialogOnSales = false;
  bool addTax = false;
  bool isLoading = false;
  bool isIGST = false;
  bool isCollectBillPrintLock = false;
  bool isPaymentTypeIsEmpty = false;
  bool isPaymentAmountEmpty = false;
  bool isPaymentMappingListExist = false;
  bool isSalesBillPadEnable = false;

  String? discountType;
  String? saleLayout;
  String? saleID;

  int paymentMappingIndex = -1;

  double returnAmount = 0.0;
  double total = 0.0;

  FnUtilities fnUtilities = new FnUtilities();
  SalesFunctions salesFun = SalesFunctions();
  PrinterUtilities printerUtilities = new PrinterUtilities();
  DBOperations dbOperations = new DBOperations();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  DiscountFunction discount = DiscountFunction();
  Validator validator = new Validator();

  CommonSalesFunction commonSalesFunction = CommonSalesFunction();

  List<Sales> activeTablesList = [];
  List<Transactions> saleProductList = [];
  List<Transactions> isActiveSaleList = [];
  List<Transactions> salesTransactionList = [];

  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  StateSetter? saveAlertSetState;
    initPrintFunction() async {
    if (!multiPrint) {
      PrinterDevices reportPrinter = PrinterDevices();
      await printerUtilities.disconnect();
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter);
    }
  }

  initFunction() async {
    setState(() {
      isLoading = true;
    });
    await initPrintFunction();
    await initSettings();
    await assignAllActiveSales();
    await checkSalesDelete();
    await assignSalesProducts();
    setState(() {
      isLoading = false;
    });
  }

  initSettings() async {
    setState(() {
      ///workspace settings
      multiPrint = fnUtilities.workSpaceSetValues(key_multiPrint) == "1";
      isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
      discountType = fnUtilities.workSpaceSetValues(key_discountType);
      isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      isCustomer = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
      addTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
      isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
      successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
      isSalesBillPadEnable = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";

      ///workspace user settings
      saleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
    });
  }

  assignAllActiveSales() async {
    var temActiveSalesList = await salesFun.getAllActiveSales(widget.flag);
    setState(() {
      activeTablesList.clear();
      activeTablesList.addAll(temActiveSalesList);
      print(activeTablesList.length);
    });
  }

  checkSalesDelete() async {
    for (Sales item in activeTablesList) {
      await assignIsActiveSaleList(item.salesId ?? "");
      if (isActiveSaleList.isEmpty) {
        await salesDetailsDBScript.deleteSales(item.salesId ?? "");
      }
    }
    assignAllActiveSales();
  }

  assignSalesProducts() async {
    var tempSalesProductList = await salesFun.getSalesProducts();
    setState(() {
      saleProductList.clear();
      saleProductList.addAll(tempSalesProductList);
      print(saleProductList.length);
    });
  }

  assignIsActiveSaleList(String salesId) async {
    var tempIsActiveSaleList = await salesFun.getAllReportsWithProductsByDateToDate(salesId);
    setState(() {
      isActiveSaleList.clear();
      isActiveSaleList.addAll(tempIsActiveSaleList);
      print(isActiveSaleList.length);
    });
  }

  updateIsActiveSalesList(String salesId) {}
  assignTransactionList(id) async {
    var tempTransactionList = await salesFun.getAllTransaction(id);
    setState(() {
      salesTransactionList.clear();
      salesTransactionList.addAll(tempTransactionList);
    });
  }

  //CALCULATE TOTAL AMOUNT
  findTotal(Sales sales) {
    double totalAmount = 0.0;
    for (Transactions p in salesTransactionList) {
      totalAmount += double.parse(p.amount ?? "0");
    }
    if (sales.discountPrice != null) {
      total = totalAmount - double.parse(sales.discountPrice ?? "0");
      print(total);
    }
    sales.totalAmount = totalAmount.toString();
  }

  activeTableUpdateTransaction(Sales salesModel) async {
    var temSalesTransactionList = await salesFun.activeTableUpdateTransaction(salesModel);
    setState(() {
      salesTransactionList.clear();
      salesTransactionList.addAll(temSalesTransactionList);
      print(saleProductList.length);
    });
    return salesTransactionList;
  }

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  @override
  // void dispose() {
  //   if (multiPrint == false) {
  //     printerUtilities.disconnect();
  //   }
  //   super.dispose();
  // }

  getSalesDetails(String salesId, String flag) async {
    var tempSalesTransactionList = await salesFun.getSalesDetails(salesId, flag);
    setState(() {
      salesTransactionList.clear();
      salesTransactionList.addAll(tempSalesTransactionList);
    });
  }

  commonDiscountFunction(Sales sales) async {
    try {
      await assignTransactionList(sales.salesId);
      if (isDiscount) {
        for (Transactions d in salesTransactionList) {
          d.discountPrice = null;
          d.discountFormula = null;
        }
        if (discountType == "Product") {
          await discount.calculateDiscount(
            "A",
          );
        } else {
          Discounts discountModel = await discount.salesCommonDiscount("A", saleID: null, saleNum: null);
          sales.discountFormula = discountModel.formula;
          sales.discountPrice = discountModel.discountAmount;
        }
      }
      await discount.callCalculate(null);
      findTotal(sales);
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "commonDiscountFunction - TableOrderPage");
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: appThemeColor,
      resizeToAvoidBottomInset: false,
      appBar: BaseAppBar(
        title: text(widget.flag == "SO" ? "Eat & pay" : "Active Tables",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          InkWell(
            onTap: () async {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                if (widget.flag == "SO") {
                  Navigator.pushReplacement(
                      context,
                      MaterialPageRoute(
                          builder: (context) =>
                              saleLayout == "SaleLayout2" ? ItemsPageLayout2(saleType: widget.flag) : SalesMainPage(saleType: widget.flag)));
                } else {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => TablesList())).then((value) {});
                }
                isLock = false;
              }
            },
            child: Row(
              children: [
                if (isSalesBillPadEnable) ...[
                  InkWell(
                    onTap: () {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => SalesNotCompletePage())).then((value) async {
                        await initFunction();
                      });
                    },
                    child: Container(
                      padding: EdgeInsets.only(right: 10, left: 10, top: 5),
                      child: SvgPicture.asset(
                        pendingSVG,
                        color: secondaryTextColor,
                        height: 23,
                        width: 15,
                      ),
                    ),
                  ),
                ],
                Container(
                  alignment: Alignment.topRight,
                  padding: EdgeInsets.only(right: 20, left: 10, top: 5),
                  height: 38,
                  child: text("New", textColor: secondaryTextColor, isCentered: true),
                  decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                ),
              ],
            ),
          ),
        ],
      ),
      body: Column(
        children: [
          SizedBox(
            height: 3,
          ),
          if (isLoading) ...[
            Expanded(
                child: Center(
                    child: CircularProgressIndicator(
              color: secondaryTextColor,
            )))
          ] else ...[
            Expanded(
              child: activeTablesList.isNotEmpty
                  ? SingleChildScrollView(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Padding(
                            padding: const EdgeInsets.all(10.0),
                            child: Column(
                              children: [
                                Align(
                                  alignment: Alignment.topCenter,
                                  child: Wrap(
                                      // padding: EdgeInsets.only(top: 5.0),
                                      children: List.generate(activeTablesList.length, (index) {
                                    var item = activeTablesList[index];
                                    List<Transactions> tempSaleProductList = [];
                                    tempSaleProductList = saleProductList.where((element) => element.salesId == item.salesId).toList();
                                    item.tableName = ListUtility.shopTableList
                                        .firstWhere((element) => element.shopTableId == item.fkTableID, orElse: () => ShopTable(shopTableName: ""))
                                        .shopTableName;
                                    return GestureDetector(
                                      onLongPress: () {
                                        if (PermissionFunctions.checkPermission(82) || PermissionFunctions.checkPermission(93)) {
                                          showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return DeleteDialog(
                                                  message: "Are you sure you want to delete?",
                                                  buttonNameOne: "Yes",
                                                  onPressOne: () async {
                                                    var isLock = false;
                                                    if (!isLock) {
                                                      isLock = true;
                                                      await salesDetailsDBScript.updateSalesDelete(item.salesId ?? "");
                                                      await salesTransDBScript.updateSalesTransactionDelete(item.salesId ?? "");
                                                      await assignAllActiveSales();
                                                      Navigator.pop(context);
                                                      isLock = false;
                                                    }
                                                  },
                                                  buttonNameTwo: "Cancel",
                                                  onPressTwo: () {
                                                    var isLock = false;
                                                    if (!isLock) {
                                                      isLock = true;
                                                      Navigator.pop(context);
                                                      isLock = false;
                                                    }
                                                  },
                                                );
                                              });
                                        }
                                      },
                                      child: Card(
                                        child: Container(
                                          width: width > tabletWidth ? width * 0.3 : width,
                                          padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                          child: InkWell(
                                            onTap: () async {
                                              await assignIsActiveSaleList(item.salesId ?? "");
                                              setState(() {
                                                if (saleID == null) {
                                                  saleID = item.salesId;
                                                } else if (saleID != item.salesId && saleID != null) {
                                                  saleID = item.salesId;
                                                } else {
                                                  saleID = null;
                                                }
                                              });
                                            },
                                            child: Column(
                                              children: <Widget>[
                                                /// show customer name and product details
                                                Column(
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        if (item.invoiceId != null) ...[
                                                          text(item.invoiceId ?? "",
                                                              textColor: buttonThemeColor,
                                                              fontSize: textSizeMedium,
                                                              fontFamily: fontSemibold,
                                                              isCentered: false,
                                                              isLongText: true),
                                                        ],
                                                        text(
                                                            item.tableName != null && item.customerName != null
                                                                ? item.tableName ?? "" + " - " + item.customerName!
                                                                : item.tableName != null && item.customerName == null
                                                                    ? item.tableName ?? ""
                                                                    : item.customerName != null && item.tableName == null
                                                                        ? item.customerName ?? ""
                                                                        : "",
                                                            textColor: buttonThemeColor,
                                                            fontSize: textSizeMedium,
                                                            fontFamily: fontSemibold,
                                                            isCentered: false,
                                                            isLongText: true),
                                                      ],
                                                    ),
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        text(
                                                            fnUtilities.checkQtyDoubleValue(double.parse(item.totalAmount ?? "0").toStringAsFixed(2)),
                                                            textColor: buttonThemeColor,
                                                            fontSize: textSizeMedium,
                                                            fontFamily: fontSemibold,
                                                            isCentered: false,
                                                            isLongText: true),
                                                        Row(
                                                          children: [
                                                            if (widget.flag != "SO") ...[
                                                              Tooltip(
                                                                message: "Change table",
                                                                child: IconButton(
                                                                    onPressed: () {
                                                                      Navigator.push(
                                                                          context,
                                                                          MaterialPageRoute(
                                                                              builder: (context) => TablesList(
                                                                                    salesModel: item,
                                                                                  ))).then((value) {
                                                                        setState(() {
                                                                          initFunction();
                                                                        });
                                                                      });
                                                                    },
                                                                    icon: Icon(Icons.change_circle_outlined)),
                                                              ),
                                                            ],
                                                            IconButton(
                                                                onPressed: () {
                                                                  var isLock = false;
                                                                  if (!isLock) {
                                                                    isLock = true;
                                                                    item.isActiveSale = null;
                                                                    Navigator.push(
                                                                            context,
                                                                            MaterialPageRoute(
                                                                                builder: (context) => saleLayout == "SaleLayout2"
                                                                                    ? ItemsPageLayout2(sales: item, saleType: widget.flag)
                                                                                    : SalesMainPage(sales: item, saleType: widget.flag)))
                                                                        .then((value) async {
                                                                      await assignAllActiveSales();
                                                                      await assignSalesProducts();
                                                                    });
                                                                    isLock = false;
                                                                  }
                                                                },
                                                                icon: Icon(Icons.add)),
                                                            IconButton(
                                                                onPressed: () async {
                                                                  var isLock = false;
                                                                  if (!isLock) {
                                                                    isLock = true;
                                                                    if (PermissionFunctions.checkPermission(100)) {
                                                                      Navigator.push(
                                                                          context,
                                                                          MaterialPageRoute(
                                                                              builder: (context) => ReportsViewPage(
                                                                                    reports: item,
                                                                                    tableName: "staging",
                                                                                  )));
                                                                    }
                                                                    isLock = false;
                                                                  }
                                                                },
                                                                icon: Icon(Icons.preview)),
                                                            IconButton(
                                                                onPressed: () async {
                                                                  await assignIsActiveSaleList(item.salesId ?? "");
                                                                  setState(() {
                                                                    if (saleID == null) {
                                                                      saleID = item.salesId;
                                                                    } else if (saleID != item.salesId && saleID != null) {
                                                                      saleID = item.salesId;
                                                                    } else {
                                                                      saleID = null;
                                                                    }
                                                                  });
                                                                },
                                                                icon: Icon(Icons.expand)),
                                                          ],
                                                        ),
                                                      ],
                                                    ),
                                                    Container(
                                                      width: width / 1.1,
                                                      alignment: Alignment.centerLeft,
                                                      child: SingleChildScrollView(
                                                        scrollDirection: Axis.horizontal,
                                                        child: Row(
                                                          children: [
                                                            for (int i = 0; i < tempSaleProductList.length; i++)
                                                              (i == tempSaleProductList.length - 1)
                                                                  ? text(tempSaleProductList[i].name! + ".",
                                                                      textColor: primaryTextColor,
                                                                      fontSize: textSizeSmall,
                                                                      fontFamily: fontSemibold,
                                                                      isCentered: false,
                                                                      isLongText: false)
                                                                  : text(tempSaleProductList[i].name! + ", ",
                                                                      textColor: primaryTextColor,
                                                                      fontSize: textSizeSmall,
                                                                      fontFamily: fontSemibold,
                                                                      isCentered: false,
                                                                      isLongText: false),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                ),

                                                /// show content while
                                                saleID == item.salesId
                                                    ? Column(
                                                        children: [
                                                          SizedBox(
                                                            height: 10,
                                                          ),
                                                          Divider(
                                                            thickness: .5,
                                                          ),
                                                          for (Transactions order in isActiveSaleList)
                                                            Container(
                                                              // height: 40,
                                                              alignment: Alignment.centerLeft,
                                                              child: Row(
                                                                children: [
                                                                  Expanded(
                                                                    child: Container(
                                                                      child: SingleChildScrollView(
                                                                        scrollDirection: Axis.horizontal,
                                                                        child: Wrap(
                                                                          children: [
                                                                            for (int i = 0; i < tempSaleProductList.length; i++)
                                                                              if (order.isActiveSale == tempSaleProductList[i].isActiveSale)
                                                                                (i == tempSaleProductList.length - 1)
                                                                                    ? text(tempSaleProductList[i].name! + ".",
                                                                                        textColor: primaryTextColor,
                                                                                        fontSize: textSizeSmall,
                                                                                        fontFamily: fontSemibold,
                                                                                        isCentered: false,
                                                                                        isLongText: false)
                                                                                    : text(tempSaleProductList[i].name! + ", ",
                                                                                        textColor: primaryTextColor,
                                                                                        fontSize: textSizeSmall,
                                                                                        fontFamily: fontSemibold,
                                                                                        isCentered: false,
                                                                                        isLongText: false),
                                                                          ],
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                  Row(
                                                                    children: [
                                                                      Container(
                                                                        child: IconButton(
                                                                            onPressed: () async {
                                                                              var isLock = false;
                                                                              if (!isLock) {
                                                                                isLock = true;
                                                                                item.isActiveSale = order.isActiveSale;
                                                                                List<Transactions> list = await activeTableUpdateTransaction(item);
                                                                                Navigator.push(
                                                                                    context,
                                                                                    MaterialPageRoute(
                                                                                        builder: (context) => saleLayout == "SaleLayout2"
                                                                                            ? ItemsPageLayout2(
                                                                                                sales: item,
                                                                                                saleTransactionList: list,
                                                                                                saleType: widget.flag)
                                                                                            : SalesMainPage(
                                                                                                sales: item,
                                                                                                saleTransactionList: list,
                                                                                                saleType: widget.flag))).then((value) async {
                                                                                  saleID = null;
                                                                                  await assignAllActiveSales();
                                                                                  await assignSalesProducts();
                                                                                  checkSalesDelete();
                                                                                });
                                                                                isLock = false;
                                                                              }
                                                                            },
                                                                            icon: Icon(
                                                                              Icons.edit,
                                                                              size: 20,
                                                                            )),
                                                                      ),
                                                                      Container(
                                                                        child: IconButton(
                                                                            onPressed: () async {
                                                                              var isLock = false;
                                                                              if (!isLock) {
                                                                                isLock = true;
                                                                                deleteOrderDialog(context, item, order.isActiveSale ?? -1);
                                                                                isLock = false;
                                                                              }
                                                                            },
                                                                            icon: Icon(
                                                                              Icons.delete,
                                                                              size: 20,
                                                                            )),
                                                                      ),
                                                                      InkWell(
                                                                        onTap: () async {
                                                                          var isLock = false;
                                                                          if (!isLock) {
                                                                            isLock = true;
                                                                            if (isSalesBillPadEnable) {
                                                                              await commonSalesFunction.onPressPrint(
                                                                                  context, item, order.isActiveSale.toString(),
                                                                                  salesStatus: SalesStatus.processbill);
                                                                                 
                                                                            } else {
                                                                              await commonSalesFunction.onPressPrint(
                                                                                  context, item, order.isActiveSale.toString(),
                                                                                  salesStatus: SalesStatus.completeorder);
                                                                                  
                                                                            }

                                                                            isLock = false;
                                                                          }
                                                                        },
                                                                        child: Container(
                                                                          child: Row(
                                                                            children: [
                                                                              Text(
                                                                                "KOT - " + (order.isActiveSale).toString(),
                                                                                style: TextStyle(fontSize: 12, color: whiteColor),
                                                                              ),
                                                                              SizedBox(
                                                                                width: 1,
                                                                              ),
                                                                              Icon(
                                                                                Icons.kitesurfing,
                                                                                color: whiteColor,
                                                                                size: 20,
                                                                              ),
                                                                            ],
                                                                          ),
                                                                          padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
                                                                          decoration:
                                                                              boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                                                                        ),
                                                                      ),
                                                                    ],
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          Divider(
                                                            thickness: .5,
                                                          ),
                                                          Container(
                                                            width: width / 1.1,
                                                            height: 50,
                                                            alignment: Alignment.center,
                                                            child: SingleChildScrollView(
                                                              scrollDirection: Axis.horizontal,
                                                              child: Row(
                                                                mainAxisAlignment: MainAxisAlignment.center,
                                                                children: [
                                                                  InkWell(
                                                                    onTap: () async {
                                                                      var isLock = false;
                                                                      if (!isLock) {
                                                                        isLock = true;
                                                                        if (isActiveSaleList.length > 0) {
                                                                          if (isSalesBillPadEnable) {
                                                                            await commonSalesFunction.onPressPrint(context, item, "KOT",
                                                                                salesStatus: SalesStatus.processbill);
                                                                                 
                                                                          } else {
                                                                            await commonSalesFunction.onPressPrint(context, item, "KOT",
                                                                                salesStatus: SalesStatus.completeorder);
                                                                                
                                                                          }
                                                                        }
                                                                        isLock = false;
                                                                      }
                                                                    },
                                                                    child: Container(
                                                                      child: Row(
                                                                        children: [
                                                                          Text(
                                                                            "KOT",
                                                                            style: TextStyle(fontSize: 12, color: whiteColor),
                                                                          ),
                                                                          SizedBox(
                                                                            width: 2,
                                                                          ),
                                                                          Icon(
                                                                            Icons.kitchen,
                                                                            color: whiteColor,
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                                      decoration: boxDecoration(
                                                                          radius: 10,
                                                                          showShadow: true,
                                                                          bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                    width: 10,
                                                                  ),
                                                                  InkWell(
                                                                    onTap: () async {
                                                                      bool isCollectBillLock = false;
                                                                      if (!isCollectBillLock) {
                                                                        isCollectBillLock = true;
                                                                        if (isActiveSaleList.length > 0) {
                                                                          int result;
                                                                          if (isSalesBillPadEnable) {
                                                                            result = await commonSalesFunction.saveDialog(
                                                                                context, item, "save", isBigLayout,
                                                                                saleStatus: SalesStatus.processbill);
                                                                          } else {
                                                                            result = await commonSalesFunction.saveDialog(
                                                                                context, item, "save", isBigLayout,
                                                                                saleStatus: SalesStatus.completeorder);
                                                                          }

                                                                          await assignAllActiveSales();
                                                                          if (successDialogOnSales && result == 1) {
                                                                            callSuccessDialog(context, item.salesId ?? "");
                                                                          }
                                                                        }
                                                                        isCollectBillLock = false;
                                                                      }
                                                                    },
                                                                    child: Container(
                                                                      child: Row(
                                                                        children: [
                                                                          Text(
                                                                            isSalesBillPadEnable ? "Process Bill" : "Complete Bill",
                                                                            style: TextStyle(fontSize: 12, color: whiteColor),
                                                                          ),
                                                                          SizedBox(
                                                                            width: 2,
                                                                          ),
                                                                          Icon(
                                                                            Icons.save,
                                                                            color: whiteColor,
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                                      decoration: boxDecoration(
                                                                          radius: 10,
                                                                          showShadow: true,
                                                                          bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
                                                                    ),
                                                                  ),
                                                                  SizedBox(
                                                                    width: 10,
                                                                  ),
                                                                  InkWell(
                                                                    onTap: () async {
                                                                      if (!isCollectBillPrintLock) {
                                                                        isCollectBillPrintLock = true;

                                                                        if (isActiveSaleList.length > 0) {
                                                                          int result;
                                                                          if (isSalesBillPadEnable) {
                                                                            result = await commonSalesFunction.saveDialog(
                                                                                context, item, "print", isBigLayout,
                                                                                saleStatus: SalesStatus.processbill);
                                                                          } else {
                                                                            result = await commonSalesFunction.saveDialog(
                                                                                context, item, "print", isBigLayout,
                                                                                saleStatus: SalesStatus.completeorder);
                                                                          }
                                                                          int;
                                                                          await assignAllActiveSales();
                                                                          if (successDialogOnSales && result == 1) {
                                                                            callSuccessDialog(context, item.salesId ?? "");
                                                                          }
                                                                        }
                                                                        isCollectBillPrintLock = false;
                                                                      }
                                                                    },
                                                                    child: Container(
                                                                      child: Row(
                                                                        children: [
                                                                          Text(
                                                                            isSalesBillPadEnable ? "Process Bill &" : "Complete Bill &",
                                                                            style: TextStyle(fontSize: 12, color: whiteColor),
                                                                          ),
                                                                          SizedBox(
                                                                            width: 2,
                                                                          ),
                                                                          Icon(
                                                                            Icons.print,
                                                                            color: whiteColor,
                                                                          ),
                                                                        ],
                                                                      ),
                                                                      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                                      decoration: boxDecoration(
                                                                          radius: 10,
                                                                          showShadow: true,
                                                                          bgColor: isActiveSaleList.length > 0 ? buttonThemeColor : Colors.grey),
                                                                    ),
                                                                  )
                                                                ],
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      )
                                                    : Container(),
                                                // Divider(height: 0.5, color: t5ViewColor)
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    );
                                  })),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    )
                  : Center(
                      child: Text(
                        widget.flag == "SO" ? "No active order available" : "No active table available",
                        style: headingTextStyle3,
                      ),
                    ),
            ),
          ]
        ],
      ),
    );
  }

  deleteOrderDialog(BuildContext context, Sales sale, int transactSaleNo) {
    bool isDeleteLock = false;
    return showDialog(
        context: context,
        builder: (context) {
          return DeleteDialog(
            message: "Are you sure want to delete order?",
            buttonNameOne: "Cancel",
            onPressOne: () {
              if (!isDeleteLock) {
                isDeleteLock = true;
                Navigator.pop(context);
                isDeleteLock = false;
              }
            },
            buttonNameTwo: "Delete",
            onPressTwo: () async {
              if (!isDeleteLock) {
                isDeleteLock = true;
                saleID = null;
                await salestransStagingDBScript.deleteSalesTransactionStagingBySaleNo(sale.salesId ?? "", transactSaleNo);
                await assignSalesProducts();
                await assignTransactionList(sale.salesId);
                await findTotal(sale);
                sale.sync = 0;
                await salesDetailsDBScript.updateSalesActiveTable(sale);
                await checkSalesDelete();
                Navigator.pop(context);
                isDeleteLock = false;
              }
            },
          );
        });
  }

  callSuccessDialog(BuildContext context, String salesId) async {
    await fnUtilities.salesSuccessDialog(context, salesId);
  }

  changeTableAlert(Sales salesModel) {}
}
