// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code, null_aware_before_operator

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/screens/reports/reports_view_page.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';

import '../../model/advance_order_note_model.dart';
import '../../model/printer_devices.dart';
import '../../model/reports.dart';
import '../../model/sales_payment_mapping.dart';
import '../../model/transaction_model.dart';
import '../../utils/common_function/common_sales_function.dart';
import '../../utils/common_function/discount_function.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_function/printer_utilities.dart';
import '../../utils/common_function/sales_function.dart';
import '../../utils/common_function/validation_utility.dart';
import '../../utils/common_widgets/advance_order_widget.dart';
import '../../utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';

import '../../utils/common_widgets/widgets.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/advance_order_db_scripts.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/dbutils/db_operations_utility.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_payment_mapping_db_script.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/sales_trans_staging.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import 'Itempage_layout2.dart';
import 'sales_main_page.dart';

class SalesNotCompletePage extends StatefulWidget {
  SalesNotCompletePage();

  @override
  State<SalesNotCompletePage> createState() => _SalesNotCompletePageState();
}

class _SalesNotCompletePageState extends State<SalesNotCompletePage> with TickerProviderStateMixin {
  double width = 0.0;
  double height = 0.0;
  double paidAmount = 0.0;
  bool isBigLayout = false;
  bool isLock = false;
  bool multiPrint = false;
  bool isDiscount = false;
  bool isPaymentType = false;
  bool isCustomer = false;
  bool isReturnAmount = false;
  bool successDialogOnSales = false;
  bool addTax = false;
  bool isLoading = false;
  bool isIGST = false;
  bool isCollectBillLock = false;
  bool isCollectBillPrintLock = false;
  bool isPaymentTypeIsEmpty = false;
  bool isPaymentAmountEmpty = false;
  bool isPaymentMappingListExist = false;
  bool isSalesBillPadEnable = false;

  String? discountType;
  String? saleLayout;
  String? saleID;

  int paymentMappingIndex = -1;

  double returnAmount = 0.0;
  double total = 0.0;

  FnUtilities fnUtilities = new FnUtilities();
  SalesFunctions salesFun = SalesFunctions();
  PrinterUtilities printerUtilities = new PrinterUtilities();
  DBOperations dbOperations = new DBOperations();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  DiscountFunction discount = DiscountFunction();
  Validator validator = new Validator();
  CommonSalesFunction commonSalesFunction = CommonSalesFunction();
  List<Sales> salesNotCompletedList = [];
  List<Sales> filterSalesNotCompletedList = [];
  List<Transactions> isActiveSaleList = [];
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();

  List<Sales> salesAdvanceList = [];
  List<Sales> filterSalesAdvanceList = [];
  List<Transactions> advanceOrderProductList = [];
  List<AdvanceOrderNoteModel> orderNotesList = [];
  AdvanceOrdersDBScripts advanceOrderDB = AdvanceOrdersDBScripts();

  /// Get Printer Info
  PrinterDevices? printerForBill;
  PrinterDevices? printerForKOT;

  StateSetter? saveAlertSetState;
  int selectIndex = 0;
  bool isEnableAdvanceOrder = false;
  List<SalesPaymentMapping>? paymentMapList;
  String? orderNote;
  final searchController = new TextEditingController();
  TabController? controller;
  CommonDB commonDB = CommonDB();

  initFunction() async {
    setState(() {
      isLoading = true;
    });
    await getPrinterInfo();
    await initSettings();
    await initPagePrefrenceFunction(selectIndex);
    if (isSalesBillPadEnable && isEnableAdvanceOrder) {
      controller = TabController(length: 2, vsync: this);
      controller?.addListener(() {
        setState(() {
          if (controller?.length == 1) {
            if (controller?.index == 0) {
              selectIndex = 0;
              initPagePrefrenceFunction(selectIndex);
              searchController.clear();
            }
          } else if (controller!.length > 1) {
            if (controller?.index == 1) {
              selectIndex = 1;

              initPagePrefrenceFunction(selectIndex);
              searchController.clear();
            }
          }
        });
      });
    }
    setState(() {
      isLoading = false;
    });
  }

  initPagePrefrenceFunction(int selectIndex) async {
    try {
      setState(() {
        isLoading = true;
      });
      if (selectIndex == 0 && isSalesBillPadEnable) {
        await assignNotCompletedSale();
      } else if (selectIndex == 1 || isEnableAdvanceOrder) {
        await getAllAdvanceOrderSales();
        await assignAdvanceOrderProductList();
      }
      await Future.delayed(Duration(milliseconds: 1000));
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ErrorLogsFunction.insertErrorLogs(e, "initPagePrefrenceFunction - SalesNotCompletePage");
    }
  }

  SalesPaymentMappingDB paymentMappingDb = SalesPaymentMappingDB();
  List<SalesPaymentMapping> paymentMappingList = [];

  initSettings() async {
    setState(() {
      ///workspace settings
      multiPrint = fnUtilities.workSpaceSetValues(key_multiPrint) == "1";
      isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
      discountType = fnUtilities.workSpaceSetValues(key_discountType);
      isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      isCustomer = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
      addTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
      isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
      successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
      isSalesBillPadEnable = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";
      isEnableAdvanceOrder = fnUtilities.workSpaceSetValues(key_advanceOrder) == "1";

      ///workspace user settings
      saleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
    });
  }

  getPrinterInfo() {
    /// Get Printer Informations
    printerForBill = printerUtilities.getCurrentPrinter(PrinterFor.bill);
    printerForKOT = printerUtilities.getCurrentPrinter(PrinterFor.kot);
  }

  ///Assign not complted sales items
  assignNotCompletedSale() async {
    var temActiveSalesList = await salesFun.getAllNotCompletedSale();
    setState(() {
      salesNotCompletedList.clear();
      salesNotCompletedList.addAll(temActiveSalesList);
      filterSalesNotCompletedList.clear();
      filterSalesNotCompletedList.addAll(salesNotCompletedList);
    });
  }

// ------------------------------------------------ ADVANCED ORDER FUNCTIONS -----------------//

  getAllAdvanceOrderSales() async {
    var temAdvanceList = await salesFun.getAllAddvanceOrder();
    setState(() {
      salesAdvanceList.clear();
      salesAdvanceList.addAll(temAdvanceList);
      filterSalesAdvanceList.clear();

      filterSalesAdvanceList.addAll(salesAdvanceList);
    });
  }

  assignAdvanceOrderProductList() async {
    var tempIsActiveSaleList = await salesFun.getAdvanceOrderSalesProducts();
    setState(() {
      advanceOrderProductList.clear();
      advanceOrderProductList.addAll(tempIsActiveSaleList);
    });
    // log(advanceOrderProductList);
  }

  getPaymentDetails(String id) async {
    final allRows = await paymentMappingDb.queryAllRowsBasedOnPaymentSalesId(id);
    setState(() {
      paymentMappingList.clear();
      allRows.forEach((row) => paymentMappingList.add(SalesPaymentMapping.fromJson(row)));
    });
  }

  getOrderNotesList(String salesID) async {
    var orderNoteMap = await commonDB.getSingleDataBasedOneColumn(db_order_notes, db_sales_salesId, salesID);
    orderNote = orderNoteMap != null ? AdvanceOrderNoteModel.fromJson(orderNoteMap).note : null;
  }

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  void searchSalesItem(String value) async {
    selectIndex = isEnableAdvanceOrder ? 1 : 0;
    try {
      setState(() {
        selectIndex == 1 ? filterSalesAdvanceList.clear() : filterSalesNotCompletedList.clear();
        if (value != "") {
          for (Sales model in selectIndex == 1 ? salesAdvanceList : salesNotCompletedList) {
            if ((model.customerName != null && model.customerName!.toLowerCase().contains(value.toLowerCase())) ||
                (model.invoiceId != null && model.invoiceId!.toLowerCase().contains(value.toLowerCase()))) {
              selectIndex == 1 ? filterSalesAdvanceList.add(model) : filterSalesNotCompletedList.add(model);
            }
          }
        } else {
          selectIndex == 1 ? filterSalesAdvanceList.addAll(salesAdvanceList) : filterSalesNotCompletedList.addAll(salesNotCompletedList);
        }
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "searchSalesItem - SalesNotCompletePage ");
    }
  }

  revertSalesQueue(Sales? salesModel, List<Transactions> salesTransactionsList) async {
    if (salesModel != null) {
      salesModel.sync = 0;
      salesModel.isActiveSale = 1;
      salesModel.salesStatus = 1;
      await salesDetailsDBScript.updateSalesActiveTable(salesModel);
      updateTransaction(salesModel.salesId ?? "", salesTransactionsList);
    }
  }

  updateTransaction(String id, List<Transactions> salesTransactionList, {bool isAdvanceOrder = false}) async {
    for (Transactions transaction in salesTransactionList) {
      await salestransStagingDBScript.updateSalesTransactionStaggingSync(transaction.transactId ?? "", 1);
      await salesTransDBScript.updateSalesTransactionSync(transaction.transactId ?? "", 2);
    }
  }

  ///Get sales transaction details
  getSalesTransactiondetails(String saleId) async {
    var tempSalesTransactionList = await salesFun.getSalesTransactionDetails(saleId);
    return tempSalesTransactionList;
  }

  void handleSaleIDChange(item) async {
    // Determine the new saleID
    String? newSaleID;

    if (saleID == null) {
      newSaleID = item.salesId;
    } else if (saleID != item.salesId && saleID != null) {
      newSaleID = item.salesId;
    } else {
      newSaleID = null;
    }

    // Perform the async operation if needed
    if (newSaleID != null) {
      await getOrderNotesList(newSaleID);
    }

    // Update the state
    setState(() {
      saleID = newSaleID;
    });
  }

  String? getTitle() {
    if (isSalesBillPadEnable && isEnableAdvanceOrder) {
      return "Sales & Orders";
    } else if (isEnableAdvanceOrder) {
      return "Orders Queue";
    } else if (isSalesBillPadEnable) {
      return "Sales Queue";
    } else {
      return "";
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return DefaultTabController(
      length: isSalesBillPadEnable && isEnableAdvanceOrder ? 2 : 0,
      child: Scaffold(
          backgroundColor: appThemeColor,
          resizeToAvoidBottomInset: false,
          appBar: AppBar(
            backgroundColor: appThemeColor,
            elevation: isSalesBillPadEnable && isEnableAdvanceOrder ? 5.0 : 0.0,
            title: text(getTitle() ?? '', textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
            bottom: isSalesBillPadEnable && isEnableAdvanceOrder
                ? TabBar(
                    onTap: (index) {},
                    controller: controller,
                    isScrollable: true,
                    labelStyle: primaryTextStyle(),
                    indicatorColor: redColor,
                    physics: BouncingScrollPhysics(),
                    labelColor: secondaryTextColor,
                    tabs: [
                      // if (isSalesBillPadEnable)
                      Tab(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.blinds_closed,
                              color: secondaryTextColor,
                            ),
                            5.width,
                            Text(
                              'Sales Queue',
                            ),
                          ],
                        ),
                      ),
                      // if (isEnableAdvanceOrder)
                      Tab(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.scale_sharp,
                              color: secondaryTextColor,
                            ),
                            5.width,
                            Text(
                              'Orders Queue',
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                : null,
          ),
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: TabBarView(controller: controller, children: [
              if (isSalesBillPadEnable) ...[
                Column(
                  children: <Widget>[
                    SizedBox(
                      height: 15,
                    ),
                    searchWidget(),
                    SizedBox(
                      height: 10,
                    ),
                    isLoading
                        ? Expanded(
                            child: Center(
                                child: CircularProgressIndicator(
                            color: secondaryTextColor,
                          )))
                        : salesQueueWidget(),
                  ],
                ),
              ],
              if (isEnableAdvanceOrder) ...[
                Column(
                  children: <Widget>[
                    SizedBox(
                      height: 15,
                    ),
                    searchWidget(),
                    SizedBox(
                      height: 10,
                    ),
                    isLoading
                        ? Expanded(
                            child: Center(
                                child: CircularProgressIndicator(
                            color: secondaryTextColor,
                          )))
                        : advanceOrderWidget(),
                  ],
                )
              ]
            ]),
          )),
    );
  }

  Widget salesQueueWidget() {
    return Expanded(
      child: filterSalesNotCompletedList.isNotEmpty
          ? SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(10.0),
                    child: Column(
                      children: [
                        Align(
                          alignment: Alignment.topCenter,
                          child: Wrap(
                              // padding: EdgeInsets.only(top: 5.0),
                              children: List.generate(filterSalesNotCompletedList.length, (index) {
                            var item = filterSalesNotCompletedList[index];

                            item.tableName = ListUtility.shopTableList
                                .firstWhere((element) => element.shopTableId == item.fkTableID, orElse: () => ShopTable(shopTableName: ""))
                                .shopTableName;
                            return GestureDetector(
                              onLongPress: () {},
                              child: Card(
                                child: Container(
                                  width: width > tabletWidth ? width * 0.3 : width,
                                  padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                  child: InkWell(
                                    onTap: () async {
                                      setState(() {
                                        if (saleID == null) {
                                          saleID = item.salesId;
                                        } else if (saleID != item.salesId && saleID != null) {
                                          saleID = item.salesId;
                                        } else {
                                          saleID = null;
                                        }
                                      });
                                    },
                                    child: Column(
                                      children: <Widget>[
                                        Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                if (item.invoiceId != null) ...[
                                                  text(item.invoiceId!,
                                                      textColor: buttonThemeColor,
                                                      fontSize: textSizeMedium,
                                                      fontFamily: fontSemibold,
                                                      isCentered: false,
                                                      isLongText: true),
                                                ],
                                                text(
                                                    item.tableName != null && item.customerName != null
                                                        ? item.tableName! + " - " + item.customerName!
                                                        : item.tableName != null && item.customerName == null
                                                            ? item.tableName!
                                                            : item.customerName != null && item.tableName == null
                                                                ? item.customerName!
                                                                : "",
                                                    textColor: buttonThemeColor,
                                                    fontSize: textSizeMedium,
                                                    fontFamily: fontSemibold,
                                                    isCentered: false,
                                                    isLongText: true),
                                              ],
                                            ),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                text(fnUtilities.checkQtyDoubleValue(double.parse(item.totalAmount ?? "0").toStringAsFixed(2)),
                                                    textColor: buttonThemeColor,
                                                    fontSize: textSizeMedium,
                                                    fontFamily: fontSemibold,
                                                    isCentered: false,
                                                    isLongText: true),
                                                Row(
                                                  children: [
                                                    IconButton(
                                                        onPressed: () async {
                                                          if (!isLock) {
                                                            isLock = true;
                                                            if (PermissionFunctions.checkPermission(100)) {
                                                              Navigator.push(
                                                                  context,
                                                                  MaterialPageRoute(
                                                                      builder: (context) =>
                                                                          ReportsViewPage(reports: item, tableName: "", screenName: "SalesQueue")));
                                                            }
                                                            isLock = false;
                                                          }
                                                        },
                                                        icon: Icon(Icons.preview)),
                                                    IconButton(
                                                        onPressed: () async {
                                                          setState(() {
                                                            if (saleID == null) {
                                                              saleID = item.salesId;
                                                            } else if (saleID != item.salesId && saleID != null) {
                                                              saleID = item.salesId;
                                                            } else {
                                                              saleID = null;
                                                            }
                                                          });
                                                        },
                                                        icon: Icon(Icons.expand)),
                                                    popUpRevertWidget(item),

                                                    /// need to add revert option
                                                  ],
                                                ),
                                              ],
                                            ),
                                          ],
                                        ),

                                        /// show content while
                                        saleID == item.salesId
                                            ? Column(
                                                children: [
                                                  SizedBox(
                                                    height: 10,
                                                  ),
                                                  Divider(
                                                    thickness: .5,
                                                  ),
                                                  Container(
                                                    width: width / 1.1,
                                                    height: 50,
                                                    child: SingleChildScrollView(
                                                      scrollDirection: Axis.horizontal,
                                                      child: Row(
                                                        //mainAxisAlignment: MainAxisAlignment.center,
                                                        children: [
                                                          InkWell(
                                                            onTap: () async {
                                                              if (!isCollectBillLock) {
                                                                isCollectBillLock = true;
                                                                //int result = await saveDialog(context, item, "save");
                                                                int result = await commonSalesFunction.saveDialog(context, item, "save", isBigLayout,
                                                                    saleStatus: SalesStatus.completebill);
                                                                await assignNotCompletedSale();
                                                                if (successDialogOnSales && result == 1) {
                                                                  callSuccessDialog(context, item.salesId ?? "");
                                                                }
                                                                isCollectBillLock = false;
                                                              }
                                                            },
                                                            child: Container(
                                                              child: Row(
                                                                children: [
                                                                  Text(
                                                                    "Complete Bill",
                                                                    style: TextStyle(fontSize: 12, color: whiteColor),
                                                                  ),
                                                                  SizedBox(
                                                                    width: 2,
                                                                  ),
                                                                  Icon(
                                                                    Icons.save,
                                                                    color: whiteColor,
                                                                  ),
                                                                ],
                                                              ),
                                                              padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                              decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            width: 10,
                                                          ),
                                                          InkWell(
                                                            onTap: () async {
                                                              if (!isCollectBillPrintLock) {
                                                                isCollectBillPrintLock = true;
                                                                int result = await commonSalesFunction.saveDialog(context, item, "print", isBigLayout,
                                                                    saleStatus: SalesStatus.completebill);
                                                                await assignNotCompletedSale();
                                                                if (successDialogOnSales && result == 1) {
                                                                  callSuccessDialog(context, item.salesId ?? "");
                                                                }
                                                                isCollectBillPrintLock = false;
                                                              }
                                                            },
                                                            child: Container(
                                                              child: Row(
                                                                children: [
                                                                  Text(
                                                                    "Complete Bill &",
                                                                    style: TextStyle(fontSize: 12, color: whiteColor),
                                                                  ),
                                                                  SizedBox(
                                                                    width: 2,
                                                                  ),
                                                                  Icon(
                                                                    Icons.print,
                                                                    color: whiteColor,
                                                                  ),
                                                                ],
                                                              ),
                                                              padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                              decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                                                            ),
                                                          ),
                                                          SizedBox(
                                                            width: 10,
                                                          ),

                                                          /// Sales Revert Button
                                                          // popUpRevertWidget(item),
                                                          // InkWell(
                                                          //   onTap: () async {
                                                          //     await revertSalesWidget(item);
                                                          //   },
                                                          //   child: Container(
                                                          //     child: Row(
                                                          //       children: [
                                                          //         Text(
                                                          //           "Revert",
                                                          //           style: TextStyle(fontSize: 12, color: whiteColor),
                                                          //         ),
                                                          //         SizedBox(
                                                          //             width: 2,
                                                          //           ),
                                                          //           Icon(
                                                          //             Icons.edit,
                                                          //             size: 20.0,
                                                          //             color: whiteColor,
                                                          //           ),
                                                          //       ],
                                                          //     ),
                                                          //     padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                          //     decoration:
                                                          //         boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),

                                                          //   ),
                                                          // )
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ],
                                              )
                                            : Container(),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            );
                          })),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            )
          : Center(
              child: Text(
                "No pending sales available",
                style: headingTextStyle3,
              ),
            ),
    );
  }

  Widget advanceOrderWidget() {
    return Expanded(
      child: filterSalesAdvanceList.isNotEmpty
          ? RefreshIndicator.adaptive(
              onRefresh: () async {
                await initPagePrefrenceFunction(selectIndex);
                searchController.clear();
              },
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Column(
                        children: [
                          Align(
                            alignment: Alignment.topCenter,
                            child: Wrap(
                                // padding: EdgeInsets.only(top: 5.0),
                                children: List.generate(filterSalesAdvanceList.length, (index) {
                              var item = filterSalesAdvanceList[index];
                              List<Transactions> tempSaleProductList = [];

                              tempSaleProductList = advanceOrderProductList.where((element) => element.salesId == item.salesId).toList();

                              return GestureDetector(
                                child: Card(
                                  child: Container(
                                    width: width > tabletWidth ? width * 0.3 : width,
                                    padding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
                                    child: InkWell(
                                      onTap: () async {
                                        handleSaleIDChange(item);
                                      },
                                      child: Column(
                                        children: <Widget>[
                                          /// show customer name and product details
                                          Column(
                                            children: [
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  if (item.invoiceId != null) ...[
                                                    text(item.invoiceId!,
                                                        textColor: buttonThemeColor,
                                                        fontSize: textSizeMedium,
                                                        fontFamily: fontSemibold,
                                                        isCentered: false,
                                                        isLongText: true),
                                                  ],
                                                  text(item.customerName ?? '',
                                                      textColor: buttonThemeColor,
                                                      fontSize: textSizeMedium,
                                                      fontFamily: fontSemibold,
                                                      isCentered: false,
                                                      isLongText: true),
                                                ],
                                              ),
                                              Row(
                                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                children: [
                                                  text(
                                                      fnUtilities
                                                          .checkQtyDoubleValue(double.parse(item.totalAmount ?? "0").round().toStringAsFixed(1)),
                                                      textColor: buttonThemeColor,
                                                      fontSize: textSizeMedium,
                                                      fontFamily: fontSemibold,
                                                      isCentered: false,
                                                      isLongText: true),
                                                  Row(
                                                    children: [
                                                      IconButton(
                                                          onPressed: () async {
                                                            if (!isLock) {
                                                              isLock = true;
                                                              if (PermissionFunctions.checkPermission(100)) {
                                                                Navigator.push(
                                                                    context,
                                                                    MaterialPageRoute(
                                                                        builder: (context) => ReportsViewPage(
                                                                              reports: item,
                                                                              tableName: "",
                                                                              screenName: "SalesQueue",
                                                                              isAdvanceOrderEnabled: isEnableAdvanceOrder,
                                                                            )));
                                                              }
                                                              isLock = false;
                                                            }
                                                          },
                                                          icon: Icon(Icons.preview)),
                                                      IconButton(
                                                          onPressed: () async {
                                                            handleSaleIDChange(item);
                                                          },
                                                          icon: Icon(Icons.expand)),
                                                      popUpAdvanceEditDeleteWidget(item),
                                                    ],
                                                  ),
                                                ],
                                              ),
                                            ],
                                          ),

                                          /// show content while
                                          saleID == item.salesId
                                              ? Column(
                                                  children: [
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    if (advanceOrderProductList.isNotEmpty)
                                                      Container(
                                                        alignment: Alignment.centerLeft,
                                                        child: Row(
                                                          children: [
                                                            Expanded(
                                                              child: Container(
                                                                child: SingleChildScrollView(
                                                                  scrollDirection: Axis.horizontal,
                                                                  child: Wrap(
                                                                    children: [
                                                                      for (int i = 0; i < tempSaleProductList.length; i++)
                                                                        text(tempSaleProductList[i].name! + ".",
                                                                            textColor: primaryTextColor,
                                                                            fontSize: textSizeSmall,
                                                                            fontFamily: fontSemibold,
                                                                            isCentered: false,
                                                                            isLongText: false)
                                                                    ],
                                                                  ),
                                                                ),
                                                              ),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    SizedBox(
                                                      height: 5,
                                                    ),
                                                    Divider(
                                                      thickness: .5,
                                                    ),
                                                    if (item.deliveryDateAndTime != null)
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Expanded(
                                                            child: RichText(
                                                              text: TextSpan(
                                                                children: [
                                                                  TextSpan(text: 'Delivery Date : ', style: black15BoldTextStyle),
                                                                  TextSpan(
                                                                    text: AdvanceOrderWidget.convertDateTime(item.deliveryDateAndTime ?? '',
                                                                        hideLabel: true),
                                                                    style: TextStyle(
                                                                      fontWeight: FontWeight.normal,
                                                                      fontSize: 16.0,
                                                                      color: Colors.black,
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                              overflow: TextOverflow.ellipsis,
                                                              softWrap: false,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    Divider(
                                                      thickness: .5,
                                                    ),
                                                    if (orderNote != '' && saleID == item.salesId) ...[
                                                      Row(
                                                        mainAxisAlignment: MainAxisAlignment.start,
                                                        children: [
                                                          Text(
                                                            'Note : ',
                                                            style: black15BoldTextStyle,
                                                          ),
                                                          Expanded(
                                                            child: Text(
                                                              orderNote ?? '',
                                                              overflow: TextOverflow.clip,
                                                              softWrap: true,
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                      Divider(
                                                        thickness: .5,
                                                      ),
                                                    ],
                                                    Container(
                                                      width: width / 1.1,
                                                      height: 50,
                                                      child: SingleChildScrollView(
                                                        scrollDirection: Axis.horizontal,
                                                        child: Row(
                                                          children: [
                                                            InkWell(
                                                              onTap: () async {
                                                                if (!isCollectBillLock) {
                                                                  isCollectBillLock = true;
                                                                  await getPaymentDetails(item.salesId ?? '');
                                                                  await assignAdvanceOrderProductList();
                                                                  int result = await commonSalesFunction.saveDialog(
                                                                      context, item, "save", isBigLayout,
                                                                      saleStatus: SalesStatus.advanceOrder, paymentMapList: paymentMappingList);
                                                                  if (result == 1) {
                                                                    await initPagePrefrenceFunction(selectIndex);
                                                                  }
                                                                  if (successDialogOnSales && result == 1) {
                                                                    callSuccessDialog(context, item.salesId ?? "");
                                                                  }
                                                                  isCollectBillLock = false;
                                                                }
                                                              },
                                                              child: Container(
                                                                child: Row(
                                                                  children: [
                                                                    Text(
                                                                      "Complete Bill",
                                                                      style: TextStyle(fontSize: 12, color: whiteColor),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 2,
                                                                    ),
                                                                    Icon(
                                                                      Icons.save,
                                                                      color: whiteColor,
                                                                    ),
                                                                  ],
                                                                ),
                                                                padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                                decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              width: 10,
                                                            ),
                                                            InkWell(
                                                              onTap: () async {
                                                                if (!isCollectBillPrintLock) {
                                                                  isCollectBillPrintLock = true;
                                                                  await getPaymentDetails(item.salesId ?? '');
                                                                  await assignAdvanceOrderProductList();
                                                                  int result = await commonSalesFunction.saveDialog(
                                                                      context, item, "print", isBigLayout,
                                                                      saleStatus: SalesStatus.advanceOrder, paymentMapList: paymentMappingList);
                                                                  if (result == 1) {
                                                                    await initPagePrefrenceFunction(selectIndex);
                                                                  }
                                                                  if (successDialogOnSales && result == 1) {
                                                                    callSuccessDialog(context, item.salesId ?? "");
                                                                  }
                                                                  isCollectBillPrintLock = false;
                                                                }
                                                              },
                                                              child: Container(
                                                                child: Row(
                                                                  children: [
                                                                    Text(
                                                                      "Complete Bill &",
                                                                      style: TextStyle(fontSize: 12, color: whiteColor),
                                                                    ),
                                                                    SizedBox(
                                                                      width: 2,
                                                                    ),
                                                                    Icon(
                                                                      Icons.print,
                                                                      color: whiteColor,
                                                                    ),
                                                                  ],
                                                                ),
                                                                padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                                                                decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                                                              ),
                                                            ),
                                                            SizedBox(
                                                              width: 10,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                    ),
                                                  ],
                                                )
                                              : Container(),
                                          // Divider(height: 0.5, color: t5ViewColor)
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            })),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            )
          : Center(
              child: Text(
                "No advance orders available",
                style: headingTextStyle3,
              ),
            ),
    );
  }

  popUpAdvanceEditDeleteWidget(item) {
    return PopupMenuButton<String>(
      onSelected: (String result) async {
        if (result == 'delete') {
          await deleteAdvanceSalesWidget(item);
        } else if (result == 'edit') {
          var isLock = false;
          if (!isLock) {
            isLock = true;
            List<Transactions> filteredItems = [];
            filteredItems.clear();

            filteredItems = advanceOrderProductList.where((element) => element.salesId == item.salesId).toList();
            advanceOrderProductList.addAll(filteredItems);

            await getPaymentDetails(item.salesId);
            await getOrderNotesList(item.salesId ?? '');
            log(orderNote);
            Navigator.push(
                context,
                MaterialPageRoute(
                    builder: (context) => saleLayout == "SaleLayout2"
                        ? ItemsPageLayout2(
                            sales: item,
                            saleType: "AO",
                            saleTransactionList: filteredItems,
                            paymentMapList: paymentMappingList,
                            orderNotes: orderNote,
                          )
                        : SalesMainPage(
                            sales: item,
                            saleType: "AO",
                            saleTransactionList: filteredItems,
                            paymentMapList: paymentMappingList,
                            orderNotes: orderNote,
                          ))).then((value) async {
              // if (value) {
              saleID = null;
              await getAllAdvanceOrderSales();
              await initPagePrefrenceFunction(selectIndex);
              // }
            });
            isLock = false;
          }
          ;
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem(
            value: 'edit',
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Edit",
                  style: TextStyle(fontSize: 12, color: Colors.black),
                ),
                SizedBox(
                  width: 2,
                ),
                Icon(
                  Icons.edit,
                  size: 20.0,
                ),
              ],
            )),
        PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Delete",
                style: TextStyle(fontSize: 12, color: Colors.black),
              ),
              SizedBox(
                width: 2,
              ),
              Icon(
                Icons.delete_forever_rounded,
                size: 20.0,
                color: Colors.red,
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///Delete sales dialog widget
  deleteAdvanceSalesWidget(Sales salesModel) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return DeleteDialog(
            message: "Are you sure you want to delete this advance order?",
            buttonNameOne: "Yes",
            onPressOne: () async {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                await salesDetailsDBScript.updateSalesDelete(salesModel.salesId ?? "");
                await salesTransDBScript.updateSalesTransactionDelete(salesModel.salesId ?? "");
                await advanceOrderDB.updateNotesStatues(salesModel.salesId ?? '');
                Navigator.pop(context);
                await initPagePrefrenceFunction(selectIndex);

                isLock = false;
              }
            },
            buttonNameTwo: "No",
            onPressTwo: () {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  revertSalesWidget(Sales salesModel) {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return DeleteDialog(
            message: "Are you sure you want to revert this order?",
            buttonNameOne: "Yes",
            onPressOne: () async {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                List<Transactions> salesTransactionList = [];
                salesTransactionList = await getSalesTransactiondetails(salesModel.salesId ?? "") ?? [];
                await revertSalesQueue(salesModel, salesTransactionList);
                await initFunction();
                await assignNotCompletedSale();
                Navigator.pop(context);
                isLock = false;
              }
            },
            buttonNameTwo: "Cancel",
            onPressTwo: () {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  popUpRevertWidget(item) {
    return PopupMenuButton<String>(
      onSelected: (String result) async {
        if (result == 'revert') {
          await revertSalesWidget(item);
        }
      },
      itemBuilder: (BuildContext context) => <PopupMenuEntry<String>>[
        PopupMenuItem<String>(
          value: 'revert',
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Revert",
                style: TextStyle(fontSize: 12, color: Colors.black),
              ),
              SizedBox(
                width: 2,
              ),
              Icon(
                Icons.edit,
                size: 20.0,
                color: Colors.black,
              ),
            ],
          ),
        ),
      ],
    );
  }

  ///search widget
  Widget searchWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            child: Card(
              color: white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(15.0), // Set border radius
              ),
              child: SizedBox(
                height: 45,
                child: TextField(
                  onChanged: searchSalesItem,
                  onTapOutside: (event) {
                    FocusManager.instance.primaryFocus?.unfocus();
                  },
                  style: TextStyle(color: Colors.black),
                  autofocus: false,
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.fromLTRB(12, 12, 12, 0),
                    hintText: "Search",
                    border: InputBorder.none,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: const BorderSide(color: grey, width: 0.0),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                      borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                    ),
                    suffixIcon: InkWell(
                      onTap: () {
                        setState(() {
                          searchController.clear();
                          searchSalesItem("");
                          FocusScope.of(context).unfocus();
                        });
                      },
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.asset(
                          searchIconSVG,
                          color: greyColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  callSuccessDialog(BuildContext context, String salesId) async {
    await fnUtilities.salesSuccessDialog(context, salesId);
  }
}
