// ignore_for_file: non_constant_identifier_names, must_be_immutable, sdk_version_ui_as_code

import 'dart:io';
import 'package:nb_utils/nb_utils.dart';
import 'package:flutter/material.dart' hide Image;
import 'package:smartbill/model/sale_model.dart';
import 'package:smartbill/screens/item_layouts/sales_main_page.dart';
import 'package:smartbill/screens/products/add_product_page.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/product_card_widget.dart';
import 'package:smartbill/utils/common_widgets/product_list_view_widget.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import '../../../main.dart';
import '../../model/card_models/card_model.dart';
import '../../model/card_models/category_card_model.dart';
import '../../model/sales_payment_mapping.dart';
import '../../utils/common_widgets/common_alert_dialogue/amount_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/dynamic_quantity_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/topping_dialog.dart';
import '../../utils/common_widgets/common_alert_dialogue/unit_measurement_dialog.dart';
import '../../utils/common_widgets/common_panel_widgets/category_panel_widget.dart';
import '../../utils/common_widgets/common_panel_widgets/normal_panel_widget.dart';
import '../../utils/common_widgets/common_panel_widgets/number_panel_widget.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/price_type_db_script.dart';
import '../../utils/dbutils/product_details_db_script.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/dbutils/stocks_db_script.dart';
import '../../utils/dbutils/toppings_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../expenses/functions/stocks_functions.dart';

class ItemsPageLayout2 extends StatefulWidget {
  static var tag = "/ItemsPage";
  Sales? sales;
  String? saleType;
  List<Transactions>? saleTransactionList;
  List<SalesPaymentMapping>? paymentMapList;
  String? orderNotes;
  ItemsPageLayout2({this.sales, this.saleTransactionList, this.saleType, this.paymentMapList, this.orderNotes});
  @override
  ItemsPageLayout2State createState() => ItemsPageLayout2State(sales, saleTransactionList, saleType, paymentMapList);
}

class ItemsPageLayout2State extends State<ItemsPageLayout2> {
  ItemsPageLayout2State(this.sales, this.saleTransactionList, this.saleType, this.paymentMapList);
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  FnUtilities fnUtilities = new FnUtilities();
  Validator validator = new Validator();
  StocksDBScript stocksDBScript = StocksDBScript();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
  ToppingsDBScript toppingsDBscript = ToppingsDBScript();
  StocksFunction stocksFunction = StocksFunction();

  final qtyController = new TextEditingController();
  final priceController = new TextEditingController();
  final customerNameController = new TextEditingController();
  final givenAmountController = new TextEditingController();
  final holdSalesController = new TextEditingController();
  final searchController = TextEditingController();
  final dbHelper = DatabaseHelper.instance;
  List<Products> filterProductList = [];
  List<ProductKey> productKeyList = [];
  List<PriceUnitType> productUnitList = [];
  List<Topping> productToppingList = [];
  List<Transactions>? saleTransactionList = [];
  List<SalesPaymentMapping>? paymentMapList = [];
  bool isLock = false;
  double width = 0.0;
  double height = 0.0;
  String? panels;
  String prodId = '';
  String prodKey = '';
  String prodName = '';
  int productsRowCount = 0;
  bool isDynamicQuantity = false;
  double dynamicQuantity = 1;
  bool useMobileLayout = false;
  Sales? sales;
  // bool showInventory = false;
  // String inventoryType;
  bool allowNegativeSale = false;
  double totalQty = 0;
  String? saleType;
  bool successDialogOnSales = false;
  bool isProductLoading = false;
  bool isFavorite = false;
  bool showImages = true;
  bool isBigLayout = false;
  String? saleLayout;

  List<Products> productsToAdd = [];
  List<CardModel> cardItemList = [];
  List<CategoryCardModel> categoryListItem = [];
  assignProductList() async {
    List<Products> productList = List.from(ListUtility.productList.map((p) => Products.fromJson(p.toJson())));
    if (productList.isNotEmpty) {
      filterProductList.clear();
      for (Products p in productList) {
        if (p.productFor == null || p.productFor?.name == "sale" || p.productFor?.name == "both") {
          filterProductList.add(p);
        }
      }
      filterProductList.add(Products());
      panelCardShow(List.from(filterProductList.map((p) => Products.fromJson(p.toJson()))));
      await queryAllProductKeys();
    }
  }

  ///To be used to show the panel card
  panelCardShow(List<Products> itemList) {
    cardItemList.clear();
    for (Products model in itemList) {
      cardItemList.add(CardModel(
          id: model.productSaleId,
          guidId: model.productId,
          name: model.name,
          price: model.price ?? "",
          imagePath: model.imagePath,
          count: model.count,
          isShowImage: showImages));
    }
  }

  //GET PRODUCT DETAILS BY Key
  queryAllProductKeys() async {
    setState(() {
      if (ListUtility.productKeyList.isNotEmpty) {
        productKeyList.clear();
        productKeyList.addAll(ListUtility.productKeyList);
        productKeyList.insert(0, ProductKey(id: 0, keywordName: "All"));
        categoryCardShow(productKeyList);
      }
    });
  }

  categoryCardShow(List<ProductKey> keyListItem) {
    categoryListItem.clear();
    for (ProductKey model in keyListItem) {
      categoryListItem.add(CategoryCardModel(id: model.id, guidId: model.productKeyId, categoryName: model.keywordName));
    }
  }

  getAllProductUnitsDetails(String? productId) async {
    try {
      if (productId != null) {
        if (ListUtility.unitList.isNotEmpty) {
          productUnitList.clear();
          for (PriceUnitType unitType in ListUtility.unitList) {
            if (unitType.productId == productId) {
              setState(() {
                productUnitList.add(unitType);
              });
            }
          }
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getAllProductUnitsDetails - ItemsPageLayout2");
    }
  }

  //GET PRODUCT Topping DETAILS FROM DB
  queryAllProductToppingDetails(String? toppingId) async {
    try {
      if (toppingId != null) {
        if (ListUtility.toppingsList.isNotEmpty) {
          productToppingList.clear();
          setState(() {
            for (Topping t in ListUtility.toppingsList) {
              if (t.toppingGroupId == toppingId) {
                t.isActive = false;
                productToppingList.add(t);
              }
            }
          });
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getAllProductUnitsDetails - ItemsPageLayout2");
    }
  }

  Future<List<Products>> assignFilePath(List<Products> productList) async {
    for (Products p in productList) {
      if (!p.image.isEmptyOrNull) {
        p.imagePath = await fnUtilities.readFileFromLocal(p.image);
      }
    }
    return productList;
  }

  initSettings() async {
    setState(() {
      ///Workspace settings
      showImages = fnUtilities.workSpaceSetValues(key_showproductImages) == "1";
      successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
      allowNegativeSale = fnUtilities.workSpaceSetValues(key_allowNegativeSales) == "1";
      // inventoryType = fnUtilities.workSpaceSetValues(key_inventoryType);
      // if (inventoryType == "All") {
      //   inventoryType = "All";
      // } else {
      //   inventoryType = "Specific";
      // }
      // showInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1";
      isDynamicQuantity = fnUtilities.workSpaceSetValues(key_dynamicQuantity) == "1";

      ///Workspace device setting
      MyApp.isListViewEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductListview) == "1";
      MyApp.isFilterEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductTapOnClear) == "1";
      panels = fnUtilities.workSpaceDeviceSetValue(key_panelType);
      isFavorite = fnUtilities.workSpaceDeviceSetValue(key_favoriteProduct) == "1";

      ///workspace user settings
      saleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
      if (saleLayout == "SaleLayout2" || saleLayout == "SaleLayout3") {
        saleLayout = "SaleLayout2";
      } else if (saleLayout == "DesktopLayout") {
        saleLayout = "DesktopLayout";
      } else {
        saleLayout = "SaleLayout1";
      }
    });
  }

  //CALCULATE TOTAL Qty
  findTotalQty() {
    if (this.mounted) {
      setState(() {
        totalQty = 0.0;
        for (Transactions p in saleTransactionList ?? []) {
          if (p.toppingId == null) {
            totalQty += double.parse(p.qty!);
          }
        }
      });
    }
  }

  //GET PRODUCT DETAILS BY ID FROM LIST
  getProductDetailsById(String prodId) {
    for (Products p in ListUtility.productList) {
      if (p.productId == prodId) {
        return p;
      }
    }
  }

  //SET PRODUCT ID DETAILS
  setProdId(String id) async {
    setState(() {
      prodId += id;
      filterProductList.clear();
      filterProductsByID(prodId);
    });
    await loopForProductQty(saleTransactionList ?? []);
  }

  //SET PRODUCT KEY DETAILS
  setProdKey(String key, String keyName) async {
    setState(() {
      filterProductList.clear();
      filterProductsByKeyword(key, keyName);
    });
    await loopForProductQty(saleTransactionList ?? []);
  }

  //GET FILTER PRODUCT FROM LIST
  filterProductsByID(String id) async {
    if (id == "") {
      setState(() {
        filterProductList.addAll(ListUtility.productList);
        panelCardShow(filterProductList);
      });
    } else {
      for (Products p in ListUtility.productList) {
        if (p.productSaleId != null && p.productSaleId.toString().contains(prodId)) {
          setState(() {
            filterProductList.add(p);
            panelCardShow(filterProductList);
          });
        }
      }
    }
    setState(() {
      filterProductList.add(Products());
      panelCardShow(filterProductList);
    });
    await loopForProductQty(saleTransactionList ?? []);
  }

  //GET FILTER PRODUCT FROM LIST
  filterProductsByKeyword(String key, String keyName) async {
    if (keyName.toLowerCase() == "all") {
      setState(() {
        filterProductList.addAll(ListUtility.productList);
        panelCardShow(filterProductList);
      });
    } else {
      for (Products p in ListUtility.productList) {
        if (p.keyID != null && p.keyID.toString().contains(key) || p.productKeyName == keyName) {
          setState(() {
            filterProductList.add(p);
            panelCardShow(filterProductList);
          });
        }
      }
    }
    setState(() {
      filterProductList.add(Products());
      panelCardShow(filterProductList);
    });
    await loopForProductQty(saleTransactionList ?? []);
  }

  //GET PRODUCT DETAILS BY NAME
  void filterProductsByName(String name) async {
    try {
      setState(() {
        prodName = name;
        filterProductList.clear();
        for (Products p in ListUtility.productList) {
          if (p.name != null && p.name!.toLowerCase().contains(name.toLowerCase())) {
            setState(() {
              filterProductList.add(p);
              panelCardShow(filterProductList);
            });
          }
        }
      });
      setState(() {
        filterProductList.add(Products());
        panelCardShow(filterProductList);
      });
      await loopForProductQty(saleTransactionList ?? []);
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "filterProductsByName - ItemsPageLayout2");
    }
  }

  //ADD PRODUCT IN SALES LIST
  addProductsInSalesList1(Products? item) async {
    int gridIndex = -1;
    double amount;
    try {
      if (item != null) {
        for (int i = 0; i < saleTransactionList!.length; i++) {
          var transaction = saleTransactionList![i];
          for (Topping t in productToppingNewList) {
            if (t.toppingId == transaction.toppingId && double.parse(t.toppingPrice ?? "0.0") == double.parse(transaction.actualPrice ?? "0.0")) {
              gridIndex = i;
              amount = (double.parse(transaction.qty ?? "0") + 1) * double.parse(t.toppingPrice ?? "0");
              saleTransactionList?[i] = Transactions(
                id: transaction.id,
                transactId: transaction.transactId,
                salesId: transaction.salesId,
                prodId: transaction.prodId,
                productType: "Topping",
                toppingId: t.toppingId,
                name: t.toppingName,
                price: t.toppingPrice,
                actualPrice: t.toppingPrice,
                cGst: transaction.cGst,
                sGst: transaction.sGst,
                qty: (double.parse(transaction.qty ?? "0") + 1).toString(),
                amount: amount.toString(),
              );
            }
          }
        }
      }

      if (gridIndex == -1) {
        if (productToppingNewList.length != 0) {
          for (Topping t in productToppingNewList) {
            saleTransactionList?.add(Transactions(
              prodId: item?.productId,
              productType: "Topping",
              toppingId: t.toppingId,
              name: t.toppingName,
              qty: "1",
              price: t.toppingPrice,
              actualPrice: t.toppingPrice,
              amount: t.toppingPrice,
              cGst: item?.cGst,
              sGst: item?.sGst,
            ));
          }
        }
      }
      await findTotalQty();
      productToppingNewList.clear();
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "addProductsInSalesList1 - ItemsPageLayout2");
    }
  }

  //ADD PRODUCT IN SALES LIST
  addProductsInSalesList(Products? item, PriceUnitType? unit) async {
    try {
      if (item != null) {
        if (await stocksFunction.checkIsStockAvailable(item, saleTransactionList ?? [], dynamicQuantity.round())) {
          int gridIndex = -1;
          String price;
          double amount;
          if (item.priceType == null) {
            if (item.price == null || item.price == "") {
              price = priceController.text;
            } else {
              price = item.price ?? "0";
            }
          } else {
            if (item.priceType == 0 || item.price == "" || item.price == null) {
              price = priceController.text;
            } else {
              price = item.price ?? "0";
            }
          }
          if (unit != null) {
            unit = PriceUnitType(
                id: unit.id,
                priceTypeId: unit.priceTypeId,
                productId: unit.productId,
                measurement: unit.measurement,
                measurementPrice: unit.measurementPrice,
                isActive: unit.isActive,
                workspaceId: unit.workspaceId,
                measurementSync: unit.measurementSync,
                rowStatus: unit.rowStatus);
            if (unit.measurementPrice == "" || unit.measurementPrice == null) {
              unit.measurementPrice = priceController.text;
            }
          }
          if (item.priceType != 2) {
            for (int i = 0; i < saleTransactionList!.length; i++) {
              var transaction = saleTransactionList![i];
              if (item.productId == transaction.prodId &&
                  double.parse(price) == double.parse(transaction.actualPrice ?? "0") &&
                  transaction.toppingId == null) {
                gridIndex = i;
                amount = (double.parse(transaction.qty ?? "0") + dynamicQuantity) * double.parse(price);
                saleTransactionList![i] = Transactions(
                    id: transaction.id,
                    transactId: transaction.transactId,
                    salesId: transaction.salesId,
                    prodId: item.productId,
                    productType: "Product",
                    name: item.name,
                    price: price,
                    actualPrice: price,
                    cGst: item.cGst,
                    sGst: item.sGst,
                    qty: (double.parse(transaction.qty ?? "0") + dynamicQuantity).toString(),
                    amount: amount.toString(),
                    isActiveSale: transaction.isActiveSale);
              }
            }
          } else {
            for (int i = 0; i < saleTransactionList!.length; i++) {
              var transaction = saleTransactionList![i];
              if (unit?.productId == transaction.prodId &&
                  unit?.priceTypeId == transaction.priceTypeId &&
                  double.parse(unit?.measurementPrice ?? "0") == double.parse(transaction.actualPrice ?? "0")) {
                gridIndex = i;
                amount = (double.parse(transaction.qty ?? "0") + dynamicQuantity) * double.parse(unit?.measurementPrice ?? "0");
                String name = item.name! + '-' + unit!.measurement!;
                saleTransactionList![i] = Transactions(
                    id: transaction.id,
                    transactId: transaction.transactId,
                    salesId: transaction.salesId,
                    prodId: unit.productId,
                    productType: "Product",
                    name: name,
                    price: unit.measurementPrice,
                    actualPrice: unit.measurementPrice,
                    cGst: item.cGst,
                    sGst: item.sGst,
                    qty: (double.parse(transaction.qty ?? "0") + dynamicQuantity).toString(),
                    amount: amount.toString(),
                    isActiveSale: transaction.isActiveSale);
              }
            }
          }

          if (gridIndex == -1) {
            if (item.priceType != 2) {
              saleTransactionList?.add(Transactions(
                prodId: item.productId,
                productType: "Product",
                name: item.name,
                qty: dynamicQuantity.toString(),
                price: price,
                actualPrice: price,
                amount: price,
                cGst: item.cGst,
                sGst: item.sGst,
              ));
            } else {
              String name = item.name! + '-' + unit!.measurement!;
              saleTransactionList?.add(Transactions(
                  prodId: item.productId,
                  productType: "Product",
                  name: name,
                  qty: dynamicQuantity.toString(),
                  price: unit.measurementPrice,
                  actualPrice: unit.measurementPrice,
                  amount: unit.measurementPrice,
                  cGst: item.cGst,
                  sGst: item.sGst,
                  priceTypeId: unit.priceTypeId));
            }
          }
          priceController.clear();
          await findTotalQty();
          dynamicQuantity = 1;
          print("length + ${saleTransactionList!.length}");
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "addProductsInSalesList - ItemsPageLayout2");
    }
  }

  getInitSalesPage() async {
    try {
      await assignProductList();
      await initSettings();
      await assignTransactionList();
    } on Exception catch (e) {
      print(e);
    }
  }

  assignTransactionList() async {
    if (saleTransactionList != null) {
      await loopForProductQty(saleTransactionList ?? []);
      await findTotalQty();
    } else {
      saleTransactionList = [];
    }
  }

  commonOnTapProduct(Products product, int index) async {
    try {
      if (product.dynamicQuantity == 1) {
        if (product.priceType == 2) {
          getAllProductUnitsDetails(product.productId);
          await unitDialog(context, product, "dynamicQty");
        } else {
          await dynamicQtyDialog(context, product.toppingGroupId, product.price, product, null);
        }
        await productCountIncrement(product.productId);
      } else {
        if (product.priceType == null) {
          if (product.price == null || product.price == "") {
            await AmountDialog(context, product);
          } else {
            await addProductsInSalesList(product, null);
            if (product.toppingGroupId != null) {
              await queryAllProductToppingDetails(product.toppingGroupId);
              if (productToppingList.length > 0) {
                await toppingDialog(context, product);
              }
            }
          }
        } else {
          if (product.priceType == 0) {
            await AmountDialog(context, product);
          } else if (product.priceType == 1) {
            await addProductsInSalesList(product, null);
            if (product.toppingGroupId != null) {
              await queryAllProductToppingDetails(product.toppingGroupId);
              if (productToppingList.length > 0) {
                await toppingDialog(context, product);
              }
            }
          } else {
            await getAllProductUnitsDetails(product.productId);
            await unitDialog(context, product, "normalQty");
          }
        }
        await productCountIncrement(product.productId);
      }
      if (MyApp.isFilterEnable) {
        await clearFunction();
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "commonOnTapProduct - ItemsPageLayout2");
    }
  }

  commonReduceProduct(Products item) {
    try {
      if (saleTransactionList != null) {
        Transactions transactions = saleTransactionList!.firstWhere((element) => element.prodId == item.productId, orElse: () => Transactions());
        if (transactions.salesId != null) {
          if (item.price == null || item.price == "") {
            int transactionIndex = saleTransactionList!.lastIndexWhere((element) => element.prodId == item.productId);
            Transactions model = saleTransactionList![transactionIndex];
            if (double.tryParse(model.qty ?? "0")! > 1) {
              model.qty = (double.parse(model.qty ?? "0") - 1).toString();
              saleTransactionList![transactionIndex] = model;
            } else {
              saleTransactionList!.removeAt(transactionIndex);
            }
          } else {
            if (double.tryParse(transactions.qty ?? "0")! > 1) {
              int transactionIndex = saleTransactionList!.indexWhere((element) => element.prodId == item.productId);
              transactions.qty = (double.parse(transactions.qty ?? "0") - 1).toString();
              saleTransactionList![transactionIndex] = transactions;
            } else {
              saleTransactionList!.removeWhere((element) => element.prodId == item.productId);
            }
          }
        } else {
          if (double.tryParse(transactions.qty ?? "0")! > 1) {
            int transactionIndex = saleTransactionList!.indexWhere((element) => element.prodId == item.productId);
            transactions.qty = (double.parse(transactions.qty ?? "0") - 1).toString();
            saleTransactionList![transactionIndex] = transactions;
          } else {
            saleTransactionList!.removeWhere((element) => element.prodId == item.productId);
          }
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "commonReduceProduct - ItemsPageLayout2");
    }
  }

  clearFunction() {
    if (prodKey != "") {
      prodKey = "";
      setProdKey("", '');
    }
    if (prodId != "") {
      prodId = "";
      setProdId("");
    }
    if (searchController.text != "") {
      searchController.text = "";
      filterProductsByName("");
    }
  }

  @override
  void initState() {
    getInitSalesPage();
    super.initState();
  }

  //DYNAMIC Unit ALERT DIALOG
  unitDialog(BuildContext context, Products item1, String dFlag) {
    bool isUnitLock = false;
    return showDialog(
        context: context,
        builder: (context) {
          return UnitMeasurementDialog(
            isBigLayout: isBigLayout,
            isSaleLayoutTwo: true,
            productUnitList: productUnitList,
            onTabItem: (value) async {
              if (!isUnitLock) {
                isUnitLock = true;
                if ("dynamicQty" == dFlag) {
                  Navigator.pop(context);
                  await dynamicQtyDialog(context, item1.toppingGroupId, value.measurementPrice, item1, value);
                } else if (value.measurementPrice == null || value.measurementPrice == "") {
                  Navigator.pop(context);
                  await AmountDialog(context, item1, unit: value);
                } else {
                  await addProductsInSalesList(item1, value);
                  Navigator.pop(context);
                  if (item1.toppingGroupId != null) {
                    await queryAllProductToppingDetails(item1.toppingGroupId);
                    if (productToppingList.length > 0) {
                      toppingDialog(context, item1);
                    }
                  }
                }
                isUnitLock = false;
              }
            },
          );
        });
  }

  List<Topping> productToppingNewList = [];
  //DYNAMIC Unit ALERT DIALOG
  toppingDialog(BuildContext context, Products? item1) {
    return showDialog(
        context: context,
        builder: (context) {
          return ToppingDialog(
            productToppingList: productToppingList,
            productToppingNewList: productToppingNewList,
            isBigLayout: false,
            onTabok: () async {
              if (!isLock) {
                isLock = true;
                await addProductsInSalesList1(item1);
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  //DYNAMIC PRICE ALERT DIALOG
  AmountDialog(BuildContext context, Products item, {PriceUnitType? unit}) {
    return showDialog(
        context: context,
        builder: (context) {
          return AmountAlertDialog(
            priceController: priceController,
            onPresOk: (value) async {
              value.validate();
              if (value.validate()) {
                Navigator.pop(context);
                if (unit != null) {
                  await addProductsInSalesList(item, unit);
                  await loopForProductQty(saleTransactionList ?? []);
                } else {
                  await addProductsInSalesList(item, null);
                  await loopForProductQty(saleTransactionList ?? []);
                }
                if (item.toppingGroupId != null) {
                  await queryAllProductToppingDetails(item.toppingGroupId);
                  if (productToppingList.length > 0) {
                    toppingDialog(context, item);
                  }
                }
              }
            },
          );
        });
  }

  //DYNAMIC QUANTITY ALERT DIALOG
  dynamicQtyDialog(BuildContext context, String? tGroupID, String? price, Products? item, PriceUnitType? unit) {
    qtyController.text = "1";
    priceController.text = price != null && price != "" ? price : "";
    return showDialog(
        context: context,
        builder: (context) {
          return DynamicQtyDialog(
            qtyController: qtyController,
            priceController: priceController,
            onTabOK: ((value1, value2) async {
              dynamicQuantity = double.parse(value1) > 0 ? double.parse(value1) : 1;
              await addProductsInSalesList(item, unit);
              await loopForProductQty(saleTransactionList ?? []);
              Navigator.pop(context);
              if (tGroupID != null) {
                await queryAllProductToppingDetails(tGroupID);
                if (productToppingList.length > 0) {
                  toppingDialog(context, item);
                }
              }
              qtyController.clear();
              priceController.clear();
            }),
          );
        });
  }

  panelSwitch() async {
    if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Normal") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Id";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Id") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Key";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    } else if (fnUtilities.workSpaceDeviceSetValue(key_panelType) == "Key") {
      settingsDetail.settingKey = key_panelType;
      settingsDetail.settingValue = "Normal";
      await fnUtilities.updateDeviceSettingDetails(settingsDetail);
    }
    setState(() {
      initSettings();
    });
  }

  productCountIncrement(productID) {
    try {
      if (this.mounted) {
        setState(() {
          List<Transactions> qtyList = saleTransactionList!.where((element) => element.toppingId == null && element.prodId == productID).toList();
          double qty = 0;
          for (Transactions qt in qtyList) {
            if (qt.qty != null) {
              qty += double.parse(qt.qty ?? "0");
            }
          }
          filterProductList.firstWhere((element) => element.productId == productID, orElse: () => Products()).count =
              qty != 0 ? double.parse(fnUtilities.checkQtyDoubleValue(qty.toString())) : null;
          cardItemList.firstWhere((element) => element.guidId == productID, orElse: () => CardModel()).count =
              qty != 0 ? double.parse(fnUtilities.checkQtyDoubleValue(qty.toString())) : null;
        });
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "productCountIncrement - ItemsPageLayout2");
    }
  }

  loopForProductQty(List<Transactions> saleTransaction) async {
    try {
      for (Products p in filterProductList) {
        p.count = null;
      }
      for (CardModel productCard in cardItemList) {
        productCard.count = null;
      }
      if (saleTransactionList!.isNotEmpty) {
        for (Transactions t in saleTransaction) {
          await productCountIncrement(t.prodId);
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "loopForProductQty - ItemsPageLayout2");
    }
  }

  conformationDialog(context) {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: ConfirmationAlertDialog(
                content: "Are you sure want to exit?",
                buttonNameOne: "Cancel",
                ontabButtonOne: () {
                  Navigator.pop(context, false);
                },
                buttonNameTwo: "Exit",
                ontabButtonTwo: () {
                  Navigator.pop(context, true);
                },
              ),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var shortestSide = MediaQuery.of(context).size.shortestSide;
    useMobileLayout = shortestSide < 600;
    isBigLayout = (width > tabletWidth && saleLayout == "DesktopLayout") || Platform.isWindows;

    // ignore: deprecated_member_use
    return WillPopScope(
      onWillPop: () async {
        if (saleTransactionList?.length != 0) {
          bool isExit = await conformationDialog(context);
          return isExit;
        } else {
          return true;
        }
      },
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        appBar: Platform.isWindows
            ? BaseAppBar(
                title: text("", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
                appBar: AppBar(),
                widgets: <Widget>[],
              )
            : null,
        body: Column(
          children: [
            SizedBox(height: 15),
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                    padding: EdgeInsets.only(bottom: 20),
                    width: width,
                    height: height,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        if (Platform.isIOS) ...[
                          SizedBox(
                            height: 25,
                          )
                        ],
                        Container(
                          width: width,
                          color: Colors.white10,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      InkWell(
                                        onTap: () async {
                                          if (saleTransactionList?.length != 0) {
                                            bool isExit = await conformationDialog(context);
                                            if (isExit) return Navigator.pop(context);
                                          } else {
                                            return Navigator.pop(context);
                                          }
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          padding: EdgeInsets.only(top: 12.0, left: 15.0),
                                          width: 35,
                                          child: Icon(Icons.arrow_back, color: blackColor),
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(top: 10.0, left: 20.0),
                                        child: TextWidget(
                                          "Total Qty: ",
                                          textColor: primaryTextColor,
                                          fontFamily: fontBold,
                                          fontSize: textSizeSMedium,
                                          isCentered: false,
                                        ),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.only(top: 10.0, left: 0.0),
                                        child: TextWidget("${fnUtilities.checkQtyDoubleValue(totalQty.toString())}",
                                            textColor: primaryTextColor,
                                            fontFamily: fontBold,
                                            // fontSize: textSizeNormal,
                                            isCentered: true),
                                      ),
                                    ],
                                  ),
                                  PopupMenuButton(
                                    padding: EdgeInsets.only(top: 10, left: 0.0),
                                    itemBuilder: (BuildContext context) {
                                      return <PopupMenuEntry>[
                                        if (PermissionFunctions.checkPermission(104, viewToast: false)) ...[
                                          PopupMenuItem(
                                            child: ListTile(
                                              title: Text("New Product"),
                                              leading: Icon(
                                                Icons.add,
                                                size: 20,
                                              ),
                                              onTap: () {
                                                if (!isLock) {
                                                  isLock = true;
                                                  Navigator.pop(context);
                                                  Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog()))
                                                      .then((value) async {
                                                    if (value ?? false) {
                                                      await clearFunction();
                                                      await fnUtilities.getAllProductDetails();
                                                      await assignProductList();
                                                      await loopForProductQty(saleTransactionList ?? []);
                                                      await findTotalQty();
                                                    }
                                                  });
                                                  isLock = false;
                                                }
                                              },
                                            ),
                                          ),
                                        ],
                                        PopupMenuItem(
                                          child: ListTile(
                                            title: Text("Switch Panel"),
                                            leading: Icon(
                                              Icons.swipe_outlined,
                                              size: 20,
                                            ),
                                            onTap: () async {
                                              if (!isLock) {
                                                isLock = true;
                                                await clearFunction();
                                                panelSwitch();
                                                Navigator.pop(context);
                                                isLock = false;
                                              }
                                            },
                                          ),
                                        ),
                                      ];
                                    },
                                  ),
                                ],
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Container(
                                    padding: const EdgeInsets.only(top: 5),
                                    width: width / 1.1,
                                    height: 50,
                                    child: TextField(
                                      controller: searchController,
                                      onChanged: filterProductsByName,
                                      style: TextStyle(color: Colors.black),
                                      autofocus: false,
                                      decoration: InputDecoration(
                                        labelText: 'Search',
                                        border: InputBorder.none,
                                        enabledBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(4),
                                          borderSide: const BorderSide(color: grey, width: 0.0),
                                        ),
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius: BorderRadius.circular(4),
                                          borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                                        ),
                                        suffixIcon: InkWell(
                                          onTap: () async {
                                            FocusScope.of(context).unfocus();
                                            if (searchController.text != "") {
                                              searchController.text = "";
                                              filterProductsByName("");
                                            }
                                          },
                                          child: new Icon(
                                            Icons.clear,
                                            color: primaryTextColor,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.fromLTRB(15, 5, 15, 0),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  panels != "Normal"
                                      ? InkWell(
                                          onTap: () async {
                                            if (!isLock) {
                                              isLock = true;
                                              await clearFunction();
                                              isLock = false;
                                            }
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            padding: EdgeInsets.all(5.0),
                                            width: 40,
                                            height: height / 20,
                                            child: Icon(Icons.cleaning_services),
                                          ),
                                        )
                                      : SizedBox(),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      if (!isLock) {
                                        isLock = true;
                                        await clearFunction();
                                        panelSwitch();
                                        isLock = false;
                                      }
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      padding: EdgeInsets.all(5.0),
                                      width: 40,
                                      height: height / 20,
                                      child: Icon(Icons.swipe_outlined),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  InkWell(
                                    onTap: () async {
                                      WorkspaceSettings settings = WorkspaceSettings();
                                      settings.settingKey = "FavoriteProduct";
                                      setState(() {
                                        if (isFavorite) {
                                          isFavorite = false;
                                          settings.settingValue = "0";
                                          fnUtilities.updateDeviceSettingDetails(settings);
                                        } else {
                                          isFavorite = true;
                                          settings.settingValue = "1";
                                          fnUtilities.updateDeviceSettingDetails(settings);
                                        }
                                      });
                                      if (!isLock) {
                                        isLock = true;
                                        await fnUtilities.getAllProductDetails();
                                        assignProductList();
                                        // filterProductList.clear();
                                        // filterProductList.addAll(ListUtility.productList);
                                        // filterProductList.add(Products());
                                        isLock = false;
                                      }
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      padding: EdgeInsets.all(5.0),
                                      width: 40,
                                      height: height / 20,
                                      child: isFavorite
                                          ? Icon(
                                              Icons.favorite_sharp,
                                              color: buttonThemeColor,
                                            )
                                          : Icon(Icons.favorite_outline),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 8,
                                  ),
                                  panels == "Id"
                                      ? Container(
                                          width: 60,
                                          child: TextWidget("$prodId",
                                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLargeMedium, isCentered: true),
                                        )
                                      : SizedBox(),
                                ],
                              ),
                              Row(
                                children: [
                                  GestureDetector(
                                    onTap: () async {
                                      if (!isLock) {
                                        if (saleTransactionList!.isNotEmpty) {
                                          isLock = true;
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) => SalesMainPage(
                                                        saleTransactionList: saleTransactionList ?? [],
                                                        sales: sales,
                                                        saleType: saleType,
                                                        orderType: 1,
                                                        paymentMapList: paymentMapList ?? [],
                                                      ))).then((value) async {
                                            if (value != null) {
                                              SaleModel model = value;
                                              if (model.isCompletedSales ?? false) {
                                                setState(() {
                                                  saleTransactionList = [];
                                                  sales = null;
                                                });
                                                await loopForProductQty(saleTransactionList ?? []);
                                                await findTotalQty();
                                                if (successDialogOnSales) {
                                                  await callSuccessDialog(context, model.fullSale?.salesId);
                                                }
                                              } else {
                                                sales = model.fullSale;
                                                saleType = model.saleType;
                                                saleTransactionList = model.transaction;
                                                if (saleTransactionList!.isNotEmpty) {
                                                  await loopForProductQty(saleTransactionList ?? []);
                                                  await findTotalQty();
                                                } else {
                                                  await assignProductList();
                                                }
                                              }
                                            }
                                          });
                                        }
                                        isLock = false;
                                      }
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(5.0),
                                      alignment: Alignment.center,
                                      child: TextWidget("COUNTER", textColor: secondaryTextColor, isCentered: true),
                                      decoration: boxDecoration(
                                          bgColor: saleTransactionList != null && saleTransactionList!.isNotEmpty ? buttonThemeColor : Colors.grey,
                                          radius: 8.0),

                                      // decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          height: 15,
                        ),
                        Expanded(
                            child: !isProductLoading
                                ? Row(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      panels == "Normal" ? normalPanelType() : Container(),
                                      panels == "Id" ? filterPanelType() : Container(),
                                      panels == "Key" ? filterKeyPanelType() : Container(),
                                    ],
                                  )
                                : Center(child: CircularProgressIndicator()))
                      ],
                    )),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget listProdItem(Products item, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 3.0),
      child: item.productId != null
          ? ProductListViewCard(
              item: item,
              onTab: () async {
                if (!isLock) {
                  isLock = true;
                  await commonOnTapProduct(item, index);
                  isLock = false;
                }
              },
              onLongPress: () async {
                await commonReduceProduct(item);
                await productCountIncrement(item.productId);
                await findTotalQty();
              },
            )
          : InkWell(
              onTap: () {
                if (!isLock) {
                  isLock = true;
                  Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog(productName: searchController.text)))
                      .then((value) async {
                    if (value) {
                      await clearFunction();
                      await fnUtilities.getAllProductDetails();
                      await assignProductList();
                      await loopForProductQty(saleTransactionList ?? []);
                      await findTotalQty();
                    }
                  });
                  isLock = false;
                }
              },
              child: Container(
                margin: const EdgeInsets.all(2.0),
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Icon(Icons.add),
                    SizedBox(width: 5),
                    TextWidget("New Product", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
                  ],
                ),
              ),
            ),
    );
  }

  Widget gridProdItem(Products item, int index) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4.0, vertical: 4.0),
      child: item.productId != null
          ? ProductCardWidget(
              item: item,
              showImages: showImages,
              isSaleLayoutTwo: true,
              onTab: () async {
                if (!isLock) {
                  isLock = true;
                  await commonOnTapProduct(item, index);
                  isLock = false;
                }
              },
              onLongPress: () async {
                await commonReduceProduct(item);
                await productCountIncrement(item.productId);
                await findTotalQty();
              },
            )
          : InkWell(
              onTap: () async {
                if (!isLock) {
                  isLock = true;
                  Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog(productName: searchController.text)))
                      .then((value) async {
                    if (value) {
                      await clearFunction();

                      await fnUtilities.getAllProductDetails();
                      await assignProductList();
                      await loopForProductQty(saleTransactionList ?? []);
                      await findTotalQty();
                    }
                  });
                  isLock = false;
                }
              },
              child: Stack(children: [
                Container(
                  padding: EdgeInsets.only(left: 0, right: 0),
                  decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Container(
                        width: width,
                        padding: EdgeInsets.all(5),
                        child: Icon(
                          Icons.add,
                          color: secondaryTextColor,
                        ),
                      ),
                      SizedBox(height: 2),
                      TextWidget("New Product",
                          textColor: secondaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)
                    ],
                  ),
                ),
              ]),
            ),
    );
  }

  onTapAddNewProduct() {
    if (!isLock) {
      isLock = true;
      Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog(productName: searchController.text))).then((value) async {
        if (value ?? false) {
          await clearFunction();
          await fnUtilities.getAllProductDetails();
          await assignProductList();
          await loopForProductQty(saleTransactionList ?? []);
          await findTotalQty();
        }
      });
      isLock = false;
    }
  }

  normalPanelType() {
    return NormalPanelWidget(
      isBigLayout: false,
      isSaleLayoutTwo: true,
      cardItemList: cardItemList,
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
        //commonOnTapProduct(value1, value2);
      }),
      onLongPressItem: ((value) async {
        for (Products model in filterProductList) {
          if (model.productId == value.guidId) {
            await commonReduceProduct(model);
            await productCountIncrement(model.productId);
            await findTotalQty();
          }
        }
      }),
      onTabAddNewItem: () {
        onTapAddNewProduct();
      },
    );
  }

  //NUMBER PANEL TYPE WIDGET
  filterPanelType() {
    return NumberPanelListWidget(
      isBigLayout: false,
      cardItemList: cardItemList,
      isSaleLayoutTwo: true,
      itemId: prodId,
      onTabItemId: (value) {
        setState(() {
          setProdId(value.toString());
        });
      },
      onLongPressItem: ((value) async {
        for (Products model in filterProductList) {
          if (model.productId == value.guidId) {
            await commonReduceProduct(model);
            await productCountIncrement(model.productId);
            await findTotalQty();
          }
        }
      }),
      ontabCancel: () {
        if (!isLock) {
          setState(() {
            prodId = "";
            setProdId(prodId);
          });
        }
      },
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
      }),
      onTabAddNewItem: () {
        onTapAddNewProduct();
      },
    );
  }

  //Key PANEL TYPE WIDGET
  filterKeyPanelType() {
    return CategoryPanelWidget(
      isBigLayout: false,
      categoryListItems: categoryListItem,
      cardItemList: cardItemList,
      isShowImages: showImages,
      isSaleLayoutTwo: true,
      prodKey: prodKey,
      onLongPressItem: ((value) async {
        for (Products model in filterProductList) {
          if (model.productId == value.guidId) {
            await commonReduceProduct(model);
            await productCountIncrement(model.productId);
            await findTotalQty();
          }
        }
      }),
      onPressCategory: (value) {
        for (ProductKey model in productKeyList) {
          if (model.productKeyId == value.guidId) {
            setProdKey(model.productKeyId ?? "", model.keywordName ?? "all");
            prodKey = model.productKeyId ?? "";
          }
        }
      },
      onTapItem: ((value1, value2) {
        for (Products model in filterProductList) {
          if (model.productId == value1.guidId) {
            commonOnTapProduct(model, value2);
          }
        }
      }),
      onTabListAddItems: () {
        onTapAddNewProduct();
      },
    );
  }

  callSuccessDialog(BuildContext context, salesIdForReport) async {
    //if (sales?.salesId == null) {
    await fnUtilities.salesSuccessDialog(context, salesIdForReport);
    // } else {
    //   await fnUtilities.salesSuccessDialog(context, sales?.salesId);
    //}
  }
}
