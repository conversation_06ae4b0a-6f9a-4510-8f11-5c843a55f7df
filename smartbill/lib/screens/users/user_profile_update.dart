import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart'; 
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';
import '../../../main.dart';
import '../../Repositories/user_repository.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';

class UserProfileUpdate extends StatefulWidget {
  @override
  UserProfileUpdateState createState() => UserProfileUpdateState();
}

class UserProfileUpdateState extends State<UserProfileUpdate> {
  FnUtilities fnUtilities = new FnUtilities();
  final validator = new Validator();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final emailController = TextEditingController();
  bool isLock = false;
  double width =0.0;
  double height = 0.0;

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    await assignValue();
  }

  //ASSIGN VALUE
  assignValue() {
    setState(() {
      firstNameController.text = MyApp.activeUser.firstName ?? "";
      lastNameController.text = MyApp.activeUser.lastName ?? "";
      emailController.text = MyApp.activeUser.email ?? "";
    });
  }

  //CLEAR TEXT FILED
  clearTextField() {
    firstNameController.clear();
    lastNameController.clear();
    emailController.clear();
  }

  //PROFILE UPDATE FUNCTION
  onUpdateProfile() async {
    if (!isLock) {
      isLock = true;
      LoadingAlertWidget.onLoading(context);;
      if (await InternetConnectionChecker().hasConnection) {
        User model = User();
        model.firstName = firstNameController.text;
        model.lastName = lastNameController.text;
        model.email = emailController.text.trim();
        updateUserProfile(model).then((value) async {
          if (this.mounted) {
            if (value.status == 1) {
              setState(() {
                MyApp.activeUser.email = model.email;
                MyApp.activeUser.firstName = model.firstName;
                MyApp.activeUser.lastName = model.lastName;
                int index = ListUtility.userList.indexWhere(
                    (element) => element.username == MyApp.activeUser.username);
                ListUtility.userList[index].firstName = model.firstName;
                ListUtility.userList[index].lastName = model.lastName;
                ListUtility.userList[index].email = model.email;
              });
              SharedPreferences prefs = await SharedPreferences.getInstance();
              final String userList = User.encode(ListUtility.userList);
              await prefs.setString('userList', userList);
              await clearTextField();
              LoadingAlertWidget.onStopping();
              Navigator.pop(context, MyApp.activeUser);
            } else {
              LoadingAlertWidget.onStopping();
              showToast(value.message);
            }
          }
        });
      } else {
        LoadingAlertWidget.onStopping();
        fnUtilities.internetTurnOnAlertDialog(context);
      }
      isLock = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Profile Update",
            textColor: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15),
        child: Form(
          key: validator.formkey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            children: <Widget>[
              SizedBox(
                height: 20,
              ),
              EditText(
                text: 'First Name',
                isPassword: false,
                mController: firstNameController,
                validator: validator.validateName,
                inputAction: TextInputAction.next,
              ),
              SizedBox(height: textSizeMedium),
              EditText(
                mController: lastNameController,
                text: 'Last Name',
                isPassword: false,
                validator: validator.validateTextField,
                inputAction: TextInputAction.next,
              ),
              SizedBox(height: textSizeMedium),
              EditText(
                mController: emailController,
                text: 'Email',
                isPassword: false,
                inputType: TextInputType.emailAddress,
                validator: validator.validateEmail,
                inputAction: TextInputAction.done,
              ),
              SizedBox(height: textSizeXLarge),
              InkWell(
                onTap: () {
                  validator.validate();
                  if (validator.validate() ?? false) {
                    onUpdateProfile();
                  }
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 50,
                  child: TextWidget("Update Profile",
                      textColor: secondaryTextColor, isCentered: true),
                  decoration:
                      boxDecoration(bgColor: buttonThemeColor, radius: 8),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
