import 'package:flutter/services.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_field_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../Repositories/user_repository.dart';
import '../../model/forget_password.dart';
import '../../utils/common_function/phone_input_formatter.dart';
import 'otp_verification_screen.dart';
import 'sign_in.dart';

class ForgotPassword extends StatefulWidget {
  // const ForgotPassword({Key? key}) : super(key: key);

  @override
  _ForgotPasswordState createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  FnUtilities fnUtilities = new FnUtilities();
  Validator validator = new Validator();
  final mobileController = TextEditingController();
  bool isLock = false;

  onpressForgotPassword() async {
    validator.validate();
    if (validator.validate() ?? false) {
      if (!isLock) {
        isLock = true;
        if (await InternetConnectionChecker().hasConnection) {
          LoadingAlertWidget.onLoading(context);;

          SendOtp sendOtp = new SendOtp();
          sendOtp.mobile = mobileController.text;
           
          await sendOTPApi(sendOtp.mobile ?? "").then((value) {
            if (value.status == 1) {
              LoadingAlertWidget.onStopping();
              Navigator.pop(context);
              Navigator.push(context,
                  MaterialPageRoute(builder: (context) => VerifyOTPScreen(usermobile: mobileController.text,verifyStatus: 1,)));
            } else {
              LoadingAlertWidget.onStopping();
              showToast(value.message);
            }
          });
        } else {
          fnUtilities.internetTurnOnAlertDialog(context);
        }
        isLock = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      body: SafeArea(
        child: Center(
          child: Container(
            decoration: boxdecoration(
                bgColor: secondaryTextColor,
                showShadow: true,
                radius: 20),
            width: width > tabletWidth ? 500 : width,
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(left: 10, right: 10),
            child: SingleChildScrollView(
              child: Form(
                key: validator.formkey,
                child: Column(
                  children: <Widget>[
                    SizedBox(height: textSizeLarge),
                    Text("Forgot Password", style: headingFontStyle2),
                    SizedBox(
                      height: 10.0,
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Column(
                      children: [
                        SizedBox(height: textSizeLarge),

                        ///Mobile number textfield
                        TextFieldWidget(
                          mController: mobileController,
                          text: 'Mobile No',
                          isPassword: false,
                          maxLength: 10,
                          inputType: TextInputType.phone,
                          validator: validator.validateMobile,
                          inputFormate: [
                            FilteringTextInputFormatter.deny(RegExp(r"\s")),
                            PhoneInputFormatter(),
                            LengthLimitingTextInputFormatter(10)
                          ],
                        ),
                        SizedBox(height: 20),

                        /// Forgot password button
                        RoundedButton(
                          onPressed: () {
                            onpressForgotPassword();
                          },
                          title: 'Send OTP',
                          width: width,
                        ),

                        SizedBox(height: textSizeLarge),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            TextWidget(
                              'Know your password?',
                              textColor: greyTextColor,
                              fontFamily: fontMedium,
                              fontSize: textSizeSmall,
                              fontWeight: FontWeight.w600,
                            ),
                            SizedBox(
                              width: 4.0,
                            ),
                            GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => SignInPage()));
                                },
                                child: TextWidget(
                                  'Sign In!',
                                  fontFamily: fontBold,
                                  fontSize: textSizeMedium,
                                  textColor: buttonThemeColor,
                                  fontWeight: FontWeight.w700,
                                )),
                          ],
                        )
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
