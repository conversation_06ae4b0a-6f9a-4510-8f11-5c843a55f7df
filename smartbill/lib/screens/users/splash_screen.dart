import 'dart:io';

import 'package:android_id/android_id.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';

import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/screens/shops/sign_in_workspace.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';

import '../../Repositories/user_repository.dart';
import '../../model/app_info/app_info_request.dart';
import '../../utils/common_function/app_update_alert.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/error_logs/error_logs_function.dart';
import '../../utils/push_notifications/notification_functions.dart';
import 'demo_workspace.dart';
import 'sign_in.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  SyncUtility syncUtility = new SyncUtility();
  DBOperations dbOperations = new DBOperations();
  PrinterUtilities printerUtilities = PrinterUtilities();
  FnUtilities fnUtilities = FnUtilities();
  NotificationFunctions notifyFun = NotificationFunctions();
  ConnectivityResult connectivityResult = ConnectivityResult.none;
  Demoworkspace demo = Demoworkspace();

  //CHECK USER LOGIN OR NOT
  checkUser() async {
    // await downloadApplogo();
    await fnUtilities.requestStoragePermission();
    await fnUtilities.requestContactPermission();
    await fnUtilities.requestNotificationPermission();
    await printerUtilities.getPrinterInfo(printFor: PrinterFor.both);
    await fnUtilities.getExpieryDate();
    await dbOperations.queryAllGetWorkspace();

    if (ListUtility.userList.isNotEmpty && MyApp.activeWorkspace.workspaceId == null) {
      MyApp.activeUser = ListUtility.userList.first;
    }

    if (MyApp.activeUser.token == null) {
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => SignInPage()));
    } else {
      if (MyApp.activeUser.token != null && MyApp.activeWorkspace.workspaceId == null) {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => SignInWorkspace(
                      screenID: 1,
                    )));
      } else if (MyApp.activeWorkspace.workspaceId != null) {
        await fnUtilities.initiateDeviceSettings();
        await fnUtilities.getMasterInfoBasedOnWorkspace();
        PermissionFunctions.getPermisionsFromLocal();
        triggerSync();

        MyApp.isDemoAccount = MyApp.activeWorkspace.workspaceId == "**********" ? true : false;

        if (MyApp.isDemoAccount ?? false) {
          if (await demo.demoWorkspaceOneDayExpiryDate()) {
            // Demo account has expired, navigate to SignInWorkspace
            await demo.deleteExpiredDataRecords(context);
          } else {
            // Demo account is still valid, navigate to BottomBar
            Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                    builder: (context) => BottomBar(
                          key: bottomBarKey,
                        )));
          }
        } else {
          // Not a demo account, navigate to BottomBar
          Navigator.pushReplacement(
              context,
              MaterialPageRoute(
                  builder: (context) => BottomBar(
                        key: bottomBarKey,
                      )));
        }
      } else {
        Navigator.push(
            context,
            MaterialPageRoute(
                builder: (context) => SignInWorkspace(
                      screenID: 1,
                    )));
      }
    }
  }

  void initPageDetails() async {
    try {
      if (!await InternetConnectionChecker().hasConnection) {
        checkUser();
      } else {
        AppInfoRequest appInfoRequest = await notifyFun.generateAppInfoReqModel();
        await getAppInfoApi(appInfoRequest).then((getAppInfoResponse) async {
                checkUser();

          // if (getAppInfoResponse.result != null) {
          //   checkAppUpdate(context, getAppInfoResponse.result).then((value) async {
          //     if (value) {
          //       checkUser();
          //     }
          //   });
          // }
        });
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "Splash Init Screen");
    }
  }

  ///Check internet Connection
  Future<void> checkInternetConnection() async {
    var connectivityResult = await Connectivity().checkConnectivity();
    setState(() {
      connectivityResult = connectivityResult;
    });
  }

  triggerSync() async {
    SyncUtility syncUtility = SyncUtility();
    syncUtility.triggerManualSync(isPullSync: true);
  }

  //GET DEVICE ID
  Future<String> getId() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
      return iosDeviceInfo.identifierForVendor ?? ""; // unique ID on iOS
    } else if (Platform.isAndroid) {
      var _androidIdPlugin = AndroidId();
      final String androidId = await _androidIdPlugin.getId() ?? "";
      return androidId; // unique ID on Android
    } else if (Platform.isWindows) {
      WindowsDeviceInfo windowsDeviceInfo = await deviceInfo.windowsInfo;
      return windowsDeviceInfo.computerName; // unique ID on Windows
    }
    return null ?? "";
  }

  downloadApplogo() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isImageDownload = await prefs.getBool('isImageDownload') ?? true;

    if (!isImageDownload) {
      await downloadImage(applogo, logo, imageFor: applogo).then((value) {
        if (value == 1) {
          prefs.setBool('isImageDownload', false);
        }
      });
    } else {
      print('No need to download');
    }
  }

  @override
  void initState() {
    super.initState();
    Future.delayed(Duration.zero, () {
      getId().then((id) {
        setState(() {
          MyApp.deviceId = id;
        });
        // checkInternetConnection();
        // Connectivity().onConnectivityChanged.listen((ConnectivityResult result) {
        //   setState(() {
        //     connectivityResult = result;
        //   });
        // });
        initPageDetails();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: Image.asset(
          nembooBill,
          fit: BoxFit.fill,
        ),
      ),
    );
  }
}
