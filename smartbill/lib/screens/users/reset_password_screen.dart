import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:smartbill/model/forget_password.dart';
import 'package:smartbill/screens/users/sign_in.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../Repositories/user_repository.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/validation_utility.dart';
import '../../utils/common_widgets/rounded_button.dart';
import '../../utils/common_widgets/text_field_widget.dart';
import '../../utils/common_widgets/widgets.dart';
import '../../utils/constants/colors.dart';

class ResetPasswordScreen extends StatefulWidget {
  ResetPasswordScreen({Key? key, this.encryptedText}) : super(key: key);

  String? encryptedText;

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  final validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  // FocusNode _textFocusNode = FocusNode();
  bool isLock = false;
  bool isPasswordNotMatch = false;

  @override
  void initState() {
    super.initState();
  }

  updatePassword() async {
    validator.validate();
    if (passwordController.text == confirmPasswordController.text) {
      if (validator.validate() ?? false) {
        if (!isLock) {
          isLock = true;
          setState(() {
            isPasswordNotMatch = false;
          });
          if (await InternetConnectionChecker().hasConnection) {
            LoadingAlertWidget.onLoading(context);;
            ResetPassword resetPassword = ResetPassword();
            resetPassword.encryptedText = widget.encryptedText;
            resetPassword.newPassword = passwordController.text;

            await resetPasswordApi(resetPassword).then((value) {
              if (value.status == 1) {
                LoadingAlertWidget.onStopping();
                Navigator.pop(context);
                Navigator.push(context,
                    MaterialPageRoute(builder: (context) => SignInPage()));
              } else {
                LoadingAlertWidget.onStopping();
                showToast(value.message);
              }
            });
          } else {
            fnUtilities.internetTurnOnAlertDialog(context);
          }
          setState(() {
            isPasswordNotMatch = true;
          });
          isLock = false;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: buttonThemeColor,
      body: SafeArea(
        child: Center(
          child: Container(
            decoration: boxdecoration(
                bgColor: secondaryTextColor,
                showShadow: true,
                radius: 20),
            width: width > tabletWidth ? 500 : width,
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(left: 10, right: 10),
            child: SingleChildScrollView(
              child: Form(
                key: validator.formkey,
                child: Column(
                  children: [
                    SizedBox(height: textSizeLarge),

                    /// Reset password text
                    Text("Reset Password", style: headingFontStyle2),

                    SizedBox(
                      height: 30.0,
                    ),

                    ///Password Text field
                    TextFieldWidget(
                      mController: passwordController,
                      text: 'Password',
                      isPassword: true,
                      isSecure: true,
                      validator: validator.validatePassword,
                      inputAction: TextInputAction.next,
                      errorText:
                          isPasswordNotMatch ? "Password mismatch" : null,
                    ),

                    SizedBox(
                      height: 10,
                    ),

                    ///Confirm Password Text field
                    TextFieldWidget(
                      mController: confirmPasswordController,
                      text: 'Confirm Password',
                      isPassword: true,
                      isSecure: true,
                      inputAction: TextInputAction.next,
                      errorText:
                          isPasswordNotMatch ? "Password mismatch" : null,
                      validator: (value) {
                        if (value == passwordController.text) {
                          return null;
                        } else {
                          return "Password and Confirm password must be same";
                        }
                      },
                    ),
                    SizedBox(
                      height: 30,
                    ),

                    ///Update Button
                    RoundedButton(
                      onPressed: () {
                        updatePassword();
                      },
                      title: 'Update',
                      width: width,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
