import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';

import '../../Repositories/user_repository.dart';
import '../../Repositories/workspace_repository.dart';
import '../../main.dart';
import '../../model/check_user_password.dart';
import '../../model/login_request_model.dart';
import '../../model/user_model.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_function/sync_utility.dart';
import '../../utils/common_widgets/widgets.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/dbutils/db_operations_utility.dart';
import '../../utils/dbutils/workspace_db_script.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class Demoworkspace {
  User user = new User();
  FnUtilities fnUtilities = new FnUtilities();
  SyncUtility syncUtility = new SyncUtility();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  CommonDB commonDB = CommonDB();
  DBOperations DBoperation = new DBOperations();
  bool isLock = false;
  addUserInList(User? user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    ListUtility.userList.clear();
    ListUtility.userList.add(user!);
    MyApp.activeUser = user;
    final String userList = User.encode(ListUtility.userList);
    await prefs.setString('userList', userList);
  }

// Function to store the current date as the expiry date
  Future<void> demoWokSpaceStartingDate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    DateTime currentDate = DateTime.now();
    DateTime currentDateIST = currentDate.toLocal();
    // DateTime expiryDate = currentDate.add(Duration(days: 2)); // Adding one day
    String formattedDate = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(currentDateIST);
    prefs.setString(key_demoaccountexpiryDate, formattedDate);
    print(formattedDate);
  }

  Future<bool> demoWorkspaceOneDayExpiryDate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    // Retrieve stored expiry date
    String storedDate = prefs.getString(key_demoaccountexpiryDate) ?? "";

    if (storedDate != null) {
      // Parse stored date
      DateTime sinceDemoAcountDate = DateTime.parse(storedDate);
      // DateTime sinceDemoAcountDate = DateTime.parse("2024-02-05 14:56:08.534");

      // Get current date
      DateTime currentDate = DateTime.now();
      DateTime currentDateIST = currentDate.toLocal();

      // Calculate the difference in hours
      Duration difference = currentDateIST.difference(sinceDemoAcountDate);

      // Check if more than 24 hours have passed
      if (difference.inHours > 24) {
        // More than 24 hours have passed

        await resetDemoWorkspaceExpiryDate();
        return true;
      } else {
        // Less than or equal to 24 hours have passed
        return false;
      }
    } else {
      // Expiry date not found
      print('Expiry date not found.');
      return true;
    }
  }

  Future<void> resetDemoWorkspaceExpiryDate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.remove(key_demoaccountexpiryDate);
    print('Demo account expiry date reset.');
  }

  updateDemoWorkSpaceExpiryDate() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String dateString = prefs.getString(key_demoaccountexpiryDate) ?? "";

    // Parsing the stored date
    DateTime expiryDate = DateTime.parse(dateString);

    // Adding 2 days to the expiry date
    expiryDate = expiryDate.add(Duration(days: 2));

    // Formatting the updated date
    String updatedDateString = DateFormat('yyyy-MM-dd HH:mm:ss.SSS').format(expiryDate);

    // Storing the updated date back in SharedPreferences (optional)
    // prefs.setString(key_demoaccountexpiryDate, updatedDateString);

    return updatedDateString;
  }

  deleteExpiredDataRecords(BuildContext context) async {
    await commonDB.deleteAllRecordsBasedOnWorkspace(MyApp.activeWorkspace.workspaceId ?? "");
    await fnUtilities.removeActiveWorkspaceAndUser(context, ListUtility.signedWorkspaceList[0]);
    ListUtility.clearAllLists();
    resetDemoWorkspaceExpiryDate();
  }

  loginfunction(BuildContext context) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    if (!isLock) {
      isLock = true;
      if (await InternetConnectionChecker().hasConnection) {
        LoginRequestModel loginRequest = new LoginRequestModel();
        loginRequest.username = '**********';
        loginRequest.password = '123456';
        loginRequest.applicationId = MyApp.shopCode;
        loginRequest.mobileId = MyApp.deviceId;
        // if (userNameController.text.isNotEmpty && passwordController.text.isNotEmpty) {
        LoadingAlertWidget.onLoading(context);;
        await login(loginRequest, "Normal").then((value) async {
          // LoadingAlertWidget.onStopping();
          if (value.status == 1) {
            addUserInList(value.response);
            user = value.response!;
            MyApp.activeUser.token = user.token;
            MyApp.activeUser.username = user.username;

            // await setDemoWorkspace(true);
            await fnUtilities.initiateDeviceSettings();
            await getWorkspaceList();

            await demoWokSpaceStartingDate();
            await updateDemoWorkSpaceExpiryDate();
            MyApp.isDemoAccount = true;
            var item = ListUtility.nonSignedWorkspaceList[0];
            CheckUserPassword loginRequest = new CheckUserPassword();
            loginRequest.password = '123456';
            // LoadingAlertWidget.onLoading(context);;
            await checkUserPassword(loginRequest).then((value) async {
              if (value.status == 1) {
                item.userName = MyApp.activeUser.username;
                await DBoperation.insertWorkspace(item);
                await DBoperation.queryAllGetWorkspace();
                await fnUtilities.setActiveWorkspaceAndUser(item);
                await syncUtility.getPrefixForWorkspace();
                await PermissionFunctions.getPermisionsFromAPI();
                syncUtility.triggerManualSync(isSigning: true,isPullSync: true);
                fnUtilities.getMasterInfoBasedOnWorkspace();
                bool isFirstTime = prefs.getBool(isOpenInitial) ?? true;

                if (isFirstTime) {
                  await prefs.setBool(isOpenInitial, false);
                  await fnUtilities.getPaymentCategory();
                }
                LoadingAlertWidget.onStopping();

                Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
              } else {
                LoadingAlertWidget.onStopping();
                showToast(value.message);
              }
            });
            // await navigatDemoWorkSpace(context);
          } else {
            showToast(value.message);
          }
        });
      } else {
        fnUtilities.internetTurnOnAlertDialog(context);
      }
      isLock = false;
    }
  }

  // navigatDemoWorkSpace(BuildContext context) async {
  //   var item = ListUtility.nonSignedWorkspaceList[0];
  //   CheckUserPassword loginRequest = new CheckUserPassword();
  //   loginRequest.password = '123456';
  //   LoadingAlertWidget.onLoading(context);;
  //   await checkUserPassword(loginRequest).then((value) async {
  //     if (value.status == 1) {
  //       item.userName = MyApp.activeUser.username;
  //       await DBoperation.insertWorkspace(item);
  //       await DBoperation.insertPermissionsDetails(item.permissions, item.userName, item.workspaceId);
  //       await DBoperation.queryAllGetWorkspace();
  //       await fnUtilities.setActiveWorkspaceAndUser(item);
  //       await DBoperation.queryAllGetsPermissionDetails();
  //       await syncUtility.getPrefixForWorkspace();
  //       syncUtility.triggerManualSync(isSigning: true);
  //       fnUtilities.getMasterInfoBasedOnWorkspace();
  //       LoadingAlertWidget.onStopping();

  //       Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
  //     } else {
  //       LoadingAlertWidget.onStopping();
  //       showToast(value.message);
  //     }
  //   });
  // }

  getWorkspaceList() async {
    ListUtility.nonSignedWorkspaceList.clear();

    await getWorkspaceListByUser().then((value) {
      if (value.status == 1) {
        ListUtility.nonSignedWorkspaceList = value.result ?? [];
      } else {
        showToast(value.message);
      }
    });
  }

  resetWorkSpace(bool resetWorkSpace) async {
    // Database db = await DatabaseHelper.instance.database;

    // List<Map<String, dynamic>> tables = await db.rawQuery(
    //     "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'android_metadata' AND name != 'DeviceSettings';");

    // var tableNames = tables.map((row) => row['name'] as String).toList(growable: false);

    // print(tableNames);
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // bool resetWorkSpace = prefs.getBool("resetWorkSpace") ?? false; // default value if key doesn't exist

    if (resetWorkSpace) {
      await commonDB.deleteAllRecordsBasedOnWorkspace(MyApp.activeWorkspace.workspaceId ?? "");

      // await workspaceDBScript.deleteSignedWorkspaceOnId(MyApp.activeWorkspace.workspaceId);
      // ListUtility.clearAllLists();
    }
  }
}
