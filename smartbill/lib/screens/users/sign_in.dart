// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter_svg/svg.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart'; 
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/screens/users/otp_verification_screen.dart';
import 'package:smartbill/screens/privacy_policy/privacy_policy_screen.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/login_request_model.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/model/validity.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_field_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../main.dart';
import '../../Repositories/user_repository.dart';
import '../../model/forget_password.dart';
import '../../utils/common_function/phone_input_formatter.dart';
import 'demo_workspace.dart';
import 'forgot_password_page.dart';
import 'sign_up.dart';

class SignInPage extends StatefulWidget {
  @override
  SignInPageState createState() => SignInPageState();
}

class SignInPageState extends State<SignInPage> {
  double height = 0.0;
  double width = 0.0;
  final userNameController = TextEditingController();
  final passwordController = TextEditingController();

  User user = new User();
  FnUtilities fnUtilities = new FnUtilities();

  bool isUserNameIsEmpty = false;
  bool isPasswordIsEmpty = false;
  bool isLock = false;
  bool isLoginWithOtp = false;

  addDeviceId(String deviceId, String deviceName) async {
    Validity valid = new Validity();
    valid.deviceId = MyApp.deviceId;
    valid.deviceName = deviceName;
    valid.token = MyApp.activeUser.token;
    addDevice(valid).then((value) {
      showToast(value.message);
    });
    for (SubscriptionDetail v in ListUtility.subscriptionDetails) {
      if (v.mobileId == deviceId) {
        SharedPreferences prefs = await SharedPreferences.getInstance();
        prefs.setString("expieryDate", v.activations?[0].expiryDate ?? "");
        MyApp.expiryDate = DateTime.parse(v.activations?[0].expiryDate ?? "");
        break;
      }
    }
  }

  addUserInList(User user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    ListUtility.userList.clear();
    ListUtility.userList.add(user);
    MyApp.activeUser = user;
    final String userList = User.encode(ListUtility.userList);
    await prefs.setString('userList', userList);
  }

  @override
  void initState() {
    super.initState();
  }

  loginWithPassword() async {
    if (!isLock) {
      isLock = true;
      if (await InternetConnectionChecker().hasConnection) {
        LoginRequestModel loginRequest = new LoginRequestModel();
        loginRequest.username = userNameController.text;
        loginRequest.password = passwordController.text;
        loginRequest.applicationId = MyApp.shopCode;
        loginRequest.mobileId = MyApp.deviceId;
        // if (userNameController.text.isNotEmpty && passwordController.text.isNotEmpty) {
        LoadingAlertWidget.onLoading(context);;
        await login(loginRequest, "Normal").then((value) async {
          LoadingAlertWidget.onStopping();
          userNameController.clear();
          passwordController.clear();
          if (value.status == 1) {
            addUserInList(value.response!);
            user = value.response!;
            MyApp.activeUser.token = user.token;
            MyApp.activeUser.username = user.username;
            await fnUtilities.initiateDeviceSettings();
            MyApp.isDemoAccount = MyApp.activeWorkspace.workspaceId == "**********" ? true : false;

            Navigator.pushReplacement(
                context,
                MaterialPageRoute(
                    builder: (context) => BottomBar(
                          key: bottomBarKey,
                        )));
          } else {
            showToast(value.message);
          }
        });
        // } else {
        //   setState(() {
        //     isUserNameIsEmpty = userNameController.text.isEmpty ? true : false;
        //     isPasswordIsEmpty = passwordController.text.isEmpty ? true : false;
        //   });
        // }
      } else {
        fnUtilities.internetTurnOnAlertDialog(context);
      }
      isLock = false;
    }
  }

  loginWithOTP() async {
    if (userNameController.text.isNotEmpty) {
      if (!isLock) {
        isLock = true;
        if (await InternetConnectionChecker().hasConnection) {
          LoadingAlertWidget.onLoading(context);;

          SendOtp sendOtp = new SendOtp();
          sendOtp.mobile = userNameController.text;

          await sendOTPApi(sendOtp.mobile ?? "").then((value) {
            if (value.status == 1) {
              LoadingAlertWidget.onStopping();
              Navigator.pop(context);
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => VerifyOTPScreen(
                            usermobile: userNameController.text,
                            verifyStatus: 2,
                          )));
            } else {
              LoadingAlertWidget.onStopping();
              showToast(value.message);
            }
          });
        } else {
          fnUtilities.internetTurnOnAlertDialog(context);
        }
        isLock = false;
      }
    } else {
      setState(() {
        isUserNameIsEmpty = userNameController.text.isEmpty ? true : false;
      });
    }
  }

  onPressLogin() {
    if (isLoginWithOtp) {
      if (userNameController.text.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PrivacyPolicyScreen()),
        ).then((value) {
          if (value ?? false) {
            loginWithOTP();
          }
        });
      } else {
        setState(() {
          isUserNameIsEmpty = userNameController.text.isEmpty ? true : false;
        });
      }
    }
    // loginWithOTP();
    else {
      if (userNameController.text.isNotEmpty && passwordController.text.isNotEmpty) {
        Navigator.push(
          context,
          MaterialPageRoute(builder: (context) => PrivacyPolicyScreen()),
        ).then((value) {
          if (value ?? false) {
            loginWithPassword();
          }
        });
      } else {
        setState(() {
          isUserNameIsEmpty = userNameController.text.isEmpty ? true : false;
          isPasswordIsEmpty = passwordController.text.isEmpty ? true : false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: backgroundColor,
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              // SizedBox(
              //   height: 20,
              // ),
              Container(
                height: 150,
                // color: Colors.pinkAccent,
                child: Image.asset(nembooLogo),
              ),
              // Container(child: SvgPicture.asset(nembooLogoSVG)),
              // SizedBox(
              //   height: 10.0,
              // ),
              // Text(
              //   appName,
              //   style: headingFontStyle1,
              // ),
              // SizedBox(
              //   height: 5.0,
              // ),
              // Text("-- Your Smart Billing App --", style: headingFontStyle1.copyWith(fontSize: 15.0)),
              // SizedBox(
              //   height: 5.0,
              // ),
              Padding(
                padding: const EdgeInsets.only(right: 15.0, left: 15.0),
                child: Card(
                  elevation: 5,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Container(
                    // margin: EdgeInsets.all(15),
                    decoration: boxdecoration(showShadow: true, radius: 20),
                    padding: EdgeInsets.all(20),
                    width: width > tabletWidth ? 500 : width,
                    child: Column(
                      children: <Widget>[
                        SizedBox(height: 0),
                        Text("Log in", style: headingFontStyle2.copyWith(fontWeight: FontWeight.w500)),
                        SizedBox(height: 10),
                        Align(
                            alignment: Alignment.bottomLeft,
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 5.0),
                              child: Text(
                                'User name',
                                style: black16w500,
                              ),
                            )),

                        /// username text filed
                        TextFieldWidget(
                          // text: "Username",

                          isPassword: false,
                          mController: userNameController,
                          errorText: isUserNameIsEmpty ? "This field is required" : null,
                          inputType: TextInputType.phone,
                          isEnabledBorder: true,
                          inputFormate: [
                            FilteringTextInputFormatter.digitsOnly,
                            FilteringTextInputFormatter.deny(RegExp(r"\s")),
                            PhoneInputFormatter(),
                            LengthLimitingTextInputFormatter(10)
                          ],
                          inputAction: TextInputAction.next,
                          maxLength: 10,
                        ),

                        ///password text field
                        if (!isLoginWithOtp) ...[
                          SizedBox(height: 10),
                          Align(
                              alignment: Alignment.bottomLeft,
                              child: Padding(
                                padding: const EdgeInsets.only(bottom: 5.0),
                                child: Text(
                                  'Password',
                                  style: black16w500,
                                ),
                              )),
                          TextFieldWidget(
                            isEnabledBorder: true,
                            // text: "Password",
                            isSecure: true,
                            isPassword: true,
                            mController: passwordController,
                            inputAction: TextInputAction.done,
                            submit: (login) {
                              if (userNameController.text.isNotEmpty && passwordController.text.isNotEmpty) {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) => PrivacyPolicyScreen()),
                                ).then((value) {
                                  if (value ?? false) {
                                    loginWithPassword();
                                  }
                                });
                              } else {
                                setState(() {
                                  isUserNameIsEmpty = userNameController.text.isEmpty ? true : false;
                                  isPasswordIsEmpty = passwordController.text.isEmpty ? true : false;
                                });
                              }
                            },
                            errorText: isPasswordIsEmpty ? "Please enter your password" : null,
                          ),
                          Align(
                            alignment: Alignment.topRight,
                            child: GestureDetector(
                                onTap: () {
                                  Navigator.pop(context);
                                  Navigator.push(context, MaterialPageRoute(builder: (context) => ForgotPassword()));
                                },
                                child: TextWidget(
                                  'Forgot Password',
                                  fontFamily: fontMedium,
                                  fontSize: textSizeSmall,
                                  textColor: greyTextColor,
                                  fontWeight: FontWeight.w500,
                                  maxLine: 2,
                                )),
                          ),
                        ],

                        SizedBox(
                          height: 15,
                        ),

                        RoundedButton(
                          width: width * 0.6,
                          onPressed: () {
                            onPressLogin();
                          },
                          title: isLoginWithOtp ? 'Get OTP' : 'Login',
                        ),
                        SizedBox(
                          height: 8,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Align(
                              alignment: Alignment.topRight,
                              child: GestureDetector(
                                  onTap: () {
                                    setState(() {
                                      isLoginWithOtp = !isLoginWithOtp;
                                    });
                                  },
                                  child: TextWidget(
                                    isLoginWithOtp ? 'Login With Password?' : 'Login With OTP?',
                                    fontFamily: fontMedium,
                                    fontSize: textSizeSMedium,
                                    textColor: buttonThemeColor,
                                    fontWeight: FontWeight.w700,
                                    maxLine: 2,
                                  )),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TextWidget(
                              "Don't have account?",
                              textColor: greyTextColor,
                              fontFamily: fontMedium,
                              fontSize: textSizeSmall,
                              fontWeight: FontWeight.w600,
                            ),
                            SizedBox(
                              width: 4.0,
                            ),
                            InkWell(
                              onTap: () {
                                Navigator.push(context, MaterialPageRoute(builder: (context) => SignUpPage()));
                              },
                              child: TextWidget(
                                'Register Now ',
                                textColor: buttonThemeColor,
                                fontFamily: fontMedium,
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.w700,
                                maxLine: 2,
                              ),
                            )
                          ],
                        ),
                        SizedBox(height: 8),
                        Text('(OR)'),
                        SizedBox(height: 8),
                        RoundedButton(
                            isEnableBorder: true,
                            width: width * 0.6,
                            title: 'Demo Account',
                            onPressed: () async {
                              Demoworkspace demo = Demoworkspace();
                              await demo.loginfunction(context);
                            }),
                             Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            TextWidget(
                              "By continuing, I agree to the",
                              textColor: greyTextColor,
                              fontFamily: fontMedium,
                              fontSize: textSizeSmall,
                              fontWeight: FontWeight.w600,
                            ),
                            SizedBox(
                              width: 4.0,
                            ),
                            
                          ],
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [

                          InkWell(
                              onTap: () {
                                Navigator.push(context, MaterialPageRoute(builder: (context) => PrivacyPolicyScreen(contentType: content.termsandconditions,)));
                              },
                              child: TextWidget(
                                'Terms & Conditions'  ,
                                textColor: buttonThemeColor,
                                fontFamily: fontMedium,
                                fontSize: textSizeSmall,
                                fontWeight: FontWeight.w400,
                                maxLine: 2,
                              ),
                            ), 
                            Text(' and '), InkWell(
                              onTap: () {
                                Navigator.push(context, MaterialPageRoute(builder: (context) => PrivacyPolicyScreen(contentType: content.privacypolicy,)));
                              },
                              child: TextWidget(
                                'PrivacyPolicy'  ,
                                textColor: buttonThemeColor,
                                fontFamily: fontMedium,
                                fontSize: textSizeSmall,
                                fontWeight: FontWeight.w400,
                                maxLine: 2,
                              ),
                            )
                        ],),
                        SizedBox(
                          height: 10,
                        ),
                        /// You tube tutorial link
                         InkWell(
                          onTap: () {
                            launch('https://youtube.com/@nemboobill');
                          },
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SvgPicture.asset(
                                width: 25,
                                height: 25,
                                youtubeIconSVG,
                                color: buttonThemeColor,
                              ),
                              SizedBox(width: 6),
                              Text('Videos | Tutorials', style: black16w500)
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                ),              
              ),
            ],
          ),
        ),
      ),
    );
  }
}
