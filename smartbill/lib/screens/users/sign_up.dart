import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; 
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/register_request.dart';
import 'package:smartbill/model/register_response.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_field_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../Repositories/user_repository.dart';
import '../../main.dart';
import '../privacy_policy/privacy_policy_screen.dart';
import 'sign_in.dart';

class SignUpPage extends StatefulWidget {
  @override
  SignUpPageState createState() => SignUpPageState();
}

class SignUpPageState extends State<SignUpPage> {
  double width = 0.0;
  double height = 0.0;
  User user = new User();
  RegisterRequest registerRequest = new RegisterRequest();
  RegisterResponse registerResponse = new RegisterResponse();
  FnUtilities fnUtilities = new FnUtilities();
  final validator = new Validator();
  final firstNameController = TextEditingController();
  final lastNameController = TextEditingController();
  final userNameController = TextEditingController();
  final mobieController = TextEditingController();
  final emailController = TextEditingController();
  final passwordController = TextEditingController();
  final confirmPasswordController = TextEditingController();
  bool isPasswordNotMatch = false;
  bool isLock = false;
  SizedBox sizedBox10 = SizedBox(
    height: 10.0,
  );

  clearTextField() {
    setState(() {
      firstNameController.clear();
      lastNameController.clear();
      userNameController.clear();
      mobieController.clear();
      emailController.clear();
      passwordController.clear();
      confirmPasswordController.clear();
    });
  }

  onSaveRegister() async {
    if (passwordController.text == confirmPasswordController.text) {
      if (!isLock) {
        isLock = true;
        setState(() {
          isPasswordNotMatch = false;
        });
        registerRequest.firstName = firstNameController.text;
        registerRequest.lastName = lastNameController.text;
        registerRequest.username = mobieController.text;
        registerRequest.mobile = mobieController.text;
        registerRequest.email = emailController.text.trim();
        registerRequest.password = confirmPasswordController.text;
        registerRequest.mobileId = MyApp.deviceId;
        registerRequest.applicationId = MyApp.shopCode;
        registerRequest.deviceName = "Home";
        if (await InternetConnectionChecker().hasConnection) {
          LoadingAlertWidget.onLoading(context);;
          await register(registerRequest).then((value) {
            LoadingAlertWidget.onStopping();
            registerResponse = value;
            if (registerResponse.response != null) {
              setState(() {
                clearTextField();
              });
              showToast(registerResponse.response?.message);
              Navigator.pop(context);
              Navigator.push(context, MaterialPageRoute(builder: (context) => SignInPage()));
            } else {
              showToast(registerResponse.message);
            }
          });
        } else {
          fnUtilities.internetTurnOnAlertDialog(context);
        }
        isLock = false;
      }
    } else {
      setState(() {
        isPasswordNotMatch = true;
      });
    }
  }

  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: buttonThemeColor,
      body: SafeArea(
        child: Align(
          alignment: Alignment.center,
          child: Container(
            decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 20),
            width: width > tabletWidth ? 500 : width,
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(left: 10, right: 10),
            child: SingleChildScrollView(
              child: Form(
                key: validator.formkey,
                child: Column(
                  children: <Widget>[
                    Text("Register", style: headingFontStyle2),

                    /// First Name text field
                    TextFieldWidget(
                      text: 'First Name',
                      isPassword: false,
                      mController: firstNameController,
                      validator: validator.validateName,
                      inputAction: TextInputAction.next,
                    ),
                    sizedBox10,

                    ///Second Name text field
                    TextFieldWidget(
                      mController: lastNameController,
                      text: 'Last Name',
                      isPassword: false,
                      validator: validator.validateTextField,
                      inputAction: TextInputAction.next,
                    ),
                    sizedBox10,

                    ///Mobile number text field
                    TextFieldWidget(
                      mController: mobieController,
                      text: 'Mobile',
                      isPassword: false,
                      maxLength: 10,
                      inputType: TextInputType.phone,
                      inputFormate: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      validator: validator.validateMobile,
                      inputAction: TextInputAction.next,
                    ),
                    sizedBox10,

                    /// email text field
                    TextFieldWidget(
                      mController: emailController,
                      text: 'Email',
                      isPassword: false,
                      // maxlength: 30,
                      inputType: TextInputType.emailAddress,
                      validator: validator.validateEmail,
                      inputAction: TextInputAction.next,
                    ),
                    sizedBox10,

                    ///Password Text field
                    TextFieldWidget(
                      mController: passwordController,
                      text: 'Password',
                      isSecure: true,
                      validator: validator.validatePassword,
                      inputAction: TextInputAction.next,
                      errorText: isPasswordNotMatch ? "Password mismatch" : null,
                    ),
                    sizedBox10,

                    ///Confirm Password Text field
                    TextFieldWidget(
                      mController: confirmPasswordController,
                      text: 'Confirm Password',
                      isSecure: true,
                      validator: validator.validatePassword,
                      inputAction: TextInputAction.done,
                      submit: (submit) {
                        validator.validate();
                        if (validator.validate() ?? false) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => PrivacyPolicyScreen()),
                          ).then((value) {
                            if (value ?? false) {
                              onSaveRegister();
                            }
                          });
                        }
                      },
                      errorText: isPasswordNotMatch ? "Password mismatch" : null,
                    ),

                    SizedBox(height: textSizeXLarge),

                    //Sign in button
                    RoundedButton(
                      onPressed: () {
                        if (validator.validate() ?? false) {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => PrivacyPolicyScreen()),
                          ).then((value) {
                            if (value ?? false) {
                              onSaveRegister();
                            }
                          });
                        }
                      },
                      title: 'Sign up',
                      width: width <= tabletWidth ? 140 : width,
                    ),
                    SizedBox(height: textSizeLarge),

                    ///Register text button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        TextWidget(
                          'Already have account?',
                          textColor: greyTextColor,
                          fontFamily: fontMedium,
                          fontSize: textSizeSmall,
                          fontWeight: FontWeight.w600,
                        ),
                        SizedBox(
                          width: 4.0,
                        ),
                        GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.push(context, MaterialPageRoute(builder: (context) => SignInPage()));
                            },
                            child: TextWidget('Sign In!',
                                fontFamily: fontMedium, fontWeight: FontWeight.w700, fontSize: textSizeMedium, textColor: buttonThemeColor)),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
