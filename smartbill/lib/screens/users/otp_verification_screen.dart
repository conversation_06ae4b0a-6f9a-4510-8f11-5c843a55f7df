import 'package:flutter/material.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/screens/users/reset_password_screen.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../Repositories/user_repository.dart';
import '../../model/forget_password.dart';
import '../../model/user_model.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_function/validation_utility.dart';
import '../../utils/common_widgets/pin_input_field.dart';
import '../../utils/common_widgets/rounded_button.dart';
import '../../utils/common_widgets/widgets.dart';
import '../navigators/bottom_bar.dart';

class VerifyOTPScreen extends StatefulWidget {
  VerifyOTPScreen({Key? key, this.usermobile, this.verifyStatus}) : super(key: key);
  String? usermobile;
  int? verifyStatus;

  @override
  State<VerifyOTPScreen> createState() => _VerifyOTPScreenState();
}

class _VerifyOTPScreenState extends State<VerifyOTPScreen> {
  FnUtilities fnUtilities = new FnUtilities();
  final otp1Controller = TextEditingController();
  final validator = new Validator();
  bool isLock = false;
  bool isValidator = false;
  User user = new User();

  addUserInList(User user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    ListUtility.userList.clear();
    ListUtility.userList.add(user);
    MyApp.activeUser = user;
    final String userList = User.encode(ListUtility.userList);
    await prefs.setString('userList', userList);
  }

  onpressVerifyOTP() async {
    validator.validate();
    if (validator.validate() ?? false) {
      if (!isLock) {
        isLock = true;
        if (await InternetConnectionChecker().hasConnection) {
          LoadingAlertWidget.onLoading(context);;
          if (widget.verifyStatus == 1) {
            VerifyOTP verifyOTP = VerifyOTP();
            verifyOTP.mobile = widget.usermobile;
            verifyOTP.otp = otp1Controller.text;

            await verifyOTPApi(verifyOTP).then((value) {
              if (value.status == 1) {
                LoadingAlertWidget.onStopping();
                Navigator.pop(context);
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => ResetPasswordScreen(
                              encryptedText: value.message ?? "",
                            )));
              } else {
                setState(() {
                  isValidator = true;
                  LoadingAlertWidget.onStopping();
                });
                showToast(value.message);
              }
            });
          } else if (widget.verifyStatus == 2) {
            VerifyLoginOTP verifyLoginOTP = VerifyLoginOTP();
            verifyLoginOTP.mobile = widget.usermobile;
            verifyLoginOTP.otp = otp1Controller.text;
            verifyLoginOTP.mobileID = MyApp.deviceId;
            await verifyLogInOTPApi(verifyLoginOTP).then((value) async {
              if (value.status == 1) {
                LoadingAlertWidget.onStopping();
                addUserInList(value.response!);
                user = value.response!;
                MyApp.activeUser.token = user.token;
                MyApp.activeUser.username = user.username;
                await fnUtilities.initiateDeviceSettings();
                MyApp.isDemoAccount = MyApp.activeWorkspace.workspaceId == "**********" ? true : false;

                Navigator.pop(context);
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => BottomBar(
                              key: bottomBarKey,
                            )));
              } else {
                setState(() {
                  isValidator = true;
                  LoadingAlertWidget.onStopping();
                });
                showToast(value.message);
              }
            });
          }
        } else {
          fnUtilities.internetTurnOnAlertDialog(context);
        }
        isLock = false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      body: SafeArea(
        child: Center(
          child: Container(
            decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 20),
            width: width > tabletWidth ? 500 : width,
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.only(left: 10, right: 10),
            child: SingleChildScrollView(
              child: Form(
                key: validator.formkey,
                child: Column(
                  children: <Widget>[
                    SizedBox(height: textSizeLarge),

                    /// Otp sent text
                    Text("OTP has been sent to", style: buttonTextStyle.apply(color: buttonThemeColor)),
                    SizedBox(
                      height: 10,
                    ),

                    /// Mobile number text
                    Text("+91 ${widget.usermobile.toString()}", style: black16BoldTextStyle),
                    SizedBox(
                      height: 30,
                    ),

                    /// Otp Input field
                    SizedBox(
                        //  height: 90,
                        width: width,
                        child: PinInputField(
                          pinController: otp1Controller,
                          isWrong: isValidator,
                          validator: validator.validateTextField,
                        )),
                    SizedBox(
                      height: 10,
                    ),

                    ///Resend otp button
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        TextWidget(
                          'Did not receive the OTP? ',
                          textColor: greyTextColor,
                          fontFamily: fontMedium,
                          fontSize: textSizeSmall,
                          fontWeight: FontWeight.w600,
                        ),
                        SizedBox(
                          width: 4.0,
                        ),
                        GestureDetector(
                            onTap: () async {
                              setState(() {
                                isValidator = false;
                              });
                              LoadingAlertWidget.onLoading(context);;
                              await sendOTPApi(widget.usermobile ?? "").then((value) {
                                if (value.status == 1) {
                                  otp1Controller.text = "";
                                  LoadingAlertWidget.onStopping();
                                } else {
                                  LoadingAlertWidget.onStopping();
                                  // showToast(value.message);
                                }
                              });
                            },
                            child: TextWidget(
                              'Resend OTP',
                              fontFamily: fontBold,
                              fontSize: textSizeMedium,
                              textColor: buttonThemeColor,
                              fontWeight: FontWeight.w700,
                            )),
                      ],
                    ),
                    SizedBox(
                      height: 30,
                    ),

                    ///Verify button
                    RoundedButton(
                      onPressed: () {
                        if (otp1Controller.text.isEmpty) {
                          setState(() {
                            isValidator = true;
                            validator.validate();
                          });
                        } else {
                          isValidator = false;
                          validator.validate();
                          onpressVerifyOTP();
                        }
                      },
                      title: 'Verify',
                      width: width,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
