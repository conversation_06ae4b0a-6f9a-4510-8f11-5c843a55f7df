import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/change_password.dart';
import 'package:smartbill/screens/users/user_profile_update.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/text_field_box_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:sqflite/sqflite.dart';
import '../../../main.dart';
import '../../Repositories/user_repository.dart';
import '../../model/user_model.dart';
import '../../model/workspace.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/rounded_button.dart';
import '../../utils/common_widgets/settings_card_widget.dart';
import '../../utils/common_widgets/text_field_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/dbutils/database_helper.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/workspace_db_script.dart';

class ProfilePage extends StatefulWidget {
  @override
  ProfilePageState createState() => ProfilePageState();
}

class ProfilePageState extends State<ProfilePage> {
  ChangePassword passwords = new ChangePassword();
  FnUtilities fnUtilities = new FnUtilities();
  final validator = new Validator();
  final passwordController = TextEditingController();
  final oldPasswordController = TextEditingController();
  final newPasswordController = TextEditingController();
  final cPasswordController = TextEditingController();
  final deletePasswordController = TextEditingController();
  bool isPasswordNotMatch = false;
  bool isLoading = false;
  double width = 0.0;
  double height = 0.0;

  CommonDB commonDB = CommonDB();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();

  @override
  void initState() {
    setState(() {
      isLoading = false;
    });
    super.initState();
  }

  //CLEAR CHANGE PASSWORD TEXT FILED
  clearTextField() {
    oldPasswordController.clear();
    newPasswordController.clear();
    cPasswordController.clear();
  }

  /// delete account function
  deleteAccount(String password) async {
    try {
      setState(() {
        LoadingAlertWidget.onLoading(context);;
      });
      await deleteAccountByPassword(password).then((value) {
        if (value.status == 1) {
          deletePasswordController.clear();
          logOutUserAndAllShopes();
          toast(value.message);
        } else {
          toast(value.message);
        }
      });
      setState(() {
        LoadingAlertWidget.onStopping();
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        LoadingAlertWidget.onStopping();
      });
    }
  }

  Future<int> deleteSignedWorkspaceOnId(String id) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_workspace);
  }

  /// Log out function
  logOutUserAndAllShopes() async {
    if (ListUtility.signedWorkspaceList.length != 0) {
      for (WorkSpace model in ListUtility.signedWorkspaceList) {
        await commonDB.deleteAllRecordsBasedOnWorkspace(model.workspaceId ?? "");
        await workspaceDBScript.deleteSignedWorkspaceOnId(model.workspaceId);
      }
      removeDataFromLocale();
    } else {
      removeDataFromLocale();
    }
  }

  ///Remove data in preference
  removeDataFromLocale() async {
    // Assuming 'db' is an instance of your SQLite database
    Database db = await DatabaseHelper.instance.database;

    // Query the 'sqlite_master' table to get the names of user-defined tables
    List<Map<String, dynamic>> tables = await db.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%' AND name != 'android_metadata' AND name != 'DeviceSettings';");

    // Extract the 'name' column from the result set to get a list of table names
    var tableNames = tables.map((row) => row['name'] as String).toList(growable: false);

    // Now you can use the 'tableNames' variable as needed
    print(tableNames);

    // Delete all user-defined tables
    for (String tableName in tableNames) {
      await db.execute('DELETE FROM $tableName');
    }
    SharedPreferences prefs = await SharedPreferences.getInstance();
    ListUtility.userList = [];
    await prefs.setString('userList', User.encode(ListUtility.userList));

    setState(() {
      ListUtility.clearAllLists();
    });

    MyApp.activeWorkspace.workspaceName = null;

    prefs.setString("activeWorkspace", jsonEncode(MyApp.activeWorkspace));

    prefs.remove("activeWorkspace");
    prefs.remove("userList");
    prefs.remove(isOpenInitial);

    Navigator.of(context).pushNamedAndRemoveUntil('signIn', (Route<dynamic> route) => false);
  }

  //CHANGE PASSWORD ALERT DIALOG
  changePasswordDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(
            builder: (context, setState) {
              return Center(
                child: SingleChildScrollView(
                  child: AlertDialog(
                    content: Form(
                      key: validator.formkey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: TextFieldBoxWidget(
                                  text: "Old Password",
                                  isPassword: true,
                                  mController: oldPasswordController,
                                  validator: validator.validatePassword,
                                  isSecure: true,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: TextFieldBoxWidget(
                                  text: "New Password",
                                  isPassword: true,
                                  isSecure: true,
                                  mController: newPasswordController,
                                  inputType: TextInputType.text,
                                  validator: validator.validatePassword,
                                  errorText: isPasswordNotMatch ? "Password Mismatch" : null,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10),
                          Row(
                            children: <Widget>[
                              //CountryCodePicker(onChanged: print, showFlag: true),
                              Expanded(
                                child: TextFieldBoxWidget(
                                  text: "Confirm Password",
                                  isPassword: true,
                                  mController: cPasswordController,
                                  inputType: TextInputType.text,
                                  validator: validator.validatePassword,
                                  errorText: isPasswordNotMatch ? "Password Mismatch" : null,
                                  isSecure: true,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 24),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              GestureDetector(
                                onTap: () {
                                  clearTextField();
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                  decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                                ),
                              ),
                              SizedBox(
                                width: 10,
                              ),
                              GestureDetector(
                                onTap: () {
                                  passwords.oldPassword = oldPasswordController.text;
                                  passwords.newPassword = cPasswordController.text;
                                  validator.validate();
                                  if (validator.validate() ?? false) {
                                    if (newPasswordController.text == cPasswordController.text) {
                                      changePassword(passwords).then((value) {
                                        showToast(value.message);
                                        setState(() {
                                          clearTextField();
                                        });
                                        Navigator.pop(context);
                                      });
                                    } else {
                                      setState(() {
                                        isPasswordNotMatch = true;
                                      });
                                    }
                                  }
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 50,
                                  width: width / 4,
                                  child: TextWidget("Change", textColor: secondaryTextColor, isCentered: true),
                                  decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: backgroundColor,
      appBar: BaseAppBar(
        title: Text('Profile', style: headingTextStyle3),
        appBar: AppBar(),
        widgets: [
          IconButton(
            icon: Icon(
              Icons.delete,
              color: secondaryTextColor,
            ),
            onPressed: () {
              deleteAccountAlertDialog();
            },
          ),
        ],
      ),
      body: Center(
        child: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  height: 20,
                ),
                Image.asset(userProfilePNG, height: 150),
                Container(
                  child: TextWidget((MyApp.activeUser.firstName ?? "") + ' ' + (MyApp.activeUser.lastName ?? ""),
                      textColor: primaryTextColor, fontSize: textSizeLarge, fontWeight: FontWeight.w700, fontFamily: fontSemibold),
                ),
                SizedBox(
                  height: 50,
                ),
                personalDetailsContainer(),
                SizedBox(
                  height: 20,
                ),
                SettingsCardWidget(
                  title: "Change Password",
                  titleStyle: headingTextStyle3.apply(color: buttonThemeColor),
                  enableDivider: false,
                  rightSideicon: Icons.keyboard_arrow_right,
                  rightSideiconColor: buttonThemeColor,
                  onPress: () {
                    setState(() {
                      isPasswordNotMatch = false;
                    });
                    changePasswordDialog(context);
                  },
                ),
                // changePasswordContainer(),
                SizedBox(
                  height: 20,
                ),
                SettingsCardWidget(
                  title: " Edit Profile",
                  titleStyle: headingTextStyle3.apply(color: buttonThemeColor),
                  enableDivider: false,
                  // bgColor: buttonThemeColor,
                  rightSideicon: Icons.edit,
                  rightSideiconColor: buttonThemeColor,
                  onPress: () {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => UserProfileUpdate())).then((value) {
                      setState(() {
                        if (value != null) {
                          MyApp.activeUser = value;
                        }
                      });
                    });
                  },
                ),
                SizedBox(
                  height: 20,
                ),
                SettingsCardWidget(
                  title: "Log Out",
                  titleStyle: red18w700,
                  enableDivider: false,
                  rightSideicon: Icons.logout,
                  rightSideiconColor: redColor,
                  onPress: () async {
                    alertSync(context);
                  },
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  ///to show Mobile number and email
  personalDetailsContainer() {
    return Container(
      width: width > tabletWidth ? 500 : width,
      decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 20),
      padding: EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: TextWidget('Mobile No : ' + (MyApp.activeUser.mobile ?? ""),
                    textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
              )
            ],
          ),
          Row(
            children: [
              Expanded(
                child: TextWidget('Mail ID : ' + (MyApp.activeUser.email ?? ""),
                    textColor: primaryTextColor, fontSize: textSizeMedium, isLongText: true, fontFamily: fontSemibold),
              )
            ],
          ),
        ],
      ),
    );
  }

  ///change Password container
  changePasswordContainer() {
    return Container(
      width: width > tabletWidth ? 500 : width,
      decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 20),
      padding: EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
      child: InkWell(
        onTap: () {
          setState(() {
            isPasswordNotMatch = false;
          });
          changePasswordDialog(context);
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Container(
              child: Text('Change Password', style: headingTextStyle3.apply(color: buttonThemeColor)),
            ),
            IconButton(
              icon: Icon(
                Icons.keyboard_arrow_right,
                color: buttonThemeColor,
              ),
              onPressed: () {},
            ),
          ],
        ),
      ),
    );
  }

  ///Edit Profile Container
  editProfileContainer() {
    return Container(
      width: width > tabletWidth ? 500 : width,
      decoration: boxdecoration(bgColor: buttonThemeColor, showShadow: true, radius: 20),
      padding: EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
      child: InkWell(
        onTap: () {
          Navigator.push(context, MaterialPageRoute(builder: (context) => UserProfileUpdate())).then((value) {
            setState(() {
              if (value != null) {
                MyApp.activeUser = value;
              }
            });
          });
        },
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Container(
              child: Text('Edit Profile', style: headingTextStyle3),
            ),
            IconButton(
              icon: Icon(
                Icons.edit,
                color: secondaryTextColor,
              ),
              onPressed: () {},
            ),
          ],
        ),
      ),
    );
  }

  /// delete account alert dialog
  deleteAccountAlertDialog() {
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
            contentPadding: EdgeInsets.all(20.0),
            content: Container(
              child: Form(
                key: validator.formkey,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 20,
                    ),
                    Text(
                      "Enter your password to confirm delete your account",
                      style: black16BoldTextStyle.copyWith(fontSize: 18),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    TextFieldWidget(
                      text: "Enter Your Password",
                      isSecure: true,
                      isPassword: true,
                      mController: deletePasswordController,
                      inputAction: TextInputAction.done,
                      validator: validator.validateTextField,
                      // errorText: "This field is required",
                    ),
                    SizedBox(
                      height: 25,
                    ),
                    RoundedButton(
                      title: "Submit",
                      onPressed: () async {
                        if (validator.validate() ?? false) {
                          deleteAccount(deletePasswordController.text);
                        }
                      },
                    ),
                    SizedBox(
                      height: 20,
                    ),
                  ],
                ),
              ),
            ),
          );
        });
  }

  //SYNC ALERT DIALOG
  alertSync(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    bool maxWidth = width > tabletWidth ? true : false;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
              child: SingleChildScrollView(
                child: AlertDialog(
                    content: Container(
                  width: maxWidth ? width * 0.4 : width * 0.8,
                  child: Form(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text("Unsync data will loss this process make sure willing to log out"),
                        SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () async {
                                logOutUserAndAllShopes();
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: maxWidth ? width * 0.18 : width * 0.3,
                                child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                            GestureDetector(
                              onTap: () async {
                                Navigator.pop(context);
                                //Navigator.pop(context);
                                //Navigator.pop(context);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: maxWidth ? width * 0.18 : width * 0.3,
                                child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
              ),
            );
          });
        });
  }
}
