// ignore_for_file: sdk_version_set_literal, sdk_version_ui_as_code

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/screens/manage_shifts/functions/manage_shifts_function.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../../../utils/common_widgets/common_search_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/common_widgets/header_footer.dart';
import 'add_shift_details.dart';

class ViewShiftDetails extends StatefulWidget {
  const ViewShiftDetails({Key? key}) : super(key: key);

  @override
  State<ViewShiftDetails> createState() => _ViewShiftDetailsState();
}

class _ViewShiftDetailsState extends State<ViewShiftDetails> {
  bool isLock = false;
  bool isLoading = false;
  List<ShiftwiseSalesModel> shiftSalesList = [];
  final TextEditingController searchController = new TextEditingController();
  ManageShiftsFunction manageShiftsFunction = ManageShiftsFunction();

  deleteShiftItems(ShiftwiseSalesModel shiftDetails) async {
    try {
      if (!isLock) {
        var tempSalesCategoryList = await manageShiftsFunction.deleteShiftDetailsItem(shiftDetails);
        setState(() {
          shiftSalesList.clear();
          shiftSalesList.addAll(tempSalesCategoryList);
        });
        Navigator.pop(context);
        isLock = false;
      }
    } on Exception catch (e) {
      isLock = false;
      ErrorLogsFunction.insertErrorLogs(e, "deleteShiftItems - ViewShiftDetails");
    }
  }

  //DYNAMIC KEYWORD ALERT DIALOG
  deleteDialog(BuildContext context, ShiftwiseSalesModel shiftDetailsModel) {
    return showDialog(
        context: context,
        builder: (context) {
          return DeleteDialog(
            message: "Are you sure you want to delete?",
            buttonNameOne: "Yes",
            onPressOne: () {
              deleteShiftItems(shiftDetailsModel);
            },
            buttonNameTwo: "Cancel",
            onPressTwo: () {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }

  //Get the sales category from the hive box
  getShiftSalesDetails() async {
    setState(() {
      isLoading = true;
    });
    setState(() {
      shiftSalesList.clear();
      shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2));
      isLoading = false;
    });
  }

  ///Search shift detail items
  void searchShiftItems(String value) {
    setState(() {
      shiftSalesList.clear();
      if (value.isEmpty) {
        shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2));
      } else {
        shiftSalesList.addAll(
            ListUtility.shiftSalesList.where((element) => element.status != 2 && element.shiftName!.toLowerCase().contains(value.toLowerCase())));
      }
    });
  }

  initPageDetails() async {
    await getShiftSalesDetails();
  }

  onRefresh() async {
    setState(() {
      isLoading = true;
    });
    await shiftListWidget();
    setState(() {
      isLoading = false;
    });
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text("View Shift Details", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: shiftAddButton(),
          )
        ],
      ),
      body: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
        child: isLoading
            ? CircularProgressIndicator()
            : Center(
                child: RefreshIndicator(
                  onRefresh: () async {
                    await onRefresh();
                  },
                  child: Padding(
                    padding: EdgeInsets.all(10.0),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 3,
                        ),
                        Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: CommonSearchComponent(
                            onSearch: (value) {
                              searchShiftItems(value);
                            },
                          ),
                        ),
                        const SizedBox(
                          height: 10,
                        ),
                        shiftListWidget()
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  shiftListWidget() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: shiftSalesList.isNotEmpty
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: ListView.builder(
                  scrollDirection: Axis.vertical,
                  itemCount: shiftSalesList.length,
                  shrinkWrap: true,
                  physics: ScrollPhysics(),
                  itemBuilder: (context, index) {
                    var item = shiftSalesList[index];
                    var shiftDetails = shiftSalesList.firstWhereOrNull(
                      (element) => element.id == item.id,
                    );
                    return Column(
                      children: [
                        InkWell(
                          onTap: () async {
                            // if (PermissionFunctions.checkPermission(30)) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => AddShiftType(
                                          shiftDetails: item,
                                          isUpdate: true,
                                        ))).then((value) => {
                                  if (value ?? false)
                                    {
                                      setState(() {
                                        initPageDetails();
                                      })
                                    }
                                });
                            // }
                          },
                          onLongPress: () async {
                            // if (PermissionFunctions.checkPermission(32)) {
                            deleteDialog(context, item);
                            // }
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Expanded(
                                  child: Text(
                                    item.shiftName ?? "",
                                    style: black16TextStyle,
                                  ),
                                ),
                                if (shiftDetails != null) ...[
                                  Container(
                                    width: width * 0.35,
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      "${item.fromTime}-${item.toTime}",
                                      style: black14RegularTextStyle,
                                    ),
                                  ),
                                ],
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 15.0),
                                  child: SvgPicture.asset(
                                    leftArrowSVG,
                                    color: primaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const Divider(
                          thickness: 1,
                        ),
                      ],
                    );
                  }),
            )
          : const Center(child: Text("No Shifts Available")),
    );
  }

  Widget shiftAddButton() {
    return InkWell(
        onTap: () async {
          // if (PermissionFunctions.checkPermission(29)) {
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => AddShiftType(
                        isUpdate: false,
                      ))).then((value) => {
                setState(() {
                  initPageDetails();
                  // shiftListWidget();
                })
              });
          // }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
