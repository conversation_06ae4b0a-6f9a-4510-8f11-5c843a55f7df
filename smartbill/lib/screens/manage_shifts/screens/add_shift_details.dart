import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/screens/manage_shifts/functions/manage_shifts_function.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/utils/common_function/common_master_table_function.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../../../model/hive_model/master_table_model.dart';
import '../../../model/time_interval_model.dart';
import '../../../utils/common_function/validation_utility.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/common_widgets/header_footer.dart';

// ignore: must_be_immutable
class AddShiftType extends StatefulWidget {
  ShiftwiseSalesModel? shiftDetails;
  bool isUpdate = false;
  AddShiftType({Key? key, this.shiftDetails, required this.isUpdate}) : super(key: key);

  @override
  // ignore: no_logic_in_create_state
  _AddShiftTypeState createState() => _AddShiftTypeState();
}

class _AddShiftTypeState extends State<AddShiftType> {
  Validator validator = Validator();
  final shiftNameController = TextEditingController();
  TextEditingController fromTimeController = TextEditingController();
  TextEditingController toTimeController = TextEditingController();
  bool isSubmitButtonLock = false;
  bool isLoading = false;
  bool isShiftName = false;
  bool isFromTime = false;
  bool isToTime = false;
  bool timeIntervalError = false;
  DateTime fromSelectedDate = DateTime.now();
  TimeOfDay fromSelectedTime = TimeOfDay.now();
  DateTime toSelectedDate = DateTime.now();
  TimeOfDay toSelectedTime = TimeOfDay.now();
  List<TimeInterval> timeIntervals = [];
  TimeInterval? selectedTimeInterval;
  _AddShiftTypeState();

  ShiftwiseSalesModel shiftBasedSalesModel = ShiftwiseSalesModel();
  ManageShiftsFunction manageShiftsFunction = ManageShiftsFunction();
  MasterTableFunction masterTableFunction = MasterTableFunction();

  List<ShiftwiseSalesModel> shiftBasedSalesList = [];

  List<MasterTableModel> masterTableListItem = [];
  int count = 0;

  assignValues() {
    if (widget.shiftDetails != null) {
      setState(() {
        shiftNameController.text = widget.shiftDetails?.shiftName ?? "";
        fromTimeController.text = widget.shiftDetails?.fromTime ?? ""; 
        fromSelectedDate = DateFormat('h:mm a').parse(widget.shiftDetails?.fromTime ?? DateTime.now().toString());
        toTimeController.text = widget.shiftDetails?.toTime ?? "";
        toSelectedDate = DateFormat('h:mm a').parse(widget.shiftDetails?.toTime ?? DateTime.now().toString());
        selectedTimeInterval = timeIntervals.firstWhereOrNull(
          (interval) => interval.toString() == widget.shiftDetails?.duration,
        );
      });
    }
  }

  onPressSubmitButton() async {
    try {
      if (!isSubmitButtonLock) {
        isSubmitButtonLock = true;
        if (!isShiftName && !isFromTime && !isToTime) {
          String formattedFromTime = fromTimeController.text;
          String formattedToTime = toTimeController.text;
          if (manageShiftsFunction.hasOverlap(widget.isUpdate, widget.shiftDetails, fromTimeController.text, toTimeController.text)) {
            showToast("Shift overlaps with existing shifts.");
          } else {
            await manageShiftsFunction.upsertShiftSalesList(
                widget.isUpdate, widget.shiftDetails, shiftNameController.text, formattedFromTime, formattedToTime, selectedTimeInterval.toString());
            List<Map<String, dynamic>> valueList = ListUtility.salesCategoryList.map((e) => e.toJson()).toList();
            String valueString = json.encode(valueList);
            await masterTableFunction.upsertMasterTable(key_shiftBasedSales, valueString);
            shiftNameController.clear();
            FocusScope.of(context).unfocus();
            Navigator.pop(context, true);
          }
        }
        isSubmitButtonLock = false;
      }
    } catch (e) {
      isSubmitButtonLock = false;
      isShiftName = false;
      isFromTime = false;
      isToTime = false;
      ErrorLogsFunction.insertErrorLogs(e, "onPressSubmitButton - AddSalesCategory");
    }
  }

  List<TimeInterval> generateTimeIntervals(int intervalMinutes, int maxDuration) {
    List<TimeInterval> intervals = [];
    for (int i = intervalMinutes; i <= maxDuration; i += intervalMinutes) {
      int hours = i ~/ 60;
      int minutes = i % 60;
      intervals.add(TimeInterval(hours: hours, minutes: minutes));
    }
    selectedTimeInterval = intervals[0];
    return intervals;
  }


// Function to calculate toTime from the selected time interval and fromTime
  addToTime(DateTime? fromDate, int hours, int minutes) {
    if (fromDate != null) {
      ///Add the time intervals value
      toSelectedDate = fromDate.add(Duration(hours: hours, minutes: minutes));
      // Convert DateTime back to TimeOfDay
      toSelectedTime = TimeOfDay.fromDateTime(toSelectedDate);

      ///Assign value of to date and time.
      toTimeController.text = manageShiftsFunction.formatTime(toSelectedTime);
      setState(() {
        isToTime = toTimeController.text.isNotEmpty ? false : true;
      });
    }
  }

  initPageDetails() async {
    timeIntervals = generateTimeIntervals(30, 1440);
    assignValues();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(!widget.isUpdate ? "Add Shift Details" : "Update Shift Details",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: SingleChildScrollView(
          child: isLoading
              ? Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        padding: EdgeInsets.all(10.0),
                        width: width > tabletWidth ? width * 0.5 : width * 0.95,
                        child: Form(
                          key: validator.formkey,
                          child: Column(
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: SizedBox(
                                      child: Padding(
                                        padding: EdgeInsets.only(right: width > tabletWidth ? 8 : 0, top: 10),
                                        child: EditText(
                                          mController: shiftNameController,
                                          inputType: TextInputType.text,
                                          isPassword: false,
                                          text: "Shift Name",
                                          onChange: (value) {
                                            if (shiftNameController.text.isNotEmpty)
                                              setState(() {
                                                isShiftName = false;
                                              });
                                          },
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              isShiftName ? manageShiftsFunction.buildErrorValidatioWidget() : SizedBox.shrink(),
                              SizedBox(height: 10),
                              estimationTimeWidget(),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 10,
                                ),
                                child: RoundedButton(
                                  title: widget.isUpdate ? "Update Shift" : "Add Shift",
                                  onPressed: () async {
                                    setState(() {
                                      isShiftName = shiftNameController.text.isNotEmpty ? false : true;
                                      isFromTime = fromTimeController.text.isNotEmpty ? false : true;
                                      isToTime = toTimeController.text.isNotEmpty ? false : true;
                                    });
                                    onPressSubmitButton();
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
      ),
    );
  }

  Widget estimationTimeWidget() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: SizedBox(
                height: 50,
                child: EditText(
                  mController: fromTimeController,
                  inputType: TextInputType.none,
                  fontSize: 10,
                  isPassword: false,
                  text: "From Time",
                  suffixIcon: const Icon(
                    Icons.timer,
                    size: 20.0,
                  ),
                  onTap: () async {
                    DateTime? fromDate = await manageShiftsFunction.timePicker(
                      context,
                      isOnlyTime: false,
                      pickedDate: widget.isUpdate ? DateFormat.Hm().parse(widget.shiftDetails?.fromTime ?? DateTime.now().toString()) : fromSelectedDate,
                      isFrom: true,
                      selectedTimeValue:
                          widget.isUpdate ? TimeOfDay.fromDateTime(DateFormat.Hm().parse(widget.shiftDetails?.fromTime ?? DateTime.now().toString())) : fromSelectedTime,
                    );

                    if (fromDate != null) {
                      setState(() {
                        fromSelectedDate = fromDate;
                        fromSelectedTime = TimeOfDay(hour: fromDate.hour, minute: fromDate.minute);
                        fromTimeController.text = manageShiftsFunction.formatTime(fromSelectedTime);
                        isFromTime = fromTimeController.text.isNotEmpty ? false : true;
                        addToTime(fromSelectedDate, selectedTimeInterval?.hours ?? 0, selectedTimeInterval?.minutes ?? 0);
                      });
                    }
                  },
                ),
              ),
            ),
            SizedBox(width: 10),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 4.0),
              child: SizedBox(
                width: 110,
                child: DropdownButtonFormField<TimeInterval>(
                  isExpanded: true,
                  menuMaxHeight: 250,
                  value: selectedTimeInterval,
                  onChanged: (TimeInterval? newValue) {
                    setState(() {
                      selectedTimeInterval = newValue;
                      addToTime(fromSelectedDate, newValue?.hours ?? 0, newValue?.minutes ?? 0);
                    });
                  },
                  items: timeIntervals.map<DropdownMenuItem<TimeInterval>>((TimeInterval value) {
                    return DropdownMenuItem<TimeInterval>(
                      value: value,
                      child: Text(value.toString(), style: black13RegularTextStyle),
                    );
                  }).toList(),
                  decoration: InputDecoration(
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(10)),
                    contentPadding: const EdgeInsets.symmetric(
                      horizontal: 5.0,
                    ),
                    focusedBorder: OutlineInputBorder(),
                  ),
                ),
              ),
            ),
          ],
        ),
        isFromTime ? manageShiftsFunction.buildErrorValidatioWidget() : SizedBox.shrink(),
        SizedBox(height: 10),
        EditText(
          mController: toTimeController,
          inputType: TextInputType.none,
          fontSize: 10,
          isPassword: false,
          text: "To Time",
          showcursor: false,
          readOnly: true,
          suffixIcon: const Icon(
            Icons.timer,
            size: 20.0,
          ),
        ),
        isToTime ? manageShiftsFunction.buildErrorValidatioWidget() : SizedBox.shrink(),
      ],
    );
  }
}
