class ShiftwiseSalesModel {
  int? id;
  String? shiftId;
  String? shiftName;
  String? duration;
  String? fromTime;
  String? toTime;
  String? workspaceID;
  int? status;
  int? sync;
  bool? isActive;
  String? createdDate;

  ShiftwiseSalesModel(
      {this.id,
      this.shiftId,
      this.shiftName,
      this.duration,
      this.fromTime,
      this.toTime,
      this.workspaceID,
      this.status,
      this.sync,
      this.isActive,
      this.createdDate});

  ShiftwiseSalesModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    shiftId = json['shiftId'];
    shiftName = json['shiftName'];
    duration = json['duration'];
    fromTime = json['fromTime'];
    toTime = json['toTime'];
    workspaceID = json['workspaceID'];
    status = json['status'];
    sync = json['sync'];
    isActive = json['isActive'];
    createdDate = json['createdDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['shiftId'] = this.shiftId;
    data['shiftName'] = this.shiftName;
    data['duration'] = this.duration;
    data['fromTime'] = this.fromTime;
    data['toTime'] = this.toTime;
    data['workspaceID'] = this.workspaceID;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['isActive'] = this.isActive;
    data['createdDate'] = this.createdDate;
    return data;
  }
}
