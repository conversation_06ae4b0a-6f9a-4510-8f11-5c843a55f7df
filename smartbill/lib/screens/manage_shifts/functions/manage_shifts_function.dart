import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../../utils/common_function/common_master_table_function.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/error_logs/error_logs_function.dart';

class ManageShiftsFunction {
  MasterTableFunction masterTableFunction = MasterTableFunction();

  ///Store and Update the shift details item in the static list of
  ///ShiftDetails list  based on shiftId
  upsertShiftSalesList(bool isUpdate, ShiftwiseSalesModel? shiftDetails, String shiftNameControllerText, String fromTimeControllerText,
      String toTimeControllerText, String duration) {
    int autoIncrementId = 0;
    try {
      if (isUpdate && shiftDetails != null) {
        int index = ListUtility.shiftSalesList.indexWhere((element) => element.shiftId == shiftDetails?.shiftId);
        if (index != -1) {
          ListUtility.shiftSalesList[index] = ShiftwiseSalesModel(
            id: shiftDetails.id,
            shiftId: shiftDetails.shiftId,
            shiftName: shiftNameControllerText,
            fromTime: fromTimeControllerText,
            toTime: toTimeControllerText,
            duration: duration,
            workspaceID: MyApp.activeWorkspace.workspaceId!,
            createdDate: DateTime.now().toString(),
            status: 1,
            sync: 0,
          );
        }
      } else {
        var uuid = new Uuid();
        autoIncrementId = ListUtility.shiftSalesList.isNotEmpty ? ListUtility.shiftSalesList.last.id! + 1 : 1;
        shiftDetails = ShiftwiseSalesModel(
          id: autoIncrementId,
          shiftId: uuid.v4().toString(),
          shiftName: shiftNameControllerText,
          fromTime: fromTimeControllerText,
          toTime: toTimeControllerText,
          duration: duration,
          workspaceID: MyApp.activeWorkspace.workspaceId!,
          createdDate: DateTime.now().toString(),
          status: 0,
          sync: 0,
        );
        ListUtility.shiftSalesList.add(shiftDetails);
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "upsertShiftSalesList - ManageShiftsFunction");
    }
  }

  ///delete the Shift item from the list
  deleteShiftDetailsItem(ShiftwiseSalesModel? shiftDetails) async {
    List<ShiftwiseSalesModel> shiftList = [];
    try {
      if (shiftDetails != null) {
        int index = ListUtility.shiftSalesList.indexWhere(
          (element) => element.shiftId == shiftDetails.shiftId,
        );
        if (index != -1) {
          ListUtility.shiftSalesList[index] = ShiftwiseSalesModel(
              id: shiftDetails.id,
              shiftId: shiftDetails.shiftId,
              shiftName: shiftDetails.shiftName,
              fromTime: shiftDetails.fromTime,
              toTime: shiftDetails.toTime,
              duration: shiftDetails.duration,
              workspaceID: shiftDetails.workspaceID,
              status: 2,
              sync: 0);
        }
        shiftList.clear();
        shiftList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2));
        List<Map<String, dynamic>> valueList = ListUtility.shiftSalesList.map((e) => e.toJson()).toList();
        String valueString = json.encode(valueList);
        await masterTableFunction.upsertMasterTable(key_shiftBasedSales, valueString);
        return shiftList;
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "deleteShiftDetailsItem - ManageShiftsFunction");
    }
  }

  Future<List<ShiftwiseSalesModel>> getNonSyncManageShiftList() async {
    if (ListUtility.shiftSalesList.isNotEmpty) {
      List<ShiftwiseSalesModel> shiftSalesList = [];
      shiftSalesList.clear();
      shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.sync == 0));
      return shiftSalesList;
    }
    return [];
  }


  ///Sync Master table data from API
  syncManageShiftsFromApi(List<ShiftwiseSalesModel>? shiftDetailsList) async {
    try {
      if (shiftDetailsList?.length != null && shiftDetailsList?.length != 0) {
        dynamic tempList = [];
        tempList.addAll(ListUtility.shiftSalesList);
        if (ListUtility.shiftSalesList.isNotEmpty) {
          for (ShiftwiseSalesModel model in shiftDetailsList!) {
            model.sync = 1;
            int modelIndex = tempList.indexWhere((element) => element.shiftId == model.shiftId);
            if (modelIndex >= 0) {
              model.status = model.isActive == true ? 1 : 2;
              ListUtility.shiftSalesList[modelIndex] = model;
            } else {
              model.isActive == true ? 0 : 2;
              ListUtility.shiftSalesList.add(model);
            }
          }
        } else {
          for (ShiftwiseSalesModel model in shiftDetailsList!) {
            ListUtility.shiftSalesList.add(ShiftwiseSalesModel(
                // id: model.id,
                shiftId: model.shiftId,
                createdDate: model.createdDate,
                isActive: model.isActive, 
                shiftName: model.shiftName,
                fromTime: model.fromTime,
                toTime: model.toTime,
                duration: model.duration,
                workspaceID: MyApp.activeWorkspace.workspaceId!,
                sync: 1,
                status: model.isActive == true ? 0 : 2));
          }
        }
        List<Map<String, dynamic>> valueList = ListUtility.shiftSalesList.map((e) => e.toJson()).toList();
        String valueString = json.encode(valueList);
        await masterTableFunction.upsertMasterTable(key_shiftBasedSales, valueString);
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "syncManageShiftsFromApi - ManageShiftsFunction");
    }
  }

// Function to check overlapping of time interval on adding multiple shift 
    bool hasOverlap(bool isUpdate, ShiftwiseSalesModel? shiftDetails, String newFromTime, String newToTime) {
    DateTime newFrom = DateFormat('h:mm a').parse(newFromTime);
    DateTime newTo = DateFormat('h:mm a').parse(newToTime);

    for (var shift in ListUtility.shiftSalesList.where((element) => element.status != 2)) {
      if (isUpdate && (shift.id == shiftDetails?.id)) {
        continue; // Skip current shift if updating
      }
      DateTime existingFrom = DateFormat('h:mm a').parse(shift.fromTime ?? DateTime.now().toString());
      ;
      DateTime existingTo = DateFormat('h:mm a').parse(shift.toTime ?? DateTime.now().toString());
      ;
      // Check for overlap considering exclusive intervals
      if ((newFrom.isBefore(existingTo) && newTo.isAfter(existingFrom)) || ((newFrom == existingFrom) && (newTo == existingTo))) {
        return true; // Overlap detected
      }
    }
    return false;
  }

  Future<DateTime?> timePicker(BuildContext context,
      {bool isOnlyTime = true, DateTime? pickedDate, bool isFrom = true, DateTime? fromDate, TimeOfDay? selectedTimeValue}) async {
    DateTime selectedDateTime;
    final TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTimeValue ?? TimeOfDay.now(),
    );

    if (picked != null) {
      selectedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked.hour,
        picked.minute,
      );
      if (!isOnlyTime && pickedDate != null) {
        selectedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          picked.hour,
          picked.minute,
        );
      }
      if (isFrom) {
        return selectedDateTime;
      } else {
        if (fromDate != null && selectedDateTime.isAfter(fromDate)) {
          return selectedDateTime;
        } else {
          showToast("Please select a time later than the from date.");
        }
      }
    } else {
      return null;
    }
    return null;
  }

  String formatTime(TimeOfDay time, {bool isFrom = true, DateTime? fromDateTime}) {
    int hour = time.hourOfPeriod;
    int minute = time.minute;
    String period = time.period == DayPeriod.am ? 'AM' : 'PM';

    return '$hour:${minute.toString().padLeft(2, '0')} $period';
  }
  

  Widget buildErrorValidatioWidget() {
  return Row(
    mainAxisAlignment: MainAxisAlignment.spaceBetween,
    children: [
           Container(
              alignment: Alignment.centerLeft,
              padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
              child: const Text(
                "This field is required",
                style: TextStyle(color: Colors.red, fontSize: 11.5),
              ),
            )
    ],
  );
}

}
