// ignore_for_file: unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../model/payment_category_model.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../../main.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/payment_type_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';

// ignore: must_be_immutable
class AddPaymentType extends StatefulWidget {
  PaymentType? addPaymentType;

  // ignore: prefer_typing_uninitialized_variables
  AddPaymentType({Key? key, this.addPaymentType}) : super(key: key);

  @override
  // ignore: no_logic_in_create_state
  _AddPaymentTypeState createState() => _AddPaymentTypeState(addPaymentType);
}

class _AddPaymentTypeState extends State<AddPaymentType> {
  Validator validator = Validator();
  FnUtilities fnUtilities = new FnUtilities();
  final dbHelper = DatabaseHelper.instance;
  DBOperations funCommonDB = DBOperations();
  final paymentController = TextEditingController();
  CommonDB commonDB = CommonDB();
  PaymentTypeDBScript paymentTypeDBScript = PaymentTypeDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  bool isLock = false;
  PaymentType? paymentType;
  PaymentCategory? selectedPaymentCategory;
  bool isLoading = false;
  bool paymentCategoryError = false;
  _AddPaymentTypeState(this.paymentType);

  clearTextFields() {
    paymentController.clear();
    selectedPaymentCategory = null ?? PaymentCategory();
  }

  assignValue() {
    if (paymentType != null) {
      paymentController.text = paymentType?.paymentName ?? "";
      if (paymentType?.paymentCategoryID != null) {
        setState(() {
          selectedPaymentCategory = ListUtility.paymentCatList.firstWhere((element) => element.paymentCategoryID == paymentType?.paymentCategoryID);
        });
      } else {
        selectedPaymentCategory = null ?? PaymentCategory();
      }
    }
  }

  /// check if user first time open
  Future<void> initSettings() async {
    await fnUtilities.getAllPaymentCategoryFromLocaldb();
  }

  insertAndUpdateFunction(PaymentType model, String operation) async {
    var paymentType;
    if (operation == "insert") {
      paymentType = {
        db_paymentTypes_paymentTypeId: model.paymentTypeID,
        db_paymentTypes_paymentTypeName: model.paymentName,
        db_paymentTypes_createdDate: model.createdDate,
        db_product_rowStatus: model.rowStatus,
        db_paymentTypes_paymentTypeSync: model.syncStatus,
        db_workspace_workspaceId: model.workspaceID,
        db_Payment_category_ID: model.paymentCategoryID
      };
      await dbScript.dbInsert(db_paymentTypes, paymentType);
    } else {
      paymentType = {
        db_paymentTypes_paymentTypeName: model.paymentName,
        db_product_rowStatus: model.rowStatus,
        db_paymentTypes_paymentTypeSync: model.syncStatus,
        db_workspace_workspaceId: model.workspaceID,
        db_Payment_category_ID: model.paymentCategoryID
      };
      await dbScript.updateModel(db_paymentTypes, paymentType, db_paymentTypes_paymentTypeId, model.paymentTypeID);
    }
  }

  onPaymentTypeRegister() async {
    try {
      LoadingAlertWidget.onLoading(context);;
      int? isExists = await commonDB.checkValueExists("PaymentTypes", "paymentTypeName", paymentController.text, "PaymentTypeId",
          paymentType?.paymentTypeID != null ? paymentType?.paymentTypeID ?? "" : "");
      if (isExists != 0) {
        showToast("Payment type already exists");
      } else {
        PaymentType model = PaymentType();
        model.paymentName = paymentController.text;
        model.syncStatus = 0;
        model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();
        if (paymentType?.paymentTypeID == null) {
          var uuid = const Uuid();
          model.paymentTypeID = uuid.v4().toString();
          model.createdDate = DateTime.now().toString();
          model.rowStatus = 0;
          model.paymentCategoryID = selectedPaymentCategory?.paymentCategoryID;
          await insertAndUpdateFunction(model, "insert");
          //await paymentTypeDBScript.insertPaymentType(model);
          showToast("Payment type added successfully");
        } else {
          model.paymentTypeID = paymentType?.paymentTypeID;
          model.paymentCategoryID = selectedPaymentCategory?.paymentCategoryID;

          model.rowStatus = await commonDB.checkNonSyncCommonFunction("PaymentTypes", "paymentTypeId",
                      paymentType?.paymentTypeID != null ? paymentType?.paymentTypeID.toString() : "", "paymentTypeSync") ==
                  0
              ? 1
              : 0;
          await insertAndUpdateFunction(model, "update");
          //await paymentTypeDBScript.updatePaymentType(model);
          showToast("Payment type updated successfully");
          paymentType = PaymentType();
        }
        clearTextFields();
      }
      await fnUtilities.getAllPaymentType();
      LoadingAlertWidget.onStopping();
      Navigator.pop(context, true);
    } catch (ex) {
      showToast("Payment type issue");
      ErrorLogsFunction.insertErrorLogs(ex, "onPaymentTypeRegister - AddPaymentType");
      LoadingAlertWidget.onStopping();
    }
  }

  initPageDetails() async {
    await initSettings();
    await assignValue();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(paymentType?.paymentTypeID == null ? "Add Payment Type" : "Update Payment Type",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      //drawer: BaseDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: SingleChildScrollView(
          child: isLoading
              ? Center(child: CircularProgressIndicator())
              : Column(
                  children: [
                    Align(
                      alignment: Alignment.center,
                      child: Container(
                        padding: EdgeInsets.all(15.0),
                        width: width > tabletWidth ? width * 0.5 : width * 0.95,
                        // decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 10),
                        child: Form(
                          key: validator.formkey,
                          child: Column(
                            children: [
                              BootstrapRow(
                                children: [
                                  BootstrapCol(
                                    sizes: 'col-md-6 col-sm-12',
                                    child: Column(
                                      children: [
                                        paymentCategoryDropdownWidget(),
                                        paymentCategoryError
                                            ? Padding(
                                                padding: const EdgeInsets.only(top: 5.0),
                                                child: Align(
                                                    alignment: Alignment.bottomLeft,
                                                    child: Text(
                                                      "This field is required",
                                                      style: TextStyle(color: Color.fromARGB(255, 242, 60, 60)),
                                                    )),
                                              )
                                            : SizedBox.shrink()
                                      ],
                                    ),
                                  ),
                                  BootstrapCol(
                                    sizes: 'col-md-6 col-sm-12',
                                    child: Row(
                                      children: [
                                        Expanded(
                                          child: SizedBox(
                                            // width: width > tabletWidth ? width * 0.23 : width * 0.78,
                                            child: Padding(
                                              padding: EdgeInsets.only(left: width > tabletWidth ? 8 : 0, top: 10),
                                              child: EditText(
                                                  mController: paymentController,
                                                  inputType: TextInputType.text,
                                                  isPassword: false,
                                                  text: "Payment Type",
                                                  onTapOutside: (event) {
                                                    FocusManager.instance.primaryFocus?.unfocus();
                                                  },
                                                  validator: (value) {
                                                    if (value.isEmpty)
                                                      return 'This field is required';
                                                    else {
                                                      try {
                                                        ListUtility.paymentTypeList.firstWhere((element) =>
                                                            element.paymentName == value && element.paymentTypeID != paymentType?.paymentTypeID);
                                                        return 'This name is already used';
                                                      } catch (e) {
                                                        return null;
                                                      }
                                                    }
                                                  }),
                                            ),
                                          ),
                                        ),
                                        // Container(
                                        //   alignment: Alignment.topCenter,
                                        //   child: IconButton(
                                        //     onPressed: () {
                                        //       setState(() {
                                        //         paymentController.clear();
                                        //         payment = null;
                                        //       });
                                        //     },
                                        //     icon: Icon(Icons.clear),
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  vertical: 10,
                                ),
                                child: RoundedButton(
                                  title: paymentType?.paymentTypeID == null ? "Add Payment Type" : "Update Payment Type",
                                  onPressed: () async {
                                    if (!isLock) {
                                      isLock = true;
                                      if ((validator.validate()) && selectedPaymentCategory != null) {
                                        await onPaymentTypeRegister();
                                      } else {
                                        setState(() {
                                          paymentCategoryError = true;
                                        });
                                      }
                                      isLock = false;
                                    }
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // paymentTypeList.length != 0
                    //     ? SingleChildScrollView(
                    //         child: GridView.builder(
                    //             shrinkWrap: true,
                    //             scrollDirection: Axis.vertical,
                    //             gridDelegate:
                    //                 SliverGridDelegateWithFixedCrossAxisCount(
                    //                     crossAxisCount: 2,
                    //                     crossAxisSpacing: 5,
                    //                     mainAxisSpacing: 5,
                    //                     childAspectRatio: 3.5),
                    //             physics: ScrollPhysics(),
                    //             itemCount: paymentTypeList.length,
                    //             itemBuilder: (BuildContext context, int index) {
                    //               var item = paymentTypeList[index];
                    //               return InkWell(
                    //                   onDoubleTap: () {
                    //                     if (!isLock) {
                    //                       isLock = true;
                    //                       setState(() {
                    //                         paymentController.text = item.paymentName;
                    //                         payment = item;
                    //                       });
                    //                       isLock = false;
                    //                     }
                    //                   },
                    //                   onLongPress: () {
                    //                     deleteDialog(context, item);
                    //                   },
                    //                   child: Container(
                    //                     alignment: Alignment.centerLeft,
                    //                     width: width / 1,
                    //                     height: 40,
                    //                     padding:
                    //                         EdgeInsets.symmetric(horizontal: 10.0),
                    //                     decoration: BoxDecoration(
                    //                       borderRadius: BorderRadius.circular(5.0),
                    //                       border: Border.all(
                    //                           color: Colors.grey,
                    //                           style: BorderStyle.solid,
                    //                           width: 0.80),
                    //                     ),
                    //                     child: text(item.paymentName,
                    //                         isCentered: false,
                    //                         textColor: primaryTextColor,
                    //                         fontSize: textSizeSMedium,
                    //                         fontFamily: fontSemibold),
                    //                   ));
                    //             }),
                    //       )
                    //     : Container(),
                  ],
                ),
        ),
      ),
    );
  }

  paymentCategoryDropdownWidget() {
    return Padding(
      padding: const EdgeInsets.only(top: 10.0),
      child: InputDecorator(
        decoration: InputDecoration(
          contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 1.0),
          labelText: 'Payment Category',
          border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
        ),
        child: DropdownButtonHideUnderline(
          child: Theme(
            data: Theme.of(context).copyWith(canvasColor: Colors.blue.shade50),
            child: DropdownButton(
              isExpanded: true,
              elevation: 0,
              isDense: true,
              hint: Text(
                'Please Select...',
                style: black13RegularTextStyle,
              ),
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: primaryTextColor,
                size: 20,
              ),
              value: selectedPaymentCategory,
              style: black13RegularTextStyle,
              items: ListUtility.paymentCatList.where((item) => item.status != 1).map<DropdownMenuItem<PaymentCategory>>((PaymentCategory value) {
                return DropdownMenuItem<PaymentCategory>(
                  value: value,
                  child: Text(value.paymentCategoryName ?? ""),
                );
              }).toList(),
              onChanged: (PaymentCategory? newValue) {
                setState(() {
                  if (newValue != null) {
                    selectedPaymentCategory =
                        ListUtility.paymentCatList.firstWhere((element) => element.paymentCategoryID == newValue.paymentCategoryID);
                    // print(selectedPaymentCategory.paymentCategoryName);
                    if (paymentCategoryError) {
                      paymentCategoryError = false;
                    }
                  } else {
                    setState(() {
                      paymentCategoryError = true;
                    });
                  }
                });
              },
            ),
          ),
        ),
      ),
    );
  }
}
