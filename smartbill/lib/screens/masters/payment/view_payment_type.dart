// ignore_for_file: sdk_version_set_literal, sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../../model/payment_category_model.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../../main.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/payment_type_db_script.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import 'add_payment_table.dart';
import '../../../utils/common_widgets/header_footer.dart';

class ViewPayment extends StatefulWidget {
  const ViewPayment({Key? key}) : super(key: key);

  @override
  State<ViewPayment> createState() => _ViewPaymentState();
}

class _ViewPaymentState extends State<ViewPayment> {
  final dbHelper = DatabaseHelper.instance;
  DBOperations funCommonDB = DBOperations();
  CommonDB commonDB = CommonDB();
  PaymentTypeDBScript paymentTypeDBScript = PaymentTypeDBScript();
  bool isLock = false;
  bool isLoading = false;
  List<PaymentCategory> paymentCategoryList = [];
  DBFuctionsScript dbScript = DBFuctionsScript();
  double width = 0.0;
  //DYNAMIC KEYWORD ALERT DIALOG
  deleteDialog(BuildContext context, PaymentType paymentType) {
    double width = MediaQuery.of(context).size.width;
    bool maxWidth = width > tabletWidth ? true : false;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: maxWidth ? width * 0.35 : width * 0.8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      child: TextWidget("Are you sure, you want to delete?",
                          textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: maxWidth ? width * 0.15 : width * 0.3,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              if (await commonDB.checkNonSyncCommonFunction(
                                      "PaymentTypes", "paymentTypeId", paymentType.paymentTypeID.toString(), "paymentTypeSync") ==
                                  0) {
                                paymentType.workspaceID = MyApp.activeWorkspace.workspaceId;
                                paymentType.syncStatus = 0;
                                paymentType.rowStatus = 2;
                                await paymentTypeDBScript.updatePaymentType(paymentType);
                              } else {
                                await commonDB.permanentDeleteCommonFunction("PaymentTypes", "paymentTypeId", paymentType.paymentTypeID.toString());
                              }
                              setState(() {
                                fnUtilities.getAllPaymentType();
                              });
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: maxWidth ? width * 0.15 : width * 0.3,
                            child: TextWidget("Delete", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  /// check if user first time open
  Future<void> initSettings() async {
    await fnUtilities.getAllPaymentCategoryFromLocaldb();
  }

  initPageDetails() async {
    setState(() {
      isLoading = true;
    });
    await initSettings();
    setState(() {
      isLoading = false;
    });
  }

  onRefresh() async {
    setState(() {
      isLoading = true;
    });
    await fnUtilities.getAllPaymentType();
    await paymentListWidget();
    setState(() {
      isLoading = false;
    });
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text("View Payment Type", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: productAddButton(),
          )
        ],
      ),
      body: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
        child: isLoading
            ? CircularProgressIndicator()
            : Center(
                child: RefreshIndicator(
                  onRefresh: () async {
                    onRefresh();
                  },
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 5.0, horizontal: 10.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Text(
                                'Payment Name',
                                style: black18w700.copyWith(fontWeight: FontWeight.w500),
                              ),
                            ),
                            SizedBox(
                              width: 5.0,
                            ),
                            Container(
                              alignment: Alignment.centerLeft,
                              width: width > tabletWidth ? width * 0.480 : width * 0.525,
                              child: Text(
                                'Category',
                                style: black18w700.copyWith(fontWeight: FontWeight.w500),
                              ),
                            )
                          ],
                        ),
                      ),
                      Divider(
                        thickness: 2.0,
                      ),
                      paymentListWidget(),
                    ],
                  ),
                ),
              ),
      ),
    );
  }

  Widget paymentListWidget() {
    return ListUtility.paymentTypeList.isNotEmpty
        ? Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: ListView.builder(
                itemCount: ListUtility.paymentTypeList.length,
                itemBuilder: (context, index) {
                  var item = ListUtility.paymentTypeList[index];
                  var paymentCategory = ListUtility.paymentCatList.firstWhere(
                    (element) => element.paymentCategoryID == item.paymentCategoryID,
                    orElse: () => PaymentCategory(),
                  );

                  return Column(
                    children: [
                      InkWell(
                        onTap: () {
                          if (PermissionFunctions.checkPermission(30)) {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) => AddPaymentType(addPaymentType: item),
                              ),
                            ).then((value) => onRefresh());
                          }
                        },
                        onLongPress: () {
                          if (PermissionFunctions.checkPermission(32)) {
                            deleteDialog(context, item);
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Expanded(
                                child: Text(
                                  item.paymentName ?? "",
                                  style: black16TextStyle,
                                ),
                              ),
                              SizedBox(width: 30.0),
                              Container(
                                width: width * 0.4,
                                alignment: Alignment.centerLeft,
                                child: Text(
                                  paymentCategory.paymentCategoryName ?? '',
                                  style: black14RegularTextStyle,
                                  overflow: TextOverflow.ellipsis,
                                  softWrap: true,
                                ),
                              ),
                              Padding(
                                padding: const EdgeInsets.symmetric(horizontal: 10.0),
                                child: SvgPicture.asset(
                                  leftArrowSVG,
                                  color: primaryTextColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      Divider(thickness: 1),
                    ],
                  );
                },
              ),
            ),
          )
        : const Center(child: Text("No Payment available"));
  }

  Widget productAddButton() {
    return InkWell(
        onTap: () async {
          if (PermissionFunctions.checkPermission(29)) {
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddPaymentType())).then((value) => {onRefresh()});
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
