// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/customers/customers_page.dart';
import 'package:smartbill/screens/manage_shifts/screens/view_shiftwise_sales.dart';
import 'package:smartbill/screens/products/product_page.dart';
import 'package:smartbill/screens/masters/payment/view_payment_type.dart';
import 'package:smartbill/screens/masters/tables/view_tables.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../model/master_card.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../expenses/screens/manage_expenses_screen.dart';
import '../expenses/screens/expenses_screen.dart';
import '../salescategory/screen/sales_category.dart';
import 'online_platforms/view_online_platform.dart';

class MastersOption extends StatefulWidget {
  @override
  MastersOptionState createState() => MastersOptionState();
}

class MastersOptionState extends State<MastersOption> {
  bool isLock = false;
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  bool enableTableOrders = false;
  List<MasterCard>? mFavouriteList;
  double width = 0.0;
  double height = 0.0;
  initSettings() async {
    var isEnableOnlineOrder = fnUtilities.workSpaceSetValues(enableOnlineOrderKey);
    var PanelCustomerNameValue = fnUtilities.workSpaceSetValues(key_isCustomer);
    // var EnableInventoryValue = fnUtilities.workSpaceSetValues(key_enableInventory);
    var PaymentTypeValue = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
    var EnableExpense = fnUtilities.workSpaceSetValues(key_expense);
    var isEnableSalesCategory = fnUtilities.workSpaceSetValues(key_sales_category);
    var isEnableShiftbasedSales = fnUtilities.workSpaceSetValues(key_shiftBasedSales);

    setState(() {
      if (PanelCustomerNameValue == "1") {
      } else {
        mFavouriteList?.removeWhere((element) => element.name == "Manage Customers");
      }

      // if (EnableInventoryValue == "1") {
      // } else {
      //   mFavouriteList.removeWhere((element) => element.name == "Manage Inventory");
      // }
      if (!PaymentTypeValue) {
        mFavouriteList?.removeWhere((element) => element.name == "Payment Type");
      }
      enableTableOrders = fnUtilities.workSpaceSetValues(key_enableTableOrders) == "1";
      if (!enableTableOrders) {
        mFavouriteList?.removeWhere((element) => element.name == "Table");
      }
      if (isEnableOnlineOrder != "1") {
        mFavouriteList?.removeWhere((element) => element.name == "Manage Online Platforms");
      }
      if (EnableExpense != "1") {
        mFavouriteList?.removeWhere((element) => element.name == "Expenses");
        mFavouriteList?.removeWhere((element) => element.name == "Manage Expenses");
      }
      if (isEnableSalesCategory != "1") {
        mFavouriteList?.removeWhere((element) => element.name == "Sales Category");
      }
      if (isEnableShiftbasedSales != "1") {
        mFavouriteList?.removeWhere((element) => element.name == "Manage Shifts");
      }
    });
  }

  initPageDetails() async {
    await initSettings();
  }

  List<MasterCard> getMastersItems() {
    List<MasterCard> list = [];

    var category1 = MasterCard();
    category1.name = "Product";
    category1.color = buttonThemeColor;
    category1.icon = productSettingsSVG;
    list.add(category1);
    var category2 = MasterCard();
    category2.name = "Table";
    category2.color = buttonThemeColor;
    category2.icon = table;
    list.add(category2);
    var category3 = MasterCard();
    category3.name = "Payment Type";
    category3.color = buttonThemeColor;
    category3.icon = payment;
    list.add(category3);
    // var category4 = MasterCard();
    // category4.name = "Manage Inventory";
    // category4.color = Colors.orange;
    // category4.icon = inventory;
    // list.add(category4);
    var category5 = MasterCard();
    category5.name = "Manage Customers";
    category5.color = buttonThemeColor;
    category5.icon = manageCustomer;
    list.add(category5);
    var category6 = MasterCard();
    category6.name = "Manage Online Platforms";
    category6.color = buttonThemeColor;
    category6.icon = manage;
    list.add(category6);

    // var category7 = MasterCard();
    // category7.name = "Manage Expenses";
    // category7.color = Colors.orange;
    // category7.icon = inventory;
    // list.add(category7);

    var category8 = MasterCard();
    category8.name = "Expenses";
    category8.color = buttonThemeColor;
    category8.icon = expenses;
    list.add(category8);

    ///Sales Category
    var category9 = MasterCard();
    category9.name = "Sales Category";
    category9.color = buttonThemeColor;
    category9.icon = sales;
    list.add(category9);

    ///Shiftwise Sales
    var category10 = MasterCard();
    category10.name = "Manage Shifts";
    category10.color = buttonThemeColor;
    category10.icon = shift;
    list.add(category10);
    return list;
  }

  @override
  void initState() {
    mFavouriteList = getMastersItems();
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    width = width - 50;

    return Scaffold(
      backgroundColor: whiteColor,
      appBar: BaseAppBar(
        title: text(
          "Masters",
          textColor: secondaryTextColor,
          fontSize: 20.0,
        ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Container(
        // padding: EdgeInsets.only(top: 28),
        height: MediaQuery.of(context).size.height,
        padding: EdgeInsets.symmetric(horizontal: 5),
        child: Column(
          children: <Widget>[
            SizedBox(height: 10),
            Expanded(
              child: Container(padding: EdgeInsets.all(5), child: GridListing(mFavouriteList, false)),
            ),
          ],
        ),
      ),
    );
  }

  //GRID WIDGET
  Widget GridListing(mFavouriteList, isScrollable) {
    return GridView.builder(
        shrinkWrap: true,
        scrollDirection: Axis.vertical,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: fnUtilities.getProductsGridCount(width), crossAxisSpacing: 10, mainAxisSpacing: 10, childAspectRatio: 1),
        physics: ScrollPhysics(),
        itemCount: mFavouriteList.length,
        // gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        //     crossAxisCount: width < tabletWidth ? 3 : 4, crossAxisSpacing: 10, mainAxisSpacing: 10, childAspectRatio: width < tabletWidth ? 1.0 : 1.5),
        itemBuilder: (BuildContext context, int index) {
          var item = mFavouriteList[index];
          return GestureDetector(
            onTap: () async {
              if (item.name == "Product") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(19)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ProductListPage()));
                  }
                  isLock = false;
                }
              } else if (item.name == "Table") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(20)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ViewTables()));
                  }
                  isLock = false;
                }
              } else if (item.name == "Payment Type") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(21)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ViewPayment()));
                  }
                  isLock = false;
                }
              }
              // else if (item.name == "Manage Inventory") {
              //   if (!isLock) {
              //     isLock = true;
              //     if (await rolePermissions.isViewPermissions(dbOperations.returnPermission("Products")) == true) {
              //       Navigator.push(context, MaterialPageRoute(builder: (context) => ManageStocks()));
              //     } else {
              //       showToast("You don't have a permission");
              //     }
              //     isLock = false;
              //   }
              // }
              else if (item.name == "Manage Customers") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(22)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => CustomerPage()));
                  }
                  isLock = false;
                }
              } else if (item.name == "Manage Online Platforms") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(24)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ViewOnlinePlatform()));
                  }
                  isLock = false;
                }
              } else if (item.name == "Expenses") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(23)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ExpensesScreen()));
                  }
                  isLock = false;
                }
              } else if (item.name == "Manage Expenses") {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(23)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ManageExpensesScreen()));
                  }
                  print("Manage Expenses Clicked");
                  isLock = false;
                }
              } else if (item.name == "Sales Category") {
                var isLock = false;
                if (!isLock) {
                  isLock = true;
                  //if (PermissionFunctions.checkPermission(23) ) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => SalesCategoryView()));
                  //}
                  isLock = false;
                }
              } else if (item.name == "Manage Shifts") {
                var isLock = false;
                if (!isLock) {
                  isLock = true;
                  // if (PermissionFunctions.checkPermission(24) ) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ViewShiftDetails()));
                  // }
                  print("Shiftwise Sales Clicked");
                  isLock = false;
                }
              }
            },
            child: Container(
              decoration: boxDecoration(radius: 10, showShadow: true, bgColor: appThemeColor),
              // width: width < tabletWidth ? width / 3 - 20 : width / 3 - 20,
              // height: width > tabletWidth ? width / 4 - 50 : width / 4 - 50,
              width: width > tabletWidth ? (width * 0.3) : (width * 0.3),
              height: width > tabletWidth ? (width * 0.3) : (width * 0.3),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Container(
                    // height: width > tabletWidth ? (width * 0.0) : (width * 0.1),
                    // width: width > tabletWidth ? (width * 0.0) : (width * 0.1),
                    height: 50,
                    width: 50,
                    padding: EdgeInsets.all(8),
                    decoration: boxDecoration(bgColor: mFavouriteList[index].color, radius: 10),
                    child: SvgPicture.asset(
                      mFavouriteList[index].icon,
                      color: secondaryTextColor,
                    ),
                  ),
                  SizedBox(
                      width: 150,
                      child: TextWidget(
                        mFavouriteList[index].name,
                        isLongText: false,
                        isCentered: true,
                        textColor: white,
                        fontSize: 15.0,
                        maxLine: 2,
                        overflow: TextOverflow.ellipsis,
                      ))
                ],
              ),
            ),
          );
        });
  }
}
