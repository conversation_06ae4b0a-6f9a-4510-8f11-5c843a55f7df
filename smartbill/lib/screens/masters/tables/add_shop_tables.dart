// ignore_for_file: unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:uuid/uuid.dart';
import '../../../../main.dart';
import '../../../utils/dbutils/db_crud_script.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/shop_tables_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';

class AddShopTables extends StatefulWidget {
  ShopTable? addShopTable;

   AddShopTables({Key? key, this.addShopTable}) : super(key: key);

  @override
  _AddShopTablesState createState() => _AddShopTablesState(addShopTable);
}

class _AddShopTablesState extends State<AddShopTables> {
  List<ShopTable> shopTableList = [];
  Validator validator = new Validator();
  final dbHelper = DatabaseHelper.instance;
  final shopTableController = TextEditingController();
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  bool isLock = false;

  ShopTable? shopTable;

  _AddShopTablesState(this.shopTable);

  assignValue() {
    if (shopTable != null) {
      shopTableController.text = shopTable?.shopTableName ?? "";
    }
  }

  insertAndUpdateFunction(ShopTable model, String operations) async {
    var shopTable;
    if (operations == "insert") {
      shopTable = {
        db_shopTables_shopTableId: model.shopTableId,
        db_shopTables_shopTableName: model.shopTableName,
        db_shopTables_shopTableSync: model.shopTableSync,
        db_workspace_workspaceId: model.workspaceId,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.dbInsert(db_shopTables, shopTable);
    } else {
      shopTable = {
        db_shopTables_shopTableName: model.shopTableName,
        db_shopTables_shopTableSync: model.shopTableSync,
        db_workspace_workspaceId: model.workspaceId,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.updateModel(db_shopTables, shopTable, db_shopTables_shopTableId, model.shopTableId);
    }
  }

  @override
  void initState() {
    assignValue();
  }

  onPressTable() async {
    ShopTable model = ShopTable();
    model.shopTableName = shopTableController.text;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;

    if (shopTable == null) {
      var uuid = new Uuid();
      model.shopTableId = uuid.v4().toString();
      model.shopTableSync = 0;
      model.rowStatus = 0;
      insertAndUpdateFunction(model, "insert");
      //await shopTableDbScript.insertShopTable(model);
    } else {
      model.shopTableId = shopTable?.shopTableId;
      model.shopTableSync = 0;
      if (await shopTableDbScript.queryGetShopTablesBySynced(shopTable?.shopTableId)) {
        model.rowStatus = 0;
      } else {
        model.rowStatus = 1;
      }
      insertAndUpdateFunction(model, "update");
      //await shopTableDbScript.updateShopTable(model);
      setState(() {
        shopTable = ShopTable();
      });
    }
    await fnUtilities.getAllShopTables();
    shopTableController.clear();
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(shopTable?.shopTableId == null ? "Add Table" : "Update Table", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      //drawer: BaseDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Align(
                alignment: Alignment.center,
                child: Container(
                  padding: EdgeInsets.all(15.0),
                  width: width > tabletWidth ? width * 0.5 : width * 0.95,
                  decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 10),
                  child: Form(
                    key: validator.formkey,
                    child: Column(
                      children: [
                        BootstrapRow(
                          children: [
                            BootstrapCol(
                                sizes: 'col-md-6 col-sm-12',
                                child: Column(
                                  children: [
                                    Row(
                                      children: [
                                        Expanded(
                                          child: SizedBox(
                                            // width: width > tabletWidth ? width * 0.23 : width * 0.78,
                                            child: Padding(
                                              padding: const EdgeInsets.only(top: 10.0, bottom: 10),
                                              child: EditText(
                                                mController: shopTableController,
                                                inputType: TextInputType.text,
                                                isPassword: false,
                                                text: "Table Name",
                                                validator: (value) {
                                              if (value.isEmpty)
                                                return 'This field is required';
                                              else{
                                                try {
                                                  ListUtility.shopTableList.firstWhere(
                                                    (element) =>
                                                        element.shopTableName == value &&
                                                        element.shopTableId != shopTable?.shopTableId,
                                                  );
                                                  return 'This name is already used';
                                                } catch (e) {
                                                  return null;
                                                }
                                              }
                                            },
                                                // suffixIcon: IconButton(
                                                //   onPressed: () {
                                                //     setState(() {
                                                //       shopTableController.clear();
                                                //       shopTable = null;
                                                //     });
                                                //   },
                                                //   icon: Icon(Icons.clear),
                                                // ),
                                              ),
                                            ),
                                          ),
                                        ),

                                        // Container(
                                        //   alignment: Alignment.topCenter,
                                        //   child: IconButton(
                                        //     onPressed: () {
                                        //       setState(() {
                                        //         shopTableController.clear();
                                        //         shopTable = null;
                                        //       });
                                        //     },
                                        //     icon: Icon(Icons.clear),
                                        //   ),
                                        // ),
                                      ],
                                    ),
                                  ],
                                )),
                            BootstrapCol(
                              sizes: 'col-md-6 col-sm-12',
                              child: Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                                    child: RoundedButton(
                                      width: width > tabletWidth ? width * 0.23 : width * 0.78,
                                      title: shopTable == null ? "Add Table" : "Update Table",
                                      onPressed: () async {
                                        if (!isLock) {
                                          isLock = true;
                                          if (validator.validate() ?? false) {
                                            await onPressTable();
                                          }
                                          isLock = false;
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
