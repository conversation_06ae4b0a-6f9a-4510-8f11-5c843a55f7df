// ignore_for_file: sdk_version_set_literal

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/utils/common_widgets/header_footer.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import '../../../../main.dart';
import '../../../utils/common_widgets/common_search_widget.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/shop_tables_db_script.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import 'add_shop_tables.dart';

class ViewTables extends StatefulWidget {
  const ViewTables({Key? key}) : super(key: key);

  @override
  State<ViewTables> createState() => _ViewTablesState();
}

class _ViewTablesState extends State<ViewTables> {
  double width = 0.0;
  double height = 0.0;
  final TextEditingController searchController = new TextEditingController();
  List<ShopTable> filterCategoryList = [];
  final dbHelper = DatabaseHelper.instance;
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  List<ShopTable> filterShopList = [];
  String? shopTableId;
  bool isLock = false;

  //final shopTableController = TextEditingController();

  void initState() {
    getAllShopTableDetails();
  }

  getAllShopTableDetails() async {
    if (ListUtility.shopTableList.isNotEmpty) {
      setState(() {
        filterShopList.clear();
        filterShopList.addAll(ListUtility.shopTableList);
      });
    } else {
      setState(() {
        filterShopList.clear();
      });
    }
  }

  searchTablesFunction(String value) {
    if (value != null && value != "") {
      setState(() {
        filterShopList.clear();
        for (ShopTable model in ListUtility.shopTableList) {
          if (model.shopTableName!.toLowerCase().contains(value.toLowerCase())) {
            filterShopList.add(model);
          }
        }
      });
    } else {
      setState(() {
        filterShopList.clear();
        filterShopList.addAll(ListUtility.shopTableList);
      });
    }
  }

  // ///Search sales category item
  // searchTableCategory(String value) {

  // }

  //DYNAMIC KEYWORD ALERT DIALOG
  deleteDialog(BuildContext context, String id) {
    double width = MediaQuery.of(context).size.width;
    bool maxWidth = width > tabletWidth ? true : false;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                // width: double.maxFinite,
                width: maxWidth ? width * 0.35 : width * 0.8,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      child: TextWidget("Are you sure,do you want to delete?",
                          textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: maxWidth ? width * 0.15 : width * 0.3,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              ShopTable model = ShopTable();
                              DBFuctionsScript dbScript = DBFuctionsScript();

                              model.shopTableId = id;
                              model.rowStatus = 2;
                              model.workspaceId = MyApp.activeWorkspace.workspaceId;
                              model.shopTableSync = 0;
                              if (await shopTableDbScript.queryGetShopTablesBySynced(id)) {
                                await dbScript.deleteFunction(db_shopTables, db_shopTables_shopTableId, id);
                              } else {
                                await shopTableDbScript.deleteUpdateShopTable(model);
                              }
                              await fnUtilities.getAllShopTables();
                              getAllShopTableDetails();
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: maxWidth ? width * 0.15 : width * 0.3,
                            child: TextWidget("Delete", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text("View Table", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: productAddButton(),
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 0.0, vertical: 0.0),
        child: Container(
          width: width,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(8.0),
                child: CommonSearchComponent(
                  onSearch: (value) {
                    searchTablesFunction(value);
                  },
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              tableListWidget()
            ],
          ),
        ),
      ),
    );
  }

  tableListWidget() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: filterShopList.isNotEmpty
          ? ListView.builder(
              itemCount: filterShopList.length,
              itemBuilder: (context, index) {
                var item = filterShopList[index];
                return Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () async {
                          if (PermissionFunctions.checkPermission(26)) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => AddShopTables(
                                          addShopTable: item,
                                        ))).then((value) => {fnUtilities.getAllShopTables(), getAllShopTableDetails()});
                          }
                        },
                        onLongPress: () async {
                          if (PermissionFunctions.checkPermission(28)) {
                            deleteDialog(context, item.shopTableId ?? "");
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              SizedBox(width: width / 1.5, child: Text(item.shopTableName ?? "")),
                            ],
                          ),
                        ),
                      ),
                      const Divider(
                        thickness: 1,
                      ),
                    ],
                  ),
                );
              })
          : const Center(child: Text("No Table Available")),
    );
  }

  Widget productAddButton() {
    return InkWell(
        onTap: () async {
          if (PermissionFunctions.checkPermission(25)) {
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddShopTables()))
                .then((value) => {fnUtilities.getAllShopTables(), getAllShopTableDetails()});
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
