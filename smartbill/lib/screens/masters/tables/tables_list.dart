import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/item_layouts/sales_main_page.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import '../../../utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/sales_details_db_script.dart';
import '../../../utils/dbutils/shop_tables_db_script.dart';
import '../../item_layouts/Itempage_layout2.dart';
import 'add_shop_tables.dart';
import '../../../utils/common_widgets/header_footer.dart';

class TablesList extends StatefulWidget {
  Sales? salesModel;
  TablesList({this.salesModel});
  @override
  _TablesListState createState() => _TablesListState();
}

class _TablesListState extends State<TablesList> {
  List<ShopTable> shopTableList = [];
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  final dbHelper = DatabaseHelper.instance;
  FnUtilities fnUtilities = new FnUtilities();
  bool isLock = false;
  String? saleLayout;
  double? width;

  @override
  void initState() {
    getAllShopTableDetails();
  }

  getfilterTablelistByKey(String key, String keyName) async {
    for (ShopTable s in shopTableList) {
      if (s.workspaceId == key || s.shopTableName == keyName) {
        setState(() {
          shopTableList.add(s);
        });
      }
    }
    setState(() {
      shopTableList.add(ShopTable());
    });
  }

  void getAllShopTableDetails() async {
    if (ListUtility.shopTableList.isNotEmpty) {
      setState(() {
        shopTableList.clear();
        shopTableList.addAll(ListUtility.shopTableList);
        isActiveFunction();
      });
    }

    await initSettings();
  }

  initSettings() {
    var SaleLayout = fnUtilities.workSpaceUserSetValue(key_saleLayout);
    setState(() {
      if (SaleLayout != null) {
        saleLayout = SaleLayout;
      } else {
        saleLayout = "SaleLayout1";
      }
    });
  }

  isActiveFunction() async {
    for (ShopTable shop in shopTableList) {
      int count = await salesDetailsDBScript.queryActiveTableRowCount(shop.shopTableId) ?? 0;
      setState(() {
        if (count > 0) {
          shop.isActive = true;
        } else {
          shop.isActive = false;
        }
      });
    }
    shopTableList.add(ShopTable());
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
// The equivalent of the "smallestWidth" qualifier on Android.
    var shortestSide = MediaQuery.of(context).size.shortestSide;

// Determine if we should use mobile layout or not, 600 here is
// a common breakpoint for a typical 7-inch tablet.
    return Scaffold(
      appBar: BaseAppBar(
        title: text("Tables", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Container(
        padding: EdgeInsets.all(15),
        width: width,
        child: shopTableList.isNotEmpty
            ? SingleChildScrollView(
                child: Column(
                  children: [
                    // ignore: sdk_version_ui_as_code
                    if (widget.salesModel != null) ...[
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 4.0),
                          child: RichText(
                            text: widget.salesModel != null
                                ? TextSpan(
                                    children: [
                                      TextSpan(
                                        text: "Current Table  :  ",
                                        style: black14RegularTextStyle,
                                      ),
                                      TextSpan(
                                        text: "${widget.salesModel?.tableName}",
                                        style: black14RegularTextStyle.copyWith(fontWeight: FontWeight.w700),
                                      ),
                                    ],
                                  )
                                : TextSpan(text: ''),
                          ),
                        ),
                      )
                    ],
                    shopTableList.length != 0
                        ? SingleChildScrollView(
                            child: GridView.builder(
                                shrinkWrap: true,
                                scrollDirection: Axis.vertical,
                                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: fnUtilities.getProductsGridCount(width),
                                    crossAxisSpacing: 10,
                                    mainAxisSpacing: 10,
                                    childAspectRatio: 1),
                                physics: ScrollPhysics(),
                                itemCount: shopTableList.length,
                                itemBuilder: (BuildContext context, int index) {
                                  var item = shopTableList[index];
                                  return item.shopTableId == null
                                      ? gridAddTable()
                                      : InkWell(
                                          onTap: () async {
                                            if (!isLock) {
                                              isLock = true;
                                              Sales sale = Sales();
                                              sale.fkTableID = item.shopTableId;
                                              bool isActive = item.isActive ?? false;
                                              if (widget.salesModel != null) {
                                                if (!isActive) {
                                                  showDialog(
                                                      context: context,
                                                      builder: (BuildContext context) {
                                                        return DeleteDialog(
                                                          message:
                                                              "Are you sure you want to change this table from ${widget.salesModel?.tableName} to ${item.shopTableName} ? ",
                                                          buttonNameOne: "Yes",
                                                          onPressOne: () async {
                                                            sale = widget.salesModel!;
                                                            sale.rowStatus = 1;
                                                            sale.sync = 0;
                                                            sale.fkTableID = item.shopTableId;
                                                            sale.tableName = item.shopTableName;
                                                            await salesDetailsDBScript.updateSalesActiveTable(sale);
                                                            Navigator.pop(context, true);
                                                            Navigator.pop(context, true);
                                                          },
                                                          buttonNameTwo: "Cancel",
                                                          onPressTwo: () {
                                                            var isLock = false;
                                                            if (!isLock) {
                                                              isLock = true;
                                                              Navigator.pop(context);
                                                              isLock = false;
                                                            }
                                                          },
                                                        );
                                                      });
                                                } else {
                                                  showToast("Table alredy use");
                                                }
                                              } else {
                                                Navigator.pop(context);
                                                Navigator.push(
                                                    context,
                                                    MaterialPageRoute(
                                                        builder: (context) => saleLayout == "SaleLayout2"
                                                            ? ItemsPageLayout2(sales: sale, saleType: "T")
                                                            : SalesMainPage(sales: sale, saleType: "T")));
                                              }
                                              isLock = false;
                                            }
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            width: width / 1,
                                            height: 40,
                                            padding: EdgeInsets.symmetric(horizontal: 10.0),
                                            decoration: boxDecoration(
                                              radius: 10,
                                              showShadow: true,
                                              bgColor: (item.isActive != null ? !item.isActive! : false) ? Colors.green : Colors.red,
                                            ),
                                            child: TextWidget(item.shopTableName ?? "",
                                                isCentered: false, textColor: whiteColor, fontSize: textSizeLargeMedium, fontFamily: fontBold),
                                          ),
                                        );
                                }),
                          )
                        : Container(),
                  ],
                ),
              )
            : Center(
                child: Text("No Tables Available"),
              ),
      ),
    );
  }

  Widget gridAddTable() {
    return InkWell(
      onTap: () async {
        if (PermissionFunctions.checkPermission(102)) {
          if (!isLock) {
            isLock = true;
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddShopTables())).then((value) {
              fnUtilities.getAllShopTables();
              getAllShopTableDetails();
            });
            isLock = false;
          }
        }
      },
      child: Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              width: width,
              padding: EdgeInsets.all(5),
              child: Icon(Icons.add),
            ),
            SizedBox(height: 2),
            textWithStroke("New Table",
                textColor: primaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)
          ],
        ),
      ),
    );
  }
}
