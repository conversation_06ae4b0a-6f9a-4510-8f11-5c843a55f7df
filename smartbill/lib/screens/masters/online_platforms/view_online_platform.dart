import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/screens/masters/online_platforms/add_online_platform.dart';
import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../../main.dart';
import '../../../utils/common_widgets/common_search_widget.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/online_platform_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/roles_permissions/permission_functions.dart';

class ViewOnlinePlatform extends StatefulWidget {
  const ViewOnlinePlatform({Key? key}) : super(key: key);

  @override
  State<ViewOnlinePlatform> createState() => _ViewOnlinePlatformState();
}

class _ViewOnlinePlatformState extends State<ViewOnlinePlatform> {
  List<OnlinePlatformModel> onlinePlatformList = [];
  List<OnlinePlatformModel> filterOnlinePlatformList = [];
  final TextEditingController searchController = new TextEditingController();
  List<OnlinePlatformModel> filterPlatformList = [];
  final dbHelper = DatabaseHelper.instance;
  DBOperations funCommonDB = DBOperations();
  CommonDB commonDB = CommonDB();
  OnlinePlatformDBScript onlinePlatformDBScript = OnlinePlatformDBScript();
  bool isLock = false;

  void initState() {
    super.initState();
    getOnlinePlatformList();
  }

  getOnlinePlatformList() async {
    onlinePlatformList = await funCommonDB.getAllOnlinePlatformsFromDB();
    setState(() {
      filterOnlinePlatformList.clear();
      filterOnlinePlatformList.addAll(onlinePlatformList);
    });
  }

  searchPlatformFunction(String value) {
    if (value != null && value != "") {
      setState(() {
        filterOnlinePlatformList.clear();
        for (OnlinePlatformModel model in onlinePlatformList) {
          if (model.onlinePlatformName!.toLowerCase().contains(value.toLowerCase())) {
            filterOnlinePlatformList.add(model);
          }
        }
      });
    } else {
      setState(() {
        filterOnlinePlatformList.clear();
        filterOnlinePlatformList.addAll(onlinePlatformList);
      });
    }
  }

  //DYNAMIC KEYWORD ALERT DIALOG
  deleteDialog(BuildContext context, OnlinePlatformModel onlinePlatformModel) {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: double.maxFinite,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      child: TextWidget("Are you sure, you want to delete?",
                          textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              if (await commonDB.checkNonSyncCommonFunction(db_salesPaymentMapping_onlinePlatform,
                                      db_salesPaymentMapping_onlinePlatformId, onlinePlatformModel.onlinePlatformId ?? "", db_common_syncStatus) ==
                                  0) {
                                onlinePlatformModel.workspaceID = MyApp.activeWorkspace.workspaceId;
                                onlinePlatformModel.syncStatus = 0;
                                onlinePlatformModel.rowStatus = 2;
                                await onlinePlatformDBScript.updateOnlinePlatform(onlinePlatformModel);
                              } else {
                                await commonDB.permanentDeleteCommonFunction(
                                  db_salesPaymentMapping_onlinePlatform,
                                  db_salesPaymentMapping_onlinePlatformId,
                                  onlinePlatformModel.onlinePlatformId ?? "",
                                );
                              }
                              await getOnlinePlatformList();
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Delete", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: text("View Online Platform", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: onlinePlatformAddButton(),
          )
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(top: 10),
              child: CommonSearchComponent(
                onSearch: (value) {
                  searchPlatformFunction(value);
                },
              ),
            ),
            const SizedBox(
              height: 10,
            ),
            onlinePlatformListWidet()
          ],
        ),
      ),
    );
  }

  onlinePlatformListWidet() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: filterOnlinePlatformList.isNotEmpty
          ? ListView.builder(
              itemCount: filterOnlinePlatformList.length,
              itemBuilder: (context, index) {
                var item = filterOnlinePlatformList[index];
                return Column(
                  children: [
                    InkWell(
                      onTap: () async {
                        if (PermissionFunctions.checkPermission(52)) {
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => AddOnlinePlatforms(
                                        onlinePlatformModel: item,
                                      ))).then((value) {
                            getOnlinePlatformList();
                          });
                        }
                      },
                      onLongPress: () async {
                        if (PermissionFunctions.checkPermission(54)) {
                          deleteDialog(context, item);
                        }
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(width: width / 1.5, child: Text(item.onlinePlatformName ?? "")),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      thickness: 1,
                    ),
                  ],
                );
              })
          : const Center(child: Text("No online platforms available")),
    );
  }

  Widget onlinePlatformAddButton() {
    return InkWell(
        onTap: () async {
          if (PermissionFunctions.checkPermission(51)) {
            Navigator.push(context, MaterialPageRoute(builder: (context) => AddOnlinePlatforms())).then((value) {
              getOnlinePlatformList();
            });
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
