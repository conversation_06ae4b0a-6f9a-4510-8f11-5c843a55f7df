// ignore_for_file: must_be_immutable, unnecessary_null_comparison

import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../../utils/dbutils/common_db_script.dart';
import '../../../utils/dbutils/db_strings.dart';
import '../../../utils/dbutils/online_platform_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';
import 'online_platform_model.dart';

class AddOnlinePlatforms extends StatefulWidget {
  OnlinePlatformModel? onlinePlatformModel;
  AddOnlinePlatforms({Key? key, this.onlinePlatformModel}) : super(key: key);

  @override
  _AddOnlinePlatformsState createState() => _AddOnlinePlatformsState(onlinePlatformModel);
}

class _AddOnlinePlatformsState extends State<AddOnlinePlatforms> {
  Validator validator = Validator();
  FnUtilities fnUtilities = new FnUtilities();
  final dbHelper = DatabaseHelper.instance;
  DBOperations funCommonDB = DBOperations();
  final onlinePlatformController = TextEditingController();
  CommonDB commonDB = CommonDB();
  OnlinePlatformDBScript onlinePlatformDBScript = OnlinePlatformDBScript();
  bool isLock = false;
  List<OnlinePlatformModel> onlinePlatformList = [];
  OnlinePlatformModel? onlinePlatformModel;

  _AddOnlinePlatformsState(this.onlinePlatformModel);

  clearTextFields() {
    onlinePlatformController.clear();
  }

  assignValue() {
    if (onlinePlatformModel != null) {
      onlinePlatformController.text = onlinePlatformModel?.onlinePlatformName ?? "";
    }
  }

  getOnlinePlatformModelList() async {
    List<OnlinePlatformModel> tempOnlinePlatformList = await funCommonDB.getAllOnlinePlatformsFromDB();
    setState(() {
      onlinePlatformList = tempOnlinePlatformList;
    });
  }

  addOnlinePlatformFun() async {
    try {
      LoadingAlertWidget.onLoading(context);;
      int? isExists = await commonDB.checkValueExists(db_salesPaymentMapping_onlinePlatform, db_salesPaymentMapping_onlinePlatformName,
          onlinePlatformController.text, db_salesPaymentMapping_onlinePlatformId, onlinePlatformModel?.onlinePlatformId ?? "");
      if (isExists != 0) {
        showToast("Online platform already exists");
      } else {
        OnlinePlatformModel model = OnlinePlatformModel();
        model.onlinePlatformName = onlinePlatformController.text;
        model.createdDate = DateTime.now().toString();
        model.syncStatus = 0;
        model.workspaceID = MyApp.activeWorkspace.workspaceId.toString();
        if (onlinePlatformModel?.onlinePlatformId == null) {
          var uuid = const Uuid();
          model.onlinePlatformId = uuid.v4().toString();

          model.rowStatus = 0;
          await onlinePlatformDBScript.insertOnlinePlatform(model);
          // showToast("Online platform added successfully");
        } else {
          model.onlinePlatformId = onlinePlatformModel?.onlinePlatformId ?? "";
          model.rowStatus = await commonDB.checkNonSyncCommonFunction(db_salesPaymentMapping_onlinePlatform, db_salesPaymentMapping_onlinePlatformId,
                      onlinePlatformModel?.onlinePlatformId ?? "", db_common_syncStatus) ==
                  0
              ? 1
              : 0;
          await onlinePlatformDBScript.updateOnlinePlatform(model);
          // showToast("Online platform updated successfully");
          onlinePlatformModel = OnlinePlatformModel();
        }
        clearTextFields();
      }
      await getOnlinePlatformModelList();
      LoadingAlertWidget.onStopping();
      Navigator.pop(context);
    } catch (ex) {
      showToast("Failed to add online platform");
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(ex, "addOnlinePlatformFun - AddOnline");
    }
  }

  @override
  void initState() {
    assignValue();
    getOnlinePlatformModelList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(onlinePlatformModel?.onlinePlatformId == null ? "Add Online Platform" : "Update Online Platform",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      //drawer: BaseDrawer(),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: SingleChildScrollView(
          child: Column(
            children: [
              Align(
                alignment: Alignment.center,
                child: Container(
                  padding: EdgeInsets.all(15.0),
                  width: width > tabletWidth ? width * 0.5 : width * 0.95,
                  decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 10),
                  child: Form(
                    key: validator.formkey,
                    child: Column(
                      children: [
                        BootstrapRow(
                          children: [
                            BootstrapCol(
                              sizes: 'col-md-6 col-sm-12',
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      SizedBox(
                                        width: width > tabletWidth ? width * 0.23 : width * 0.78,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 10),
                                          child: EditText(
                                            mController: onlinePlatformController,
                                            inputType: TextInputType.text,
                                            isPassword: false,
                                            text: "Online Platform",
                                            validator: (value) {
                                              if (value.isEmpty)
                                                return 'This field is required';
                                              else{
                                                try {
                                                  onlinePlatformList.firstWhere(
                                                    (element) =>
                                                        element.onlinePlatformName == value &&
                                                        element.onlinePlatformId != onlinePlatformModel?.onlinePlatformId,
                                                  );
                                                  return 'This name is already used';
                                                } catch (e) {
                                                  return null;
                                                }
                                              }
                                            },
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            BootstrapCol(
                              sizes: 'col-md-6 col-sm-12',
                              child: Column(
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 10),
                                    child: RoundedButton(
                                      title: onlinePlatformModel?.onlinePlatformId == null ? "Add" : "Update",
                                      onPressed: () async {
                                        if (!isLock) {
                                          isLock = true;
                                          if (validator.validate()) {
                                            await addOnlinePlatformFun();
                                          }
                                          isLock = false;
                                        }
                                      },
                                    ),
                                  )
                                ],
                              ),
                            )
                          ],
                        )
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
