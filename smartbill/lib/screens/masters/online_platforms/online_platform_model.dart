class OnlinePlatformModel {
  int? id;
  String? onlinePlatformId;
  String? onlinePlatformName;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  OnlinePlatformModel(
      {this.id,
      this.onlinePlatformId,
      this.onlinePlatformName,
      this.createdDate,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive});

  factory OnlinePlatformModel.fromJsonDB(Map<String, dynamic> json) =>
      OnlinePlatformModel(
        id: json["id"],
        onlinePlatformId: json["onlinePlatformId"],
        onlinePlatformName: json["onlinePlatformName"],
        createdDate: json["createdDate"],
        rowStatus: json["rowStatus"],
        workspaceID: json["workspaceId"],
        isActive: json["isActive"],
      );
  factory OnlinePlatformModel.fromJson(Map<String, dynamic> json) =>
      OnlinePlatformModel(
        id: json["id"],
        onlinePlatformId: json["onlinePlatformID"],
        onlinePlatformName: json["onlinePlatformName"],
        createdDate: json["createdDate"],
        rowStatus: json["status"],
        workspaceID: json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "onlinePlatformID": onlinePlatformId,
        "onlinePlatformName": onlinePlatformName,
        "createdDate": createdDate,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}
