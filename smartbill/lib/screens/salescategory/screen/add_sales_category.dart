// ignore_for_file: must_be_immutable

import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:smartbill/screens/salescategory/functions/sales_category_function.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../../../utils/common_function/common_master_table_function.dart';
import '../../../utils/common_function/validation_utility.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/rounded_button.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/constants/styles.dart';
import '../../salescategory/model/sales_category_model.dart';

class AddSalesCategory extends StatefulWidget {
  SalesCategoryModel? salesCategory;
  bool isUpdate = false;
  AddSalesCategory({Key? key,  this.salesCategory, required this.isUpdate}) : super(key: key);

  @override
  State<AddSalesCategory> createState() => _AddSalesCategoryState();
}

class _AddSalesCategoryState extends State<AddSalesCategory> {
  Validator validator = new Validator();
  final salesCatController = TextEditingController();
  SalesCategoryModel salesCategoryModel = SalesCategoryModel();
  MasterTableFunction masterTableFunction = MasterTableFunction();
  SalesCategoryFunction salesCategoryFUnction = SalesCategoryFunction();
  bool isSubmitButtonLock = false;

  

  onPressSubmitButton() async {
    try {
      if (!isSubmitButtonLock) {
        isSubmitButtonLock = true;
        if (validator.validate()) {
          await salesCategoryFUnction.upsertSalesCategoryList(widget.isUpdate,widget.salesCategory,salesCatController.text);
          List<Map<String, dynamic>> valueList = ListUtility.salesCategoryList.map((e) => e.toJson()).toList();
          String valueString = json.encode(valueList);
          await masterTableFunction.upsertMasterTable(key_salesCategory, valueString);
          salesCatController.clear();
          FocusScope.of(context).unfocus();
          Navigator.pop(context, true);
        }
        isSubmitButtonLock = false;
      }
    } catch (e) {
      isSubmitButtonLock = false;
      ErrorLogsFunction.insertErrorLogs(e, "onPressSubmitButton - AddSalesCategory");
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  initPageDetails() async {
    if (widget.isUpdate) {
      salesCategoryModel = widget.salesCategory!;
      salesCatController.text = salesCategoryModel.salesCatName ?? "";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: text(widget.isUpdate ? "Update Sales Category" : "Add Sales Category",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      //drawer: BaseDrawer(),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20.0, vertical: 20.0),
          child: Column(
            children: [
              Align(
                alignment: Alignment.center,
                child: Form(
                  key: validator.formkey,
                  child: Column(
                    children: [
                      BootstrapRow(
                        children: [
                          BootstrapCol(
                              sizes: 'col-md-6 col-sm-12',
                              child: Column(
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: SizedBox(
                                          child: Padding(
                                            padding: const EdgeInsets.only(top: 10.0, bottom: 10),
                                            child: EditText(
                                                mController: salesCatController,
                                                inputType: TextInputType.text,
                                                isPassword: false,
                                                text: "Sales category name",
                                                validator: validator.validateName),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              )),
                          BootstrapCol(
                            sizes: 'col-md-6 col-sm-12',
                            child: Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(top: 10.0, bottom: 10.0),
                                  child: RoundedButton(
                                    title: widget.isUpdate ? "Update Sales Category" : "Add Sales Category",
                                    onPressed: () {
                                      onPressSubmitButton();
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
