// ignore_for_file: sdk_version_ui_as_code
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../main.dart';
import '../../../utils/common_widgets/common_alert_dialogue/common_delete_dialogue.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/common_widgets/widgets.dart';
import '../../../utils/constants/colors.dart';
import '../../../utils/constants/images.dart';
import '../../../utils/error_logs/error_logs_function.dart';
import '../../salescategory/model/sales_category_model.dart';
import '../../salescategory/screen/add_sales_category.dart';
import '../functions/sales_category_function.dart';

class SalesCategoryView extends StatefulWidget {
  const SalesCategoryView({Key? key}) : super(key: key);

  @override
  State<SalesCategoryView> createState() => _SalesCategoryViewState();
}

class _SalesCategoryViewState extends State<SalesCategoryView> {
  double width = 0.0;
  double height = 0.0;
  final TextEditingController searchController = new TextEditingController();
  List<SalesCategoryModel> filterCategoryList = [];
  bool isLoading = false;
  var isLock = false;

  initPageDetails() async {
    await getSalesCategoryDetails();
  }

  //Store the sales category list in filterCategoryList
  getSalesCategoryDetails() async {
    setState(() {
      isLoading = true;
    });
    setState(() {
      filterCategoryList.clear();
      filterCategoryList.addAll(ListUtility.salesCategoryList.where((element) => element.status != 2 && element.workspaceID == MyApp.activeWorkspace.workspaceId));
      isLoading = false;
    });
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  deleteSalesCategory(SalesCategoryModel salesCategory) async {
    try {
      if (!isLock) {
        SalesCategoryFunction salesCategoryFunction = SalesCategoryFunction();
        var tempSalesCategoryList = await salesCategoryFunction.deleteSalesCategory(salesCategory);
        setState(() {
          filterCategoryList.clear();
          filterCategoryList.addAll(tempSalesCategoryList);
        });
        Navigator.pop(context);
        isLock = false;
      }
    } on Exception catch (e) {
      isLock = false;
      ErrorLogsFunction.insertErrorLogs(e, "deleteSalesCategory - SalesCategoryView");
    }
  }

  ///Search sales category item
  searchSalesCategory(String value) {
    List<SalesCategoryModel> tempSalesCategoryList = [];
    tempSalesCategoryList.clear();
    tempSalesCategoryList.addAll(ListUtility.salesCategoryList.where((element) => element.status != 2));
    if (value != "") {
      setState(() {
        filterCategoryList.clear();
        for (SalesCategoryModel model in tempSalesCategoryList) {
          if (model.salesCatName!.toLowerCase().contains(value.toLowerCase())) {
            filterCategoryList.add(model);
          }
        }
      });
    } else {
      setState(() {
        filterCategoryList.clear();
        filterCategoryList.addAll(tempSalesCategoryList);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      // backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: text(
          "View Sales Category",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: salesCategoryAddButton(),
          )
        ],
      ),
      body: Container(
        decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
        child: Padding(
          padding: const EdgeInsets.all(10.0),
          child: Column(
            children: [
              SizedBox(
                height: 3,
              ),
              searchFilterSortFunction(),
              if (isLoading) ...[
                SizedBox(height: height * 0.5, child: Center(child: CircularProgressIndicator()))
              ] else ...[
                salesCategoryListFunction(),
              ]
            ],
          ),
        ),
      ),
    );
  }

  Widget salesCategoryAddButton() {
    return InkWell(
        onTap: () {
          FocusScope.of(context).unfocus();
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => AddSalesCategory(
                        salesCategory: null,
                        isUpdate: false,
                      ))).then(
            (value) async {
              //if (value) {
              setState(() {
                initPageDetails();
              });
              // }
            },
          );
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }

  Widget searchFilterSortFunction() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(top: 7, left: 5),
            height: 50,
            child: TextField(
              controller: searchController,
              onChanged: searchSalesCategory,
              style: TextStyle(color: Colors.black),
              autofocus: false,
              decoration: InputDecoration(
                labelText: 'Search ',
                border: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: BorderSide(color: grey, width: 0.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                ),
                suffixIcon: InkWell(
                  onTap: () {
                    setState(() {
                      FocusScope.of(context).unfocus();
                      searchController.clear();
                      searchSalesCategory("");
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(
                      searchIconSVG,
                      color: greyColor,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget salesCategoryListFunction() {
    return Expanded(
      child: Align(
        alignment: Alignment.topCenter,
        child: filterCategoryList.isNotEmpty
            ? ListView.builder(
                padding: EdgeInsets.symmetric(horizontal: 5),
                scrollDirection: Axis.vertical,
                itemCount: filterCategoryList.length,
                shrinkWrap: true,
                physics: ScrollPhysics(),
                itemBuilder: (context, index) {
                  //DELETE ALERT DIALOG FOR PRODUCTS
                  var item = filterCategoryList[index];
                  return GestureDetector(
                    onLongPress: () async {
                      await deleteDialog(context, item);
                    },
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.symmetric(vertical: 10),
                          child: InkWell(
                            onTap: () async {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) => AddSalesCategory(
                                            salesCategory: item,
                                            isUpdate: true,
                                          ))).then((value) {
                                  initPageDetails();
                               });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                              

                                Expanded(
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: <Widget>[
                                      if (item.salesCatName != null) ...[
                                        Padding(
                                          padding: const EdgeInsets.only(right: 8,top: 8),
                                          child: Container(
                                            child: TextWidget(item.salesCatName!,
                                                textColor: primaryTextColor,
                                                fontSize: textSizeMedium,
                                                //fontWeight: FontWeight.bold,
                                                isCentered: false,
                                                isLongText: true),
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Divider(height: 1.0, color: t5ViewColor)
                      ],
                    ),
                  );
                })
            : Center(
                child: Text("Sales Category Not Found"),
              ),
      ),
    );
  }

  //DYNAMIC KEYWORD ALERT DIALOG
  deleteDialog(BuildContext context, SalesCategoryModel salesCategoryModel) {
    return showDialog(
        context: context,
        builder: (context) {
          return DeleteDialog(
            message: "Are you sure you want to delete?",
            buttonNameOne: "Yes",
            onPressOne: () {
              deleteSalesCategory(salesCategoryModel);
            },
            buttonNameTwo: "Cancel",
            onPressTwo: () {
              var isLock = false;
              if (!isLock) {
                isLock = true;
                Navigator.pop(context);
                isLock = false;
              }
            },
          );
        });
  }
}
