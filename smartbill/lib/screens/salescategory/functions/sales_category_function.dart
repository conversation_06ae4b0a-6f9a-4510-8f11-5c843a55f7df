import 'dart:convert';

import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../../utils/common_function/common_master_table_function.dart';
import '../../../utils/common_function/list_class.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/error_logs/error_logs_function.dart';
import '../model/sales_category_model.dart';

class SalesCategoryFunction {
  MasterTableFunction masterTableFunction = MasterTableFunction();

  ///Store and Update the sales category item in the static list of
  ///SalesCategory list  based on salesCategoryId
  upsertSalesCategoryList(bool isUpdate, SalesCategoryModel? salesCategoryModel, String salesCatControllerText) {
    int autoIncrementId = 0;
    try {
      if (isUpdate && salesCategoryModel != null ) {
        int index = ListUtility.salesCategoryList.indexWhere((element) => element.salesCategoryId == salesCategoryModel?.salesCategoryId);
        if (index != -1) {
          ListUtility.salesCategoryList[index] = SalesCategoryModel(
              id: salesCategoryModel.id,
              createdDate: DateTime.now().toString(),
              salesCategoryId: salesCategoryModel.salesCategoryId,
              salesCatName: salesCatControllerText,
              workspaceID: MyApp.activeWorkspace.workspaceId!,
              status: 1,
              sync: 0);
        }
      } else {
        var uuid = new Uuid();
        autoIncrementId = ListUtility.salesCategoryList.isNotEmpty ? ListUtility.salesCategoryList.last.id! + 1 : 1;
        salesCategoryModel = SalesCategoryModel(
            id: autoIncrementId,
            salesCategoryId: uuid.v4().toString(),
            salesCatName: salesCatControllerText,
            workspaceID: MyApp.activeWorkspace.workspaceId!,
            sync: 0,
            status: 0,
            createdDate: DateTime.now().toString());
        ListUtility.salesCategoryList.add(salesCategoryModel);
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "upsertSalesCategoryList - SalesCategoryFunction");
    }
  }

  ///delete the sales category item from the hive box
  deleteSalesCategory(SalesCategoryModel? salesCategory) async {
    List<SalesCategoryModel> salesCategoryList = [];
    try {
      if (salesCategory != null) {
        int index = ListUtility.salesCategoryList.indexWhere(
          (element) => element.salesCategoryId == salesCategory.salesCategoryId,
        );
        if (index != -1) {
          ListUtility.salesCategoryList[index] = SalesCategoryModel(
              id: salesCategory.id,
              salesCategoryId: salesCategory.salesCategoryId,
              salesCatName: salesCategory.salesCatName,
              workspaceID: salesCategory.workspaceID,
              status: 2,
              sync: 0);
        }
        salesCategoryList.clear();
        salesCategoryList.addAll(
            ListUtility.salesCategoryList.where((element) => element.status != 2 && element.workspaceID == MyApp.activeWorkspace.workspaceId));
        List<Map<String, dynamic>> valueList = ListUtility.salesCategoryList.map((e) => e.toJson()).toList();
        String valueString = json.encode(valueList);
        await masterTableFunction.upsertMasterTable(key_salesCategory, valueString);
        return salesCategoryList;
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "deleteSalesCategory - SalesCategoryFunction");
    }
  }

  Future<List<SalesCategoryModel>> getNonSyncSalesCategoryList() async {
    if (ListUtility.salesCategoryList.isNotEmpty) {
      List<SalesCategoryModel> salesCategoryList = [];
      salesCategoryList.clear();
      salesCategoryList
          .addAll(ListUtility.salesCategoryList.where((element) => element.sync == 0 && element.workspaceID == MyApp.activeWorkspace.workspaceId));
      return salesCategoryList;
    }
    return [];
  }

  ///Sync Master table data from API
  syncSalesCategoryFromApi(List<SalesCategoryModel>? salesCategoryList) async {
    try {
      if (salesCategoryList?.length != null && salesCategoryList?.length != 0) {
        dynamic tempList = [];
        tempList.addAll(ListUtility.salesCategoryList);
        if (ListUtility.salesCategoryList.isNotEmpty) {
          for (SalesCategoryModel model in salesCategoryList!) {
            model.sync = 1;
            int modelIndex = tempList.indexWhere(
                (element) => element.salesCategoryId == model.salesCategoryId && element.workspaceID == MyApp.activeWorkspace.workspaceId);
            if (modelIndex >= 0) {
              model.status = model.isActive == true ? 1 : 2;
              ListUtility.salesCategoryList[modelIndex] = model;
            } else {
              model.isActive == true ? 0 : 2;
              ListUtility.salesCategoryList.add(model);
            }
          }
        } else {
          for (SalesCategoryModel model in salesCategoryList!) {
            ListUtility.salesCategoryList.add(SalesCategoryModel(
                createdDate: model.createdDate,
                isActive: model.isActive,
                salesCategoryId: model.salesCategoryId,
                salesCatName: model.salesCatName,
                workspaceID: MyApp.activeWorkspace.workspaceId!,
                sync: 1,
                status: model.isActive == true ? 0 : 2));
          }
        }
        List<Map<String, dynamic>> valueList = ListUtility.salesCategoryList.map((e) => e.toJson()).toList();
        String valueString = json.encode(valueList);
        await masterTableFunction.upsertMasterTable(key_salesCategory, valueString);
      }
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "deleteSalesCategory - SalesCategoryFunction");
    }
  }
}
