class SalesCategoryModel {
  int? id;
  String? salesCategoryId;
  String? salesCatName;
  String? workspaceID;
  int? status;
  int? sync;
  bool? isActive;
  String? createdDate;

  SalesCategoryModel({
    this.id,
    this.salesCategoryId,
    this.salesCatName,
    this.workspaceID,
    this.status,
    this.sync,
    this.isActive,
    this.createdDate
  });

  SalesCategoryModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    salesCategoryId = json['salesCategoryId'];
    salesCatName = json['salesCatName'];
    workspaceID = json['workspaceID'];
    status = json['status'];
    sync = json['sync'];
    isActive = json['isActive'];
    createdDate = json['createdDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['salesCategoryId'] = this.salesCategoryId;
    data['salesCatName'] = this.salesCatName;
    data['workspaceID'] = this.workspaceID;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['isActive'] = this.isActive;
    data['createdDate'] = this.createdDate;
    return data;
  }
}
