// ignore_for_file: missing_required_param

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:share/share.dart';
import 'package:smartbill/screens/settings/general_settings_page.dart';
import 'package:smartbill/screens/settings/order_settings.dart';
import 'package:smartbill/screens/settings/panel_settings_page.dart';
import 'package:smartbill/screens/settings/parcel_settings_page.dart';
import 'package:smartbill/screens/settings/printer_settings.dart';
import 'package:smartbill/screens/settings/product_settings_page.dart';
import 'package:smartbill/screens/settings/report_settings.dart';
import 'package:smartbill/screens/settings/shop_setting_page.dart';
import 'package:smartbill/screens/settings/subscription_page.dart';
import 'package:smartbill/screens/settings/support.dart';
import 'package:smartbill/screens/settings/tax_settings_page.dart';
import 'package:smartbill/screens/settings/user_settings.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/settings_card_widget.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../main.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../../utils/roles_permissions/screens/manage_roles.dart';
import '../settings/disount_settings/discount_settings.dart';
import 'package:sqlite_viewer/sqlite_viewer.dart';

class HomeSettings extends StatefulWidget {
  ParentFunctionCallback? parentRefreshFun;
  HomeSettings({this.parentRefreshFun});
  @override
  HomeSettingsState createState() => HomeSettingsState();
}

class HomeSettingsState extends State<HomeSettings> {
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  double width = 0.0;

  @override
  void initState() {
    super.initState();
  }

  getAppVersionCode() {
    if (Platform.isAndroid) {
      return MyApp.androidVersionCode;
    } else if (Platform.isIOS) {
      return MyApp.iosVersionCode;
    } else if (Platform.isWindows) {
      return MyApp.windowsVersionCode;
    } else {
      return "";
    }
  }

  updateParentClass(int isUpdate) {
    if (widget.parentRefreshFun != null) {
      widget.parentRefreshFun!(isUpdate);
    }
  }

  // /// delete user account dialog
  // deleteUserAccountDialog(BuildContext buildContext) {
  //   return showDialog(
  //       context: buildContext,
  //       builder: (context) {
  //         return ConfirmationDialogEidget(
  //           description: "Are you sure want to delete you account?",
  //           button1Text: "Yes",
  //           button1OnTap: (() async {
  //             await deleteAccountFun(buildContext);
  //             Navigator.pop(context);
  //           }),
  //           button2Text: "No",
  //           button2OnTap: (() {
  //             Navigator.pop(context);
  //           }),
  //         );
  //       });
  // }

  // /// delete account function
  // deleteAccountFun(BuildContext buildContext) async {
  //   try {
  //     fnUtilities.onLoading(buildContext);
  //     await deleteAccountAPI(MyApp.activeUser.username).then((value) async {
  //       LoadingAlertWidget.onStopping();
  //       showToast(value.message);
  //       if (value.status == 1) {
  //         fnUtilities.removeActiveWorkspaceAndUser(
  //             buildContext, MyApp.activeWorkspace,
  //             isRemoveUser: false);
  //       }
  //     });
  //   } on Exception catch (e) {
  //     LoadingAlertWidget.onStopping();
  //     showToast("Failed to delete user");
  //   }
  // }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      body: SingleChildScrollView(
        child: Column(
          children: [
            ///shop settings
            SettingsCardWidget(
              titleIcon: shopSettingsSVG,
              title: "Shop Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(125)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ShopSettings())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///Printer settings
            SettingsCardWidget(
              titleIcon: printerSVG,
              title: "Printer Settings",
              onPress: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => printerSettingsPage())).then((value) {
                  updateParentClass(1);
                });
              },
            ),

            ///Panel settings
            SettingsCardWidget(
              titleIcon: panelSVG,
              title: "Panel Settings",
              onPress: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => PanelSettingsPage())).then((value) {
                  updateParentClass(1);
                });
              },
            ),

            ///Subscription settings
            // ignore: sdk_version_ui_as_code
            if (!Platform.isIOS) ...[
              if (!(MyApp.isDemoAccount ?? false)) ...[
                SettingsCardWidget(
                  titleIcon: subscriptionSVG,
                  title: "Subscriptions",
                  onPress: () {
                    if (PermissionFunctions.checkPermission(128)) {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => Subscriptions())).then((value) {
                        updateParentClass(1);
                      });
                    }
                  },
                ),
              ],
            ],

            ///Tax Settings
            SettingsCardWidget(
              titleIcon: taxSVG,
              title: "Tax Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(129)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => TaxSettingsPage())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///Parcel Settings
            SettingsCardWidget(
              titleIcon: parcelSVG,
              title: "Parcel Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(130)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ParcelSettingsPage())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///Discount Settings
            SettingsCardWidget(
              titleIcon: discountSVG,
              title: "Discount Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(131)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => DiscountSettings())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///General Settings
            SettingsCardWidget(
              titleIcon: generalSettingSVG,
              title: "General Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(132)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => GeneralSettingsPage())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///User Settings
            SettingsCardWidget(
              titleIcon: userSVG,
              title: "User Settings",
              onPress: () {
                // if (PermissionFunctions.checkPermission(133)) {
                Navigator.push(context, MaterialPageRoute(builder: (context) => UserSettings())).then((value) {
                  updateParentClass(1);
                });
                // }
              },
            ),

            ///Product Settings
            SettingsCardWidget(
              titleIcon: productSettingsSVG,
              title: "Product Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(134)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ProductSettingsPage())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///Report Settings
            SettingsCardWidget(
              titleIcon: reportSVG,
              title: "Report Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(135)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ReportSettings())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///order Settings
            SettingsCardWidget(
              titleIcon: orderSVG,
              title: "Order Settings",
              onPress: () {
                if (PermissionFunctions.checkPermission(136)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => OrderSettings())).then((value) {
                    updateParentClass(1);
                  });
                }
              },
            ),

            ///Roles and permissions
            SettingsCardWidget(
              titleIcon: userSVG,
              title: "Manage Permissions",
              onPress: () {
                if (PermissionFunctions.checkPermission(275)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => ManegeRolesScreen()));
                }
              },
            ),

            ///Support Settings
            SettingsCardWidget(
              titleIcon: supportSVG,
              title: "Support",
              onPress: () {
                if (PermissionFunctions.checkPermission(137)) {
                  Navigator.push(context, MaterialPageRoute(builder: (context) => Support()));
                }
              },
            ), 
            ///Share Settings
            SettingsCardWidget(
              titleIcon: shareSVG,
              title: "Share",
              onPress: () {
           
              },
            ),

            ///Rate Us Settings
            SettingsCardWidget(
              titleIcon: rateUsSVG,
              title: "Rate Us",
              onPress: () {
               },
            ),
             ///Tutorials
            SettingsCardWidget(
              titleIcon: youtubeIconSVG,
              title: "Videos | Tutorials",
              onPress: () {
                // ignore: deprecated_member_use  
              },
            ),

            ///Rate Us Settings
            SettingsCardWidget(
              titleIcon: generalSettingSVG,
              title: 'Version : ${getAppVersionCode()}',
              onPress: () {},
            ),

            // ///Delete user account dialog
            // SettingsCardWidget(
            //   titleIcon: generalSettingSVG,
            //   title: 'Remove Account',
            //   onPress: () {
            //     deleteUserAccountDialog(context);
            //   },
            // ),

            //View Database
            SettingsCardWidget(
              titleIcon: rateUsSVG,
              title: "View Database",
              onPress: () {
                Navigator.push(context, MaterialPageRoute(builder: (context) => DatabaseList()));
              },
            ),

            SizedBox(
              height: 20,
            ),
          ],
        ),
      ),
    );
  }
}
