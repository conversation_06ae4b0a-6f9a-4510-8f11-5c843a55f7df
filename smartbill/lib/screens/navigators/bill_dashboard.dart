// ignore_for_file: sdk_version_ui_as_code

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/screens/item_layouts/sales_not_complete_screen.dart';
import 'package:smartbill/screens/settings/subscription_page.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/dashboard_grid_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:wakelock_plus/wakelock_plus.dart';
import '../../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/database_helper.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../item_layouts/Itempage_layout2.dart';
import '../item_layouts/sales_main_page.dart';
import '../item_layouts/table_and_eatandpay.dart';

class BillDashBoard extends StatefulWidget {
  ParentFunctionCallback? parentRefreshFun;
  BillDashBoard({Key? key, this.parentRefreshFun}) : super(key: key);
  @override
  BillDashBoardState createState() => BillDashBoardState();
}

class BillDashBoardState extends State<BillDashBoard> {
  SyncUtility syncUtility = new SyncUtility();
  DBOperations dbOperations = new DBOperations();
  final fnUtilities = new FnUtilities();
  final dbHelper = DatabaseHelper.instance;
  bool passwordVisible = false;
  bool isLock = false;
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  double width = 0.0;
  double height = 0.0;
  bool isTableOrder = false;
  bool isEatAndPay = false;
  bool enableOnlineOrder = false;
  bool isEnableSaleBillPad = false;
  bool isEnableAdvanceOrder = false;

  String saleLayout = "SaleLayout1";
  String takeAwayName = "Take Away";
  String tableOrderName = "Table Order";
  String eatPayName = "Eat & Pay";
  String onlineOrderName = "Online Order";
  String todaySalesAmount = "0.0";
  bool showTodaySales = false;
  String? liveTime;

  void getTime() async {
    if (this.mounted) {
      final String formattedDateTime = DateFormat.jm().format(DateTime.now()).toString();
      setState(() {
        liveTime = formattedDateTime;
      });
      getTodaySalesAmountFun();
    }
  }

  getTodaySalesAmountFun() async {
    var salesAmount = await fnUtilities.getTodaySalesAmount();
    setState(() {
      todaySalesAmount = salesAmount;
    });
  }

  getBillCardNames() {
    takeAwayName = fnUtilities.workSpaceSetValues(key_takeAwayName) ?? "Take Away";
    eatPayName = fnUtilities.workSpaceSetValues(key_eatAndPayName) ?? "Eat & Pay";
    tableOrderName = fnUtilities.workSpaceSetValues(key_tableOrderName) ?? "Table Order";
  }

  updateParentClass(int isUpdate) {
    if (widget.parentRefreshFun != null) {
      widget.parentRefreshFun!(isUpdate);
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  initPageDetails() {
    getTodaySalesAmountFun();
    getTime();
    Timer.periodic(Duration(seconds: 10), (Timer t) => getTime());
    getBillCardNames();
    initSettings();
  }

  var EnableTableOrdersValue; // This field only for check settings exists for re initiate settings on click order page.
  initSettings() async {
    var AlwaysOnDisplay = fnUtilities.workSpaceUserSetValue(key_alwaysOnDisplay);
    var SaleLayout = await fnUtilities.workSpaceUserSetValue(key_saleLayout);

    setState(() {
      isTableOrder = fnUtilities.workSpaceSetValues(key_enableTableOrders) == "1";
      isEatAndPay = fnUtilities.workSpaceSetValues(key_eatAndPay) == "1";
      showTodaySales = fnUtilities.workSpaceSetValues(showTodaySalesKey) == "1";
      enableOnlineOrder = fnUtilities.workSpaceSetValues(enableOnlineOrderKey) == "1";
      isEnableSaleBillPad = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";
      isEnableAdvanceOrder = fnUtilities.workSpaceSetValues(key_advanceOrder) == "1";

      if (SaleLayout != null) {
        saleLayout = SaleLayout;
      } else {
        saleLayout = "SaleLayout1";
      }
      if (AlwaysOnDisplay == "1") {
        WakelockPlus.toggle(enable: true);
      } else {
        WakelockPlus.toggle(enable: false);
      }
    });
  }

//VALIDITY ALERT ALERT DIALOG
  validityAlertDialog(BuildContext context, String msg, String page, {bool isGraceTime = false}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          child: AlertDialog(
            content: Container(
              child: Text(
                msg,
                style: TextStyle(color: primaryTextColor, fontSize: 18.0),
              ),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  if (isGraceTime) {
                    if (page == eatPayName) {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => TableOrderPage("SO"))).then((value) {
                        updateParentClass(1);
                      });
                    } else if (page == tableOrderName) {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => TableOrderPage("T"))).then((value) {
                        updateParentClass(1);
                      });
                    } else {
                      Navigator.push(
                              context, MaterialPageRoute(builder: (context) => saleLayout == "SaleLayout2" ? ItemsPageLayout2() : SalesMainPage()))
                          .then((value) {
                        updateParentClass(1);
                      });
                    }
                  } else {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => Subscriptions()));
                  }
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 40,
                  width: 40,
                  child: TextWidget("OK", fontSize: textSizeMedium, textColor: buttonThemeColor, isCentered: true),
                  decoration: boxDecoration(radius: 8.0),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Future<int> getValidityDays() async {
    try {
      DateTime now = DateTime.now();
      int validityDays = DateTime.parse(MyApp.activeWorkspace.expiryDate ?? '').difference(now).inDays;
      return validityDays;
    } catch (e) {
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    bootstrapGridParameters(gutterSize: 0);
    return Scaffold(
      body: SafeArea(
        child: BootstrapContainer(
          fluid: true,
          children: [
            BootstrapRow(
              children: [
                BootstrapCol(
                  sizes: 'col-md-6 col-sm-12',
                  child: Container(
                    width: width,
                    color: appThemeColor,
                    // padding: EdgeInsets.all(5.0),
                    child: Column(
                      children: <Widget>[
                        firstHalfSection(),
                      ],
                    ),
                  ),
                ),
                BootstrapCol(
                  sizes: 'col-md-6 col-sm-12',
                  child: Container(
                    child: Column(
                      children: [
                        SizedBox(height: 10),
                        billGrids(),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget firstHalfSection() {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      // double contentHeight = constraints.maxHeight;
      return Container(
        height: MediaQuery.of(context).orientation == Orientation.portrait
            ? (width < 380 ? height * 0.30 : (width >= tabletWidth ? height : height * 0.35))
            : height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            if (PermissionFunctions.checkPermission(79, viewToast: false)) ...[
              showTodaySales ? totalSalesCardWidget(amount: todaySalesAmount) : SizedBox.shrink()
            ],
          ],
        ),
      );
    });
  }

  Widget billGrids() {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      double contentWidth = constraints.maxWidth;
      return Container(
        padding: EdgeInsets.only(left: 5, right: 5),
        child: Column(
          children: [
            /// Time Widget
            if (liveTime != null) ...[
              if (isEnableSaleBillPad || isEnableAdvanceOrder) ...[
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Container(
                        alignment: Alignment.centerRight,
                        child: TextWidget(liveTime ?? '', textColor: primaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
                      ),
                      InkWell(
                        onTap: () {
                          Navigator.push(context, MaterialPageRoute(builder: (context) => SalesNotCompletePage())).then((value) {
                            updateParentClass(1);
                          });
                        },
                        child: Container(
                          height: 40,
                          width: 50,
                          alignment: Alignment.center,
                          decoration: boxDecoration(radius: 10, showShadow: true, bgColor: appThemeColor),
                          child: Container(
                            child: SvgPicture.asset(
                              pendingSVG,
                              color: secondaryTextColor,
                              height: 20,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 30),
              ] else ...[
                Container(
                  alignment: Alignment.centerRight,
                  child: TextWidget(liveTime ?? '', textColor: primaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
                ),
              ],
              SizedBox(height: 10),
            ],

            /// Show Grids
            if (isEatAndPay && isTableOrder) ...[
              Container(
                width: contentWidth * 0.7,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    dashBoardRowGrid(contentWidth, eatPayName, productSettingsSVG, () async {
                      eatAndPayOntap();
                    }),
                    SizedBox(
                      width: 20,
                    ),
                    dashBoardRowGrid(contentWidth, tableOrderName, reportSVG, () async {
                      tableOrderOntap();
                    }),
                  ],
                ),
              ),
            ] else if (isEatAndPay && !isTableOrder) ...[
              dashBoardColumnGrid(contentWidth, eatPayName, productSettingsSVG, () async {
                eatAndPayOntap();
              })
            ] else if (!isEatAndPay && isTableOrder) ...[
              dashBoardColumnGrid(contentWidth, tableOrderName, reportSVG, () async {
                tableOrderOntap();
              })
            ],
            if (enableOnlineOrder) ...[
              Container(
                width: contentWidth * 0.7,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    dashBoardRowGrid(contentWidth, takeAwayName, generalSettingSVG, () async {
                      takeAwayOntap(1);
                    }),
                    SizedBox(
                      width: 20,
                    ),
                    dashBoardRowGrid(contentWidth, onlineOrderName, generalSettingSVG, () async {
                      takeAwayOntap(2);
                    }),
                  ],
                ),
              ),
            ] else ...[
              dashBoardColumnGrid(contentWidth, takeAwayName, generalSettingSVG, () async {
                takeAwayOntap(1);
              }),
            ],
          ],
        ),
      );
    });
  }

  ///Eat and Pay ontap function
  eatAndPayOntap() async {
    if (PermissionFunctions.checkPermission(75)) {
      if (await getValidityDays() > 0) {
        if (!isLock) {
          isLock = true;
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => TableOrderPage(
                        "SO",
                      ))).then((value) {
            updateParentClass(1);
          });
          isLock = false;
        }
      } else if (await getValidityDays() >= -5) {
        validityAlertDialog(context, "Your plan expired!, you need to activate the subscription for sales", eatPayName, isGraceTime: true);
      } else {
        validityAlertDialog(context, "You should activate the subscription for sales", eatPayName, isGraceTime: false);
      }
    }
  }

  ///Table order ontap function
  tableOrderOntap() async {
    if (PermissionFunctions.checkPermission(76)) {
      if (await getValidityDays() > 0) {
        if (!isLock) {
          isLock = true;
          Navigator.push(context, MaterialPageRoute(builder: (context) => TableOrderPage("T"))).then((value) {
            updateParentClass(1);
          });
          isLock = false;
        }
      } else if (await getValidityDays() >= -5) {
        validityAlertDialog(context, "Your plan expired!, you need to activate the subscription for sales", tableOrderName, isGraceTime: true);
      } else {
        validityAlertDialog(context, "You should activate the subscription for sales", tableOrderName, isGraceTime: false);
      }
    }
  }

  ///Take away ontap function
  takeAwayOntap(int orderType) async {
    if (PermissionFunctions.checkPermission(orderType == 1 ? 77 : 78)) {
      if (await getValidityDays() > 0) {
        if (!isLock) {
          isLock = true;
          Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => saleLayout == "SaleLayout2"
                      ? ItemsPageLayout2()
                      : SalesMainPage(
                          orderType: orderType,
                        ))).then((value) {
            updateParentClass(1);
          });
          isLock = false;
        }
      } else if (await getValidityDays() >= -5) {
        validityAlertDialog(context, "Your plan expired!, you need to activate the subscription for sales", takeAwayName, isGraceTime: true);
      } else {
        validityAlertDialog(context, "You should activate the subscription for sales", takeAwayName, isGraceTime: false);
      }
    }
  }

  ///Not completed onTap function
  notCompletedOnTap() {
    Navigator.push(context, MaterialPageRoute(builder: (context) => SalesNotCompletePage())).then((value) {
      updateParentClass(1);
    });
  }
}
