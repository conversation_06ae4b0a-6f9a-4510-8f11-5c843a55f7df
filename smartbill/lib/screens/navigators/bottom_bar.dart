// ignore_for_file: must_be_immutable
import 'dart:io';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/navigators/bill_dashboard.dart';
import 'package:smartbill/screens/navigators/options_dashboard.dart';
import 'package:smartbill/screens/shops/sign_in_workspace.dart';
import 'package:smartbill/utils/common_function/background_sync.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/screens/shops/all_shop_screen.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';

import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../main.dart';
import '../../utils/common_function/value_notifier.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../users/demo_workspace.dart';
import 'home_settings.dart';

GlobalKey<BottomBarState> bottomBarKey = GlobalKey<BottomBarState>();
int currentIndex = 1;
int selectedIndex = 1;

// currentIndex = 0 means Options Page
// currentIndex = 1 means Bill Page
// currentIndex = 2 means Settings Page

navigateBottomBar() {
  selectedIndex = 1;
  bottomBarKey.currentState?.updateBottmBars();
  navigatorKey1.currentState?.popUntil((Route<dynamic> route) => route.isFirst);
}

class BottomBar extends StatefulWidget {
  dynamic routeData;
  BottomBar({
    this.routeData,
    Key? key,
  }) : super(key: key);

  @override
  State<BottomBar> createState() => BottomBarState();
}

class BottomBarState extends State<BottomBar> {
  GlobalKey<BillDashBoardState> billDashBoardKey = GlobalKey<BillDashBoardState>();
  DateTime? currentBackPressTime;
  double width = 0.0;
  double height = 0.0;
  CommonDB commonDB = CommonDB();
  List<Widget> pagesList = [];
  final dbHelper = DatabaseHelper.instance;

  List<Widget> returnBottomPages() {
    if (MyApp.activeWorkspace.workspaceId == null && ListUtility.signedWorkspaceList.isEmpty) {
      return [
        OptionsDashBoard(parentRefreshFun: parentRefFun),
        BillDashBoard(key: billDashBoardKey, parentRefreshFun: parentRefFun),
        HomeSettings(parentRefreshFun: parentRefFun)
      ];
    }
    return [
      OptionsDashBoard(parentRefreshFun: parentRefFun),
      BillDashBoard(key: billDashBoardKey, parentRefreshFun: parentRefFun),
      HomeSettings(parentRefreshFun: parentRefFun)
    ];
  }

  SyncUtility syncUtility = new SyncUtility();
  FnUtilities fnUtilities = new FnUtilities();
  Demoworkspace demo = Demoworkspace();

  changeIndex(index) {
    setState(() {
      currentIndex = index;
    });
  }

  initPageDetails() async {
    setState(() {
      currentIndex = selectedIndex;
      pagesList = returnBottomPages();
      selectedIndex = 0;
      checkSignedWorkspace();
    });
  }

  updateBottmBars() {
    currentIndex = selectedIndex;
    pagesList = returnBottomPages();
    selectedIndex = 0;
    billDashBoardKey.currentState?.initPageDetails();
  }

  parentRefFun(int index) {
    syncUtility.checkSyncStatus();
  }

  checkSignedWorkspace() async {
    DBOperations dbOperations = new DBOperations();
    await dbOperations.queryAllGetWorkspace();
    if (ListUtility.signedWorkspaceList.isEmpty) {
      Navigator.push(
          context,
          MaterialPageRoute(
              builder: (context) => SignInWorkspace(
                    screenID: 1,
                  )));
    }
  }

  @override
  void initState() {
    initPageDetails();
    // Timer.periodic(Duration(seconds: 10), (Timer t) => syncColor());
    super.initState();
  }

  onPressDemoNavigation() async {
    await commonDB.deleteAllRecordsBasedOnWorkspace(MyApp.activeWorkspace.workspaceId ?? '');
    await fnUtilities.removeActiveWorkspaceAndUser(context, ListUtility.signedWorkspaceList[0]);
    ListUtility.clearAllLists();

    MyApp.isDemoAccount = false;

    demo.resetDemoWorkspaceExpiryDate();
  }

  @override
  Widget build(BuildContext context) {
    height = MediaQuery.of(context).size.height;
    width = MediaQuery.of(context).size.width;
    return Scaffold(
        appBar: AppBar(
          automaticallyImplyLeading: false,
          backgroundColor: appThemeColor,
          elevation: 0,
          leading: GestureDetector(
            onTap: () {
              if (MyApp.isDemoAccount ?? false) {
                onPressDemoNavigation();
              } else {
                Navigator.push(context, MaterialPageRoute(builder: (context) => ViewAllShopScreen()));
              }
            },
            child: Container(
              padding: EdgeInsets.all(12),
              child: InkWell(
                child: SvgPicture.asset(
                  shopSettingSVG,
                  color: Colors.white,
                  height: 20,
                  width: 20,
                ),
              ),
            ),
          ),
          title: TextWidget(MyApp.activeWorkspace.workspaceName != null ? MyApp.activeWorkspace.workspaceName ?? '' : appName,
              textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          actions: MyApp.activeWorkspace.workspaceId != null
              ? <Widget>[
                  Padding(
                    padding: EdgeInsets.only(right: MyApp.isDemoAccount ?? false ? 15 : 0),
                    child: profileIconWidget(),
                  ),
                  syncIconWidget(),
                ]
              : null,
        ),

        // drawer: BaseDrawer(),
        body: WillPopScope(
            onWillPop: () async {
              bool backStatus = onWillPop();
              if (backStatus) {
                return true;
              } else {
                return false;
              }
            },
            child: Column(
              children: [
                Expanded(
                  child: Center(
                    child: pagesList.elementAt(currentIndex),
                  ),
                ),
              ],
            )),
        floatingActionButton: SizedBox(
          width: 70,
          height: 70,
          child: FloatingActionButton(
              backgroundColor: backgroundColor,
              //Floating action button on Scaffold
              onPressed: () {
                changeIndex(1);
              },
              child: getBottomBarItemTile(1, addBadgeSVG, "Bill", width * 0.2, iconColor: appThemeColor, textColor: appThemeColor)
              //icon inside button
              ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
        //floating action button position to center

        bottomNavigationBar: BottomAppBar(
            //bottom navigation bar on scaffold
            color: appThemeColor,
            elevation: 20.0,
            shape: CircularNotchedRectangle(), //shape of notch
            //notche margin between floating button and bottom appbar
            notchMargin: 1,
            child: Container(
              height: 60,
              color: appThemeColor,
              child: Row(
                //children inside bottom appbar
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: [
                      SizedBox(
                        width: width * 0.1,
                      ),
                      getBottomBarItemTile(0, homeSVG, "Options", width * 0.2),
                      SizedBox(
                        width: 5,
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      SizedBox(
                        width: 5,
                      ),
                      getBottomBarItemTile(2, settingsSVG, "Settings", width * 0.2),
                      SizedBox(
                        width: width * 0.1,
                      ),
                    ],
                  ),
                ],
              ),
            )));
  }

  // onWillPop() {
  //   DateTime now = DateTime.now();
  //   if (currentIndex == 1) {
  //     if (currentBackPressTime == null || now.difference(currentBackPressTime) > const Duration(seconds: 2)) {
  //       currentBackPressTime = now;
  //       showToast("Press again to close app");
  //       return false;
  //     } else {
  //       return true;
  //     }
  //   } else {
  //     currentIndex = 1;

  //     Navigator.push(
  //       context,
  //       MaterialPageRoute(builder: (context) => BottomBar()),
  //     );
  //     return false;
  //   }
  // }
  onWillPop() {
    DateTime now = DateTime.now();
    if (currentIndex == 1) {
      if (currentBackPressTime == null || now.difference(currentBackPressTime ?? now) > const Duration(seconds: 2)) {
        currentBackPressTime = now;
        showToast("Press again to close app");
        return false;
      } else {
        return true;
      }
    } else {
      changeIndex(1);
      return false;
    }
  }

  getBottomBarItemTile(int index, String icon, String title, double cardWidth, {Color? iconColor, Color? textColor}) {
    return GestureDetector(
      onTap: (() {
        changeIndex(index);
      }),
      child: SizedBox(
        width: cardWidth,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            SvgPicture.asset(
              icon,
              height: 30,
              width: 30,
              color: iconColor ?? backgroundColor,
            ),
            SizedBox(
              height: 2,
            ),
            Text(
              title,
              style: TextStyle(color: textColor ?? backgroundColor, fontSize: textSizeMedium, fontWeight: FontWeight.bold),
              overflow: TextOverflow.ellipsis,
            )
          ],
        ),
      ),
    );
  }

  //SYNC WIDGET
  Widget syncIconWidget() {
    return ValueListenableBuilder<SyncStatusInfo>(
      valueListenable: ValueNotifiers.instance.syncStatusNotifier,
      builder: (BuildContext context, SyncStatusInfo? value, Widget? child) {
        Color syncIconColor = value?.syncStatus == 0
            ? Colors.deepOrangeAccent
            : value?.syncStatus == 2
                ? Colors.yellowAccent
                : secondaryTextColor;
        return InkWell(
          onTap: () async {
            if (await InternetConnectionChecker().hasConnection) {
              if (!isSyncProgress) {
                try {
                  if (value?.syncStatus != 2) {
                    await syncUtility.triggerManualSync(isPullSync: true);
                    syncUtility.checkSyncStatus();
                  }
                } catch (e) {
                  ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(0, 0);
                  showToast("Sync failed");
                }
              }
              else{
                 showToast("Sync in progress");
              }
            } else {
              fnUtilities.internetTurnOnAlertDialog(context);
            }
          },
          child: value?.syncStatus == 2
              ? buildCircularLoadingIndicator(value?.syncPercentage)
              : Icon(
                  Icons.sync,
                  color: syncIconColor,
                  size: 30,
                ),
        );
      },
    );
  }

  Widget buildCircularLoadingIndicator(int? syncPercentage) {
    return Padding(
      padding: const EdgeInsets.only(right: 5),
      child: Stack(
        alignment: Alignment.center,
        children: [
          SizedBox(
            height: 25,
            width: 25,
            child: CircularProgressIndicator(
              // value: syncPercentage.toDouble(),
              // backgroundColor: Colors.yellowAccent,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.yellowAccent),
              strokeWidth: 3.0,
            ),
          ),
          Text(
            fnUtilities.checkQtyDoubleValue(syncPercentage.toString()) ?? "",
            style: TextStyle(fontSize: 10, color: secondaryTextColor), // Customize the text style
          ),
        ],
      ),
    );
  }

  profileIconWidget() {
    return InkWell(
      onTap: () {
        if (MyApp.isDemoAccount ?? false) {
          onPressDemoNavigation();
        } else {
          Navigator.of(context).pushNamed(
            "profile",
          );
        }
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // ignore: sdk_version_ui_as_code
          if (MyApp.isDemoAccount ?? false) ...[
            Container(
              width: width * 0.2,
              padding: EdgeInsets.all(5.0),
              decoration: boxDecoration(bgColor: backgroundColor, radius: 8.0),
              alignment: Alignment.center,
              child: Text(
                'Logout',
                style: TextStyle(color: Colors.red, fontSize: 18, fontWeight: FontWeight.w600),
              ),
            ),
          ] else ...[
            Container(
              height: 30,
              width: 30,
              padding: EdgeInsets.all(2.0),
              margin: EdgeInsets.all(10.0),
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(50), color: greyColor),
              child: SvgPicture.asset(
                userSVG,
                color: lightGreyColor,
              ),
            ),
          ]
        ],
      ),
    );
  }
}

// class NoWorkspaceWidget extends StatelessWidget {
//   const NoWorkspaceWidget({Key key}) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     var width = MediaQuery.of(context).size.width;
//     var height = MediaQuery.of(context).size.height;
//     return Container(
//       width: width,
//       height: height,
//       child: Center(
//         child:
//         //CircularProgressIndicator(color: buttonThemeColor,)
//         TextWidget(
//           "You must signin worksapce!",
//           fontSize: textSizeLarge,
//           fontFamily: fontMedium,
//           fontWeight: FontWeight.bold,
//         ),
//       ),
//     );
//   }
// }
