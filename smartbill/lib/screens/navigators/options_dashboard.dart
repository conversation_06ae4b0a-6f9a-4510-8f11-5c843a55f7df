// ignore_for_file: sdk_version_ui_as_code

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/screens/products/product_page.dart';
import 'package:smartbill/screens/reports/reports_main_page.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/screens/masters/masters_option.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/dashboard_grid_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import '../../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../expenses/screens/expenses_dashboard.dart';

class OptionsDashBoard extends StatefulWidget {
  ParentFunctionCallback? parentRefreshFun;
  OptionsDashBoard({this.parentRefreshFun});
  @override
  OptionsDashBoardState createState() => OptionsDashBoardState();
}

class OptionsDashBoardState extends State<OptionsDashBoard> {
  SyncUtility syncUtility = new SyncUtility();
  DBOperations dbOperations = new DBOperations();
  final fnUtilities = new FnUtilities();
  final printerUtilities = new PrinterUtilities();
  final dbHelper = DatabaseHelper.instance;

  bool isLock = false;
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  double width = 0.0;
  double height = 0.0;

  String? liveTime;
  String todaySalesAmount = "0.0";
  bool showTodaySales = false;
  bool enableExpenses = false;
  bool enablePurchases = false;

  void getTime() {
    if (this.mounted) {
      final String formattedDateTime = DateFormat.jm().format(DateTime.now()).toString();

      setState(() {
        liveTime = formattedDateTime;
      });
      getTodaySalesAmountFun();
    }
  }

  getTodaySalesAmountFun() async {
    var salesAmount = await fnUtilities.getTodaySalesAmount();
    setState(() {
      todaySalesAmount = salesAmount;
    });
  }

  updateParentClass(int isUpdate) {
    if (widget.parentRefreshFun != null) {
      widget.parentRefreshFun!(isUpdate);
    }
  }

//get settings data

  initSettings() {
    setState(() {
      showTodaySales = fnUtilities.workSpaceSetValues(showTodaySalesKey) == "1";
      enableExpenses = fnUtilities.workSpaceSetValues(key_expense) == "1" || fnUtilities.workSpaceSetValues(key_purchase) == "1";
      enablePurchases = fnUtilities.workSpaceSetValues(key_expense) != "1" && fnUtilities.workSpaceSetValues(key_purchase) == "1";
    });
  }

  //'dd-MM-yyyy KK:mm:ss a'
  @override
  void initState() {
    initSettings();
    getTodaySalesAmountFun();
    getTime();
    Timer.periodic(Duration(seconds: 10), (Timer t) => getTime());
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    bootstrapGridParameters(gutterSize: 0);
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          physics: width > mobileWidth ? NeverScrollableScrollPhysics() : AlwaysScrollableScrollPhysics(),
          child: BootstrapContainer(
            fluid: true,
            children: [
              BootstrapRow(
                children: [
                  BootstrapCol(
                    sizes: 'col-md-6 col-sm-12',
                    child: Container(
                      width: width,
                      color: appThemeColor,
                      // padding: EdgeInsets.all(5.0),
                      child: Column(
                        children: <Widget>[
                          //SizedBox(height: 50),
                          firstHalfSection(),
                          //SizedBox(height: 50),
                        ],
                      ),
                    ),
                  ),
                  BootstrapCol(
                    sizes: 'col-md-6 col-sm-12',
                    child: Container(
                      child: Column(
                        children: [
                          SizedBox(height: 10),
                          optionsGrids(),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget firstHalfSection() {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      // double contentHeight = constraints.maxHeight;
      return Container(
        height: MediaQuery.of(context).orientation == Orientation.portrait
            ? width < 380
                ? height * 0.30
                : (width >= tabletWidth ? height : height * 0.35)
            : height,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // SizedBox(
            //   height: width > 380 ? 90 : 30,
            // ),
            if (PermissionFunctions.checkPermission(13, viewToast: false)) ...[
              showTodaySales ? totalSalesCardWidget(amount: todaySalesAmount) : SizedBox.shrink()
            ]
          ],
        ),
      );
    });
  }

  Widget optionsGrids() {
    return LayoutBuilder(builder: (BuildContext context, BoxConstraints constraints) {
      double contentWidth = constraints.maxWidth;
      return Container(
        padding: EdgeInsets.only(left: 5, right: 5),
        child: Column(
          children: [
            /// Time Widget
            if (liveTime != null) ...[
              Container(
                alignment: Alignment.centerRight,
                child: TextWidget(liveTime ?? '', textColor: primaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
              ),
              SizedBox(height: 10),
            ],

            /// Show Grids
            Container(
              width: contentWidth * 0.7,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  dashBoardRowGrid(contentWidth, "Products", productSettingsSVG, () async {
                    if (PermissionFunctions.checkPermission(9)) {
                      if (!isLock) {
                        isLock = true;
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ProductListPage())).then((value) {
                          updateParentClass(1);
                        });
                        isLock = false;
                      }
                    }
                  }),
                  SizedBox(
                    width: 20,
                  ),
                  dashBoardRowGrid(contentWidth, "Reports", reportSVG, () async {
                    // if(PermissionFunctions.checkPermission(10)){
                    if (!isLock) {
                      isLock = true;
                      if (MyApp.activeWorkspace.workspaceId != null) {
                        // if (await rolePermissions.isViewPermissions(
                        //         dbOperations.returnPermission("Reports")) ==
                        //     true) {
                        // SyncUtility syncUtility = SyncUtility();
                        // syncUtility.triggerManualSync();
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsMainPage())).then((value) {
                          updateParentClass(1);
                        });
                        // } else {
                        //   showToast("You don't have a permission");
                        // }
                      }
                      isLock = false;
                      // }
                    }
                  }),
                ],
              ),
            ),

            /// Show Grids
            if (enableExpenses == true) ...[
              Container(
                width: contentWidth * 0.7,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ///Masters
                    dashBoardRowGrid(contentWidth, "Masters", generalSettingSVG, () async {
                      // if (PermissionFunctions.checkPermission(11)) {
                      if (!isLock) {
                        isLock = true;
                        if (MyApp.activeWorkspace.workspaceId != null) {
                          Navigator.push(context, MaterialPageRoute(builder: (context) => MastersOption())).then((value) {
                            updateParentClass(1);
                          });
                        }
                        isLock = false;
                      }
                      // }
                    }),
                    SizedBox(
                      width: 20,
                    ),
                    //  manage expences
                    dashBoardRowGrid(contentWidth, enablePurchases ? "Manage Purchases" : "Manage Expenses", filterIconSVG, () async {
                      if (PermissionFunctions.checkPermission(12)) {
                        if (!isLock) {
                          isLock = true;
                          Navigator.push(context, MaterialPageRoute(builder: (context) => ExpensesDashboard()));
                          print("Manage Expenses Clicked");
                          isLock = false;
                        }
                      }
                    }),
                  ],
                ),
              ),
            ] else ...[
              ///Masters
              dashBoardColumnGrid(contentWidth, "Masters", generalSettingSVG, () async {
                // if (PermissionFunctions.checkPermission(11)) {
                if (!isLock) {
                  isLock = true;
                  if (MyApp.activeWorkspace.workspaceId != null) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => MastersOption())).then((value) {
                      updateParentClass(1);
                    });
                  }
                  isLock = false;
                }
                // }
              }),
            ],
          ],
        ),
      );
    });
  }
}
