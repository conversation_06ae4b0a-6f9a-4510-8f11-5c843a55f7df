import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/screens/settings/support.dart';
import 'package:smartbill/screens/shops/sign_in_workspace.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_widgets/divider_widget.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/shop_card_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';

import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';

import '../../main.dart';
import '../../utils/common_function/background_sync.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/dbutils/workspace_db_script.dart';
import 'members/add_member.dart';
import '../../utils/common_widgets/header_footer.dart';

class ViewAllShopScreen extends StatefulWidget {
  const ViewAllShopScreen({Key? key}) : super(key: key);

  @override
  State<ViewAllShopScreen> createState() => _ViewAllShopScreenState();
}

class _ViewAllShopScreenState extends State<ViewAllShopScreen> {
  double width = 0.0;
  double height = 0.0;
  FnUtilities fnUtilities = new FnUtilities();
  DBOperations dbOperations = new DBOperations();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  bool isLock = false;
  final dbHelper = DatabaseHelper.instance;
  CommonDB commonDB = CommonDB();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  List<WorkSpace> activeWorkspaceList = [];

  // bool isNavigateToChooseOutlet = false ;
  // bool isNavigateToManageUser = false ;

  //DELETE ALL RECODES BASED ON WORKSPACE
  deleteAllRecordsBasedOnWorkspace(int index) async {
    await commonDB.deleteAllRecordsBasedOnWorkspace(ListUtility.signedWorkspaceList[index].workspaceId) ;
  }

  //CHECK DATA SYNCED OR NOT
  checkAlertSync(int index) async {
    if (await commonDB.checkSync(ListUtility.signedWorkspaceList[index]) != 0) {
      alertSync(context, index);
    } else {
      await deleteAllRecordsBasedOnWorkspace(index);
      await fnUtilities.removeActiveWorkspaceAndUser(context, ListUtility.signedWorkspaceList[index]);
    }
  }

  initPageDetails() async {
    await dbOperations.queryAllGetWorkspace();
    setState(() {
      activeWorkspaceList = ListUtility.signedWorkspaceList;
      // outletScreenPermisionsList.addAll(ListUtility.permisionsList.where((element) => element.categoryID == 3));
      // outletScreenNavigationPermision =  outletScreenPermisionsList.firstWhere((element) => element.subCategoryID == 5);
      // manageUserScreenNavigationPermision =  outletScreenPermisionsList.firstWhere((element) => element.subCategoryID == 6);
      // activeWorkspaceList.length = ListUtility.signedWorkspaceList.length ;
    });
  }

  @override
  void initState() {
    super.initState();
    initPageDetails();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        title: text("Outlets", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          createOutletButton(),
          buildAppBarPopupMenuButton(),
        ],
      ),
      backgroundColor: appThemeColor,
      body: SingleChildScrollView(
        child: Column(
          children: [SizedBox(child: viewAllSignInShops())],
        ),
      ),
    );
  }

  /// View all shops widget
  viewAllSignInShops() {
    return Wrap(
        children: List.generate(activeWorkspaceList.length, (index) {
      var item = activeWorkspaceList[index];
      return InkWell(
        onTap: () async {
          try {
            if (!isLock) {
              isLock = true;
              if (!isSyncProgress) {
                LoadingAlertWidget.onLoading(context);;
                setState(() {
                  MyApp.activeWorkspace = item;
                });
                await fnUtilities.setActiveWorkspaceAndUser(item);
                await dbOperations.queryAllShopDeatails();
                await fnUtilities.getMasterInfoBasedOnWorkspace();
                selectedIndex = 1;
                await PermissionFunctions.getPermisionsFromLocal();
                LoadingAlertWidget.onStopping();
                Navigator.of(context).pushNamedAndRemoveUntil('bottombar', (Route<dynamic> route) => false);
              } else {
                showToast("Syncing now, can't switch workspaces");
              }
              isLock = false;
            }
          } catch (ex) {
            LoadingAlertWidget.onStopping();
            isLock = false;
          }
        },
        child: Container(
          width: width > tabletWidth ? width * 0.3 : width,
          child: ShopCardWidget(
            isItemSelected: item.workspaceId == MyApp.activeWorkspace.workspaceId ? true : false,
            isShowPopUp: true,
            isShowRadioButtons: true,
            workspace: item,
            ontap: () async {
              checkAlertSync(index);
            },
            // popupMenuBuilder: buildPopupMenuButton(index),
          ),
        ),
      );
    }));
  }

  /// sign out popup
  Future<void> signOutPopup(BuildContext context, int index) async {
    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                title: Text("Sign out"),
                leading: Icon(Icons.exit_to_app),
                onTap: () async {
                  //Navigator.pop(context); // Close the popup
                  // if (!isLock) {
                  // isLock = true;
                  if(!isSyncProgress){
                    await checkAlertSync(index);
                  } else {
                    showToast("Syncing, can't sign out.");
                  }
                  // isLock = false;
                  // }
                },
              ),
            ],
          ),
        );
      },
    );
  }

  PopupMenuButton<String> buildPopupMenuButton(int index) {
    return PopupMenuButton<String>(
      itemBuilder: (BuildContext context) {
        return <PopupMenuEntry<String>>[
          PopupMenuItem<String>(
            value: 'option1',
            onTap: () async {
              Navigator.pop(context); // Close the popup
              if (!isLock) {
                isLock = true;
                await checkAlertSync(index);
                isLock = false;
              }
            },
            child: Text("Sign out"),
          ),
        ];
      },
    );
  }

  //SYNC ALERT DIALOG
  alertSync(BuildContext context, int index) {
    var width = MediaQuery.of(context).size.width;
    bool maxWidth = width > tabletWidth ? true : false;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
              child: SingleChildScrollView(
                child: AlertDialog(
                    content: Container(
                  width: maxWidth ? width * 0.4 : width * 0.8,
                  child: Form(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text("Some records not synced, Please process to sync else you will loose you unsynced data"),
                        SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () async {
                                await deleteAllRecordsBasedOnWorkspace(index);
                                await fnUtilities.removeActiveWorkspaceAndUser(context, ListUtility.signedWorkspaceList[index]);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: maxWidth ? width * 0.18 : width * 0.3,
                                child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                            GestureDetector(
                              onTap: () async {
                                Navigator.pop(context);
                                //Navigator.pop(context);
                                //Navigator.pop(context);
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: maxWidth ? width * 0.18 : width * 0.3,
                                child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
              ),
            );
          });
        });
  }

  PopupMenuButton<String> buildAppBarPopupMenuButton() {
    return PopupMenuButton<String>(
      onSelected: (value) {
        if (value == 'option1') {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => AddMember()),
          );
        } else if (value == 'option2') {
          // Handle the action for "Help"
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => Support()),
          );
        }
      },
      itemBuilder: (BuildContext context) {
        return <PopupMenuEntry<String>>[
          PopupMenuItem<String>(
            value: 'option1',
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.people_rounded, color: primaryTextColor, size: 20),
                    SizedBox(width: 20),
                    Text('Manage User', style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                DividerWidget(),
              ],
            ),
          ),
          PopupMenuItem<String>(
            value: 'option2',
            child: Column(
              children: [
                Row(
                  children: [
                    Icon(Icons.help, color: primaryTextColor, size: 20),
                    SizedBox(width: 20),
                    Text('Help', style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
                  ],
                ),
                SizedBox(
                  height: 8,
                ),
                DividerWidget(),
              ],
            ),
          ),
        ];
      },
    );
  }

  Widget createOutletButton() {
    return InkWell(
        onTap: () async {
          Navigator.push(context, MaterialPageRoute(builder: (context) => SignInWorkspace()));
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
