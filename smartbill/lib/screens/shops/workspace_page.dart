import 'package:flutter/material.dart';
import 'package:smartbill/screens/shops/add_workspace_page.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_widgets/reports_card_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import 'sign_in_workspace.dart';

class WorkspacePage extends StatefulWidget {
  @override
  _WorkspacePageState createState() => _WorkspacePageState();
}

class _WorkspacePageState extends State<WorkspacePage> {
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  double width = 0.0;
  bool isLock = false;

  //WIDGET FOR LIST ICON WITH NAME
  Widget settingItem(String name, IconData icon, {var pad = 2.0}) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.all(pad),
        child: Row(
          children: <Widget>[
            Container(
              margin: EdgeInsets.only(right: 5),
              width: width / 5.8,
              height: 50,
              child: Icon(icon),
            ),
            TextWidget(name,
                textColor: primaryTextColor,
                fontFamily: fontMedium,
                fontSize: textSizeMedium)
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: appThemeColor,
      appBar: BaseAppBar(
        title: text(
          "Manage Shops",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        // automaticallyImplementing: false,
      ),
      body: Container(
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: SingleChildScrollView(
                child: Container(
                  child: Column(
                    children: <Widget>[
                      SizedBox(height: 24),
                      signInShopWidget(),
                      createNewShop(),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// sign in to shop card widget
  signInShopWidget() {
    return ReportCardWidget(
      title: "Sign in to shops",
      titleIcon: generalSettingSVG,
      isEnableTrailIcon: true,
      onPress: () async {
        if (!isLock) {
          isLock = true;
          Navigator.push(context,
              MaterialPageRoute(builder: (context) => SignInWorkspace()));
          isLock = false;
        }
      },
    );
  }

  /// create a new shop widget
  createNewShop() {
    return ReportCardWidget(
      title: "Create a new shops",
      titleIcon: editSVG,
      isEnableTrailIcon: true,
      onPress: () {
        if (!isLock) {
          isLock = true;
          Navigator.push(context,
              MaterialPageRoute(builder: (context) => AddWorkspacePage()));
          isLock = false;
        }
      },
    );
  }
}
