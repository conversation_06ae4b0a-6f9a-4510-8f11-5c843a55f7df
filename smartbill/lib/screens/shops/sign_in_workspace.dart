// ignore_for_file: sdk_version_ui_as_code

import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
 
import 'package:smartbill/screens/shops/add_workspace_page.dart';
import 'package:smartbill/utils/common_function/background_sync.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/check_user_password.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/login_request_model.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/utils/common_widgets/text_field_box_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../main.dart';
import '../../Repositories/user_repository.dart';
import '../../Repositories/workspace_repository.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/workspace_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../navigators/bottom_bar.dart';

class SignInWorkspace extends StatefulWidget {
  SignInWorkspace({this.screenID});
  int? screenID;
  @override
  _SignInWorkspaceState createState() => _SignInWorkspaceState();
}

class _SignInWorkspaceState extends State<SignInWorkspace> {
  double height = 0.0;
  double width = 0.0;
  final dbHelper = DatabaseHelper.instance;
  Validator validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  SyncUtility syncUtility = new SyncUtility();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  // ignore: non_constant_identifier_names
  DBOperations DBoperation = new DBOperations();
  final userIdController = new TextEditingController();
  final passwordController = new TextEditingController();
  final workspaceNameController = TextEditingController();
  List<WorkSpace> nonSignedList = [];
  bool isLoading = false;
  bool isLock = false;
  bool isExpanded = false;
  String? workID;

  //GET WORKSPACE
  getWorkspaceList() async {
    setState(() {
      ListUtility.nonSignedWorkspaceList.clear();
      isLoading = true;
    });
    if (await InternetConnectionChecker().hasConnection) {
      getWorkspaceListByUser().then((value) {
        if (this.mounted) {
          setState(() {
            isLoading = false;
          });
          if (value.status == 1) {
            setState(() {
              ListUtility.nonSignedWorkspaceList = value.result ?? [];
            });
          } else {
            showToast(value.message);
          }
        }
      });
    } else {
      setState(() {
        isLoading = false;
      });
      fnUtilities.internetTurnOnAlertDialog(context);
    }
  }

  //CHECK SIGNED WORKSPACE
  bool checkSigned(workspaceId) {
    for (WorkSpace sign in ListUtility.signedWorkspaceList) {
      if (sign.workspaceId == workspaceId) {
        return true;
      }
    }
    return false;
  }

  //ADD USER IN WORKSPACE FUNCTION
  addUserInList(User user) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (checkUserExists(user)) {
      final String userList = User.encode(ListUtility.userList);
      await prefs.setString('userList', userList);
    } else {
      ListUtility.userList.add(user);
      final String userList = User.encode(ListUtility.userList);
      await prefs.setString('userList', userList);
    }
  }

  //CHECK USER EXIST
  bool checkUserExists(User userDetail) {
    for (User user in ListUtility.userList) {
      if (user.username == userDetail.username) {
        ListUtility.userList[ListUtility.userList.indexWhere((element) => element.username == userDetail.username)] = userDetail;
        return true;
      }
    }
    return false;
  }

  initPageDetails() async {
    await getWorkspaceList();
    SharedPreferences prefs = await SharedPreferences.getInstance();
    bool isFirstTime = prefs.getBool(isOpenInitial) ?? true;
    if (isFirstTime) {
      await prefs.setBool(isOpenInitial, false);
      await fnUtilities.getPaymentCategory();
    }
  }

  @override
  void initState() {
    initPageDetails();

    super.initState();
  }

  //DIFFERENT WORKSPACE ALERT DIALOG
  differentWorkspaceDialog(BuildContext context, String userName) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    return showDialog(
        context: context,
        builder: (context) {
          return AlertDialog(
              content: Container(
            height: height / 2,
            width: width > tabletWidth ? width * 0.4 : width * 0.8,
            child: Column(
              children: [
                Expanded(
                  child: ListView.builder(
                      itemCount: nonSignedList.length,
                      shrinkWrap: true,
                      itemBuilder: (BuildContext context, int index) {
                        var item = nonSignedList[index];
                        return SingleChildScrollView(
                          child: ListTile(
                            leading: Icon(
                              Icons.cloud_circle,
                              color: primaryTextColor,
                              size: 50,
                            ),
                            title: Text(item.workspaceName ?? "", style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
                            subtitle: Text(item.workspaceId ?? "", style: TextStyle(fontFamily: fontMedium)),
                            onTap: () async {
                              try {
                                if (!isLock) {
                                  isLock = true;
                                  if (!isSyncProgress) {
                                    if (checkSigned(item.workspaceId)) {
                                      showToast("Already singed");
                                    } else {
                                      if (await InternetConnectionChecker().hasConnection) {
                                        LoadingAlertWidget.onLoading(context);;
                                        item.userName = userName;
                                        await DBoperation.insertWorkspace(item);
                                        // ignore: await_only_futures
                                        await DBoperation.queryAllGetWorkspace();
                                        await fnUtilities.setActiveWorkspaceAndUser(item);
                                        // await DBoperation.insertPermissionsDetails(item.permissions, item.userName, item.workspaceId);
                                        await syncUtility.getPrefixForWorkspace();
                                        await syncUtility.triggerManualSync(isSigning: true,isPullSync: true);
                                        fnUtilities.getMasterInfoBasedOnWorkspace();
                                        await PermissionFunctions.getPermisionsFromAPI();
                                        LoadingAlertWidget.onStopping();
                                        // Navigator.pop(context);
                                        // navigateBottomBar();
                                        Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
                                      } else {
                                        fnUtilities.internetTurnOnAlertDialog(context);
                                      }
                                    }
                                  } else {
                                    showToast("Syncing now, can't switch workspaces");
                                  }
                                  isLock = false;
                                }
                              } catch (ex) {
                                LoadingAlertWidget.onStopping();
                                isLock = false;
                              }
                            },
                          ),
                        );
                      }),
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    GestureDetector(
                      onTap: () {
                        if (!isLock) {
                          isLock = true;
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 50,
                        width: width > tabletWidth ? width * 0.18 : width * 0.3,
                        child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                        decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ));
        });
  }

  //SIGN IN PASSEWORD ALERT DIALOG
  passwordWorkspaceDialog(BuildContext context, int index) {
    var width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                // width: double.maxFinite,
                child: Form(
                  key: validator.formkey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Container(
                        child: TextFieldBoxWidget(
                          mController: passwordController,
                          inputType: TextInputType.text,
                          isPassword: true,
                          isSecure: true,
                          text: "Password",
                          validator: validator.validateTextField,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              if (!isLock) {
                                isLock = true;
                                passwordController.text = "";
                                Navigator.pop(context);
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width / 3.5,
                              child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          GestureDetector(
                            onTap: () async {
                              if (!isLock) {
                                LoadingAlertWidget.onLoading(context);;
                                isLock = true;
                                if (await InternetConnectionChecker().hasConnection) {
                                  validator.validate();
                                  if (validator.validate()) {
                                    var item = ListUtility.nonSignedWorkspaceList[index];
                                    CheckUserPassword loginRequest = new CheckUserPassword();
                                    loginRequest.password = passwordController.text;
                                    await checkUserPassword(loginRequest).then((value) async {
                                      if (value.status == 1) {
                                        item.userName = MyApp.activeUser.username;
                                        await ListUtility.clearAllLists();
                                        await DBoperation.insertWorkspace(item);
                                        // await DBoperation.insertPermissionsDetails(item.permissions, item.userName, item.workspaceId);
                                        await DBoperation.queryAllGetWorkspace();
                                        await fnUtilities.setActiveWorkspaceAndUser(item);
                                        // await DBoperation.queryAllGetsPermissionDetails();
                                        await syncUtility.getPrefixForWorkspace();
                                        syncUtility.triggerManualSync(isSigning: true,isPullSync: true);
                                        await fnUtilities.getMasterInfoBasedOnWorkspace();
                                        passwordController.text = "";
                                        LoadingAlertWidget.onStopping();
                                        Navigator.pop(context);
                                        navigateBottomBar();
                                      } else {
                                        LoadingAlertWidget.onStopping();
                                        showToast(value.message);
                                      }
                                    });
                                  }
                                } else {
                                  LoadingAlertWidget.onStopping();
                                  fnUtilities.internetTurnOnAlertDialog(context);
                                }
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width / 3.5,
                              child: TextWidget("Sign In", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
              )),
            ),
          );
        });
  }

  //DIFFERENT WORKSPACE SIGN IN ALERT DIALOG
  signInWorkspaceDialog(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: width > tabletWidth ? width * 0.4 : width * 0.8,
                child: Form(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Container(
                        child: TextFieldBoxWidget(
                          mController: userIdController,
                          inputType: TextInputType.phone,
                          isPassword: false,
                          maxLength: 10,
                          text: "User ID",
                          validator: validator.validateMobile,
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        child: TextFieldBoxWidget(
                          mController: passwordController,
                          inputType: TextInputType.text,
                          isSecure: true,
                          isPassword: true,
                          text: "Password",
                          validator: validator.validateTextField,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              if (!isLock) {
                                isLock = true;
                                userIdController.text = "";
                                passwordController.text = "";
                                Navigator.pop(context);
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width > tabletWidth ? width * 0.18 : width * 0.3,
                              child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                          GestureDetector(
                            onTap: () async {
                              if (!isLock) {
                                isLock = true;
                                if (await InternetConnectionChecker().hasConnection) {
                                  LoadingAlertWidget.onLoading(context);;
                                  LoginRequestModel loginRequest = new LoginRequestModel();
                                  loginRequest.username = userIdController.text;
                                  loginRequest.password = passwordController.text;
                                  loginRequest.applicationId = MyApp.shopCode;
                                  loginRequest.mobileId = MyApp.deviceId;
                                  await login(loginRequest, "Different").then((value) {
                                    LoadingAlertWidget.onStopping();
                                    if (value.status == 1) {
                                      addUserInList(value.response!);
                                      setState(() {
                                        nonSignedList.clear();
                                        if (value.response?.workspaceDetails != null) {
                                          nonSignedList.addAll(value.response?.workspaceDetails ?? []);
                                          Navigator.pop(context);
                                          differentWorkspaceDialog(context, value.response?.username ?? "");
                                        } else {
                                          Navigator.pop(context);
                                          fnUtilities.commonNormalAlertDialog(context, "No shops available");
                                        }
                                      });
                                      userIdController.text = "";
                                      passwordController.text = "";
                                    } else {
                                      showToast(value.message);
                                    }
                                  });
                                } else {
                                  fnUtilities.internetTurnOnAlertDialog(context);
                                }
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width > tabletWidth ? width * 0.18 : width * 0.3,
                              child: TextWidget("Sign In", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              )),
            ),
          );
        });
  }

  editDeleteFunction(WorkSpace item, flag) async {
    if (!isLock) {
      isLock = true;
      if (await InternetConnectionChecker().hasConnection) {
        validator.validate();
        if (validator.validate()) {
          LoadingAlertWidget.onLoading(context);;
          CheckUserPassword loginRequest = CheckUserPassword();
          loginRequest.password = passwordController.text;
          await checkUserPassword(loginRequest).then((value) async {
            if (value.status == 1) {
              if (flag == "Delete") {
                await deleteWorkspace(item.workspaceId.toString()).then((value) async {
                  if (value.status == 1) {
                    setState(() {
                      ListUtility.nonSignedWorkspaceList.removeWhere((element) => element.workspaceId == item.workspaceId);
                    });
                    passwordController.clear();
                    workspaceNameController.clear();
                    LoadingAlertWidget.onStopping();
                    showToast("Your shop is deleted");
                    Navigator.pop(context);
                  } else {
                    LoadingAlertWidget.onStopping();
                    showToast("Shop not delete");
                  }
                });
              } else if (flag == "Edit") {
                WorkSpace model = WorkSpace();
                model.workspaceId = item.workspaceId;
                model.workspaceName = workspaceNameController.text;
                await updateWorkspaceName(model).then((value) async {
                  if (value.status == 1) {
                    setState(() {
                      item.workspaceName = workspaceNameController.text;
                    });
                    if (checkSigned(item.workspaceId)) {
                      ListUtility.signedWorkspaceList.firstWhere((element) => element.workspaceId == item.workspaceId).workspaceName =
                          item.workspaceName;
                      await workspaceDBScript.updateWorkspaceName(item);
                    }
                    if (MyApp.activeWorkspace.workspaceId == item.workspaceId) {
                      item.userName = MyApp.activeUser.username;
                      await fnUtilities.setActiveWorkspaceAndUser(item);
                      passwordController.clear();
                      workspaceNameController.clear();
                      LoadingAlertWidget.onStopping();
                      Navigator.pop(context);
                      navigateBottomBar();
                    } else {
                      passwordController.clear();
                      workspaceNameController.clear();
                      LoadingAlertWidget.onStopping();
                      Navigator.pop(context);
                    }
                    showToast("Your shop name changed");
                  } else {
                    LoadingAlertWidget.onStopping();
                    showToast("Shop name not change");
                  }
                });
              }
            } else {
              LoadingAlertWidget.onStopping();
              showToast(value.message);
            }
          });
        }
      } else {
        fnUtilities.internetTurnOnAlertDialog(context);
      }
      isLock = false;
    }
  }

  //SIGN IN Edit Delete ALERT DIALOG
  editDeletePasswordWorkspaceDialog(BuildContext context, WorkSpace item, String flag) {
    var width = MediaQuery.of(context).size.width;
    bool maxWidth = width > tabletWidth ? true : false;
    workspaceNameController.text = item.workspaceName.toString();
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: SizedBox(
                width: maxWidth ? width * 0.35 : width * 0.8,
                //width: double.maxFinite,
                child: Form(
                  key: validator.formkey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Container(
                        child: Text(
                          flag == "Delete" ? "Are you sure, do you want to delete outlet" : "Are you sure, do you want to change outlet name",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        child: TextFieldBoxWidget(
                          mController: passwordController,
                          inputType: TextInputType.text,
                          isPassword: true,
                          isSecure: true,
                          text: "Password",
                          validator: validator.validateTextField,
                        ),
                      ),
                      flag != "Delete"
                          ? SizedBox(
                              height: 10,
                            )
                          : Container(),
                      flag != "Delete"
                          ? Container(
                              child: TextFieldBoxWidget(
                                mController: workspaceNameController,
                                inputType: TextInputType.text,
                                isPassword: false,
                                isSecure: false,
                                text: "Shop Name",
                                validator: validator.validateTextField,
                              ),
                            )
                          : Container(),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              if (!isLock) {
                                isLock = true;
                                passwordController.clear();
                                workspaceNameController.clear();
                                Navigator.pop(context);
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: maxWidth ? width * 0.15 : width * 0.3,
                              child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              editDeleteFunction(item, flag);
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: maxWidth ? width * 0.15 : width * 0.3,
                              child: TextWidget(flag != "Delete" ? "Change" : "Delete", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxdecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
              )),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    height = MediaQuery.of(context).size.height;
    width = MediaQuery.of(context).size.width;
    // ignore: deprecated_member_use
    return WillPopScope(
      /// widget.screenID == 1 (from splash screen & bottom bar)
      onWillPop: () async {
        if (widget.screenID == 1) {
          showToast("Please select a outlet");
        } else {
          Navigator.pop(context);
        }

        return false;
      },
      child: Scaffold(
        appBar: BaseAppBar(
          title: text("Choose Outlets", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          appBar: AppBar(),
          widgets: [
            if (MyApp.activeUser.mobile != "0987654321") ...[
              Padding(
                padding: const EdgeInsets.only(right: 20.0, top: 10.0),
                child: createOutelesWidget(),
              ),
            ],
            Padding(
              padding: const EdgeInsets.only(right: 20.0, top: 10.0),
              child: profileIconWidget(),
            ),
          ],
          automaticallyImplementing: widget.screenID == 1 ? false : true,
        ),
        backgroundColor: appThemeColor,
        body: SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              children: [
                SizedBox(
                  height: 10,
                ),
                isLoading
                    ? Container(
                        height: height,
                        child: Center(
                          child: CircularProgressIndicator(
                            color: backgroundColor,
                          ),
                        ))
                    : Container(
                        height: height * 0.8,
                        child: ListUtility.nonSignedWorkspaceList.length > 0
                            ? SingleChildScrollView(
                                child: Padding(
                                  padding: EdgeInsets.all(1.0),
                                  child: viewAllShops(),
                                ),
                              )
                            : Container(
                                height: height,
                                child: Center(
                                    child: Text(
                                  "No shops found",
                                  style: greyTextStyle.apply(color: secondaryTextColor),
                                )),
                              ),
                      ),
              ],
            ),
          ),
        ),
        bottomSheet: MyApp.activeUser.mobile != "0987654321"
            ? Container(
                height: height / 14,
                color: appThemeColor,
                alignment: Alignment.topCenter,
                padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
                child: GestureDetector(
                    onTap: () {
                      if (!isLock) {
                        isLock = true;
                        signInWorkspaceDialog(context);
                        isLock = false;
                      }
                    },
                    child:
                        TextWidget('Sign in to different  Outlets', fontFamily: fontMedium, fontSize: textSizeMedium, textColor: secondaryTextColor)),
              )
            : SizedBox(),
      ),
    );
  }

  /// View all shops
  viewAllShops() {
    width = MediaQuery.of(context).size.width;
    return Wrap(
        children: List.generate(ListUtility.nonSignedWorkspaceList.length, (index) {
      var item = ListUtility.nonSignedWorkspaceList[index];
      return SingleChildScrollView(
        child: item.isActive == true
            ? Padding(
                padding: const EdgeInsets.only(top: 5, bottom: 5, right: 5, left: 5),
                child: Container(
                  width: width > tabletWidth ? width * 0.3 : width,
                  decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 16),
                  padding: EdgeInsets.only(left: 10, right: 0, bottom: 20, top: 20),
                  child: InkWell(
                    child: Column(
                      children: [
                        Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
                          SizedBox(
                            child: Icon(
                              checkSigned(item.workspaceId) ? Icons.cloud_done_rounded : Icons.cloud_circle,
                              color: blackColor,
                              size: 45,
                            ),
                          ),
                          Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                child: Text(item.workspaceName ?? "",
                                    style: TextStyle(
                                      fontSize: 17,
                                    ),
                                    textAlign: TextAlign.start),
                              ),
                              SizedBox(
                                child: Text(item.workspaceId ?? "", style: TextStyle(fontSize: 12, color: grey), textAlign: TextAlign.start),
                              ),
                            ],
                          ),
                          IconButton(
                            alignment: Alignment.centerRight,
                            icon: Icon(item.workspaceId.toString() == workID ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down),
                            onPressed: () {
                              if (!isLock) {
                                isLock = true;
                                if (workID == null) {
                                  setState(() {
                                    workID = item.workspaceId;
                                    isExpanded = false;
                                  });
                                } else if (workID != item.workspaceId) {
                                  setState(() {
                                    workID = item.workspaceId;
                                    isExpanded = false;
                                  });
                                } else {
                                  setState(() {
                                    workID = null;
                                    isExpanded = false;
                                  });
                                }
                                isLock = false;
                              }
                            },
                          ),
                        ]),
                        item.workspaceId.toString() == workID && MyApp.activeUser.mobile != "0987654321"
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      // if (PermissionFunctions.checkPermission(2)) {
                                      if (!isLock) {
                                        isLock = true;
                                        editDeletePasswordWorkspaceDialog(context, item, "Edit");
                                        isLock = false;
                                      }
                                      // }
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 20),
                                      child: Column(
                                        children: const [
                                          Icon(
                                            Icons.edit,
                                            color: Colors.black,
                                          ),
                                          SizedBox(
                                            height: 2,
                                          ),
                                          Text(
                                            "Edit",
                                            style: TextStyle(
                                              fontSize: 12.0,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      //if (PermissionFunctions.checkPermission(3)) {
                                      if (!isLock) {
                                        isLock = true;
                                        if (checkSigned(item.workspaceId)) {
                                          showToast("Please sign out shops");
                                        } else {
                                          editDeletePasswordWorkspaceDialog(context, item, "Delete");
                                        }
                                        isLock = false;
                                      }
                                      // }
                                    },
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(horizontal: 20),
                                      child: Column(
                                        children: const [
                                          Icon(
                                            Icons.delete,
                                            color: Colors.black,
                                          ),
                                          SizedBox(
                                            height: 2,
                                          ),
                                          Text(
                                            "Delete",
                                            style: TextStyle(
                                              fontSize: 12.0,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  SizedBox(
                                    width: 5,
                                  ),
                                ],
                              )
                            : Container(),
                      ],
                    ),
                    onTap: () async {
                      try {
                        if (!isLock) {
                          isLock = true;
                          if (checkSigned(item.workspaceId)) {
                            showToast("Already singed");
                          } else {
                            LoadingAlertWidget.onLoading(context);;
                            if (await InternetConnectionChecker().hasConnection) {
                              var item = ListUtility.nonSignedWorkspaceList[index];
                              CheckUserPassword loginRequest = new CheckUserPassword();
                              loginRequest.password = passwordController.text;
                              await checkUserPassword(loginRequest).then((value) async {
                                item.userName = MyApp.activeUser.username;
                                await DBoperation.insertWorkspace(item);
                                // await DBoperation.insertPermissionsDetails(item.permissions, item.userName, item.workspaceId);
                                await fnUtilities.setActiveWorkspaceAndUser(item);
                                // await DBoperation.queryAllGetsPermissionDetails();
                                await syncUtility.getPrefixForWorkspace();
                                syncUtility.triggerManualSync(isSigning: true,isPullSync: true);
                                await fnUtilities.getMasterInfoBasedOnWorkspace();
                                await PermissionFunctions.getPermisionsFromAPI();
                                LoadingAlertWidget.onStopping();
                                Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
                              });
                            } else {
                              LoadingAlertWidget.onStopping();
                              fnUtilities.internetTurnOnAlertDialog(context);
                            }
                          }
                          isLock = false;
                        }
                      } catch (ex) {
                        LoadingAlertWidget.onStopping();
                        isLock = false;
                      }
                    },
                  ),
                ),
              )
            : SizedBox.shrink(),
      );
    }));
  }

  Widget profileIconWidget() {
    return InkWell(
      onTap: () {
        Navigator.of(context).pushNamed(
          "profile",
        );
      },
      child: Container(
        width: 30.0,
        height: 30.0,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: greyColor,
        ),
        padding: EdgeInsets.all(2.0),
        // decoration: BoxDecoration(borderRadius: BorderRadius.circular(50), color: greyColor),
        child: SvgPicture.asset(
          userSVG,
          color: lightGreyColor,
        ),
      ),
    );
  }

  Widget createOutelesWidget() {
    return InkWell(
        onTap: () async {
          Navigator.push(context, MaterialPageRoute(builder: (context) => AddWorkspacePage()));
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
