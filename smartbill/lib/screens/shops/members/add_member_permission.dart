import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart'; 
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/add_workspace_member.dart';
import 'package:smartbill/model/delete_member_model.dart';
import 'package:smartbill/model/update_permission_model.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/workspace_member.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';
import '../../../Repositories/permissions_repository.dart';
import '../../../Repositories/workspace_repository.dart';
import '../../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/text_field_widget.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/error_logs/error_logs_function.dart';
import '../../../utils/roles_permissions/model/receiving/get_all_categories.dart';
import '../../../utils/roles_permissions/model/receiving/get_roles_model.dart';
import '../../../utils/roles_permissions/permission_repositories.dart';
import '../../../utils/roles_permissions/screens/manage_roles.dart';
import '../../../utils/roles_permissions/widgets/permission_dropdown_widget.dart';
import 'member_view_page.dart';

class AddMemberPermission extends StatefulWidget {
  //CONTACTOR FOR GET WORKSPACE AND MEMBER
  WorkSpace? workSpace;
  WorkspaceMember? member;
  AddMemberPermission({this.workSpace, this.member});

  @override
  _AddMemberPermissionState createState() => _AddMemberPermissionState();
}

class _AddMemberPermissionState extends State<AddMemberPermission> {
  FnUtilities fnUtilities = FnUtilities();
  Validator validator = new Validator();
  final userIdController = new TextEditingController();
  bool? isUpdate;
  bool isLock = false;

  List<RolesModel> rolesList = [];
  RolesModel? selectedRole;
  int? roleId;
  bool isRoleLoading = false;
  List<Categories> categoriesList = [];
  double width = 0.0;

  //   RoleID 1 admin
  //   RoleID 2 manager
  //   RoleID 3 employee

  /// Get roles from api (default and custom roles)
  getAllRoles() async {
    try {
      await getAllRolesApi().then((value) {
        if (value.status == 1) {
          setState(() {
            rolesList = value.result ?? [];
          });
        }
      });
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "GetRoles - Add member permission screen ");
      setState(() {
        isRoleLoading = false;
      });
    }
  }

  //CHECK USER NAME
  userNameCheck() {
    if (widget.member?.userName != null) {
      userIdController.text = widget.member?.userName ?? "";
      selectedRole = rolesList.firstWhere((element) => element.roleId == widget.member?.roleId);
      roleId = selectedRole?.roleId;
      isUpdate = false;
    } else {
      // roleId = 3;
      isUpdate = true;
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  initPageDetails() async {
    setState(() {
      isRoleLoading = true;
    });
    await getAllRoles();
    await userNameCheck();
    setState(() {
      isRoleLoading = false;
    });
  }

  onTapAddMember() async {
    if (isUpdate == true) {
      validator.validate();
      if ((validator.validate() ?? false) && roleId != null) {
        if (!isLock) {
          isLock = true;
          LoadingAlertWidget.onLoading(context);;
          AddWorkSpaceMember model = new AddWorkSpaceMember();
          model.username = userIdController.text;
          model.workspaceId = widget.workSpace?.workspaceId;
          model.roleId = roleId;
          UpdateMember updateMember = new UpdateMember();
          updateMember.roleId = roleId;
          widget.member?.userName == null
              ? await addWorkspaceMember(model).then((value) {
                  if (value.status == 1) {
                    setState(() {
                      userIdController.text = "";
                    });
                    LoadingAlertWidget.onStopping();
                    Navigator.pop(context);
                    Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => MemberView(
                                  workSpaceMember: widget.workSpace,
                                )));
                    showToast(value.message);
                  } else {
                    LoadingAlertWidget.onStopping();
                    showToast(value.message);
                  }
                })
              : await updateWorkspaceMember(widget.member?.id ??0, updateMember).then((value) {
                  if (value.status == 1) {
                    setState(() {
                      userIdController.text = "";
                    });
                    LoadingAlertWidget.onStopping();
                    Navigator.pop(context);
                    Navigator.pushReplacement(
                        context,
                        MaterialPageRoute(
                            builder: (context) => MemberView(
                                  workSpaceMember: widget.workSpace,
                                )));
                    showToast(value.message);
                  } else {
                    LoadingAlertWidget.onStopping();
                    showToast(value.message);
                  }
                });

          isLock = false;
        }
      } else {
        showToast('Please enter valid user id & select a role ');
      }
    }
  }

  //EDIT WIDGET
  Widget editWidget() {
    return Padding(
      padding: EdgeInsets.all(5),
      child: IconButton(
        iconSize: 25,
        icon: Icon(Icons.edit),
        onPressed: () {
          setState(() {
            isUpdate = true;
          });
        },
        color: Colors.white,
      ),
    );
  }

  //DELETE WIDGET
  Widget deleteWidget() {
    return Padding(
      padding: EdgeInsets.all(5),
      child: IconButton(
        iconSize: 25,
        icon: Icon(Icons.delete),
        onPressed: () {
          deleteMemberDialog(context, widget.member!);
        },
        color: Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      // resizeToAvoidBottomInset: false,
      appBar: BaseAppBar(
        title: text("Add Member", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        //automaticallyImplementing: false,
        widgets: widget.member?.userName != null ? <Widget>[editWidget(), deleteWidget()] : null,
      ),
      body: Container(
        height: height,
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  height: 20,
                ),

                /// User ID
                Text(
                  "User ID:",
                  style: black16w500.copyWith(fontSize: 18),
                ),
                Container(
                  padding: EdgeInsets.fromLTRB(0, 10, 0, 0),
                  child: Form(
                    key: validator.formkey,
                    child: TextFieldWidget(
                      mController: userIdController,
                      inputType: TextInputType.phone,
                      isPassword: false,
                      maxLength: 10,
                      // text: "User ID",
                      isEnabledBorder: true,
                      validator: validator.validateMobile,
                      readOnly: widget.member?.userName == null ? false : true,
                    ),
                  ),
                ),

                SizedBox(
                  height: 10,
                ),

                /// Shop Name
                Text(
                  "Shop Name",
                  style: black16w500.copyWith(fontSize: 18),
                ),
                SizedBox(
                  height: 10,
                ),
                Container(
                  width: width,
                  decoration: boxDecoration(bgColor: secondaryTextColor, radius: 10, showShadow: true),
                  padding: EdgeInsets.fromLTRB(20, 10, 10, 10),
                  child: TextWidget(widget.workSpace?.workspaceName ?? "", textColor: primaryTextColor, fontFamily: fontBold, fontSize: 18),
                ),
                SizedBox(
                  height: 10,
                ),

                /// Roles
                Text(
                  "Roles",
                  style: black16w500.copyWith(fontSize: 18),
                ),
                // ignore: sdk_version_ui_as_code
                if (isRoleLoading) ...[
                  SizedBox(
                    height: 50,
                    child: Align(
                      alignment: Alignment.center,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                      ),
                    ),
                  )
                ] else ...[
                  permissionsDropDownWidget(),
                ],

                GestureDetector(
                  onTap: () {
                    onTapAddMember();
                  },
                  child: Container(
                    margin: EdgeInsets.fromLTRB(0, 20, 0, 20),
                    alignment: Alignment.center,
                    height: 50,
                    child: widget.member?.userName == null
                        ? TextWidget("Add Member", textColor: secondaryTextColor, isCentered: true)
                        : TextWidget("Update Member", textColor: secondaryTextColor, isCentered: true),
                    decoration: boxDecoration(bgColor: isUpdate == true ? buttonThemeColor : gray, radius: 8),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Permission dropdown widget
  permissionsDropDownWidget() {
    return Row(
      children: [
        Expanded(
          child: Padding(
            padding: const EdgeInsets.only(top: 15.0),
            child: PermissionDropdown(
              rolesList: rolesList,
              selectedRole: selectedRole,
              onChange: (value) {
                if (isUpdate ?? false) {
                  setState(() {
                    selectedRole = value;
                    roleId = selectedRole?.roleId;
                    // getAllCategoriesByIdFromApi(roleId);
                    FocusScope.of(context).requestFocus(FocusNode());
                  });
                }
              },
            ),
          ),
        ),
        SizedBox(
          width: 10,
        ),
        Padding(
          padding: const EdgeInsets.only(top: 15.0),
          child: InkWell(
            onTap: () {
              if (isUpdate ?? false) {
                if (roleId != null) {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ManegeRolesScreen(
                                roleID: roleId,
                              )));
                } else {
                  showToast('Select a role to view permissions');
                }
              }
            },
            child: Container(
              padding: const EdgeInsets.all(10.0),
              // margin: EdgeInsets.fromLTRB(20, 20, 20, 20),
              alignment: Alignment.center,
              height: 45,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Icons.visibility,
                    color: secondaryTextColor,
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Text(
                    "View",
                    style: black16TextStyle.copyWith(color: secondaryTextColor, fontSize: 18),
                  ),
                ],
              ),
              decoration: boxDecoration(bgColor: isUpdate == true ? buttonThemeColor : gray, radius: 8),
            ),
          ),
        )
      ],
    );
  }

  /// delete member dialog
  deleteMemberDialog(BuildContext context, WorkspaceMember member) {
    return showDialog(
        context: context,
        builder: ((context) {
          return ConfirmationAlertDialog(
              content: "Are you sure to delete this role",
              buttonNameOne: 'No',
              buttonNameTwo: 'Yes',
              ontabButtonOne: () {
                Navigator.pop(context);
              },
              ontabButtonTwo: () async {
                if (!isLock) {
                  this.isLock = true;
                  DeleteMember deleteMember = new DeleteMember();
                  LoadingAlertWidget.onLoading(context);;
                  deleteMember.workspaceId = widget.workSpace?.workspaceId;
                  deleteMember.userName = widget.member?.userName;
                  await deleteWorkspaceMember(deleteMember).then((value) {
                    if (value.status == 1) {
                      userIdController.text = "";
                      LoadingAlertWidget.onStopping();
                      Navigator.pop(context);
                      Navigator.pop(context);
                      Navigator.pushReplacement(
                          context,
                          MaterialPageRoute(
                              builder: (context) => MemberView(
                                    workSpaceMember: widget.workSpace,
                                  )));
                      showToast(value.message);
                    } else {
                      LoadingAlertWidget.onStopping();
                      Navigator.pop(context);
                      showToast(value.message);
                    }
                  });
                }
                isLock = false;
              });
        }));
  }
}
