import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart'; 
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace.dart';

import 'package:smartbill/utils/common_widgets/shop_card_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../Repositories/workspace_repository.dart';
import '../../../utils/common_widgets/header_footer.dart';
import 'member_view_page.dart';

class AddMember extends StatefulWidget {
  @override
  _AddMemberState createState() => _AddMemberState();
}

class _AddMemberState extends State<AddMember> {
  Validator validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  List<WorkSpace> adminWorkspaceList = [];
  bool isLoading = false;
  bool isLock = false;

  //GET WORKSPACE DETAILS BY ADMIN
  getAdminWorkspaceDetails() async {
    setState(() {
      isLoading = true;
    });
    if (await InternetConnectionChecker().hasConnection) {
      getWorkspaceListByAdmin().then((value) {
        if (this.mounted) {
          setState(() {
            isLoading = false;
          });
          if (value.result != null) {
            setState(() {
              adminWorkspaceList = value.result!;
            });
          } else {
            showToast("No shops found");
          }
        }
      });
    } else {
      setState(() {
        isLoading = false;
      });
      fnUtilities.internetTurnOnAlertDialog(context);
    }
  }

  @override
  void initState() {
    getAdminWorkspaceDetails();

    setState(() {});
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    return SafeArea(
      child: Scaffold(
        backgroundColor: appThemeColor,
        appBar: BaseAppBar(
          title: text("Choose Outlets",
              textColor: secondaryTextColor,
              fontSize: textSizeNormal,
              fontFamily: fontMedium),
          appBar: AppBar(),
          //automaticallyImplementing: false,
        ),
        body: SingleChildScrollView(
          child: Container(
              child: Column(
            children: [
              SizedBox(
                height: 10,
              ),
              isLoading
                  ? Container(
                      height: height,
                      child: Center(
                          child: CircularProgressIndicator(
                        color: backgroundColor,
                      )))
                  : Container(
                      height: height,
                      padding: EdgeInsets.only(bottom: 100),
                      child: adminWorkspaceList.length > 0
                          ? chooseShopsLists()
                          : Center(),
                    ),
            ],
          )),
        ),
      ),
    );
  }

  /// choose shops list to add member
  chooseShopsLists() {
    return ListView.builder(
        itemCount: adminWorkspaceList.length,
        itemBuilder: (BuildContext context, int index) {
          var item = adminWorkspaceList[index];
          return SingleChildScrollView(
              child: InkWell(
            onTap: () async {
              if (!isLock) {
                isLock = true;
                Navigator.push(
                    context,
                    MaterialPageRoute(
                        builder: (context) => MemberView(
                              workSpaceMember: item,
                            )));
                isLock = false;
              }
            },
            child: ShopCardWidget(
              isItemSelected: false,
              isShowPopUp: false,
              isShowRadioButtons: false,
              workspace: item,
            ),
          ));
        });
  }
}
