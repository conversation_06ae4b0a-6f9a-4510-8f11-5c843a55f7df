import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart'; 
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/workspace_member.dart';
import 'package:smartbill/screens/shops/members/add_member_permission.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../Repositories/workspace_repository.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/common_widgets/text_widget.dart';

class MemberView extends StatefulWidget {
  //CONTACTOR FOR GET MEMBER BY WORKSPACE
  WorkSpace? workSpaceMember;
  MemberView({this.workSpaceMember});

  @override
  _MemberViewState createState() => _MemberViewState();
}

class _MemberViewState extends State<MemberView> {
  FnUtilities fnUtilities = new FnUtilities();
  List<WorkspaceMember> workspaceMemberList = [];
  bool isLoading = false;
  bool isLock = false;

  //GET MEMBER DETAILS
  getWorkspaceMemberList() async {
    setState(() {
      isLoading = true;
    });
    if (await InternetConnectionChecker().hasConnection) {
      getWorkspaceMember(widget.workSpaceMember?.workspaceId).then((value) {
        if (this.mounted) {
          setState(() {
            isLoading = false;
          });
          if (value.status == 1) {
            setState(() {
              workspaceMemberList.clear();
              workspaceMemberList = value.result ?? [];
            });
          } else {
            showToast(value.message);
          }
        }
      });
    } else {
      setState(() {
        isLoading = false;
      });
      fnUtilities.internetTurnOnAlertDialog(context);
    }
  }

  @override
  void initState() {
    getWorkspaceMemberList();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(widget.workSpaceMember?.workspaceName ?? "" + " Members",
            textColor: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),
        appBar: AppBar(),
        //automaticallyImplementing: false,
      ),
      backgroundColor: appThemeColor,
      body: SingleChildScrollView(
        child: Container(
            child: Column(
          children: [
            SizedBox(
              height: 10,
            ),
            isLoading
                ? Container(
                    height: height,
                    child: Center(
                        child: CircularProgressIndicator(
                      color: backgroundColor,
                    )))
                : Container(
                    height: height / 1.3,
                    child: workspaceMemberList.length > 0
                        ? viewMemberList()
                        : Center(),
                  ),
          ],
        )),
      ),
      bottomNavigationBar: Container(
        height: height / 14,
        alignment: Alignment.topCenter,
        padding: EdgeInsets.fromLTRB(0, 0, 0, 0),
        child: GestureDetector(
            onTap: () async {
              if (!isLock) {
                isLock = true;
                if (await InternetConnectionChecker().hasConnection) {
                  await Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => AddMemberPermission(
                                workSpace: widget.workSpaceMember,
                              )));
                } else {
                  fnUtilities.internetTurnOnAlertDialog(context);
                }
                isLock = false;
              }
            },
            child: TextWidget('Add new member',
                fontFamily: fontMedium,
                fontSize: textSizeMedium,
                textColor: secondaryTextColor)),
      ),
    );
  }

  ///View members  list
  viewMemberList() {
    return Container(
      child: ListView.builder(
          itemCount: workspaceMemberList.length,
          itemBuilder: (BuildContext context, int index) {
            var item = workspaceMemberList[index];
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Container(
                  decoration: boxdecoration(
                      bgColor: backgroundColor, showShadow: true, radius: 16),
                  padding:
                      EdgeInsets.only(left: 10, right: 0, bottom: 8, top: 8),
                  child: ListTile(
                    leading: Icon(
                      Icons.account_circle,
                      color: primaryTextColor,
                      size: 50,
                    ),
                    title: Text((item.firstName??"") + " " + (item.lastName??""),
                        style: TextStyle(
                            color: primaryTextColor,
                            fontFamily: fontMedium)),
                    subtitle: Text("UID:" + (item.userName?? "") ,
                        style: TextStyle(fontFamily: fontMedium)),
                    onTap: () async {
                      if (!isLock) {
                        isLock = true;
                        if (await InternetConnectionChecker().hasConnection) {
                          await Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => AddMemberPermission(
                                        workSpace: widget.workSpaceMember,
                                        member: item,
                                      )));
                        } else {
                          fnUtilities.internetTurnOnAlertDialog(context);
                        }
                        isLock = false;
                      }
                    },
                  ),
                ),
              ),
            );
          }),
    );
  }
}
