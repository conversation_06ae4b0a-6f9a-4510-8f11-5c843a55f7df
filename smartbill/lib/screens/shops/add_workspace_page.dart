import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/add_workspace.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../main.dart';
import '../../Repositories/workspace_repository.dart';
import '../../model/get_shop_type.dart'; 
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class AddWorkspacePage extends StatefulWidget {
  @override
  _AddWorkspacePageState createState() => _AddWorkspacePageState();
}

class _AddWorkspacePageState extends State<AddWorkspacePage> {
  Validator validator = Validator();
  DBOperations DBoperation = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  DBOperations dbOperations = new DBOperations();
  final workspaceNameController = TextEditingController();
  var width;
  var height;
  int? index;
  bool isLock = false;
  bool isLoading = false;

  ShopTypeList? selectKeyword;
  List<ShopTypeList> shopTypeList = [];
  bool shopTypeError = false;

  @override
  void initState() {
    super.initState();
    initDetails();
  }

  initDetails() async {
    await getShopType();
  }

  /// get shop types from api
  getShopType() async {
    try {
      setState(() {
        isLoading = true;
      });
      await getShopTypeAPI().then((value) {
        if (value.status == 1) {
          setState(() {
            shopTypeList = value.result ?? [];
          });
        } else {
          toast(value.message);
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      log(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      resizeToAvoidBottomInset: false,
      appBar: BaseAppBar(
        title: text("Create Outlets", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        //automaticallyImplementing: false,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // ignore: sdk_version_ui_as_code
            if (isLoading) ...[
              SizedBox(height: height - 100, child: Center(child: CircularProgressIndicator()))
            ] else ...[
              Center(
                child: Container(
                  alignment: Alignment.center,
                  margin: EdgeInsets.all(24),
                  decoration: boxDecoration(bgColor: secondaryTextColor, showShadow: true, radius: 4),
                  padding: EdgeInsets.all(20),
                  child: Form(
                    key: validator.formkey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        SizedBox(height: 10),
                        EditText(
                          text: "Outlet Name",
                          mController: workspaceNameController,
                          isPassword: false,
                          maxLength: 40,
                          validator: validator.validateTextField,
                        ),
                        SizedBox(height: 16),
                        shopTypeDropDownWidget(),
                        shopTypeError
                            ? Padding(
                                padding: const EdgeInsets.only(top: 5.0),
                                child: Align(
                                    alignment: Alignment.bottomLeft,
                                    child: Text(
                                      "This field is required",
                                      style: TextStyle(color: Color.fromARGB(255, 242, 60, 60)),
                                    )),
                              )
                            : SizedBox.shrink(),
                        SizedBox(height: 16),
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: GestureDetector(
                                onTap: () async {
                                  if (!isLock) {
                                    isLock = true;
                                    if (await InternetConnectionChecker().hasConnection) {
                                      validator.validate();
                                      if ((validator.validate() ?? false) && selectKeyword != null) {
                                        if (workspaceNameController.text != null) {
                                          LoadingAlertWidget.onLoading(context);;
                                          AddWorkspace model = new AddWorkspace();
                                          model.workspaceName = workspaceNameController.text;
                                          model.shopTypeId = selectKeyword?.id;
                                          await addWorkspace(model).then((value) async {
                                            if (value.status == 1) {
                                              WorkSpace model = new WorkSpace();
                                              model.workspaceId = value.result?.workspaceId;
                                              model.workspaceName = value.result?.workspaceName;
                                              model.userName = MyApp.activeUser.username;
                                              model.isActive = value.result?.isActive;
                                              model.roleId = value.result?.roleId;
                                              model.expiryDate = value.result?.expiryDate;
                                              model.permissions = value.result?.permissions;
                                              fnUtilities.setActiveWorkspaceAndUser(model);
                                              if (value.result?.salesCount != null) {
                                                SharedPreferences prefs = await SharedPreferences.getInstance();
                                                prefs.setInt("salesCount", value.result?.salesCount ??0 );
                                              }
                                              DBoperation.insertWorkspace(model);
                                              SyncUtility sync = SyncUtility();
                                              ListUtility.clearAllLists();
                                              await sync.getPrefixForWorkspace();
                                              await PermissionFunctions.getPermisionsFromAPI();
                                              await fnUtilities.startInsertSettingDetails(value.result?.workspaceSettings??[], 1);
                                              // await DBoperation.insertPermissionsDetails(model.permissions, model.userName, model.workspaceId);
                                              // await DBoperation.queryAllGetsPermissionDetails();
                                              workspaceNameController.text = "";
                                              LoadingAlertWidget.onStopping();
                                              Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
                                              showToast("Shop Created");
                                            } else {
                                              LoadingAlertWidget.onStopping();
                                              showToast(value.message);
                                            }
                                          });
                                        }
                                      } else {
                                        setState(() {
                                          shopTypeError = true;
                                        });
                                      }
                                    } else {
                                      fnUtilities.internetTurnOnAlertDialog(context);
                                    }
                                    isLock = false;
                                  }
                                },
                                child: Container(
                                  // margin: EdgeInsets.only(right: 16),
                                  alignment: Alignment.center,
                                  height: 50,
                                  child: TextWidget("Submit", textColor: secondaryTextColor, isCentered: true),
                                  decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8),
                                ),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 10),
                      ],
                    ),
                  ),
                ),
              )
            ],
          ],
        ),
      ),
    );
  }

  shopTypeDropDownWidget() {
    return InputDecorator(
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
        labelText: 'Shop Type',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
      ),
      child: DropdownButtonHideUnderline(
        child: new DropdownButton<ShopTypeList>(
          menuMaxHeight: 250,
          hint: Text("Select Shop Type"),
          value: selectKeyword,
          items: shopTypeList.map((ShopTypeList value) {
            return new DropdownMenuItem<ShopTypeList>(
              value: value,
              child: new Text(value.shopType ?? ""),
            );
          }).toList(),
          onChanged: (ShopTypeList? val) async {
            if (val != null) {
              setState(() {
                selectKeyword = val;
                FocusScope.of(context).requestFocus(FocusNode());
                if (shopTypeError) {
                  shopTypeError = false;
                }
              });
            } else {
              setState(() {
                shopTypeError = true;
              });
            }
          },
        ),
      ),
    );
  }
}
