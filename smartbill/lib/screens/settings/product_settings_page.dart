// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';

import '../../main.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ProductSettingsPage extends StatefulWidget {
  @override
  _ProductSettingsPageState createState() => _ProductSettingsPageState();
}

class _ProductSettingsPageState extends State<ProductSettingsPage> {
  final dbHelper = DatabaseHelper.instance;
  Validator validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  bool isLock = false;
  double width = 0.0;
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  SyncUtility syncUtility = new SyncUtility();
  bool showDynamicQty = false;
  bool showMeasurement = false;
  bool showToppings = false;
  bool showTax = false;

//GET PRODUCT SETTINGS FROM DB
  initSettings() async {
    setState(() {
      showMeasurement =
          fnUtilities.workSpaceSetValues(key_enableMeasurement) == "1";
      showDynamicQty =
          fnUtilities.workSpaceSetValues(key_enableDynamicQty) == "1";
      showToppings = fnUtilities.workSpaceSetValues(key_enableToppings) == "1";
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Product Settings",
          style: TextStyle(color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),  ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SingleChildScrollView(
                  padding: EdgeInsets.only(top: 10, right: 15, left: 15),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width / 1.5,
                              child: Text(
                                'Show Unit Measurement',
                                style: TextStyle(
                                    color: primaryTextColor,
                                    fontSize: textSizeMedium,
                                    fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: showMeasurement,
                              onChanged: (value) async {
                                 if(PermissionFunctions.checkPermission(151)){
                                setState(() {
                                  showMeasurement = value;
                                  settingsDetail.settingKey = "ShowMeasurement";
                                  settingsDetail.settingValue =
                                      value ? "1" : "0";
                                  settingsDetail.workspaceId =
                                      MyApp.activeWorkspace.workspaceId;
                                  settingsDetail.sync = 0;
                                  fnUtilities
                                      .updateSettingDetails(settingsDetail);
                                });
                                 }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width / 1.5,
                              child: Text(
                                'Show Dynamic Quantity',
                                style: TextStyle(
                                    color: primaryTextColor,
                                    fontSize: textSizeMedium,
                                    fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: showDynamicQty,
                              onChanged: (value) async {
                                if(PermissionFunctions.checkPermission(152)){
                                setState(() {
                                  showDynamicQty = value;
                                  settingsDetail.settingKey = "ShowDynamicQty";
                                  settingsDetail.settingValue =
                                      value ? "1" : "0";
                                  settingsDetail.workspaceId =
                                      MyApp.activeWorkspace.workspaceId;
                                  settingsDetail.sync = 0;
                                  fnUtilities
                                      .updateSettingDetails(settingsDetail);
                                });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width / 1.5,
                              child: Text(
                                'Show Toppings',
                                style: TextStyle(
                                    color: primaryTextColor,
                                    fontSize: textSizeMedium,
                                    fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: showToppings,
                              onChanged: (value) async {
                                if(PermissionFunctions.checkPermission(153)){
                                setState(() {
                                  showToppings = value;
                                  settingsDetail.settingKey = "ShowToppings";
                                  settingsDetail.settingValue =
                                      value ? "1" : "0";
                                  settingsDetail.workspaceId =
                                      MyApp.activeWorkspace.workspaceId;
                                  settingsDetail.sync = 0;
                                  fnUtilities
                                      .updateSettingDetails(settingsDetail);
                                });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      // Padding(
                      //   padding: const EdgeInsets.symmetric(
                      //       horizontal: 8.0, vertical: 5.0),
                      //   child: Row(
                      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //     children: <Widget>[
                      //       Container(
                      //         width: width / 1.5,
                      //         child: Text(
                      //           'Show Tax',
                      //           style: TextStyle(
                      //               color: primaryTextColor,
                      //               fontSize: textSizeMedium,
                      //               fontFamily: fontSemibold),
                      //         ),
                      //       ),
                      //       Switch(
                      //         value: showTax,
                      //         onChanged: (value) async {
                      //           setState(() {
                      //             showTax = value;
                      //             settingsDetail.settingKey = "ShowTax";
                      //             settingsDetail.settingValue =
                      //                 value ? "1" : "0";
                      //             settingsDetail.workspaceId =
                      //                 MyApp.activeWorkspace.workspaceId;
                      //             settingsDetail.sync = 0;
                      //             fnUtilities
                      //                 .updateSettingDetails(settingsDetail);
                      //             MyApp.syncStatus = 0;
                      //           });
                      //         },
                      //         activeTrackColor: buttonThemeColor,
                      //         activeColor: buttonThemeColor,
                      //       ),
                      //     ],
                      //   ),
                      // ),
                      // divider(),
                      // InkWell(
                      //   onTap: () async {
                      //     showToast('Products upload started');
                      //     await syncUtility.startUploadImageSync('products');
                      //     showToast('Products upload completed');
                      //   },
                      //   child: Container(
                      //     height: 50,
                      //     padding: EdgeInsets.all(8),
                      //     child: Row(
                      //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //       crossAxisAlignment: CrossAxisAlignment.center,
                      //       children: <Widget>[
                      //         Container(
                      //           child: Text(
                      //             'Upload Images',
                      //             style: TextStyle(
                      //                 color: primaryTextColor,
                      //                 fontSize: textSizeMedium,
                      //                 fontFamily: fontSemibold),
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                      // divider(),
                      // InkWell(
                      //   onTap: () async {
                      //     showToast('Products download started');
                      //     await syncUtility.startDownloadImageSync('products');
                      //     showToast('Products download completed');
                      //   },
                      //   child: Container(
                      //     height: 50,
                      //     padding: EdgeInsets.all(8),
                      //     child: Row(
                      //       mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //       crossAxisAlignment: CrossAxisAlignment.center,
                      //       children: <Widget>[
                      //         Container(
                      //           child: Text(
                      //             'Download Images',
                      //             style: TextStyle(
                      //                 color: primaryTextColor,
                      //                 fontSize: textSizeMedium,
                      //                 fontFamily: fontSemibold),
                      //           ),
                      //         ),
                      //       ],
                      //     ),
                      //   ),
                      // ),
                      // divider(),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
