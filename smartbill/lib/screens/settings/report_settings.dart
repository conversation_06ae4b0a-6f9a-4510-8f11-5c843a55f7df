import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';

import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ReportSettings extends StatefulWidget {
  @override
  ReportSettingsState createState() => ReportSettingsState();
}

class ReportSettingsState extends State<ReportSettings> {
  final takeAwayController = TextEditingController();
  final eatAndPayController = TextEditingController();
  final tableOrderController = TextEditingController();
  Validator validator = new Validator();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  bool isLock = false;

  @override
  void dispose() {
    takeAwayController.clear();
    eatAndPayController.clear();
    tableOrderController.clear();
    super.dispose();
  }

  //GET PANEL DETAILS FROM DB
  initSettings() async {
    setState(() {
      takeAwayController.text =
          fnUtilities.workSpaceSetValues(key_takeAwayName) ?? "";
      eatAndPayController.text =
          fnUtilities.workSpaceSetValues(key_eatAndPayName) ?? "";
      tableOrderController.text =
          fnUtilities.workSpaceSetValues(key_tableOrderName) ?? "";
    });
  }

  initFunction() async {
    await initSettings();
  }

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Report Settings",
           style: TextStyle( color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              height: height,
              alignment: Alignment.center,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      margin: EdgeInsets.all(24),
                      decoration: boxDecoration(
                          bgColor: secondaryTextColor,
                          showShadow: true,
                          radius: 4),
                      padding: EdgeInsets.all(20),
                      child: Form(
                        key: validator.formkey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            SizedBox(height: 10),
                            EditText(
                              text: "Take Away Title",
                              mController: takeAwayController,
                              isPassword: false,
                              maxLength: 40,
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Eat & Pay Title",
                              mController: eatAndPayController,
                              isPassword: false,
                              maxLength: 40,
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Table Order Title",
                              mController: tableOrderController,
                              isPassword: false,
                              maxLength: 40,
                            ),
                            SizedBox(height: 16),
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () async {
                                      if (!isLock) {
                                        isLock = true;
                                      if(PermissionFunctions.checkPermission(154) || PermissionFunctions.checkPermission(155)||PermissionFunctions.checkPermission(156)){
                                          validator.validate();
                                          if (validator.validate() ?? false) {
                                            LoadingAlertWidget.onLoading(context);;
                                            WorkspaceSettings workspace1 =
                                                new WorkspaceSettings();
                                            workspace1.settingKey =
                                                "TakeAwayTitle";
                                            workspace1.settingValue =
                                                takeAwayController.text;
                                            workspace1.sync = 0;
                                            await fnUtilities
                                                .updateSettingDetails(
                                                    workspace1);
                                            WorkspaceSettings workspace2 =
                                                new WorkspaceSettings();
                                            workspace2.settingKey =
                                                "EatAndPayTitle";
                                            workspace2.settingValue =
                                                eatAndPayController.text;
                                            workspace2.sync = 0;
                                            await fnUtilities
                                                .updateSettingDetails(
                                                    workspace2);
                                            WorkspaceSettings workspace3 =
                                                new WorkspaceSettings();
                                            workspace3.settingKey =
                                                "TableOrderTitle";
                                            workspace3.settingValue =
                                                tableOrderController.text;
                                            workspace3.sync = 0;
                                            await fnUtilities
                                                .updateSettingDetails(
                                                    workspace3);
                                            await initSettings();
                                            LoadingAlertWidget.onStopping();
                                            Navigator.pop(context);
                                          }
                                        } else {
                                          showToast(
                                              "You don't have a permission");
                                        }
                                        isLock = false;
                                      }
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(right: 16),
                                      alignment: Alignment.center,
                                      height: 50,
                                      child: TextWidget("Update",
                                          textColor: secondaryTextColor, isCentered: true),
                                      decoration: boxDecoration(
                                          bgColor: buttonThemeColor, radius: 8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // SizedBox(height: 10),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
