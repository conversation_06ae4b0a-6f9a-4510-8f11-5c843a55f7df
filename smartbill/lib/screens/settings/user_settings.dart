// ignore_for_file: non_constant_identifier_names
import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import '../../main.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';

class UserSettings extends StatefulWidget {
  @override
  UserSettingsState createState() => UserSettingsState();
}

class UserSettingsState extends State<UserSettings> {
  double width = 0.0;
  double height = 0.0;
  String? saleLayout;
  bool isAlwaysOnDisplay = false;
  bool isAutoSync = false;
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();

  //GET PANEL DETAILS FROM DB
  initSettings() {
    setState(() {
      saleLayout =
          fnUtilities.workSpaceUserSetValue(key_saleLayout) ?? "SaleLayout1";
      isAutoSync = fnUtilities.workSpaceUserSetValue(key_autoSync) == "1";
      isAlwaysOnDisplay =
          fnUtilities.workSpaceSetValues(key_alwaysOnDisplay) == "1";
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("User Settings",
           style: TextStyle( color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 10),
        child: Column(
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Container(
                    width: width / 1.5,
                    child: Text(
                      'Auto Sync',
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: textSizeMedium,
                          fontFamily: fontSemibold),
                    ),
                  ),
                  Switch(
                    value: isAutoSync,
                    onChanged: (value) async {
                      setState(() {
                        isAutoSync = value;
                        settingsDetail.settingKey = "AutoSync";
                        settingsDetail.settingValue = value ? "1" : "0";
                        settingsDetail.sync = 0;
                        settingsDetail.workspaceId =
                            MyApp.activeWorkspace.workspaceId;
                        fnUtilities.updateSettingDetails(settingsDetail,
                            tableType: "WorkSpaceUserSettings");
                      });
                    },
                    activeTrackColor: buttonThemeColor,
                    activeColor: buttonThemeColor,
                  ),
                ],
              ),
            ),
            divider(),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 5),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Container(
                    width: width / 1.5,
                    child: Text(
                      'Always On Display',
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: textSizeMedium,
                          fontFamily: fontSemibold),
                    ),
                  ),
                  Switch(
                    value: isAlwaysOnDisplay,
                    onChanged: (value) async {
                      setState(() {
                        isAlwaysOnDisplay = value;
                        settingsDetail.settingKey = "AlwaysOnDisplay";
                        settingsDetail.settingValue = value ? "1" : "0";
                        settingsDetail.sync = 0;
                        settingsDetail.workspaceId =
                            MyApp.activeWorkspace.workspaceId;
                        fnUtilities.updateSettingDetails(settingsDetail,
                            tableType: "WorkSpaceUserSettings");
                      });
                    },
                    activeTrackColor: buttonThemeColor,
                    activeColor: buttonThemeColor,
                  ),
                ],
              ),
            ),
            divider(),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(top: 10, left: 7),
                  child: Text("Select Sales Layout",
                      style: TextStyle(
                          color: primaryTextColor,
                          fontSize: textSizeMedium,
                          fontFamily: fontSemibold)),
                ),
                RadioListTile(
                  groupValue: saleLayout,
                  value: 'SaleLayout1',
                  title: Text('Layout 1'),
                  onChanged: (val) {
                    setState(() {
                      saleLayout = "SaleLayout1";
                      settingsDetail.settingKey = "SaleLayout";
                      settingsDetail.settingValue = saleLayout;
                      settingsDetail.sync = 0;
                      settingsDetail.workspaceId =
                          MyApp.activeWorkspace.workspaceId;
                      fnUtilities.updateSettingDetails(settingsDetail,
                          tableType: "WorkSpaceUserSettings");
                    });
                  },
                ),
                RadioListTile(
                  groupValue: saleLayout,
                  value: 'SaleLayout2',
                  title: Text('Layout 2'),
                  onChanged: (val) {
                    setState(() {
                      saleLayout = "SaleLayout2";
                      settingsDetail.settingKey = "SaleLayout";
                      settingsDetail.settingValue = saleLayout;
                      settingsDetail.sync = 0;
                      settingsDetail.workspaceId =
                          MyApp.activeWorkspace.workspaceId;
                      fnUtilities.updateSettingDetails(settingsDetail,
                          tableType: "WorkSpaceUserSettings");
                    });
                  },
                ),

                ///Desktop layout
                RadioListTile(
                  groupValue: saleLayout,
                  value: 'DesktopLayout',
                  title: Text('Desktop Layout'),
                  onChanged: (val) {
                    setState(() {
                      saleLayout = "DesktopLayout";
                      settingsDetail.settingKey = "SaleLayout";
                      settingsDetail.settingValue = saleLayout;
                      settingsDetail.sync = 0;
                      settingsDetail.workspaceId =
                          MyApp.activeWorkspace.workspaceId;
                      fnUtilities.updateSettingDetails(settingsDetail,
                          tableType: "WorkSpaceUserSettings");
                    });
                  },
                ),
                // RadioListTile(
                //   groupValue: saleLayout,
                //   value: 'SaleLayout3',
                //   title: Text('Layout 3'),
                //   onChanged: (val) {
                //     setState(() {
                //       saleLayout = "SaleLayout3";
                //       settingsDetail.settingKey = "SaleLayout";
                //       settingsDetail.settingValue = saleLayout;
                //       settingsDetail.sync = 0;
                //       settingsDetail.workspaceId =
                //           MyApp.activeWorkspace.workspaceId;
                //       fnUtilities.updateSettingDetails(settingsDetail,
                //           tableType: "WorkSpaceUserSettings");
                //     });
                //   },
                // ),
              ],
            ),
            divider(),
          ],
        ),
      ),
    );
  }
}
