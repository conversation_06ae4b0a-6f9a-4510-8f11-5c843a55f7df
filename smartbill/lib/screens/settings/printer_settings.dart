// ignore_for_file: non_constant_identifier_names, must_be_immutable, sdk_version_ui_as_code, sdk_version_set_literal
import 'dart:io';

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:flutter/material.dart' hide Image;
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/screens/settings/printer_add_page.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';

import 'package:geolocator/geolocator.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/text_widget.dart';

class printerSettingsPage extends StatefulWidget {
  PrinterDevices? printerDevice;
  @override
  _printerSettingsPageState createState() => _printerSettingsPageState(printerDevice);
}

class _printerSettingsPageState extends State<printerSettingsPage> {
  _printerSettingsPageState(PrinterDevices? printerDevice);
  PrinterDevices? printerDevice;
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  FnUtilities fnUtilities = FnUtilities();
  List<BluetoothDevice> devicesList = [];
  List<PrinterDevices> configuredPrinters = [];
  BluetoothDevice? device;
  bool isLock = false;
  bool isMultiPrinter = false;
  WorkspaceSettings settingsDetail = new WorkspaceSettings();

  //FOR BLUETOOTH CONNECTION
  Future<void> getBondedDevices({String type = "bluetooth"}) async {
    List<BluetoothDevice> devices = [];
    try {
      devices = await bluetooth.getBondedDevices(type: type);
    } on PlatformException {}
    setState(() {
      devicesList = devices;
    });
  }

  //ASSIGN PRINTER DETAILS
  initPrint() async {
    await printerUtilities.getPrinterInfo(printFor: PrinterFor.both);
    setState(() {
      configuredPrinters.addAll(ListUtility.printerDevicesList);
    });
  }

  bool checkPrinterExists(PrinterDevices printerDevices) {
    for (PrinterDevices model in configuredPrinters) {
      if (model.printerAddress == printerDevices.printerAddress) {
        return true;
      }
    }
    return false;
  }

  addPrinter(PrinterDevices model) {
    if (model != null) {
      if (!checkPrinterExists(model)) {
        setState(() {
          configuredPrinters.add(model);
        });
        printerUtilities.setPrinterInfo(configuredPrinters);
        updateMultiPrintSetting();
      } else {
        showToast("Already this device was added");
      }
    }
  }

  initSettings() {
    setState(() {
      isMultiPrinter = fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1";
      if (isMultiPrinter || configuredPrinters.length >= 2) {
        isMultiPrinter = true;
      } else {
        isMultiPrinter = false;
      }
    });
  }

  updateMultiPrintSetting() async {
    if (configuredPrinters.length >= 2) {
      isMultiPrinter = true;
    }
    settingsDetail.sync = 0;
    settingsDetail.settingKey = multiPrinter;
    settingsDetail.settingValue = isMultiPrinter ? "1" : "0";
    await fnUtilities.updateDeviceSettingDetails(settingsDetail);
  }

  initPageDetails() async {
    await initPrint();
    await initSettings();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  //PRINTER UNPAIR ALERT DIALOG
  createAlertDialogDisconnectBlutooth(BuildContext buildContext, int removeIndex) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return Container(
            height: 200,
            child: AlertDialog(
              content: Text(
                "Are you sure want to unpair this device ?",
                style: TextStyle(color: primaryTextColor, fontSize: 15.0),
              ),
              actions: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: 100,
                    child: TextWidget("No", textColor: secondaryTextColor, isCentered: true),
                    decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                  ),
                ),
                GestureDetector(
                  onTap: () async {
                    setState(() {
                      configuredPrinters.removeAt(removeIndex);
                    });
                    printerUtilities.setPrinterInfo(configuredPrinters);
                    Navigator.pop(context, 'true');
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: 100,
                    child: TextWidget("Yes", textColor: secondaryTextColor, isCentered: true),
                    decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                  ),
                ),
              ],
            ),
          );
        });
  }

  /// To show pop up Dialogue
  onTapAddButton() {
    return showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Container(
              height: 250,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Select a device type ',
                    style: headingFontStyle2.apply(color: primaryTextColor),
                  ),
                  SizedBox(
                    height: 16,
                  ),
                  if (Platform.isAndroid || Platform.isIOS) ...[
                    Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.all(8.0),
                      decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 5),
                      child: InkWell(
                        onTap: () async {
                          getPairedDevices("bluetooth");
                          Navigator.pop(context);
                        },
                        child: Text(
                          'Bluetooth device',
                          style: textStyle18.apply(color: primaryTextColor),
                        ),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                  ],
                  Container(
                    width: MediaQuery.of(context).size.width,
                    padding: EdgeInsets.all(8.0),
                    decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 5),
                    child: InkWell(
                      onTap: () {
                        Navigator.pop(context);
                        Navigator.push(context, MaterialPageRoute(builder: (context) => AddPrinterPage(PrinterDevices(printerType: "network"))))
                            .then((value) {
                          if (value != null) {
                            addPrinter(value);
                          }
                        });
                      },
                      child: Text(
                        'Wi-fi device ',
                        style: textStyle18.apply(color: primaryTextColor),
                      ),
                    ),
                  ),
                  if (Platform.isAndroid || Platform.isWindows) ...[
                    SizedBox(
                      height: 10,
                    ),
                   if(Platform.isWindows)...[
                      Container(
                      width: MediaQuery.of(context).size.width,
                      padding: EdgeInsets.all(8.0),
                      decoration: boxdecoration(bgColor: backgroundColor, showShadow: true, radius: 5),
                      child: InkWell(
                        onTap: () {
                          getPairedDevices("usb");
                          Navigator.pop(context);
                        },
                        child: Text(
                          'USB',
                          style: textStyle18.apply(color: primaryTextColor),
                        ),
                      ),
                    )
                  ]
                  ]
                ],
              ),
            ),
          );
        });
  }

  /// To get paired bluetooth device
  getPairedDevices(String printerType) async {
    if (printerType == "bluetooth") {
      if (await bluetooth.isOn ?? false) {
        if (Platform.isIOS || await Geolocator.isLocationServiceEnabled()) {
          LoadingAlertWidget.onLoading(context);;
          await getBondedDevices();
          LoadingAlertWidget.onStopping();
          showBootomSheet(context,printerType: printerType);
        } else {
          printerUtilities.createAlertDialogLocationPermisssion(context);
        }
      } else {
        printerUtilities.bluetoothTurnOnAlertDialog(context);
      }
    } else {
      //LoadingAlertWidget.onLoading(context);;
      await getBondedDevices(type: printerType);
      //LoadingAlertWidget.onStopping();
      showBootomSheet(context,printerType: printerType);
    }
  }

  /// Bootom sheet to choose bluetooth device
  void showBootomSheet(BuildContext context, {String printerType = "bluetooth"}) async {
    showModalBottomSheet(
        // isScrollControlled: true,
        shape: const RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(36))),
        context: context,
        builder: (BuildContext context) {
          return Padding(
            padding: const EdgeInsets.all(10.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(left: 20),
                  //width: width,
                  child: TextWidget("Paired Devices:  ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal),
                ),
                SizedBox(
                  height: 5,
                ),
                Container(
                  child: devicesList.isEmpty
                      ? Center(
                          child: Text("No device configured"),
                        )
                      : Expanded(
                          child: ListView.builder(
                              itemCount: devicesList.length,
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              physics: AlwaysScrollableScrollPhysics(),
                              itemBuilder: (BuildContext context, int index) {
                                var item = devicesList[index];
                                return InkWell(
                                  onTap: () async {
                                    if (!isLock) {
                                      isLock = true;
                                      Navigator.pop(context);
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) => AddPrinterPage(PrinterDevices(
                                                  printerName: item.name, printerAddress: item.address, printerType: printerType)))).then((value) {
                                        if (value != null) {
                                          addPrinter(value);
                                        }
                                      });
                                      isLock = false;
                                    }
                                  },
                                  child: Column(
                                    children: <Widget>[
                                      Container(
                                        height: 55,
                                        padding: EdgeInsets.only(left: 10),
                                        alignment: Alignment.centerLeft,
                                        child: Row(
                                          children: <Widget>[
                                            Icon(Icons.print),
                                            SizedBox(width: 10),
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment: CrossAxisAlignment.start,
                                                mainAxisAlignment: MainAxisAlignment.center,
                                                children: <Widget>[
                                                  Text(devicesList[index].name ?? ''),
                                                  Text(devicesList[index].address ?? ""),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(),
                                    ],
                                  ),
                                );
                              }),
                        ),
                ),
              ],
            ),
          );
        });
  }

  Widget printerAddButton() {
    double width = MediaQuery.of(context).size.width;
    return InkWell(
        onTap: () async {
          if (!isLock) {
            isLock = true;
            // Platform.isWindows
            //     ? Navigator.push(context, MaterialPageRoute(builder: (context) => AddPrinterPage(PrinterDevices(printerType: "network"))))
            //         .then((value) {
            //         addPrinter(value);
            //       })
            //     :
            onTapAddButton();
            isLock = false;
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Printers", style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium)),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 20.0, top: 10.0),
            child: printerAddButton(),
          ),
        ],
      ),
      body: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          configuredPrinters.length > 0
              ? Container(
                  padding: EdgeInsets.only(left: 20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      SizedBox(
                        height: 10,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text("Multi Mobile Print"),
                          Checkbox(
                            value: isMultiPrinter,
                            onChanged: (value) {
                              setState(() {
                                if (configuredPrinters.length >= 2) {
                                  isMultiPrinter = true;
                                  showToast('Multi mobile print option cannot be false, when two or more devices are connected');
                                } else {
                                  isMultiPrinter = value!;
                                }
                                updateMultiPrintSetting();
                              });
                            },
                          ),
                        ],
                      ),
                      Container(
                        width: width,
                        child: TextWidget("Configured Devices:  ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal),
                      ),
                      ListView.builder(
                          itemCount: configuredPrinters.length,
                          shrinkWrap: true,
                          scrollDirection: Axis.vertical,
                          physics: AlwaysScrollableScrollPhysics(),
                          itemBuilder: (BuildContext context, int index) {
                            var item = configuredPrinters[index];
                            return ListTile(
                                leading: Icon(Icons.print),
                                title: Text(item.printerName ?? ""),
                                subtitle: Text(item.printerAddress ?? ""),
                                trailing: PopupMenuButton(
                                  itemBuilder: (BuildContext context) {
                                    return <PopupMenuEntry>[
                                      PopupMenuItem(
                                        child: ListTile(
                                          title: Text("Settings"),
                                          leading: Icon(Icons.settings),
                                          onTap: () async {
                                            Navigator.pop(context);
                                            Navigator.push(context, MaterialPageRoute(builder: (context) => AddPrinterPage(item))).then((value) {
                                              if (value != null) {
                                                setState(() {
                                                  configuredPrinters[index] = value;
                                                });
                                                printerUtilities.setPrinterInfo(configuredPrinters);
                                              }
                                            });
                                          },
                                        ),
                                      ),
                                      PopupMenuItem(
                                        child: ListTile(
                                          title: Text("UnPair"),
                                          leading: Icon(Icons.exit_to_app),
                                          onTap: () {
                                            Navigator.pop(context);
                                            createAlertDialogDisconnectBlutooth(context, index);
                                          },
                                        ),
                                      ),
                                    ];
                                  },
                                ));
                          }),
                    ],
                  ),
                )
              : Center(
                  child: SizedBox(
                      height: 100,
                      child: Padding(
                        padding: const EdgeInsets.all(10.0),
                        child: Text(
                          'Please connect a printer device ',
                          style: textStyle18.apply(color: primaryTextColor),
                        ),
                      )),
                ),
        ],
      ),
    );
  }
}
