import 'package:flutter/material.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/screens/settings/disount_settings/create_discount.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/discount_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/roles_permissions/permission_functions.dart';

//PRODUCT LIST VIEW CLASS
class ManageDiscount extends StatefulWidget {
  @override
  ManageDiscountState createState() => ManageDiscountState();
}

class ManageDiscountState extends State<ManageDiscount> {
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  DiscountDB discountDB = DiscountDB();
  CommonDB commonDB = CommonDB();
  FnUtilities function = FnUtilities();
  List<Discounts> discountList = [];
  bool isLock = false;
  var width;
  var height;

  //GET ALL DISCOUNT DETAILS
  getAllDiscount() async {
    final allRows = await discountDB.selectAllDiscount();
    setState(() {
      discountList.clear();
      allRows.forEach((row) => discountList.add(Discounts.fromMap(row)));
    });
  }

  //MESSAGE FOR PERMISSION
  // ignore: missing_return
  bool? showMessage() {
    showToast("You don't have permission.");
  }

  @override
  void initState() {
    getAllDiscount();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: BaseAppBar(
        title: Text("Discount", style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          SizedBox(
            height: 3,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    height: height / 1.3,
                    child: Column(
                      children: [
                        Container(
                          height: 40,
                          child: Card(
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Container(
                                  alignment: Alignment.centerLeft,
                                  width: width / 5,
                                  child: TextWidget("Mode", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Discount", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Min", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                                ),
                                Container(
                                  alignment: Alignment.centerRight,
                                  width: width / 4,
                                  child: TextWidget("Max", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: ListView.builder(
                                padding: EdgeInsets.only(right: 0.0),
                                scrollDirection: Axis.vertical,
                                itemCount: discountList.length,
                                shrinkWrap: true,
                                physics: ScrollPhysics(),
                                itemBuilder: (context, index) {
                                  //DELETE ALERT DIALOG FOR DISCOUNT
                                  var item = discountList[index];
                                  function.splitValueFromFormula(item);
                                  return GestureDetector(
                                    onLongPress: () {
                                       bool res = PermissionFunctions.checkPermission(150);
                                      if(PermissionFunctions.checkPermission(150)){
                                         showDialog(
                                              context: context,
                                              builder: (BuildContext context) {
                                                return AlertDialog(
                                                  content: Text("Are you sure, do you want to delete ?"),
                                                  actions: <Widget>[
                                                    GestureDetector(
                                                      onTap: () async {
                                                        if (!isLock) {
                                                          isLock = true;
                                                          final syncNo =
                                                              await commonDB.checkNonSyncCommonFunction("Discount", "discountID", item.discountID.toString(), "discountSync");
                                                          if (syncNo == 0) {
                                                            item.rowStatus = 2;
                                                            item.discountSync = 0;
                                                            await discountDB.updateDiscount(item);
                                                          } else {
                                                            await commonDB.permanentDeleteCommonFunction("Discount", "discountID", item.discountID.toString());
                                                          }

                                                          setState(() {
                                                            getAllDiscount();
                                                          });
                                                          Navigator.pop(context);
                                                          isLock = false;
                                                        }
                                                      },
                                                      child: Container(
                                                        alignment: Alignment.center,
                                                        height: 40,
                                                        width: width / 5,
                                                        child: TextWidget("Yes", textColor: secondaryTextColor, isCentered: true),
                                                        decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                                      ),
                                                    ),
                                                    GestureDetector(
                                                      onTap: () {
                                                        if (!isLock) {
                                                          isLock = true;
                                                          Navigator.pop(context);
                                                          isLock = false;
                                                        }
                                                      },
                                                      child: Container(
                                                        alignment: Alignment.center,
                                                        height: 40,
                                                        width: width / 5,
                                                        child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                                        decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                                      ),
                                                    ),
                                                  ],
                                                );
                                              });
                                         
                                      }else{
                                        showMessage();
                                      }
                                      // return res;
                                    },
                                    child: Card(
                                      margin: EdgeInsets.all(0.0),
                                      color: index % 2 == 0 ? secondaryTextColor : Colors.blue[50],
                                      child: Container(
                                        child: InkWell(
                                          onDoubleTap: () async {
                                            if (PermissionFunctions.checkPermission(149)) {
                                              Navigator.push(
                                                  context,
                                                  MaterialPageRoute(
                                                      builder: (context) => CreateDiscount(
                                                            discounts: item,
                                                          ))).then((value) async {
                                                await getAllDiscount();
                                              });
                                            } 
                                          },
                                          child: Column(
                                            children: <Widget>[
                                              Container(
                                                margin: EdgeInsets.only(top: 10, bottom: 10),
                                                child: Column(
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                      children: <Widget>[
                                                        Container(
                                                          width: width / 5,
                                                          alignment: Alignment.centerLeft,
                                                          child: TextWidget(item.mode == "A" ? "Auto" : "Coupon", fontSize: textSizeSMedium, isCentered: true),
                                                        ),
                                                        Container(
                                                          width: width / 4,
                                                          alignment: Alignment.centerRight,
                                                          child: TextWidget(item.discountOn == "P" ? item.discount! + "%" : item.discount ??"",
                                                              textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: false, isLongText: true),
                                                        ),
                                                        Container(
                                                          width: width / 4,
                                                          alignment: Alignment.centerRight,
                                                          child: TextWidget(double.parse(item.minDiscount ?? "0.0").toStringAsFixed(2),
                                                              textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                                        ),
                                                        Container(
                                                          width: width / 4,
                                                          alignment: Alignment.centerRight,
                                                          child: TextWidget(
                                                              item.maxDiscount == null || item.maxDiscount == "" ? "" : double.parse(item.maxDiscount ?? "0.0").toStringAsFixed(2),
                                                              textColor: primaryTextColor,
                                                              fontSize: textSizeMedium,
                                                              fontFamily: fontSemibold),
                                                        ),
                                                      ],
                                                    ),
                                                    // SingleChildScrollView(
                                                    //   scrollDirection:
                                                    //       Axis.horizontal,
                                                    //   child: Row(
                                                    //     mainAxisAlignment:
                                                    //         MainAxisAlignment
                                                    //             .spaceBetween,
                                                    //     children: <Widget>[
                                                    //       TextWidget(
                                                    //           "Formula: " +
                                                    //               item.formula,
                                                    //           fontSize:
                                                    //               textSizeSMedium,
                                                    //           isCentered:
                                                    //               false),
                                                    //       // Container(
                                                    //       //   width: width / 6,
                                                    //       //   alignment:
                                                    //       //       Alignment.center,
                                                    //       //   child:
                                                    //       //       item.discountActive ==
                                                    //       //               1
                                                    //       //           ? Icon(
                                                    //       //               Icons
                                                    //       //                   .check,
                                                    //       //               color: Colors
                                                    //       //                   .green,
                                                    //       //             )
                                                    //       //           : Icon(
                                                    //       //               Icons
                                                    //       //                   .remove,
                                                    //       //               color: Colors
                                                    //       //                   .red,
                                                    //       //             ),
                                                    //       // ),
                                                    //     ],
                                                    //   ),
                                                    // ),
                                                  ],
                                                ),
                                              ),
                                              Divider(height: 0.5, color: t5ViewColor)
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  );
                                }),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
