import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:uuid/uuid.dart';
import '../../../../main.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/dbutils/discount_db_script.dart';
import '../../../utils/dbutils/product_key_db_script.dart';
import '../../../utils/common_widgets/header_footer.dart';

// ignore: must_be_immutable
class CreateDiscount extends StatefulWidget {
  Discounts? discounts;
  CreateDiscount({this.discounts});
  @override
  _CreateDiscountState createState() => _CreateDiscountState(this.discounts);
}

class _CreateDiscountState extends State<CreateDiscount> {
  Discounts? discounts;
  _CreateDiscountState(this.discounts);
  final dbHelper = DatabaseHelper.instance;
  Validator validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  DiscountDB discountDB = DiscountDB();
  CommonDB commonDB = CommonDB();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  final couponNameController = TextEditingController();
  final discountController = TextEditingController();
  final minLimitController = TextEditingController();
  final maxLimitController = TextEditingController();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  List<ProductKey> productKeyList = [];
  List<ProductKey> selectedProductKeyList = [];
  bool isLock = false;
  String? selectedDiscountType;
  String? selectedDiscountMode;
  String? selectedDiscountOn;

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked;
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      DateTime currentDate = DateTime.now();
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //GET Key
  queryAllProductKeys() async {
    for (ProductKey item in ListUtility.productKeyList) {
      item.isActive = false;
      productKeyList.add(item);
    }
    await assignValueForUpdate();
  }

  clearTextFiled() {
    discountController.clear();
    minLimitController.clear();
    maxLimitController.clear();
    fromDate = DateTime.now();
    toDate = DateTime.now();
    selectedProductKeyList.clear();
    selectedDiscountType = null;
    selectedDiscountMode = null;
    selectedDiscountOn = null;
  }

  categoryString() {
    String category = "";
    for (ProductKey p in selectedProductKeyList) {
      category += "${p.productKeyId},";
    }
    return category;
  }

  onPressDiscount() async {
    Discounts model = Discounts();
    model.type = selectedDiscountType == "Sales Wise" ? "Sales" : "Product";
    model.mode = selectedDiscountMode;
    model.discountOn = selectedDiscountOn;
    model.categoryID = await categoryString();
    model.discount = discountController.text;
    model.minDiscount = minLimitController.text;
    model.maxDiscount = maxLimitController.text != "" ? maxLimitController.text : null;
    model.fromDate = fromDate;
    model.toDate = toDate;
    model.formula = await fnUtilities.createDiscountFormula(model);
    model.couponName = couponNameController.text != "" ? couponNameController.text : null;
    model.discountActive = 1;
    model.workspaceID = MyApp.activeWorkspace.workspaceId;
    model.discountSync = 0;
    if (discounts == null) {
      var uuid = const Uuid();
      model.discountID = uuid.v4().toString();
      model.createdBy = MyApp.activeUser.username;
      model.createdDate = DateTime.now().toString();
      model.rowStatus = 0;
      await discountDB.insertDiscount(model);
    } else {
      model.discountID = discounts?.discountID;
      model.rowStatus =
          await commonDB.checkNonSyncCommonFunction("Discount", "discountID", discounts!.discountID.toString(), "discountSync") == 0 ? 1 : 0;
      await discountDB.updateDiscount(model);
    }
    await clearTextFiled();
    Navigator.pop(context);
  }

  categoryList() {
    var slitCategory = discounts?.categoryID?.split(",");
    for (String s in slitCategory!) {
      try {
        ProductKey key = productKeyList.firstWhere((element) => element.productKeyId == s);
        if (key != null) {
          productKeyList.firstWhere((element) => element.productKeyId == s).isActive = true;
          key.isActive = true;
          selectedProductKeyList.add(key);
        }
      } catch (ex) {}
    }
  }

  assignValueForUpdate() async {
    if (discounts != null) {
      if (discounts?.categoryID != null) {
        await categoryList();
      }
      selectedDiscountType = discounts?.type == "S" ? "Sales Wise" : "Product Wise";
      selectedDiscountMode = discounts?.mode == "A" ? "Auto" : "Coupon";
      selectedDiscountOn = discounts?.discountOn == "F" ? "Fixed Amount" : "Percentage";
      discountController.text = discounts?.discount ?? "";
      minLimitController.text = discounts?.minDiscount ?? "";
      maxLimitController.text = (discounts?.maxDiscount == null ? "" : discounts?.maxDiscount) ?? "";
      fromDate = discounts!.fromDate!;
      toDate = discounts!.toDate!;
      couponNameController.text = (discounts?.couponName == null ? "" : discounts?.couponName)!;
    }
  }

  @override
  void initState() {
    queryAllProductKeys();
    super.initState();
  }

  //Category DIALOG
  categoryDialog(BuildContext context) async {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
                contentPadding: EdgeInsets.all(10),
                scrollable: true,
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.maxFinite,
                      height: 250,
                      child: ListView.builder(
                          itemCount: productKeyList.length,
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemBuilder: (BuildContext context, int index) {
                            var item = productKeyList[index];
                            return Container(
                              margin: const EdgeInsets.all(0),
                              padding: EdgeInsets.zero,
                              decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Container(
                                    width: width / 1.5,
                                    child: new CheckboxListTile(
                                        dense: false,
                                        controlAffinity: ListTileControlAffinity.leading,
                                        contentPadding: EdgeInsets.zero,
                                        value: item.isActive,
                                        title: Row(
                                          children: [
                                            Container(
                                              width: width / 2,
                                              alignment: Alignment.centerLeft,
                                              child: TextWidget(item.keywordName ?? "",
                                                  textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isLongText: true),
                                            ),
                                          ],
                                        ),
                                        onChanged: (bool? isValue) {
                                          setState(() {
                                            item.isActive = isValue;
                                          });
                                        }),
                                  ),
                                ],
                              ),
                            );
                          }),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              setState(() {
                                selectedProductKeyList.clear();
                                selectedProductKeyList.addAll(productKeyList.where((element) => element.isActive == true));
                                if (selectedProductKeyList.length <= 0) {
                                  showToast("Please select category");
                                }
                              });
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ));
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text(
          discounts == null ? "Create Discount" : "Update Discount",
          style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Container(
                          width: width / 1.8,
                          child: Text(
                            'Discount Type',
                            style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                          ),
                        ),
                        DropdownButtonHideUnderline(
                          child: new DropdownButton<String>(
                            hint: Text("Type"),
                            value: selectedDiscountType,
                            items: <String>['Sales Wise', 'Product Wise'].map((String value) {
                              return new DropdownMenuItem<String>(
                                value: value,
                                child: new Text(value),
                              );
                            }).toList(),
                            onChanged: (String? value) async {
                              if (!isLock) {
                                isLock = true;
                                setState(() {
                                  selectedDiscountType = value;
                                  if (selectedDiscountType == "Sales Wise") {
                                    selectedProductKeyList.clear();
                                  }
                                });
                                isLock = false;
                              }
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  divider(),
                  selectedDiscountType != null
                      ? Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Container(
                                    width: width / 1.8,
                                    child: Text(
                                      'Discount Mode',
                                      style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                    ),
                                  ),
                                  DropdownButtonHideUnderline(
                                    child: new DropdownButton<String>(
                                      hint: Text("Mode"),
                                      value: selectedDiscountMode,
                                      items: <String>['Auto', 'Coupon'].map((String value) {
                                        return new DropdownMenuItem<String>(
                                          value: value,
                                          child: new Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? value) async {
                                        if (!isLock) {
                                          isLock = true;
                                          setState(() {
                                            selectedDiscountMode = value;
                                          });
                                          isLock = false;
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            divider(),
                          ],
                        )
                      : Container(),
                  selectedDiscountMode != null
                      ? Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(8.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: <Widget>[
                                  Container(
                                    width: width / 1.8,
                                    child: Text(
                                      'Discount On',
                                      style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                    ),
                                  ),
                                  DropdownButtonHideUnderline(
                                    child: new DropdownButton<String>(
                                      hint: Text("Discount"),
                                      value: selectedDiscountOn,
                                      items: <String>['Percentage', 'Fixed Amount'].map((String value) {
                                        return new DropdownMenuItem<String>(
                                          value: value,
                                          child: new Text(value),
                                        );
                                      }).toList(),
                                      onChanged: (String? value) async {
                                        if (!isLock) {
                                          isLock = true;
                                          setState(() {
                                            selectedDiscountOn = value;
                                          });
                                          isLock = false;
                                        }
                                      },
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            divider(),
                            selectedDiscountOn != null
                                ? Form(
                                    key: validator.formkey,
                                    child: Column(
                                      children: [
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            selectedDiscountMode == "Coupon"
                                                ? SizedBox(
                                                    width: width / 2.2,
                                                    child: EditText(
                                                      text: "Coupon Name",
                                                      isPassword: false,
                                                      onTapOutside: (event) {
                                                        FocusManager.instance.primaryFocus?.unfocus();
                                                      },
                                                      mController: couponNameController,
                                                      inputType: TextInputType.text,
                                                      inputAction: TextInputAction.next,
                                                      validator: validator.validateTextField,
                                                    ),
                                                  )
                                                : Container(),
                                            selectedDiscountType == "Product Wise"
                                                ? SizedBox(
                                                    width: width / 2.2,
                                                    child: InkWell(
                                                      onTap: () async {
                                                        FocusScope.of(context).requestFocus(FocusNode());
                                                        await categoryDialog(context);
                                                      },
                                                      child: Card(
                                                        elevation: 2,
                                                        child: Padding(
                                                          padding: const EdgeInsets.symmetric(vertical: 14, horizontal: 20),
                                                          child: Row(
                                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                            children: [
                                                              Text(
                                                                "Select Category",
                                                                style: primaryTextStyle(),
                                                              ),
                                                            ],
                                                          ),
                                                        ),
                                                      ),
                                                    ),
                                                  )
                                                : Container(),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        selectedProductKeyList.length > 0
                                            ? Column(
                                                children: [
                                                  Text(
                                                    "Selected Category list",
                                                    style: TextStyle(fontWeight: FontWeight.bold),
                                                  ),
                                                  SingleChildScrollView(
                                                    child: GridView.builder(
                                                        padding: EdgeInsets.symmetric(horizontal: 5, vertical: 5),
                                                        scrollDirection: Axis.vertical,
                                                        physics: ScrollPhysics(),
                                                        itemCount: selectedProductKeyList.length,
                                                        shrinkWrap: true,
                                                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                                            crossAxisCount: 3, crossAxisSpacing: 2, mainAxisSpacing: 2, childAspectRatio: 4),
                                                        itemBuilder: (BuildContext context, int index) {
                                                          var item = selectedProductKeyList[index];
                                                          return Container(
                                                            padding: EdgeInsets.only(left: 5),
                                                            child: TextWidget(item.keywordName ?? "",
                                                                textColor: primaryTextColor,
                                                                fontSize: textSizeMedium,
                                                                fontFamily: fontSemibold,
                                                                isLongText: true),
                                                          );
                                                        }),
                                                  ),
                                                ],
                                              )
                                            : Container(),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            SizedBox(
                                              width: width / 2.2,
                                              child: EditText(
                                                text: selectedDiscountOn == "Percentage" ? "Percentage (%)" : "Amount (Rs)",
                                                isPassword: false,
                                                mController: discountController,
                                                inputType: TextInputType.number,
                                                onTapOutside: (event) {
                                                  FocusManager.instance.primaryFocus?.unfocus();
                                                },
                                                inputFormate: [
                                                  FilteringTextInputFormatter.digitsOnly,
                                                  FilteringTextInputFormatter.deny(RegExp(r"\s")),
                                                  // PhoneInputFormatter(),
                                                  // LengthLimitingTextInputFormatter(10)
                                                ],
                                                // inputFormate: [
                                                //   FilteringTextInputFormatter
                                                //       .allow(RegExp(r'[0-9.]')),
                                                // ],
                                                onChange: (value) {
                                                  setState(() {
                                                    // ignore: unnecessary_statements
                                                    value;
                                                  });
                                                },
                                                validator: validator.validateAmount,
                                                inputAction: TextInputAction.next,
                                              ),
                                            ),
                                            Container(
                                              width: width / 2.2,
                                              alignment: Alignment.center,
                                              child: Text(
                                                selectedDiscountOn == "Percentage" ? "Percentage" : "Rupees",
                                                style: TextStyle(fontWeight: FontWeight.bold),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            SizedBox(
                                              width: width / 2.2,
                                              child: EditText(
                                                text: "Min Limit (Rs)",
                                                isPassword: false,
                                                mController: minLimitController,
                                                inputType: TextInputType.number,
                                                inputFormate: [
                                                  FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                                                ],
                                                onChange: (value) {
                                                  setState(() {
                                                    // ignore: unnecessary_statements
                                                    value;
                                                  });
                                                },
                                                validator: validator.validateMinValue,
                                                inputAction: TextInputAction.next,
                                              ),
                                            ),
                                            SizedBox(
                                              width: width / 2.2,
                                              child: selectedDiscountType == "Sales Wise" && selectedDiscountOn == "Fixed Amount"
                                                  ? Container()
                                                  : EditText(
                                                      text: "Max Limit (Rs)",
                                                      isPassword: false,
                                                      mController: maxLimitController,
                                                      inputType: TextInputType.number,
                                                      inputFormate: [
                                                        FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                                                      ],
                                                      onChange: (value) {
                                                        setState(() {
                                                          // ignore: unnecessary_statements
                                                          value;
                                                        });
                                                      },
                                                      // validator: validator.validateMaxValue,
                                                      validator: (String? value) {
                                                        {
                                                          if ((int.tryParse(value ?? "0") ?? 0) <= 0 && value!.isNotEmpty) {
                                                            return "Max must be greatter than zero";
                                                          } else if ((int.tryParse(value ?? "0") ?? 0) <= int.tryParse(minLimitController.text)!) {
                                                            return "Max limit must be greatter than min limit";
                                                          } else {
                                                            return null;
                                                          }
                                                        }
                                                      },
                                                      inputAction: TextInputAction.next,
                                                    ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            InkWell(
                                              onTap: () {
                                                FocusScope.of(context).requestFocus(FocusNode());
                                                selectDate(context, 'fromDate');
                                              },
                                              child: Card(
                                                elevation: 2,
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 25),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Text(
                                                        "${fnUtilities.convertDate(fromDate.toString())}",
                                                        style: secondaryTextStyle(),
                                                      ),
                                                      SizedBox(
                                                        width: 15,
                                                      ),
                                                      Icon(Icons.calendar_today)
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                            InkWell(
                                              onTap: () {
                                                FocusScope.of(context).requestFocus(FocusNode());
                                                selectDate(context, 'toDate');
                                              },
                                              child: Card(
                                                elevation: 2,
                                                child: Padding(
                                                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 25),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      Text(
                                                        fnUtilities.convertDate(toDate.toString()),
                                                        style: secondaryTextStyle(),
                                                      ),
                                                      SizedBox(
                                                        width: 15,
                                                      ),
                                                      Icon(Icons.calendar_today)
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        // Column(
                                        //   children: [
                                        //     Text(
                                        //       "Formula:",
                                        //       style: TextStyle(
                                        //           fontWeight: FontWeight.bold),
                                        //     ),
                                        //     SingleChildScrollView(
                                        //       scrollDirection: Axis.horizontal,
                                        //       child: Row(
                                        //         children: [
                                        //           Text(selectedDiscountType ==
                                        //                   "Sales Wise"
                                        //               ? "S_"
                                        //               : "P_"),
                                        //           Text(selectedDiscountMode ==
                                        //                   "Auto"
                                        //               ? "A_"
                                        //               : "C_"),
                                        //           Text(selectedDiscountOn ==
                                        //                   "Percentage"
                                        //               ? "P_"
                                        //               : "F_"),
                                        //           Text(
                                        //               discountController.text !=
                                        //                       ""
                                        //                   ? discountController
                                        //                           .text +
                                        //                       "_"
                                        //                   : ""),
                                        //           Text(
                                        //               minLimitController.text !=
                                        //                       ""
                                        //                   ? minLimitController
                                        //                           .text +
                                        //                       "_"
                                        //                   : ""),
                                        //           Text(
                                        //               maxLimitController.text !=
                                        //                       ""
                                        //                   ? maxLimitController
                                        //                           .text +
                                        //                       "_"
                                        //                   : ""),
                                        //           Text(fromDate.toString() +
                                        //               "_"),
                                        //           Text(toDate.toString()),
                                        //         ],
                                        //       ),
                                        //     ),
                                        //   ],
                                        // ),
                                        SizedBox(
                                          height: 10,
                                        ),
                                        InkWell(
                                          onTap: () async {
                                            if (!isLock) {
                                              isLock = true;
                                              if (selectedProductKeyList.length > 0 || selectedDiscountType == "Sales Wise") {
                                                validator.validate();
                                                if (validator.validate() ?? false) {
                                                  await onPressDiscount();
                                                }
                                              } else {
                                                showToast("Please select category");
                                              }
                                              isLock = false;
                                            }
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: 45,
                                            width: width / 1.1,
                                            child: TextWidget(discounts == null ? "Create Discount" : "Update Discount",
                                                textColor: secondaryTextColor, isCentered: true),
                                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                          ),
                                        ),
                                      ],
                                    ),
                                  )
                                : Container()
                          ],
                        )
                      : Container(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
