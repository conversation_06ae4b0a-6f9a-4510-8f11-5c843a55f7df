// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/screens/settings/disount_settings/create_discount.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';
import '../../../main.dart';
import '../../../utils/common_widgets/text_widget.dart';
import '../../../utils/constants/strings_keys.dart';
import '../../../utils/common_widgets/header_footer.dart';
import '../../../utils/constants/styles.dart';
import '../../../utils/roles_permissions/permission_functions.dart';
import 'manage_discount.dart';

class DiscountSettings extends StatefulWidget {
  @override
  _DiscountSettingsState createState() => _DiscountSettingsState();
}

class _DiscountSettingsState extends State<DiscountSettings> {
  FnUtilities fnUtilities = new FnUtilities();
  bool isLock = false;
  bool isDiscount = false;
  String? selectedDiscountType;

  initSettings() async {
    setState(() {
      isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
      var EnableDiscountType = fnUtilities.workSpaceSetValues(key_discountType);
      if (EnableDiscountType != null) {
        selectedDiscountType =
            EnableDiscountType == "Sales" ? "Sales Wise" : "Product Wise";
      }
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Discount Settings",
        style: TextStyle(color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),
            ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                        padding: const EdgeInsets.symmetric(
                            horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width / 1.5,
                              child: Text(
                                'Enable Discount',
                                style: TextStyle(
                                    color: primaryTextColor,
                                    fontSize: textSizeMedium,
                                    fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: isDiscount,
                              onChanged: (value) async {
                                if(PermissionFunctions.checkPermission(146)){
                                if (!isLock) {
                                  isLock = true;
                                  setState(() {
                                    isDiscount = value;
                                  });
                                  WorkspaceSettings settings =
                                      new WorkspaceSettings();
                                  settings.settingKey = "EnableDiscount";
                                  settings.settingValue = value ? "1" : "0";
                                  settings.sync = 0;
                                  settings.workspaceId =
                                      MyApp.activeWorkspace.workspaceId;
                                  await fnUtilities
                                      .updateSettingDetails(settings);
                                  WorkspaceSettings setting =
                                      new WorkspaceSettings();
                                  setting.settingKey = "DiscountType";
                                  setting.settingValue = "Sales";
                                  setting.sync = 0;
                                  setting.workspaceId =
                                      MyApp.activeWorkspace.workspaceId;
                                  await fnUtilities
                                      .updateSettingDetails(setting);
                                  isLock = false;
                                }
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      isDiscount
                          ? Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.all(8.0),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Container(
                                        width: width / 1.8,
                                        child: Text(
                                          'Discount Type',
                                          style: TextStyle(
                                              color: primaryTextColor,
                                              fontSize: textSizeMedium,
                                              fontFamily: fontSemibold),
                                        ),
                                      ),
                                      DropdownButtonHideUnderline(
                                        child: new DropdownButton<String>(
                                          hint: Text("Type"),
                                          value: selectedDiscountType,
                                          items: <String>[
                                            'Sales Wise',
                                            // 'Product Wise'
                                          ].map((String value) {
                                            return new DropdownMenuItem<String>(
                                              value: value,
                                              child: new Text(value),
                                            );
                                          }).toList(),
                                          onChanged: (String? val) async {
                                            setState(() {
                                              selectedDiscountType = val;
                                            });
                                            WorkspaceSettings settings =
                                                new WorkspaceSettings();
                                            settings.settingKey =
                                                "DiscountType";
                                            settings.settingValue =
                                                selectedDiscountType ==
                                                        "Sales Wise"
                                                    ? "Sales"
                                                    : "Product";
                                            settings.sync = 0;
                                            settings.workspaceId = MyApp
                                                .activeWorkspace.workspaceId;
                                            await fnUtilities
                                                .updateSettingDetails(settings);
                                          },
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceAround,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                         if(PermissionFunctions.checkPermission(147)){
                                        if (!isLock) {
                                          isLock = true;
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      CreateDiscount()));
                                          isLock = false;
                                        }
                                         }
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 45,
                                        width: width / 3,
                                        child: TextWidget("Create Discount",
                                            textColor: secondaryTextColor,
                                            isCentered: true),
                                        decoration: boxDecoration(
                                            bgColor: buttonThemeColor,
                                            radius: 8.0),
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                         if(PermissionFunctions.checkPermission(148)){
                                        if (!isLock) {
                                          isLock = true;
                                          Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                  builder: (context) =>
                                                      ManageDiscount()));
                                          isLock = false;
                                        }
                                         }
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 45,
                                        width: width / 3,
                                        child: TextWidget("View Discount",
                                            textColor: secondaryTextColor,
                                            isCentered: true),
                                        decoration: boxDecoration(
                                            bgColor: buttonThemeColor,
                                            radius: 8.0),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            )
                          : Container(),
                      divider(),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
