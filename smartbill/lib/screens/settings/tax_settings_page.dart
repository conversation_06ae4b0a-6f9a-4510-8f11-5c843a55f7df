// ignore_for_file: non_constant_identifier_names, unused_local_variable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class TaxSettingsPage extends StatefulWidget {
  static var tag = "";

  @override
  TaxSettingsPageState createState() => TaxSettingsPageState();
}

class TaxSettingsPageState extends State<TaxSettingsPage> {
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  Validator validator = new Validator();
  final gstController = TextEditingController();
  double width = 0.0;
  double height = 0.0;
  bool? includedTax;
  bool addTax = false;
  String? applyTaxFor;
  bool isLock = false;

  //GET TAX DETAILS
  initSettings() async {
    var TaxEnableValue = fnUtilities.workSpaceSetValues(key_taxEnable);
    var ApplyTaxForValue = fnUtilities.workSpaceSetValues(key_applyTaxFor);
    setState(() {
      includedTax = fnUtilities.workSpaceSetValues(key_taxMode) == "1";

      if (TaxEnableValue == "1") {
        addTax = true;
        if (ApplyTaxForValue == null) {
          updateApplyForTax();
        }
      } else {
        addTax = false;
      }
      applyTaxFor = ApplyTaxForValue ?? 'Specific';
      gstController.text =
          fnUtilities.workSpaceSetValues(key_overAllTax) ?? '0';
    });
  }

  updateApplyForTax() {
    settingsDetail.settingKey = "ApplyTaxFor";
    settingsDetail.settingValue = 'Specific';
    settingsDetail.sync = 0;
    fnUtilities.updateSettingDetails(settingsDetail);
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Tax Settings",
            style: TextStyle(color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Form(
              key: validator.formkey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(top: 0),
                    padding: EdgeInsets.only(top: 0),
                    alignment: Alignment.topCenter,
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(24),
                            topRight: Radius.circular(24))),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Padding(
                          padding: EdgeInsets.all(15.0),
                          child: Column(
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Container(
                                      width: width / 1.5,
                                      child: Text(
                                        'Enable Tax',
                                        style: TextStyle(
                                            color: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                    ),
                                    Switch(
                                      value: addTax,
                                      onChanged: (value) {
                                        if (PermissionFunctions.checkPermission(142)) {
                                          setState(() {
                                            addTax = value;
                                            settingsDetail.settingKey =
                                                "TaxEnable";
                                            String addTaxEnabel;
                                            addTax == true
                                                ? addTaxEnabel = "1"
                                                : addTaxEnabel = "0";
                                            settingsDetail.settingValue =
                                                addTaxEnabel;
                                            settingsDetail.sync = 0;
                                            fnUtilities.updateSettingDetails(
                                                settingsDetail);
                                          });
                                        }
                                      },
                                      activeTrackColor: buttonThemeColor,
                                      activeColor: buttonThemeColor,
                                    ),
                                  ],
                                ),
                              ),
                              divider(),
                              addTax == true
                                  ? Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Container(
                                                width: width / 1.5,
                                                child: Text(
                                                  'Mode of Tax',
                                                  style: TextStyle(
                                                      color: primaryTextColor,
                                                      fontSize: textSizeMedium,
                                                      fontFamily: fontSemibold),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children: [
                                            RadioListTile(
                                              groupValue: includedTax,
                                              value: true,
                                              title: Text('Include Tax'),
                                              onChanged: (val) {
                                                if (PermissionFunctions.checkPermission(143)) {
                                                  setState(() {
                                                    includedTax = true;
                                                    settingsDetail.settingKey =
                                                        "TaxMode";
                                                    String addTaxMode = "1";
                                                    settingsDetail
                                                            .settingValue =
                                                        addTaxMode;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities
                                                        .updateSettingDetails(
                                                            settingsDetail);
                                                  });
                                                }
                                              },
                                            ),
                                            RadioListTile(
                                              groupValue: includedTax,
                                              value: false,
                                              title: Text('Exclude Tax'),
                                              onChanged: (val) {
                                                if (PermissionFunctions.checkPermission(143)) {
                                                  setState(() {
                                                    includedTax = false;
                                                    settingsDetail.settingKey =
                                                        "TaxMode";
                                                    String addTaxMode = "0";
                                                    settingsDetail
                                                            .settingValue =
                                                        addTaxMode;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities
                                                        .updateSettingDetails(
                                                            settingsDetail);
                                                  });
                                                } else {
                                                  showToast(
                                                      "You don't have a permission");
                                                }
                                              },
                                            )
                                          ],
                                        ),
                                      ],
                                    )
                                  : Container(),
                              divider(),
                              addTax == true
                                  ? Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Container(
                                                width: width / 1.5,
                                                child: Text(
                                                  'Apply Tax For',
                                                  style: TextStyle(
                                                      color: primaryTextColor,
                                                      fontSize: textSizeMedium,
                                                      fontFamily: fontSemibold),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children: [
                                            RadioListTile(
                                              groupValue: applyTaxFor,
                                              value: "All",
                                              title: Text('All Products'),
                                              onChanged: (val) {
                                                if ( PermissionFunctions.checkPermission(143)) {
                                                  setState(() {
                                                    applyTaxFor = val;
                                                    settingsDetail.settingKey =
                                                        "ApplyTaxFor";
                                                    settingsDetail
                                                            .settingValue =
                                                        applyTaxFor;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities
                                                        .updateSettingDetails(
                                                            settingsDetail);
                                                  });
                                                }
                                              },
                                            ),
                                            RadioListTile(
                                              groupValue: applyTaxFor,
                                              value: 'Specific',
                                              title: Text('Specific Products'),
                                              onChanged: (val) {
                                                if ( PermissionFunctions.checkPermission(143,)) {
                                                  setState(() {
                                                    applyTaxFor = val;
                                                    settingsDetail.settingKey =
                                                        "ApplyTaxFor";
                                                    settingsDetail
                                                            .settingValue =
                                                        applyTaxFor;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities
                                                        .updateSettingDetails(
                                                            settingsDetail);
                                                  });
                                                }
                                              },
                                            )
                                          ],
                                        ),
                                      ],
                                    )
                                  : Container(),
                              divider(),
                              addTax && applyTaxFor == "All"
                                  ? Column(
                                      children: [
                                        SizedBox(
                                          height: 20,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceAround,
                                          children: [
                                            Container(
                                              width: width / 1.8,
                                              child: EditText(
                                                text: "GST (%)",
                                                isPassword: false,
                                                mController: gstController,
                                                inputType: TextInputType.number,
                                                inputFormate: [
                                                  FilteringTextInputFormatter
                                                      .allow(RegExp(r'[0-9.]')),
                                                ],
                                                validator:
                                                    validator.validateTextField,
                                                inputAction:
                                                    TextInputAction.done,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 5,
                                            ),
                                            InkWell(
                                              onTap: () async {
                                                if(PermissionFunctions.checkPermission(143)){
                                                if (!isLock) {
                                                  isLock = true;
                                                  if (validator.validate() ?? false) {
                                                    settingsDetail.settingKey =
                                                        "OverAllTax";
                                                    settingsDetail
                                                            .settingValue =
                                                        gstController.text;
                                                    settingsDetail.sync = 0;
                                                    await fnUtilities
                                                        .updateSettingDetails(
                                                            settingsDetail);
                                                    showToast(
                                                        "Tax updated successfully");
                                                    FocusScope.of(context)
                                                        .unfocus();
                                                  }
                                                }
                                                isLock = false;
                                                }
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: 50,
                                                width: width / 4,
                                                child: TextWidget("Update",
                                                    textColor: secondaryTextColor,
                                                    isCentered: true),
                                                decoration: boxDecoration(
                                                    bgColor: buttonThemeColor,
                                                    radius: 8.0),
                                              ),
                                            )
                                          ],
                                        ),
                                      ],
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
