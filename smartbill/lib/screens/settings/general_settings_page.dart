// ignore_for_file: non_constant_identifier_names

import 'dart:convert';

import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:smartbill/model/check_user_password.dart';
import 'package:smartbill/model/checkbox.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/workspace_settings.dart'; 
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/custom_dropdown.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import '../../Repositories/workspace_repository.dart';
import '../../main.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class GeneralSettingsPage extends StatefulWidget {
  @override
  _GeneralSettingsPageState createState() => _GeneralSettingsPageState();
}

class _GeneralSettingsPageState extends State<GeneralSettingsPage> {
  final dbHelper = DatabaseHelper.instance;
  Validator validator = new Validator();
  FnUtilities fnUtilities = new FnUtilities();
  final passwordController = new TextEditingController();
  bool isLock = false;
  bool resetSalesNo = false;
  bool enableTableOrder = false;
  bool dynamicQuantity = false;
  bool kotPrint = false;
  bool twoDays = false;
  bool kotIndividualPrint = false;
  double width = 0.0;
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  CommonDB commonDB = CommonDB();
  bool isPaymentType = false;
  // bool enableInventory = false;
  // String inventoryType;
  bool allowNegativeSale = false;
  bool isReturnAmount = true;
  bool isEatAndPay = false;
  bool successDialogOnSales = false;
  bool isCustomer = false;
  bool alwaysOnDisplay = false;
  bool isAutoSync = false;
  bool imagePrint = false;
  bool additionalKOTButton = false;
  List<PaymentType> paymentList = [];
  bool showImages = true;
  bool showTodaySales = false;
  bool showEmployeeNameInKOT = false;
  bool enableOnlineOrder = false;
  bool enableExpense = false;
  bool enablePurchase = false;
  bool enableSalesBillPad = false;
  bool isCustomerNameMandatory = false;
  bool enableSalesCategory = false;
  bool enableShiftbasedSales = false;
  bool splitInvoiceByPayment = false;
  bool storeCustomerDetails = false;
  bool isAdvanceOrder = false;

//GET PANEL DETAILS FROM DB
  initSettings() async {
    setState(() {
      ///Worksapce Settings
      resetSalesNo = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
      enableTableOrder = fnUtilities.workSpaceSetValues(key_enableTableOrders) == "1";
      kotPrint = fnUtilities.workSpaceSetValues(key_kotEnable) == "1";
      kotIndividualPrint = fnUtilities.workSpaceSetValues(key_kotIndividualEnable) == "1";
      dynamicQuantity = fnUtilities.workSpaceSetValues(key_dynamicQuantity) == "1";
      isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      // enableInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1";
      // inventoryType = fnUtilities.workSpaceSetValues(key_inventoryType);
      allowNegativeSale = fnUtilities.workSpaceSetValues(key_allowNegativeSales) == "1";
      isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
      isEatAndPay = fnUtilities.workSpaceSetValues(key_eatAndPay) == "1";
      isCustomer = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
      isCustomerNameMandatory = fnUtilities.workSpaceSetValues(key_isCustomerNameMandatory) == "1";
      successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
      showEmployeeNameInKOT = fnUtilities.workSpaceSetValues(showEmployeeNamesInKOT) == "1";
      showTodaySales = fnUtilities.workSpaceSetValues(showTodaySalesKey) == "1";
      splitInvoiceByPayment = fnUtilities.workSpaceSetValues(splitInvoiceByPaymentKey) == "1";
      imagePrint = fnUtilities.workSpaceSetValues(key_imagePrint) == "1";
      additionalKOTButton = fnUtilities.workSpaceSetValues(key_additionalKOTButton) == "1";
      enableOnlineOrder = fnUtilities.workSpaceSetValues(enableOnlineOrderKey) == "1";
      enableExpense = fnUtilities.workSpaceSetValues(key_expense) == "1";
      enablePurchase = fnUtilities.workSpaceSetValues(key_purchase) == "1";
      enableSalesBillPad = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";
      enableSalesCategory = fnUtilities.workSpaceSetValues(key_sales_category) == "1";
      enableShiftbasedSales = fnUtilities.workSpaceSetValues(key_shiftBasedSales) == "1";
      storeCustomerDetails = fnUtilities.workSpaceSetValues(key_store_customerDetails) == "1";
      isAdvanceOrder = fnUtilities.workSpaceSetValues(key_advanceOrder) == "1";
      var isShowImageInSalesPage = fnUtilities.workSpaceSetValues(key_showproductImages);
      var InvoiceExtension = fnUtilities.workSpaceSetValues(key_invoiceExtension);
      var InvoicePaymentType = fnUtilities.workSpaceSetValues(key_invoicePaymentType);

      // if (inventoryType == "All") {
      //   inventoryType = "All";
      // } else {
      //   inventoryType = "Specific";
      // }
      if (isShowImageInSalesPage == null) {
        showImages = true;
        uppdateShowImagesSetting(true);
      } else {
        showImages = isShowImageInSalesPage == "1" ? true : false;
      }
      if (InvoiceExtension != null) {
        invoiceDropDownModel = invoiceDropDownlist.where((element) => element.id == InvoiceExtension).first;
      }
      if (InvoicePaymentType != null) {
        selectedInvoicePaymentType = json.decode(InvoicePaymentType).cast<String>();
      }
    });
  }

  onChangeEnableCustomerBasedSales(bool value, String settingKey) async {
    setState(() {
      if (isAdvanceOrder) {
        showToast("Unable to disable while using the advanced order feature");
      } else {
        // Update the respective setting details
        if (settingKey == key_isCustomer) {
          isCustomer = value;
        } else if (settingKey == key_isCustomerNameMandatory) {
          isCustomerNameMandatory = value;
        }

        // Update the settings detail object
        settingsDetail.settingKey = settingKey;
        settingsDetail.settingValue = value ? "1" : "0";
        settingsDetail.sync = 0;
        settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;

        // Update the setting details
        fnUtilities.updateSettingDetails(settingsDetail);
      }
    });
  }

  List<CheckboxModel> invoicePaymentCheckBox = [];
  //GET ALL DETAILS PAYMENT
  getAllPaymentTypes() async {
    setState(() {
      if (ListUtility.paymentTypeList.isNotEmpty) {
        invoicePaymentCheckBox.add(CheckboxModel(id: "All", title: "All", value: false));
        for (PaymentType payment in ListUtility.paymentTypeList) {
          invoicePaymentCheckBox.add(CheckboxModel(id: payment.paymentTypeID, title: payment.paymentName, value: false));
        }
      }
    });
  }

  List<DropDown> invoiceDropDownlist = [];
  DropDown? invoiceDropDownModel;
  buildInvoiceDropDown() {
    invoiceDropDownlist.clear();
    invoiceDropDownlist.add(DropDown(id: "Number", name: "Number"));
    invoiceDropDownlist.add(DropDown(id: "FinancialYear", name: "FinancialYear"));

    invoiceDropDownModel = invoiceDropDownModel ?? invoiceDropDownlist.first;
  }

  //SYNC ALERT DIALOG
  alertSync(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
              child: SingleChildScrollView(
                child: AlertDialog(
                    content: Container(
                  width: double.maxFinite,
                  child: Form(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Text("Some records not synced, Please process to sync else you will loose you unsynced data"),
                        SizedBox(
                          height: 20,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            GestureDetector(
                              onTap: () {
                                if (!isLock) {
                                  isLock = true;
                                  Navigator.pop(context);
                                  passwordWorkspaceDialog(context);
                                  isLock = false;
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: width / 3.5,
                                child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                if (!isLock) {
                                  isLock = true;
                                  Navigator.pop(context);
                                  isLock = false;
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                width: width / 3.5,
                                child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
              ),
            );
          });
        });
  }

  //DYNAMIC PRICE ALERT DIALOG
  warningDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: double.maxFinite,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(child: Text("Your old data will be erased permanently, There is no way to roll back")),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              if (await fnUtilities.workSpaceSetValues(key_deleteBy2Days) == "1") {
                                setState(() {
                                  twoDays = true;
                                });
                              } else {
                                setState(() {
                                  twoDays = false;
                                });
                              }
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              settingsDetail.settingKey = "DeleteBy2Days";
                              String add2DayEnabel;
                              twoDays == true ? add2DayEnabel = "1" : add2DayEnabel = "0";
                              settingsDetail.settingValue = add2DayEnabel;
                              settingsDetail.sync = 0;
                              fnUtilities.updateSettingDetails(settingsDetail);
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  //SIGN IN PASSWORD ALERT DIALOG
  passwordWorkspaceDialog(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: double.maxFinite,
                child: Form(
                  key: validator.formkey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      Text("Your data will be erased permanently, There is no way to roll back"),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                        child: EditText(
                          mController: passwordController,
                          inputType: TextInputType.text,
                          isPassword: true,
                          isSecure: true,
                          text: "Password",
                          validator: validator.validateTextField,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          GestureDetector(
                            onTap: () {
                              if (!isLock) {
                                isLock = true;
                                passwordController.clear();
                                Navigator.pop(context);
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width / 3.5,
                              child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                          GestureDetector(
                            onTap: () async {
                              if (!isLock) {
                                isLock = true;
                                if (await InternetConnectionChecker().hasConnection) {
                                  validator.validate();
                                  if (validator.validate()) {
                                    CheckUserPassword loginRequest = new CheckUserPassword();
                                    loginRequest.password = passwordController.text;
                                    LoadingAlertWidget.onLoading(context);;
                                    await checkUserPassword(loginRequest).then((value) async {
                                      if (value.status == 1) {
                                        passwordController.clear();
                                        LoadingAlertWidget.onStopping();
                                        Navigator.pop(context);
                                        confirmationDialog(context);
                                      } else {
                                        LoadingAlertWidget.onStopping();
                                        showToast(value.message ?? "");
                                      }
                                    });
                                  }
                                } else {
                                  fnUtilities.internetTurnOnAlertDialog(context);
                                }
                                isLock = false;
                              }
                            },
                            child: Container(
                              alignment: Alignment.center,
                              height: 50,
                              width: width / 3.5,
                              child: TextWidget("Confirm", textColor: secondaryTextColor, isCentered: true),
                              decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                  ),
                ),
              )),
            ),
          );
        });
  }

  confirmationDialog(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    bool isDialogLock = false;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  actionsPadding: EdgeInsets.zero,
                  content: Container(
                    width: double.maxFinite,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisSize: MainAxisSize.max,
                      children: [
                        Column(
                          children: <Widget>[
                            GestureDetector(
                              onTap: () async {
                                if (!isDialogLock) {
                                  isDialogLock = true;
                                  if (await InternetConnectionChecker().hasConnection) {
                                    LoadingAlertWidget.onLoading(context);;
                                    await resetWorkspace(MyApp.activeWorkspace.workspaceId!).then((value) async {
                                      if (value.status == 1) {
                                        await commonDB.deleteAllRecordsBasedOnWorkspace(MyApp.activeWorkspace.workspaceId!, reset: true);
                                        LoadingAlertWidget.onStopping();
                                        Navigator.pop(context);
                                        Navigator.of(context).pushNamedAndRemoveUntil('bottombar', (Route<dynamic> route) => false);
                                      } else {
                                        LoadingAlertWidget.onStopping();
                                        showToast(value.message ?? "");
                                      }
                                    });
                                  } else {
                                    fnUtilities.internetTurnOnAlertDialog(context);
                                  }
                                  isDialogLock = false;
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 50,
                                width: width / 2,
                                child: TextWidget("Reset Shop", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                            SizedBox(
                              height: 20,
                            ),
                            GestureDetector(
                              onTap: () async {
                                if (!isDialogLock) {
                                  isDialogLock = true;
                                  if (await InternetConnectionChecker().hasConnection) {
                                    LoadingAlertWidget.onLoading(context);;
                                    await resetSalesByWorkspace(MyApp.activeWorkspace.workspaceId!).then((value) async {
                                      if (value.status == 1) {
                                        await dbScript.deleteFunction(
                                            db_salesTransactions, db_workspace_workspaceId, MyApp.activeWorkspace.workspaceId!);
                                        await dbScript.deleteFunction(db_salesDetails, db_workspace_workspaceId, MyApp.activeWorkspace.workspaceId!);
                                        // await salesDetailsDBScript
                                        //     .deleteAllSalesAndTransactionBasedOnWorkspace(
                                        //         MyApp.activeWorkspace
                                        //             .workspaceId);
                                        LoadingAlertWidget.onStopping();
                                        selectedIndex = 1;
                                        Navigator.pop(context);
                                        Navigator.of(context).pushNamedAndRemoveUntil('bottombar', (Route<dynamic> route) => false);
                                      } else {
                                        LoadingAlertWidget.onStopping();
                                        showToast(value.message ?? "");
                                      }
                                    });
                                  } else {
                                    fnUtilities.internetTurnOnAlertDialog(context);
                                  }
                                  isDialogLock = false;
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 50,
                                width: width / 2,
                                child: TextWidget("Reset Sales", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                  )),
            ),
          );
        });
  }

  uppdateShowImagesSetting(bool isShowImage) {
    settingsDetail.settingKey = key_showproductImages;
    settingsDetail.settingValue = isShowImage ? "1" : "0";
    settingsDetail.sync = 0;
    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
    fnUtilities.updateSettingDetails(settingsDetail);
  }

  /// to show served person name in bill
  uppdateShowServedBySetting(bool isShowEmployeeName) {
    settingsDetail.settingKey = showEmployeeNamesInKOT;
    settingsDetail.settingValue = isShowEmployeeName ? "1" : "0";
    settingsDetail.sync = 0;
    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
    fnUtilities.updateSettingDetails(settingsDetail);
  }

  initPageDetails() async {
    await getAllPaymentTypes();
    await initSettings();
    await updateInvoicePaymentCheckBox();
  }

  @override
  void initState() {
    initPageDetails();
    buildInvoiceDropDown();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text(
          "General Settings",
          style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      InkWell(
                        onTap: () async {
                          if (PermissionFunctions.checkPermission(160)) {
                            passwordWorkspaceDialog(context);
                          }
                        },
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: <Widget>[
                              Container(
                                child: Text(
                                  'Factory Reset',
                                  style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Reset Sales No Every Day',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: resetSalesNo,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(161)) {
                                  setState(() {
                                    resetSalesNo = value;
                                    settingsDetail.settingKey = "ResetInvoiceNo";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      // divider(),
                      // Padding(
                      //   padding: const EdgeInsets.symmetric(
                      //       horizontal: 8.0, vertical: 5.0),
                      //   child: Row(
                      //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      //     children: <Widget>[
                      //       Container(
                      //         width: width * 0.8,
                      //         child: Text(
                      //           'Auto delete by 2 days',
                      //           style: TextStyle(
                      //               color: primaryTextColor,
                      //               fontSize: textSizeMedium,
                      //               fontFamily: fontSemibold),
                      //         ),
                      //       ),
                      //       Switch(
                      //         value: twoDays,
                      //         onChanged: (value) {
                      //           if (rolePermissions.isEditPermissions(
                      //                   dbOperations
                      //                       .returnPermission("Settings")) ==
                      //               true) {
                      //             setState(() {
                      //               twoDays = value;
                      //               warningDialog(context);
                      //             });
                      //           } else {
                      //             showToast("You don't have a permission");
                      //           }
                      //         },
                      //         activeTrackColor: buttonThemeColor,
                      //         activeColor: buttonThemeColor,
                      //       ),
                      //     ],
                      //   ),
                      // ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'KOT Print',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: kotPrint,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(162)) {
                                  setState(() {
                                    kotPrint = value;
                                  });
                                  settingsDetail.settingKey = "KOTEnable";
                                  String addKOTEnabel;
                                  kotPrint == true ? addKOTEnabel = "1" : addKOTEnabel = "0";
                                  settingsDetail.settingValue = addKOTEnabel;
                                  settingsDetail.sync = 0;
                                  await fnUtilities.updateSettingDetails(settingsDetail);
                                  if (kotPrint == false) {
                                    settingsDetail.settingKey = "KOTIndividualEnable";
                                    settingsDetail.settingValue = "0";
                                    settingsDetail.sync = 0;
                                    await fnUtilities.updateSettingDetails(settingsDetail);
                                  }
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      kotPrint
                          ? Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Container(
                                        width: width * 0.79,
                                        child: Text(
                                          'KOT Confirmation',
                                          style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                        ),
                                      ),
                                      Switch(
                                        value: kotIndividualPrint,
                                        onChanged: (value) async {
                                          if (PermissionFunctions.checkPermission(163)) {
                                            setState(() {
                                              kotIndividualPrint = value;
                                            });
                                            settingsDetail.settingKey = "KOTIndividualEnable";
                                            String addKOTConfEnabel;
                                            kotIndividualPrint == true ? addKOTConfEnabel = "1" : addKOTConfEnabel = "0";
                                            settingsDetail.settingValue = addKOTConfEnabel;
                                            settingsDetail.sync = 0;
                                            await fnUtilities.updateSettingDetails(settingsDetail);
                                          }
                                        },
                                        activeTrackColor: buttonThemeColor,
                                        activeColor: buttonThemeColor,
                                      ),
                                    ],
                                  ),
                                ),
                                //divider(),
                                ///show employee Name in kot
                                showEmployeeNameWidget(),
                              ],
                            )
                          : Container(),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Customer Based Sales',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: isCustomer,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(165)) {
                                  await onChangeEnableCustomerBasedSales(value, key_isCustomer);

                                  // setState(() {
                                  //   if (isAdvanceOrder) {
                                  //     showToast("Unable to disable while using the advanced order feature");
                                  //   } else {
                                  //     isCustomer = value;
                                  //     settingsDetail.settingKey = "IsCustomer";
                                  //     settingsDetail.settingValue = value ? "1" : "0";
                                  //     settingsDetail.sync = 0;
                                  //     settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                  //     fnUtilities.updateSettingDetails(settingsDetail);
                                  //   }
                                  // });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      isCustomer ? customerNameWidget() : Container(),
                      isCustomer ? storeCustomerDetailsWidget(): SizedBox(),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Table Orders',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: enableTableOrder,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(166)) {
                                  setState(() {
                                    enableTableOrder = value;
                                    settingsDetail.settingKey = "EnableTableOrders";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Eat & Pay Orders',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: isEatAndPay,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(167)) {
                                  setState(() {
                                    isEatAndPay = value;
                                    settingsDetail.settingKey = "EatAndPay";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Payment Type',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: isPaymentType,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(168)) {
                                  setState(() {
                                    isPaymentType = value;
                                    settingsDetail.settingKey = "PaymentType";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Return Amount',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: isReturnAmount,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(169)) {
                                  setState(() {
                                    isReturnAmount = value;
                                    settingsDetail.settingKey = "ReturnAmountPrint";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      // divider(),
                      // inventorySettingsWidget(),
                      divider(),
                      showProductImage(),
                      divider(),
                      showTodaysalesWidget(),
                      divider(),
                      enableSuccessDialogWidget(),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Enable Logo On Print',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: imagePrint,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(174)) {
                                  setState(() {
                                    imagePrint = value;
                                    settingsDetail.settingKey = "ImagePrint";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail, tableType: "WorkSpaceSettings");
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      divider(),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: <Widget>[
                            Container(
                              width: width * 0.79,
                              child: Text(
                                'Additional KOT Button',
                                style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                              ),
                            ),
                            Switch(
                              value: additionalKOTButton,
                              onChanged: (value) async {
                                if (PermissionFunctions.checkPermission(175)) {
                                  setState(() {
                                    additionalKOTButton = value;
                                    settingsDetail.settingKey = "AdditionalKOTButton";
                                    settingsDetail.settingValue = value ? "1" : "0";
                                    settingsDetail.sync = 0;
                                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                    fnUtilities.updateSettingDetails(settingsDetail);
                                  });
                                }
                              },
                              activeTrackColor: buttonThemeColor,
                              activeColor: buttonThemeColor,
                            ),
                          ],
                        ),
                      ),
                      Divider(height: 6),
                      invoiceDropDownlist.isNotEmpty
                          ? Padding(
                              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                children: [
                                  Container(
                                    width: width / 2.5,
                                    child: Text(
                                      'Invoice Format : ',
                                      style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                    ),
                                  ),
                                  SizedBox(
                                    width: width / 1.8,
                                    child: CommonDropDown(
                                      hintText: 'Invoice Type',
                                      dropDownList: invoiceDropDownlist,
                                      dropDown: invoiceDropDownModel!,
                                      isExpanded: true,
                                      isDense: true,
                                      onChanged: (DropDown? newValue) async {
                                        if (PermissionFunctions.checkPermission(177)) {
                                          setState(() {
                                            FocusScope.of(context).requestFocus(FocusNode());
                                            invoiceDropDownModel = newValue;
                                            settingsDetail.settingKey = "InvoiceExtension";
                                            settingsDetail.settingValue = invoiceDropDownModel?.id;
                                            settingsDetail.sync = 0;
                                            settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                                            fnUtilities.updateSettingDetails(settingsDetail);
                                          });
                                        }
                                      },
                                    ),
                                  )
                                ],
                              ),
                            )
                          : Container(),

                      // invoicePaymentCheckBox.isNotEmpty
                      //     ? Column(
                      //         children: [
                      //           Padding(
                      //             padding: const EdgeInsets.all(8.0),
                      //             child: Row(
                      //               mainAxisAlignment:
                      //                   MainAxisAlignment.spaceBetween,
                      //               children: <Widget>[
                      //                 Container(
                      //                   width: width * 0.8,
                      //                   child: Text(
                      //                     'Invoice PaymentType',
                      //                     style: TextStyle(
                      //                         color: primaryTextColor,
                      //                         fontSize: textSizeMedium,
                      //                         fontFamily: fontSemibold),
                      //                   ),
                      //                 ),
                      //               ],
                      //             ),
                      //           ),
                      //           Column(
                      //             children: [
                      //               Align(
                      //                 alignment: Alignment.topCenter,
                      //                 child: ListView.builder(
                      //                     padding: EdgeInsets.only(right: 0.0),
                      //                     scrollDirection: Axis.vertical,
                      //                     itemCount:
                      //                         invoicePaymentCheckBox.length,
                      //                     shrinkWrap: true,
                      //                     physics: ScrollPhysics(),
                      //                     itemBuilder: (context, index) {
                      //                       var item =
                      //                           invoicePaymentCheckBox[index];
                      //                       return buildSingleCheckbox(item);
                      //                     }),
                      //               ),
                      //             ],
                      //           ),
                      //           divider(),
                      //         ],
                      //       )
                      //     : Container(),
                      divider(),
                      enableOrderOnlineWidgetWidget(),
                      // SizedBox(
                      //   height: 10,
                      // ),
                      divider(),
                      enableExpenseWidget(),

                      divider(),
                      enablePurchaseWidget(),
                      // ignore: sdk_version_ui_as_code
                      if (enablePurchase) ...[
                        inventorySettingsWidget(),
                      ],

                      // SizedBox(
                      //   height: 10,
                      // ),
                      divider(),
                      enableSalesBillPadWidget(),
                      // SizedBox(
                      //   height: 20,
                      // ),
                      divider(),
                      splitInvoiceByPaymentWidget(),
                      // SizedBox(
                      //   height: 10,
                      // ),
                     
                      // SizedBox(
                      //   height: 20,
                      // ),
                      divider(),
                      enableSalesCategoryWidget(),
                      // SizedBox(
                      //   height: 20,
                      // ),
                      divider(),
                      enableShiftbasedSalesWidget(),
                      // SizedBox(
                      //   height: 20,
                      // ),
                      divider(),
                      enableAdvanceOrderWidget(),
                      divider(),

                      SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildSingleCheckbox(CheckboxModel model) {
    return Container(
      child: CheckboxListTile(
        title: Text(model.title ?? ""),
        contentPadding: EdgeInsets.all(0),
        controlAffinity: ListTileControlAffinity.leading,
        value: model.value,
        onChanged: (bool? value) {
          onItemClicked(model);
        },
      ),
    );
  }

  List<String> selectedInvoicePaymentType = [];
  onItemClicked(CheckboxModel model) {
    // if (model.id == "All") {
    //   var value = !model.value;
    //   invoicePaymentCheckBox.forEach((element) {
    //     setState(() {
    //       element.value = value;
    //     });
    //   });
    //   upsertInvoicePayment(model.id);
    // } else {
    setState(() {
      model.value = model.value != null ? !model.value! : null;
    });
    upsertInvoicePayment(model.id ?? "");
    //}
    settingsDetail.settingKey = "InvoicePaymentType";
    settingsDetail.settingValue = jsonEncode(selectedInvoicePaymentType);
    settingsDetail.sync = 0;
    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
    fnUtilities.updateSettingDetails(settingsDetail);
  }

  upsertInvoicePayment(String id) {
    if (id == "All") {
      selectedInvoicePaymentType.clear();
      selectedInvoicePaymentType.add(id);
    } else {
      selectedInvoicePaymentType.remove("All");
      if (!selectedInvoicePaymentType.contains(id)) {
        selectedInvoicePaymentType.add(id);
      } else {
        selectedInvoicePaymentType.remove(id);
      }
    }
  }

  updateInvoicePaymentCheckBox() {
    for (CheckboxModel model in invoicePaymentCheckBox) {
      var value = selectedInvoicePaymentType.where((element) => element == model.id);
      if (value.isNotEmpty) {
        model.value = true;
      }
    }
  }

  ///show product images in sales page
  showProductImage() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Show Product Images',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: showImages,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(171)) {
                setState(() {
                  showImages = value;
                  uppdateShowImagesSetting(value);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///show employee Name in kot
  showEmployeeNameWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Show Employee Name',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: showEmployeeNameInKOT,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(164)) {
                setState(() {
                  showEmployeeNameInKOT = value;
                  uppdateShowServedBySetting(value);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///show today sales
  showTodaysalesWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Show Today Sales',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: showTodaySales,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(172)) {
                setState(() {
                  showTodaySales = value;
                  settingsDetail.settingKey = showTodaySalesKey;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///Enable online order
  enableOrderOnlineWidgetWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Online Order',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enableOnlineOrder,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(176)) {
                setState(() {
                  enableOnlineOrder = value;
                  settingsDetail.settingKey = enableOnlineOrderKey;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///Enable Expenses
  enableExpenseWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Expense',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enableExpense,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(178)) {
                setState(() {
                  enableExpense = value;
                  settingsDetail.settingKey = key_expense;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///Enable Expenses
  enablePurchaseWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Purchase',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enablePurchase,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(178)) {
                setState(() {
                  enablePurchase = value;
                  settingsDetail.settingKey = key_purchase;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  /// Invetory Settings
  inventorySettingsWidget() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Container(
                width: width * 0.79,
                child: Text(
                  'Allow Negative Sales For Inventory Products',
                  style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                ),
              ),
              Switch(
                value: allowNegativeSale,
                onChanged: (value) async {
                  if (PermissionFunctions.checkPermission(170)) {
                    setState(() {
                      allowNegativeSale = value;
                      settingsDetail.settingKey = "AllowNegativeSales";
                      settingsDetail.settingValue = value ? "1" : "0";
                      settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                      settingsDetail.sync = 0;
                      fnUtilities.updateSettingDetails(settingsDetail);
                    });
                  }
                },
                activeTrackColor: buttonThemeColor,
                activeColor: buttonThemeColor,
              ),
            ],
          ),
        ),
        // Padding(
        //   padding: const EdgeInsets.all(8.0),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     children: <Widget>[
        //       Container(
        //         width: width * 0.79,
        //         child: Text(
        //           'Enable Inventory',
        //           style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
        //         ),
        //       ),
        //       Switch(
        //         value: enableInventory,
        //         onChanged: (value) async {
        //           setState(() {
        //             enableInventory = value;
        //             settingsDetail.settingKey = "EnableInventory";
        //             settingsDetail.settingValue = enableInventory == true ? "1" : "0";
        //             settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
        //             settingsDetail.sync = 0;
        //           });
        //           await fnUtilities.updateSettingDetails(settingsDetail);
        //           if (enableInventory == true) {
        //             settingsDetail.settingKey = "AllowNegativeSales";
        //             settingsDetail.settingValue = "1";
        //             await fnUtilities.updateSettingDetails(settingsDetail);
        //             settingsDetail.settingKey = "InventoryType";
        //             settingsDetail.settingValue = "All";
        //             await fnUtilities.updateSettingDetails(settingsDetail);
        //           }
        //           setState(() {
        //             allowNegativeSale = true;
        //             inventoryType = "All";
        //           });
        //         },
        //         activeTrackColor: buttonThemeColor,
        //         activeColor: buttonThemeColor,
        //       ),
        //     ],
        //   ),
        // ),
        // enableInventory == true
        //     ? Column(
        //         children: [
        //           Padding(
        //             padding: const EdgeInsets.all(8.0),
        //             child: Row(
        //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //               children: <Widget>[
        //                 Container(
        //                   width: width * 0.8,
        //                   child: Text(
        //                     'Inventory Type',
        //                     style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
        //                   ),
        //                 ),
        //               ],
        //             ),
        //           ),
        //           Column(
        //             children: [
        //               RadioListTile(
        //                 groupValue: inventoryType,
        //                 value: 'All',
        //                 title: Text('All Products'),
        //                 onChanged: (val) {
        //                   setState(() {
        //                     inventoryType = "All";
        //                     settingsDetail.settingKey = "InventoryType";
        //                     settingsDetail.settingValue = inventoryType;
        //                     settingsDetail.sync = 0;
        //                     settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
        //                     fnUtilities.updateSettingDetails(settingsDetail);
        //                   });
        //                 },
        //               ),
        //               RadioListTile(
        //                 groupValue: inventoryType,
        //                 value: 'Specific',
        //                 title: Text('Specific Products'),
        //                 onChanged: (val) {
        //                   setState(() {
        //                     inventoryType = "Specific";
        //                     settingsDetail.settingKey = "InventoryType";
        //                     settingsDetail.settingValue = inventoryType;
        //                     settingsDetail.sync = 0;
        //                     settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
        //                     fnUtilities.updateSettingDetails(settingsDetail);
        //                   });
        //                 },
        //               )
        //             ],
        //           ),
        //           divider(),
        //           Padding(
        //             padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
        //             child: Row(
        //               mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //               children: <Widget>[
        //                 Container(
        //                   width: width * 0.79,
        //                   child: Text(
        //                     'Allow Negative Sales',
        //                     style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
        //                   ),
        //                 ),
        //                 Switch(
        //                   value: allowNegativeSale,
        //                   onChanged: (value) async {
        //                     setState(() {
        //                       allowNegativeSale = value;
        //                       settingsDetail.settingKey = "AllowNegativeSales";
        //                       settingsDetail.settingValue = value ? "1" : "0";
        //                       settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
        //                       settingsDetail.sync = 0;
        //                       fnUtilities.updateSettingDetails(settingsDetail);
        //                     });
        //                   },
        //                   activeTrackColor: buttonThemeColor,
        //                   activeColor: buttonThemeColor,
        //                 ),
        //               ],
        //             ),
        //           ),
        //         ],
        //       )
        //     : Container(),
      ],
    );
  }

  /// Enable success Dialog Widget
  enableSuccessDialogWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Success Dialog',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: successDialogOnSales,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(173)) {
                setState(() {
                  successDialogOnSales = value;
                  settingsDetail.settingKey = "SuccessDialogOnSales";
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///Enable sales bill pad settings
  enableSalesBillPadWidget() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Sales Bill Pad',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enableSalesBillPad,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(253)) {
                setState(() {
                  enableSalesBillPad = value;
                  settingsDetail.settingKey = key_sales_billPad;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  ///split Invoice By Payment
  splitInvoiceByPaymentWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Split Invoice By Payment',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: splitInvoiceByPayment,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(255)) {
                setState(() {
                  if (splitInvoiceByPayment) {
                    showToast("you can't disable for this settings.");
                  } else {
                    splitInvoiceByPayment = value;
                    settingsDetail.settingKey = splitInvoiceByPaymentKey;
                    settingsDetail.settingValue = value ? "1" : "0";
                    settingsDetail.sync = 0;
                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                    fnUtilities.updateSettingDetails(settingsDetail);
                  }
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  /// Make customer name manndatory
  customerNameWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Make Customer Name Mandatory',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: isCustomerNameMandatory,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(254)) {
               setState(() {
                isCustomerNameMandatory = value;
                  settingsDetail.settingKey = key_isCustomerNameMandatory;
                    settingsDetail.settingValue = value ? "1" : "0";
                    settingsDetail.sync = 0;
                    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                    fnUtilities.updateSettingDetails(settingsDetail);
               });
                
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  /// Store customer details from sales
  storeCustomerDetailsWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Store Customer Details From Sales',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: storeCustomerDetails,
            onChanged: (value) async {
              setState(() {
                storeCustomerDetails = value;
                settingsDetail.settingKey = key_store_customerDetails;
                settingsDetail.settingValue = value ? "1" : "0";
                settingsDetail.sync = 0;
                settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                fnUtilities.updateSettingDetails(settingsDetail);
              });
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  enableSalesCategoryWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Sales Category',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enableSalesCategory,
            onChanged: (value) async {
              setState(() {
                enableSalesCategory = value;
                settingsDetail.settingKey = key_sales_category;
                settingsDetail.settingValue = value ? "1" : "0";
                settingsDetail.sync = 0;
                settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                fnUtilities.updateSettingDetails(settingsDetail);
              });
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  /// Settings for Shift based Sales
  enableShiftbasedSalesWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Shift-based Sales',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: enableShiftbasedSales,
            onChanged: (value) async {
              if (PermissionFunctions.checkPermission(253)) {
                setState(() {
                  enableShiftbasedSales = value;
                  settingsDetail.settingKey = key_shiftBasedSales;
                  settingsDetail.settingValue = value ? "1" : "0";
                  settingsDetail.sync = 0;
                  settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                  fnUtilities.updateSettingDetails(settingsDetail);
                });
              }
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }

  enableAdvanceOrderWidget() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Container(
            width: width * 0.79,
            child: Text(
              'Enable Advance Order',
              style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
            ),
          ),
          Switch(
            value: isAdvanceOrder,
            onChanged: (value) async {
              // if (PermissionFunctions.checkPermission(253)) {
              setState(() {
                isAdvanceOrder = value;
                isCustomer = value;
              });
              settingsDetail.settingKey = key_advanceOrder;
              settingsDetail.settingValue = value ? "1" : "0";
              settingsDetail.sync = 0;
              settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
              await fnUtilities.updateSettingDetails(settingsDetail);
              //

              settingsDetail.settingKey = key_isCustomer;
              settingsDetail.settingValue = value ? "1" : "0";
              settingsDetail.sync = 0;
              settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
              await fnUtilities.updateSettingDetails(settingsDetail);
            },
            activeTrackColor: buttonThemeColor,
            activeColor: buttonThemeColor,
          ),
        ],
      ),
    );
  }
}
