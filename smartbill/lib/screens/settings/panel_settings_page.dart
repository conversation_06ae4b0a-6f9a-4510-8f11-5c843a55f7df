// ignore_for_file: non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';

import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../main.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';

class PanelSettingsPage extends StatefulWidget {
  @override
  PanelSettingsPageState createState() => PanelSettingsPageState();
}

class PanelSettingsPageState extends State<PanelSettingsPage> {
  double width = 0.0;
  double height = 0.0;
  String? selectedPanel;
  bool customerNameEnable = false;
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();

  //GET PANEL DETAILS FROM DB
  initSettings() async {
    setState(() {
      MyApp.isFilterEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductTapOnClear) == "1";
      MyApp.isListViewEnable = fnUtilities.workSpaceDeviceSetValue(key_panelProductListview) == "1";
      customerNameEnable = fnUtilities.workSpaceDeviceSetValue(key_panelCustomerName) == "1";
      selectedPanel = fnUtilities.workSpaceDeviceSetValue(key_panelType);
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Panel Settings", style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                SingleChildScrollView(
                  padding: EdgeInsets.only(top: 0),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.only(top: 0),
                        padding: EdgeInsets.only(top: 0),
                        alignment: Alignment.topCenter,
                        decoration: BoxDecoration(borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
                        child: Column(
                          mainAxisSize: MainAxisSize.max,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: <Widget>[
                            Padding(
                              padding: EdgeInsets.all(15.0),
                              child: Column(
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Container(
                                          width: width / 1.5,
                                          child: Text(
                                            'Products Shows In List View',
                                            style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                          ),
                                        ),
                                        Switch(
                                          value: MyApp.isListViewEnable,
                                          onChanged: (value) async {
                                            setState(() {
                                              MyApp.isListViewEnable = value;
                                              settingsDetail.settingKey = "PanelProductListview";
                                              String listView;
                                              MyApp.isListViewEnable == true ? listView = "1" : listView = "0";
                                              settingsDetail.settingValue = listView;
                                              fnUtilities.updateDeviceSettingDetails(settingsDetail);
                                            });
                                          },
                                          activeTrackColor: buttonThemeColor,
                                          activeColor: buttonThemeColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                  divider(),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Container(
                                          width: width / 1.5,
                                          child: Text(
                                            'Clear Filter Data Whether Tap On The Products',
                                            style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                          ),
                                        ),
                                        Switch(
                                          value: MyApp.isFilterEnable,
                                          onChanged: (value) async {
                                            setState(() {
                                              MyApp.isFilterEnable = value;
                                              settingsDetail.settingKey = "PanelProductTapOnClear";
                                              String tapClear;
                                              MyApp.isFilterEnable == true ? tapClear = "1" : tapClear = "0";
                                              settingsDetail.settingValue = tapClear.toString();
                                              fnUtilities.updateDeviceSettingDetails(settingsDetail);
                                            });
                                          },
                                          activeTrackColor: buttonThemeColor,
                                          activeColor: buttonThemeColor,
                                        ),
                                      ],
                                    ),
                                  ),
                                  divider(),
                                  Padding(
                                    padding: const EdgeInsets.all(8.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Container(
                                          width: width / 1.8,
                                          child: Text(
                                            'Set Panel Type',
                                            style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                          ),
                                        ),
                                        DropdownButtonHideUnderline(
                                          child: new DropdownButton<String>(
                                            hint: Text("Panel"),
                                            value: selectedPanel,
                                            items: <String>['Normal', 'Id', 'Key'].map((String value) {
                                              return new DropdownMenuItem<String>(
                                                value: value,
                                                child: new Text(value),
                                              );
                                            }).toList(),
                                            onChanged: (String? val) async {
                                              setState(() {
                                                selectedPanel = val;
                                                settingsDetail.settingKey = "PanelType";
                                                settingsDetail.settingValue = selectedPanel;
                                                fnUtilities.updateDeviceSettingDetails(settingsDetail);
                                              });
                                            },
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  divider(),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
