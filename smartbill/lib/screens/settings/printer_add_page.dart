// ignore_for_file: non_constant_identifier_names, must_be_immutable, sdk_version_ui_as_code, sdk_version_set_literal
import 'dart:io';

import 'package:flutter/material.dart' hide Image;
import 'package:flutter/services.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';

import '../../utils/common_function/validation_utility.dart';
import '../../utils/common_widgets/text_field_box_widget.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/common_widgets/text_widget.dart';

class AddPrinterPage extends StatefulWidget {
  PrinterDevices printerDevice;
  AddPrinterPage(this.printerDevice);

  @override
  State<AddPrinterPage> createState() => _AddPrinterPageState(printerDevice);
}

class _AddPrinterPageState extends State<AddPrinterPage> {
  double width = 0.0;
  double height = 0.0;
  _AddPrinterPageState(this.printerDevice);

  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  FnUtilities fnUtilities = new FnUtilities();

  TextEditingController printerNameController = TextEditingController();
  TextEditingController printerAddressController = TextEditingController();
  TextEditingController printerCustomizeNameController = TextEditingController();
  TextEditingController portController = TextEditingController();
  final feedController = TextEditingController();
  int paperSize = 58;
  bool isPaperCut = false;
  bool isLock = false;
  String? printerFormat;
  PrinterFor selectedPrintFor = PrinterFor.both;
  String networkType = ptBluetooth;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  final validator = new Validator();

  setBillType(PrinterFor? value) {
    setState(() {
      selectedPrintFor = value!;
    });
  }

  setNetworkType(String? value) {
    setState(() {
      networkType = value!;
    });
  }

  PrinterDevices? printerDevice;

  initSettings() async {
    setState(() {
      printerNameController.text = printerDevice?.printerName ?? "";
      printerCustomizeNameController.text = printerDevice?.customizeName ?? '';
      printerAddressController.text = printerDevice?.printerAddress ?? "";
      if (printerDevice?.portNumber != null) {
        portController.text = printerDevice?.portNumber.toString() ?? '';
      }
      paperSize = printerDevice?.paperSize ?? 58;
      printerFormat = printerDevice?.printerFormat ?? "Default";
      feedController.text = printerDevice?.feedSize != null ? printerDevice!.feedSize.toString() : "";
      isPaperCut = printerDevice?.paperCut ?? false;
      selectedPrintFor = printerDevice?.printerFor ?? selectedPrintFor;
      networkType = printerDevice?.printerType ?? networkType;
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  /// test printer function
  callTestPrinterMethod() async {
    try {
      LoadingAlertWidget.onLoading(context);;
      if (validator.validate() ?? false) {
        PrinterDevices testPrintDevice = PrinterDevices();
        testPrintDevice.printerName = printerNameController.text;
        testPrintDevice.customizeName = printerCustomizeNameController.text;
        testPrintDevice.printerAddress = printerAddressController.text;
        testPrintDevice.paperSize = paperSize;
        testPrintDevice.printerFormat = printerFormat;
        testPrintDevice.paperCut = isPaperCut;
        testPrintDevice.feedSize = int.tryParse(feedController.text) ?? 0;
        testPrintDevice.printerFor = selectedPrintFor;
        testPrintDevice.printerType = networkType;
        testPrintDevice.portNumber = int.tryParse(portController.text);
        await printerUtilities.testPrinterMethod(testPrintDevice);
        LoadingAlertWidget.onStopping();
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "callTestPrinterMethod - AddPrinterPage");
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    bool isBigLayout = width > tabletWidth ? true : false;
    return Scaffold(
      backgroundColor: buttonThemeColor,
      appBar: BaseAppBar(
        title: Text(
          "Printer Settings",
          style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Center(
          child: SingleChildScrollView(
            child: Container(
              decoration: boxdecoration(bgColor: secondaryTextColor, showShadow: true, radius: 12),
              padding: EdgeInsets.all(20),
              width: isBigLayout ? width * 0.45 : width,
              child: Form(
                key: validator.formkey,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                      TextWidget("Printer Name : "),
                      Expanded(
                        child: SizedBox(
                          child: TextField(
                            controller: printerNameController,
                            keyboardType: TextInputType.text,
                            textInputAction: TextInputAction.done,
                            autofocus: false,
                            autocorrect: true,
                            maxLines: 1,
                            readOnly: networkType == "network" ? false : true,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                              border: new OutlineInputBorder(
                                borderSide: new BorderSide(color: Colors.blue),
                              ),
                            ),
                          ),
                        ),
                      ),
                    ]),
                    SizedBox(
                      height: 10,
                    ),
                    if (networkType == ptNetwork) ...[
                      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                        TextWidget("Printer IP       : "),
                        Expanded(
                          child: SizedBox(
                            child: TextFieldBoxWidget(
                              mController: printerAddressController,
                              inputType: TextInputType.numberWithOptions(signed: true, decimal: true),
                              inputAction: TextInputAction.done,
                              autofocus: false,
                              isPassword: false,
                              maxLine: 1,
                              validator: validator.validateTextField,
                            ),
                          ),
                        ),
                      ]),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                    if (networkType == ptNetwork) ...[
                      Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
                        TextWidget(" Port number : "),
                        Expanded(
                          child: SizedBox(
                            child: TextFieldBoxWidget(
                              mController: portController,
                              inputType: TextInputType.number,
                              inputAction: TextInputAction.done,
                              autofocus: false,
                              isPassword: false,
                              maxLine: 1,
                              validator: validator.validateTextField,
                            ),
                          ),
                        ),
                      ]),
                      SizedBox(
                        height: 10,
                      ),
                    ],
                    Row(children: [
                      Text("Default Paper Size"),
                    ]),
                    new Row(children: [
                      new Radio(
                        value: 58,
                        groupValue: paperSize,
                        onChanged: (value) {
                          setState(() {
                            paperSize = value!;
                          });
                        },
                      ),
                      new Text('58mm'),
                      new Radio(
                        value: 80,
                        groupValue: paperSize,
                        onChanged: (value) {
                          setState(() {
                            paperSize = value!;
                          });
                        },
                      ),
                      new Text('80mm'),
                    ]),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: isBigLayout ? width * 0.18 : width * 0.4,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 0.0),
                              labelText: 'Printer Format',
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: new DropdownButton<String>(
                                hint: Text("Status"),
                                value: printerFormat,
                                items: <String>['Default', 'Format 01'].map((String value) {
                                  return new DropdownMenuItem<String>(
                                    value: value,
                                    child: new Text(value),
                                  );
                                }).toList(),
                                onChanged: (String? val) {
                                  setState(() {
                                    printerFormat = val!;
                                  });
                                },
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: isBigLayout ? width * 0.18 : width * 0.4,
                          child: TextField(
                            controller: feedController,
                            keyboardType: TextInputType.number,
                            textInputAction: TextInputAction.done,
                            inputFormatters: [
                              FilteringTextInputFormatter.digitsOnly,
                            ],
                            autofocus: false,
                            autocorrect: true,
                            maxLines: 1,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(vertical: 15, horizontal: 10),
                              border: new OutlineInputBorder(
                                borderSide: new BorderSide(color: Colors.blue),
                              ),
                              labelText: "Feed size",
                              isDense: true,
                            ),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text("Paper Cut"),
                        Checkbox(
                          value: isPaperCut,
                          onChanged: (value) {
                            setState(() {
                              isPaperCut = value!;
                            });
                          },
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Align(alignment: Alignment.bottomLeft, child: Text("Print for")),
                    Row(
                      children: [
                        Radio<PrinterFor>(
                          value: PrinterFor.kot,
                          groupValue: selectedPrintFor,
                          onChanged: setBillType,
                        ),
                        Text('KOT'),
                        Radio<PrinterFor>(
                          value: PrinterFor.bill,
                          groupValue: selectedPrintFor,
                          onChanged: setBillType,
                        ),
                        Text('Bill'),
                        Radio<PrinterFor>(
                          value: PrinterFor.both,
                          groupValue: selectedPrintFor,
                          onChanged: setBillType,
                        ),
                        Text('Both'),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Align(alignment: Alignment.bottomLeft, child: Text("Printer Type")),
                    Row(
                      children: [
                        if (!Platform.isWindows) ...[
                          Radio<String>(
                            value: ptBluetooth,
                            groupValue: networkType,
                            onChanged: setNetworkType,
                          ),
                          Text('Bluetooth'),
                        ],
                        Radio<String>(
                          value: ptNetwork,
                          groupValue: networkType,
                          onChanged: setNetworkType,
                        ),
                        Text('Network'),
                        if (Platform.isWindows) ...[
                          Radio<String>(
                            value: ptUSB,
                            groupValue: networkType,
                            onChanged: setNetworkType,
                          ),
                          Text('USB'),
                        ]
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    InkWell(
                      onTap: () async {
                        if (validator.validate()) {
                          if (!isLock) {
                            isLock = true;
                            PrinterDevices model = new PrinterDevices();
                            model.printerName = printerNameController.text;
                            model.customizeName = printerCustomizeNameController.text;
                            model.portNumber = int.tryParse(portController.text);
                            model.printerAddress = printerAddressController.text;
                            model.paperSize = paperSize;
                            model.printerFormat = printerFormat;
                            model.feedSize = int.tryParse(feedController.text) ?? 0;
                            model.paperCut = isPaperCut;
                            model.printerType = networkType;
                            model.printerFor = selectedPrintFor;
                            Navigator.pop(context, model);
                            isLock = false;
                          }
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 50,
                        width: isBigLayout ? width * 0.45 : width,
                        child: TextWidget("Update Settings ", textColor: secondaryTextColor, isCentered: true),
                        decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                      ),
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    InkWell(
                      onTap: () {
                        if (validator.validate() ?? false) {
                          callTestPrinterMethod();
                        }
                      },
                      child: Container(
                        alignment: Alignment.center,
                        height: 50,
                        width: isBigLayout ? width * 0.45 : width,
                        child: TextWidget("Test Printer", textColor: secondaryTextColor, isCentered: true),
                        decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
