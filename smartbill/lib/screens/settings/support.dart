import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/colors.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/styles.dart';

class Support extends StatefulWidget {
  @override
  SupportState createState() => SupportState();
}

class SupportState extends State<Support> {
  FnUtilities fnUtilities = new FnUtilities();
  double width= 0.0;
  double height= 0.0;
  String? mobileNo;
  String? mailID;

  initSettings() async {
    String mailId = await fnUtilities.getGeneralSetValue("Subscription.Email");
    String mobile = await fnUtilities.getGeneralSetValue("Subscription.Mobile");
    setState(() {
      mailID = mailId;
      mobileNo = mobile;
    });
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Support",
            style: TextStyle(color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          SingleChildScrollView(
            padding: EdgeInsets.only(top: 0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: <Widget>[
                SizedBox(
                  height: 20,
                ),
                CircleAvatar(
                    backgroundColor: Colors.white,
                    child: Icon(
                      Icons.contact_support_outlined,
                      size: 80,
                      color: Colors.black,
                    ),
                    radius: 50),
                Container(
                  margin: EdgeInsets.only(top: 0),
                  padding: EdgeInsets.only(top: 0),
                  alignment: Alignment.topCenter,
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(24),
                          topRight: Radius.circular(24))),
                  child: Column(
                    mainAxisSize: MainAxisSize.max,
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      Padding(
                        padding: EdgeInsets.all(24.0),
                        child: Column(
                          children: <Widget>[
                            SizedBox(height: 24),
                            divider(),
                            SizedBox(height: 10),
                            Column(
                              children: [
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Container(
                                        width: width / 4,
                                        child: TextWidget('Mobile No',
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      Container(
                                        width: width / 25,
                                        child: TextWidget(':',
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      Container(
                                        width: width / 3.5,
                                        child: TextWidget(
                                            // mobileNo != null ? mobileNo : ""  ,
                                            mobileNo ?? "",
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      Container(
                                        height: height / 20,
                                        width: width / 10,
                                        alignment: Alignment.centerLeft,
                                        child: IconButton(
                                            onPressed: () {
                                              Clipboard.setData(ClipboardData(
                                                  text: mobileNo ?? ""));
                                              showToast("copied");
                                            },
                                            icon: Icon(Icons.copy)),
                                      ),
                                    ]),
                                Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Container(
                                        width: width / 4,
                                        child: TextWidget('Mail ID',
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      Container(
                                        width: width / 25,
                                        child: TextWidget(':',
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      Container(
                                        width: width / 2.2,
                                        child: Text(
                                            mailID != null ? mailID??"" : "",
                                            style: TextStyle(fontSize: 16)),
                                      ),
                                      Container(
                                        height: height / 20,
                                        width: width / 10,
                                        alignment: Alignment.centerLeft,
                                        child: IconButton(
                                            onPressed: () {
                                              Clipboard.setData(
                                                  ClipboardData(text: mailID ?? ""));
                                              showToast("copied");
                                            },
                                            icon: Icon(Icons.copy)),
                                      ),
                                    ]),
                              ],
                            ),
                            SizedBox(height: 20),

                            /// You tube tutorial link
                            InkWell(
                              onTap: () {
                                launch('https://youtube.com/@nemboobill');
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                    width: 35,
                                    height: 35,
                                    youtubeIconSVG,
                                    color: buttonThemeColor,
                                  ),
                                  SizedBox(width: 8),
                                  Text('Videos | Tutorials',style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700))
                                ],
                              ),
                            ),

                            SizedBox(height: 20),
                            divider(),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
