// ignore_for_file: non_constant_identifier_names, unused_local_variable

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import 'package:smartbill/utils/constants/colors.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ParcelSettingsPage extends StatefulWidget {
  @override
  ParcelSettingsPageState createState() => ParcelSettingsPageState();
}

class ParcelSettingsPageState extends State<ParcelSettingsPage> {
  double width = 0.0;
  double height = 0.0;
  FnUtilities fnUtilities = new FnUtilities();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  Validator validator = new Validator();
  final parcelController = TextEditingController();
  bool enableParcel = false;
  String? applyParceChargeFor;
  bool isLock = false;

  //GET PARCEL DETAILS
  initSettings() {
    setState(() {
      var ParcelEnableValue = fnUtilities.workSpaceSetValues(key_parcelEnable) == "1";
      var ApplyParcelForValue = fnUtilities.workSpaceSetValues(key_applyParcelFor);
      if (ParcelEnableValue) {
        enableParcel = true;
        if (ApplyParcelForValue == null) {
          updateApplyForParcel();
        }
      } else {
        enableParcel = false;
      }
      applyParceChargeFor = ApplyParcelForValue ?? 'Specific';
      parcelController.text = fnUtilities.workSpaceSetValues(key_overAllParcelCharge) ?? '0';
    });
  }

  updateApplyForParcel() {
    settingsDetail.settingKey = key_applyParcelFor;
    settingsDetail.settingValue = 'Specific';
    settingsDetail.sync = 0;
    fnUtilities.updateSettingDetails(settingsDetail);
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Parcel Settings",style: TextStyle( color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Form(
              key: validator.formkey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.only(top: 0),
                    padding: EdgeInsets.only(top: 0),
                    alignment: Alignment.topCenter,
                    decoration: BoxDecoration(borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24))),
                    child: Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        Padding(
                          padding: EdgeInsets.all(15.0),
                          child: Column(
                            children: <Widget>[
                              Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: <Widget>[
                                    Container(
                                      width: width / 1.5,
                                      child: Text(
                                        'Enable Parcel',
                                        style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                      ),
                                    ),
                                    Switch(
                                      value: enableParcel,
                                      onChanged: (value) {
                                        if (PermissionFunctions.checkPermission(144)) {
                                          setState(() {
                                            enableParcel = value;
                                            settingsDetail.settingKey = "ParcelEnable";
                                            settingsDetail.settingValue = enableParcel ? "1" : "0";
                                            settingsDetail.sync = 0;
                                            fnUtilities.updateSettingDetails(settingsDetail);
                                          });
                                        } 
                                      },
                                      activeTrackColor: buttonThemeColor,
                                      activeColor: buttonThemeColor,
                                    ),
                                  ],
                                ),
                              ),
                              divider(),
                              enableParcel == true
                                  ? Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(8.0),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              Container(
                                                width: width / 1.5,
                                                child: Text(
                                                  'Apply Parcel For',
                                                  style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Column(
                                          children: [
                                            RadioListTile(
                                              groupValue: applyParceChargeFor,
                                              value: "All",
                                              title: Text('All Products'),
                                              onChanged: (val) {
                                                if (PermissionFunctions.checkPermission(145) ) {
                                                  setState(() {
                                                    applyParceChargeFor = val;
                                                    settingsDetail.settingKey = "ApplyParcelFor";
                                                    settingsDetail.settingValue = applyParceChargeFor;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities.updateSettingDetails(settingsDetail);
                                                  });
                                                } 
                                              },
                                            ),
                                            RadioListTile(
                                              groupValue: applyParceChargeFor,
                                              value: 'Specific',
                                              title: Text('Specific Products'),
                                              onChanged: (val) {
                                                if (PermissionFunctions.checkPermission(145) ) {
                                                  setState(() {
                                                    applyParceChargeFor = val;
                                                    settingsDetail.settingKey = "ApplyParcelFor";
                                                    settingsDetail.settingValue = applyParceChargeFor;
                                                    settingsDetail.sync = 0;
                                                    fnUtilities.updateSettingDetails(settingsDetail);
                                                  });
                                                }
                                              },
                                            )
                                          ],
                                        ),
                                      ],
                                    )
                                  : Container(),
                              divider(),
                              enableParcel && applyParceChargeFor == "All"
                                  ? Column(
                                      children: [
                                        SizedBox(
                                          height: 20,
                                        ),
                                        Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          children: [
                                            Container(
                                              width: width / 1.8,
                                              child: EditText(
                                                text: "Parcel Amount (₹)",
                                                isPassword: false,
                                                mController: parcelController,
                                                inputType: TextInputType.number,
                                                inputFormate: [
                                                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                                                ],
                                                validator: validator.validateTextField,
                                                inputAction: TextInputAction.done,
                                              ),
                                            ),
                                            SizedBox(
                                              width: 5,
                                            ),
                                            InkWell(
                                              onTap: () async {
                                                if(PermissionFunctions.checkPermission(145)){
                                                if (!isLock) {
                                                  isLock = true;
                                                  if (validator.validate() ?? false) {
                                                    settingsDetail.settingKey = "OverAllParcelCharge";
                                                    settingsDetail.settingValue = parcelController.text;
                                                    settingsDetail.sync = 0;
                                                    await fnUtilities.updateSettingDetails(settingsDetail);
                                                    showToast("Parcel charge updated successfully");
                                                    Navigator.pop(context);
                                                    FocusScope.of(context).unfocus();
                                                  }
                                                }
                                                isLock = false;
                                                }
                                              },
                                              child: Container(
                                                alignment: Alignment.center,
                                                height: 50,
                                                width: width / 4,
                                                child: TextWidget("Update", textColor: secondaryTextColor, isCentered: true),
                                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                              ),
                                            )
                                          ],
                                        ),
                                      ],
                                    )
                                  : Container(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
