import 'dart:convert';

import 'package:barcode_scan2/barcode_scan2.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/subscriptions/get_all_subscriptions.dart'; 
import 'package:smartbill/screens/settings/support.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/extend_validity.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
 
import 'package:smartbill/utils/razorpay/model/payment_model.dart';
import 'package:smartbill/utils/razorpay/model/razorpay_request.dart';
import 'package:smartbill/utils/razorpay/razorpay_class.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../Repositories/subscription_repository.dart';
import '../../Repositories/workspace_repository.dart';
import '../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/workspace_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';

class Subscriptions extends StatefulWidget {
  @override
  _SubscriptionsState createState() => _SubscriptionsState();
}

class _SubscriptionsState extends State<Subscriptions> {
  FnUtilities fnUtilities = new FnUtilities();
  final coupenCodeController = TextEditingController();
  final dbHelper = DatabaseHelper.instance;
  List<WorkSpace> adminWorkSpaceList = [];
  List<SubscriptionPlans> subscriptionPlans = [];
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  var now = DateTime.now();
  var width;
  var height;
  bool isAccount = false;
  bool isShowSignedDevices = false;
  bool isShowAdminDevices = false;
  bool isActivateByCode = false;
  bool isLock = false;
  String? currentDeviceId;
  String? currentDeviceName;
  String? validityDays = '';
  String? validityDescribe = '';
  bool load = false;
  bool isPaymentLock = false;

  //PLAN DETAILS FUNCTION
  getPlanDetails(DateTime? expDate) {
    if (expDate != null) {
      if (expDate.difference(now).inDays > 0) {
        setState(() {
          validityDays = "${expDate.difference(now).inDays} days to go!";
          validityDescribe = "Your plan is about to expire";
        });
      } else {
        setState(() {
          validityDays = "Plan expired!";
          validityDescribe = "You don't have any plan";
        });
      }
    } else {
      setState(() {
        validityDays = "Plan expired!";
        validityDescribe = "You don't have any plan";
      });
    }
  }

  //GET WORKSPACE DETAILS
  getWorkspaceNameAndId(WorkSpace model) {
    setState(() {
      currentDeviceId = model.workspaceId;
      currentDeviceName = model.workspaceName;
    });
    getPlanDetails(DateTime.parse(model.expiryDate ?? "0.0"));
  }

  //UPDATE EXPIRY DATE
  updateExpDate(date, workspaceId) async {
    await workspaceDBScript.updateExpiryDate(date, workspaceId);
  }

  //GET WORKSPACE DETAILS USING DB
  void queryAllGetWorkspace() async {
    final allRows = await workspaceDBScript.queryAllGetWorkspace();
    setState(() {
      ListUtility.signedWorkspaceList.clear();
      allRows.forEach(
          (row) => ListUtility.signedWorkspaceList.add(WorkSpace.fromMap(row)));
    });
  }

  //EXTEND VALIDITY FUNCTION
  callExtendValidity() async {
    if (!isLock) {
      isLock = true;
      if (MyApp.activeUser.token != null) {
        if (coupenCodeController.text.isNotEmpty) {
          ExtendValidity model = ExtendValidity();
          model.applicationId = MyApp.shopCode;
          model.mobileId = MyApp.deviceId;
          model.workspaceId = currentDeviceId;
          model.activationCode = coupenCodeController.text;
          if (await InternetConnectionChecker().hasConnection) {
            LoadingAlertWidget.onLoading(context);;
            await extendValidity(model).then((value) async {
              LoadingAlertWidget.onStopping();
              coupenCodeController.clear();
              if (value.response?.status == 1) {
                if (MyApp.activeWorkspace.workspaceId == currentDeviceId) {
                  setState(() {
                    MyApp.expiryDate = value.response?.expiryDate ?? DateTime.now();
                    MyApp.activeWorkspace.expiryDate =
                        value.response?.expiryDate.toString();
                  });
                  SharedPreferences prefs =
                      await SharedPreferences.getInstance();
                  prefs.setString(
                      "activeWorkspace", jsonEncode(MyApp.activeWorkspace));
                }
                await updateExpDate(
                    value.response!.expiryDate.toString(), currentDeviceId);
                getPlanDetails(value.response?.expiryDate);
              }
              showToast(value.response?.message);
            });
          } else {
            fnUtilities.internetTurnOnAlertDialog(context);
          }
        }
      }
      isLock = false;
    }
  }

  //SCANNER FUNCTION
  Future scan() async {
    try {
      var barcode = await BarcodeScanner.scan();
      setState(() {
        coupenCodeController.text = barcode.rawContent;
      });
    } on PlatformException catch (e) {
      if (e.code == BarcodeScanner.cameraAccessDenied) {
        setState(() {
          showToast('The user did not grant the camera permission!');
        });
      } else {
        showToast('Unknown error: $e');
      }
    } on FormatException {
      setState(() => showToast(
          'null (User returned using the "back"-button before scanning anything. Result)'));
    } catch (e) {
      setState(() => showToast('Unknown error: $e'));
    }
  }

  //GET ADMIN WORKSPACE DETAILS
  getAdminWorkspaceDetails() {
    LoadingAlertWidget.onLoading(context);;
    getWorkspaceListByAdmin().then((value) {
      LoadingAlertWidget.onStopping();
      if (value.result != null) {
        setState(() {
          adminWorkSpaceList = value.result!;
        });
      }
    });
  }

  //GET ALL PLANS API
  getAllPlansApi() {
    LoadingAlertWidget.onLoading(context);;
    getAllSubscriptionPlans().then((value) {
      LoadingAlertWidget.onStopping();
      if (value.result != null) {
        setState(() {
          subscriptionPlans = value.result!;
          subscriptionPlans.add(SubscriptionPlans());
        });
      } else {
        setState(() {
          subscriptionPlans.add(SubscriptionPlans());
        });
      }
    });
  }

  Future<void> pageRefreshDynamic(int index) async {
    if (index == 1) {
      showToast("Activation succeeded");
      refreshSubscription();
    } else {
      showToast("Activation failed");
    }
  }

  refreshSubscription() async {
    if (MyApp.activeUser.token != null) {
      if (await InternetConnectionChecker().hasConnection) {
        setState(() {
          load = true;
        });
        await getWorkspaceById(currentDeviceId).then((value) async {
          setState(() {
            load = false;
          });
          if (value.status == 1) {
            if (MyApp.activeWorkspace.workspaceId == currentDeviceId) {
              setState(() {
                MyApp.expiryDate = value.result?.expiryDate;
                MyApp.activeWorkspace.expiryDate =
                    value.result?.expiryDate.toString();
              });
              SharedPreferences prefs = await SharedPreferences.getInstance();
              prefs.setString(
                  "activeWorkspace", jsonEncode(MyApp.activeWorkspace));
            }
            await updateExpDate(
                value.result?.expiryDate.toString(), currentDeviceId);
            getPlanDetails(value.result?.expiryDate);
          }
        });
      }
      queryAllGetWorkspace();
    } else {
      fnUtilities.internetTurnOnAlertDialog(context);
    }
  }

  @override
  void initState() {
    getWorkspaceNameAndId(MyApp.activeWorkspace);
    queryAllGetWorkspace();
    super.initState();
  }

  Widget subscriptionBanner() {
    return Container(
      width: width,
      height: 280,
      child: Card(
        color: Colors.indigo,
        elevation: 50.0,
        shape: RoundedRectangleBorder(),
        shadowColor: Colors.black,
        child: Padding(
          padding: EdgeInsets.all(10.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        child: Text(
                          validityDays ??"",
                          style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w700,
                              color: Colors.white),
                        ),
                      ),
                      SizedBox(
                        height: 10,
                      ),
                      Container(
                          child: Text(
                        validityDescribe ??"",
                        style: TextStyle(color: Colors.white),
                      )),
                    ],
                  ),
                  !load
                      ? IconButton(
                          splashColor: Colors.indigo,
                          splashRadius: 30,
                          alignment: Alignment(12, -12),
                          icon: Icon(
                            Icons.refresh,
                            color: Colors.white,
                            size: 30,
                          ),
                          onPressed: () {
                            refreshSubscription();
                          })
                      : const SizedBox(
                          height: 25,
                          width: 25,
                          child: CircularProgressIndicator(
                            color: Colors.white,
                          ),
                        )
                ],
              ),
              SizedBox(
                height: 10,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    children: [
                      Container(
                        width: width / 2.5,
                        alignment: Alignment.centerLeft,
                        child: Text(
                          "Active Shops",
                          style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.w500,
                              color: Colors.white),
                        ),
                      ),
                      SizedBox(
                        height: 5,
                      ),
                      Container(
                        width: width / 2.5,
                        alignment: Alignment.centerLeft,
                        child: Text(
                          currentDeviceName ??"",
                          style: TextStyle(
                              fontSize: 17,
                              fontWeight: FontWeight.w500,
                              color: Colors.white),
                        ),
                      ),
                      Container(
                        width: width / 2.5,
                        height: height / 18,
                        alignment: Alignment.centerLeft,
                        child: Row(
                          children: [
                            Text(currentDeviceId ??"",
                                style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w400,
                                    color: Colors.white)),
                            IconButton(
                                onPressed: () {
                                  Clipboard.setData(
                                      ClipboardData(text: currentDeviceId ?? ""));
                                  showToast("copy");
                                },
                                icon: Icon(
                                  Icons.copy,
                                  color: Colors.white,
                                ))
                          ],
                        ),
                      ),
                    ],
                  ),
                  Column(
                    children: [
                      Container(
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            minimumSize: Size(width / 2.1, 30),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: BorderSide(color: Colors.red),
                            ),
                            primary: Colors.amberAccent,
                          ),
                          label: isShowSignedDevices
                              ? Text(
                                  "Hide Shops",
                                  style: TextStyle(color: Colors.black),
                                )
                              : Text(
                                  "Signed Shops",
                                  style: TextStyle(color: Colors.black),
                                ),
                          icon: isShowSignedDevices
                              ? Icon(
                                  Icons.arrow_upward,
                                  color: Colors.black,
                                )
                              : Icon(
                                  Icons.arrow_downward,
                                  color: Colors.black,
                                ),
                          onPressed: () {
                            setState(() {
                              isAccount = false;
                              isActivateByCode = false;
                              isShowAdminDevices = false;
                              isShowSignedDevices = !isShowSignedDevices;
                            });
                          },
                        ),
                      ),
                      Container(
                        child: ElevatedButton.icon(
                          style: ElevatedButton.styleFrom(
                            minimumSize: Size(width / 2.1, 30),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(18.0),
                              side: BorderSide(color: Colors.red),
                            ),
                            primary: Colors.amberAccent,
                          ),
                          label: isShowAdminDevices
                              ? Text(
                                  "Hide Shops",
                                  style: TextStyle(color: Colors.black),
                                )
                              : Text(
                                  "My Shops",
                                  style: TextStyle(color: Colors.black),
                                ),
                          icon: isShowAdminDevices
                              ? Icon(
                                  Icons.arrow_upward,
                                  color: Colors.black,
                                )
                              : Icon(
                                  Icons.arrow_downward,
                                  color: Colors.black,
                                ),
                          onPressed: () {
                            isShowAdminDevices == false
                                ? getAdminWorkspaceDetails()
                                : null;
                            setState(() {
                              isAccount = false;
                              isActivateByCode = false;
                              isShowSignedDevices = false;
                              isShowAdminDevices = !isShowAdminDevices;
                            });
                          },
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(
                height: 5,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MaterialButton(
                    height: 30,
                    shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(18.0),
                        side: const BorderSide(color: Colors.black)),
                    child: const Text("Support"),
                    color: Colors.blue,
                    onPressed: () {
                      Navigator.push(context,
                          MaterialPageRoute(builder: (context) => Support()));
                    },
                  ),
                  Container(
                    child: MaterialButton(
                      height: 30,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(18.0),
                          side: BorderSide(color: Colors.red)),
                      child: Text("Activation"),
                      color: Colors.green,
                      onPressed: () async {
                        //await getAllPlansApi();
                        setState(() {
                          isAccount = false;
                          isActivateByCode = true;
                          isShowSignedDevices = false;
                          isShowAdminDevices = false;
                        });
                      },
                    ),
                  )
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  //ACTIVATION PAGE WIDGET
  Widget activationPage() {
    return Column(
      children: [
        SizedBox(
          height: 40,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Container(
              width: width / 1.2,
              child: TextField(
                controller: coupenCodeController,
                decoration: InputDecoration(
                  suffixIcon: IconButton(
                    icon: Icon(
                      Icons.clear,
                      color: Colors.blue,
                    ),
                    onPressed: () {
                      coupenCodeController.clear();
                    },
                  ),
                  contentPadding: EdgeInsets.fromLTRB(16, 10, 16, 10),
                  hintText: "Enter your key",
                  hintStyle: TextStyle(color: t5ViewColor),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide:
                        const BorderSide(color: t5ViewColor, width: 0.0),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide:
                        const BorderSide(color: t5ViewColor, width: 0.0),
                  ),
                ),
              ),
            ),
            coupenCodeController.text.length == 0
                ? IconButton(
                    icon: Icon(
                      Icons.qr_code_scanner,
                      color: Colors.black,
                      size: 30,
                    ),
                    onPressed: () {
                      scan();
                    },
                  )
                : IconButton(
                    icon: Icon(
                      Icons.download_done_sharp,
                      color: Colors.black,
                      size: 30,
                    ),
                    onPressed: () async {
                      callExtendValidity();
                    },
                  ),
          ],
        ),
        SizedBox(
          height: 20,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            GestureDetector(
              onTap: () {
                callExtendValidity();
              },
              child: Container(
                alignment: Alignment.center,
                height: 40,
                width: width / 2.2,
                child: TextWidget("Activate", textColor: secondaryTextColor, isCentered: true),
                decoration:
                    boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
              ),
            ),
          ],
        ),
      ],
    );
  }

  //Payment PAGE WIDGET
  Widget paymentPage() {
    return Expanded(
      child: GridView.builder(
          padding: EdgeInsets.only(bottom: 15),
          scrollDirection: Axis.vertical,
          physics: ScrollPhysics(),
          itemCount: subscriptionPlans.length,
          shrinkWrap: true,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 3,
              crossAxisSpacing: 2,
              mainAxisSpacing: 2,
              childAspectRatio: 1.0),
          itemBuilder: (BuildContext context, int index) {
            var item = subscriptionPlans[index];
            return Padding(
              padding: EdgeInsets.all(1.0),
              child: item.id == null
                  ? gridActivateByCode()
                  : InkWell(
                      onTap: () async {
                        if (!isPaymentLock) {
                          try {
                            isPaymentLock = true;
                            RazorPayRequest requestModel = RazorPayRequest();
                            requestModel.planId = item.id;
                            requestModel.payment =
                                Payment(remainingAmount: item.amount);
                            RazorPayClass razorPayClass = RazorPayClass(context,
                                refreshFun: pageRefreshDynamic);
                            razorPayClass.startPayment(requestModel);
                            isPaymentLock = false;
                          } catch (e) {
                            isPaymentLock = false;
                          }
                        }
                      },
                      child: Container(
                        padding: EdgeInsets.only(left: 0, right: 0),
                        decoration: boxDecoration(
                            radius: 10,
                            showShadow: true,
                            bgColor: secondaryTextColor),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: <Widget>[
                            Container(
                              width: width,
                              padding: EdgeInsets.only(right: 5),
                              alignment: Alignment.center,
                              child: TextWidget(item.name ?? "",
                                  textColor: primaryTextColor,
                                  fontSize: textSizeNormal,
                                  fontFamily: fontBold,
                                  isCentered: true),
                            ),
                            SizedBox(height: 2),
                            Column(
                              children: [
                                Container(
                                    child: TextWidget(item.priceText1 ?? "",
                                        textColor: primaryTextColor,
                                        fontSize: textSizeSmall,
                                        fontFamily: fontBold,
                                        isLongText: true,
                                        isCentered: true,
                                        lineThrough: true)),
                                SizedBox(height: 2),
                                item.priceText2 != null
                                    ? Container(
                                        child: TextWidget(item.priceText2 ??"",
                                            textColor:
                                                primaryTextColor,
                                            fontSize: textSizeLargeMedium,
                                            fontFamily: fontSemibold,
                                            isLongText: true,
                                            isCentered: true),
                                      )
                                    : Container()
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
            );
          }),
    );
  }

  //GRID ACTIVATE BY CODE WIDGET
  Widget gridActivateByCode() {
    return InkWell(
      onTap: () async {
        setState(() {
          isActivateByCode = true;
          isAccount = false;
        });
      },
      child: Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        decoration: boxDecoration(
            radius: 10, showShadow: true, bgColor: secondaryTextColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              width: width,
              padding: EdgeInsets.all(5),
              child: Icon(Icons.add),
            ),
            SizedBox(height: 2),
            TextWidget("Activate By Code",
                textColor: primaryTextColor,
                fontSize: textSizeSmall,
                fontFamily: fontBold,
                isLongText: true,
                isCentered: true)
          ],
        ),
      ),
    );
  }

//VIEW SINGED DEVICES WIDGET
  Widget viewSignedDevices() {
    return Container(
      alignment: Alignment.topCenter,
      height: height / 2.5,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          ListUtility.signedWorkspaceList.length != 0
              ? Expanded(
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: ListView.builder(
                        padding: EdgeInsets.only(right: 0),
                        itemCount: ListUtility.signedWorkspaceList.length,
                        shrinkWrap: true,
                        reverse: true,
                        itemBuilder: (context, index) {
                          var item = ListUtility.signedWorkspaceList[index];
                          return InkWell(
                            onTap: () async {
                              await getWorkspaceNameAndId(item);
                            },
                            child: Column(
                              children: <Widget>[
                                Container(
                                  margin: EdgeInsets.all(5),
                                  height: 60,
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: <Widget>[
                                      Column(
                                        children: [
                                          Container(
                                            width: width / 2,
                                            alignment: Alignment.centerLeft,
                                            child: TextWidget(
                                                item.workspaceName.toString(),
                                                fontSize: textSizeMedium),
                                          ),
                                          Container(
                                            width: width / 2,
                                            alignment: Alignment.centerLeft,
                                            child: TextWidget(
                                                item.workspaceId.toString(),
                                                fontSize: textSizeSmall),
                                          ),
                                        ],
                                      ),
                                      Container(
                                        width: width / 3.5,
                                        alignment: Alignment.centerRight,
                                        child: TextWidget(
                                            DateTime.parse(item.expiryDate ??"0.0")
                                                        .difference(now)
                                                        .inDays >
                                                    0
                                                ? '${DateTime.parse(item.expiryDate ?? "0.0").difference(now).inDays.toString()} days'
                                                : "Plan Expired",
                                            textColor:
                                                primaryTextColor,
                                            fontSize: textSizeSmall,
                                            fontFamily: fontSemibold),
                                      ),
                                    ],
                                  ),
                                ),
                                Divider(height: 0.5, color: t5ViewColor)
                              ],
                            ),
                          );
                        }),
                  ),
                )
              : Container(
                  height: 100,
                  child: Center(
                    child: Text("No Other Devices !"),
                  ),
                ),
        ],
      ),
    );
  }

  //VIEW ADMIN DEVICES WIDGET
  Widget viewAdminDevices() {
    return adminWorkSpaceList.length != 0
        ? Expanded(
            child: Align(
              alignment: Alignment.topCenter,
              child: ListView.builder(
                  padding: EdgeInsets.only(right: 0),
                  itemCount: adminWorkSpaceList.length,
                  shrinkWrap: true,
                  reverse: true,
                  itemBuilder: (context, index) {
                    var item = adminWorkSpaceList[index];
                    return InkWell(
                      onTap: () async {
                        await getWorkspaceNameAndId(item);
                      },
                      child: Column(
                        children: <Widget>[
                          Container(
                            margin: EdgeInsets.all(5),
                            height: 60,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                Column(
                                  children: [
                                    Container(
                                      width: width / 2,
                                      alignment: Alignment.centerLeft,
                                      child: TextWidget(item.workspaceName.toString(),
                                          fontSize: textSizeMedium),
                                    ),
                                    Container(
                                      width: width / 2,
                                      alignment: Alignment.centerLeft,
                                      child: TextWidget(item.workspaceId.toString(),
                                          fontSize: textSizeSmall),
                                    ),
                                  ],
                                ),
                                Container(
                                  width: width / 3.5,
                                  alignment: Alignment.centerRight,
                                  child: TextWidget(
                                      DateTime.parse(item.expiryDate ?? "0.0")
                                                  .difference(now)
                                                  .inDays >
                                              0
                                          ? '${DateTime.parse(item.expiryDate ?? "0.0").difference(now).inDays.toString()} days'
                                          : "Plan Expired",
                                      textColor: primaryTextColor,
                                      fontSize: textSizeSmall,
                                      fontFamily: fontSemibold),
                                ),
                              ],
                            ),
                          ),
                          Divider(height: 0.5, color: t5ViewColor)
                        ],
                      ),
                    );
                  }),
            ),
          )
        : Container(
            height: 100,
            child: Center(
              child: Text("No Other Devices !"),
            ),
          );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text("Subscription Settings",
            style: TextStyle(color: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium),),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          subscriptionBanner(),
          SizedBox(
            height: 20,
          ),
          isAccount ? paymentPage() : Container(),
          isShowSignedDevices ? viewSignedDevices() : Container(),
          isShowAdminDevices ? viewAdminDevices() : Container(),
          isActivateByCode ? activationPage() : Container()
        ],
      ),
    );
  }
}
