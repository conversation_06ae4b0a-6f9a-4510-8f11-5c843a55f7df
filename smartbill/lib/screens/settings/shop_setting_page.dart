import 'dart:io';

import 'package:flutter/material.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/utils/common_function/list_class.dart';

import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:uuid/uuid.dart';

import '../../main.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/product_details_db_script.dart';
import '../../utils/dbutils/shop_details_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ShopSettings extends StatefulWidget {
  @override
  ShopSettingsState createState() => ShopSettingsState();
}

class ShopSettingsState extends State<ShopSettings> {
  final shopNameController = TextEditingController();
  final address1Controller = TextEditingController();
  final address2Controller = TextEditingController();
  final address3Controller = TextEditingController();
  final footer1Controller = TextEditingController();
  final footer2Controller = TextEditingController();
  final gstNoController = TextEditingController();
  final dbHelper = DatabaseHelper.instance;
  ShopDeatails shopDetails = new ShopDeatails();
  Validator validator = new Validator();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ShopDetailsDBScript shopDetailsDBScript = ShopDetailsDBScript();
  CommonDB commonDB = CommonDB();
  String? wSpace;
  int? shopId;
  bool isLock = false;
  String? imageId = '';

  //INSERT SHOP DETAILS
  insertShopDetails(ShopDeatails shopDetails) async {
    Map<String, dynamic> row = {
      db_shop_shopName: shopDetails.name,
      db_shop_shopAddress1: shopDetails.address1,
      db_shop_shopAddress2: shopDetails.address2,
      db_shop_shopAddress3: shopDetails.address3,
      db_shop_shopGstNo: shopDetails.gstNo,
      db_shop_shopFooterText: shopDetails.footerText,
      db_shop_shopFooterText2: shopDetails.footerText2,
      db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
      db_shop_shopSync: shopDetails.shopSync,
      db_shop_shopImageID: shopDetails.imageID
    };
    ShopDeatails pass = ShopDeatails.fromMap(row);
    await shopDetailsDBScript.insertShopDetails(pass);
    showToast("Successfully registered");
  }

  //UPDATE SHOP DETAILS
  updateShopDetails(ShopDeatails shopDetails) async {
    ShopDeatails shop = ShopDeatails(
        id: shopDetails.id,
        name: shopDetails.name,
        address1: shopDetails.address1,
        address2: shopDetails.address2,
        address3: shopDetails.address3,
        gstNo: shopDetails.gstNo,
        footerText: shopDetails.footerText,
        footerText2: shopDetails.footerText2,
        workspaceId: shopDetails.workspaceId,
        status: await commonDB.nonSyncShopFunctionCheck("ShopDetails", "shopSync") == 0 ? 1 : 0,
        shopSync: shopDetails.shopSync,
        imageID: shopDetails.imageID);
    await shopDetailsDBScript.updateShopDetails(shop);
  }

  //GET SHOP DETAILS USING LIST
  shopDetailsValue() async {
    for (ShopDeatails r in ListUtility.shopDetailsList) {
      shopNameController.text = r.name ?? "";
      address1Controller.text = r.address1 ?? "";
      address2Controller.text = r.address2 ?? "";
      address3Controller.text = r.address3 ?? "";
      gstNoController.text = r.gstNo ?? "";
      footer1Controller.text = r.footerText ?? "";
      footer2Controller.text = r.footerText2 ?? "";
      wSpace = r.workspaceId;
      shopId = r.id;
      if (!r.imageID.isEmptyOrNull) {
        imageId = r.imageID ?? "";
        var filepath = await fnUtilities.readFileFromLocal(imageId);
        setState(() {
          imageFile = filepath;
        });
      }
    }
  }

  //ASSIGN THE SHOP DETAILS TO LIST
  void _queryAllShopDetails() async {
    final allRows = await shopDetailsDBScript.queryallRowsShopDetails();
    ListUtility.shopDetailsList.clear();
    allRows.forEach((row) => ListUtility.shopDetailsList.add(ShopDeatails.fromMap(row)));
    await shopDetailsValue();
  }

  @override
  void dispose() {
    shopNameController.clear();
    address1Controller.clear();
    address2Controller.clear();
    address3Controller.clear();
    gstNoController.clear();
    footer1Controller.clear();
    footer2Controller.clear();
    super.dispose();
  }

  File? imageFile;

  pickImage() async {
    try {
      XFile? pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        imageFile = File(pickedFile.path);
      }
      var cropFile = await cropImage(imageFile?.path);
      setState(() {
        imageFile = cropFile;
      });
    } catch (ex) {
      imageFile = null;
    }
  }

  Future<File> cropImage(filePath) async {
    CroppedFile? croppedImage = await ImageCropper().cropImage(sourcePath: filePath, maxWidth: 150, maxHeight: 150, aspectRatio: null
        //  CropAspectRatio(ratioX: 1, ratioY: 1)
        );
    return File(croppedImage!.path);
  }

  Future<String?> saveImage() async {
    try {
      var uuid = new Uuid();
      String id = uuid.v4().toString();
      if (imageFile == null) return null;
      final String path1 = (await getApplicationDocumentsDirectory()).path;
      await imageFile?.copy('$path1/$id.png');
      return id;
    } catch (ex) {
      //print(ex);
      return null;
    }
  }

  Widget showImage() {
    return !imageFile.toString().isEmptyOrNull
        ? Stack(
            children: [
              Container(width: 160, height: 200, alignment: Alignment.center, child: Image.file(imageFile!)),
              Positioned(
                top: 3,
                right: -15,
                child: IconButton(
                  onPressed: () {
                    setState(() {
                      imageFile = null;
                      imageId = null;
                    });
                  },
                  icon: Icon(Icons.delete, size: 25.0, color: Colors.redAccent[400]),
                ), //Icon
              ),
            ],
          )
        : Container(
            decoration: boxDecoration(radius: 8.0),
            alignment: Alignment.center,
            child: Image(image: AssetImage("assets/images/no-image.jpeg")),
            width: 150,
            height: 150,
          );
  }

  @override
  void initState() {
    _queryAllShopDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: Text(
          "Shop Details",
          style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        ),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Expanded(
            child: Container(
              height: height,
              alignment: Alignment.center,
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      margin: EdgeInsets.all(24),
                      decoration: boxDecoration(bgColor: secondaryTextColor, showShadow: true, radius: 4),
                      padding: EdgeInsets.all(20),
                      child: Form(
                        key: validator.formkey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: <Widget>[
                            SizedBox(height: 10),
                            EditText(
                                text: "Shop Name",
                                mController: shopNameController,
                                isPassword: false,
                                maxLength: 40,
                                onTapOutside: (event) {
                                  FocusManager.instance.primaryFocus?.unfocus();
                                },
                                validator: validator.validateTextField),
                            SizedBox(height: 16),
                            EditText(
                              text: "Address1",
                              isPassword: false,
                              maxLength: 40,
                              maxLine: 4,
                              mController: address1Controller,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Address 2",
                              isPassword: false,
                              maxLength: 40,
                              mController: address2Controller,
                              inputType: TextInputType.text,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Address 3",
                              isPassword: false,
                              maxLength: 40,
                              mController: address3Controller,
                              inputType: TextInputType.text,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "GST No",
                              isPassword: false,
                              maxLength: 15,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                              validator: (value) {
                                if (value.isNotEmpty && value.length != 15)
                                  return 'gst no 15 digit';
                                else
                                  return null;
                              },
                              mController: gstNoController,
                              inputType: TextInputType.text,
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Footer Text1",
                              isPassword: false,
                              maxLength: 40,
                              mController: footer1Controller,
                              inputType: TextInputType.text,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                            SizedBox(height: 16),
                            EditText(
                              text: "Footer Text2",
                              isPassword: false,
                              maxLength: 40,
                              mController: footer2Controller,
                              inputType: TextInputType.text,
                              onTapOutside: (event) {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                            ),
                            SizedBox(
                              height: 16,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                GestureDetector(
                                  onTap: () {
                                    pickImage();
                                  },
                                  child: Container(
                                    alignment: Alignment.center,
                                    height: 50,
                                    width: width / 3.5,
                                    child: TextWidget("Image", textColor: secondaryTextColor, isCentered: true),
                                    decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                  ),
                                ),
                                showImage(),
                              ],
                            ),
                            Row(
                              children: <Widget>[
                                Expanded(
                                  child: GestureDetector(
                                    onTap: () async {
                                      if (!isLock) {
                                        isLock = true;
                                        if (PermissionFunctions.checkPermission(141)) {
                                          validator.validate();
                                          if (validator.validate() ?? false) {
                                            shopDetails.name = shopNameController.text.toUpperCase();
                                            shopDetails.address1 = address1Controller.text;
                                            shopDetails.address2 = address2Controller.text;
                                            shopDetails.address3 = address3Controller.text;
                                            shopDetails.footerText = footer1Controller.text;
                                            shopDetails.shopSync = 0;
                                            shopDetails.footerText2 = footer2Controller.text;
                                            shopDetails.gstNo = gstNoController.text.toUpperCase();
                                            imageId = await saveImage();
                                            shopDetails.imageID = imageId;
                                            if (MyApp.activeWorkspace.workspaceId != wSpace) {
                                              await insertShopDetails(shopDetails);
                                            } else {
                                              shopDetails.workspaceId = wSpace;
                                              shopDetails.id = shopId;
                                              await updateShopDetails(shopDetails);
                                            }
                                            selectedIndex = 1;
                                            Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
                                          }
                                        }
                                        isLock = false;
                                      }
                                    },
                                    child: Container(
                                      margin: EdgeInsets.only(right: 16),
                                      alignment: Alignment.center,
                                      height: 50,
                                      child: MyApp.activeWorkspace.workspaceId != wSpace
                                          ? TextWidget("Submit", textColor: secondaryTextColor, isCentered: true)
                                          : TextWidget("Update", textColor: secondaryTextColor, isCentered: true),
                                      decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            // SizedBox(height: 10),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
