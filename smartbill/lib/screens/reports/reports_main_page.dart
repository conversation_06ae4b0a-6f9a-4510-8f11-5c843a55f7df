// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code, sdk_version_set_literal

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/reports/additional_reports.dart';
import 'package:smartbill/screens/reports/customer_reports.dart';
import 'package:smartbill/screens/reports/expense_report_page.dart';
import 'package:smartbill/screens/reports/payment_reports_filter_page.dart';
import 'package:smartbill/screens/reports/product_reports_filter_page.dart';
import 'package:smartbill/screens/reports/purchase_report_page.dart';
import 'package:smartbill/screens/reports/reports_page.dart';
import 'package:smartbill/screens/reports/sale_table_reports.dart';
import 'package:smartbill/screens/reports/sales_category_reports.dart';
import 'package:smartbill/screens/reports/stock_reports.dart';
import 'package:smartbill/screens/reports/tax_report_filter_page.dart';
import 'package:smartbill/screens/reports/users_reports.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/reports_card_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import 'shift_wise_report_page.dart';

class ReportsMainPage extends StatefulWidget {
  @override
  ReportsMainPageState createState() => ReportsMainPageState();
}

class ReportsMainPageState extends State<ReportsMainPage> {
  double? width;
  FnUtilities fnUtilities = new FnUtilities();
  TabController? controller;
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  bool enableTableOrders = false;
  bool enablePaymentType = false;
  // bool enableInventory = false;
  bool enableTax = false;
  bool isDiscount = false;
  bool isReportView = false;
  bool isExpenseEnable = false;
  bool isEnableShiftWiseSales = false;
  bool enableSalesCategory = false;

  initSettings() async {
    isReportView = PermissionFunctions.checkPermission(252, viewToast: false);
    log(isReportView.toString());
    setState(() {
      enableTableOrders = fnUtilities.workSpaceSetValues(key_enableTableOrders) == "1";
      enablePaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      // enableInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1";
      enableTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
      isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
      isExpenseEnable = fnUtilities.workSpaceSetValues(key_expense) == "1";
      isEnableShiftWiseSales = fnUtilities.workSpaceSetValues(key_shiftBasedSales) == "1";
      enableSalesCategory = fnUtilities.workSpaceSetValues(key_salesCategory) == "1";
    });
  }

  initPageDetails() async {
    await initSettings();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;

    return DefaultTabController(
      length: 4,
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        // appBar: BaseAppBar(
        appBar: AppBar(
          backgroundColor: appThemeColor,
          title: text("Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          bottom: TabBar(
              onTap: (index) {},
              controller: controller,
              isScrollable: true,
              labelStyle: primaryTextStyle(),
              indicatorColor: redColor,
              physics: BouncingScrollPhysics(),
              labelColor: secondaryTextColor,
              tabs: [
                Tab(
                  child: Text(
                    'Sales',
                  ),
                ),
                Tab(
                  child: Text(
                    'Download',
                  ),
                ),
                Tab(
                  child: Text(
                    'Exp & Purchase',
                  ),
                ),
                Tab(
                  child: Text(
                    'Additional',
                  ),
                ),
              ]),
        ),
        // ),
        body: TabBarView(controller: controller, children: [
          SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 20),
                if (PermissionFunctions.checkPermission(179, viewToast: false)) ...[
                  ReportCardWidget(
                    titleIcon: shopSettingsSVG,
                    title: "Sales Reports",
                    isEnableTrailIcon: true,
                    onPress: () {
                      if (PermissionFunctions.checkPermission(179)) {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsPage()));
                      }
                    },
                  ),
                ],
                if (isReportView) ...{
                  ReportCardWidget(
                    titleIcon: productSettingsSVG,
                    title: "Products Reports",
                    isEnableTrailIcon: true,
                    onPress: () {
                      if (PermissionFunctions.checkPermission(180)) {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ProductReportsPage()));
                      }
                    },
                  ),
                  if (enableTableOrders) ...[
                    ReportCardWidget(
                      titleIcon: taxSVG,
                      title: "Table Reports",
                      isEnableTrailIcon: true,
                      onPress: () {
                        if (PermissionFunctions.checkPermission(181)) {
                          Navigator.push(context, MaterialPageRoute(builder: (context) => SaleTableReports()));
                        }
                      },
                    ),
                  ] else ...[
                    SizedBox()
                  ],
                  ReportCardWidget(
                    titleIcon: userSVG,
                    title: "User Reports",
                    isEnableTrailIcon: true,
                    onPress: () {
                      if (PermissionFunctions.checkPermission(184)) {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => UserReportsPage()));
                      }
                    },
                  ),
                  if (enableTax) ...[
                    ReportCardWidget(
                      titleIcon: taxSVG,
                      title: "Tax Reports",
                      isEnableTrailIcon: true,
                      onPress: () {
                        if (PermissionFunctions.checkPermission(185)) {
                          Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsFilterByTax()));
                        }
                      },
                    ),
                  ] else ...[
                    SizedBox()
                  ],
                }
              ],
            ),
          ),
          SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 20),
                ReportCardWidget(
                  titleIcon: taxSVG,
                  title: "Download Reports",
                  isEnableTrailIcon: true,
                  onPress: () {
                    if (PermissionFunctions.checkPermission(183)) {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => AdditionalReportsPage()));
                    }
                  },
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            child: Column(
              children: [
                SizedBox(height: 20),
                if (isExpenseEnable) ...[
                  ReportCardWidget(
                    titleIcon: expenses,
                    title: "Expense Reports",
                    isEnableTrailIcon: true,
                    onPress: () {
                      if (PermissionFunctions.checkPermission(187)) {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ExpenseReportsPage()));
                      }
                    },
                  ),
                  ReportCardWidget(
                    titleIcon: taxSVG,
                    title: "Purchase Reports",
                    isEnableTrailIcon: true,
                    onPress: () {
                      if (PermissionFunctions.checkPermission(188)) {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => PurchaseReportsPage()));
                      }
                    },
                  ),
                ] else ...[
                  SizedBox()
                ],
                ReportCardWidget(
                  titleIcon: taxSVG,
                  title: "Stock Reports",
                  isEnableTrailIcon: true,
                  onPress: () {
                    if (PermissionFunctions.checkPermission(182)) {
                      Navigator.push(context, MaterialPageRoute(builder: (context) => StockReports()));
                    }
                  },
                ),
              ],
            ),
          ),
          SingleChildScrollView(
              child: Column(children: [
            SizedBox(height: 20),

            if (enablePaymentType) ...[
              ///Payment Reports
              ReportCardWidget(
                titleIcon: payment,
                title: "Payment Reports",
                isEnableTrailIcon: true,
                onPress: () {
                  if (PermissionFunctions.checkPermission(186)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => PaymentReportsPage()));
                  }
                },
              ),
            ] else ...[
              SizedBox()
            ],
            if (enableSalesCategory) ...[
              ReportCardWidget(
                titleIcon: taxSVG,
                title: "Sales Category Reports",
                isEnableTrailIcon: true,
                onPress: () {
                  if (PermissionFunctions.checkPermission(188)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => SalesCategoryReportPage()));
                  }
                },
              ),
            ],
            ReportCardWidget(
              titleIcon: reportSVG,
              title: "Deleted Reports",
              isEnableTrailIcon: true,
              onPress: () {
                if (PermissionFunctions.checkPermission(266)) {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => ReportsPage(
                                isDeleteReport: true,
                              )));
                }
              },
            ),
            if (isEnableShiftWiseSales) ...{
              ReportCardWidget(
                titleIcon: reportSVG,
                title: "Shift Wise Reports",
                isEnableTrailIcon: true,
                onPress: () {
                  if (PermissionFunctions.checkPermission(184)) {
                    Navigator.push(context, MaterialPageRoute(builder: (context) => ShiftWiseReportPage()));
                  }
                },
              ),
            } else ...{
              SizedBox(),
            },
            ReportCardWidget(
              titleIcon: taxSVG,
              title: "Customer Reports",
              isEnableTrailIcon: true,
              onPress: () {
                // if (PermissionFunctions.checkPermission(183)) {
                Navigator.push(context, MaterialPageRoute(builder: (context) => CustomerReportPage()));
                // }
              },
            ),
            // if (isDiscount) ...[
            //   ///Discount Reports
            //   ReportCardWidget(
            //     titleIcon: taxSVG,
            //     title: "Discount Reports",
            //     isEnableTrailIcon: true,
            //     onPress: () {
            //       Navigator.push(
            //           context,
            //           MaterialPageRoute(
            //               builder: (context) =>
            //                   DiscountReport()));
            //     },
            //   ),
            // ] else ...[
            //   SizedBox()
            // ]
          ]))
        ]),
      ),
    );
  }
}
