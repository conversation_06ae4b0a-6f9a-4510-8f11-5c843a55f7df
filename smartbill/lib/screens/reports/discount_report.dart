import 'package:blue_thermal_printer/blue_thermal_printer.dart';
// ignore: implementation_imports
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';

import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import '../../Repositories/reports_repository.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../model/reports/get_discount_reports.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';

class DiscountReport extends StatefulWidget {
  @override
  _DiscountReportState createState() => _DiscountReportState();
}

class _DiscountReportState extends State<DiscountReport> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  String selectDiscountType = "Sales";
  double totalDiscount = 0.0;
  var width;
  var height;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isLoading = false;
  bool isBigLayout = false;
  TotalDiscount? totalDiscounts;
  List<ProductDiscounts> productDiscountLists = [];

  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  multiPrintChecker() async {
    if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter!);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked;
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
     if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //GET TOTAL Discount FUNCTION
  getTotalDetails() {
    totalDiscount = 0.0;
    setState(() {
      totalDiscount = totalDiscounts?.totalDiscount ?? 0.0;
    });
  }

  ///get tax reports
  getDiscontReport(
    fromDate,
    toDate,
  ) async {
    try {
      setState(() {
        isLoading = true;
      });

      await getDiscountReports(
        fromDate,
        toDate,
      ).then((value) {
        if (value.status == 1) {
          totalDiscounts = value.result?.totalDiscount;
          productDiscountLists = value.result?.productDiscounts ?? [];

          groupList = productDiscountLists.groupListsBy((element) => element.invoiceID);
          getTotalDetails();
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    await initSettings();
    await multiPrintChecker();
  }

  initSettings() async {
    isReportView = PermissionFunctions.checkPermission(261,viewToast: false);
  }

  ///Discount Report Print Method
  discountReportPrint() async {
    if (reportPrinter?.printerFormat == "Default") {
      if (reportPrinter?.paperSize == 58) {
        await discountReportPrintLineBuildFunction();
      } else {
        await discountReportPrintLineBuildFunction80MM();
      }
    } else {
      if (reportPrinter?.paperSize == 58) {
        await discountReportPrintLineBuildFunctionTVS();
      } else {
        await discountReportPrintLineBuildFunctionTVS80MM();
      }
    }
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        IconButton(
          iconSize: 25,
          icon: Icon(Icons.print),
          onPressed: () async {
            if (multiPrint) {
              reportPrinter = printerUtilities.getReportPrinter();
              await printerUtilities.initPrint(reportPrinter!);
              discountReportPrint();
            } else {
              discountReportPrint();
            }
          },
          color: Colors.white,
        ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    await discountReportTextBuildFunction();
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    await pdfReportBuildFunction(null);
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    await pdfReportBuildFunction(DateTime.now().toString());
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Discount Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[productDiscountLists.isNotEmpty && PermissionFunctions.checkPermission(262,viewToast: false) ? iconTopWidget() : Container()],
      ),
      body: Container(
          child: SingleChildScrollView(
        child: Column(
          children: [
            SizedBox(height: 20),
            Container(
              padding: EdgeInsets.all(2),
              alignment: Alignment.center,
              width: width,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  isReportView
                      ? BootstrapRow(children: [
                          BootstrapCol(
                              sizes: 'col-md-8 col-sm-12',
                              child: Padding(
                                padding: const EdgeInsets.only(top: 10),
                                child: Row(
                                  mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                  children: [
                                    InkWell(
                                      onTap: () {
                                        selectDate(context, 'fromDate');
                                      },
                                      child: Card(
                                        elevation: 4,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                "${fnUtilities.convertDate(fromDate.toString())}",
                                                style: secondaryTextStyle(),
                                              ),
                                              SizedBox(
                                                width: 15,
                                              ),
                                              Icon(Icons.calendar_today)
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () {
                                        selectDate(context, 'toDate');
                                      },
                                      child: Card(
                                        elevation: 4,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                fnUtilities.convertDate(toDate.toString()),
                                                style: secondaryTextStyle(),
                                              ),
                                              SizedBox(
                                                width: 15,
                                              ),
                                              Icon(Icons.calendar_today)
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                          BootstrapCol(
                              sizes: 'col-md-4 col-sm-12',
                              child: Padding(
                                padding: const EdgeInsets.only(top: 10.0),
                                child: Row(
                                  mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                  children: [
                                    InkWell(
                                      onTap: () async {
                                        setState(() {
                                          getDiscontReport(
                                            fromDate,
                                            toDate,
                                          );
                                        });
                                      },
                                      child: Container(
                                        alignment: Alignment.center,
                                        height: 40,
                                        width: isBigLayout ? width * 0.15 : width * 0.98,
                                        child: Text("Submit",
                                            style: TextStyle(
                                              color: secondaryTextColor,
                                              fontSize: 14,
                                              fontWeight: FontWeight.w700,
                                            )),
                                        decoration: BoxDecoration(
                                          borderRadius: BorderRadius.circular(5),
                                          color: buttonThemeColor,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )),
                        ])
                      : SizedBox.shrink(),
                  15.height,
                  isLoading
                      ? Container(
                          width: width,
                          child: Center(
                            child: Container(height: 40, child: CircularProgressIndicator()),
                          ),
                        )
                      : Container(
                          width: width,
                          padding: EdgeInsets.all(8.0),
                          alignment: Alignment.topCenter,
                          child: Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: <Widget>[
                                  Container(
                                    width: width / 2.5,
                                    alignment: Alignment.centerLeft,
                                    child: Text("Sale ID", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                  ),
                                  Container(
                                    width: width / 1.9,
                                    alignment: Alignment.centerRight,
                                    child: Text(
                                      "Amount",
                                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                    ),
                                  )
                                ],
                              ),
                              SizedBox(
                                height: 5,
                              ),
                              // ignore: sdk_version_ui_as_code
                              if (productDiscountLists.isNotEmpty)
                                ListView.builder(
                                    padding: const EdgeInsets.only(right: 0),
                                    itemCount: productDiscountLists.length,
                                    shrinkWrap: true,
                                    reverse: true,
                                    physics: NeverScrollableScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      var item = productDiscountLists[index];
                                      return Container(
                                        alignment: Alignment.centerLeft,
                                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 2),
                                        child: Column(
                                          children: [
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.start,
                                              children: <Widget>[
                                                Container(
                                                  width: width / 2.5,
                                                  alignment: Alignment.centerLeft,
                                                  child: Text(
                                                    item.invoiceID ?? "",
                                                  ),
                                                ),
                                                Container(
                                                  width: width / 1.9,
                                                  alignment: Alignment.centerRight,
                                                  child: Text(
                                                    item.discount.toString(),
                                                  ),
                                                )
                                              ],
                                            ),
                                            divider()
                                          ],
                                        ),
                                      );
                                    })
                            ],
                          )),
                  SizedBox(
                    height: isPortrait == Orientation.portrait ? height / 8 : height / 6,
                  )
                ],
              ),
            ),
          ],
        ),
      )),
      bottomSheet: Container(
        height: isPortrait == Orientation.portrait ? height / 8 : height / 6,
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: 5.0),
                  child: TextWidget("Total Discount: ",
                      textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                ),
                Padding(
                  padding: EdgeInsets.only(top: 5.0),
                  child: TextWidget("Rs.${totalDiscount.toStringAsFixed(2)}",
                      textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

//------------------------------------------------Start PrintBuild Function-------------------------------------------------//

  discountReportPrintLineBuildFunction() async {
    String listData = "";
    if (productDiscountLists.length != 0) {
      for (int list = 0; list < productDiscountLists.length; list++) {
        var item = productDiscountLists[list];
        listData +=
            '''{"text": "${item.invoiceID?.padRight(20)} ${double.parse(item.discount?.toString() ?? "").toStringAsFixed(2).padLeft(19)}", "size": 0, "alignment": 0},''';
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Discount Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   {"text": "Sale ID                   Amount", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
  {"text": "Total Discount: ${totalDiscount.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  discountReportPrintLineBuildFunctionTVS() async {
    String listData = "";
    if (productDiscountLists.length != 0) {
      for (int list = 0; list < productDiscountLists.length ; list++) {
        var item = productDiscountLists[list];
        listData +=
            '''{"text": "${item.invoiceID?.padRight(20)} ${double.parse(item.discount.toString()).toStringAsFixed(2).padLeft(11)}", "size": 0, "alignment": 0},''';
      }
    }
    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Discount Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 0, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "Sale ID                 Amount", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "Total Discount: ${totalDiscount.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  discountReportPrintLineBuildFunction80MM() async {
    String listData = "";
    if (productDiscountLists.length != 0) {
      for (int list = 0; list < productDiscountLists.length; list++) {
        var item = productDiscountLists[list];
        listData +=
            '''{"text": "${item.invoiceID?.padRight(31)} ${double.parse(item.discount.toString()).toStringAsFixed(2).padLeft(30)}", "size": 0, "alignment": 0},''';
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Discount Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Sale ID                                  Amount", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Total Discount: ${totalDiscount.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  discountReportPrintLineBuildFunctionTVS80MM() async {
    String listData = "";
    if (productDiscountLists.length != 0) {
      for (int list = 0; list < productDiscountLists.length; list++) {
        var item = productDiscountLists[list];
        listData +=
            '''{"text": "${item.invoiceID?.padRight(31)} ${double.parse(item.discount.toString()).toStringAsFixed(2).padLeft(30)}", "size": 0, "alignment": 0},''';
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Discount Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Sale ID                                    Amount", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Total Discount: ${totalDiscount.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }
//------------------------------------------------End PrintBuild Function-------------------------------------------------//

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  discountReportTextBuildFunction() async {
    String data = "";
    data += "\nDiscount Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(),isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(),isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nSale ID                         Amount";
    if (productDiscountLists.length != 0) {
      for (int list = 0; list < productDiscountLists.length; list++) {
        var item = productDiscountLists[list];
        data += "\n${item.invoiceID!.padRight(26) + double.parse(item.discount.toString()).toStringAsFixed(2).padLeft(20)}";
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nTotal Discount: ${totalDiscount.toStringAsFixed(2).padLeft(10)}";
    await printerUtilities.shareCommonReport(data, "Discount");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//
  pw.Widget pdfDiscountReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Discount Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(),isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(),isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 40, child: pw.Text("Sale ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 195, child: pw.Text("Formula", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 95, child: pw.Text("Discount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          for (ProductDiscounts discount in productDiscountLists)
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
              pw.Container(width: 40, child: pw.Text((sNo += 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 40, child: pw.Text(discount.invoiceID.toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 195,
                  child: pw.Text(discount.discountFormula.toString(), textAlign: pw.TextAlign.left, style: const pw.TextStyle(fontSize: 10))),
              pw.Container(
                  width: 95,
                  child: pw.Text(double.parse(discount.discount.toString()).toStringAsFixed(2),
                      textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
            ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Discount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalDiscount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfReportBuildFunction(String? currentDate) {
    String fileName = "Discount_Report";
    sharePDF.savePDFFormat(pdfDiscountReportBodyBuildFunction(), fileName, currentDate);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//

}
