import 'package:flutter/material.dart';
import 'package:month_year_picker/month_year_picker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../../Repositories/download_reports_repository.dart';
import '../../main.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/common_widgets/reports_card_widget.dart';
import '../../utils/common_widgets/widgets.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/images.dart';
import '../../utils/constants/styles.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class AdditionalReportsPage extends StatefulWidget {
  @override
  _AdditionalReportsPageState createState() => _AdditionalReportsPageState();
}

class _AdditionalReportsPageState extends State<AdditionalReportsPage> {
  bool isBigLayout = false;
  double width = 0.0;
  double height = 0.0;
  FnUtilities fnUtilities = FnUtilities();
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  DateTime singleDate = DateTime.now();

  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    await fnUtilities.requestStoragePermission();
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName) async {
    try {
      LoadingAlertWidget.onLoading(context);

      // await requestStoragePermission();
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
      ErrorLogsFunction.insertErrorLogs(e, "downloadReportFunction - AdditionalReportPage");
    }
  }

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showMonthYearPicker(
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null && picked != fromDate && dateType == 'fromDate') {
      setState(() {
        fromDate = picked;
      });
    }

    if (picked != null && picked != toDate && dateType == 'toDate') {
      setState(() {
        toDate = picked;
      });
    }

    if (picked != null && picked != singleDate && dateType == 'currentMonth') {
      setState(() {
        singleDate = picked;
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBigLayout = width > tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: appThemeColor,
      appBar: BaseAppBar(
        title: text("Download Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(10),
            child: Row(
              mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 5),
                      child: TextWidget("Start Month :", textColor: secondaryTextColor, fontSize: textSizeSMedium, fontWeight: FontWeight.w500),
                    ),
                    InkWell(
                      onTap: () {
                        selectDate(context, 'fromDate');
                      },
                      child: Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "${fnUtilities.convertDateMonthYear(fromDate.toString())}",
                                style: secondaryTextStyle(),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Icon(Icons.calendar_today)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 5),
                      child: TextWidget("End Month :", textColor: secondaryTextColor, fontSize: textSizeSMedium, fontWeight: FontWeight.w500),
                    ),
                    InkWell(
                      onTap: () {
                        selectDate(context, 'toDate');
                      },
                      child: Card(
                        elevation: 4,
                        child: Padding(
                          padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                fnUtilities.convertDateMonthYear(toDate.toString()),
                                style: secondaryTextStyle(),
                              ),
                              SizedBox(
                                width: 15,
                              ),
                              Icon(Icons.calendar_today)
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Expanded(
                  child: SingleChildScrollView(
                    child: Container(
                      padding: EdgeInsets.only(bottom: 20),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Wrap(children: [
                            ///Comparative Sales Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Comparative Sales Report",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(245)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = fromDate.month;
                                    downloadReportsRequest.year = fromDate.year;
                                    downloadReportsRequest.nMonth = toDate.month;
                                    downloadReportsRequest.nYear = toDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v1/sales/getsalescategoryreport", "Comparative Sales Report");
                                  }
                                },
                              ),
                            ),

                            ///Comparative Quantity Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Comparative Quantity Report",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(246)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = fromDate.month;
                                    downloadReportsRequest.year = fromDate.year;
                                    downloadReportsRequest.nMonth = toDate.month;
                                    downloadReportsRequest.nYear = toDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v1/sales/getsalescategoryqtyreport", "Comparative Quantity Report");
                                  }
                                },
                              ),
                            )
                          ]),
                          SizedBox(
                            height: 50,
                          ),
                          Padding(
                            padding: const EdgeInsets.all(10),
                            child: Row(
                              mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                              children: [
                                TextWidget("Choose Month : ", textColor: secondaryTextColor, fontSize: textSizeNormal, fontWeight: FontWeight.w500),
                                InkWell(
                                  onTap: () {
                                    selectDate(context, 'currentMonth');
                                  },
                                  child: Card(
                                    elevation: 4,
                                    child: Padding(
                                      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(
                                            fnUtilities.convertDateMonthYear(singleDate.toString()),
                                            style: secondaryTextStyle(),
                                          ),
                                          SizedBox(
                                            width: 15,
                                          ),
                                          Icon(Icons.calendar_today)
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Wrap(children: [
                            ///Sales Graph Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Saleswise Monthly Report",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(247)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = singleDate.month;
                                    downloadReportsRequest.year = singleDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v1/sales/gettotalsalesgraphreport", "Saleswise Monthly Report");
                                  }
                                },
                              ),
                            ),

                            ///Monthly Category Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Categorywise Monthly Report",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(248)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = singleDate.month;
                                    downloadReportsRequest.year = singleDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v1/sales/getmonthlycategoryreport", "Categorywise Monthly Report");
                                  }
                                },
                              ),
                            ),

                            ///Overall Sales by Payment Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Overall Sales by Payment",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(249)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = singleDate.month;
                                    downloadReportsRequest.year = singleDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v2/sales/getcategorymonthlysalesreport", "Overall Sales by Payment");
                                  }
                                },
                              ),
                            ),

                            ///Daywise Sales by Payment Report
                            Container(
                              constraints: BoxConstraints(maxWidth: 400),
                              child: ReportCardWidget(
                                titleIcon: fileDownloadSVG,
                                title: "Daywise Sales by Payment",
                                isEnableTrailIcon: false,
                                onPress: () {
                                  if (PermissionFunctions.checkPermission(250)) {
                                    downloadReportsRequest = DownloadReportsRequest();
                                    downloadReportsRequest.month = singleDate.month;
                                    downloadReportsRequest.year = singleDate.year;
                                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;

                                    downloadReportFunction(
                                        downloadReportsRequest, "/api/v2/sales/getcategoryweeklysalesreport", "Daywise Sales by Payment");
                                  }
                                },
                              ),
                            ),
                          ]),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
