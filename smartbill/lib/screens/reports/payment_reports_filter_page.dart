// ignore_for_file: non_constant_identifier_names, unused_local_variable, sdk_version_ui_as_code, sdk_version_set_literal

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_payment_reports.dart';
import 'package:smartbill/model/sales_reports_printer.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/screens/reports/reports_list_view.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/printer_utilities/report_print.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../Repositories/download_reports_repository.dart';
import '../../Repositories/reports_repository.dart';
import '../../main.dart';
import '../../model/reports.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/reports_db_script.dart';
import '../../utils/error_logs/error_logs_function.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class PaymentReportsPage extends StatefulWidget {
  bool isShiftWiseReport;
  PaymentReportsPage({this.isShiftWiseReport = false});

  @override
  PaymentReportsPageState createState() => PaymentReportsPageState();
}

class PaymentReportsPageState extends State<PaymentReportsPage> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  ReportPrint reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  PaymentTypeReport paymentSalesReports = new PaymentTypeReport();
  DBOperations dbOperations = new DBOperations();
  SalesReportsPrinter salesReportsPrinter = new SalesReportsPrinter();
  final dbHelper = DatabaseHelper.instance;
  List<DropdownMenuItem<PaymentType>>? dropdownMenuItems;
  List<DropdownMenuItem<ShiftwiseSalesModel>>? dropdownMenuShiftItems;
  List<Sales> paymentReportList = [];
  List<Sales> shiftWiseReportList = [];
  List<PaymentType> paymentTypeList = [];
  List<ShiftwiseSalesModel> shiftWiseList = [];
  PaymentReports? paymentReport = PaymentReports();
  PaymentType? selectPaymentType;
  ShiftwiseSalesModel? selectedShift;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  bool isLoading = false;
  bool initPageLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalSales = 0;
  double totalQty = 0;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isBigLayout = false;
  bool isLock = false;
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  bool isTwoDaysRecord = false;

  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    isReportView = await PermissionFunctions.checkPermission(231, viewToast: false);
    if (MultiPrintValue == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter!);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked.subtract(Duration(
    //         hours: picked.hour, minutes: picked.minute, seconds: picked.second, milliseconds: picked.millisecond, microseconds: picked.microsecond));
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked.subtract(Duration(
                hours: picked.hour,
                minutes: picked.minute,
                seconds: picked.second,
                milliseconds: picked.millisecond,
                microseconds: picked.microsecond));
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  getPaymentReport(fromDate, toDate, paymentId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getPaymentReportByDatToDate(fromDate, toDate, paymentId: paymentId).then((value) {
        if (value.status == 1) {
          paymentReportList = value.result?.reportList ?? [];
          totalSales = value.result?.totalSales ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = paymentReportList.groupListsBy((element) => element.paymentID);
          paymentReport = value.result;
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getPaymentReport - PaymentReportsPage ");

      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  getShiftWiseReport(fromDate, toDate, String shiftId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getShiftReportByDatToDate(fromDate, toDate, shiftId: shiftId).then((value) {
        if (value.status == 1) {
          shiftWiseReportList = value.result?.reportList ?? [];
          totalSales = value.result?.totalSales ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = shiftWiseReportList.groupListsBy((element) => element.shiftId);
          // shift
          paymentReport = value.result;
        } else {
          showToast(value.message);
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      ErrorLogsFunction.insertErrorLogs(e, "getShiftWiseReport - PaymentReportsPage ");
      setState(() {
        isLoading = false;
      });
    }
  }

  getShiftWiseReportFromLocale(fromDate, toDate, String shiftId) async {
    setState(() {
      isLoading = true;
    });
    try {
      var now = new DateTime.now();
      fromDate = now.subtract(
          Duration(days: 2, hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      toDate = now.subtract(
          Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      var result = await ReportDetailsDB.getCommonReports(fromDate, toDate, keyName: db_shift_Id, keyValue: shiftId);
      if (result != null) {
        paymentReport = PaymentReports.fromJson(result);
        shiftWiseReportList = paymentReport?.reportList ?? [];
        totalSales = paymentReport?.totalSales ?? 0;
        totalAmount = paymentReport?.totalAmount ?? 0;
        for (Sales model in shiftWiseReportList) {
          if (model.shiftId != null) {
            var shiftWiseModel = ListUtility.shiftSalesList.firstWhere((element) => element.shiftId == model.shiftId, orElse: null);
            model.shiftName = shiftWiseModel.shiftName != null ? shiftWiseModel.shiftName : "None";
          }
        }
        groupList = shiftWiseReportList.groupListsBy((element) => element.shiftId);
      }
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ErrorLogsFunction.insertErrorLogs(e, "getShiftWiseReportFromLocale - PaymentReportsPage ");
    }
  }
  //------------------------------------------------Start DropDown Function-------------------------------------------------//

  //GET ALL DETAILS BY PRODUCT
  queryAllPaymentTypeDetails() async {
    setState(() {
      paymentTypeList.clear();
      PaymentType model = PaymentType();
      model.paymentName = "All";
      model.paymentTypeID = "";
      paymentTypeList.add(model);
      paymentTypeList.addAll(ListUtility.paymentTypeList);
    });
    if (paymentTypeList.length > 0) {
      setState(() {
        dropdownMenuItems = buildDropdownMenuItems(paymentTypeList);
        selectPaymentType = dropdownMenuItems![0].value;
      });
    }
  }

  //PRODUCT LIST VIEW
  List<DropdownMenuItem<PaymentType>> buildDropdownMenuItems(List payment) {
    List<DropdownMenuItem<PaymentType>> items = [];
    for (PaymentType p in payment) {
      items.add(
        DropdownMenuItem(value: p, child: Text(p.paymentName ?? '')),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  void onChangeDropdownItem(PaymentType? payment) {
    if (PermissionFunctions.checkPermission(232)) {
      setState(() {
        selectPaymentType = payment;
      });
    }
  }

  //------------------------------------------------End DropDown Function-------------------------------------------------//

  /// -- Shift wise dropdown

  queryAllShiftWiseDetails() async {
    setState(() {
      shiftWiseList.clear();
      ShiftwiseSalesModel model = ShiftwiseSalesModel();
      model.shiftName = "All";
      model.shiftId = "";
      model.id = 0;
      shiftWiseList.add(model);
      shiftWiseList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2 && element.isActive == true));
    });
    if (shiftWiseList.length > 0) {
      setState(() {
        dropdownMenuShiftItems = buildDropdownMenuShiftItems(shiftWiseList);
        selectedShift = dropdownMenuShiftItems?[0].value;
      });
    }
  }

  // shift
  List<DropdownMenuItem<ShiftwiseSalesModel>> buildDropdownMenuShiftItems(List shiftList) {
    List<DropdownMenuItem<ShiftwiseSalesModel>> items = [];
    for (ShiftwiseSalesModel model in shiftList) {
      items.add(
        DropdownMenuItem(value: model, child: Text(model.shiftName ?? '')),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownShiftItem(ShiftwiseSalesModel? shift) {
    if (PermissionFunctions.checkPermission(232)) {
      setState(() {
        selectedShift = shift;
      });
    }
  }

  /// shift wise dropdown end
  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });

    await initSettings();

    if (widget.isShiftWiseReport) {
      await queryAllShiftWiseDetails();
    } else {
      await queryAllPaymentTypeDetails();
    }
    setState(() {
      initPageLoading = false;
    });
  }

  //Shift-Wise PRINTER REPORT FUNCTION
  shiftWiseReportPrint(PaymentReports? getPaymentReport) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
      }
      salesReportsPrinter.SalesReportsPrinterList = shiftWiseReportList;
      salesReportsPrinter.fromDate = fromDate.toString();
      salesReportsPrinter.toDate = toDate.toString();
      salesReportsPrinter.totAmount = getPaymentReport?.totalAmount.toString();
      salesReportsPrinter.salesNo = getPaymentReport?.totalSales.toString();
      salesReportsPrinter.isShort = false;
      if (multiPrint == true) {
        reportPrinter = printerUtilities.getReportPrinter();
        await printerUtilities.initPrint(reportPrinter);

        await Future.delayed(Duration(seconds: 2));
        LoadingAlertWidget.onStopping();
        await reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
        disconnect();
      } else {
        reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "shiftWiseReportPrint - PaymentReportPage");
    }
  }

  //PRINTER REPORT FUNCTION
  paymentReportPrint(PaymentReports? getPaymentReport) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
      }
      salesReportsPrinter.SalesReportsPrinterList = paymentReportList;
      salesReportsPrinter.fromDate = fromDate.toString();
      salesReportsPrinter.toDate = toDate.toString();
      salesReportsPrinter.totAmount = getPaymentReport?.totalAmount.toString();
      salesReportsPrinter.salesNo = getPaymentReport?.totalSales.toString();
      salesReportsPrinter.isShort = false;
      if (multiPrint == true) {
        reportPrinter = printerUtilities.getReportPrinter();
        await printerUtilities.initPrint(reportPrinter!);

        await Future.delayed(Duration(seconds: 2));
        LoadingAlertWidget.onStopping();
        await reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
        disconnect();
      } else {
        reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "paymentReportPrint - PaymentReportsPage");
    }
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      // await requestStoragePermission();
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
    }
  }

  //BLUETOOTH DISCONNECTION
  void disconnect() {
    try {
      bluetooth.disconnect();
    } catch (e) {}
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        if (!widget.isShiftWiseReport)
          IconButton(
            iconSize: 25,
            icon: Icon(Icons.print),
            onPressed: () async {
              if (PermissionFunctions.checkPermission(233)) {
                paymentReportPrint(paymentReport);
              }
            },
            color: Colors.white,
          ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report For Sale"),
                  onTap: () async {
                    if (widget.isShiftWiseReport) {
                      if (PermissionFunctions.checkPermission(274)) {
                        Navigator.pop(context);
                        downloadReportsRequest = DownloadReportsRequest();
                        downloadReportsRequest.fromDate = fromDate.toString();
                        downloadReportsRequest.toDate = toDate.toString();
                        downloadReportsRequest.shiftTypeID = selectedShift?.shiftId;
                        downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                        await downloadReportFunction(downloadReportsRequest, "/api/v1/sales/getshiftwisesalesreport", "Shift-wise Report");
                      }
                      print(MyApp.activeUser.token);
                    } else {
                      if (PermissionFunctions.checkPermission(236)) {
                        Navigator.pop(context);
                        await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                      }
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report For Product"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(236)) {
                      Navigator.pop(context);

                      downloadReportsRequest = DownloadReportsRequest();
                      downloadReportsRequest.fromDate = fromDate.toString();
                      downloadReportsRequest.toDate = toDate.toString();
                      downloadReportsRequest.paymentType = selectPaymentType?.paymentTypeID;
                      downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                      await downloadReportFunction(downloadReportsRequest, "/api/v1/sales/paymenttypereportbasedonproduct", "Product Report");
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text(widget.isShiftWiseReport ? "Shift Wise Reports" : "Payment Reports",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          (paymentReportList.isNotEmpty || shiftWiseReportList.isNotEmpty) && PermissionFunctions.checkPermission(233, viewToast: false)
              ? iconTopWidget()
              : Container()
        ],
      ),
      body: Container(
        padding: EdgeInsets.all(2),
        child: initPageLoading
            ? Center(child: CircularProgressIndicator())
            : paymentTypeList.length > 0 || shiftWiseList.length > 0
                ? SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        SizedBox(height: 20),
                        Container(
                          height: 40,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.only(left: 10.0, right: 10.0),
                                child: TextWidget("Filter Reports : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                              ),
                              if (widget.isShiftWiseReport) ...[
                                Container(
                                  width: width / 1.7,
                                  child: InputDecorator(
                                    decoration: InputDecoration(
                                      contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton(
                                        value: selectedShift,
                                        items: dropdownMenuShiftItems,
                                        onChanged: onChangeDropdownShiftItem,
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ),
                              ] else ...[
                                Container(
                                  width: width / 1.7,
                                  child: InputDecorator(
                                    decoration: InputDecoration(
                                      contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                    ),
                                    child: DropdownButtonHideUnderline(
                                      child: DropdownButton(
                                        value: selectPaymentType,
                                        items: dropdownMenuItems,
                                        onChanged: onChangeDropdownItem,
                                        isExpanded: true,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        isReportView
                            ? BootstrapRow(children: [
                                BootstrapCol(
                                    sizes: 'col-md-8 col-sm-12',
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 10),
                                      child: Row(
                                        mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                        children: [
                                          InkWell(
                                            onTap: () {
                                              selectDate(context, 'fromDate');
                                            },
                                            child: Card(
                                              elevation: 4,
                                              child: Padding(
                                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Text(
                                                      "${fnUtilities.convertDate(fromDate.toString())}",
                                                      style: secondaryTextStyle(),
                                                    ),
                                                    SizedBox(
                                                      width: 15,
                                                    ),
                                                    Icon(Icons.calendar_today)
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                          InkWell(
                                            onTap: () {
                                              selectDate(context, 'toDate');
                                            },
                                            child: Card(
                                              elevation: 4,
                                              child: Padding(
                                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                child: Row(
                                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                  children: [
                                                    Text(
                                                      fnUtilities.convertDate(toDate.toString()),
                                                      style: secondaryTextStyle(),
                                                    ),
                                                    SizedBox(
                                                      width: 15,
                                                    ),
                                                    Icon(Icons.calendar_today)
                                                  ],
                                                ),
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )),
                                BootstrapCol(
                                    sizes: 'col-md-4 col-sm-12',
                                    child: Padding(
                                      padding: const EdgeInsets.only(top: 10.0),
                                      child: Row(
                                        mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                        children: [
                                          InkWell(
                                            onTap: () async {
                                              if (widget.isShiftWiseReport) {
                                                final dayDifference = toDate.difference(fromDate).inDays;
                                                if (dayDifference <= 2) {
                                                  getShiftWiseReportFromLocale(fromDate, toDate, selectedShift?.shiftId ?? '');
                                                  isTwoDaysRecord = true;
                                                } else {
                                                  isTwoDaysRecord = false;
                                                  setState(() {
                                                    getShiftWiseReport(fromDate, toDate, selectedShift?.shiftId ?? '');
                                                  });
                                                }
                                                if (PermissionFunctions.checkPermission(272)) {
                                                  setState(() {
                                                    getShiftWiseReport(fromDate, toDate, selectedShift?.shiftId ?? '');
                                                  });
                                                }
                                              } else {
                                                if (PermissionFunctions.checkPermission(231)) {
                                                  setState(() {
                                                    getPaymentReport(fromDate, toDate, selectPaymentType?.paymentTypeID);
                                                  });
                                                }
                                              }
                                            },
                                            child: Container(
                                              alignment: Alignment.center,
                                              height: 40,
                                              width: isBigLayout ? width * 0.15 : width * 0.98,
                                              child: Text("Submit",
                                                  style: TextStyle(
                                                    color: secondaryTextColor,
                                                    fontSize: 14,
                                                    fontWeight: FontWeight.w700,
                                                  )),
                                              decoration: BoxDecoration(
                                                borderRadius: BorderRadius.circular(5),
                                                color: buttonThemeColor,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    )),
                              ])
                            : Container(),
                        isLoading
                            ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                            : paymentReportList.length != 0 || shiftWiseReportList.length != 0
                                ? Container(
                                    width: width,
                                    alignment: Alignment.topCenter,
                                    child: Column(
                                      children: [
                                        if (paymentReportList.isNotEmpty || shiftWiseReportList.isNotEmpty) ...{
                                          for (var i in groupList.keys) ...{
                                            if (groupList[i].length != 0) ...{
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                child: Column(
                                                  children: <Widget>[
                                                    //if (groupList[i][0].paymentTypeName != null) ...{
                                                    Container(
                                                      color: Colors.black12,
                                                      padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                                      child: Row(
                                                        children: [
                                                          Padding(
                                                            padding: const EdgeInsets.symmetric(vertical: 8),
                                                            child: Container(
                                                              alignment: Alignment.centerLeft,
                                                              child: Text(
                                                                widget.isShiftWiseReport
                                                                    ? groupList[i][0].shiftName ?? "None"
                                                                    : groupList[i][0].paymentTypeName ?? "None",
                                                                style: TextStyle(
                                                                  fontSize: textSizeMedium,
                                                                  fontWeight: FontWeight.bold,
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                    ),
                                                    // } else ...{
                                                    //   SizedBox()
                                                    // },
                                                    paymentBasedFilterList(groupList[i]),
                                                  ],
                                                ),
                                              )
                                            } else ...{
                                              SizedBox()
                                            },
                                          },
                                          SizedBox(
                                            height: 100,
                                          )
                                        }
                                      ],
                                    ))
                                : SizedBox(
                                    height: 200,
                                    child: Align(
                                        alignment: Alignment.bottomCenter,
                                        child: Container(
                                          child: Text(
                                            "Reports not found",
                                            style: black16w500,
                                          ),
                                        )),
                                  ),
                      ],
                    ),
                  )
                : Center(
                    child: Container(
                      padding: EdgeInsets.all(15.0),
                      child: TextWidget(widget.isShiftWiseReport ? "No Shifts Available" : "No PaymentType Available",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                    ),
                  ),
      ),
      bottomSheet: initPageLoading
          ? SizedBox()
          : Container(
              height: isPortrait == Orientation.portrait ? height * 0.12 : height * 0.2,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Grand Total: ",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget paymentBasedFilterList(List<Sales>? item) {
// Shift-wise and payment type reports are both managed by a single variable

    var groupListByPaymentName =
        item != null ? item.groupListsBy((element) => widget.isShiftWiseReport ? element.shiftName : element.paymentTypeName) : null;

    Map<String, double>? totalAmountByKeyword = {};
    int count = 0;
    item?.forEach((element) {
      final key = widget.isShiftWiseReport ? element.shiftName : element.paymentTypeName;
      if (totalAmountByKeyword.containsKey(key)) {
        totalAmountByKeyword[key ?? ''] = (totalAmountByKeyword[key] ?? 0) + double.parse(element.totalAmount.toString());
      } else {
        totalAmountByKeyword[key ?? ''] = double.parse(element.totalAmount.toString());
      }
      count++;
    });
    // item.forEach((element) {
    //   if (totalAmountByKeyword.containsKey(element.paymentTypeName)) {
    //     totalAmountByKeyword[element.paymentTypeName] = totalAmountByKeyword[element.paymentTypeName] == null
    //         ? 0
    //         : totalAmountByKeyword[element.paymentTypeName] + double.parse(element.totalAmount.toString());
    //     count = count + 1;
    //   } else {
    //     totalAmountByKeyword[element.paymentTypeName] = double.parse(element.totalAmount.toString());
    //     count = 1;
    //   }
    // });
    return Padding(
      padding: EdgeInsets.only(bottom: 0),
      child: Column(
        children: [
          for (var i in groupListByPaymentName!.keys) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: ReportListView(
                isTwoDaysRecord == true ? 0 : 2,
                item,
                reportFor: ReportFor.payment,
                isShiftWiseReport: widget.isShiftWiseReport,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 0),
              child: Container(
                color: Colors.black12,
                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Total",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "₹" + double.parse(totalAmountByKeyword[i].toString()).toStringAsFixed(2),
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${widget.isShiftWiseReport ? groupListByPaymentName[i]![0].shiftName ?? "None" : groupListByPaymentName[i]![0].paymentTypeName ?? "None"}",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            count.toString(),
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  paymentReportTextBuildFunction() async {
    String data = "";
    data += "\nPayments Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\n No     ID        PaidBy     Amount";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        data += "\n";
        for (int list = 0; list < groupList[i].length; list++) {
          data +=
              "\n${groupList[i][list].invoiceNo.toString().padLeft(2)} ${groupList[i][list].invoiceId.toString().padLeft(8)} ${groupList[i][list].paymentTypeName.toString().padLeft(9)} ${double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(2).padLeft(10)}";
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\nTotal Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}";
    await printerUtilities.shareCommonReport(data, widget.isShiftWiseReport ? "Shift-wise" : "Payments");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pw.Widget pdfPaymentsReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [
              pw.Text(widget.isShiftWiseReport ? "Shift Wise Report" : "Payments Report",
                  style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 60, child: pw.Text("No/Date", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 50, child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(
                width: 100,
                child: pw.Text(widget.isShiftWiseReport ? "Shift-wise" : "Paid By",
                    style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (groupList != null)
            for (var i in groupList.keys)
              for (int list = 0; list < groupList[i].length; list++)
                list == 0
                    ? pw.Column(children: [
                        pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                          pw.Container(
                              alignment: pw.Alignment.centerLeft,
                              width: 60,
                              child: pw.Column(
                                  mainAxisAlignment: pw.MainAxisAlignment.start,
                                  crossAxisAlignment: pw.CrossAxisAlignment.start,
                                  children: [
                                    pw.Text(groupList[i][list].paymentInvoice.toString(),
                                        style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left),
                                    pw.Text(
                                      fnUtilities.convertDateMonthYearFormat(groupList[i][list].date.toString(), isShowTime: true) ?? "",
                                      textAlign: pw.TextAlign.left,
                                      style: const pw.TextStyle(fontSize: 10),
                                    ),
                                  ])),
                          pw.Container(
                              width: 50,
                              child: pw.Text(groupList[i][list].invoiceId.toString(),
                                  textAlign: pw.TextAlign.left, style: const pw.TextStyle(fontSize: 10))),
                          pw.Container(
                            width: 100,
                            // child: pw.Column(
                            //   children: [
                            // pw.Text(
                            //   fnUtilities.convertDateMonthYearFormat(groupList[i][list].date.toString(), isShowTime: true),
                            //   textAlign: pw.TextAlign.right,
                            //   style: const pw.TextStyle(fontSize: 10),
                            // ),
                            child: pw.Padding(
                                padding: pw.EdgeInsets.only(top: 5.0, bottom: 5.0),
                                child: pw.Text(
                                    widget.isShiftWiseReport
                                        ? groupList[i][list].shiftTypeName.toString()
                                        : groupList[i][list].paymentTypeName.toString(),
                                    textAlign: pw.TextAlign.center,
                                    style: const pw.TextStyle(fontSize: 10))),
                            //   ],
                            // )
                          ),
                          pw.Container(
                              width: 95,
                              child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                        ]),
                      ])
                    : pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                        pw.Container(
                            alignment: pw.Alignment.centerLeft,
                            width: 60,
                            child:
                                pw.Column(mainAxisAlignment: pw.MainAxisAlignment.start, crossAxisAlignment: pw.CrossAxisAlignment.start, children: [
                              pw.Text(groupList[i][list].paymentInvoice.toString(),
                                  style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left),
                              pw.Text(
                                fnUtilities.convertDateMonthYearFormat(groupList[i][list].date.toString(), isShowTime: true) ?? "",
                                textAlign: pw.TextAlign.left,
                                style: const pw.TextStyle(fontSize: 10),
                              ),
                            ])),
                        pw.Container(
                            width: 50,
                            child: pw.Text(groupList[i][list].invoiceId.toString(),
                                textAlign: pw.TextAlign.left, style: const pw.TextStyle(fontSize: 10))),
                        pw.Container(
                            width: 100,
                            // child: pw.Column(
                            //   children: [
                            //     pw.Text(
                            //       fnUtilities.convertDateMonthYearFormat(groupList[i][list].date.toString(), isShowTime: true),
                            //       textAlign: pw.TextAlign.right,
                            //       style: const pw.TextStyle(fontSize: 10),
                            //     ),
                            child: pw.Padding(
                                padding: pw.EdgeInsets.only(top: 5.0, bottom: 5.0),
                                child: pw.Text(
                                    widget.isShiftWiseReport
                                        ? groupList[i][list].shiftTypeName.toString()
                                        : groupList[i][list].paymentTypeName.toString(),
                                    textAlign: pw.TextAlign.center,
                                    style: const pw.TextStyle(fontSize: 10)))
                            //   ],
                            // )
                            ),
                        pw.Container(
                            width: 95,
                            child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                      ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalAmount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfReportBuildFunction(String currentDate, {BuildContext? context}) {
    String fileName = widget.isShiftWiseReport ? "Shift_Wise_Report" : "Payments_Report";
    sharePDF.savePDFFormat(pdfPaymentsReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
