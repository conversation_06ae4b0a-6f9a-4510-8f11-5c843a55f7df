import 'package:flutter/material.dart';
import 'package:smartbill/model/payment_category_model.dart';

class MultiSelectCheckBox extends StatefulWidget {
  MultiSelectCheckBox({Key? key, required this.paymentCategoryList, required this.selectedCategoryListItems, this.onPressSubmit}) : super(key: key);
  List<PaymentCategory> paymentCategoryList;
  List<int>? selectedCategoryListItems;
  final ValueChanged<List<int>>? onPressSubmit;

  @override
  State<MultiSelectCheckBox> createState() => _MultiSelectCheckBoxState();
}

class _MultiSelectCheckBoxState extends State<MultiSelectCheckBox> {
  List<int> selectedListItems = [];

  // This function is triggered when a checkbox is checked or unchecked
  void _itemChange(int idValue, bool isSelected) {
    setState(() {
      if (isSelected) {
        if (idValue == 5) {
          selectedListItems.clear();
          selectedListItems.add(idValue);
        } else {
          // If the selected value is not 5, add it to the list
          selectedListItems.add(idValue);
          // If 5 is already in the list, remove it
          selectedListItems.removeWhere((item) => item == 5);
        }
      } else {
        if (idValue == 5) {
          selectedListItems.addAll([1, 2, 3]);
        }
        // If a checkbox is unchecked, remove it from the list
        selectedListItems.remove(idValue);
      }
    });
  }

  setSelectedListItems() {
    if (widget.selectedCategoryListItems != null) {
      selectedListItems.addAll(widget.selectedCategoryListItems!);
    }
  }

  @override
  void initState() {
    setSelectedListItems();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(20.0))),
        title: const Text('Select Topics'),
        content: SingleChildScrollView(
          child: ListBody(
            children: widget.paymentCategoryList
                .map((item) => CheckboxListTile(
                      value: selectedListItems.contains(item.paymentCategoryID),
                      title: Text(item.paymentCategoryName ?? ""),
                      controlAffinity: ListTileControlAffinity.leading,
                      onChanged: (isChecked) => _itemChange(item.paymentCategoryID ?? 0, isChecked!),
                    ))
                .toList(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              widget.onPressSubmit!(selectedListItems);
              Navigator.pop(context);
            },
            child: const Text('Submit'),
          ),
        ],
      ),
    );
  }
}
