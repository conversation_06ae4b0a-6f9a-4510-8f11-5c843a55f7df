import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_reports.dart';
import 'package:smartbill/model/sales_payment_mapping.dart';
import 'package:smartbill/screens/reports/reports_page.dart';
import 'package:smartbill/utils/common_widgets/advance_order_widget.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/advance_order_db_scripts.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../Repositories/reports_repository.dart';
import '../../model/get_transactions_fromapi.dart';
import '../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../main.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_payment_mapping_db_script.dart';
import '../../utils/constants/colors.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/salestype_db_script.dart';
import '../../utils/dbutils/shop_tables_db_script.dart';
import '../../utils/printer_utilities/report_print.dart';
import '../../utils/printer_utilities/sales_print.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../expenses/functions/stocks_functions.dart';
import '../item_layouts/Itempage_layout2.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../item_layouts/sales_main_page.dart';
import '../manage_shifts/model/manage_shifts_model.dart';
import '../salescategory/model/sales_category_model.dart';

// ignore: must_be_immutable
class ReportsViewPage extends StatefulWidget {
  int selectedIndex;
  Sales? reports;
  String? tableName;
  String? screenName;
  bool isDeleteReport;
  bool isShifWiseReport;
  bool isSalesReport;
  bool isAdvanceOrderEnabled;
  ReportsViewPage(
      {this.reports,
      this.tableName,
      this.selectedIndex = 0,
      this.screenName,
      this.isDeleteReport = false,
      this.isShifWiseReport = false,
      this.isSalesReport = false,
      this.isAdvanceOrderEnabled = false});
  @override
  _ReportsViewPageState createState() => _ReportsViewPageState(reports, tableName, selectedIndex, screenName);
}

class _ReportsViewPageState extends State<ReportsViewPage> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  SalesPrint salesPrint = SalesPrint();
  ReportPrint reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  GetReports getReports = GetReports();
  DBOperations dbOperations = new DBOperations();
  CommonDB commonDB = CommonDB();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  String paymentId = "";
  String? screenName;
  final dbHelper = DatabaseHelper.instance;
  Sales? reports;
  double totalAmount = 0.0;
  bool connected = false;
  bool multiPrint = false;
  double totalQuantity = 0;
  var dateFormate = '';
  var width;
  var height;
  String? tableName;
  var groupList;
  bool isLock = false;
  List<ShopTable> shopTablesList = [];
  List<Transactions> transactionList = [];
  String? saleLayout;
  double totalDiscount = 0.0;
  bool isResetInvoice = false;
  bool isLoading = false;
  int selectedIndex = 0;
  int selectIndex = 0;
  PrinterDevices? reportPrinter;
  List<SalesPaymentMapping> paymentMappingList = [];
  bool? isBigLayout;
  String? orderFromName;
  GetTransactions? getTransactions;
  //SINGLE REPORT VIEW INDEX
  _ReportsViewPageState(this.reports, this.tableName, this.selectedIndex, this.screenName);
  SalesPaymentMappingDB paymentMappingDb = SalesPaymentMappingDB();
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  SalesCategoryModel? salesCategoryModel;
  String? salesCategoryName;
  StocksFunction stocksFunction = StocksFunction();

  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  AdvanceOrdersDBScripts advanceOrderDB = AdvanceOrdersDBScripts();
  ShiftwiseSalesModel? shiftNameModel;
  AdvanceOrderNoteModel? orderNote;
  // String shiftName;
  bool showInventory = false;
  String? shifName;
  bool enableTax = false;
  int balance = 0;
  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    var SaleLayout = await fnUtilities.workSpaceUserSetValue(key_saleLayout);
    setState(() {
      isResetInvoice = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
      showInventory = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      enableTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";

      if (MultiPrintValue == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
        reportPrinter = printerUtilities.getReportPrinter();
        printerUtilities.initPrint(reportPrinter);
      }
      if (SaleLayout != null) {
        saleLayout = SaleLayout;
      } else {
        saleLayout = "SaleLayout1";
      }
    });
  }

  //GET ALL DETAILS BY PRODUCT
  getTableDetails() async {
    if (ListUtility.shopTableList.isNotEmpty) {
      setState(() {
        shopTablesList.clear();
        shopTablesList.add(new ShopTable(shopTableId: "All", shopTableName: "All"));
        shopTablesList.addAll(ListUtility.shopTableList);
      });
    }
  }

  //GET SINGLE SALES DETAILS
  queryGetSalesDetails(id, tableName) async {
    await getTableDetails();
    final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id, tableName: tableName, deletedRecord: widget.isDeleteReport);
    setState(() {
      transactionList.clear();
      allRows.forEach((row) => transactionList.add(Transactions.fromMap(row)));
      shopTableAssign();
      totalQty();
      findTotal();
    });
  }

  ///GET Payment Mapping details from db
  void getSalesPaymentDetails(id) async {
    try {
      if (selectedIndex == 2) {
        setState(() {
          paymentMappingList.clear();
          paymentMappingList.addAll(getTransactions?.salesPayments ?? []);
        });
      } else {
        final allRows = await paymentMappingDb.queryAllRowsBasedOnPaymentSalesId(id);
        setState(() {
          paymentMappingList.clear();
          allRows.forEach((row) => paymentMappingList.add(SalesPaymentMapping.fromJson(row)));
        });
      }
    } catch (e) {
      log(e.toString());
      setState(() {
        paymentMappingList = [];
      });
    }
  }

  //GET TRANSACTIONS FROM API BASED ON SALES ID
  getTransactionDetailsFromApi(salesId) async {
    try {
      setState(() {
        isLoading = true;
      });
      await getTableDetails();
      await getTransactionDetailsBySalesID(salesId).then((value) {
        setState(() {
          if (value.status == 1) {
            getTransactions = value.result;
            transactionList = value.result?.salesTransaction ?? [];
          }
          shopTableAssign();
          totalQty();
          findTotal();
        });
      });
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  shopTableAssign() {
    if (reports?.fkTableID != null) {
      try {
        reports?.tableName = shopTablesList.firstWhere((element) => element.shopTableId == reports?.fkTableID).shopTableName;
      } catch (ex) {
        reports?.tableName = "";
      }
    }
    groupList = transactionList.groupListsBy((element) => element.prodId);
  }

  //TOTAL QUANTITY FUNCTION
  totalQty() {
    totalQuantity = 0;
    for (Transactions p in transactionList) {
      totalQuantity += double.parse(p.qty ?? "");
      reports?.isIGST = p.isIGST;
    }
  }

  //TOTAL AMOUNT FUNCTION
  findTotal() {
    setState(() {
      totalAmount = 0.0;
      totalDiscount = 0.0;
      for (Transactions p in transactionList) {
        if (reports?.paymentCategoryID == 4) {
          totalAmount += double.parse(p.amountWithoutGst ?? "0").round();
        } else {
          totalAmount += double.parse(p.amount ?? "0").round();
        }
      }
      if (reports?.discountPrice != null) {
        totalDiscount = double.parse(reports?.discountPrice ?? "0");
      }
      // totalAmount = totalAmount - totalDiscount;
    });
  }

  assignValues() async {
    var tempOrderFromName = "";
    if (reports?.salesId != null) {
      if (reports?.onlinePlatformID != null) {
        tempOrderFromName = await commonDB.getSingleColumnBasedOnOneColumn(db_salesPaymentMapping_onlinePlatform,
            db_salesPaymentMapping_onlinePlatformId, reports?.onlinePlatformID ?? "", db_salesPaymentMapping_onlinePlatformName);
      }
    }
    setState(() {
      orderFromName = tempOrderFromName;
    });
    // print(orderFromName);
  }

  getOrderNotesList(String salesID) async {
    // orderNotesList.clear();
    // orderNotesList.addAll(await advanceOrderDB.getOrderNotesFromLocal());
    // orderNote = orderNotesList.firstWhere((element) => element.salesId == salesID).note;
    if (selectedIndex != 2) {
      var orderNoteMap = await commonDB.getSingleDataBasedOneColumn(db_order_notes, db_sales_salesId, salesID);
      orderNote = orderNoteMap != null ? AdvanceOrderNoteModel.fromJson(orderNoteMap) : null;
    } else {
      orderNote = getTransactions?.advanceOrderNotes != null && getTransactions!.advanceOrderNotes!.length > 0
          ? getTransactions!.advanceOrderNotes![0]
          : null;
    }
  }

  initFunctions() async {
    initSettings();
    List<ShiftwiseSalesModel>? shiftSalesList = [];

    shiftSalesList.clear();
    shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2 && element.isActive == true));

    if (reports?.salesCategoryId != null) {
      salesCategoryModel = ListUtility.salesCategoryList.firstWhere((element) => element.salesCategoryId == reports?.salesCategoryId, orElse: null);
      salesCategoryName = salesCategoryModel != null ? salesCategoryModel?.salesCatName : null;
    }

    if (selectedIndex == 2) {
      await getTransactionDetailsFromApi(reports?.salesId);
      reports?.shiftName =
          getTransactions?.salesPayments != null && getTransactions!.salesPayments!.isNotEmpty ? getTransactions?.salesPayments![0].shiftName : "";
    } else {
      await queryGetSalesDetails(reports?.salesId, tableName);
      reports?.shiftName = shiftSalesList.where((element) => element.shiftId == reports?.shiftId).map((element) => element.shiftName).firstOrNull;
    }

    getSalesPaymentDetails(reports?.salesId);
    setState(() {
      dateFormate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${reports?.date}"));
    });
    await assignValues();
    await getOrderNotesList(widget.reports?.salesId ?? "");
  }

  @override
  void initState() {
    initFunctions();
    super.initState();
  }

  @override
  void dispose() {
    printerUtilities.disconnect();
    super.dispose();
  }

  iconTopWidget(BuildContext context) {
    return Row(
      children: [
        IconButton(
          iconSize: 25,
          icon: Icon(Icons.print),
          onPressed: () async {
            printForAlertDialog();
          },
          color: Colors.white,
        ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            log(widget.isShifWiseReport);
            return <PopupMenuEntry>[
              if (!widget.isShifWiseReport)
                if (selectedIndex < 2 && !widget.isDeleteReport)
                  PopupMenuItem(
                    child: ListTile(
                      leading: Icon(Icons.edit),
                      title: Text("Edit"),
                      onTap: () async {
                        Navigator.pop(context);
                        if (PermissionFunctions.checkPermission(197)) {
                          Navigator.pop(context);
                          Navigator.pushReplacement(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => saleLayout == "SaleLayout2"
                                      ? ItemsPageLayout2(
                                          sales: reports,
                                          saleTransactionList: transactionList,
                                          paymentMapList: paymentMappingList,
                                          orderNotes: orderNote?.note ?? "",
                                        )
                                      : SalesMainPage(
                                          sales: reports,
                                          saleTransactionList: transactionList,
                                          paymentMapList: paymentMappingList,
                                          orderNotes: orderNote?.note ?? "",
                                        )));
                        }
                      },
                    ),
                  ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    if (PermissionFunctions.checkPermission(200)) {
                      await salesReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    if (PermissionFunctions.checkPermission(201)) {
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    Navigator.pop(context);
                    if (PermissionFunctions.checkPermission(201)) {
                      await pdfReportBuildFunction(DateTime.now().toString(), isDownload: true);
                    }
                  },
                ),
              ),
              if (widget.isDeleteReport) ...[
                PopupMenuItem(
                  child: ListTile(
                    leading: Icon(Icons.arrow_back_ios),
                    title: Text("Revert"),
                    onTap: () async {
                      showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return StatefulBuilder(
                            builder: (BuildContext context, StateSetter setState) {
                              return ConfirmationAlertDialog(
                                content: "Are you sure you want to revert the transaction ${reports?.invoiceId.toString()} ?",
                                buttonNameOne: "No",
                                ontabButtonOne: () {
                                  if (!isLock) {
                                    isLock = true;
                                    Navigator.pop(context);

                                    isLock = false;
                                  }
                                },
                                buttonNameTwo: "Yes",
                                ontabButtonTwo: () async {
                                  if (!isLock) {
                                    isLock = true;

                                    reports?.deletedBy = null;
                                    reports?.deletedDate = null;
                                    reports?.deletedReason = null;
                                    reports?.deletedUserName = null;

                                    if (showInventory) {
                                      await stocksFunction.updateStockForDeleteSales(reports?.salesId ?? '');
                                    }

                                    await revertDeletedRecord(reports);
                                    Navigator.of(context).pop();
                                    Navigator.of(context).pop();
                                    Navigator.of(context).pop();
                                    Navigator.pushReplacement(
                                        context,
                                        MaterialPageRoute(
                                            builder: (context) => ReportsPage(
                                                  isDeleteReport: true,
                                                )));
                                    // Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsPage()));
                                    isLock = false;
                                  }
                                },
                              );
                            },
                          );
                        },
                      );
                    },
                  ),
                ),
              ]
            ];
          },
        ),
      ],
    );
  }

  revertDeletedRecord(Sales? sales) async {
    await salesDetailsDBScript.revertSalesDeleted(sales?.salesId, sales: sales);
    await salesTransDBScript.updateSalesTransactionRevert(sales?.salesId);
    await salesTypeDB.updateSalesTypeForRevertBySalesID(sales?.salesId);
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBigLayout = width >= tabletWidth;
    return Scaffold(
        backgroundColor: secondaryTextColor,
        appBar: BaseAppBar(
          title: text(
            reports?.invoiceId != null ? "View Reports" : "View Order",
            textColor: secondaryTextColor,
            fontSize: textSizeNormal,
            fontFamily: fontMedium,
          ),
          appBar: AppBar(),
          widgets: reports?.invoiceId != null && screenName != "SalesQueue"
              ? <Widget>[
                  PermissionFunctions.checkPermission(199, viewToast: false) ? iconTopWidget(context) : SizedBox.shrink(),
                ]
              : [],
        ),
        body: isLoading
            ? Center(
                child: Container(
                child: CircularProgressIndicator(),
              ))
            : SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    reports?.invoiceId != null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Padding(
                                padding: EdgeInsets.only(top: 10.0),
                                child: TextWidget("Ref No: " + (reports?.invoiceId ?? ""),
                                    textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                              ),
                              // reports.invoiceNo != null && !isResetInvoice && reports.invoiceNo.isNotEmpty
                              //     ? Padding(
                              //         padding: EdgeInsets.only(top: 10.0),
                              //         child: TextWidget("Bill No: ${reports.paymentInvoice} ",
                              //             textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                              //       )
                              //     : Container(),
                              // ignore: sdk_version_set_literal
                              if (reports?.paymentInvoice != null && !isResetInvoice && reports?.paymentInvoice?.isNotEmpty == true) ...{
                                Padding(
                                    padding: EdgeInsets.only(top: 10),
                                    child: Row(
                                      children: [
                                        TextWidget("Bill No:"),
                                        fnUtilities.paymentInvoicewidget(context, "${fnUtilities.getPaymentInvoiceNo(selectedIndex ?? 0, reports)}")
                                      ],
                                    ))
                              }
                            ],
                          )
                        : Container(),
                    if (reports?.onlinePlatformID != null && reports?.onlineRefNo != null) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 10.0),
                            child: TextWidget("Online No: " + (reports?.onlineRefNo ?? ""),
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                        ],
                      )
                    ],
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("Date: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("${dateFormate.toString()}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                        ),
                      ],
                    ),
                    // ignore: sdk_version_set_literal
                    if (widget.isDeleteReport && reports?.deletedBy != null && reports?.deletedDate != null) ...{
                      Container(
                        width: width * 0.9,
                        padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 4.0),
                        child: labelValueBuilderWidget("Deleted By : ", reports?.deletedUserName != null ? reports?.deletedUserName : ' '),
                      ),
                      Container(
                        width: width * 0.9,
                        padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 5.0),
                        child: labelValueBuilderWidget(
                            "Deleted Date : ", reports?.deletedDate != null ? printerUtilities.formatDate(reports?.deletedDate ?? '') : ' '),
                      ),
                      Container(
                          width: width * 0.9,
                          padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 5.0),
                          child: RichText(
                            text: TextSpan(
                              text: 'Deleted Reason: ',
                              style: black14RegularTextStyle.copyWith(fontWeight: FontWeight.w500),
                              children: <TextSpan>[
                                TextSpan(
                                  text: reports?.deletedReason ?? '',
                                  style: black14RegularTextStyle.copyWith(fontWeight: FontWeight.w400),
                                ),
                              ],
                            ),
                          ))
                    },
                    SizedBox(
                      height: 5,
                    ),
                    if (reports?.shiftName != null && !widget.isDeleteReport && reports!.shiftName!.isNotEmpty) ...[
                      Container(
                        width: width * 0.9,
                        padding: const EdgeInsets.symmetric(horizontal: 2.0, vertical: 5.0),
                        child: labelValueBuilderWidget("Shift Name: ", reports?.shiftName != null ? reports?.shiftName : ''),
                      )
                    ],

                    if (salesCategoryName != null) ...[
                      Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 5.0),
                            child: TextWidget("Category : ",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 5.0),
                            child: TextWidget(salesCategoryName ?? '',
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 5,
                      ),
                    ],
                    reports?.customerName != null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: EdgeInsets.only(left: 0.0),
                                    child: TextWidget("Name: ",
                                        textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(left: 0.0),
                                    child: TextWidget("${reports?.customerName}",
                                        textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                                  ),
                                ],
                              ),
                              reports?.mobileNo != null
                                  ? Row(
                                      mainAxisAlignment: MainAxisAlignment.start,
                                      children: [
                                        Padding(
                                          padding: EdgeInsets.only(left: 0.0),
                                          child: TextWidget("Mobile: ",
                                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                                        ),
                                        Padding(
                                          padding: EdgeInsets.only(left: 0.0),
                                          child: TextWidget("${reports?.mobileNo}",
                                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                                        ),
                                      ],
                                    )
                                  : Container(),
                            ],
                          )
                        : Container(),
                    SizedBox(
                      height: 10,
                    ),
                    if (widget.isAdvanceOrderEnabled) ...[
                      Padding(
                        padding: EdgeInsets.only(left: 0.0),
                        child: TextWidget(
                            reports?.deliveryDateAndTime != null ? AdvanceOrderWidget.convertDateTime(reports?.deliveryDateAndTime ?? "") : '',
                            textColor: primaryTextColor,
                            fontFamily: fontBold,
                            fontSize: textSizeSmall,
                            isCentered: true),
                      ),
                    ],
                    SizedBox(
                      height: reports?.fkTableID != null ? 10 : 0,
                    ),
                    reports?.fkTableID != null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              TextWidget("Table :  ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                              TextWidget("${reports?.tableName}",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                            ],
                          )
                        : Container(),
                    SizedBox(
                      height: reports?.fkTableID != null ? 10 : 0,
                    ),
                    reports?.discountPrice != null
                        ? Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              TextWidget("Discount :  ",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                              TextWidget("Rs.${double.tryParse(reports?.discountPrice ?? '0')?.round()}",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                            ],
                          )
                        : Container(),
                    SizedBox(
                      height: 5.0,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        if (paymentMappingList.isNotEmpty)
                          GestureDetector(
                            onTap: () {
                              amountHistoryDialog(context);
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: 10.0),
                              child: Container(
                                  padding: EdgeInsets.symmetric(horizontal: 5.0),
                                  alignment: Alignment.center,
                                  height: width >= tabletWidth ? 50 : 40,
                                  child: text("Payment history", textColor: primaryTextColor, fontSize: textSizeSMedium, isCentered: false),
                                  decoration: boxDecoration(bgColor: transparentColor, radius: 8.0, color: lightGray)),
                            ),
                          ),
                        if (widget.isAdvanceOrderEnabled && orderNote?.note != null)
                          GestureDetector(
                            onTap: () {
                              showOrderNotesDialog();
                            },
                            child: Padding(
                              padding: EdgeInsets.only(right: 10.0),
                              child: Container(
                                  alignment: Alignment.center,
                                  height: width >= tabletWidth ? 50 : 40,
                                  width: 80,
                                  child: text("Notes", textColor: primaryTextColor, fontSize: textSizeSMedium, isCentered: false),
                                  decoration: boxDecoration(bgColor: transparentColor, radius: 8.0, color: lightGray)),
                            ),
                          )
                      ],
                    ),
                    // },
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      height: height / 2.5,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                        boxShadow: <BoxShadow>[BoxShadow(color: Colors.black12, blurRadius: 5.0, offset: Offset(0.75, 0.75))],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(8.0),
                        child: Column(
                          children: [
                            Card(
                              child: Container(
                                color: Colors.white10,
                                width: width,
                                height: 40,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    Container(
                                      alignment: Alignment.centerLeft,
                                      width: width / 2.5,
                                      child: TextWidget("Product", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ),
                                    Container(
                                      alignment: Alignment.centerLeft,
                                      width: width / 9.5,
                                      child: TextWidget("Qty", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ),
                                    Container(
                                      alignment: Alignment.center,
                                      width: width / 3,
                                      child: TextWidget("Amount(Rs)", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Expanded(
                              child: ListView.builder(
                                // physics: NeverScrollableScrollPhysics(),
                                itemCount: transactionList.length,
                                shrinkWrap: true,
                                padding: EdgeInsets.only(right: 0),
                                itemBuilder: (context, index) {
                                  var item = transactionList[index];
                                  return InkWell(
                                    child: Card(
                                      child: Container(
                                        height: 40,
                                        width: width,
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                                          children: [
                                            Container(
                                              child: Text(
                                                item.name ?? "",
                                                textAlign: TextAlign.left,
                                              ),
                                              width: width / 2.5,
                                            ),
                                            Container(
                                              child: Text(
                                                fnUtilities.checkQtyDoubleValue(item.qty.toString()),
                                                textAlign: TextAlign.left,
                                              ),
                                              width: width / 9.5,
                                            ),
                                            Container(
                                              child: Text(
                                                "${double.parse(item.amountWithoutGst ?? "0").round()}  ",
                                                textAlign: TextAlign.right,
                                              ),
                                              width: width / 3,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
        bottomSheet: Container(
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    if (!orderFromName.isEmptyOrNull) ...[
                      Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 10.0),
                            child: TextWidget("Order From: ",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 10.0),
                            child: TextWidget(orderFromName ?? "",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                          ),
                        ],
                      ),
                    ],
                    Container(),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("Total Items: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("${transactionList.length}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("Total Qty: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("${fnUtilities.checkQtyDoubleValue(totalQuantity.toString())}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                      ],
                    ),
                  ],
                ),
                // paymentTypeList(),
                if (reports?.paymentCategoryID != 4) gstSectionWidget(),
                Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                        child:
                            TextWidget("Total Amount: ", textColor: appThemeColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                        child: TextWidget("Rs.${totalAmount.round()}",
                            textColor: appThemeColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                      ),
                    ],
                  ),
                ),
                // paymentMappingList.isNotEmpty
                //     ? Center(
                //         child: GestureDetector(
                //           onTap: () {
                //             amountHistoryDialog(context);
                //           },
                //           child: Padding(
                //             padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                //             child: Text(
                //               'Payment history',
                //               style: TextStyle(
                //                 color: primaryTextColor,
                //                 decoration: TextDecoration.underline,
                //                 fontSize: 16,
                //                 fontWeight: FontWeight.bold,
                //               ),
                //             ),
                //           ),
                //         ),
                //       )
                //     : SizedBox.shrink()
              ],
            ),
          ),
        ));
  }

  void showOrderNotesDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: orderNote?.note != "" ? CrossAxisAlignment.start : CrossAxisAlignment.center,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  text(
                    'Notes',
                    textColor: primaryTextColor,
                    fontSize: textSizeNormal,
                    fontWeight: FontWeight.w700,
                    isCentered: true,
                  ),
                  // Flexible(
                  //   child: Column(
                  //     crossAxisAlignment: CrossAxisAlignment.start,
                  //     children: [
                  //       text(
                  //         "Created Date: ",
                  //         textColor: primaryTextColor,
                  //         fontSize: textSizeSmall,
                  //         fontWeight: FontWeight.w600,
                  //         isCentered: true,
                  //       ),
                  //       text(
                  //         orderNote?.createdDate != null
                  //             ? "${fnUtilities.convertDate(orderNote?.createdDate)}(${fnUtilities.getTime(orderNote?.createdDate)})"
                  //             : "${fnUtilities.convertDate(orderNote?.updatedDate)}(${fnUtilities.getTime(orderNote?.updatedDate)})",
                  //         textColor: primaryTextColor,
                  //         fontSize: 12.0,
                  //         fontWeight: FontWeight.w500,
                  //         isCentered: false,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              ),
              Divider(
                thickness: 2.0,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(vertical: 4.0),
                child: Center(
                  child: Text(
                    orderNote?.note != "" ? orderNote?.note ?? '' : "No notes",
                    style: TextStyle(
                      color: primaryTextColor,
                      fontSize: textSizeNormal,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ),
              )
            ],
          ),
          actions: <Widget>[
            Center(
              child: Container(
                height: 40,
                margin: EdgeInsets.all(5),
                decoration: BoxDecoration(
                  color: secondaryTextColor,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: lightGreyColor),
                ),
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Close',
                    style: TextStyle(
                      color: appThemeColor,
                      fontSize: textSizeSMedium,
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
              ),
            )
          ],
        );
      },
    );
  }

  labelValueBuilderWidget(
    String label,
    dynamic value,
  ) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          label,
          style: black14RegularTextStyle,
        ),
        Text(
          value,
          style: black14RegularTextStyle,
        )
      ],
    );
  }

  /// To show the payment details information for sale
  paymentTypeList() {
    if (paymentMappingList.isNotEmpty && paymentMappingList != []) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 10.0),
            child: TextWidget("Payment Types", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLargeMedium, isCentered: true),
          ),
          ListView.builder(
            itemCount: paymentMappingList.length,
            scrollDirection: Axis.vertical,
            physics: NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (BuildContext context, index) {
              var item = paymentMappingList[index];
              return Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      TextWidget("${item.paymentName} : ",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                      SizedBox(
                        width: 10,
                      ),
                      TextWidget("Rs.${fnUtilities.checkQtyDoubleValue(item.amount.toString())}",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                      Spacer(),
                      TextWidget(item.createdDate != null ? fnUtilities.convertDate(item.createdDate.toString()) : "",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: false),
                    ],
                  ),
                ],
              );
            },
          )
        ],
      );
    } else {
      return SizedBox.shrink();
    }
  }

  gstSectionWidget() {
    double cgstAmount = 0.0;
    double sgstAmount = 0.0;

    if (transactionList.isNotEmpty) {
      cgstAmount = transactionList.map((element) => element.cGstAmount.toDouble()).reduce((value, element) => value + element);
      sgstAmount = transactionList.map((element) => element.sGstAmount.toDouble()).reduce((value, element) => value + element);

      if (cgstAmount > 0 && sgstAmount > 0)
        return Padding(
          padding: const EdgeInsets.only(left: 0.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Divider(
                thickness: 2.0,
              ),
              Row(
                children: [
                  Text(
                    "CGST (Rs) : ",
                    style: TextStyle(
                      color: primaryTextColor,
                      fontFamily: fontBold,
                      fontSize: textSizeMedium,
                    ),
                  ),
                  Text(
                    "${fnUtilities.checkQtyDoubleValue(cgstAmount.toStringAsFixed(2))}",
                    style: TextStyle(
                      color: primaryTextColor,
                      fontFamily: fontBold,
                      fontSize: textSizeMedium,
                    ),
                  ),
                ],
              ),
              Row(
                children: [
                  Text(
                    "SGST (Rs) : ",
                    style: TextStyle(
                      color: primaryTextColor,
                      fontFamily: fontBold,
                      fontSize: textSizeMedium,
                    ),
                  ),
                  Text(
                    "${fnUtilities.checkQtyDoubleValue(sgstAmount.toStringAsFixed(2))}",
                    style: TextStyle(
                      color: primaryTextColor,
                      fontFamily: fontBold,
                      fontSize: textSizeMedium,
                    ),
                  ),
                ],
              )
            ],
          ),
        );
      // else if (!enableTax && cgstAmount.toInt() > 0 && sgstAmount.toInt() > 0)
      //   return Padding(
      //     padding: const EdgeInsets.only(left: 0.0),
      //     child: Column(
      //       mainAxisAlignment: MainAxisAlignment.start,
      //       crossAxisAlignment: CrossAxisAlignment.start,
      //       mainAxisSize: MainAxisSize.min,
      //       children: [
      //         Divider(
      //           thickness: 2.0,
      //         ),
      //         Row(
      //           children: [
      //             TextWidget("CGST (Rs) :", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
      //             TextWidget("${fnUtilities.checkQtyDoubleValue(cgstAmount.toString())}",
      //                 textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
      //           ],
      //         ),
      //         Row(
      //           children: [
      //             TextWidget("SGST (Rs) :", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
      //             SizedBox(
      //               width: 10,
      //             ),
      //             TextWidget("${fnUtilities.checkQtyDoubleValue(sgstAmount.toString())}",
      //                 textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
      //           ],
      //         )
      //       ],
      //     ),
      //   );
      else
        return SizedBox.shrink();
    } else {
      return SizedBox.shrink();
    }
  }

  ///Alert dialog to choose print for kot or bill
  printForAlertDialog() {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              content: Container(
                height: 100,
                width: isBigLayout ?? false ? width * 0.35 : width * 0.8,
                child: Column(
                  children: [
                    SizedBox(
                      height: 20,
                    ),
                    Text("Choose Print Type"),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: widget.isDeleteReport ? MainAxisAlignment.center : MainAxisAlignment.spaceBetween,
                      children: [
                        GestureDetector(
                          onTap: () async {
                            if (multiPrint) {
                              reportPrinter = printerUtilities.getReportPrinter();
                              await printerUtilities.initPrint(reportPrinter);
                              await reportPrint.startReportPrint(context, reports, transactionList);
                              await Future.delayed(Duration(seconds: 1));
                              await printerUtilities.disconnect();
                            } else {
                              reportPrint.startReportPrint(context, reports, transactionList);
                            }
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 40,
                            width: isBigLayout ?? false ? width * 0.15 : width * 0.3,
                            child: TextWidget("Bill", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        widget.isDeleteReport
                            ? SizedBox.shrink()
                            : GestureDetector(
                                onTap: () async {
                                  if (multiPrint) {
                                    reportPrinter = printerUtilities.getReportPrinter();
                                    await printerUtilities.initPrint(reportPrinter);
                                    await salesPrint.startKotReportPrint(context, reports, transactionList);
                                    await Future.delayed(Duration(seconds: 1));
                                    await printerUtilities.disconnect();
                                  } else {
                                    salesPrint.startKotReportPrint(context, reports, transactionList);
                                  }
                                  Navigator.pop(context);
                                },
                                child: Container(
                                  alignment: Alignment.center,
                                  height: 40,
                                  width: isBigLayout ?? false ? width * 0.15 : width * 0.3,
                                  child: TextWidget("KOT", textColor: secondaryTextColor, isCentered: true),
                                  decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                ),
                              ),
                      ],
                    )
                  ],
                ),
              ),
            );
          });
        });
  }
//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  salesReportTextBuildFunction() async {
    String data = "";
    var invoiceExtension = reports?.invoiceExtension != null ? reports?.invoiceExtension : "";
    data += "\nRef No: ${reports?.invoiceId}";
    // data += reports.invoiceNo != null && !isResetInvoice ? "\nBill No: ${reports.invoiceNo}" : "";
    data += reports?.paymentInvoice != null && !isResetInvoice ? "\nBill No: ${(reports?.paymentInvoice ?? "") + (invoiceExtension ?? "")}" : "";
    // data += reports.fkTableID != null ? "\nTABLE ORDER" : "\nTAKE AWAY";
    data += "\n";
    data += reports?.customerName != null ? "\nName: ${reports?.customerName}" : "";
    data += "\nServed by: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}";
    data += "\nDate: ${dateFormate.toString()}";
    if (widget.isShifWiseReport || reports?.shiftName != null) {
      data += "\nShift Name : ${reports?.shiftName == null ? "" : reports?.shiftName}";
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";

    if (reports?.deletedBy != null && reports?.deletedDate != null && reports?.deletedReason != null && reports?.deletedUserName != null) {
      data += "\nDeleted By: ${reports?.deletedUserName == null ? "" : reports?.deletedUserName}";

      data += "\nDeleted Date: ${reports?.deletedDate == null ? "" : printerUtilities.formatDate(reports?.deletedDate ?? '')}";
      data += "\nDeleted Reason: ${reports?.deletedReason == null ? "" : reports?.deletedReason}";
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";

    data += "\nProduct       Qty      Rate     Amount";
    var totalCGST = 0.0;
    var totalSGST = 0.0;
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        for (int list = 0; list < groupList[i].length; list++) {
          if (groupList[i][list].cGstAmount != null) {
            totalCGST += double.parse(groupList[i][list].cGstAmount);
          }
          if (groupList[i][list].sGstAmount != null) {
            totalSGST += double.parse(groupList[i][list].sGstAmount);
          }
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].name.toString(), 8);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              data +=
                  "\n${productSplit[p].padRight(9)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].qty.toString()).padLeft(9)} ${double.parse(groupList[i][list].price.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i][list].amountWithoutGst.toString()).toStringAsFixed(2).padLeft(12)}";
            } else {
              data += "\n${productSplit[p].padRight(9) + " ".padLeft(9) + " ".padLeft(8) + " ".padLeft(8) + " ".padLeft(12)}";
            }
          }
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += reports?.discountPrice != null ? "\nTotal Discount: ${double.parse(reports?.discountPrice ?? "").toStringAsFixed(2).padLeft(8)}" : "";
    data += (reports?.isIGST == null || reports?.isIGST == false) && totalCGST != 0.0 ? "\nCGST: ${totalCGST.toStringAsFixed(2).padLeft(8)}" : "";
    data += (reports?.isIGST == null || reports?.isIGST == false) && totalSGST != 0.0 ? "\nSGST: ${totalSGST.toStringAsFixed(2).padLeft(8)}" : "";
    data += (reports?.isIGST != null && reports?.isIGST == true) && totalSGST != 0.0
        ? "\nIGST: ${(totalCGST + totalSGST).toStringAsFixed(2).padLeft(8)}"
        : "";
    data += "\nTotal Items: ${transactionList.length.toString().padLeft(10)}";
    data += "\nTotal Qty: ${await fnUtilities.checkQtyDoubleValue(totalQuantity.toString()).padLeft(10)}";
    data += "\nTotal Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}";
    await printerUtilities.shareCommonReport(data, "Sale");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pdfReportBuildFunction(String? currentDate, {bool isDownload = false}) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      String fileName = widget.isDeleteReport ? "Sales_Deleted_Full_Report" : "Sales_Full_Report";
      await sharePDF.savePDFFormat(sharePDF.pdfCommonSalesReportBodyBuildFunction(reports, transactionList), fileName, currentDate,
          context: isDownload ? context : null);
      LoadingAlertWidget.onStopping();
    } catch (e) {
      LoadingAlertWidget.onStopping();
    }
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//

  amountHistoryDialog(BuildContext context) {
    double totalPaidAmount = 0.0;
    Map<String, List<SalesPaymentMapping>> groupedPayments = groupPaymentsByDateAndOrderByAsc(paymentMappingList);
    log(groupedPayments);
    return showModalBottomSheet(
        isScrollControlled: true,
        isDismissible: true,
        context: context,
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        builder: (context) {
          totalPaidAmount = 0.0;
          return StatefulBuilder(builder: (context, setState) {
            return Padding(
              padding: EdgeInsets.only(
                  left: width > tabletWidth ? width * 0.3 : 0.0,
                  right: width > tabletWidth ? width * 0.3 : 0.0,
                  top: 8.0,
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                padding: EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.0),
                    topRight: Radius.circular(16.0),
                  ),
                ),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Container(
                          width: width,
                          alignment: Alignment.topLeft,
                          child: Text(
                            "Payment Info",
                            style: headingTextStyle3.copyWith(color: primaryTextColor),
                          )),
                      SizedBox(
                        height: 10.0,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          TextWidget("Total Amount: ", textColor: appThemeColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                          TextWidget("Rs.${totalAmount.round()}",
                              textColor: appThemeColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                        ],
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          Container(
                              width: width * 0.25,
                              alignment: Alignment.topLeft,
                              child: Text(
                                "Date",
                                style: black16BoldTextStyle,
                              )),
                          Container(width: width * 0.3, alignment: Alignment.topLeft, child: Text("Paid (₹)", style: black16BoldTextStyle)),
                          Container(width: width * 0.25, alignment: Alignment.topLeft, child: Text("Balance (₹)", style: black16BoldTextStyle))
                        ],
                      ),
                      Divider(
                        thickness: 2.0,
                        color: lightGray,
                      ),
                      SizedBox(
                        height: 10.0,
                      ),
                      SizedBox(
                        height: 300,
                        width: width,
                        child: ListView.builder(
                          itemCount: groupedPayments.length,
                          itemBuilder: (context, index) {
                            String groupedKey = groupedPayments.keys.elementAt(index);
                            String date = groupedKey.isEmptyOrNull
                                ? (reports?.date == null || reports?.date == "")
                                    ? ""
                                    : DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(reports!.date!))
                                : DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(groupedKey));
                            List<SalesPaymentMapping> payments = groupedPayments[groupedKey] ?? [];
                            double totalPaid = getTotalPaid(payments);
                            totalPaidAmount = totalPaidAmount + totalPaid;
                            balance = (totalAmount - totalPaidAmount).round();

                            return Column(
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(width: width * 0.25, alignment: Alignment.centerLeft, child: Text(date)),
                                    Container(
                                        width: width * 0.3,
                                        alignment: Alignment.centerLeft,
                                        child: Text('₹' + fnUtilities.checkQtyDoubleValue(totalPaid.toString()))),
                                    Container(
                                        width: width * 0.25,
                                        alignment: Alignment.centerRight,
                                        child: Text('₹' + fnUtilities.checkQtyDoubleValue(balance.round().toString()))),
                                  ],
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                  children: [
                                    SizedBox(
                                      width: width * 0.25,
                                    ),
                                    Container(
                                        width: (width * 0.3) + 10,
                                        child: Wrap(
                                          alignment: WrapAlignment.start,
                                          children: payments
                                              .map(
                                                (payment) => Padding(
                                                  padding: const EdgeInsets.all(2.0),
                                                  child: Text('${payment.paymentName} : ₹${payment.amount}'),
                                                ),
                                              )
                                              .toList(),
                                        )),
                                    SizedBox(
                                      width: width * 0.25,
                                    )
                                  ],
                                ),
                                Divider(
                                  thickness: 0.5,
                                  color: lightGray,
                                ),
                              ],
                            );
                          },
                        ),
                      )
                    ],
                  ),
                ),
              ),
            );
          });
        });
  }

  Map<String, List<SalesPaymentMapping>> groupPaymentsByDateAndOrderByAsc(List<SalesPaymentMapping> payments) {
    Map<String, List<SalesPaymentMapping>> groupedPayments = {};
    for (SalesPaymentMapping payment in payments) {
      if (payment.createdDate != null) {
        String dateTimeWithoutSeconds = payment.createdDate!.substring(0, payment.createdDate!.lastIndexOf(':'));
        if (groupedPayments.containsKey(dateTimeWithoutSeconds)) {
          groupedPayments[dateTimeWithoutSeconds]!.add(payment);
        } else {
          groupedPayments[dateTimeWithoutSeconds] = [payment];
        }
      } else {
        // Handle null created dates
        if (groupedPayments.containsKey("")) {
          groupedPayments[""]!.add(payment);
        } else {
          groupedPayments[""] = [payment];
        }
      }
    }
    return groupedPayments;
  }

  double getTotalPaid(List<SalesPaymentMapping> payments) {
    double totalPaid = 0;
    for (var payment in payments) {
      totalPaid += payment.amount ?? 0;
    }
    return totalPaid;
  }
}
