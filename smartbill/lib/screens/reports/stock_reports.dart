import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';

import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../Repositories/reports_repository.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../model/reports/get_stock_reports.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/colors.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class StockReports extends StatefulWidget {
  @override
  StockReportsState createState() => StockReportsState();
}

class StockReportsState extends State<StockReports> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  FnUtilities fnUtilities = new FnUtilities();
  final dbHelper = DatabaseHelper.instance;
  bool connected = false;
  bool multiPrint = false;
  double width = 0.0;
  double height = 0.0;
  PrinterDevices? reportPrinter;
  bool isLoading = false;
  List<StockReport> stockReportList = [];
  StockReport? stocks;

  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    if (MultiPrintValue == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  ///Stock Report print method
  stockReportsPrint() async {
    if (reportPrinter?.printerFormat == "Default") {
      if (reportPrinter?.paperSize == 58) {
        await stockReportPrintLineBuildFunction();
      } else {
        await stockReportPrintLineBuildFunction80MM();
      }
    } else {
      if (reportPrinter?.paperSize == 58) {
        await stockReportPrintLineBuildFunctionTVS();
      } else {
        await stockReportPrintLineBuildFunctionTVS80MM();
      }
    }
  }

  ///Get Stock reports from Api
  getStockReportsFromAPi() async {
    try {
      setState(() {
        isLoading = true;
      });

      await getStockReports().then((value) {
        if (value.status == 1) {
          setState(() {
            stockReportList = value.result ?? [];
          });
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void initState() {
    initSettings();
    getStockReportsFromAPi();
    super.initState();
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        IconButton(
          iconSize: 25,
          icon: Icon(Icons.print),
          onPressed: () async {
            if (PermissionFunctions.checkPermission(215)) {
              if (multiPrint) {
                reportPrinter = printerUtilities.getReportPrinter();
                await printerUtilities.initPrint(reportPrinter);
                stockReportsPrint();
              } else {
                stockReportsPrint();
              }
            }
          },
          color: Colors.white,
        ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(216)) {
                      Navigator.pop(context);
                      await productReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(217)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(218)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Stock Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[stockReportList.isNotEmpty && PermissionFunctions.checkPermission(215, viewToast: false) ? iconTopWidget() : Container()],
      ),
      body: Container(
        child: stockReportList.length != 0
            ? SingleChildScrollView(
                child: Column(
                  children: <Widget>[
                    Container(
                      padding: EdgeInsets.all(2),
                      alignment: Alignment.center,
                      width: width,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          15.height,
                          isLoading
                              ? Container(
                                  width: width,
                                  child: Center(
                                    child: Container(height: 40, child: CircularProgressIndicator()),
                                  ),
                                )
                              : Container(
                                  width: width,
                                  alignment: Alignment.topCenter,
                                  child: Column(
                                    children: [
                                      Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: <Widget>[
                                          Container(
                                            width: width / 5,
                                            alignment: Alignment.centerLeft,
                                            child: Text("ID", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                          ),
                                          Container(
                                            width: width / 2.3,
                                            alignment: Alignment.centerLeft,
                                            child: Text("Product", style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                                          ),
                                          Container(
                                            width: width / 3,
                                            alignment: Alignment.centerRight,
                                            child: Text(
                                              "Stock",
                                              style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                                            ),
                                          )
                                        ],
                                      ),
                                      SizedBox(
                                        height: 10,
                                      ),
                                      // ignore: sdk_version_ui_as_code
                                      if (stockReportList.isNotEmpty)
                                        ListView.builder(
                                            padding: const EdgeInsets.only(right: 0),
                                            itemCount: stockReportList.length,
                                            shrinkWrap: true,
                                            reverse: true,
                                            physics: NeverScrollableScrollPhysics(),
                                            itemBuilder: (context, index) {
                                              var item = stockReportList[index];
                                              return Container(
                                                alignment: Alignment.centerLeft,
                                                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 2),
                                                child: Column(
                                                  children: [
                                                    Row(
                                                      mainAxisAlignment: MainAxisAlignment.start,
                                                      children: <Widget>[
                                                        Container(
                                                          width: width / 5,
                                                          alignment: Alignment.centerLeft,
                                                          child: Text(
                                                            item.id != null ? item.id.toString() : "",
                                                          ),
                                                        ),
                                                        Container(
                                                          width: width / 2.3,
                                                          alignment: Alignment.centerLeft,
                                                          child: Text(
                                                            item.productName.toString(),
                                                          ),
                                                        ),
                                                        Container(
                                                          width: width / 3,
                                                          alignment: Alignment.centerRight,
                                                          child: Text(
                                                            item.stock.toString(),
                                                          ),
                                                        )
                                                      ],
                                                    ),
                                                    divider()
                                                  ],
                                                ),
                                              );
                                            })
                                    ],
                                  )),
                        ],
                      ),
                    ),
                  ],
                ),
              )
            : Center(
                child: Container(
                  padding: EdgeInsets.all(15.0),
                  child: isLoading
                      ? Container(
                          width: width,
                          child: Center(
                            child: Container(height: 40, child: CircularProgressIndicator()),
                          ),
                        )
                      : TextWidget("No Stocks Available",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                ),
              ),
      ),
    );
  }
//------------------------------------------------Start PrintBuild Function-------------------------------------------------//

  stockReportPrintLineBuildFunction() async {
    String listData = "";
    for (StockReport stock in stockReportList) {
      var productSplit = printerUtilities.splitStringByLength(stock.productName.toString(), 19);
      for (int p = 0; p < productSplit.length; p++) {
        if (p == 0) {
          listData += '''{"text": "${productSplit[p].padRight(20)} ${stock.stock.toString().padLeft(19)}", "size": 0, "alignment": 0},''';
        } else {
          listData += '''{"text": "${productSplit[p].padRight(20) + " ".padLeft(19)}", "size": 0, "alignment": 0},''';
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Stocks Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   {"text": "Product                    Stock", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  stockReportPrintLineBuildFunctionTVS() async {
    String listData = "";
    for (StockReport stock in stockReportList) {
      var productSplit = printerUtilities.splitStringByLength(stock.productName.toString(), 19);
      for (int p = 0; p < productSplit.length; p++) {
        if (p == 0) {
          listData += '''{"text": "${productSplit[p].padRight(20)} ${stock.stock.toString().padLeft(11)}", "size": 0, "alignment": 0},''';
        } else {
          listData += '''{"text": "${productSplit[p].padRight(20) + " ".padLeft(11)}", "size": 0, "alignment": 0},''';
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Stocks Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "Product                  Stock", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  stockReportPrintLineBuildFunction80MM() async {
    String listData = "";
    for (StockReport stock in stockReportList) {
      var productSplit = printerUtilities.splitStringByLength(stock.productName.toString(), 30);
      for (int p = 0; p < productSplit.length; p++) {
        if (p == 0) {
          listData += '''{"text": "${productSplit[p].padRight(31)} ${stock.stock.toString().padLeft(30)}", "size": 0, "alignment": 0},''';
        } else {
          listData += '''{"text": "${productSplit[p].padRight(31) + " ".padLeft(30)}", "size": 0, "alignment": 0},''';
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Stocks Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product                                    Stock", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  stockReportPrintLineBuildFunctionTVS80MM() async {
    String listData = "";
    for (StockReport stock in stockReportList) {
      var productSplit = printerUtilities.splitStringByLength(stock.productName.toString(), 30);
      for (int p = 0; p < productSplit.length; p++) {
        if (p == 0) {
          listData += '''{"text": "${productSplit[p].padRight(31)} ${stock.stock.toString().padLeft(30)}", "size": 0, "alignment": 0},''';
        } else {
          listData += '''{"text": "${productSplit[p].padRight(31) + " ".padLeft(30)}", "size": 0, "alignment": 0},''';
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Stocks Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product                                    Stock", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

//------------------------------------------------End PrintBuild Function-------------------------------------------------//

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  productReportTextBuildFunction() async {
    String data = "";
    data += "\nStocks Report";
    data += "\n";
    data += "\nDate: ${DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.now())}";
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nProduct                          Stock";
    for (StockReport stock in stockReportList) {
      var productSplit = printerUtilities.splitStringByLength(stock.productName.toString(), 25);
      for (int p = 0; p < productSplit.length; p++) {
        if (p == 0) {
          data += "\n${productSplit[p].padRight(26) + stock.stock.toString().padLeft(20)}";
        } else {
          data += "\n${productSplit[p].padRight(26) + stock.stock.toString().padLeft(20)}";
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    await printerUtilities.shareCommonReport(data, "Stocks");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pw.Widget pdfProductReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Wrap(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Stocks Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 40, child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 130, child: pw.Text("Product", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 85, child: pw.Text("Sold", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
            pw.Container(width: 95, child: pw.Text("Stock", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          for (StockReport stocks in stockReportList) stockReportFun(sNo, stocks),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Items :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(stockReportList.length.toString(),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pw.Widget stockReportFun(int sNo, StockReport stock) {
    return pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
      pw.Container(width: 40, child: pw.Text((sNo += 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
      pw.Container(
          width: 40,
          child: pw.Text(stock.id != null ? stock.id.toString() : "", style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
      pw.Container(width: 130, child: pw.Text(stock.productName.toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
      pw.Container(width: 85, child: pw.Text(stock.stocksSold.toString(), textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
      pw.Container(width: 95, child: pw.Text(stock.stock.toString(), textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
    ]);
  }

  pdfReportBuildFunction(String? currentDate, {BuildContext? context}) async {
    String fileName = "Stocks_Report";
    await sharePDF.savePDFFormat(pdfProductReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
