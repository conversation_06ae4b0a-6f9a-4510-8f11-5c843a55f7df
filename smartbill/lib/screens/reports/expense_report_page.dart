// ignore_for_file: non_constant_identifier_names, unused_local_variable, sdk_version_ui_as_code, sdk_version_set_literal
import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/expenses/model/expense_category_model.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../Repositories/reports_repository.dart';
import '../../model/reports/get_expenses_report.dart'; 
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/exp_purchase_report_view.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ExpenseReportsPage extends StatefulWidget {
  @override
  ExpenseReportsPageState createState() => ExpenseReportsPageState();
}

class ExpenseReportsPageState extends State<ExpenseReportsPage> {
  SharePDF sharePDF = SharePDF();
  FnUtilities fnUtilities = FnUtilities();
  PrinterUtilities printerUtilities = PrinterUtilities();
  RolePermissionsUtilities rolePermissions = RolePermissionsUtilities();
  DBOperations dbOperations = DBOperations();
  List<Expenses> expenseReportList = [];
  ExpenseReportModel? expenseReport = ExpenseReportModel();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool isLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalExpenses = 0;
  double totalQty = 0;
  var groupList;
  bool isReportView = false;
  bool isBigLayout = false;
  bool isLock = false;
  bool initPageLoading = false;

  List<ExpensesCategoryModel> expenseCategoryList = [];
  List<DropdownMenuItem<ExpensesCategoryModel>>? dropdownMenuItems;
  ExpensesCategoryModel? selectedExpenseCat;

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked.subtract(Duration(
                hours: picked.hour,
                minutes: picked.minute,
                seconds: picked.second,
                milliseconds: picked.millisecond,
                microseconds: picked.microsecond));
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  getExpenseReport(fromDate, toDate) async {
    setState(() {
      isLoading = true;
    });
    try {
      await getExpenseReportByDatToDate(fromDate, toDate).then((value) {
        if (value.status == 1) {
          expenseReportList = value.result?.reportList ?? [];
          totalExpenses = value.result?.totalExpenses ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = expenseReportList.groupListsBy((element) => element.expCategoryId);
          expenseReport = value.result;
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }
  //------------------------------------------------Start DropDown Function-------------------------------------------------//

  ///Get All Expense Category List data
  getAllExpenseCategoryList() {
    setState(() {
      expenseCategoryList.clear();
      ExpensesCategoryModel model = ExpensesCategoryModel();
      model.expCategoryName = "All";
      expenseCategoryList.add(model);
      expenseCategoryList.addAll(ListUtility.expensesCategoryList);
    });
  }

  //------------------------------------------------End DropDown Function-------------------------------------------------//

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });
    await initSettings();
    await getAllExpenseCategoryList();
    setState(() {
      initPageLoading = false;
    });
  }

  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    isReportView = await PermissionFunctions.checkPermission(237, viewToast: false);
  }

  @override
  void dispose() {
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(238)) {
                      Navigator.pop(context);
                      await expenseReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(239)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(240)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width > tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Expense Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[expenseReportList.isNotEmpty && PermissionFunctions.checkPermission(238, viewToast: false) ? iconTopWidget() : Container()],
      ),
      body: Container(
          padding: EdgeInsets.all(2),
          child: initPageLoading
              ? Center(child: CircularProgressIndicator())
              : Column(
                  children: <Widget>[
                    SizedBox(height: 20),
                    isReportView
                        ? BootstrapRow(children: [
                            BootstrapCol(
                                sizes: 'col-md-8 col-sm-12',
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 10),
                                  child: Row(
                                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                    children: [
                                      InkWell(
                                        onTap: () {
                                          selectDate(context, 'fromDate');
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Text(
                                                  "${fnUtilities.convertDate(fromDate.toString())}",
                                                  style: secondaryTextStyle(),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Icon(Icons.calendar_today)
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                      InkWell(
                                        onTap: () {
                                          selectDate(context, 'toDate');
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Text(
                                                  fnUtilities.convertDate(toDate.toString()),
                                                  style: secondaryTextStyle(),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Icon(Icons.calendar_today)
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                            BootstrapCol(
                                sizes: 'col-md-4 col-sm-12',
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 10.0),
                                  child: Row(
                                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                    children: [
                                      InkWell(
                                        onTap: () async {
                                          if (PermissionFunctions.checkPermission(237)) {
                                            setState(() {
                                              getExpenseReport(fromDate, toDate);
                                            });
                                          }
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          height: 40,
                                          width: isBigLayout ? width * 0.15 : width * 0.98,
                                          child: Text("Submit",
                                              style: TextStyle(
                                                color: secondaryTextColor,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              )),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(5),
                                            color: buttonThemeColor,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                          ])
                        : Container(),
                    isLoading
                        ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                        : expenseReportList.length != 0
                            ? expenseBasedFilterList(expenseReportList)
                            : SizedBox(
                                height: 200,
                                child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Container(
                                      child: Text(
                                        "Reports not found",
                                        style: black16w500,
                                      ),
                                    )),
                              ),
                  ],
                )),
      bottomSheet: initPageLoading
          ? SizedBox()
          : Container(
              height: isPortrait == Orientation.portrait ? height * 0.10 : height / 4,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Grand Total: ",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget expenseBasedFilterList(List<Expenses> item) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(bottom: 100),
        child: ReportViewWidget(
          expenseList: item,
          flag: expense,
        ),
      ),
    );
  }

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  expenseReportTextBuildFunction() async {
    String data = "";
    data += "\nExpense Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\n No     ID         Date     Amount";

    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        data += "\n";
        for (int list = 0; list < groupList[i].length; list++) {
          data +=
              "\n${groupList[i][list].invoiceNo.toString().padLeft(2)} ${groupList[i][list].invoiceId.toString().padLeft(8)} ${fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true)?.padLeft(9)} ${double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(2).padLeft(10)}";
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\nTotal Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}";
    await printerUtilities.shareCommonReport(data, "Expense");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pw.Widget pdfExpenseReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Expense Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(
                //width: 30,
                child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 100, child: pw.Text("Date", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          if (groupList != null)
            for (var i in groupList.keys)
              for (int list = 0; list < groupList[i].length; list++)
                list == 0
                    ? pw.Column(children: [
                        pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                          pw.Container(
                              width: 20,
                              child: pw.Text(groupList[i][list].invoiceNo.toString(),
                                  style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                          pw.Container(
                              width: 90,
                              child: pw.Text(groupList[i][list].invoiceId.toString(),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                          pw.Container(
                              width: 200,
                              child: pw.Column(
                                children: [
                                  pw.Text(
                                    (fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true) ?? ""),
                                    textAlign: pw.TextAlign.right,
                                    style: const pw.TextStyle(fontSize: 10),
                                  ),
                                  // pw.Padding(
                                  //     padding: pw.EdgeInsets.only(top: 5.0, bottom: 5.0),
                                  //     child: pw.Text(groupList[i][list].expCategoryName.toString(),
                                  //         textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                                ],
                              )),
                          pw.Container(
                              width: 95,
                              child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                        ]),
                      ])
                    : pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                        pw.Container(
                            width: 20,
                            child: pw.Text(groupList[i][list].invoiceNo.toString(),
                                style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                        pw.Container(
                            width: 90,
                            child: pw.Text(groupList[i][list].invoiceId.toString(),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                        pw.Container(
                            width: 200,
                            child: pw.Column(
                              children: [
                                pw.Text(
                                  fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true) ?? "",
                                  textAlign: pw.TextAlign.right,
                                  style: const pw.TextStyle(fontSize: 10),
                                ),
                                // pw.Padding(
                                //     padding: pw.EdgeInsets.only(top: 5.0, bottom: 5.0),
                                //     child: pw.Text(groupList[i][list].expCategoryName.toString(),
                                //         textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                              ],
                            )),
                        pw.Container(
                            width: 95,
                            child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                      ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalAmount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfReportBuildFunction(String? currentDate, {BuildContext? context}) {
    String fileName = "Expenses_Report";
    sharePDF.savePDFFormat(pdfExpenseReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
