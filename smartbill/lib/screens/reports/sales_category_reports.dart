// ignore_for_file: non_constant_identifier_names, unused_local_variable, sdk_version_ui_as_code, sdk_version_set_literal

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/reports_repository.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_sales_category_report.dart';
import 'package:smartbill/model/sales_reports_printer.dart';
import 'package:smartbill/screens/reports/reports_list_view.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/printer_utilities/report_print.dart';
import '../../Repositories/download_reports_repository.dart';
import '../../main.dart';
import '../../model/reports.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/date_range_picker_widget.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/reports_db_script.dart';
import '../../utils/error_logs/error_logs_function.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../salescategory/model/sales_category_model.dart';

class SalesCategoryReportPage extends StatefulWidget {
  @override
  _SalesCategoryReportPageState createState() => _SalesCategoryReportPageState();
}

class _SalesCategoryReportPageState extends State<SalesCategoryReportPage> with SingleTickerProviderStateMixin {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  ReportPrint reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  PaymentTypeReport paymentSalesReports = new PaymentTypeReport();
  DBOperations dbOperations = new DBOperations();
  SalesReportsPrinter salesReportsPrinter = new SalesReportsPrinter();
  final dbHelper = DatabaseHelper.instance;
  List<DropdownMenuItem<SalesCategoryModel>>? dropdownMenuItems;
  List<Sales> salesCategoryReportList = [];
  List<SalesCategoryModel> salesCategoryList = [];
  SalesCategoryReports? salesCategoryReport = SalesCategoryReports();
  SalesCategoryModel? selectedSalesCategory;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  bool isLoading = false;
  bool initPageLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalSales = 0;
  double totalQty = 0;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isBigLayout = false;
  bool isLock = false;
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  bool isTwoDaysRecord = false;
  TabController? controller;
  int selectIndex = 0;

  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    isReportView = await PermissionFunctions.checkPermission(231, viewToast: false);
    if (MultiPrintValue == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  getSalesCategoryReport(fromDate, toDate, salesCategoryId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getSalesCategoryReportByDateToDate(fromDate, toDate, salesCategoryId: salesCategoryId).then((value) {
        if (value.status == 1) {
          salesCategoryReportList = value.result?.reportList ?? [];
          totalSales = value.result?.totalSales ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = salesCategoryReportList.groupListsBy((element) => element.salesCategoryId);
          salesCategoryReport = value.result;
        } else {
          showToast(value.message);
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "getSalesCategoryReport - SalesCategoryReportPage");
      setState(() {
        isLoading = false;
      });
    }
  }

  getSalesCategoryReportFromLocale(fromDate, toDate, salesCategoryId) async {
    setState(() {
      isLoading = true;
    });
    try {
      var now = new DateTime.now();
      fromDate = now.subtract(
          Duration(days: 2, hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      toDate = now.subtract(
          Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      var result = await ReportDetailsDB.getCommonReports(fromDate, toDate, keyName: db_salesCategoryId, keyValue: salesCategoryId);
      if (result != null) {
        salesCategoryReport = SalesCategoryReports.fromJson(result);
        salesCategoryReportList = salesCategoryReport?.reportList ?? [];
        totalSales = salesCategoryReport?.totalSales ?? 0;
        totalAmount = salesCategoryReport?.totalAmount ?? 0;

        ///Assign sales category name from the ListUtility.salesCategory
        ///into list of sales report list based on salesCategorId
        for (Sales model in salesCategoryReportList) {
          if (model.salesCategoryId != null) {
            var salesCategoryModel =
                ListUtility.salesCategoryList.firstWhere((element) => element.salesCategoryId == model.salesCategoryId, orElse: null);
            model.salesCatName = salesCategoryModel != null ? salesCategoryModel.salesCatName : "None";
          }
        }
        groupList = salesCategoryReportList.groupListsBy((element) => element.salesCategoryId);
      }

      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ErrorLogsFunction.insertErrorLogs(e, "getSalesCategoryReportFromLocale - SalesCategoryReportPage");
    }
  }
  //------------------------------------------------Start DropDown Function-------------------------------------------------//

  //GET ALL DETAILS BY PRODUCT
  queryAllPaymentTypeDetails() async {
    setState(() {
      salesCategoryList.clear();
      SalesCategoryModel model = SalesCategoryModel();
      model.salesCatName = "All";
      model.salesCategoryId = "";
      salesCategoryList.add(model);
      salesCategoryList.addAll(ListUtility.salesCategoryList.where((element) => element.status != 2 && element.salesCatName != null));
    });
    if (salesCategoryList.length > 0) {
      setState(() {
        dropdownMenuItems = buildDropdownMenuItems(salesCategoryList);
        selectedSalesCategory = dropdownMenuItems?[0].value;
      });
    }
  }

  //PRODUCT LIST VIEW
  List<DropdownMenuItem<SalesCategoryModel>> buildDropdownMenuItems(List salesCategory) {
    List<DropdownMenuItem<SalesCategoryModel>> items = [];
    for (SalesCategoryModel model in salesCategory) {
      items.add(DropdownMenuItem(value: model, child: Text(model.salesCatName ?? '')));
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownItem(SalesCategoryModel? salesCategory) {
    setState(() {
      selectedSalesCategory = salesCategory;
    });
  }

  //------------------------------------------------End DropDown Function-------------------------------------------------//

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });

    await initSettings();
    await initPageDetails();
    await queryAllPaymentTypeDetails();

    setState(() {
      initPageLoading = false;
    });
  }

  initPageDetails() async {
    setState(() {
      initPageLoading = true;
    });

    await initFunctionCall(selectIndex);

    controller = TabController(length: 2, vsync: this);
    controller?.addListener(() {
      setState(() {
        if (controller?.index == 0) {
          selectIndex = 0;
        }

        if (controller?.index == 1) {
          selectIndex = 1;
          fromDate = DateTime.now();
          toDate = DateTime.now();
          salesCategoryReportList = [];
          salesCategoryReport = new SalesCategoryReports();
          totalSales = 0;
          totalAmount = 0;
        }
        initFunctionCall(selectIndex);
      });
    });

    setState(() {
      initPageLoading = false;
    });
  }

  initFunctionCall(int index) async {
    if (index == 0) {
      await getSalesCategoryReportFromLocale(fromDate, toDate, selectedSalesCategory?.salesCategoryId ?? "");
      isTwoDaysRecord = true;
    } else {
      isTwoDaysRecord = false;
    }
  }

  //PRINTER REPORT FUNCTION
  salesCategoryReportPrint(SalesCategoryReports salesCategoryReport) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
      }
      salesReportsPrinter.SalesReportsPrinterList = salesCategoryReportList;
      salesReportsPrinter.fromDate = fromDate.toString();
      salesReportsPrinter.toDate = toDate.toString();
      salesReportsPrinter.totAmount = salesCategoryReport.totalAmount.toString();
      salesReportsPrinter.salesNo = salesCategoryReport.totalSales.toString();
      salesReportsPrinter.isShort = false;
      if (multiPrint == true) {
        reportPrinter = printerUtilities.getReportPrinter();
        await printerUtilities.initPrint(reportPrinter);

        await Future.delayed(Duration(seconds: 2));

        await reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
        disconnect();
        LoadingAlertWidget.onStopping();
      } else {
        reportPrint.startFullReportPrint(context, salesReportsPrinter, isPaymentReport: true);
        LoadingAlertWidget.onStopping();
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "salesCategoryReportPrint - SalesCategoryReport");
    }
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName) async {
    try {
      LoadingAlertWidget.onLoading(context);

      // await requestStoragePermission();
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
    }
  }

  //BLUETOOTH DISCONNECTION
  void disconnect() {
    try {
      bluetooth.disconnect();
    } catch (e) {}
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        // IconButton(
        //   iconSize: 25,
        //   icon: Icon(Icons.print),
        //   onPressed: () async {
        //     salesCategoryReportPrint(salesCategoryReport);
        //   },
        //   color: Colors.white,
        // ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report For Sales Category"),
                  onTap: () async {
                    Navigator.pop(context);
                    downloadReportsRequest = DownloadReportsRequest();
                    downloadReportsRequest.fromDate = fromDate.toString();
                    downloadReportsRequest.toDate = toDate.toString();
                    downloadReportsRequest.salesCategoryId = selectedSalesCategory?.salesCategoryId;
                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                    await downloadReportFunction(downloadReportsRequest, "/api/v1/sales/salescategoryreport", "Sales Category Report");
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        appBar: AppBar(
          title: text("Sales Category Report", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          actions: <Widget>[salesCategoryReportList.isNotEmpty ? iconTopWidget() : Container()],
          bottom: TabBar(
            onTap: (index) {},
            controller: controller,
            isScrollable: true,
            labelStyle: primaryTextStyle(),
            indicatorColor: redColor,
            physics: BouncingScrollPhysics(),
            labelColor: secondaryTextColor,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      '2 Days',
                    ),
                  ],
                ),
              ),
              // if (isReportView)
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.supervised_user_circle,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      'Date to Date',
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        body: TabBarView(
          controller: controller,
          children: [
            Container(
              padding: EdgeInsets.all(2),
              child: initPageLoading
                  ? Center(child: CircularProgressIndicator())
                  : salesCategoryList.length > 0
                      ? SingleChildScrollView(
                          child: Column(
                            children: <Widget>[
                              isLoading
                                  ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                                  : salesCategoryReportList.length != 0
                                      ? Container(
                                          width: width,
                                          alignment: Alignment.topCenter,
                                          child: Column(
                                            children: [
                                              if (salesCategoryReportList.isNotEmpty) ...{
                                                for (var i in groupList.keys) ...{
                                                  if (groupList[i].length != 0) ...{
                                                    Padding(
                                                      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                      child: Column(
                                                        children: <Widget>[
                                                          if (groupList[i][0].salesCatName != null) ...{
                                                            Container(
                                                              color: Colors.black12,
                                                              padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                                              child: Row(
                                                                children: [
                                                                  Padding(
                                                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                                                    child: Container(
                                                                      alignment: Alignment.centerLeft,
                                                                      child: Text(
                                                                        groupList[i][0].salesCatName ?? "None",
                                                                        style: TextStyle(
                                                                          fontSize: textSizeMedium,
                                                                          fontWeight: FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          } else ...{
                                                            SizedBox()
                                                          },
                                                          salesCategoryBasedFilterList(groupList[i]),
                                                        ],
                                                      ),
                                                    )
                                                  } else ...{
                                                    SizedBox()
                                                  },
                                                },
                                                SizedBox(
                                                  height: 100,
                                                )
                                              }
                                            ],
                                          ))
                                      : SizedBox(
                                          height: 200,
                                          child: Align(
                                              alignment: Alignment.bottomCenter,
                                              child: Container(
                                                child: Text(
                                                  "Reports not found",
                                                  style: black16w500,
                                                ),
                                              )),
                                        ),
                            ],
                          ),
                        )
                      : Center(
                          child: Container(
                            padding: EdgeInsets.all(15.0),
                            child: TextWidget("No Sales Category Available",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                        ),
            ),
            Container(
              padding: EdgeInsets.all(2),
              child: initPageLoading
                  ? Center(child: CircularProgressIndicator())
                  : salesCategoryList.length > 0
                      ? SingleChildScrollView(
                          child: Column(
                            children: <Widget>[
                              SizedBox(height: 20),
                              Container(
                                height: 40,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  children: [
                                    Container(
                                      padding: EdgeInsets.only(left: 10.0, right: 10.0),
                                      child: TextWidget("Filter Reports : ",
                                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                    ),
                                    Container(
                                      width: width / 1.7,
                                      child: InputDecorator(
                                        decoration: InputDecoration(
                                          contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                                          border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                        ),
                                        child: DropdownButtonHideUnderline(
                                          child: DropdownButton(
                                            value: selectedSalesCategory,
                                            items: dropdownMenuItems,
                                            onChanged: onChangeDropdownItem,
                                            isExpanded: true,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              SizedBox(height: 10),
                              isReportView
                                  ? BootstrapRow(children: [
                                      BootstrapCol(
                                          sizes: 'col-md-8 col-sm-12',
                                          child: Padding(
                                              padding: const EdgeInsets.only(top: 10),
                                              child: DateRangePickerWidget(
                                                isBigLayout: isBigLayout,
                                                fromDate: fromDate,
                                                toDate: toDate,
                                                onPressFromDate: (value) {
                                                  if (value != null) {
                                                    fromDate = value;
                                                  }
                                                },
                                                onPressToDate: (value) {
                                                  if (value != null) {
                                                    toDate = value;
                                                  }
                                                },
                                              ))),
                                      BootstrapCol(
                                          sizes: 'col-md-4 col-sm-12',
                                          child: Padding(
                                            padding: const EdgeInsets.only(top: 10.0),
                                            child: Row(
                                              mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                              children: [
                                                InkWell(
                                                  onTap: () async {
                                                    getSalesCategoryReport(fromDate, toDate, selectedSalesCategory?.salesCategoryId);
                                                  },
                                                  child: Container(
                                                    alignment: Alignment.center,
                                                    height: 40,
                                                    width: isBigLayout ? width * 0.15 : width * 0.98,
                                                    child: Text("Submit",
                                                        style: TextStyle(
                                                          color: secondaryTextColor,
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.w700,
                                                        )),
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(5),
                                                      color: buttonThemeColor,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )),
                                    ])
                                  : Container(),
                              isLoading
                                  ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                                  : salesCategoryReportList.length != 0
                                      ? Container(
                                          width: width,
                                          alignment: Alignment.topCenter,
                                          child: Column(
                                            children: [
                                              if (salesCategoryReportList.isNotEmpty) ...{
                                                for (var i in groupList.keys) ...{
                                                  if (groupList[i].length != 0) ...{
                                                    Padding(
                                                      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                                      child: Column(
                                                        children: <Widget>[
                                                          if (groupList[i][0].salesCatName != null) ...{
                                                            Container(
                                                              color: Colors.black12,
                                                              padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                                              child: Row(
                                                                children: [
                                                                  Padding(
                                                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                                                    child: Container(
                                                                      alignment: Alignment.centerLeft,
                                                                      child: Text(
                                                                        groupList[i][0].salesCatName ?? "None",
                                                                        style: TextStyle(
                                                                          fontSize: textSizeMedium,
                                                                          fontWeight: FontWeight.bold,
                                                                        ),
                                                                      ),
                                                                    ),
                                                                  ),
                                                                ],
                                                              ),
                                                            ),
                                                          } else ...{
                                                            SizedBox()
                                                          },
                                                          salesCategoryBasedFilterList(groupList[i]),
                                                        ],
                                                      ),
                                                    )
                                                  } else ...{
                                                    SizedBox()
                                                  },
                                                },
                                                SizedBox(
                                                  height: 100,
                                                )
                                              }
                                            ],
                                          ))
                                      : SizedBox(
                                          height: 200,
                                          child: Align(
                                              alignment: Alignment.bottomCenter,
                                              child: Container(
                                                child: Text(
                                                  "Reports not found",
                                                  style: black16w500,
                                                ),
                                              )),
                                        ),
                            ],
                          ),
                        )
                      : Center(
                          child: Container(
                            padding: EdgeInsets.all(15.0),
                            child: TextWidget("No Sales Category Available",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                        ),
            ),
          ],
        ),
        bottomSheet: initPageLoading
            ? SizedBox()
            : Container(
                height: isPortrait == Orientation.portrait ? height * 0.12 : height * 0.2,
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("Grand Total: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget salesCategoryBasedFilterList(List<Sales>? item) {
    var groupListBySalesCategoryName = item != null ? item.groupListsBy((element) => element.salesCatName) : null;
    Map<String?, double> totalAmountByKeyword = {};
    int count = 0;
    item?.forEach((element) {
      final key = element.salesCatName;
      if (totalAmountByKeyword.containsKey(key)) {
        totalAmountByKeyword[key] = (totalAmountByKeyword[key] ?? 0) + double.parse(element.totalAmount.toString());
      } else {
        totalAmountByKeyword[key] = double.parse(element.totalAmount.toString());
      }
      count++;
    });
    return Padding(
      padding: EdgeInsets.only(bottom: 0),
      child: Column(
        children: [
          for (var i in groupListBySalesCategoryName!.keys) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: ReportListView(
                isTwoDaysRecord == true ? 0 : 2,
                item,
                reportFor: ReportFor.salescategory,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 0),
              child: Container(
                color: Colors.black12,
                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Total",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "₹ ${double.tryParse(totalAmountByKeyword[i].toString())?.toStringAsFixed(2)} ",
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${groupListBySalesCategoryName[i]?[0].salesCatName ?? "None"}",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            count.toString(),
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
