// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_expenses_report.dart';
import 'package:smartbill/screens/expenses/model/get_expense_transaction_byId.dart';
import 'package:smartbill/screens/expenses/model/get_purchase_transaction_byId.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../Repositories/reports_repository.dart';
import '../../model/reports/get_purchase_reports.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_function/role_permissions_utilities.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/colors.dart';
import '../../utils/dbutils/db_operations_utility.dart';
import '../../utils/printer_utilities/report_print.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../expenses/screens/manage_expenses_screen.dart';

// ignore: must_be_immutable
class ExpenseReportViewPage extends StatefulWidget {
  Expenses? reports;
  PurchaseTransactionReport? pReports;
  String? flag;
  ExpenseReportViewPage({
     this.reports,
     this.flag,
     this.pReports,
  });
  @override
  _ExpenseReportViewPageState createState() => _ExpenseReportViewPageState(reports, flag, pReports);
}

class _ExpenseReportViewPageState extends State<ExpenseReportViewPage> {
  PrinterUtilities printerUtilities = new PrinterUtilities();
  RolePermissionsUtilities? rolePermissions = new RolePermissionsUtilities();
  DBOperations? dbOperations = new DBOperations();
  ReportPrint? reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  DateTime? fromDate = DateTime.now();
  DateTime? toDate = DateTime.now();
  bool multiPrint = false;
  var dateFormate = '';
  var expDateFormate = '';
  var pDateFormate = '';
  double width = 0.0;
  double height = 0.0;
  bool isLock = false;
  String? saleLayout;
  bool isResetInvoice = false;
  bool isLoading = false;
  PrinterDevices? reportPrinter;
  bool? isBigLayout;

  ///Expense
  List<ExpenseTransactions> expTransList = [];
  int totalQuantity = 0;
  int totalExpenseTransaction = 0;
  double totalAmount = 0.0;
  Expenses? reports;
  String? flag;
  String? expDate;
  String? purchaseDate;

  ///Purchase
  List<PurchaseTransactions> purchaseTransList = [];
  int totalPurchaseTransaction = 0;
  double totalPurchaseAmount = 0.0;
  PurchaseTransactionReport? pReports;
  String referenceNo = "";
  int? billNo;

  _ExpenseReportViewPageState(this.reports, this.flag, this.pReports);

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    var SaleLayout = await fnUtilities.workSpaceUserSetValue(key_saleLayout);
    setState(() {
      isResetInvoice = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
      if (MultiPrintValue == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
        reportPrinter = printerUtilities.getReportPrinter();
        printerUtilities.initPrint(reportPrinter!);
      }
      if (SaleLayout != null) {
        saleLayout = SaleLayout;
      } else {
        saleLayout = "SaleLayout1";
      }
    });
  }

  //GET TRANSACTIONS FROM API BASED ON SALES ID
   getExpenseTransDetailsFromApi(salesId) async {
    try {
      setState(() {
        isLoading = true;
      });
      await getExpenseTransDetailsByExpId(salesId).then((value) {
        setState(() {
          if (value.status == 1) {
            expTransList = value.result?.expenseTransactions ?? [];
            totalExpenseTransaction = value.result?.totalExpenseTransaction ?? 0;
            totalAmount = value.result?.totalAmount ?? 0.0;
            expDate = value.result?.expDate;
            expDateFormate = DateFormat("dd-MM-yyyy").format(DateTime.parse("${value.result?.expDate}"));
            print(expTransList.length);
          }
          totalQty();
        });
      });
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  //GET TRANSACTIONS FROM API BASED ON PURCHASE ID
   getPurchaseTransactionById(salesId) async {
    try {
      setState(() {
        isLoading = true;
      });
      await getPurchaseTransDetailsByExpId(salesId).then((value) {
        setState(() {
          if (value.status == 1) {
            purchaseTransList = value.result?.purchaseTransactions ?? [];
            totalAmount = value.result?.totalAmount ?? 0.0;
            totalPurchaseTransaction = value.result?.totalPurchaseTransaction ?? 0;
            purchaseDate = value.result?.purchaseDate;
            pDateFormate = DateFormat("dd-MM-yyyy").format(DateTime.parse("${value.result?.purchaseDate ?? DateTime.now().toString()}"));
          }
          totalQty();
        });
      });
      setState(() {
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
    }
  }

  //TOTAL QUANTITY FUNCTION
  totalQty() {
    totalQuantity = 0;
    if (flag == expense) {
      for (ExpenseTransactions p in expTransList) {
        totalQuantity += p.quantity ?? 0;
      }
    } else {
      for (PurchaseTransactions p in purchaseTransList) {
        totalQuantity += p.quantity ?? 0;
      }
    }
  }

  initFunctions() async {
    initSettings();
    if (flag == expense) {
      await getExpenseTransDetailsFromApi(reports?.expId ?? 0);
      referenceNo = reports?.invoiceId ?? "";
      billNo = int.tryParse(reports?.invoiceNo ?? '') ?? 0;
      dateFormate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${reports?.createdDate ?? DateTime.now().toString()}"));
    } else {
      await getPurchaseTransactionById(pReports?.purchaseId);
      referenceNo = pReports?.invoiceId ?? "";
      billNo = pReports?.invoiceNo;
      dateFormate = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${pReports?.createdDate ?? DateTime.now().toString()}"));
    }
  }

  @override
  void initState() {
    initFunctions();
    super.initState();
  }

  @override
  void dispose() {
    printerUtilities.disconnect();
    super.dispose();
  }

  iconTopWidget(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        // IconButton(
        //   iconSize: 25,
        //   icon: Icon(Icons.print),
        //   onPressed: () async {
        //     //printForAlertDialog();
        //   },
        //   color: Colors.white,
        // ),
        PopupMenuButton(
          itemBuilder: (context) {
            return [
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.edit),
                  title: Text("Edit"),
                  onTap: () async {
                    Navigator.pop(context);
                    if ( PermissionFunctions.checkPermission(257,) ||  PermissionFunctions.checkPermission(259,)) {
                      Navigator.pop(context);
                      if (flag == expense) {
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ManageExpensesScreen(
                                      expenses: reports,
                                      expTransactionList: expTransList,
                                      flag: expense,
                                      expDate: expDate ?? "",
                                    )));
                      } else {
                        Navigator.pushReplacement(
                            context,
                            MaterialPageRoute(
                                builder: (context) => ManageExpensesScreen(
                                      purchase: pReports,
                                      purchaseTransList: purchaseTransList,
                                      flag: purchase,
                                      purchaseDate: purchaseDate ?? "",
                                    )));
                      }
                    } 
                  },
                ),
              ),
            ];
          },
        )
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBigLayout = width >= tabletWidth;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(title: text("View Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium), appBar: AppBar(), widgets: <Widget>[
        flag == expense
            ? reports != null && fnUtilities.checkIsPossibletoModifyRecord(reports?.createdDate, 1)
                ? iconTopWidget(context)
                : SizedBox()
            : pReports != null && fnUtilities.checkIsPossibletoModifyRecord(pReports?.createdDate, 1)
                ? iconTopWidget(context)
                : SizedBox()
      ]),
      body: isLoading
          ? Center(
              child: Container(
              child: CircularProgressIndicator(),
            ))
          : SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: TextWidget("Ref No: " + referenceNo, textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                      ),
                      billNo != null && !isResetInvoice
                          ? Padding(
                              padding: EdgeInsets.only(top: 10.0),
                              child: TextWidget("Bill No: ${billNo} ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                            )
                          : Container(),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Date: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("${dateFormate.toString()}", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget(flag == expense ? "Expense Date: " : "Purchase Date: ",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget(flag == expense ? expDateFormate : pDateFormate,
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSmall, isCentered: true),
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  Container(
                    height: height / 1.8,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(24), topRight: Radius.circular(24)),
                      boxShadow: <BoxShadow>[BoxShadow(color: Colors.black12, blurRadius: 5.0, offset: Offset(0.75, 0.75))],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(8.0),
                      child: Column(
                        children: [
                          Card(
                            child: Container(
                              color: Colors.white10,
                              width: width,
                              height: 40,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceAround,
                                children: [
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    width: width / 2.5,
                                    child: TextWidget(flag == expense ? "Expenses" : "Purchases", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                  ),
                                  Container(
                                    alignment: Alignment.centerLeft,
                                    width: width / 9.5,
                                    child: TextWidget("Qty", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                  ),
                                  Container(
                                    alignment: Alignment.center,
                                    width: width / 3,
                                    child: TextWidget("Amount(Rs)", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          if (widget.flag == expense) ...[buildTransactionList(expTransList, width)] else ...[buildTransactionList(purchaseTransList, width)]
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
      bottomSheet: Container(
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: TextWidget("Total Items: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: TextWidget("${flag == expense ? totalExpenseTransaction : totalPurchaseTransaction}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: TextWidget("Total Qty: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 10.0),
                        child: TextWidget("${totalQuantity.toString()}", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                      ),
                    ],
                  ),
                ],
              ),
              Row(
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.0, vertical: 10.0),
                    child: TextWidget("Total Amount: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
                    child: TextWidget("Rs.${fnUtilities.checkQtyDoubleValue(totalAmount.toString())}",
                        textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  buildTransactionList(List transactionList, double width) {
    return Expanded(
      child: ListView.builder(
        itemCount: transactionList.length,
        shrinkWrap: true,
        padding: EdgeInsets.only(right: 0),
        itemBuilder: (context, index) {
          var item = transactionList[index];
          return InkWell(
            child: Card(
              child: Container(
                height: 40,
                width: width,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    Container(
                      child: Text(
                        item.productName ?? "",
                        textAlign: TextAlign.left,
                      ),
                      width: width / 2.5,
                    ),
                    Container(
                      child: Text(
                        item.quantity.toString(),
                        textAlign: TextAlign.left,
                      ),
                      width: width / 9.5,
                    ),
                    Container(
                      child: Text(
                        "${fnUtilities.checkQtyDoubleValue(item.amount.toString())}",
                        textAlign: TextAlign.right,
                      ),
                      width: width / 3,
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  ///Alert dialog to choose print for kot or bill
  // printForAlertDialog() {
  //   return showDialog(
  //       context: context,
  //       builder: (context) {
  //         return StatefulBuilder(builder: (context, setState) {
  //           return AlertDialog(
  //             content: Container(
  //               height: 100,
  //               width: isBigLayout ? width * 0.35 : width * 0.8,
  //               child: Column(
  //                 children: [
  //                   SizedBox(
  //                     height: 20,
  //                   ),
  //                   Text("Choose Print Type"),
  //                   SizedBox(
  //                     height: 20,
  //                   ),
  //                   Row(
  //                     mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                     children: [
  //                       GestureDetector(
  //                         onTap: () async {
  //                           if (multiPrint) {
  //                             reportPrinter = printerUtilities.getReportPrinter();
  //                             await printerUtilities.initPrint(reportPrinter);
  //                             await reportPrint.startReportPrint(context, reports, transactionList);
  //                             await Future.delayed(Duration(seconds: 1));
  //                             await printerUtilities.disconnect();
  //                           } else {
  //                             reportPrint.startReportPrint(context, reports, transactionList);
  //                           }
  //                           Navigator.pop(context);
  //                         },
  //                         child: Container(
  //                           alignment: Alignment.center,
  //                           height: 40,
  //                           width: isBigLayout ? width * 0.15 : width * 0.3,
  //                           child: TextWidget("Bill", textColor: secondaryTextColor, isCentered: true),
  //                           decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
  //                         ),
  //                       ),
  //                       GestureDetector(
  //                         onTap: () async {
  //                           if (multiPrint) {
  //                             reportPrinter = printerUtilities.getReportPrinter();
  //                             await printerUtilities.initPrint(reportPrinter);
  //                             await salesPrint.startKotReportPrint(context, reports, transactionList);
  //                             await Future.delayed(Duration(seconds: 1));
  //                             await printerUtilities.disconnect();
  //                           } else {
  //                             salesPrint.startKotReportPrint(context, reports, transactionList);
  //                           }
  //                           Navigator.pop(context);
  //                         },
  //                         child: Container(
  //                           alignment: Alignment.center,
  //                           height: 40,
  //                           width: isBigLayout ? width * 0.15 : width * 0.3,
  //                           child: TextWidget("KOT", textColor: secondaryTextColor, isCentered: true),
  //                           decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
  //                         ),
  //                       ),
  //                     ],
  //                   )
  //                 ],
  //               ),
  //             ),
  //           );
  //         });
  //       });
  // }

}
