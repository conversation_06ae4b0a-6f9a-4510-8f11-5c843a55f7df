// ignore_for_file: non_constant_identifier_names, unused_local_variable, sdk_version_ui_as_code, sdk_version_set_literal

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/sales_reports_printer.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/printer_utilities/report_print.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../Repositories/reports_repository.dart';
import '../../model/products.dart';
import '../../model/reports/get_purchase_reports.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/exp_purchase_report_view.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/constants/styles.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class PurchaseReportsPage extends StatefulWidget {
  @override
  PurchaseReportsPageState createState() => PurchaseReportsPageState();
}

class PurchaseReportsPageState extends State<PurchaseReportsPage> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  ReportPrint reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  SalesReportsPrinter salesReportsPrinter = new SalesReportsPrinter();
  final dbHelper = DatabaseHelper.instance;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  bool isLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalSales = 0;
  double totalQty = 0;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isBigLayout = false;
  bool isLock = false;
  bool initPageLoading = false;

  List<ProductKey> productCategoryList = [];
  ProductKey? selectedProductCategory;
  List<DropdownMenuItem<ProductKey>>? dropdownMenuItems;

  PurchaseReportsResult? purchaseReports = PurchaseReportsResult();
  List<PurchaseTransactionReport> purchaseReportList = [];

  int totalPurchase = 0;

  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  initSettings() async {
    //var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    isReportView = await PermissionFunctions.checkPermission(241, viewToast: false);
    // if (MultiPrintValue == "1") {
    //   multiPrint = true;
    // } else {
    //   multiPrint = false;
    //   reportPrinter = printerUtilities.getReportPrinter();
    //   await printerUtilities.initPrint(reportPrinter);
    // }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked.subtract(Duration(
                hours: picked.hour,
                minutes: picked.minute,
                seconds: picked.second,
                milliseconds: picked.millisecond,
                microseconds: picked.microsecond));
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  getPurchaseReport(fromDate, toDate, productCategoryId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getPurchaseReportByDatToDate(fromDate, toDate, purchaseProductId: productCategoryId).then((value) {
        if (value.status == 1) {
          purchaseReportList = value.result?.reportList ?? [];
          totalSales = value.result?.totalPurchase ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = purchaseReportList.groupListsBy((element) => element.productKeywordId);
          purchaseReports = value.result;
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  //------------------------------------------------Start DropDown Function-------------------------------------------------//

  ///Get All Expense Category List data
  getAllPurchaseCategoryList() {
    setState(() {
      productCategoryList.clear();
      ProductKey model = ProductKey();
      model.keywordName = "All";
      productCategoryList.add(model);
      productCategoryList.addAll(ListUtility.productKeyList);
    });
    if (productCategoryList.isNotEmpty) {
      setState(() {
        dropdownMenuItems = buildDropdownMenuItems(productCategoryList);
        selectedProductCategory = dropdownMenuItems?[0].value;
      });
    }
  }

  //PRODUCT LIST VIEW
  List<DropdownMenuItem<ProductKey>> buildDropdownMenuItems(List purchase) {
    // ignore: deprecated_member_use
    List<DropdownMenuItem<ProductKey>> items = [];
    for (ProductKey e in purchase) {
      items.add(
        DropdownMenuItem(
          value: e,
          child: Text(e.keywordName ?? ""),
        ),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownItem(ProductKey model) {
    setState(() {
      selectedProductCategory = model;
    });
  }

  //------------------------------------------------End DropDown Function-------------------------------------------------//

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });
    await initSettings();
    await getAllPurchaseCategoryList();
    setState(() {
      initPageLoading = false;
    });
  }

  //BLUETOOTH DISCONNECTION
  void disconnect() {
    try {
      bluetooth.disconnect();
    } catch (e) {}
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        // IconButton(
        //   iconSize: 25,
        //   icon: Icon(Icons.print),
        //   onPressed: () async {
        //     purchaseReportPrint(purchaseReports);
        //   },
        //   color: Colors.white,
        // ),

        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(242)) {
                      Navigator.pop(context);
                      await purchaseReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(243)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(244)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width > tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Purchase Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          purchaseReportList.isNotEmpty && PermissionFunctions.checkPermission(242, viewToast: false) ? iconTopWidget() : Container()
        ],
      ),
      body: Container(
          padding: EdgeInsets.all(2),
          child: initPageLoading
              ? Center(child: CircularProgressIndicator())
              : Column(
                  children: <Widget>[
                    SizedBox(height: 20),
                    isReportView
                        ? BootstrapRow(children: [
                            BootstrapCol(
                                sizes: 'col-md-8 col-sm-12',
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 10),
                                  child: Row(
                                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                    children: [
                                      /// To select from date
                                      InkWell(
                                        onTap: () {
                                          selectDate(context, 'fromDate');
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Text(
                                                  "${fnUtilities.convertDate(fromDate.toString())}",
                                                  style: secondaryTextStyle(),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Icon(Icons.calendar_today)
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),

                                      /// To select to date
                                      InkWell(
                                        onTap: () {
                                          selectDate(context, 'toDate');
                                        },
                                        child: Card(
                                          elevation: 4,
                                          child: Padding(
                                            padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                            child: Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              children: [
                                                Text(
                                                  fnUtilities.convertDate(toDate.toString()),
                                                  style: secondaryTextStyle(),
                                                ),
                                                SizedBox(
                                                  width: 15,
                                                ),
                                                Icon(Icons.calendar_today)
                                              ],
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )),

                            ///Submit button
                            BootstrapCol(
                                sizes: 'col-md-4 col-sm-12',
                                child: Padding(
                                  padding: const EdgeInsets.only(top: 10.0),
                                  child: Row(
                                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                    children: [
                                      InkWell(
                                        onTap: () async {
                                          if (PermissionFunctions.checkPermission(241)) {
                                            setState(() {
                                              getPurchaseReport(fromDate, toDate, selectedProductCategory?.productKeyId);
                                            });
                                          }
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          height: 40,
                                          width: isBigLayout ? width * 0.15 : width * 0.98,
                                          child: Text("Submit",
                                              style: TextStyle(
                                                color: secondaryTextColor,
                                                fontSize: 14,
                                                fontWeight: FontWeight.w700,
                                              )),
                                          decoration: BoxDecoration(
                                            borderRadius: BorderRadius.circular(5),
                                            color: buttonThemeColor,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                          ])
                        : Container(),
                    isLoading
                        ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                        : purchaseReportList.length != 0
                            ? purchaseBasedFilterList(purchaseReportList)
                            : SizedBox(
                                height: 200,
                                child: Align(
                                    alignment: Alignment.bottomCenter,
                                    child: Container(
                                      child: Text(
                                        "Reports not found",
                                        style: black16w500,
                                      ),
                                    )),
                              ),
                  ],
                )),
      bottomSheet: initPageLoading
          ? SizedBox()
          : Container(
              height: isPortrait == Orientation.portrait ? height * 0.15 : height / 4,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Grand Total: ",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

  Widget purchaseBasedFilterList(List<PurchaseTransactionReport> item) {
    return Expanded(
      child: Padding(
        padding: EdgeInsets.only(bottom: 120),
        child: ReportViewWidget(
          flag: purchase,
          purchaseReportList: item,
        ),
      ),
    );
  }

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  purchaseReportTextBuildFunction() async {
    String data = "";
    data += "\nPurchase Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\n No     ID        Date        Amount";

    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        data += "\n";
        for (int list = 0; list < groupList[i].length; list++) {
          data +=
              "\n${groupList[i][list].invoiceNo.toString().padLeft(2)} ${groupList[i][list].invoiceId.toString().padLeft(8)} ${fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true)?.padLeft(9)} ${double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(2).padLeft(10)}";
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(50)}";
    data += "\nTotal Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}";
    await printerUtilities.shareCommonReport(data, "Purchase");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pw.Widget pdfPurchaseReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Purchase Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(
                //width: 30,
                child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 100, child: pw.Text("Date", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (groupList != null)
            for (var i in groupList.keys)
              for (int list = 0; list < groupList[i].length; list++)
                list == 0
                    ? pw.Column(children: [
                        pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                          pw.Container(
                              width: 20,
                              child: pw.Text(groupList[i][list].invoiceNo.toString(),
                                  style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                          pw.Container(
                              width: 90,
                              child: pw.Text(groupList[i][list].invoiceId.toString(),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                          pw.Container(
                              width: 200,
                              child: pw.Column(
                                children: [
                                  pw.Text(
                                    fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true) ?? "",
                                    textAlign: pw.TextAlign.right,
                                    style: const pw.TextStyle(fontSize: 10),
                                  ),
                                  // pw.Padding(
                                  //     padding: pw.EdgeInsets.only(
                                  //         top: 5.0, bottom: 5.0),
                                  //     child: pw.Text(
                                  //         groupList[i][list]
                                  //             .productKeywordName
                                  //             .toString(),
                                  //         textAlign: pw.TextAlign.right,
                                  //         style: const pw.TextStyle(
                                  //             fontSize: 10)))
                                ],
                              )),
                          pw.Container(
                              width: 95,
                              child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                        ]),
                      ])
                    : pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                        pw.Container(
                            width: 20,
                            child: pw.Text(groupList[i][list].invoiceNo.toString(),
                                style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                        pw.Container(
                            width: 90,
                            child: pw.Text(groupList[i][list].invoiceId.toString(),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                        pw.Container(
                            width: 200,
                            child: pw.Column(
                              children: [
                                pw.Text(
                                  fnUtilities.convertDateMonthYearFormat(groupList[i][list].createdDate.toString(), isShowTime: true) ?? "",
                                  textAlign: pw.TextAlign.right,
                                  style: const pw.TextStyle(fontSize: 10),
                                ),
                                // pw.Padding(
                                //     padding: pw.EdgeInsets.only(
                                //         top: 5.0, bottom: 5.0),
                                //     child: pw.Text(
                                //         groupList[i][list]
                                //             .productKeywordName
                                //             .toString(),
                                //         textAlign: pw.TextAlign.right,
                                //         style: const pw.TextStyle(
                                //             fontSize: 10)))
                              ],
                            )),
                        pw.Container(
                            width: 95,
                            child: pw.Text(double.parse(groupList[i][list].totalAmount.toString()).toStringAsFixed(1),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                      ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalAmount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfReportBuildFunction(String? currentDate, {BuildContext? context}) {
    String fileName = "Purchase_Report";
    sharePDF.savePDFFormat(pdfPurchaseReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
