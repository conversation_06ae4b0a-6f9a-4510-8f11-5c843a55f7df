import 'package:blue_thermal_printer/blue_thermal_printer.dart';
// ignore: implementation_imports
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_tax_reports.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';

import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../../Repositories/reports_repository.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ReportsFilterByTax extends StatefulWidget {
  static var tag = "/T5Settings";

  @override
  _ReportsFilterByTaxState createState() => _ReportsFilterByTaxState();
}

class _ReportsFilterByTaxState extends State<ReportsFilterByTax> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  String selectTax = "All";
  String productId = "All";
  double totalCGST = 0.0;
  double totalSGST = 0.0;
  double totalGST = 0.0;
  double totalQty = 0;
  List<TaxReports> taxReportLists = [];
  TaxAmounts? taxAmount;
  bool isLoading = false;
  var width;
  var height;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isBigLayout = false;
  bool initPageLoading = false;
  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  multiPrintChecker() async {
    if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked;
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //GET TOTAL TAX FUNCTION
  getTotalDetails() {
    totalQty = 0;
    totalCGST = 0.0;
    totalSGST = 0.0;
    totalGST = 0.0;
    setState(() {
      if (taxAmount?.cgst != "null" && taxAmount?.sgst != "null") {
        totalCGST = taxAmount?.cgst ?? 0.0;
        totalSGST = taxAmount?.sgst ?? 0.0;
        totalGST = taxAmount?.gst ?? 0.0;
      }
    });
  }

  ///get tax reports
  getTaxReports(
    fromDate,
    toDate,
  ) async {
    try {
      setState(() {
        isLoading = true;
      });

      await getTaxReportsByDate(
        fromDate,
        toDate,
      ).then((value) {
        if (value.status == 1) {
          taxAmount = value.result?.tax;
          taxReportLists = value.result?.getTax ?? [];
          groupList = taxReportLists.groupListsBy((element) => element.productID);
          getTotalDetails();
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });
    await initSettings();
    setState(() {
      initPageLoading = false;
    });
  }

  initSettings() async {
    await multiPrintChecker();
    setState(() {
      isReportView = PermissionFunctions.checkPermission(225, viewToast: false);
    });
  }

  ///Tax Report print method
  taxReportPrint() async {
    if (reportPrinter?.printerFormat == "Default") {
      if (reportPrinter?.paperSize == 58) {
        await taxReportPrintLineBuildFunction();
      } else {
        await taxReportPrintLineBuildFunction80MM();
      }
    } else {
      if (reportPrinter?.paperSize == 58) {
        await taxReportPrintLineBuildFunctionTVS();
      } else {
        await taxReportPrintLineBuildFunctionTVS80MM();
      }
    }
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  startPrintFunction() async {
    try {
      LoadingAlertWidget.onLoading(context);;
      if (PermissionFunctions.checkPermission(227)) {
        if (multiPrint) {
          reportPrinter = printerUtilities.getReportPrinter();
          await printerUtilities.initPrint(reportPrinter);
          await taxReportPrint();
        } else {
          await taxReportPrint();
        }
      }
      LoadingAlertWidget.onStopping();
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "startPrintFunction - ReportsFilterByTax");
    }
  }

  iconTopWidget() {
    return Row(
      children: [
        IconButton(
          iconSize: 25,
          icon: Icon(Icons.print),
          onPressed: () {
            startPrintFunction();
          },
          color: Colors.white,
        ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(228)) {
                      Navigator.pop(context);
                      await tableReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(229)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(230)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Tax Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[taxReportLists.isNotEmpty && PermissionFunctions.checkPermission(227, viewToast: false) ? iconTopWidget() : Container()],
      ),
      body: Container(
          child: initPageLoading
              ? Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      SizedBox(height: 20),
                      Container(
                        height: 40,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            Container(
                              padding: EdgeInsets.only(left: 10.0, right: 10.0),
                              child: TextWidget("Filter Reports : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                            ),
                            Container(
                              width: width / 1.7,
                              child: InputDecorator(
                                decoration: InputDecoration(
                                  contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                                  // labelText: 'Printer Format',
                                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                ),
                                child: DropdownButtonHideUnderline(
                                  child: DropdownButton(
                                    value: selectTax,
                                    items: <String>['All', 'CGST', 'SGST', 'GST'].map((String value) {
                                      return new DropdownMenuItem<String>(
                                        value: value,
                                        child: new Text(value),
                                      );
                                    }).toList(),
                                    onChanged: (String? val) {
                                      if (PermissionFunctions.checkPermission(226)) {
                                        setState(() {
                                          selectTax = val ?? "";
                                        });
                                      }
                                    },
                                    isExpanded: true,
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 10),
                      Container(
                        padding: EdgeInsets.all(2),
                        alignment: Alignment.center,
                        width: width,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            isReportView
                                ? BootstrapRow(children: [
                                    BootstrapCol(
                                        sizes: 'col-md-8 col-sm-12',
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 10),
                                          child: Row(
                                            mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                            children: [
                                              InkWell(
                                                onTap: () {
                                                  selectDate(context, 'fromDate');
                                                },
                                                child: Card(
                                                  elevation: 4,
                                                  child: Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        Text(
                                                          "${fnUtilities.convertDate(fromDate.toString())}",
                                                          style: secondaryTextStyle(),
                                                        ),
                                                        SizedBox(
                                                          width: 15,
                                                        ),
                                                        Icon(Icons.calendar_today)
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                              InkWell(
                                                onTap: () {
                                                  selectDate(context, 'toDate');
                                                },
                                                child: Card(
                                                  elevation: 4,
                                                  child: Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                    child: Row(
                                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                      children: [
                                                        Text(
                                                          fnUtilities.convertDate(toDate.toString()),
                                                          style: secondaryTextStyle(),
                                                        ),
                                                        SizedBox(
                                                          width: 15,
                                                        ),
                                                        Icon(Icons.calendar_today)
                                                      ],
                                                    ),
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )),
                                    BootstrapCol(
                                        sizes: 'col-md-4 col-sm-12',
                                        child: Padding(
                                          padding: const EdgeInsets.only(top: 10.0),
                                          child: Row(
                                            mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                            children: [
                                              InkWell(
                                                onTap: () async {
                                                  if (PermissionFunctions.checkPermission(225)) {
                                                    setState(() {
                                                      getTaxReports(
                                                        fromDate,
                                                        toDate,
                                                      );
                                                    });
                                                  }
                                                },
                                                child: Container(
                                                  alignment: Alignment.center,
                                                  height: 40,
                                                  width: isBigLayout ? width * 0.15 : width * 0.98,
                                                  child: Text("Submit",
                                                      style: TextStyle(
                                                        color: secondaryTextColor,
                                                        fontSize: 14,
                                                        fontWeight: FontWeight.w700,
                                                      )),
                                                  decoration: BoxDecoration(
                                                    borderRadius: BorderRadius.circular(5),
                                                    color: buttonThemeColor,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          ),
                                        )),
                                  ])
                                : CircularProgressIndicator(),
                            15.height,
                            isLoading
                                ? Container(
                                    width: width,
                                    child: Center(
                                      child: Container(height: 40, child: CircularProgressIndicator()),
                                    ),
                                  )
                                : Container(
                                    width: width,
                                    alignment: Alignment.topCenter,
                                    child: Column(
                                      children: [
                                        // ignore: sdk_version_ui_as_code
                                        if (taxReportLists.isNotEmpty) ...[
                                          for (var i in groupList.keys)
                                            Padding(
                                              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                              child: Column(
                                                children: <Widget>[
                                                  Padding(
                                                    padding: const EdgeInsets.symmetric(vertical: 8),
                                                    child: Container(
                                                      alignment: Alignment.centerLeft,
                                                      child: Text(
                                                        groupList[i][0].productName.toString(),
                                                        style: TextStyle(fontWeight: FontWeight.bold),
                                                      ),
                                                    ),
                                                  ),
                                                  ListView.builder(
                                                      padding: const EdgeInsets.only(right: 0),
                                                      itemCount: groupList[i].length,
                                                      shrinkWrap: true,
                                                      reverse: true,
                                                      physics: NeverScrollableScrollPhysics(),
                                                      itemBuilder: (context, index) {
                                                        var item = groupList[i][index];
                                                        return Container(
                                                          alignment: Alignment.centerLeft,
                                                          padding: const EdgeInsets.symmetric(vertical: 2),
                                                          child: Row(
                                                            mainAxisAlignment: MainAxisAlignment.start,
                                                            children: <Widget>[
                                                              Container(
                                                                width: width / 2.5,
                                                                alignment: Alignment.centerLeft,
                                                                child: Text(
                                                                  double.parse(item.cgstAmount.toString()).toStringAsFixed(2) +
                                                                      ' + ' +
                                                                      double.parse(item.sgstAmount.toString()).toStringAsFixed(2),
                                                                ),
                                                              ),
                                                              Container(
                                                                width: width / 2,
                                                                alignment: Alignment.centerRight,
                                                                child: Text(
                                                                  "GST: Rs. " + double.parse(item.totalGst.toString()).toStringAsFixed(2),
                                                                  style: TextStyle(fontWeight: FontWeight.bold),
                                                                ),
                                                              )
                                                            ],
                                                          ),
                                                        );
                                                      }),
                                                  const Divider(
                                                    height: 0.5,
                                                  )
                                                ],
                                              ),
                                            )
                                        ] else ...[
                                          SizedBox(
                                            height: 200,
                                            child: Align(
                                                alignment: Alignment.bottomCenter,
                                                child: Container(
                                                  child: Text(
                                                    "Reports not found",
                                                    style: black16w500,
                                                  ),
                                                )),
                                          ),
                                        ]
                                      ],
                                    )),
                            SizedBox(
                              height: isPortrait == Orientation.portrait
                                  ? selectTax == "All"
                                      ? height / 5.2
                                      : height / 8
                                  : selectTax == "All"
                                      ? height / 2.8
                                      : height / 6,
                            )
                          ],
                        ),
                      ),
                    ],
                  ),
                )),
      bottomSheet: initPageLoading
          ? SizedBox()
          : Container(
              height: isPortrait == Orientation.portrait
                  ? selectTax == "All"
                      ? height * 0.21
                      : height / 8
                  : selectTax == "All"
                      ? height / 2.8
                      : height / 6,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  selectTax == "CGST" || selectTax == "All"
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Total CGST: ",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Rs.${totalCGST.toStringAsFixed(2)}",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                          ],
                        )
                      : Container(),
                  selectTax == "SGST" || selectTax == "All"
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Total SGST: ",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Rs.${totalSGST.toStringAsFixed(2)}",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                          ],
                        )
                      : Container(),
                  selectTax == "GST" || selectTax == "All"
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Total GST  : ",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                            Padding(
                              padding: EdgeInsets.only(top: 5.0),
                              child: TextWidget("Rs.${totalGST.toStringAsFixed(2)}",
                                  textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                            ),
                          ],
                        )
                      : Container(),
                ],
              ),
            ),
    );
  }

//------------------------------------------------Start PrintBuild Function-------------------------------------------------//

  taxReportPrintLineBuildFunction() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 9);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(11)} ${double.parse(groupList[i][list].cgstAmount.toString()).toStringAsFixed(2).padLeft(9)} ${double.parse(groupList[i][list].sgstAmount.toString()).toStringAsFixed(1).padLeft(9)} ${double.parse(groupList[i][list].totalGst.toString()).toStringAsFixed(2).padLeft(10)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(11) + "  " + " ".padLeft(5) + "  " + ' '.padLeft(5) + "       " + " ".padLeft(10)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String gST = selectTax == "All"
        ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}, {"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2},{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}'''
        : selectTax == "CGST"
            ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}'''
            : selectTax == "SGST"
                ? '''{"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}'''
                : selectTax == "GST"
                    ? '''{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}'''
                    : "";
    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tax Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   {"text": "Product     CGST    SGST    GST", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   $gST
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  taxReportPrintLineBuildFunctionTVS() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 9);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(10)} ${double.parse(groupList[i][list].cgstAmount.toString()).toStringAsFixed(2).padLeft(7)} ${double.parse(groupList[i][list].sgstAmount.toString()).toStringAsFixed(1).padLeft(7)} ${double.parse(groupList[i][list].totalGst.toString()).toStringAsFixed(2).padLeft(7)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(10) + " ".padLeft(7) + ' '.padLeft(7) + " ".padLeft(7)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String gST = selectTax == "All"
        ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}, {"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2},{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}'''
        : selectTax == "CGST"
            ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}'''
            : selectTax == "SGST"
                ? '''{"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}'''
                : selectTax == "GST"
                    ? '''{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}'''
                    : "";
    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tax Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 0, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "Product    CGST   SGST   GST", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   $gST
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  taxReportPrintLineBuildFunction80MM() async {
    String listData = "";
    if (groupList != null) {
      for (var i in groupList.keys) {
        if (groupList[i]?.length != 0) {
          for (int list = 0; list < groupList[i].length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 20);
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(22)} ${double.parse(groupList[i][list].cgstAmount.toString()).toStringAsFixed(2).padLeft(12)} ${double.parse(groupList[i][list].sgstAmount.toString()).toStringAsFixed(1).padLeft(12)} ${double.parse(groupList[i][list].totalGst.toString()).toStringAsFixed(2).padLeft(15)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(22) + "  " + " ".padLeft(12) + "  " + ' '.padLeft(12) + "       " + " ".padLeft(15)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }

    String gST = selectTax == "All"
        ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}, {"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
        : selectTax == "CGST"
            ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
            : selectTax == "SGST"
                ? '''{"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
                : selectTax == "GST"
                    ? '''{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
                    : "";
    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tax Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product               CGST       SGST        GST", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $gST
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  taxReportPrintLineBuildFunctionTVS80MM() async {
    String listData = "";
    if (groupList != null) {
      for (var i in groupList.keys) {
        if (groupList[i]?.length != 0) {
          for (int list = 0; list < groupList[i].length; list++) {
            var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 20);
            for (int p = 0; p < productSplit.length; p++) {
              if (p == 0) {
                listData +=
                    '''{"text": "${productSplit[p].padRight(22)} ${double.parse(groupList[i][list].cgstAmount.toString()).toStringAsFixed(2).padLeft(12)} ${double.parse(groupList[i][list].sgstAmount.toString()).toStringAsFixed(1).padLeft(12)} ${double.parse(groupList[i][list].totalGst.toString()).toStringAsFixed(2).padLeft(15)}", "size": 0, "alignment": 0},''';
              } else {
                listData +=
                    '''{"text": "${productSplit[p].padRight(22) + "  " + " ".padLeft(12) + "  " + ' '.padLeft(12) + "       " + " ".padLeft(15)}", "size": 0, "alignment": 0},''';
              }
            }
          }
        }
      }
    }

    String gST = selectTax == "All"
        ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}, {"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2},{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
        : selectTax == "CGST"
            ? '''{"text": "Total CGST Amount: ${totalCGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
            : selectTax == "SGST"
                ? '''{"text": "Total SGST Amount: ${totalSGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
                : selectTax == "GST"
                    ? '''{"text": "Total GST Amount: ${totalGST.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}'''
                    : "";
    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tax Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product               CGST       SGST        GST", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $gST
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }
//------------------------------------------------End PrintBuild Function-------------------------------------------------//

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  tableReportTextBuildFunction() async {
    String data = "";
    data += "\nTax Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nProduct       CGST      SGST       GST";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 8);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              data +=
                  "\n${productSplit[p].padRight(10)} ${double.parse(groupList[i][list].cgstAmount.toString()).toStringAsFixed(2).padLeft(7)} ${double.parse(groupList[i][list].sgstAmount.toString()).toStringAsFixed(1).padLeft(7)} ${double.parse(groupList[i][list].totalGst.toString()).toStringAsFixed(2).padLeft(7)}";
            } else {
              data += "\n${productSplit[p].padRight(9) + " ".padLeft(9) + " ".padLeft(8) + " ".padLeft(8) + " ".padLeft(12)}";
            }
          }
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += selectTax == "All" || selectTax == "CGST" ? "\nTotal CGST: ${totalCGST.toStringAsFixed(2).padLeft(10)}" : "";
    data += selectTax == "All" || selectTax == "SGST" ? "\nTotal SGST: ${totalSGST.toStringAsFixed(2).padLeft(10)}" : "";
    data += selectTax == "All" || selectTax == "GST" ? "\nTotal GST: ${totalGST.toStringAsFixed(2).padLeft(10)}" : "";
    await printerUtilities.shareCommonReport(data, "Tax");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//
  pw.Widget pdfTaxReportBodyBuildFunction() {
    int sNo = 0;
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Tax Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 130, child: pw.Text("Product", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 80, child: pw.Text("CGST", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
            pw.Container(width: 90, child: pw.Text("SGST", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
            pw.Container(width: 95, child: pw.Text("GST", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (groupList != null)
            for (var i in groupList.keys)
              for (int list = 0; list < groupList[i].length; list++) pdfListWidget(groupList[i][list], sNo),
          pw.Divider(thickness: 2),
          selectTax == "All" || selectTax == "CGST"
              ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                  pw.Container(
                    width: 120,
                    child: pw.Text("Total CGST :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  ),
                  pw.Container(
                    width: 150,
                    child: pw.Text(totalCGST.toStringAsFixed(2),
                        textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  )
                ])
              : pw.Container(),
          selectTax == "All" || selectTax == "SGST"
              ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                  pw.Container(
                    width: 120,
                    child: pw.Text("Total SGST :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  ),
                  pw.Container(
                    width: 150,
                    child: pw.Text(totalSGST.toStringAsFixed(2),
                        textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  )
                ])
              : pw.Container(),
          selectTax == "All" || selectTax == "GST"
              ? pw.Row(
                  mainAxisAlignment: pw.MainAxisAlignment.end,
                  children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text(
                        "Total GST :",
                        textAlign: pw.TextAlign.right,
                        style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
                      ),
                    ),
                    pw.Container(
                      width: 150,
                      child: pw.Text(
                        totalGST.toStringAsFixed(2),
                        textAlign: pw.TextAlign.right,
                        style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
                      ),
                    )
                  ],
                )
              : pw.Container(),

          pw.SizedBox(height: 10)
        ]));
  }

  pw.Widget pdfListWidget(var groupList, int sNo) {
    return pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
      pw.Container(width: 40, child: pw.Text((sNo += 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
      pw.Container(
          width: 130, child: pw.Text(groupList.productName.toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
      pw.Container(
          width: 80,
          child: pw.Text(double.parse(groupList.cgstAmount.toString()).toStringAsFixed(2),
              textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
      pw.Container(
          width: 90,
          child: pw.Text(double.parse(groupList.sgstAmount.toString()).toStringAsFixed(2),
              textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
      pw.Container(
          width: 95,
          child: pw.Text(double.parse(groupList.totalGst.toString()).toStringAsFixed(2),
              textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
    ]);
  }

  pdfReportBuildFunction(String? currentDate, {BuildContext? context}) {
    String fileName = "Tax_Report";
    sharePDF.savePDFFormat(pdfTaxReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
