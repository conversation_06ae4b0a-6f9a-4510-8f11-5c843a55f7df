import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/reports/get_payment_reports.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/screens/reports/reports_list_view.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../Repositories/download_reports_repository.dart';
import '../../Repositories/reports_repository.dart';
import '../../main.dart';
import '../../model/reports.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/date_range_picker_widget.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/reports_db_script.dart';
import '../../utils/error_logs/error_logs_function.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class ShiftWiseReportPage extends StatefulWidget {
  ShiftWiseReportPage({Key? key});

  @override
  ShiftWiseReportPageState createState() => ShiftWiseReportPageState();
}

class ShiftWiseReportPageState extends State<ShiftWiseReportPage> with SingleTickerProviderStateMixin {
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();

  List<DropdownMenuItem<ShiftwiseSalesModel>>? dropdownMenuShiftItems;

  List<Sales> shiftWiseReportList = [];

  List<ShiftwiseSalesModel> shiftWiseList = [];
  PaymentReports? shiftReport = PaymentReports();

  ShiftwiseSalesModel? selectedShift;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();

  bool isLoading = false;
  bool initPageLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalSales = 0;
  double totalQty = 0;
  var groupList;

  bool isBigLayout = false;
  bool isLock = false;
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  bool isTwoDaysRecord = false;
  TabController? controller;
  int selectIndex = 0;

  getShiftWiseReport(fromDate, toDate, String shiftId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getShiftReportByDatToDate(fromDate, toDate, shiftId: shiftId).then((value) {
        if (value.status == 1) {
          shiftWiseReportList = value.result?.reportList ?? [];
          totalSales = value.result?.totalSales ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = shiftWiseReportList.groupListsBy((element) => element.shiftId);
          // shift
          shiftReport = value.result;
        } else {
          showToast(value.message);
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      ErrorLogsFunction.insertErrorLogs(e, "getShiftWiseReport - PaymentReportsPage ");
      setState(() {
        isLoading = false;
      });
    }
  }

  getShiftWiseReportFromLocale(String shiftId) async {
    setState(() {
      isLoading = true;
    });
    try {
      var now = new DateTime.now();
      fromDate = now.subtract(
          Duration(days: 2, hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      toDate = now.subtract(
          Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      var result = await ReportDetailsDB.getCommonReports(fromDate, toDate, keyName: db_shift_Id, keyValue: shiftId);
      if (result != null) {
        shiftReport = PaymentReports.fromJson(result);
        shiftWiseReportList = shiftReport?.reportList ?? [];
        totalSales = shiftReport?.totalSales ?? 0;
        totalAmount = shiftReport?.totalAmount ?? 0;
        for (Sales model in shiftWiseReportList) {
          if (model.shiftId != null) {
            var shiftWiseModel = ListUtility.shiftSalesList.firstWhere((element) => element.shiftId == model.shiftId, orElse: null);
            model.shiftName = shiftWiseModel != null ? shiftWiseModel.shiftName : "None";
          }
        }
        groupList = shiftWiseReportList.groupListsBy((element) => element.shiftId);
      }
      setState(() {
        fromDate = DateTime.now();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ErrorLogsFunction.insertErrorLogs(e, "getShiftWiseReportFromLocale - PaymentReportsPage ");
    }
  }

  /// -- Shift wise dropdown

  queryAllShiftWiseDetails() async {
    setState(() {
      shiftWiseList.clear();
      ShiftwiseSalesModel model = ShiftwiseSalesModel();
      model.shiftName = "All";
      model.shiftId = "";
      model.id = 0;
      shiftWiseList.add(model);
      shiftWiseList.addAll(ListUtility.shiftSalesList.where((element) => element.status != 2 && element.isActive == true));
    });
    if (shiftWiseList.length > 0) {
      setState(() {
        dropdownMenuShiftItems = buildDropdownMenuShiftItems(shiftWiseList);
        selectedShift = dropdownMenuShiftItems?[0].value;
      });
    }
  }

  // shift
  List<DropdownMenuItem<ShiftwiseSalesModel>> buildDropdownMenuShiftItems(List shiftList) {
    List<DropdownMenuItem<ShiftwiseSalesModel>> items = [];
    for (ShiftwiseSalesModel model in shiftList) {
      items.add(
        DropdownMenuItem(value: model, child: Text(model.shiftName ?? '')),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownShiftItem(ShiftwiseSalesModel? shift) {
    if (PermissionFunctions.checkPermission(232)) {
      setState(() {
        selectedShift = shift;
      });
    }
  }

  /// shift wise dropdown end
  @override
  void initState() {
    queryAllShiftWiseDetails();
    initPageDetails();
    super.initState();
  }

  initPageDetails() async {
    setState(() {
      initPageLoading = true;
    });

    await initFunction(selectIndex);

    controller = TabController(length: 2, vsync: this);
    controller?.addListener(() {
      setState(() {
        if (controller?.index == 0) {
          selectIndex = 0;
        }

        if (controller?.index == 1) {
          selectIndex = 1;
          fromDate = DateTime.now();
          toDate = DateTime.now();
          shiftWiseReportList = [];
          shiftReport = new PaymentReports();
          totalSales = 0;
          totalAmount = 0;
        }
        initFunction(selectIndex);
      });
    });

    setState(() {
      initPageLoading = false;
    });
  }

  initFunction(int index) async {
    if (index == 0) {
      await getShiftWiseReportFromLocale(selectedShift?.shiftId ?? '');
      isTwoDaysRecord = true;
    } else {
      isTwoDaysRecord = false;
    }
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      // await requestStoragePermission();
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
    }
  }

  //BLUETOOTH DISCONNECTION
  void disconnect() {
    // try {
    //   bluetooth.disconnect();
    // } catch (e) {}
  }

  @override
  void dispose() {
    // if (!multiPrint) {
    //   printerUtilities.disconnect();
    // }
    super.dispose();
  }

  iconTopWidget() {
    return Row(
      children: [
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report For Sale"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(274)) {
                      Navigator.pop(context);
                      downloadReportsRequest = DownloadReportsRequest();
                      downloadReportsRequest.fromDate = fromDate.toString();
                      downloadReportsRequest.toDate = toDate.toString();
                      downloadReportsRequest.shiftTypeID = selectedShift?.shiftId;
                      downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                      await downloadReportFunction(downloadReportsRequest, "/api/v1/sales/getshiftwisesalesreport", "Shift-wise Report");
                    }
                    print(MyApp.activeUser.token);
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        appBar: AppBar(
          backgroundColor: appThemeColor,
          iconTheme: IconThemeData(
            color: secondaryTextColor, //change your color here
          ),
          title: text("Shift Wise Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          bottom: TabBar(
            onTap: (index) {},
            controller: controller,
            isScrollable: true,
            labelStyle: primaryTextStyle(),
            indicatorColor: redColor,
            physics: BouncingScrollPhysics(),
            labelColor: secondaryTextColor,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      '2 Days',
                    ),
                  ],
                ),
              ),
              // if (isReportView)
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.supervised_user_circle,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      'Date to Date',
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            (shiftWiseReportList.isNotEmpty) && PermissionFunctions.checkPermission(233, viewToast: false) ? iconTopWidget() : Container()
          ],
        ),
        body: TabBarView(
          controller: controller,
          children: [
            isLoading
                ? Center(child: CircularProgressIndicator())
                : shiftWiseReportList.isNotEmpty || shiftWiseReportList.length != 0
                    ? twoDaysRecordTab()
                    : Container(),
            Container(
              padding: EdgeInsets.all(2),
              child: initPageLoading
                  ? Center(child: CircularProgressIndicator())
                  : shiftWiseList.length > 0
                      ? DateToDateRecordTab()
                      : Center(
                          child: Container(
                            padding: EdgeInsets.all(15.0),
                            child: TextWidget("No Shifts Available",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                        ),
            ),
          ],
        ),
        bottomSheet: initPageLoading
            ? SizedBox()
            : Container(
                height: isPortrait == Orientation.portrait ? height * 0.12 : height * 0.2,
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("Grand Total: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget paymentBasedFilterList(List<Sales>? item) {
// Shift-wise and payment type reports are both managed by a single variable

    var groupListByPaymentName = item != null ? item.groupListsBy((element) => element.shiftName) : null;

    Map<String?, double> totalAmountByKeyword = {};
    int count = 0;
    item?.forEach((element) {
      final key = element.shiftName;
      if (totalAmountByKeyword.containsKey(key)) {
        totalAmountByKeyword[key] = (totalAmountByKeyword[key] ?? 0) + double.parse(element.totalAmount.toString());
      } else {
        totalAmountByKeyword[key] = double.parse(element.totalAmount.toString());
      }
      count++;
    });

    return Padding(
      padding: EdgeInsets.only(bottom: 0),
      child: Column(
        children: [
          for (var i in groupListByPaymentName!.keys) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: ReportListView(
                isTwoDaysRecord == true ? 1 : 2,
                item,
                reportFor: ReportFor.payment,
                isShiftWiseReport: true,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 0),
              child: Container(
                color: Colors.black12,
                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Total",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "₹ ${double.tryParse(totalAmountByKeyword[i].toString())?.toStringAsFixed(2)} ",
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "${groupListByPaymentName[i] != null ? groupListByPaymentName[i]![0].shiftName ?? "None" : ""}",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            count.toString(),
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget twoDaysRecordTab() {
    return Container(
        width: width,
        alignment: Alignment.topCenter,
        child: SingleChildScrollView(
          child: Column(
            children: [
              if (shiftWiseReportList.isNotEmpty) ...{
                for (var i in groupList.keys) ...{
                  if (groupList[i].length != 0) ...{
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                      child: Column(
                        children: <Widget>[
                          //if (groupList[i][0].paymentTypeName != null) ...{
                          Container(
                            color: Colors.black12,
                            padding: EdgeInsets.only(left: 5.0, right: 5.0),
                            child: Row(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      groupList[i][0].shiftName ?? "None",
                                      style: TextStyle(
                                        fontSize: textSizeMedium,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // } else ...{
                          //   SizedBox()
                          // },
                          paymentBasedFilterList(groupList[i]),
                        ],
                      ),
                    )
                  } else ...{
                    SizedBox()
                  },
                },
                SizedBox(
                  height: 100,
                )
              }
            ],
          ),
        ));
  }

  Widget DateToDateRecordTab() {
    return SingleChildScrollView(
      child: Column(
        children: <Widget>[
          SizedBox(height: 20),
          shiftWiseFilterWidget(),
          SizedBox(height: 10),
          // isReportView
          //     ?
          BootstrapRow(children: [
            BootstrapCol(
                sizes: 'col-md-8 col-sm-12',
                child: Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: DateRangePickerWidget(
                      isBigLayout: isBigLayout,
                      fromDate: fromDate,
                      toDate: toDate,
                      onPressFromDate: (value) {
                        if (value != null) {
                          fromDate = value;
                        }
                      },
                      onPressToDate: (value) {
                        if (value != null) {
                          toDate = value;
                        }
                      },
                    ))),
            BootstrapCol(
                sizes: 'col-md-4 col-sm-12',
                child: Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Row(
                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () async {
                          if (PermissionFunctions.checkPermission(272)) {
                            setState(() {
                              getShiftWiseReport(fromDate, toDate, selectedShift?.shiftId ?? '');
                            });
                          }
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 40,
                          width: isBigLayout ? width * 0.15 : width * 0.98,
                          child: Text("Submit",
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                              )),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: buttonThemeColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ]),
          // : Container(),
          isLoading
              ? Container(width: 50, height: 50, child: CircularProgressIndicator())
              : shiftWiseReportList.length != 0
                  ? Container(
                      width: width,
                      alignment: Alignment.topCenter,
                      child: Column(
                        children: [
                          if (shiftWiseReportList.isNotEmpty) ...{
                            for (var i in groupList.keys) ...{
                              if (groupList[i].length != 0) ...{
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                  child: Column(
                                    children: <Widget>[
                                      //if (groupList[i][0].paymentTypeName != null) ...{
                                      Container(
                                        color: Colors.black12,
                                        padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                        child: Row(
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                child: Text(
                                                  groupList[i][0].shiftName ?? "None",
                                                  style: TextStyle(
                                                    fontSize: textSizeMedium,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // } else ...{
                                      //   SizedBox()
                                      // },
                                      paymentBasedFilterList(groupList[i]),
                                    ],
                                  ),
                                )
                              } else ...{
                                SizedBox()
                              },
                            },
                            SizedBox(
                              height: 100,
                            )
                          }
                        ],
                      ))
                  : Container(),
        ],
      ),
    );
  }

  Widget shiftWiseFilterWidget() {
    return Container(
      height: 40,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(left: 10.0, right: 10.0),
            child: TextWidget("Filter Reports : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
          ),
          Container(
            width: width / 1.7,
            child: InputDecorator(
              decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton(
                  value: selectedShift,
                  items: dropdownMenuShiftItems,
                  onChanged: onChangeDropdownShiftItem,
                  isExpanded: true,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
