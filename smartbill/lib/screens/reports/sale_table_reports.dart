// ignore_for_file: non_constant_identifier_names

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
// ignore: implementation_imports
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/reports_repository.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_table_reports.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/productwise_sales.dart';

import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:pdf/widgets.dart' as pw;
import 'package:smartbill/utils/error_logs/error_logs_function.dart';

import '../../utils/common_function/list_class.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/shop_tables_db_script.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

class SaleTableReports extends StatefulWidget {
  static var tag = "/T5Settings";

  @override
  SaleTableReportsState createState() => SaleTableReportsState();
}

class SaleTableReportsState extends State<SaleTableReports> {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  ProductwiseSalesReports productWiseSalesReports = new ProductwiseSalesReports();
  final dbHelper = DatabaseHelper.instance;
  List<DropdownMenuItem<ShopTable>>? dropdownMenuItems;
  List<ShopTable> shopTablesList = [];
  List<TableReports> tableReportList = [];
  ShopTable? selectTable;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  bool connected = false;
  bool multiPrint = false;
  String tableId = "All";
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  double totalQty = 0;
  var groupList;
  bool isReportView = false;
  PrinterDevices? reportPrinter;
  bool isLoading = false;
  bool isBigLayout = false;
  bool initPageLoading = false;
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  //------------------------------------Start Printer Connection------------------------------------------------------//

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    isReportView = await PermissionFunctions.checkPermission(208, viewToast: false);
    if (MultiPrintValue == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter);
    }
  }

  //------------------------------------End Printer Connection------------------------------------------------------//

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked;
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //GET TOTAL FUNCTION
  getTotalDetails() {
    totalAmount = 0.0;
    totalQty = 0;
    setState(() {
      for (TableReports p in tableReportList) {
        totalQty += double.parse(p.quantity.toString());
        totalAmount += double.parse(p.amount.toString());
      }
    });
  }

  //------------------------------------------------Start DropDown Function-------------------------------------------------//

  //GET ALL DETAILS BY PRODUCT
  void getTableDetails() async {
    if (ListUtility.shopTableList.isNotEmpty) {
      setState(() {
        shopTablesList.clear();
        isLoading = true;
        shopTablesList.add(new ShopTable(shopTableId: "", shopTableName: "All"));
        shopTablesList.addAll(ListUtility.shopTableList);
        isLoading = false;
      });
    }

    if (shopTablesList.length != 0) {
      setState(() {
        dropdownMenuItems = buildDropdownMenuItems(shopTablesList);
        selectTable = dropdownMenuItems?[0].value;
      });
    }
  }

  //PRODUCT LIST VIEW
  List<DropdownMenuItem<ShopTable>> buildDropdownMenuItems(List tableList) {
    // ignore: deprecated_member_use
    List<DropdownMenuItem<ShopTable>> items = [];
    for (ShopTable p in tableList) {
      items.add(
        DropdownMenuItem(
          value: p,
          child: Text(p.shopTableName ?? ""),
        ),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownItem(ShopTable? tableList) {
    if (PermissionFunctions.checkPermission(209)) {
      setState(() {
        selectTable = tableList;
      });
    }
  }

  //------------------------------------------------End DropDown Function-------------------------------------------------//

  /// Get table  reports from Api

  getTableReports(fromDate, toDate, tableId) async {
    try {
      setState(() {
        isLoading = true;
      });

      await getTableReportsById(fromDate, toDate, tableId: tableId).then((value) {
        if (value.status == 1) {
          tableReportList = value.result ?? [];
          groupList = tableReportList.groupListsBy((element) => element.tableID);
          getTotalDetails();
        } else {
          showToast(value.message ?? "");
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        isLoading = false;
      });
    }
  }

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  initFunction() async {
    setState(() {
      initPageLoading = true;
    });
    await initSettings();
    getTableDetails();
    setState(() {
      initPageLoading = false;
    });
  }

  salesTablePrint() async {
    if (reportPrinter?.printerFormat == "Default") {
      if (reportPrinter?.paperSize == 58) {
        await tableReportPrintLineBuildFunction();
      } else {
        await tableReportPrintLineBuildFunction80MM();
      }
    } else {
      if (reportPrinter?.paperSize == 58) {
        await tableReportPrintLineBuildFunctionTVS();
      } else {
        await tableReportPrintLineBuildFunctionTVS80MM();
      }
    }
  }

  @override
  void dispose() {
    if (!multiPrint) {
      printerUtilities.disconnect();
    }
    super.dispose();
  }

  startPrintFunction() async {
    try {
      LoadingAlertWidget.onLoading(context);;
      if (PermissionFunctions.checkPermission(210)) {
        if (multiPrint) {
          reportPrinter = printerUtilities.getReportPrinter();
          await printerUtilities.initPrint(reportPrinter);
          await salesTablePrint();
        } else {
          await salesTablePrint();
        }
      }
      LoadingAlertWidget.onStopping();
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "startPrintFunction - SalesTableReports");
    }
  }

  iconTopWidget() {
    return Row(
      children: [
        IconButton(
          iconSize: 25,
          icon: Icon(Icons.print),
          onPressed: () {
            startPrintFunction();
          },
          color: Colors.white,
        ),
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("Text Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(211)) {
                      Navigator.pop(context);
                      await tableReportTextBuildFunction();
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.share),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(212)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(null);
                    }
                  },
                ),
              ),
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report"),
                  onTap: () async {
                    if (PermissionFunctions.checkPermission(213)) {
                      Navigator.pop(context);
                      await pdfReportBuildFunction(DateTime.now().toString(), context: context);
                    }
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Table Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[tableReportList.isNotEmpty && PermissionFunctions.checkPermission(210, viewToast: false) ? iconTopWidget() : Container()],
      ),
      body: Container(
        child: initPageLoading
            ? Center(child: CircularProgressIndicator())
            : shopTablesList.length != 0
                ? SingleChildScrollView(
                    child: Column(
                      children: <Widget>[
                        SizedBox(height: 20),
                        Container(
                          height: 40,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              Container(
                                padding: EdgeInsets.only(left: 10.0, right: 10.0),
                                child: TextWidget("Filter Reports : ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                              ),
                              Container(
                                width: width / 1.7,
                                child: InputDecorator(
                                  decoration: InputDecoration(
                                    contentPadding: EdgeInsets.symmetric(horizontal: 5.0, vertical: 0.0),
                                    // labelText: 'Printer Format',
                                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                                  ),
                                  child: DropdownButtonHideUnderline(
                                    child: DropdownButton(
                                      value: selectTable,
                                      items: dropdownMenuItems,
                                      onChanged: onChangeDropdownItem,
                                      isExpanded: true,
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(height: 10),
                        Container(
                          padding: EdgeInsets.all(2),
                          alignment: Alignment.center,
                          width: width,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            mainAxisAlignment: MainAxisAlignment.start,
                            children: [
                              isReportView
                                  ? BootstrapRow(children: [
                                      BootstrapCol(
                                          sizes: 'col-md-8 col-sm-12',
                                          child: Padding(
                                            padding: const EdgeInsets.only(top: 10),
                                            child: Row(
                                              mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                              children: [
                                                InkWell(
                                                  onTap: () {
                                                    selectDate(context, 'fromDate');
                                                  },
                                                  child: Card(
                                                    elevation: 4,
                                                    child: Padding(
                                                      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          Text(
                                                            "${fnUtilities.convertDate(fromDate.toString())}",
                                                            style: secondaryTextStyle(),
                                                          ),
                                                          SizedBox(
                                                            width: 15,
                                                          ),
                                                          Icon(Icons.calendar_today)
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                InkWell(
                                                  onTap: () {
                                                    selectDate(context, 'toDate');
                                                  },
                                                  child: Card(
                                                    elevation: 4,
                                                    child: Padding(
                                                      padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                                      child: Row(
                                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                        children: [
                                                          Text(
                                                            fnUtilities.convertDate(toDate.toString()),
                                                            style: secondaryTextStyle(),
                                                          ),
                                                          SizedBox(
                                                            width: 15,
                                                          ),
                                                          Icon(Icons.calendar_today)
                                                        ],
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )),
                                      BootstrapCol(
                                          sizes: 'col-md-4 col-sm-12',
                                          child: Padding(
                                            padding: const EdgeInsets.only(top: 10.0),
                                            child: Row(
                                              mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                              children: [
                                                InkWell(
                                                  onTap: () async {
                                                    if (PermissionFunctions.checkPermission(208)) {
                                                      setState(() {
                                                        getTableReports(
                                                          fromDate,
                                                          toDate,
                                                          selectTable?.shopTableId,
                                                        );
                                                      });
                                                    }
                                                  },
                                                  child: Container(
                                                    alignment: Alignment.center,
                                                    height: 40,
                                                    width: isBigLayout ? width * 0.15 : width * 0.98,
                                                    child: Text("Submit",
                                                        style: TextStyle(
                                                          color: secondaryTextColor,
                                                          fontSize: 14,
                                                          fontWeight: FontWeight.w700,
                                                        )),
                                                    decoration: BoxDecoration(
                                                      borderRadius: BorderRadius.circular(5),
                                                      color: buttonThemeColor,
                                                    ),
                                                  ),
                                                ),
                                              ],
                                            ),
                                          )),
                                    ])
                                  : Container(),
                              15.height,
                              isLoading
                                  ? Container(
                                      width: width,
                                      child: Center(
                                        child: Container(height: 40, child: CircularProgressIndicator()),
                                      ),
                                    )
                                  : Container(
                                      width: width,
                                      alignment: Alignment.topCenter,
                                      child: Column(
                                        children: [
                                          // ignore: sdk_version_ui_as_code
                                          if (tableReportList.isNotEmpty) ...[
                                            for (var i in groupList.keys)
                                              Padding(
                                                padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                                                child: Column(
                                                  children: <Widget>[
                                                    Padding(
                                                      padding: const EdgeInsets.symmetric(vertical: 8),
                                                      child: Container(
                                                        alignment: Alignment.centerLeft,
                                                        child: Text(
                                                          groupList[i][0].tableName.toString(),
                                                          style: TextStyle(fontWeight: FontWeight.bold),
                                                        ),
                                                      ),
                                                    ),
                                                    ListView.builder(
                                                        padding: const EdgeInsets.only(right: 0),
                                                        itemCount: groupList[i].length,
                                                        shrinkWrap: true,
                                                        reverse: true,
                                                        physics: NeverScrollableScrollPhysics(),
                                                        itemBuilder: (context, index) {
                                                          var item = groupList[i][index];
                                                          return Container(
                                                            alignment: Alignment.centerLeft,
                                                            padding: const EdgeInsets.symmetric(vertical: 2),
                                                            child: Row(
                                                              mainAxisAlignment: MainAxisAlignment.start,
                                                              children: <Widget>[
                                                                Container(
                                                                  width: width / 2.5,
                                                                  alignment: Alignment.centerLeft,
                                                                  child: Text(
                                                                    item.productName +
                                                                        ' - ' +
                                                                        fnUtilities.checkQtyDoubleValue(item.quantity.toString()) +
                                                                        ' x ' +
                                                                        double.parse(item.productPrice.toString()).toStringAsFixed(2),
                                                                  ),
                                                                ),
                                                                Container(
                                                                  width: width / 2,
                                                                  alignment: Alignment.centerRight,
                                                                  child: Text(
                                                                    "Rs. " + double.parse(item.amount.toString()).toStringAsFixed(2),
                                                                    style: TextStyle(fontWeight: FontWeight.bold),
                                                                  ),
                                                                )
                                                              ],
                                                            ),
                                                          );
                                                        }),
                                                    const Divider(
                                                      height: 0.5,
                                                    )
                                                  ],
                                                ),
                                              )
                                          ] else ...[
                                            SizedBox(
                                              height: 200,
                                              child: Align(
                                                  alignment: Alignment.bottomCenter,
                                                  child: Container(
                                                    child: Text(
                                                      "Reports not found",
                                                      style: black16w500,
                                                    ),
                                                  )),
                                            ),
                                          ],
                                        ],
                                      )),
                              SizedBox(
                                height: isPortrait == Orientation.portrait ? height / 7 : height / 4,
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : Center(
                    child: Container(
                      padding: EdgeInsets.all(15.0),
                      child: TextWidget("No Tables Available",
                          textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                    ),
                  ),
      ),
      bottomSheet: initPageLoading
          ? SizedBox()
          : Container(
              height: isPortrait == Orientation.portrait ? height * 0.15 : height / 4,
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child:
                            TextWidget("Total Qty: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("${fnUtilities.checkQtyDoubleValue(totalQty.toString())}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                    ],
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Total Amount: ",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                      Padding(
                        padding: EdgeInsets.only(top: 5.0),
                        child: TextWidget("Rs.${totalAmount.toStringAsFixed(2)}",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                      ),
                    ],
                  ),
                ],
              ),
            ),
    );
  }

//------------------------------------------------Start PrintBuild Function-------------------------------------------------//

  tableReportPrintLineBuildFunction() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        listData += '''
   {"text": "Table: ${groupList[i][0].tableName.toString()}", "size": 1, "alignment": 0},''';
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 9);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(10)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()).padLeft(9)} ${double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i][list].amount.toString()).toStringAsFixed(2).padLeft(12)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(10) + "  " + " ".padLeft(5) + "  " + ' '.padLeft(5) + "       " + " ".padLeft(10)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tables Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 1, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   {"text": "Product    Qty    Rate    Amount", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(42)}", "size": 0, "alignment": 0},
   {"text": "   Total Qty: ${await fnUtilities.checkQtyDoubleValue(totalQty.toString()).padLeft(10)}", "size": 1, "alignment": 2},
   {"text": "Total Amount: ${totalAmount.toStringAsFixed(2).padLeft(10)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  tableReportPrintLineBuildFunctionTVS() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        listData += '''
   {"text": "Table: ${groupList[i][0].tableName.toString()}", "size": 1, "alignment": 0},''';
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 10);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(11)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()).padLeft(3)} ${double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i][list].amount.toString()).toStringAsFixed(2).padLeft(9)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(11) + " ".padLeft(3) + ' '.padLeft(8) + " ".padLeft(9)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tables Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 0, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "Product  Qty Rate  Amount(RS)", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(31)}", "size": 0, "alignment": 0},
   {"text": "   Total Qty: ${await fnUtilities.checkQtyDoubleValue(totalQty.toString()).padLeft(8)}", "size": 1, "alignment": 2},
   {"text": "Total Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  tableReportPrintLineBuildFunction80MM() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        listData += '''
   {"text": "Table: ${groupList[i][0].tableName.toString()}", "size": 1, "alignment": 0},''';
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 20);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(21)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()).padLeft(10)} ${double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1).padLeft(13)} ${double.parse(groupList[i][list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + ' '.padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tables Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 0, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "   Total Qty: ${await fnUtilities.checkQtyDoubleValue(totalQty.toString()).padLeft(15)}", "size": 1, "alignment": 2},
   {"text": "Total Amount: ${totalAmount.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

  tableReportPrintLineBuildFunctionTVS80MM() async {
    String listData = "";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        listData += '''
   {"text": "Table: ${groupList[i][0].tableName.toString()}", "size": 1, "alignment": 0},''';
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 20);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              listData +=
                  '''{"text": "${productSplit[p].padRight(21)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()).padLeft(10)} ${double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1).padLeft(13)} ${double.parse(groupList[i][list].amount.toString()).toStringAsFixed(2).padLeft(17)}", "size": 0, "alignment": 0},''';
            } else {
              listData +=
                  '''{"text": "${productSplit[p].padRight(21) + " ".padLeft(10) + ' '.padLeft(13) + " ".padLeft(17)}", "size": 0, "alignment": 0},''';
            }
          }
        }
      }
    }

    String printArray = '''
  [
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "Tables Report", "size": 1, "alignment": 1},
   {"text": " ", "size": 0, "alignment": 0},
   {"text": "From Date: ${DateFormat("dd-MM-yyyy hh:mm a").format(fromDate)}", "size": 0, "alignment": 0},
   {"text": "To Date  : ${DateFormat("dd-MM-yyyy hh:mm a").format(toDate)}", "size": 0, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "Product              Qty      Rate    Amount(RS)", "size": 1, "alignment": 0},
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   $listData
   {"text": "${await printerUtilities.returnDashedLines(64)}", "size": 0, "alignment": 0},
   {"text": "   Total Qty: ${await fnUtilities.checkQtyDoubleValue(totalQty.toString()).padLeft(15)}", "size": 1, "alignment": 2},
   {"text": "Total Amount: ${totalAmount.toStringAsFixed(2).padLeft(15)}", "size": 1, "alignment": 2}
   ]
''';
    await printerUtilities.commonReportPrint(context, printArray);
  }

//------------------------------------------------End PrintBuild Function-------------------------------------------------//

//------------------------------------------------Start ShareBuild Function-------------------------------------------------//

  tableReportTextBuildFunction() async {
    String data = "";
    data += "\nTables Report";
    data += "\n";
    data += "\nFrom Date: ${fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false)}";
    data += "\nTo Date: ${fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false)}";
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nProduct       Qty      Rate     Amount";
    for (var i in groupList.keys) {
      if (groupList[i]?.length != 0) {
        data += "\n";
        data += "\nTable: ${groupList[i][0].tableName.toString()}";
        for (int list = 0; list < groupList[i].length; list++) {
          var productSplit = printerUtilities.splitStringByLength(groupList[i][list].productName.toString(), 8);
          for (int p = 0; p < productSplit.length; p++) {
            if (p == 0) {
              data +=
                  "\n${productSplit[p].padRight(9)} ${fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()).padLeft(9)} ${double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1).padLeft(8)} ${double.parse(groupList[i][list].amount.toString()).toStringAsFixed(2).padLeft(12)}";
            } else {
              data += "\n${productSplit[p].padRight(9) + " ".padLeft(9) + " ".padLeft(8) + " ".padLeft(8) + " ".padLeft(12)}";
            }
          }
        }
      }
    }
    data += "\n${await printerUtilities.returnDashedLines(45)}";
    data += "\nTotal Qty: ${await fnUtilities.checkQtyDoubleValue(totalQty.toString()).padLeft(10)}";
    data += "\nTotal Amount: ${totalAmount.toStringAsFixed(2).padLeft(8)}";
    await printerUtilities.shareCommonReport(data, "Tables");
  }

//------------------------------------------------End ShareBuild Function-------------------------------------------------//

//------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pw.Widget pdfTablesReportBodyBuildFunction() {
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Tables Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 130, child: pw.Text("Product", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 80, child: pw.Text("Qty", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 90, child: pw.Text("Price", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
            pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (groupList != null)
            for (var i in groupList.keys)
              for (int list = 0; list < groupList[i].length; list++)
                list == 0
                    ? pw.Column(children: [
                        groupList[i][0].tableID != null
                            ? pw.Padding(
                                padding: const pw.EdgeInsets.symmetric(vertical: 8),
                                child: pw.Container(
                                  alignment: pw.Alignment.centerLeft,
                                  child: pw.Text("Table: " + groupList[i][0].tableName.toString(),
                                      style: pw.TextStyle(fontSize: 13, fontWeight: pw.FontWeight.bold)),
                                ),
                              )
                            : pw.Container(),
                        pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                          pw.Container(
                              width: 40,
                              child: pw.Text((list + 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                          pw.Container(
                              width: 130,
                              child: pw.Text(groupList[i][list].productName.toString(),
                                  style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                          pw.Container(
                              width: 80,
                              child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()),
                                  textAlign: pw.TextAlign.center, style: const pw.TextStyle(fontSize: 10))),
                          pw.Container(
                              width: 90,
                              child: pw.Text(double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                          pw.Container(
                              width: 95,
                              child: pw.Text(double.parse(groupList[i][list].amount.toString()).toStringAsFixed(1),
                                  textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                        ]),
                      ])
                    : pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                        pw.Container(
                            width: 40, child: pw.Text((list + 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                        pw.Container(
                            width: 130,
                            child: pw.Text(groupList[i][list].productName.toString(),
                                style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                        pw.Container(
                            width: 80,
                            child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i][list].quantity.toString()),
                                textAlign: pw.TextAlign.center, style: const pw.TextStyle(fontSize: 10))),
                        pw.Container(
                            width: 90,
                            child: pw.Text(double.parse(groupList[i][list].productPrice.toString()).toStringAsFixed(1),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                        pw.Container(
                            width: 95,
                            child: pw.Text(double.parse(groupList[i][list].amount.toString()).toStringAsFixed(1),
                                textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                      ]),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Items :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(tableReportList.length.toString(),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Qty :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(fnUtilities.checkQtyDoubleValue(totalQty.toString()),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalAmount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pdfReportBuildFunction(String? currentDate, {BuildContext? context}) {
    String fileName = "Tables_Report";
    sharePDF.savePDFFormat(pdfTablesReportBodyBuildFunction(), fileName, currentDate, context: context);
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//
}
