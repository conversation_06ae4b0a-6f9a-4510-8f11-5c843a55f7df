// ignore_for_file: non_constant_identifier_names, unnecessary_statements

import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/src/iterable_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:pdf/pdf.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports/get_reports.dart';
import 'package:smartbill/screens/reports/widgets/alert_multi_select_checkbox.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/sales_reports_printer.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_widgets/custom_dropdown.dart';
import 'package:smartbill/utils/dbutils/reports_db_script.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:smartbill/utils/reports_utils/reports_utils.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../Repositories/download_reports_repository.dart';
import '../../Repositories/reports_repository.dart';
import '../../main.dart';
import '../../model/payment_category_model.dart';
import '../../model/payment_type.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/constants/images.dart';
import '../../utils/dbutils/common_db_script.dart';
import '../../utils/common_function/fn_utilities.dart';
import 'package:pdf/widgets.dart' as pw;
import '../../utils/common_function/list_class.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/strings_keys.dart';
import '../../utils/dbutils/db_crud_script.dart';
import '../../utils/dbutils/shop_tables_db_script.dart';
import '../../utils/printer_utilities/report_print.dart';
import '../../utils/printer_utilities/share_pdf.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import 'reports_list_view.dart';

class ReportsPage extends StatefulWidget {
  bool isDeleteReport;

  ReportsPage({Key? key, this.isDeleteReport = false}) : super(key: key);

  @override
  _ReportsPageState createState() => _ReportsPageState();
}

class _ReportsPageState extends State<ReportsPage> with SingleTickerProviderStateMixin {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  SalesReportsPrinter salesReportsPrinter = new SalesReportsPrinter();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SharePDF sharePDF = SharePDF();
  ReportPrint reportPrint = ReportPrint();
  FnUtilities fnUtilities = new FnUtilities();
  DBOperations dbOperations = new DBOperations();
  DBFuctionsScript dbScript = DBFuctionsScript();
  final dbHelper = DatabaseHelper.instance;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();
  TabController? controller;
  bool connected = false;
  bool isShortPrint = true;
  bool multiPrint = false;
  double totAmount = 0;
  bool isLock = false;
  var width;
  var height;
  double totQty = 0;
  int selectIndex = 0;
  double totDiscount = 0.0;
  bool isResetInvoice = false;
  bool isReportView = false;
  String? roleId;
  String paymentId = "";
  GetReports? getReports = GetReports();
  List<Sales> reportList = [];
  List<Transactions> staticTransactionsList = [];
  List<ShopTable> shopTablesList = [];
  bool isLoading = false;
  PrinterDevices? reportPrinter;
  bool isBigLayout = false;
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  StateSetter? updateState;
  List<PaymentCategory> paymentCategoryList = [];
  List<int> selectedPaymentCatListItems = [];
  PaymentCategory? selectedPaymentCategory;
  List<DropdownMenuItem<PaymentCategory>>? paymentCategorydropdownMenuItems;
  List<Sales> tempSalesReportList = [];
  String? paymentCategoryId;
  String? paymentCategoryName;
  bool splitInvoiceByPayment = false;

  getAllPaymentCategorys() async {
    setState(() {
      paymentCategoryList.clear();
      if (splitInvoiceByPayment) {
        paymentCategoryList.addAll(ListUtility.paymentCatList);
        selectedPaymentCatListItems = [1, 2, 3];
      } else {
        paymentCategoryList =
            ListUtility.paymentCatList.where((element) => element.paymentCategoryID != 1 && element.paymentCategoryID != 2).toList();
        selectedPaymentCatListItems = [3];
      }
      paymentCategoryList.add(PaymentCategory(id: 5, paymentCategoryName: "Advance Orders", paymentCategoryID: 5));
    });
  }

  submitAction(List<int> selectedPaymentListItems) {
    if (PermissionFunctions.checkPermission(262)) {
      selectedPaymentCatListItems.clear();
      selectedPaymentCatListItems.addAll(selectedPaymentListItems);
      paymentCategoryId = selectedPaymentCatListItems.join(',');
      setState(() {
        setPaymentCategoryName(selectedPaymentCatListItems);
      });
      if (selectIndex < 2) {
        getSalesListByDateToDate(selectIndex, paymentCategoryId: paymentCategoryId);
      }
    }
  }

  setPaymentCategoryName(List<int> selectedPaymentCategoryIds) {
    List<PaymentCategory> tempPaymentCategoryList = [];
    tempPaymentCategoryList =
        paymentCategoryList.where((element) => selectedPaymentCategoryIds.map((e) => e).contains(element.paymentCategoryID)).toList();
    paymentCategoryName = tempPaymentCategoryList.map((e) => e.paymentCategoryName).join(',');
  }

  //GET PRINTER DETAILS
  initSettings() async {
    var MultiPrintValue = fnUtilities.workSpaceDeviceSetValue(key_multiPrint);
    // isReportView = await rolePermissions.isViewPermissions(await dbOperations.returnPermission("Reports"));
    isReportView = ((PermissionFunctions.checkPermission(190, viewToast: false) && PermissionFunctions.checkPermission(191, viewToast: false)) ||
        (PermissionFunctions.checkPermission(268, viewToast: false) && PermissionFunctions.checkPermission(269, viewToast: false)));
    var RoleId = await fnUtilities.workSpaceUserSetValue(key_roleId);
    if (MultiPrintValue == "1") {
      multiPrint = true;
    } else {
      multiPrint = false;
      reportPrinter = printerUtilities.getReportPrinter();
      await printerUtilities.initPrint(reportPrinter!);
    }
    setState(() {
      isResetInvoice = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
      roleId = RoleId;
    });
  }

  //CHECK BLUETOOTH CONNECTION
  void connect(BluetoothDevice? _device) {
    if (_device == null) {
    } else {
      try {
        bluetooth.isConnected.then((isConnected) {
          if (!isConnected!) {
            bluetooth.connect(_device).catchError((error) {
              setState(() => connected = false);
            });
            setState(() => connected = true);
          }
        });
      } catch (e) {}
    }
  }

  //BLUETOOTH DISCONNECTION
  void disconnect() {
    try {
      bluetooth.disconnect();
    } catch (e) {}
  }

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child ?? Container(),
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked;
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  //CONVERT STRING INTO DATE FUNCTION
  String convertDate(originalDate) {
    var date = DateFormat("dd-MM-yyyy").format(DateTime.parse("$originalDate"));
    return date;
  }

  //PRINTER FUNCTION USING START AND END DATE
  startPrint(fromDate, toDate) async {
    printReports(getReports);
  }

  //PRINTER REPORT FUNCTION
  printReports(GetReports? getReports) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;

      if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "1") {
        multiPrint = true;
      } else {
        multiPrint = false;
      }
      salesReportsPrinter.SalesReportsPrinterList = reportList;
      salesReportsPrinter.fromDate = fromDate.toString();
      salesReportsPrinter.toDate = toDate.toString();
      salesReportsPrinter.totQty = getReports?.totalQty.toString();
      salesReportsPrinter.totAmount = getReports?.totalAmount.toString();
      salesReportsPrinter.isShort = isShortPrint;
      if (multiPrint == true) {
        reportPrinter = printerUtilities.getReportPrinter();
        await printerUtilities.initPrint(reportPrinter!);

        await Future.delayed(Duration(seconds: 2));

        await reportPrint.startFullReportPrint(context, salesReportsPrinter);
        disconnect();
        LoadingAlertWidget.onStopping();
      } else {
        reportPrint.startFullReportPrint(context, salesReportsPrinter);
        LoadingAlertWidget.onStopping();
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "printReports - ReportsPage");
    }
  }

  initFunction() async {
    getSalesListByDateToDate(selectIndex);
    await initSettings();
    controller = TabController(length: isReportView ? 3 : 1, vsync: this);
    controller?.addListener(() {
      setState(() {
        if (controller?.index == 0) {
          selectIndex = 0;
          selectedPaymentCategory = paymentCategoryList.first;
          selectedPaymentCatListItems = splitInvoiceByPayment ? [1, 2, 3] : [3];
          paymentCategoryId = null;
        }
        if (isReportView) {
          if (controller?.index == 1) {
            selectIndex = 1;
            selectedPaymentCategory = paymentCategoryList.first;
            selectedPaymentCatListItems = splitInvoiceByPayment ? [1, 2, 3] : [3];
            paymentCategoryId = null;
          }
          if (controller?.index == 2) {
            setState(() {
              isLoading = true;
              selectIndex = 2;
              fromDate = DateTime.now();
              toDate = DateTime.now();
              reportList = [];
              getReports = new GetReports();
              isLoading = false;
              selectedPaymentCategory = paymentCategoryList.first;
              selectedPaymentCatListItems = splitInvoiceByPayment ? [1, 2, 3] : [3];
              paymentCategoryId = null;
            });
          }
        }
        if (selectIndex != 2) {
          getSalesListByDateToDate(selectIndex);
        }
      });
    });
  }

  getSalesListByDateToDate(int index, {String? paymentCategoryId}) async {
    try {
      setState(() {
        getReports = new GetReports();
        isLoading = true;
      });
      var now = new DateTime.now();
      if (index == 0) {
        fromDate = now.subtract(
            Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
        toDate = now.subtract(
            Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
        paymentCategoryId = selectedPaymentCatListItems.join(',');
        setPaymentCategoryName(selectedPaymentCatListItems);
      } else if (index == 1) {
        fromDate = now.subtract(Duration(
            days: 2, hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
        toDate = now.subtract(
            Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
        paymentCategoryId = selectedPaymentCatListItems.join(',');
        setPaymentCategoryName(selectedPaymentCatListItems);
      } else if (index == 2) {
        fromDate = fromDate.subtract(Duration(
            hours: fromDate.hour,
            minutes: fromDate.minute,
            seconds: fromDate.second,
            milliseconds: fromDate.millisecond,
            microseconds: fromDate.microsecond));
        toDate = toDate;
        paymentCategoryId = selectedPaymentCatListItems.join(',');
        setPaymentCategoryName(selectedPaymentCatListItems);
      }
      if (index < 2) {
        var result = await ReportDetailsDB.getAllSalesByDateToDateForReport(fromDate, toDate,
            paymentId: paymentId, deletedRecord: widget.isDeleteReport ? true : false, paymentCategoryId: paymentCategoryId);
        getReports = result != null ? GetReports.fromDBJson(result) : null;
        await Future.delayed(Duration(milliseconds: 1200));
      } else if (index == 2) {
        var result;
        if (widget.isDeleteReport) {
          result = await getDeltedSalesReportByDatToDate(fromDate, toDate, paymentCategoryID: paymentCategoryId);
        } else {
          result = await getSalesReportByDatToDate(fromDate, toDate, paymentCategoryID: paymentCategoryId);
        }
        if (result.status == 1) {
          if (result.result != null) {
            getReports = result.result;
          }
        } else {
          showToast(result.message);
          setState(() {
            reportList = [];
            getReports = new GetReports();
            isLoading = false;
          });
        }
      }

      setState(() {
        if (getReports != null) {
          reportList = getReports?.reportList ?? [];
          tempSalesReportList = reportList;
          assignPaymentAndTable();
          isLoading = false;
        } else {
          reportList = [];
          getReports = new GetReports();
          isLoading = false;
        }
      });
    } catch (e) {
      log(e.toString());
      setState(() {
        reportList = [];
        getReports = new GetReports();
        isLoading = false;
      });
    }
  }

  getSalesListByPaymentCategoryID(String paymentCategoryID) {
    if (paymentCategoryID != 0) {
      setState(() {
        reportList.clear();
        reportList = tempSalesReportList.where((element) => element.paymentCategoryID == paymentCategoryID).toList();
      });
    } else {
      setState(() {
        reportList.clear();
        reportList.addAll(tempSalesReportList);
      });
    }
  }

  geTransactionsListByDateToDate() async {
    try {
      if (selectIndex == 2) {
        await ReportsUtils.getTransactionListFromApi(fromDate, toDate, paymentId: paymentId).then((value) {
          staticTransactionsList = value.salesTransaction ?? [];
          print(staticTransactionsList);
        });
      }
    } catch (e) {
      log(e.toString());
      setState(() {
        staticTransactionsList = [];
      });
    }
  }

  checkPaymentCategory() {
    if (selectedPaymentCategory == null) {
      setState(() {
        selectedPaymentCategory = null;
      });
    } else {
      return selectedPaymentCategory;
    }
  }

  assignPaymentAndTable() {
    for (Sales s in getReports?.reportList ?? []) {
      setState(() {
        if (s.paymentType != null) {
          try {
            var paymentDetails = ListUtility.paymentTypeList.firstWhereOrNull(
              (element) => element.paymentTypeID == s.paymentType,
            );
            if (paymentDetails != null) {
              s.paymentName = paymentDetails.paymentName;
            }
          } catch (ex) {
            s.paymentName = "";
          }
        }

        if (s.fkTableID != null) {
          try {
            var tableDetail = shopTablesList.firstWhereOrNull((element) => element.shopTableId == s.fkTableID);
            if (tableDetail != null) {
              s.tableName = tableDetail.shopTableName;
            }
          } catch (ex) {
            s.tableName = "";
          }
        }
      });
    }
  }

  Future<List<Transactions>> getTransactionDetailsBySaleId(salesId) async {
    List<Transactions> transactionList = [];
    CommonDB commonDB = CommonDB();
    try {
      if (selectIndex < 2) {
        var transactListRows = await commonDB.getAllListDataBasedOneColumn('SalesTransactions', 'fkSalesId', salesId);
        transactListRows.forEach((row) => transactionList.add(Transactions.fromMap(row)));
      } else {
        transactionList.addAll(staticTransactionsList.where((element) => element.salesId == salesId).toList());
      }

      return transactionList;
    } catch (e) {
      return transactionList;
    }
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName, {bool isSharePDF = false}) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      // await requestStoragePermission();
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context, isSharePDF: isSharePDF);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
    }
  }

  /// check if user first time open
  Future<void> initPaymentCategory() async {
    splitInvoiceByPayment = fnUtilities.workSpaceSetValues(splitInvoiceByPaymentKey) == "1";
    await fnUtilities.getAllPaymentCategoryFromLocaldb();
    await getAllPaymentCategorys();
  }

  initPageDetails() async {
    await initPaymentCategory();
    await buildReportTypeDropDownList();
    await initFunction();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  @override
  void dispose() {
    if (multiPrint == false) {
      disconnect();
    }
    super.dispose();
  }

  //PRINT TYPE ALERT DIALOG
  // ignore: missing_return
  AlertDialog? dialogForSetPrinterType(BuildContext context) {
    showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              content: Container(
                height: width / 2,
                width: height / 3,
                child: Column(
                  children: [
                    Text("Choose Print Type"),
                    RadioListTile(
                      title: const Text('Short'),
                      value: true,
                      groupValue: isShortPrint,
                      onChanged: (value) {
                        if (value is bool) {
                          setState(() {
                            isShortPrint = value;
                          });
                        }
                      },
                    ),
                    RadioListTile(
                      title: const Text('Long'),
                      value: false,
                      groupValue: isShortPrint,
                      onChanged: (value) {
                        if (value is bool) {
                          setState(() {
                            isShortPrint = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              actions: [
                GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                    startPrint(fromDate, toDate);
                  },
                  child: Container(
                    alignment: Alignment.center,
                    height: 40,
                    width: width / 5,
                    child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
                    decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                  ),
                ),
              ],
            );
          });
        });
    return null;
  }

  iconTopWidget() {
    return Row(
      children: [
        InkWell(
          onTap: () {
            paymentCategoryAlertDialog(context);
          },
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 15.0),
            child: SvgPicture.asset(
              filterIconSVG,
              color: Colors.white,
            ),
          ),
        ),

        reportList.isNotEmpty == true
            ? IconButton(
                iconSize: 25,
                icon: Icon(Icons.print),
                onPressed: () {
                  if (PermissionFunctions.checkPermission(192) || PermissionFunctions.checkPermission(270)) {
                    dialogForSetPrinterType(context);
                  }
                },
                color: Colors.white,
              )
            : Container(),
        // ignore: sdk_version_ui_as_code

        reportList.isNotEmpty
            ? PopupMenuButton(
                icon: const Icon(Icons.more_vert),
                itemBuilder: (BuildContext context) {
                  return <PopupMenuEntry>[
                    // ignore: sdk_version_ui_as_code
                    if (!widget.isDeleteReport) ...[
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text("Short PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/getsalesshortpdfreportbydate", "Sales Short PDF Report",
                                    isSharePDF: true);
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text("Full PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/getsalesfullpdfreportbydate", "Sales Full PDF Report",
                                    isSharePDF: true);
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text("Bill wise PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(downloadReportsRequest, "/api/v2/sales/getsalespdfreportbydate", "Sales PDF Report",
                                    isSharePDF: true);

                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.download),
                          title: Text("Short PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/getsalesshortpdfreportbydate", "Sales Short PDF Report");
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.download),
                          title: Text("Full PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/getsalesfullpdfreportbydate", "Sales Full PDF Report");
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.download),
                          title: Text("Bill wise PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(downloadReportsRequest, "/api/v2/sales/getsalespdfreportbydate", "Sales PDF Report");
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                    ] else ...[
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.share),
                          title: Text("Full PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(194)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/deletesalesreport", "Deleted Sales Full PDF Report",
                                    isSharePDF: true);
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                      PopupMenuItem(
                        child: ListTile(
                          leading: Icon(Icons.download),
                          title: Text("Full PDF Report"),
                          onTap: () async {
                            if (PermissionFunctions.checkPermission(271)) {
                              if (!isLock) {
                                isLock = true;
                                Navigator.pop(context);
                                downloadReportsRequest = DownloadReportsRequest();
                                downloadReportsRequest.fromDate = fromDate.toString();
                                downloadReportsRequest.toDate = toDate.toString();
                                downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                                downloadReportsRequest.paymentCategoryID = paymentCategoryId ?? "1,2,3";
                                await downloadReportFunction(
                                    downloadReportsRequest, "/api/v2/sales/deletesalesreport", "Deleted Sales Full PDF Report");
                                isLock = false;
                              }
                            }
                          },
                        ),
                      ),
                    ]
                  ];
                },
              )
            : Container(),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    isBigLayout = width >= tabletWidth ? true : false;
    return DefaultTabController(
      length: isReportView ? 3 : 1,
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: appThemeColor,
          iconTheme: IconThemeData(
            color: secondaryTextColor, //change your color here
          ),
          title: TextWidget(widget.isDeleteReport ? "Deleted Reports" : "Sales Reports",
              textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          actions: [
            (PermissionFunctions.checkPermission(192, viewToast: false) || (PermissionFunctions.checkPermission(193, viewToast: false))) ||
                    ((PermissionFunctions.checkPermission(270, viewToast: false) || PermissionFunctions.checkPermission(271, viewToast: false)))
                ? iconTopWidget()
                : SizedBox.shrink(),
          ],
          bottom: TabBar(
            onTap: (index) {},
            controller: controller,
            isScrollable: true,
            labelStyle: primaryTextStyle(),
            indicatorColor: redColor,
            physics: BouncingScrollPhysics(),
            labelColor: secondaryTextColor,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.home,
                      color: secondaryTextColor,
                    ),
                    isReportView ? 5.width : 1.width,
                    Text(
                      'Today',
                    ),
                    isReportView ? 1.width : 250.width,
                  ],
                ),
              ),
              // ignore: sdk_version_ui_as_code
              if (isReportView)
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.insert_drive_file,
                        color: secondaryTextColor,
                      ),
                      5.width,
                      Text(
                        '2 Days',
                      ),
                    ],
                  ),
                ),
              // ignore: sdk_version_ui_as_code
              if (isReportView)
                Tab(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        Icons.supervised_user_circle,
                        color: secondaryTextColor,
                      ),
                      5.width,
                      Text(
                        'Date to Date',
                      ),
                    ],
                  ),
                ),
            ],
          ),
        ),
        body: TabBarView(
          controller: controller,
          children: [
            Container(
              padding: EdgeInsets.all(2),
              width: context.width(),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    isLoading
                        ? SizedBox(
                            height: 250,
                            child: Align(alignment: Alignment.center, child: Container(width: 50, height: 50, child: CircularProgressIndicator())))
                        : Padding(
                            padding: const EdgeInsets.only(bottom: 100),
                            child: ReportListView(
                              0,
                              reportList,
                              reportFor: ReportFor.sale,
                              isDeleteReport: widget.isDeleteReport,
                              isSalesReport: true,
                            ),
                          ),
                  ],
                ),
              ),
            ),
            // ignore: sdk_version_ui_as_code
            if (isReportView)
              Container(
                padding: EdgeInsets.all(2),
                width: context.width(),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      isLoading
                          ? SizedBox(
                              height: 250,
                              child: Align(alignment: Alignment.center, child: Container(width: 50, height: 50, child: CircularProgressIndicator())))
                          : Padding(
                              padding: const EdgeInsets.only(bottom: 100),
                              child: ReportListView(
                                1,
                                reportList,
                                reportFor: ReportFor.sale,
                                isDeleteReport: widget.isDeleteReport,
                                isSalesReport: true,
                              ),
                            ),
                      15.height,
                    ],
                  ),
                ),
              ),
            // ignore: sdk_version_ui_as_code
            if (isReportView)
              Container(
                padding: EdgeInsets.all(2),
                width: context.width(),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      BootstrapRow(children: [
                        BootstrapCol(
                            sizes: 'col-md-8 col-sm-12',
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10),
                              child: Row(
                                mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
                                children: [
                                  InkWell(
                                    onTap: () {
                                      selectDate(context, 'fromDate');
                                    },
                                    child: Card(
                                      elevation: 4,
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              "${fnUtilities.convertDate(fromDate.toString())}",
                                              style: secondaryTextStyle(),
                                            ),
                                            SizedBox(
                                              width: 15,
                                            ),
                                            Icon(Icons.calendar_today)
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  InkWell(
                                    onTap: () {
                                      selectDate(context, 'toDate');
                                    },
                                    child: Card(
                                      elevation: 4,
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
                                        child: Row(
                                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              fnUtilities.convertDate(toDate.toString()),
                                              style: secondaryTextStyle(),
                                            ),
                                            SizedBox(
                                              width: 15,
                                            ),
                                            Icon(Icons.calendar_today)
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                        BootstrapCol(
                            sizes: 'col-md-4 col-sm-12',
                            child: Padding(
                              padding: const EdgeInsets.only(top: 10.0),
                              child: Row(
                                mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                                children: [
                                  InkWell(
                                    onTap: () async {
                                      setState(() {
                                        getSalesListByDateToDate(2, paymentCategoryId: paymentCategoryId);
                                      });
                                    },
                                    child: Container(
                                      alignment: Alignment.center,
                                      height: 40,
                                      width: isBigLayout ? width * 0.15 : width * 0.98,
                                      child: Text("Submit",
                                          style: TextStyle(
                                            color: secondaryTextColor,
                                            fontSize: 14,
                                            fontWeight: FontWeight.w700,
                                          )),
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(5),
                                        color: buttonThemeColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                      ]),
                      isLoading
                          ? Container(width: 50, height: 50, child: CircularProgressIndicator())
                          : reportList.length != 0
                              ? Padding(
                                  padding: const EdgeInsets.only(bottom: 100),
                                  child: ReportListView(
                                    2,
                                    reportList,
                                    reportFor: ReportFor.sale,
                                    isDeleteReport: widget.isDeleteReport,
                                    isSalesReport: true,
                                  ),
                                )
                              : SizedBox(
                                  height: 200,
                                  child: Align(
                                      alignment: Alignment.bottomCenter,
                                      child: Container(
                                        child: Text(
                                          "Reports not found",
                                          style: black16w500,
                                        ),
                                      )),
                                ),
                      15.height,
                    ],
                  ),
                ),
              ),
          ],
        ),
        bottomSheet: Container(
          height: getPaymentName() != null
              ? 130
              : reportList.length != 0
                  ? 110
                  : 100,

          //color: Colors.blue,
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("Total Sales: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget(getReports?.totalSales != null ? "${getReports?.totalSales}" : "0",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                      ],
                    ),
                    Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget("Total Qty: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 10.0),
                          child: TextWidget(
                              getReports?.totalQty != null ? "${fnUtilities.checkQtyDoubleValue(getReports?.totalQty.toString() ?? "")}" : "0.0",
                              textColor: primaryTextColor,
                              fontFamily: fontBold,
                              fontSize: textSizeSMedium,
                              isCentered: true),
                        ),
                      ],
                    ),
                  ],
                ),
                if (reportList.length != 0 && paymentCategoryName != null) ...[
                  Row(
                    children: [Text('Payment Category : ${paymentCategoryName ?? ''}')],
                  )
                ],
                Row(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(top: 10.0),
                      child:
                          TextWidget("Total Amount: ", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLarge, isCentered: true),
                    ),
                    Padding(
                      padding: EdgeInsets.only(top: 10.0),
                      child: TextWidget(
                          getReports?.totalAmount != null
                              ? "Rs.${fnUtilities.checkQtyDoubleValue(getReports?.totalAmount?.round().toString() ?? "")}"
                              : "Rs.0.00",
                          textColor: primaryTextColor,
                          fontFamily: fontBold,
                          fontSize: textSizeLarge,
                          isCentered: true),
                    ),
                  ],
                ),
                getPaymentName() != null
                    ? Row(
                        children: [
                          Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child:
                                TextWidget("Showing", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child: TextWidget("${getPaymentName()} ",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                          Padding(
                            padding: EdgeInsets.only(top: 8.0),
                            child: TextWidget("payments",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeSMedium, isCentered: true),
                          ),
                        ],
                      )
                    : Container(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  paymentCategoryAlertDialog(BuildContext context) {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (BuildContext context) {
        return MultiSelectCheckBox(
          paymentCategoryList: paymentCategoryList,
          selectedCategoryListItems: selectedPaymentCatListItems,
          onPressSubmit: submitAction,
        );
      },
    );
  }

  getPaymentName() {
    try {
      String? paymentName =
          ListUtility.paymentTypeList.firstWhere((element) => element.paymentTypeID == paymentId, orElse: () => PaymentType()).paymentName;
      return paymentName;
    } catch (e) {
      return null;
    }
  }

  pw.Widget pdfShortSalesBodyBuildFunction(GetReports getReports) {
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.center,
            children: [pw.Text("Sales Short Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ])
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
            pw.Container(width: 50, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            !isResetInvoice
                ? pw.Container(width: 50, child: pw.Text("No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left))
                : pw.Container(),
            pw.Container(width: 50, child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            // pw.Container(
            //     width: 50,
            //     child: pw.Text("Order",
            //         style: const pw.TextStyle(fontSize: 15),
            //         textAlign: pw.TextAlign.left)),
            pw.Container(width: 120, child: pw.Text("Date", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 100, child: pw.Text("Customer Name", style: const pw.TextStyle(fontSize: 15))),
            pw.Container(width: 100, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (getReports.reportList!.isNotEmpty)
            for (int item = 0; item < getReports.reportList!.length; item++) pdfShortTransactionBuildFunction(item),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Sales :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(getReports.totalSales.toString(),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(double.parse(getReports.totalAmount.toString()).toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  pw.Widget pdfShortTransactionBuildFunction(int index) {
    Sales item = getReports!.reportList![index];
    var invoiceExtension = item.invoiceExtension != null ? item.invoiceExtension : "";
    return pw.Column(children: [
      item != null
          ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
              pw.Container(width: 40, child: pw.Text((index + 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              !isResetInvoice
                  ?
                  // pw.Container(
                  //     width: 50,
                  //     child: pw.Text(item.invoiceNo != null ? item.invoiceNo.toString() : "-",
                  //         style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left))
                  // : pw.Container(),
                  pw.Container(
                      width: 65,
                      child: pw.Text(item.paymentInvoice != null ? item.paymentInvoice ?? "" + invoiceExtension.toString() : "-",
                          style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left))
                  : pw.Container(),
              pw.Container(
                  width: 50,
                  child: pw.Text(item.invoiceId != null ? item.invoiceId.toString() : "-",
                      style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              // pw.Container(
              //     width: 50,
              //     child: pw.Text(
              //        reportList[item].fkTableID != null
              //             ? "Table"
              //             : "Take",
              //         style: const pw.TextStyle(fontSize: 10),
              //         textAlign: pw.TextAlign.left)),
              pw.Container(
                width: 120,
                child: item.date != null
                    ? pw.Text(DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(item.date.toString())), style: const pw.TextStyle(fontSize: 10))
                    : pw.Container(),
              ),
              pw.Container(
                width: 100,
                child: pw.Text(item.customerName != null ? item.customerName.toString() : "-", style: const pw.TextStyle(fontSize: 10)),
              ),
              pw.Container(
                width: 100,
                child: pw.Text(double.parse(item.totalAmount.toString()).toStringAsFixed(1),
                    textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)),
              )
            ])
          : pw.Container()
    ]);
  }

  pdfShortBuildFunction(String currentDate, GetReports getReports) {
    String fileName = "Sales_Short_Report";
    sharePDF.savePDFFormat(pdfShortSalesBodyBuildFunction(getReports), fileName, currentDate);
  }

  Future<pw.Widget> pdfFullSalesBodyBuildFunction(GetReports getReports) async {
    return pw.Wrap(children: [
      pw.SizedBox(height: 10),
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [
          pw.Text(widget.isDeleteReport ? "Deleted Sales Report" : "Sales Short Report",
              style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))
        ],
      ),
      pw.SizedBox(height: 10),
      pw.Container(
          width: 500,
          child: pw.Column(
            crossAxisAlignment: pw.CrossAxisAlignment.start,
            children: [
              pw.Row(children: [
                pw.Container(
                  width: 120,
                  child: pw.Text("From Date ", style: const pw.TextStyle(fontSize: 15)),
                ),
                pw.Container(
                  width: 300,
                  child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(fromDate.toString(), isShowTime: false) ?? ""),
                      style: const pw.TextStyle(fontSize: 15)),
                )
              ]),
              pw.Row(children: [
                pw.Container(
                  width: 120,
                  child: pw.Text("To Date ", style: const pw.TextStyle(fontSize: 15)),
                ),
                pw.Container(
                  width: 300,
                  child: pw.Text(": " + (fnUtilities.convertDateMonthYearFormat(toDate.toString(), isShowTime: false) ?? ""),
                      style: const pw.TextStyle(fontSize: 15)),
                )
              ])
            ],
          )),
      pw.SizedBox(height: 10),
      pw.Divider(thickness: 2),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceEvenly, children: [
        pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        !isResetInvoice
            ? pw.Container(width: 65, child: pw.Text("No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left))
            : pw.Container(),
        pw.Container(width: 50, child: pw.Text("ID", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        // pw.Container(
        //     width: 50,
        //     child: pw.Text("Order",
        //         style: const pw.TextStyle(fontSize: 15),
        //         textAlign: pw.TextAlign.left)),
        pw.Container(width: 120, child: pw.Text("Date", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
        pw.Container(width: 100, child: pw.Text("Customer Name", style: const pw.TextStyle(fontSize: 15))),
        pw.Container(width: 100, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
      ]),
      pw.Divider(thickness: 2),
      // ignore: sdk_version_ui_as_code
      if (getReports.reportList!.isNotEmpty)
        for (int item = 0; item < getReports.reportList!.length; item++) pdfShortTransactionBuildFunction(item),
      pw.Divider(thickness: 2),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 120,
          child: pw.Text("Total Sales :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 150,
          child: pw.Text(getReports.totalSales.toString(),
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 120,
          child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 150,
          child: pw.Text(double.parse(getReports.totalAmount.toString()).toStringAsFixed(2),
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
      pw.SizedBox(height: 10),
      pw.SizedBox(height: 20),
      pw.Row(
        mainAxisAlignment: pw.MainAxisAlignment.center,
        children: [pw.Text("Items Full Report", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))],
      ),
      pw.SizedBox(height: 10),
      pw.Divider(thickness: 2),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
        pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        pw.Container(width: 130, child: pw.Text("Product", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
        pw.Container(width: 80, child: pw.Text("Qty", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
        pw.Container(width: 90, child: pw.Text("Price", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
        pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
      ]),
      pw.Divider(thickness: 2),
      // ignore: sdk_version_ui_as_code
      for (Sales sales in getReports.reportList ?? []) await pdfFullTransactionBodyBuildFunction(sales),
      pw.Divider(thickness: 2),
      getReports.totalDiscount != null
          ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
              pw.Container(
                width: 150,
                child: pw.Text("Total Discount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
              ),
              pw.Container(
                width: 100,
                child: pw.Text(getReports.totalDiscount!.toStringAsFixed(2),
                    textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
              )
            ])
          : pw.Container(),
      getReports.totalQty != null
          ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
              pw.Container(
                width: 150,
                child: pw.Text("Total Qty :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
              ),
              pw.Container(
                width: 100,
                child: pw.Text(fnUtilities.checkQtyDoubleValue(getReports.totalQty.toString()),
                    textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
              )
            ])
          : pw.Container(),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 150,
          child: pw.Text("Total Items :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 100,
          child: pw.Text(getReports.totalSales.toString(),
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
      pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
        pw.Container(
          width: 150,
          child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        ),
        pw.Container(
          width: 100,
          child: pw.Text(getReports.totalAmount?.toStringAsFixed(2) ?? "",
              textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
        )
      ]),
    ]);
  }

  Future<pw.Widget> pdfFullTransactionBodyBuildFunction(sales) async {
    // ignore: prefer_typing_uninitialized_variables
    var groupList;
    List<Transactions> transactionList = [];

    transactionList = await getTransactionDetailsBySaleId(sales.salesId);

    print(transactionList.length);
    groupList = transactionList.groupListsBy((element) => element.fkTableID);
    var invoiceExtension = sales.invoiceExtension != null ? sales.invoiceExtension : "";

    return pw.Column(children: [
      // ignore: sdk_version_ui_as_code
      for (var i in groupList.keys)
        pw.Column(children: [
          sales.paymentInvoice != null && !isResetInvoice
              ?
              // pw.Row(children: [
              //     pw.Text(
              //       "Bill No: " + sales.invoiceNo,
              //       style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
              //     )
              //   ])
              // : pw.Container(),
              pw.Row(children: [
                  pw.Text(
                    "Bill No: " + sales.paymentInvoice + invoiceExtension,
                    style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
                  )
                ])
              : pw.Container(),
          pw.Row(children: [
            pw.Text(
              "Ref No: " + sales.invoiceId,
              style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold),
            ),
          ]),
          pw.Row(children: [
            pw.Text("Date : " + DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(sales.date.toString())),
                style: const pw.TextStyle(fontSize: 13))
          ]),
          sales.customerName != null
              ? pw.Row(children: [pw.Text("Name : " + sales.customerName, style: const pw.TextStyle(fontSize: 13))])
              : pw.Container(),
          pw.SizedBox(height: 5),
          groupList[i][0].fkTableID != null
              ? pw.Padding(
                  padding: const pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Table: " + groupList[i][0].tableName.toString(), style: const pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          sales.discountPrice != null
              ? pw.Padding(
                  padding: const pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Discount: " + double.parse(sales.discountPrice.toString()).toStringAsFixed(1),
                        style: const pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          sales.paymentType != null
              ? pw.Padding(
                  padding: const pw.EdgeInsets.symmetric(vertical: 8),
                  child: pw.Container(
                    alignment: pw.Alignment.centerLeft,
                    child: pw.Text("Payment: " + sales.paymentName.toString(), style: const pw.TextStyle(fontSize: 13)),
                  ),
                )
              : pw.Container(),
          for (int list = 0; list < groupList[i].length; list++)
            pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
              pw.Container(width: 40, child: pw.Text((list + 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 130,
                  child: pw.Text(groupList[i][list].name.toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
              pw.Container(
                  width: 80,
                  child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i][list].qty.toString()),
                      textAlign: pw.TextAlign.center, style: const pw.TextStyle(fontSize: 10))),
              pw.Container(
                  width: 90,
                  child: pw.Text(double.parse(groupList[i][list].price.toString()).toStringAsFixed(1),
                      textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
              pw.Container(
                  width: 95,
                  child: pw.Text(double.parse(groupList[i][list].amount.toString()).toStringAsFixed(1),
                      textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
            ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 70,
              child: pw.Text("Total :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 100,
              child: pw.Text(double.parse(sales.totalAmount.toString()).toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
        ]),
    ]);
  }

  pdfFullBuildFunction(String currentDate, GetReports getReports) async {
    LoadingAlertWidget.onLoading(context);
    ;
    await geTransactionsListByDateToDate();
    String fileName = widget.isDeleteReport ? "Deleted_Sales_Full_Report" : "Sales_Full_Report";
    await sharePDF.savePDFFormat(
      await pdfFullSalesBodyBuildFunction(getReports),
      fileName,
      currentDate,
    );
    LoadingAlertWidget.onStopping();
  }

  List<DropDown> reportTypeDropdownList = [];
  DropDown? filterdropDownModel;
  buildReportTypeDropDownList() async {
    reportTypeDropdownList.add(DropDown(id: "1", name: "Sales Reports"));
    reportTypeDropdownList.add(DropDown(id: "2", name: "Payment Reports"));
    filterdropDownModel = filterdropDownModel ?? reportTypeDropdownList.first;
  }

  pdfReportBuildFunction(String? currentDate) async {
    LoadingAlertWidget.onLoading(context);
    ;
    await geTransactionsListByDateToDate();
    String fileName = "Sales_Report";
    final pdf = pw.Document(pageMode: PdfPageMode.outlines);
    for (Sales sales in getReports?.reportList ?? []) {
      List<Transactions> transactionList = [];
      transactionList = await getTransactionDetailsBySaleId(sales.salesId);

      await sharePDF.buildSalesPDFFormat(pdf, sharePDF.pdfCommonSalesReportBodyBuildFunction(sales, transactionList), fileName, currentDate ?? "");
    }
    sharePDF.savePDF(pdf, fileName, currentDate);
    LoadingAlertWidget.onStopping();
  }
}
