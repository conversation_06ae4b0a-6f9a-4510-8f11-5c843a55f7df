// ignore_for_file: non_constant_identifier_names, sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/screens/reports/reports_page.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import '../../model/reports/get_expenses_report.dart';
import '../../model/reports/get_purchase_reports.dart';
import '../../utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_operations_utility.dart';
import '../../utils/dbutils/sales_details_db_script.dart';
import '../../utils/dbutils/sales_trans_db_script.dart';
import '../../utils/dbutils/salestype_db_script.dart';
import '../../utils/common_function/role_permissions_utilities.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import '../expenses/functions/stocks_functions.dart';
import 'reports_view_page.dart';

enum ReportFor { sale, payment, salescategory, customer }

class ReportListView extends StatefulWidget {
  int selectedIndex;
  List<Sales>? reportList = [];
  List<Expenses>? expenseList = [];
  List<PurchaseTransactionReport>? purchaseReportList = [];
  ReportFor reportFor;
  bool isDeleteReport;
  bool isShiftWiseReport;
  bool isSalesReport;

  ReportListView(this.selectedIndex, this.reportList,
      {this.reportFor = ReportFor.payment,
      this.expenseList,
      this.purchaseReportList,
      this.isDeleteReport = false,
      this.isShiftWiseReport = false,
      this.isSalesReport = false});
  @override
  _ReportListViewState createState() => _ReportListViewState(selectedIndex, expenseList, reportList, purchaseReportList, reportFor: reportFor);
}

class _ReportListViewState extends State<ReportListView> {
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  StocksFunction stocksFunction = StocksFunction();
  int selectedIndex;
  bool isLock = false;
  bool showInventory = false;
  List<Sales>? reportList = [];
  List<Expenses>? expenseList = [];
  List<PurchaseTransactionReport>? purchaseReportList = [];
  TextEditingController reasenController = TextEditingController();
  bool isreasenControllerIsEmpty = false;

  ReportFor? reportFor;
  //REPORT VIEW INDEX
  _ReportListViewState(this.selectedIndex, this.expenseList, this.reportList, this.purchaseReportList, {this.reportFor});

  //DELETE SALES LIKE INVISIBLE
  updateWithDelete(Sales sales) async {
    await salesDetailsDBScript.updateSalesDelete(sales.salesId, sales: sales);
    await salesTransDBScript.updateSalesTransactionDelete(sales.salesId);
    await salesTypeDB.updateSalesTypeForDeleteBySalesID(sales.salesId);
  }

  revertDeletedRecord(Sales sales) async {
    await salesDetailsDBScript.revertSalesDeleted(sales.salesId, sales: sales);

    await salesTransDBScript.updateSalesTransactionRevert(sales.salesId);
    await salesTypeDB.updateSalesTypeForRevertBySalesID(sales.salesId);
  }

  //MESSAGE FOR PERMISSION
  bool showMessage() {
    showToast("You don't have permission.");
    return true;
  }

  @override
  void initState() {
    initSettings();
    super.initState();
  }

  initSettings() async {
    if (this.mounted) {
      setState(() {
        showInventory = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    var width = MediaQuery.of(context).size.width;
    var height = MediaQuery.of(context).size.height;

    return Padding(
      padding: const EdgeInsets.all(1.0),
      child: Container(
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Card(
              child: Container(
                color: Colors.white10,
                width: width,
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: reportFor == ReportFor.customer ? width * 0.42 : width * 0.32,
                      alignment: Alignment.topLeft,
                      child: TextWidget("No / Date", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                    Container(
                      width: width * 0.15,
                      alignment: Alignment.topLeft,
                      child: TextWidget("ID", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                    if (reportFor == ReportFor.payment) ...[
                      Container(
                        alignment: Alignment.topLeft,
                        width: width * 0.25,
                        child: TextWidget(widget.isShiftWiseReport ? "Shift-wise" : "PaidBy",
                            textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                      ),
                    ] else if (reportFor == ReportFor.salescategory) ...[
                      Container(
                        alignment: Alignment.topLeft,
                        width: width * 0.25,
                        child: TextWidget("Category", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                      ),
                    ]
                    //  else if (reportFor == ReportFor.customer) ...[
                    //   Container(
                    //     alignment: Alignment.topLeft,
                    //     width: width * 0.25,
                    //     child: TextWidget("Customer Name", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    //   ),
                    // ]
                    else if (reportFor != ReportFor.customer) ...[
                      Container(
                        alignment: Alignment.topLeft,
                        width: width * 0.25,
                        child: TextWidget("Name", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                      ),
                    ],
                    Container(
                      alignment: Alignment.topRight,
                      width: width * 0.23,
                      child: TextWidget("Amt(₹Rs)", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                  ],
                ),
              ),
            ),
            salesReportListWidget(reportList ?? [], width),
          ],
        ),
      ),
    );
  }

  FocusNode reasenFocusNode = FocusNode();
  salesReportListWidget(List reportList, double width) {
    if (reportList != null && reportList.isNotEmpty) {
      return Align(
          alignment: Alignment.topCenter,
          child: ListView.builder(
              padding: EdgeInsets.only(right: 0),
              itemCount: reportList.isNotEmpty ? reportList.length : 0,
              shrinkWrap: true,
              physics: ScrollPhysics(),
              itemBuilder: (context, index) {
                //SALES DELETE ALERT DIALOG
                var item = reportList[index];
                // var dateFormat = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${item.date}"));
                var dateFormat = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${item.date}"));
                // Convert the string to a DateTime object
                DateTime dateTime = DateFormat("dd-MM-yyyy hh:mm a").parse(dateFormat);

                // Format the date
                String formattedDate = DateFormat("dd-MM-yyyy").format(dateTime);

                // Format the time
                String formattedTime = DateFormat("hh:mm a").format(dateTime);

                // print(dateFormat);
                return GestureDetector(
                  onLongPress: () async {
                    if (selectedIndex < 2) {
                      final bool res = PermissionFunctions.checkPermission(251);
                      if (res) {
                        reasenFocusNode.requestFocus();
                        if (!widget.isDeleteReport) {
                          showDialog(
                            context: context,
                            builder: (BuildContext context) {
                              return StatefulBuilder(
                                builder: (BuildContext context, StateSetter setState) {
                                  return ConfirmationAlertDialog(
                                    content: "Are you sure you want to delete the transaction ${item.invoiceId.toString()} ?",
                                    buttonNameOne: "No",
                                    isDeletedReport: true,
                                    reasenController: reasenController,
                                    focus: reasenFocusNode,
                                    isDeletedReasenIsEmpty: isreasenControllerIsEmpty,
                                    ontabButtonOne: () {
                                      if (!isLock) {
                                        isLock = true;
                                        Navigator.pop(context);
                                        if (isreasenControllerIsEmpty) {
                                          setState(() {
                                            isreasenControllerIsEmpty = false;
                                          });
                                        }
                                        isLock = false;
                                      }
                                    },
                                    buttonNameTwo: "Yes",
                                    ontabButtonTwo: () async {
                                      if (reasenController.text != "") {
                                        if (!isLock) {
                                          isLock = true;

                                          item.deletedBy = MyApp.activeUser.username.toString();
                                          item.deletedDate = DateTime.now().toString();
                                          item.deletedReason = reasenController.text;
                                          item.deletedUserName = (MyApp.activeUser.firstName ?? '') + (MyApp.activeUser.lastName ?? '');

                                          if (showInventory) {
                                            await stocksFunction.updateStockForDeleteSales(item.salesId);
                                          }
                                          // log(item);
                                          await updateWithDelete(item);
                                          if (isreasenControllerIsEmpty) {
                                            setState(() {
                                              isreasenControllerIsEmpty = false;
                                            });
                                          }
                                          Navigator.of(context).pop();
                                          Navigator.of(context).pop();
                                          Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsPage()));
                                          isLock = false;
                                        }
                                      } else {
                                        setState(() {
                                          isreasenControllerIsEmpty = true;
                                        });
                                      }
                                    },
                                  );
                                },
                              );
                            },
                          );
                        }
                      }
                      // }
                    }
                    ;
                  },
                  child: InkWell(
                    onTap: () {
                      if (PermissionFunctions.checkPermission(195)) {
                        if (!isLock) {
                          isLock = true;
                          Navigator.push(
                              context,
                              MaterialPageRoute(
                                  builder: (context) => ReportsViewPage(
                                        reports: item,
                                        selectedIndex: selectedIndex,
                                        isDeleteReport: widget.isDeleteReport,
                                        isShifWiseReport: widget.isShiftWiseReport,
                                        isSalesReport: widget.isSalesReport,
                                        isAdvanceOrderEnabled: item?.deliveryDateAndTime != null,
                                      )));
                          isLock = false;
                        }
                      }
                    },
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Container(
                          margin: EdgeInsets.only(top: 10, bottom: 10),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    width: reportFor == ReportFor.customer ? width * 0.48 : width * 0.32,
                                    alignment: Alignment.topLeft,
                                    child: fnUtilities.paymentInvoicewidget(context, fnUtilities.getPaymentInvoiceNo(selectedIndex, item)),
                                  ),
                                  // Container(
                                  //   width: width * 0.28,
                                  //   alignment: Alignment.topLeft,
                                  //   child: TextWidget(
                                  //     dateFormat,
                                  //     textColor: textSecondaryColor,
                                  //     fontSize: textSizeSmall,
                                  //     fontFamily: fontSemibold,
                                  //     maxLine: 2,
                                  //   ),
                                  // ),

                                  Padding(
                                    padding: const EdgeInsets.only(top: 5.0),
                                    child: Container(
                                      width: reportFor == ReportFor.customer ? width * 0.48 : width * 0.32,
                                      alignment: Alignment.topLeft,
                                      child: Text(formattedDate),
                                    ),
                                  ),
                                  Container(
                                      width: reportFor == ReportFor.customer ? width * 0.48 : width * 0.32,
                                      alignment: Alignment.topLeft,
                                      child: Text(formattedTime)),
                                ],
                              ),
                              Container(
                                width: reportFor != ReportFor.customer ? width * 0.15 : width * 0.25,
                                alignment: Alignment.topLeft,
                                child: TextWidget(item.invoiceId != null ? item.invoiceId.toString() : "-", fontSize: textSizeMedium),
                              ),
                              if (reportFor != ReportFor.customer) ...[
                                Container(
                                  width: width * 0.25,
                                  alignment: Alignment.topLeft,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (reportFor != ReportFor.salescategory && reportFor != ReportFor.customer && !widget.isShiftWiseReport) ...[
                                        item.customerName != null
                                            ? TextWidget(
                                                item.customerName,
                                                textColor: primaryTextColor,
                                                fontSize: textSizeSMedium,
                                                fontFamily: fontSemibold,
                                                overflow: TextOverflow.ellipsis,
                                              )
                                            : SizedBox.shrink(),
                                      ],
                                      if (reportFor == ReportFor.payment) ...[
                                        TextWidget(
                                          widget.isShiftWiseReport ? item.shiftName ?? 'None' : item.paymentTypeName ?? "",
                                          textColor: primaryTextColor,
                                          fontSize: textSizeSMedium,
                                          fontFamily: fontSemibold,
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      ],
                                      if (reportFor == ReportFor.salescategory) ...[
                                        TextWidget(
                                          item.salesCatName ?? "",
                                          textColor: primaryTextColor,
                                          fontSize: textSizeSMedium,
                                          fontFamily: fontSemibold,
                                          overflow: TextOverflow.ellipsis,
                                        )
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                              Container(
                                width: width * 0.23,
                                alignment: Alignment.topRight,
                                child: TextWidget(
                                    item.totalAmount != "null"
                                        ? fnUtilities.checkQtyDoubleValue(double.parse(item.totalAmount ?? 0.0).round().toString())
                                        : "0.0",
                                    textColor: primaryTextColor,
                                    fontSize: textSizeSMedium,
                                    fontFamily: fontSemibold),
                              )
                            ],
                          ),
                        ),
                        Divider(height: 0.5, color: t5ViewColor)
                      ],
                    ),
                  ),
                );
              }));
    } else {
      return Center(
        child: Container(child: text("No Sales Found", isCentered: true, textColor: primaryTextColor, fontSize: textSizeNormal)),
      );
    }
  }
}
