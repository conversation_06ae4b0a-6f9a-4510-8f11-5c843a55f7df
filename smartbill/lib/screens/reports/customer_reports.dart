import 'package:collection/collection.dart';
// ignore: implementation_imports
import 'package:flutter/material.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/reports/get_payment_reports.dart';
import 'package:smartbill/screens/reports/reports_list_view.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/dbutils/customers_db_script.dart';
import '../../Repositories/download_reports_repository.dart';
import '../../Repositories/reports_repository.dart';
import '../../main.dart';
import '../../model/reports.dart';
import '../../model/reports/download_reports_request.dart';
import '../../utils/common_widgets/date_range_picker_widget.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/reports_db_script.dart';
import '../../utils/error_logs/error_logs_function.dart';

class CustomerReportPage extends StatefulWidget {
  CustomerReportPage({Key? key});

  @override
  CustomerReportPageState createState() => CustomerReportPageState();
}

class CustomerReportPageState extends State<CustomerReportPage> with SingleTickerProviderStateMixin {
  FnUtilities fnUtilities = new FnUtilities();
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  CustomersDBScript customerDB = CustomersDBScript();

  List<DropdownMenuItem<Customer>>? dropdownMenuCustomer;

  List<Sales> customerReportList = [];

  List<Customer> customerList = [];
  PaymentReports? customerReport = PaymentReports();
  TextEditingController textController = TextEditingController();
  TextEditingController searchController = TextEditingController();

  Customer? selectedCustomer;
  DateTime fromDate = DateTime.now();
  DateTime toDate = DateTime.now();

  bool isLoading = false;
  bool initPageLoading = false;
  double width = 0.0;
  double height = 0.0;
  double totalAmount = 0.0;
  int totalSales = 0;
  double totalQty = 0;
  var groupList;

  bool isBigLayout = false;
  bool isLock = false;
  DownloadReportsRequest downloadReportsRequest = DownloadReportsRequest();
  bool isTwoDaysRecord = false;
  TabController? controller;
  int selectIndex = 0;

  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    // if (picked != null && picked != fromDate && dateType == 'fromDate')
    //   setState(() {
    //     fromDate = picked.subtract(Duration(
    //         hours: picked.hour, minutes: picked.minute, seconds: picked.second, milliseconds: picked.millisecond, microseconds: picked.microsecond));
    //   });
    // if (picked != null && picked != toDate && dateType == 'toDate')
    //   setState(() {
    //     toDate = picked;
    //   });
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked.subtract(Duration(
                hours: picked.hour,
                minutes: picked.minute,
                seconds: picked.second,
                milliseconds: picked.millisecond,
                microseconds: picked.microsecond));
          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
          });
        }
      }
    }
  }

  getCustomerReport(fromDate, toDate, String custId) async {
    setState(() {
      isLoading = true;
    });

    try {
      await getCustomerReportByDatToDate(fromDate, toDate, customerId: custId).then((value) {
        if (value.status == 1) {
          customerReportList = value.result?.customerSales ?? [];
          totalSales = value.result?.totalSales ?? 0;
          totalAmount = value.result?.totalAmount ?? 0;
          groupList = customerReportList.groupListsBy((element) => element.customerID);
          // shift
          customerReport = value.result;
        } else {
          showToast(value.message);
        }
        setState(() {
          isLoading = false;
        });
      });
    } catch (e) {
      log(e.toString());
      ErrorLogsFunction.insertErrorLogs(e, "getCustomerReport - CustomerReportPage ");
      setState(() {
        isLoading = false;
      });
    }
  }

  getCustomerReportFromLocale(fromDate, toDate, String customerId) async {
    setState(() {
      isLoading = true;
    });
    try {
      var now = new DateTime.now();
      fromDate = now.subtract(
          Duration(days: 2, hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      toDate = now.subtract(
          Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
      var result = await ReportDetailsDB.getCommonReports(fromDate, toDate, keyName: db_sales_fK_CustomerID, keyValue: customerId);
      if (result != null) {
        customerReport = PaymentReports.fromJson(result);
        customerReportList = customerReport?.reportList ?? [];
        totalSales = customerReport?.totalSales ?? 0;
        totalAmount = customerReport?.totalAmount ?? 0;
        // for (Sales model in customerReportList) {
        //   if (model.salesId != null) {
        //     var customerModel = customerList.firstWhereOrNull((element) => element.firstName == model.customerName);
        //     model.customerID = customerModel != null ? customerModel.customerID : "None";
        //   }
        // }
        groupList = customerReportList.groupListsBy((element) => element.customerID == null ? "None" : element.customerID);
      }
      setState(() {
        fromDate = DateTime.now();
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        isLoading = false;
      });
      ErrorLogsFunction.insertErrorLogs(e, "getCustomerReportFromLocale - CustomerReportsPage ");
    }
  }

  /// -- Customer dropdown

  queryAllCustomerDetails() async {
    final allRows = await customerDB.selectAllCustomers();
    setState(() {
      customerList.clear();
      allRows.forEach((row) => customerList.add(Customer.fromMap(row)));
    });
    if (customerList.length != 0) {
      setState(() {
        dropdownMenuCustomer = buildDropdownMenuCustomerItems(customerList);
        //selectedCustomer = dropdownMenuCustomer?[0].value;
      });
    }
  }

  // shift
  List<DropdownMenuItem<Customer>> buildDropdownMenuCustomerItems(List customerList) {
    List<DropdownMenuItem<Customer>> items = [];
    for (Customer model in customerList) {
      items.add(
        DropdownMenuItem(value: model, child: Text(model.firstName ?? '')),
      );
    }
    return items;
  }

  //ONCHANGE FUNCTION FOR LIST
  onChangeDropdownCustomerItem(Customer? customer) {
    setState(() {
      selectedCustomer = customer;
    });
    // }
  }

  /// customer dropdown end
  @override
  void initState() {
    queryAllCustomerDetails();
    initPageDetails();
    super.initState();
  }

  initPageDetails() async {
    setState(() {
      initPageLoading = true;
    });

    await initFunction(selectIndex);

    controller = TabController(length: 2, vsync: this);
    controller?.addListener(() {
      setState(() {
        if (controller?.index == 0) {
          selectIndex = 0;
        }
        if (controller?.index == 1) {
          selectIndex = 1;
          fromDate = DateTime.now();
          toDate = DateTime.now();
          customerReportList = [];
          customerReport = new PaymentReports();
          totalSales = 0;
          totalAmount = 0;
        }
        initFunction(selectIndex);
      });
    });

    setState(() {
      initPageLoading = false;
    });
  }

  initFunction(int index) async {
    if (index == 0) {
      await getCustomerReportFromLocale(fromDate, toDate, '');
      isTwoDaysRecord = true;
    } else {
      isTwoDaysRecord = false;
    }
  }

  downloadReportFunction(DownloadReportsRequest requestModel, String downloadUrl, String fileName) async {
    try {
      LoadingAlertWidget.onLoading(context);
      ;
      await downloadReportsAPI(requestModel, downloadUrl, fileName, context);
      LoadingAlertWidget.onStopping();
    } on Exception catch (e) {
      LoadingAlertWidget.onStopping();
      toast("Failed to download reports");
      ErrorLogsFunction.insertErrorLogs(e, "downloadReportFunction - CustomerReportPage");
    }
  }

  iconTopWidget() {
    return Row(
      children: [
        PopupMenuButton(
          icon: Icon(Icons.more_vert),
          itemBuilder: (BuildContext context) {
            return <PopupMenuEntry>[
              PopupMenuItem(
                child: ListTile(
                  leading: Icon(Icons.download),
                  title: Text("PDF Report For Sale"),
                  onTap: () async {
                    // if (PermissionFunctions.checkPermission(274)) {
                    Navigator.pop(context);
                    downloadReportsRequest = DownloadReportsRequest();
                    downloadReportsRequest.fromDate = fromDate.toString();
                    downloadReportsRequest.toDate = toDate.toString();
                    downloadReportsRequest.customerID = selectedCustomer?.customerID;
                    downloadReportsRequest.workspaceID = MyApp.activeWorkspace.workspaceId;
                    await downloadReportFunction(downloadReportsRequest, "/api/v1/sales/getcustomersalespdfreport", "Customer Report");
                    // }
                    print(MyApp.activeUser.token);
                  },
                ),
              ),
            ];
          },
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    var isPortrait = MediaQuery.of(context).orientation;
    isBigLayout = width >= tabletWidth ? true : false;

    return DefaultTabController(
      length: 2,
      child: Scaffold(
        backgroundColor: secondaryTextColor,
        appBar: AppBar(
          backgroundColor: appThemeColor,
          iconTheme: IconThemeData(
            color: secondaryTextColor,
          ),
          title: text("Customer Reports", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
          bottom: TabBar(
            onTap: (index) {},
            controller: controller,
            isScrollable: true,
            labelStyle: primaryTextStyle(),
            indicatorColor: redColor,
            physics: BouncingScrollPhysics(),
            labelColor: secondaryTextColor,
            tabs: [
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.insert_drive_file,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      '2 Days',
                    ),
                  ],
                ),
              ),
              Tab(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      Icons.supervised_user_circle,
                      color: secondaryTextColor,
                    ),
                    5.width,
                    Text(
                      'Date to Date',
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[(customerReportList.isNotEmpty && selectIndex == 1) ? iconTopWidget() : Container()],
        ),
        body: TabBarView(
          controller: controller,
          children: [
            isLoading
                ? Center(child: CircularProgressIndicator())
                : customerReportList.isNotEmpty && customerReportList.length != 0 && isTwoDaysRecord
                    ? twoDaysRecordTab()
                    : SizedBox(
                        height: 200,
                        child: Align(
                            alignment: Alignment.bottomCenter,
                            child: Container(
                              child: Text(
                                "Reports not found",
                                style: black16w500,
                              ),
                            )),
                      ),
            Container(
              padding: EdgeInsets.all(2),
              child: initPageLoading
                  ? Center(child: CircularProgressIndicator())
                  : customerList.length > 0
                      ? DateToDateRecordTab()
                      : Center(
                          child: Container(
                            padding: EdgeInsets.all(15.0),
                            child: TextWidget("No Sales Available",
                                textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium, isCentered: true),
                          ),
                        ),
            ),
          ],
        ),
        bottomSheet: initPageLoading
            ? SizedBox()
            : Container(
                height: isPortrait == Orientation.portrait ? height * 0.12 : height * 0.2,
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("Grand Total: ",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                        Padding(
                          padding: EdgeInsets.only(top: 5.0),
                          child: TextWidget("₹${totalAmount.toStringAsFixed(2)}",
                              textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeNormal, isCentered: true),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget paymentBasedFilterList(List<Sales>? item) {
    var groupListByCustomerName = item != null ? item.groupListsBy((element) => element.customerID == null ? "None" : element.customerID) : null;

    Map<String?, double> totalAmountByKeyword = {};
    int count = 0;
    item?.forEach((element) {
      final key = element.customerID ?? "None";
      if (totalAmountByKeyword.containsKey(key)) {
        totalAmountByKeyword[key] = (totalAmountByKeyword[key] ?? 0) + double.parse(element.totalAmount.toString());
      } else {
        totalAmountByKeyword[key] = double.parse(element.totalAmount.toString());
      }
      count++;
    });

    return Padding(
      padding: EdgeInsets.only(bottom: 0),
      child: Column(
        children: [
          for (var i in groupListByCustomerName!.keys) ...[
            Padding(
              padding: const EdgeInsets.only(bottom: 0),
              child: ReportListView(
                isTwoDaysRecord == true ? 0 : 2,
                item,
                reportFor: ReportFor.customer,
              ),
            ),
            Padding(
              padding: EdgeInsets.only(bottom: 0),
              child: Container(
                color: Colors.black12,
                padding: EdgeInsets.only(left: 5.0, right: 5.0),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              "Total",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            "₹ ${double.tryParse(totalAmountByKeyword[i].toString())?.toStringAsFixed(2)} ",
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(vertical: 8),
                          child: Container(
                            alignment: Alignment.centerLeft,
                            child: Text(
                              isTwoDaysRecord
                                  ? "${groupListByCustomerName[i]?[0].customerID != null ? groupListByCustomerName[i]![0].customerName : "None"}"
                                  : "${groupListByCustomerName[i] != null ? groupListByCustomerName[i]![0].firstName ?? "None" : ""}",
                              style: TextStyle(
                                fontSize: textSizeMedium,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                        Container(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            count.toString(),
                            style: TextStyle(
                              fontSize: textSizeMedium,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget twoDaysRecordTab() {
    return Container(
        width: width,
        alignment: Alignment.topCenter,
        child: SingleChildScrollView(
          child: Column(
            children: [
              if (customerReportList.isNotEmpty) ...{
                for (var i in groupList.keys) ...{
                  if (groupList[i].length != 0) ...{
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                      child: Column(
                        children: <Widget>[
                          // if (groupList[i][0].customerName != null) ...{
                          Container(
                            color: Colors.black12,
                            padding: EdgeInsets.only(left: 5.0, right: 5.0),
                            child: Row(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.symmetric(vertical: 8),
                                  child: Container(
                                    alignment: Alignment.centerLeft,
                                    child: Text(
                                      groupList[i][0].customerID == null ? "None" : groupList[i][0].customerName,
                                      style: TextStyle(
                                        fontSize: textSizeMedium,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          // } else ...{
                          //   SizedBox()
                          // },
                          paymentBasedFilterList(groupList[i]),
                        ],
                      ),
                    )
                  } else ...{
                    SizedBox(
                      height: 200,
                      child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            child: Text(
                              "Reports not found",
                              style: black16w500,
                            ),
                          )),
                    ),
                  },
                },
                SizedBox(
                  height: 100,
                )
              } else ...{
                SizedBox(
                  height: 200,
                  child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        child: Text(
                          "Reports not found",
                          style: black16w500,
                        ),
                      )),
                ),
              }
            ],
          ),
        ));
  }

  Widget DateToDateRecordTab() {
    return SingleChildScrollView(
      child: Column(
        children: <Widget>[
          SizedBox(height: 20),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: Container(
              height: 50,
              child: TextField(
                controller: searchController,
                keyboardType: TextInputType.none,
                onTap: () {
                  showCustomerSearchDialog(context);
                  FocusScope.of(context).unfocus();
                },
                decoration: InputDecoration(
                  // labelText: 'Search',
                  hintText: 'Search...',
                  suffixIcon: searchController.text == '' ? Icon(Icons.search) : SizedBox.shrink(),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.all(Radius.circular(40.0)),
                  ),
                ),
              ),
            ),
          ),
          SizedBox(height: 10),
          // isReportView
          //     ?
          BootstrapRow(children: [
            BootstrapCol(
                sizes: 'col-md-8 col-sm-12',
                child: Padding(
                    padding: const EdgeInsets.only(top: 10),
                    child: DateRangePickerWidget(
                      isBigLayout: isBigLayout,
                      fromDate: fromDate,
                      toDate: toDate,
                      onPressFromDate: (value) {
                        if (value != null) {
                          fromDate = value;
                        }
                      },
                      onPressToDate: (value) {
                        if (value != null) {
                          toDate = value;
                        }
                      },
                    ))),
            BootstrapCol(
                sizes: 'col-md-4 col-sm-12',
                child: Padding(
                  padding: const EdgeInsets.only(top: 10.0),
                  child: Row(
                    mainAxisAlignment: isBigLayout ? MainAxisAlignment.end : MainAxisAlignment.center,
                    children: [
                      InkWell(
                        onTap: () async {
                          // if (PermissionFunctions.checkPermission(272)) {
                          setState(() {
                            getCustomerReport(fromDate, toDate, selectedCustomer?.customerID ?? "");
                            // FocusScope.of(context).unfocus();
                          });
                          // }
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 40,
                          width: isBigLayout ? width * 0.15 : width * 0.98,
                          child: Text("Submit",
                              style: TextStyle(
                                color: secondaryTextColor,
                                fontSize: 14,
                                fontWeight: FontWeight.w700,
                              )),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5),
                            color: buttonThemeColor,
                          ),
                        ),
                      ),
                    ],
                  ),
                )),
          ]),
          // : Container(),
          isLoading
              ? Container(width: 50, height: 50, child: CircularProgressIndicator())
              : customerReportList.length != 0
                  ? Container(
                      width: width,
                      alignment: Alignment.topCenter,
                      child: Column(
                        children: [
                          if (customerReportList.isNotEmpty) ...{
                            for (var i in groupList.keys) ...{
                              if (groupList[i].length != 0) ...{
                                Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                                  child: Column(
                                    children: <Widget>[
                                      //if (groupList[i][0].paymentTypeName != null) ...{
                                      Container(
                                        color: Colors.black12,
                                        padding: EdgeInsets.only(left: 5.0, right: 5.0),
                                        child: Row(
                                          children: [
                                            Padding(
                                              padding: const EdgeInsets.symmetric(vertical: 8),
                                              child: Container(
                                                alignment: Alignment.centerLeft,
                                                child: Text(
                                                  groupList[i][0].firstName ?? "None",
                                                  style: TextStyle(
                                                    fontSize: textSizeMedium,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // } else ...{
                                      //   SizedBox()
                                      // },
                                      paymentBasedFilterList(groupList[i]),
                                    ],
                                  ),
                                )
                              } else ...{
                                SizedBox()
                              },
                            },
                            SizedBox(
                              height: 100,
                            )
                          }
                        ],
                      ))
                  : SizedBox(
                      height: 200,
                      child: Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            child: Text(
                              "Reports not found",
                              style: black16w500,
                            ),
                          )),
                    ),
        ],
      ),
    );
  }

  AlertDialog? showCustomerSearchDialog(BuildContext context) {
    List<Customer> filteredCustomers = List.from(customerList);

    showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Text('Select Customer'),
              content: FractionallySizedBox(
                heightFactor: 0.7,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 50,
                      child: TextField(
                        decoration: InputDecoration(
                          hintText: 'Search customers...',
                          contentPadding: EdgeInsets.all(10),
                          suffixIcon: Icon(Icons.search),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.all(Radius.circular(40.0)),
                          ),
                        ),
                        onChanged: (value) {
                          setState(() {
                            filteredCustomers =
                                customerList.where((customer) => customer.firstName!.toLowerCase().contains(value.toLowerCase())).toList();
                          });
                        },
                      ),
                    ),
                    SizedBox(height: 10),
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          children: filteredCustomers.map((Customer customer) {
                            return InkWell(
                              onTap: () {
                                selectedCustomer = customer;
                                searchController.text = selectedCustomer?.firstName ?? "";
                                Navigator.of(context).pop();
                              },
                              child: Container(
                                height: 40,
                                width: width * 0.6,
                                child: Text(customer.firstName ?? ''),
                              ),
                            );
                          }).toList(),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
    return null;
  }
}
