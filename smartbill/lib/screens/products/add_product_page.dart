// ignore_for_file: sdk_version_ui_as_code, unused_local_variable, non_constant_identifier_names

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bootstrap/flutter_bootstrap.dart';
import 'package:flutter_svg/svg.dart';
import 'package:image_cropper/image_cropper.dart';
import 'package:image_picker/image_picker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/screens/products/view_product_category.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_widgets/text_field_box_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:uuid/uuid.dart';
import '../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/dbutils/price_type_db_script.dart';
import '../../utils/dbutils/product_details_db_script.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/dbutils/toppings_groups_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import 'toppings_list.dart';

enum ProductFor { purchase, sale, both }

//ADD PRODUCT DIALOG CLASS
// ignore: must_be_immutable
class AddProductDialog extends StatefulWidget {
  String? id;
  String? productName;
  String? screenFrom;
  AddProductDialog({this.id, this.productName,this.screenFrom});
  @override
  _AddProductDialogState createState() => _AddProductDialogState(id, productName,screenFrom);
}

class _AddProductDialogState extends State<AddProductDialog> {
  String? id;
  String? productName;
  String? screenFrom;
  _AddProductDialogState(this.id, this.productName,this.screenFrom);

  final productSaleIdController = TextEditingController();
  final productController = TextEditingController();
  final priceController = TextEditingController();
  final mrpController = TextEditingController();
  final gstController = TextEditingController();
  final unitController = TextEditingController();
  final unitPriceController = TextEditingController();
  // final stocksController = TextEditingController();
  bool isMeasurementIsEmpty = false;
  Validator validator = new Validator();
  FocusNode measure = FocusNode();
  FocusNode product = new FocusNode();
  bool taxupdate = false;
  bool measurementUpdate = false;
  bool dynamicQuantity = false;
  bool isUpdate = false;
  bool isLock = false;
  ProductKey? selectKeyword;
  ToppingsGroup? selectGroup;
  String imageId = '';
  int? unitIndex;
  List<ProductKey> productKeyList = [];
  List<ToppingsGroup> toppingsGroupList = [];
  List<PriceUnitType> productUnitList = [];
  var width;
  var height = 100.0;
  bool showDynamicQty = false;
  bool showMeasurement = false;
  bool showToppings = false;
  bool showProductFor = false;
  FnUtilities fnUtilities = new FnUtilities();
  DBOperations dbUtils = new DBOperations();
  bool showTax = false;
  // bool showInventory = false;
  // String inventoryType;
  // bool enableInventory = false;
  bool showParcel = false;
  bool parcelupdate = false;
  final parcelController = TextEditingController();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
  ToppingGroupsDBScript toppingsGroupsDBscript = ToppingGroupsDBScript();
  SizedBox sizeHeight = SizedBox(height: 10);
  ProductFor? selectedProductFor;
  // StocksFunction stocksFunction = StocksFunction();

  //INSERT PRODUCT DETAILS
  insert(String prodId, String name, String price, String imageId, String sgst, String cgst, String mrp, String key, String groupId, int prodSaleId,
      int priceType, int dynamicQty, String keyID, String parcelAmount, ProductFor selectedProductFor) async {
    Products products = Products(
        productId: prodId,
        productSaleId: prodSaleId,
        name: name,
        price: price,
        mrp: mrp,
        productKeyName: key,
        toppingGroupId: groupId,
        priceType: priceType,
        dynamicQuantity: dynamicQty,
        // enableInventory: enableInventory ? 1 : 0,
        cGst: cgst,
        sGst: sgst,
        image: imageId,
        workspaceId: MyApp.activeWorkspace.workspaceId,
        sync: 0,
        rowStatus: 0,
        productDate: DateTime.now().toString(),
        keyID: keyID,
        parcelAmount: parcelAmount,
        productFor: selectedProductFor);
    await productDetailsDBScript.insertProductDetails(products);
    showToast("Product created successfully");
    // if (stocksController.text != "" && enableInventory) {
    //   ModifyStocksDto model = new ModifyStocksDto();
    //   model.productId = prodId;
    //   model.stocksQty = stocksController.text;
    //   model.stocksSold = "0";
    //   model.modifiedStocks = stocksController.text;
    //   await stocksFunction.modifyStocks(model);
    // }
  }

  //Get product unit details from static unit list
  getAllProductUnitsDetails(String productId) async {
    if (ListUtility.unitList.isNotEmpty) {
      productUnitList.clear();
      for (PriceUnitType unitType in ListUtility.unitList) {
        if (unitType.productId == productId) {
          setState(() {
            productUnitList.add(unitType);
          });
        }
      }
    }
  }

  //CLEAR PRODUCT TEXT FILED FUNCTION
  clearTextField() {
    Future.delayed(Duration(milliseconds: 50), () {
      productController.clear();
      priceController.clear();
      gstController.clear();
      parcelController.clear();
      mrpController.clear();
      // stocksController.clear();
      unitController.clear();
      unitPriceController.clear();
    });
  }

  //SET PRODUCT TEXT FILED VALUE
  setTextFieldValues() async {
    await getAllProductKey();
    await getAllToppingGroup();
    for (Products p in ListUtility.productList) {
      if (p.productId == id) {
        await getAllProductUnitsDetails(p.productId ?? "");
        setState(() {
          productSaleIdController.text = p.productSaleId.toString();
          productController.text = p.name ?? "";
          priceController.text = p.price ?? "";
          if (p.productKeyName.isEmptyOrNull && p.keyID.isEmptyOrNull) {
            selectKeyword = productKeyList.first;
          } else {
            ProductKey? selectKey;
            if (!p.productKeyName.isEmptyOrNull && p.keyID == null) {
              selectKey = productKeyList.singleWhere((item) => item.keywordName == p.productKeyName);
            } else if (!p.keyID.isEmptyOrNull) {
              selectKey = productKeyList.singleWhere((item) => item.productKeyId == p.keyID);
            }

            if (selectKey == null) {
              selectKeyword = productKeyList.first;
            } else {
              selectKeyword = selectKey;
            }
          }
          if (p.toppingGroupId == null) {
            selectGroup = toppingsGroupList.first;
          } else {
            ToppingsGroup group;
            group = toppingsGroupList.singleWhere((item) => item.toppingGroupId == p.toppingGroupId, orElse: () => null ?? ToppingsGroup());
            if (group.toppingGroupId == null) {
              selectGroup = toppingsGroupList.first;
            } else {
              selectGroup = toppingsGroupList.singleWhere((item) => item.toppingGroupId == p.toppingGroupId);
            }
          }
          if (p.priceType == 2) {
            if (productUnitList.length != 0) {
              measurementUpdate = true;
            }
          }
          if (p.dynamicQuantity == 1) {
            dynamicQuantity = true;
          }

          if (showTax) {
            if (p.sGst != null || p.cGst != null) {
              taxupdate = true;
              gstController.text = (double.parse(p.sGst ?? "") + double.parse(p.cGst ?? "")).toString();
            }
          }
          mrpController.text = p.mrp ?? "";
          parcelController.text = p.parcelAmount ?? "";
          // if (p.enableInventory == 1) {
          //   enableInventory = true;
          // } else {
          //   enableInventory = false;
          // }
        });
        if (!p.image.isEmptyOrNull) {
          imageId = p.image ?? "";
          var filepath = await fnUtilities.readFileFromLocal(imageId);
          setState(() {
            imageFile = filepath;
          });
        }
        if (p.productFor != null) {
          setState(() {
            selectedProductFor = p.productFor ?? ProductFor.sale;
          });
        }
      }
    }
  }

  //UPDATE PRODUCT DETAILS
  update(String id, String name, String price, String imageId, String sgst, String cgst, String mrp, String key, String groupId, int prodSaleId,
      int priceType, int dynamicQty, String keyID, String parcelAmount, ProductFor selectedProductFor) async {
    final syncNo = await productDetailsDBScript.queryGetProductSyncNo(id);
    Products products = Products(
        productId: id,
        productSaleId: prodSaleId,
        name: name,
        price: price,
        mrp: mrp,
        productKeyName: key,
        toppingGroupId: groupId,
        priceType: priceType,
        dynamicQuantity: dynamicQty,
        // enableInventory: enableInventory ? 1 : 0,
        cGst: cgst,
        sGst: sgst,
        image: imageId,
        sync: 0,
        rowStatus: syncNo == 0 ? 0 : 1,
        productDate: DateTime.now().toString(),
        keyID: keyID,
        parcelAmount: parcelAmount,
        productFor: selectedProductFor);
    await productDetailsDBScript.updateProductsTable(products);
    showToast("Product updated successfully");
  }

  //SET PRODUCT SALES ID
  setProductSaleId() async {
    int id = await productDetailsDBScript.queryGetMaxProductSaleId();
    setState(() {
      productSaleIdController.text = (id + 1).toString();
    });
  }

  //GET PRODUCT DETAILS BY Key
  getAllProductKey() async {
    setState(() {
      productKeyList.clear();
      ProductKey model = ProductKey();
      model.keywordName = "None";
      productKeyList.add(model);
      productKeyList.addAll(ListUtility.productKeyList);
      if (productKeyList.length != 0 && id == null) {
        selectKeyword = productKeyList.first;
      }
    });
  }

  //GET PRODUCT DETAILS BY Key
  getAllToppingGroup() async {
    setState(() {
      toppingsGroupList.clear();
      ToppingsGroup model = ToppingsGroup();
      model.toppingGroupName = "None";
      toppingsGroupList.add(model);
      toppingsGroupList.addAll(ListUtility.toppingGroupList);
      if (toppingsGroupList.length != 0 && id == null) {
        selectGroup = toppingsGroupList.first;
      }
    });
  }

//GET PRODUCT SETTINGS FROM DB
  initSettings() async {
    setState(() {
      showMeasurement = fnUtilities.workSpaceSetValues(key_enableMeasurement) == "1";
      showDynamicQty = fnUtilities.workSpaceSetValues(key_enableDynamicQty) == "1";
      showToppings = fnUtilities.workSpaceSetValues(key_enableToppings) == "1";
      showProductFor = fnUtilities.workSpaceSetValues(key_purchase) == "1";
      var TaxEnableValue = fnUtilities.workSpaceSetValues(key_taxEnable);
      var ApplyTaxForValue = fnUtilities.workSpaceSetValues(key_applyTaxFor);
      if ((TaxEnableValue == "1") && (ApplyTaxForValue == null || ApplyTaxForValue == 'Specific')) {
        showTax = true;
      } else {
        showTax = false;
      }
      // showInventory = fnUtilities.workSpaceSetValues(key_enableInventory) == "1";
      // inventoryType = fnUtilities.workSpaceSetValues(key_inventoryType);
      // if (inventoryType == "All") {
      //   inventoryType = "All";
      //   enableInventory = true;
      // } else {
      //   inventoryType = "Specific";
      // }
      var ShowParcel = fnUtilities.workSpaceSetValues(key_parcelEnable);
      var applyParcelFor = fnUtilities.workSpaceSetValues(key_applyParcelFor);
      if ((ShowParcel == "1") && (applyParcelFor == null || applyParcelFor == 'Specific')) {
        showParcel = true;
      } else {
        showParcel = false;
      }
    });
  }

  setProductType(ProductFor? value) {
    if (isUpdate) {
      setState(() {
        selectedProductFor = value ?? ProductFor.sale;
      });
    }
  }

  initPageDetails() async {
    await initSettings();
    await getAllProductKey();
    await getAllToppingGroup();
    if (id != null) {
      setTextFieldValues();
    } else {
      if (productName != null) {
        productController.text = productName ?? "";
      }
      if(screenFrom == "Purchase"){
        selectedProductFor = ProductFor.purchase;
      } else {
         selectedProductFor = ProductFor.sale;
      }
      isUpdate = true;
      setProductSaleId();
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  onSaveProduct(String press) async {
    if (!isLock) {
      isLock = true;
      var uuid = new Uuid();
      String prodId = uuid.v4().toString();
      String name = productController.text;
      String price = priceController.text;
      String mrp = mrpController.text;
      int dynamicQty = 0;
      if (dynamicQuantity == true) {
        dynamicQty = 1;
      }
      String sgst;
      String cgst;
      if (gstController.text != '') {
        sgst = (double.parse(gstController.text) / 2).toString();
        cgst = (double.parse(gstController.text) / 2).toString();
      } else {
        sgst = "";
        cgst = "";
      }
      int prodSaleId = int.parse(productSaleIdController.text);
      String key;
      String keyID;
      if (selectKeyword?.keywordName != "None") {
        key = selectKeyword?.keywordName ?? "";
        keyID = selectKeyword?.productKeyId ?? "";
      } else {
        key = "";
        keyID = "";
      }
      String groupId;
      if (selectGroup?.toppingGroupName != "None") {
        groupId = selectGroup?.toppingGroupId ?? "";
      } else {
        groupId = "";
      }
      int? priceType;
      if (priceController.text == '' && !measurementUpdate) {
        priceType = 0;
        if (productUnitList.length > 0) {
          for (PriceUnitType obj in productUnitList) {
            if (obj.priceTypeId != null) {
              PriceUnitType model = PriceUnitType();
              model.priceTypeId = obj.priceTypeId;
              model.measurement = obj.measurement;
              model.measurementPrice = obj.measurementPrice;
              model.workspaceId = MyApp.activeWorkspace.workspaceId;
              model.rowStatus = 2;
              model.measurementSync = 0;
              await priceTypeDBScript.updateUnitTable(model);
            }
          }
        }
      } else if (priceController.text != '' && !measurementUpdate) {
        priceType = 1;
        if (productUnitList.length > 0) {
          for (PriceUnitType obj in productUnitList) {
            if (obj.priceTypeId != null) {
              PriceUnitType model = PriceUnitType();
              model.priceTypeId = obj.priceTypeId;
              model.measurement = obj.measurement;
              model.measurementPrice = obj.measurementPrice;
              model.workspaceId = MyApp.activeWorkspace.workspaceId;
              model.rowStatus = 2;
              model.measurementSync = 0;
              await priceTypeDBScript.updateUnitTable(model);
            }
          }
        }
      } else if (productUnitList.length > 0 && measurementUpdate) {
        priceType = 2;
        price = "";
        mrp = "";
      }
      if (priceType == 2) {
        PriceUnitType model = PriceUnitType();
        for (PriceUnitType obj in productUnitList) {
          if (obj.priceTypeId != null) {
            model.priceTypeId = obj.priceTypeId;
            model.measurement = obj.measurement;
            model.measurementPrice = obj.measurementPrice;
            model.workspaceId = MyApp.activeWorkspace.workspaceId;
            model.rowStatus = obj.rowStatus;
            model.measurementSync = 0;
            await priceTypeDBScript.updateUnitTable(model);
          } else {
            if (id == null) {
              model.productId = prodId;
            } else {
              model.productId = id;
            }
            model.measurement = obj.measurement;
            model.measurementPrice = obj.measurementPrice;
            model.workspaceId = MyApp.activeWorkspace.workspaceId;
            String priceUnitId = uuid.v4().toString();
            model.priceTypeId = priceUnitId;
            model.measurementSync = 0;
            model.rowStatus = obj.rowStatus;
            await priceTypeDBScript.insertUnits(model);
          }
        }
      }
      imageId = (await saveImage()) ?? "";
      // if(!showProductFor){
      //    selectedProductFor = ProductFor.sale;
      // }
      if (id == null) {
        await insert(prodId, name, price, imageId, sgst, cgst, mrp, key, groupId, prodSaleId, priceType ?? 0, dynamicQty, keyID,
            parcelController.text, selectedProductFor ?? ProductFor.sale);
      } else {
        await update(id ?? "", name, price, imageId, sgst, cgst, mrp, key, groupId, prodSaleId, priceType ?? 0, dynamicQty, keyID,
            parcelController.text, selectedProductFor ?? ProductFor.sale);
      }
      setState(() {
        clearTextField();
        imageFile = null;
        imageId = "";
      });
      productUnitList.clear();
      await setProductSaleId();
      FocusScope.of(context).requestFocus(product);
      await fnUtilities.getAllUnits();
      if (press == "add") {
        Navigator.pop(context, true);
      }
      isLock = false;
    }
  }

  onSubmitUnitMeasurement() async {
    if (unitController.text != '') {
      PriceUnitType model = PriceUnitType();
      model.measurement = unitController.text;
      model.measurementPrice = unitPriceController.text != "" ? unitPriceController.text : null;
      model.rowStatus = 0;
      setState(() {
        if (productUnitList.length != 0) {
          if (unitIndex == null) {
            productUnitList.add(model);
          } else {
            if (productUnitList[unitIndex ?? 0].priceTypeId != null) {
              productUnitList[unitIndex ?? 0].rowStatus = 1;
            }
            productUnitList[unitIndex ?? 0].measurement = model.measurement;
            productUnitList[unitIndex ?? 0].measurementPrice = model.measurementPrice;
            unitIndex = null;
          }
        } else {
          productUnitList.add(model);
          unitIndex = null;
        }
        isMeasurementIsEmpty = false;
        unitController.clear();
        unitPriceController.clear();
        fnUtilities.getAllUnits();
        FocusScope.of(context).requestFocus(measure);
      });
    } else {
      setState(() {
        isMeasurementIsEmpty = unitController.text.isEmpty ? true : false;
      });
    }
  }

  //EDIT WIDGET
  Widget editWidget() {
    return InkWell(
      onTap: () {
        setState(() {
          isUpdate = true;
        });
      },
      child: SvgPicture.asset(
        editSVG,
        color: Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    // height = 200.0;
    return Scaffold(
      backgroundColor: appThemeColor,
      appBar: BaseAppBar(
        title: text(
          id == null ? "Add Product" : "Update Product",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        widgets: id != null
            ? <Widget>[
                Padding(
                  padding: const EdgeInsets.only(right: 20.0),
                  child: editWidget(),
                )
              ]
            : null,
      ),
      body: Stack(
        children: [
          Padding(
            padding: const EdgeInsets.all(0.0),
            child: Container(
              height: height,
              decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
              child: Padding(
                padding:
                    // const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
                    const EdgeInsets.all(10.0),
                child: SingleChildScrollView(
                  child: Form(
                    key: validator.formkey,
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        BootstrapRow(children: [
                          BootstrapCol(
                            sizes: 'col-md-6 col-sm-12',
                            child: Padding(
                              padding: const EdgeInsets.all(10.0),
                              child: Column(
                                children: [
                                  sizeHeight,
                                  // product id
                                  Container(width: double.maxFinite, child: productIdWidget()),
                                  sizeHeight,
                                  // product name
                                  Container(width: double.maxFinite, child: productNameWidget()),
                                  if (showDynamicQty) sizeHeight,
                                  // dynamic quantity
                                  if (showDynamicQty) Container(child: dynamicQuantityWidget()),
                                  sizeHeight,
                                  Container(
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        Container(width: width >= tabletWidth ? width * 0.42 : width * 0.6897, child: categoryDropDownWidget()),
                                        // add icon for catagory
                                        addCategoryIcon()
                                      ],
                                    ),
                                  ),
                                  // toppings widget
                                  if (showToppings) sizeHeight,
                                  if (showToppings)
                                    Container(
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                        children: <Widget>[
                                          toppingsGroupList.length != 0
                                              ? Column(
                                                  mainAxisAlignment: MainAxisAlignment.start,
                                                  children: [
                                                    Container(
                                                        width: width >= tabletWidth ? width * 0.4 : width * 0.6897, child: toppingDropDownWidget()),
                                                  ],
                                                )
                                              : Container(),
                                          // topping add icon
                                          toppingAddIcon(),
                                        ],
                                      ),
                                    ),
                                  if (showMeasurement) sizeHeight,
                                  if (showMeasurement)

                                    /// measurement widget check box
                                    Container(
                                      child: measurementCheckBoxWidget(),
                                    ),
                                  measurementUpdate
                                      ? Column(
                                          children: [
                                            /// measurement input widget
                                            measurementInputField(),
                                            Row(
                                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                productUnitList.length != 0
                                                    ? SizedBox(
                                                        // width:? width * 0.47 :* 0.8,
                                                        width: width > tabletWidth ? width * 0.47 : width * 0.8,

                                                        /// measurement units list widget
                                                        child: measurementUnitListWidget(),
                                                      )
                                                    : SizedBox.shrink(),
                                              ],
                                            ),
                                          ],
                                        )
                                      : SizedBox.shrink(),
                                  sizeHeight,

                                  /// price input field
                                  !measurementUpdate ? priceInputWidget() : SizedBox.shrink(),
                                  sizeHeight,

                                  /// MRP input field
                                  !measurementUpdate ? mrpInputWidget() : SizedBox.shrink(),
                                  showTax
                                      ? Column(
                                          children: [sizeHeight, addTaxWidget()],
                                        )
                                      : SizedBox.shrink(),
                                  taxupdate == true ? gstTextFieldWidget() : SizedBox.shrink(),
                                ],
                              ),
                            ),
                          ),
                          BootstrapCol(
                              sizes: 'col-md-6 col-sm-12',
                              child: Padding(
                                padding: const EdgeInsets.all(10.0),
                                child: Column(
                                  children: [
                                    // showInventory
                                    //     ? SizedBox(
                                    //         height: 10,
                                    //       )
                                    //     : Container(),
                                    // showInventory

                                    //     /// add stock
                                    //     ? Container(child: addStockWidget())
                                    //     : SizedBox.shrink(),
                                    // enableInventory == true && id == null && showInventory ? addStockTextFieldWidget() : SizedBox.shrink(),

                                    ///Product for
                                    if (showProductFor) ...[
                                      sizeHeight,
                                      productForWidget(),
                                    ],

                                    /// parcel details widget
                                    parcelDetailsWidget(),
                                    sizeHeight,
                                    //TEMP IMAGE PICKER
                                    Padding(
                                      padding: const EdgeInsets.only(bottom: 20),
                                      child: Row(
                                        mainAxisAlignment: MainAxisAlignment.start,
                                        children: [
                                          // image update
                                          imageUpdateWidget(),
                                          // imageFromGallery(),
                                          showImage(),
                                        ],
                                      ),
                                    ),
                                    SizedBox(height: 24),

                                    // if (tabletWidth > width) ...[
                                    //   Row(
                                    //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    //     children: [
                                    //       addUpdateProductButton(),
                                    //       id == null ? addMoreProductButton() : SizedBox.shrink(),
                                    //     ],
                                    //   ),
                                    // ] else ...[
                                    //   SizedBox.shrink()
                                    // ]
                                  ],
                                ),
                              )),
                        ]),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
          Container(
            child: Positioned(
                right: 20,
                bottom: 20,
                child:
                    // width > tabletWidth
                    //     ?
                    Container(
                  padding: EdgeInsets.only(top: 10),
                  color: secondaryTextColor,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Padding(
                        padding: EdgeInsets.only(right: 3.0),
                        child: addUpdateProductButton(),
                      ),
                      id == null
                          ? Padding(
                              padding: EdgeInsets.only(left: 3.0),
                              child: addMoreProductButton(),
                            )
                          : SizedBox.shrink(),
                    ],
                  ),
                )
                // : SizedBox()
                ),
          )
        ],
      ),
    );
  }

  /// product Id text field

  productIdWidget() {
    return TextFieldBoxWidget(
      text: "Product Id",
      isPassword: false,
      mController: productSaleIdController,
      inputFormate: [
        FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
      ],
      inputType: TextInputType.number,
      validator: (value) {
        if (value.isEmpty)
          return 'This field is required';
        else {
          try {
            final formattedValue = value.replaceAll(RegExp(r'^0+'), '');
            ListUtility.productList.firstWhere(
              (element) => element.productSaleId.toString() == formattedValue && id != element.productId.toString(),
            );
            return 'This name is already used';
          } catch (e) {
            return null;
          }
        }
      },
      inputAction: TextInputAction.next,
      enable: isUpdate,
    );
  }
// product name text field

  productNameWidget() {
    return TextFieldBoxWidget(
      text: "Product Name",
      isPassword: false,
      mController: productController,
      autofocus: true,
      focus: product,
      maxLength: 40,
      validator: (value) {
        if (value.isEmpty)
          return 'This field is required';
        else {
          try {
            ListUtility.productList.firstWhere(
              (element) => element.name == value && id != element.productId,
            );
            return 'This name is already used';
          } catch (e) {
            return null;
          }
        }
      },
      inputAction: TextInputAction.next,
      enable: isUpdate,
    );
  }

  /// dynameic quantity

  dynamicQuantityWidget() {
    return CheckboxListTile(
      title: Text("Dynamic quantity"),
      activeColor: !isUpdate ? grey : null,
      contentPadding: EdgeInsets.all(0),
      controlAffinity: ListTileControlAffinity.leading,
      value: dynamicQuantity,
      onChanged: (bool? value) {
        setState(() {
          if (isUpdate) {
            dynamicQuantity = value!;
            FocusScope.of(context).requestFocus(FocusNode());
          }
        });
      },
    );
  }

  /// input category dropdown widget

  categoryDropDownWidget() {
    return InputDecorator(
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
        labelText: 'Category',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
      ),
      child: DropdownButtonHideUnderline(
        child: new DropdownButton<ProductKey>(
          hint: Text("Category"),
          value: selectKeyword,
          items: productKeyList.map((ProductKey value) {
            return new DropdownMenuItem<ProductKey>(
              value: value,
              child: new Text(value.keywordName ?? ""),
            );
          }).toList(),
          onChanged: !isUpdate
              ? null
              : (ProductKey? val) async {
                  setState(() {
                    if (isUpdate) {
                      selectKeyword = val;
                      FocusScope.of(context).requestFocus(FocusNode());
                    }
                  });
                },
        ),
      ),
    );
  }

  /// add category button

  addCategoryIcon() {
    return InkWell(
      onTap: () {
        if (!isLock) {
          isLock = true;
          if (isUpdate) {
            Navigator.push(context, MaterialPageRoute(builder: (context) => ViewProductCategory())).then((value) async {
              setState(() {
                selectKeyword = null;
              });
              if (id == null) {
                // ignore: await_only_futures
                await fnUtilities.getAllProductKey();
                getAllProductKey();
                if (productKeyList.length != 0) {
                  selectKeyword = productKeyList.last;
                }
              } else {
                await setTextFieldValues();
              }
            });
          }
          isLock = false;
        }
      },
      child: Container(
          padding: EdgeInsets.symmetric(vertical: 13),
          // width: 25,
          child: Icon(Icons.add)),
    );
  }

  /// topping drop down widget

  toppingDropDownWidget() {
    return InputDecorator(
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
        labelText: 'toppings',
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
      ),
      child: DropdownButtonHideUnderline(
        child: new DropdownButton<ToppingsGroup>(
          hint: Text("toppings"),
          value: selectGroup,
          items: toppingsGroupList.map((ToppingsGroup value) {
            return new DropdownMenuItem<ToppingsGroup>(
              value: value,
              child: new Text(value.toppingGroupName ?? ""),
            );
          }).toList(),
          onChanged: !isUpdate
              ? null
              : (ToppingsGroup? val) async {
                  setState(() {
                    if (isUpdate) {
                      selectGroup = val;
                      FocusScope.of(context).requestFocus(FocusNode());
                    }
                  });
                },
        ),
      ),
    );
  }

  /// topping add icon

  toppingAddIcon() {
    return InkWell(
      onTap: () {
        if (!isLock) {
          isLock = true;
          if (isUpdate) {
            Navigator.push(context, MaterialPageRoute(builder: (context) => ToppingsList())).then((value) async {
              setState(() {
                selectGroup = null;
              });
              if (id == null) {
                // ignore: await_only_futures
                await getAllToppingGroup();
                if (toppingsGroupList.length != 0) {
                  selectGroup = toppingsGroupList.last;
                }
              } else {
                await setTextFieldValues();
              }
            });
          }
          isLock = false;
        }
      },
      child: Container(padding: EdgeInsets.symmetric(vertical: 13), child: Icon(Icons.add)),
    );
  }

// measurement widget checkbox

  measurementCheckBoxWidget() {
    return CheckboxListTile(
      title: Text("Add Measurement"),
      activeColor: !isUpdate ? grey : null,
      contentPadding: EdgeInsets.all(0),
      controlAffinity: ListTileControlAffinity.leading,
      value: measurementUpdate,
      onChanged: (bool? priceUpdateValue) {
        setState(() {
          if (isUpdate) {
            measurementUpdate = priceUpdateValue ?? false;
            FocusScope.of(context).requestFocus(FocusNode());
          }
        });
      },
    );
  }

// measurement input field and add button widget

  measurementInputField() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Container(
          padding: EdgeInsets.only(bottom: 5),
          // width: width * 0.3704,
          width: width > tabletWidth ? width * 0.3 : width * 0.3704,
          child: TextFieldBoxWidget(
            text: "Measurement",
            isPassword: false,
            mController: unitController,
            errorText: isMeasurementIsEmpty ? "This field is required" : null,
            focus: measure,
            inputType: TextInputType.text,
            inputAction: TextInputAction.next,
            enable: isUpdate,
          ),
        ),
        SizedBox(
          width: width * 0.01,
        ),
        Expanded(
          child: Container(
            width: width > tabletWidth ? width * 0.3 : width * 0.3704,
            padding: EdgeInsets.only(bottom: 5, right: 10),
            child: TextFieldBoxWidget(
              text: "Price",
              isPassword: false,
              mController: unitPriceController,
              inputType: TextInputType.number,
              inputFormate: [
                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
              ],
              inputAction: TextInputAction.done,
              enable: isUpdate,
              submit: (submit) {
                if (isUpdate) {
                  onSubmitUnitMeasurement();
                }
              },
            ),
          ),
        ),
        // SizedBox(
        //   width: 5,
        // ),
        InkWell(
          onTap: () {
            if (isUpdate) {
              onSubmitUnitMeasurement();
            }
          },
          child: Container(padding: EdgeInsets.symmetric(vertical: 13), child: unitIndex == null ? Icon(Icons.add) : Icon(Icons.update)),
        )
      ],
    );
  }

  ///  price input widget

  priceInputWidget() {
    return Container(
      child: TextFieldBoxWidget(
        text: "Price",
        isPassword: false,
        mController: priceController,
        inputType: TextInputType.number,
        inputFormate: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        ],
        inputAction: TextInputAction.next,
        enable: isUpdate,
      ),
    );
  }

  /// MRP input widget
  mrpInputWidget() {
    return Container(
      child: TextFieldBoxWidget(
          text: "MRP",
          isPassword: false,
          mController: mrpController,
          inputType: TextInputType.number,
          inputFormate: [
            FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
          ],
          inputAction: taxupdate == true ? TextInputAction.next : TextInputAction.done,
          enable: isUpdate,
          submit: (submit) {
            if (validator.validate() ?? false) {
              if (isUpdate) {
                onSaveProduct("more");
              }
            }
          }),
    );
  }

  /// add tax check box widget

  addTaxWidget() {
    return Container(
      child: CheckboxListTile(
        title: Text("Add Tax"),
        contentPadding: EdgeInsets.all(0),
        activeColor: !isUpdate ? grey : null,
        controlAffinity: ListTileControlAffinity.leading,
        value: taxupdate,
        onChanged: (bool? priceupdateValue) {
          setState(() {
            if (isUpdate) {
              taxupdate = priceupdateValue ?? false;
              FocusScope.of(context).requestFocus(FocusNode());
            }
          });
        },
      ),
    );
  }

  // /// add stock check box widget
  // addStockWidget() {
  //   return CheckboxListTile(
  //     title: Text("Add Stock"),
  //     contentPadding: EdgeInsets.all(0),
  //     activeColor: !isUpdate ? grey : null,
  //     controlAffinity: ListTileControlAffinity.leading,
  //     value: enableInventory,
  //     onChanged: (bool inventory) {
  //       setState(() {
  //         if (isUpdate) {
  //           enableInventory = inventory;
  //           FocusScope.of(context).requestFocus(FocusNode());
  //         }
  //       });
  //     },
  //   );
  // }

  // /// add stock text field
  // addStockTextFieldWidget() {
  //   return Column(
  //     children: [
  //       TextFieldBoxWidget(
  //           text: "Stocks",
  //           isPassword: false,
  //           mController: stocksController,
  //           inputType: TextInputType.number,
  //           inputFormate: [
  //             FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
  //           ],
  //           inputAction: TextInputAction.done,
  //           enable: isUpdate,
  //           submit: (submit) {
  //             if (validator.validate()) {
  //               if (isUpdate) {
  //                 onSaveProduct("more");
  //               }
  //             }
  //           }),
  //       sizeHeight,
  //     ],
  //   );
  // }

  /// GST widget
  gstTextFieldWidget() {
    return TextFieldBoxWidget(
        text: "GST (%)",
        isPassword: false,
        mController: gstController,
        inputType: TextInputType.number,
        inputFormate: [
          FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
        ],
        validator: validator.validateTextField,
        inputAction: TextInputAction.done,
        enable: isUpdate,
        submit: (submit) {
          if (validator.validate() ?? false) {
            if (isUpdate) {
              onSaveProduct("more");
            }
          }
        });
  }

  /// image update
  imageUpdateWidget() {
    return GestureDetector(
      onTap: () {
        if (isUpdate) {
          pickImage();
        }
        //pickImageFromGallery(ImageSource.gallery);
      },
      child: Container(
        alignment: Alignment.center,
        height: 50,
        // width: width * 0.3333,
        width: width > tabletWidth ? width * 0.20 : width * 0.20,
        child: TextWidget("Image", textColor: secondaryTextColor, isCentered: true),
        decoration: boxDecoration(bgColor: !isUpdate ? grey : buttonThemeColor, radius: 8.0),
      ),
    );
  }

  /// add or update product
  addUpdateProductButton() {
    return InkWell(
      onTap: () {
        validator.validate();
        if (validator.validate() ?? false) {
          if (isUpdate) {
            onSaveProduct("add");
          }
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: 50,
        width: width > tabletWidth
            ? (id == null ? width * 0.234 : width * 0.45)
            : id == null
                ? width * 0.45
                : width * 0.79,
        child: TextWidget(id == null ? "Add" : "Update Product ", textColor: secondaryTextColor, isCentered: true),
        decoration: boxDecoration(bgColor: !isUpdate ? grey : buttonThemeColor, radius: 8.0),
      ),
    );
  }

  /// add more product button

  addMoreProductButton() {
    return InkWell(
      onTap: () {
        validator.validate();
        if (validator.validate() ?? false) {
          if (isUpdate) {
            onSaveProduct("more");
          }
        }
      },
      child: Container(
        alignment: Alignment.center,
        height: 50,
        width: width > tabletWidth ? width * 0.234 : width * 0.45,
        child: TextWidget("More", textColor: secondaryTextColor, isCentered: true),
        decoration: boxDecoration(bgColor: !isUpdate ? grey : buttonThemeColor, radius: 8.0),
      ),
    );
  }

  File? imageFile;

  pickImage() async {
    try {
      XFile? pickedFile = await ImagePicker().pickImage(source: ImageSource.gallery);
      if (pickedFile != null) {
        imageFile = File(pickedFile.path);
      }
      var cropFile = await cropImage(imageFile?.path);
      setState(() {
        imageFile = cropFile;
      });
    } catch (ex) {
      imageFile = null;
    }
  }

  Future<File> cropImage(filePath) async {
    CroppedFile? croppedImage =
        await ImageCropper().cropImage(sourcePath: filePath, maxWidth: 150, maxHeight: 150, aspectRatio: CropAspectRatio(ratioX: 1, ratioY: 1));
    return File(croppedImage!.path);
  }

  Future<String?> saveImage() async {
    try {
      var uuid = new Uuid();
      String id = uuid.v4().toString();
      if (imageFile == null) return null;
      final String path1 = (await getApplicationDocumentsDirectory()).path;
      //var fileName = path.basename(imageFile.path);
      final File? storedImage = await imageFile?.copy('$path1/$id.png');
      String localImagePath = storedImage!.path;
      return id;
    } catch (ex) {
      //print(ex);
      return null;
    }
  }

  Widget showImage() {
    return !imageFile.toString().isEmptyOrNull
        ? Stack(
            children: [
              isUpdate
                  ? Positioned(
                      top: 5.0,
                      right: -10.0,
                      child: IconButton(
                        onPressed: () {
                          setState(() {
                            imageFile = null;
                            imageId = "";
                          });
                        },
                        icon: Icon(Icons.delete, size: 25.0, color: Colors.redAccent[400]),
                      ), //Icon
                    )
                  : Container(),
              SizedBox(
                height: 5,
              ),
              Container(width: 200, height: 200, alignment: Alignment.center, child: Image.file(imageFile!))
            ],
          )
        : Container(
            decoration: boxDecoration(radius: 8.0),
            alignment: Alignment.center,
            child: Image(image: AssetImage("assets/images/no-image.jpeg")),
            width: 200,
            height: 200,
          );
  }

  parcelDetailsWidget() {
    if (showParcel) {
      return Column(
        children: [
          SizedBox(
            height: 10,
          ),
          Column(children: <Widget>[
            TextFieldBoxWidget(
                text: "Parcel Amount (₹)",
                isPassword: false,
                mController: parcelController,
                inputType: TextInputType.number,
                inputFormate: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                // validator: validator.validateTextField,
                inputAction: TextInputAction.done,
                enable: isUpdate,
                submit: (submit) {
                  if (validator.validate() ?? false) {
                    if (isUpdate) {
                      onSaveProduct("more");
                    }
                  }
                }),
            SizedBox(height: 10)
          ]),
          SizedBox(
            height: 10,
          ),
        ],
      );
    } else {
      return Container();
    }
  }

  /// measurement units list

  measurementUnitListWidget() {
    return SingleChildScrollView(
      child: ListView.builder(
          itemCount: productUnitList.length,
          scrollDirection: Axis.vertical,
          physics: ScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            var item = productUnitList[index];
            return productUnitList[index].rowStatus != 2
                ? Container(
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          child: Row(
                            children: [
                              Container(
                                // color: blueViolet,
                                // width: width * 0.1571,
                                width: width > tabletWidth ? width * 0.216 : width * 0.28,
                                child: TextWidget(item.measurement ?? "",
                                    textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                              ),
                              Container(
                                // color: yellowGreen,
                                // width: width * 0.1571,
                                width: width > tabletWidth ? width * 0.217 : width * 0.35,
                                child: TextWidget(item.measurementPrice ?? "",
                                    textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                              ),
                              Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  InkWell(
                                      onTap: () {
                                        if (!isLock) {
                                          isLock = true;
                                          setState(() {
                                            if (isUpdate) {
                                              unitController.text = item.measurement ?? "";
                                              unitPriceController.text = item.measurementPrice ?? "";
                                              unitIndex = index;
                                            }
                                          });
                                          isLock = false;
                                        }
                                      },
                                      child: Icon(Icons.edit)),
                                  SizedBox(
                                    width: 2,
                                  ),
                                  InkWell(
                                      onTap: () {
                                        if (!isLock) {
                                          isLock = true;
                                          setState(() {
                                            if (isUpdate) {
                                              if (productUnitList[index].priceTypeId != null) {
                                                productUnitList[index].rowStatus = 2;
                                              } else {
                                                productUnitList.removeAt(index);
                                              }
                                            }
                                          });
                                          isLock = false;
                                        }
                                      },
                                      child: Icon(Icons.delete)),
                                ],
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : SizedBox.shrink();
          }),
    );
  }

  productForWidget() {
    return Column(
      children: [
        Align(
            alignment: Alignment.bottomLeft,
            child: Text(
              "Product for",
              style: black16TextStyle,
            )),
        Row(
          children: [
            Radio<ProductFor>(
              value: ProductFor.purchase,
              groupValue: selectedProductFor,
              onChanged: setProductType,
            ),
            Text('Purchase'),
            Radio<ProductFor>(
              value: ProductFor.sale,
              groupValue: selectedProductFor,
              onChanged: setProductType,
            ),
            Text('Sale'),
            Radio<ProductFor>(
              value: ProductFor.both,
              groupValue: selectedProductFor,
              onChanged: setProductType,
            ),
            Text('Both'),
          ],
        ),
      ],
    );
  }
}
