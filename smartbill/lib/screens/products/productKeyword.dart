import 'package:flutter/material.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:uuid/uuid.dart';
import '../../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';

class ProductKeyDetails extends StatefulWidget {
  ProductKey? productCategory;

  ProductKeyDetails({Key? key, this.productCategory}) : super(key: key);

  @override
  _ProductKeyDetailsState createState() => _ProductKeyDetailsState(productCategory);
}

class _ProductKeyDetailsState extends State<ProductKeyDetails> {
  final dbHelper = DatabaseHelper.instance;
  final keyController = TextEditingController();
  bool isLock = false;
  CommonDB commonDB = CommonDB();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  Validator validator = Validator();
  ProductKey? productCategory;
  DBFuctionsScript dbScript = DBFuctionsScript();

  _ProductKeyDetailsState(this.productCategory);

  @override
  void initState() {
    assignValue();
  }

  assignValue() {
    if (productCategory != null) {
      keyController.text = productCategory?.keywordName ?? "";
    }
  }

  productKeyWordOperation(ProductKey model, String operation) async {
    var productKey;
    if (operation == "insert") {
      productKey = {
        db_userPermissions_productKeyId: model.productKeyId,
        db_userPermissions_keywordName: model.keywordName,
        db_workspace_workspaceId: model.workspaceId,
        db_userPermissions_keywordSync: model.keywordSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.dbInsert(db_userPermissions_productKeyword, productKey);
    } else {
      productKey = {
        db_userPermissions_keywordName: model.keywordName,
        db_workspace_workspaceId: model.workspaceId,
        db_userPermissions_keywordSync: model.keywordSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.updateModel(db_userPermissions_productKeyword, productKey, db_userPermissions_productKeyId, model.productKeyId);
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    // double height = MediaQuery.of(context).size.height;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(productCategory == null ? "Add Category" : "Update Category",
            textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      body: Container(
        padding: EdgeInsets.all(20),
        width: width,
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                width: width,
                child: Form(
                  key: validator.formkey,
                  child: Column(
                    children: [
                      Row(
                        children: [
                          SizedBox(
                            width: width / 1.15,
                            child: EditText(
                                mController: keyController,
                                inputType: TextInputType.text,
                                isPassword: false,
                                text: "Category",
                                maxLength: 20,
                                validator: (value) {
                                  if (value.isEmpty)
                                    return 'This field is required';
                                  else {
                                    try {
                                      ListUtility.productKeyList.firstWhere(
                                        (element) => element.keywordName == value && element.productKeyId != productCategory?.productKeyId,
                                      );
                                      return 'This name is already used';
                                    } catch (e) {
                                      return null;
                                    }
                                  }
                                }),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      GestureDetector(
                        onTap: () async {
                          if (!isLock) {
                            isLock = true;
                            if (validator.validate() ?? false) {
                              ProductKey model = ProductKey();
                              model.keywordName = keyController.text;
                              model.workspaceId = MyApp.activeWorkspace.workspaceId;

                              if (productCategory == null) {
                                var uuid = new Uuid();
                                String productKeyId = uuid.v4().toString();
                                model.productKeyId = productKeyId;
                                model.keywordSync = 0;
                                model.rowStatus = 0;
                                productKeyWordOperation(model, "insert");
                              } else {
                                model.productKeyId = productCategory?.productKeyId;
                                model.keywordSync = 0;
                                model.rowStatus = await commonDB.checkNonSyncCommonFunction("ProductKeyword", "productKeyId",
                                            productCategory?.productKeyId != null ? productCategory?.productKeyId.toString() : "", "keywordSync") ==
                                        0
                                    ? 1
                                    : 0;
                                // await productKeyDBScript
                                //     .updateProductsKeyTable(model);
                                productKeyWordOperation(model, "update");
                                setState(() {
                                  productCategory = ProductKey();
                                });
                              }
                              await fnUtilities.getAllProductKey();
                              Navigator.pop(context);
                              keyController.clear();
                            }
                            isLock = false;
                          }
                        },
                        child: Container(
                          alignment: Alignment.center,
                          height: 50,
                          width: width,
                          child: TextWidget(productCategory == null ? "Add Category" : "Update Category",
                              textColor: secondaryTextColor, isCentered: true),
                          decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(
                height: 20,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
