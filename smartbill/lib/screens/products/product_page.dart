// ignore_for_file: sdk_version_ui_as_code, unused_local_variable, non_constant_identifier_names

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';

import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import '../../utils/common_function/fn_utilities.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/dbutils/db_crud_script.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/product_details_db_script.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';
import 'add_product_page.dart';

//PRODUCT LIST VIEW CLASS
class ProductListPage extends StatefulWidget {
  @override
  ProductListPageState createState() => ProductListPageState();
}

class ProductListPageState extends State<ProductListPage> {
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  Validator validator = new Validator();
  final dbHelper = DatabaseHelper.instance;
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  final searchController = new TextEditingController();
  bool enableAddProduct = true;
  bool taxupdate = false;
  bool isLock = false;
  var width;
  var height;
  bool isProductIDDesc = false;
  bool isProductNameDesc = false;
  bool isProductPriceDesc = false;
  List<Products> filterProductList = [];
  List<ProductKey> productKeyList = [];
  ProductKey? selectCategory;
  FnUtilities fnUtilities = FnUtilities();

  assignProductList() async {
    if (ListUtility.productList.isNotEmpty) {
      ListUtility.productList = await fnUtilities.assignFilePath(ListUtility.productList);
      setState(() {
        filterProductList.clear();
        filterProductList.addAll(ListUtility.productList);
        print(filterProductList.length);
      });
    } else {
      setState(() {
        filterProductList.clear();
      });
    }
  }

  //GET ALL PRODUCT DETAILS
  queryAllProductDetailsByCategory(String categoryID) async {
    final allRows = await productDetailsDBScript.queryAllRowsProductDetailsByCategory(categoryID);
    setState(() {
      filterProductList.clear();
      allRows.forEach((row) => filterProductList.add(Products.fromMap(row)));
      print(filterProductList.length);
    });
  }

  getAllProductKey() async {
    if (ListUtility.productKeyList.isNotEmpty) {
      setState(() {
        productKeyList.clear();
        ProductKey model = ProductKey();
        model.keywordName = "All";
        productKeyList.add(model);
        productKeyList.addAll(ListUtility.productKeyList);
      });
    }
  }

  //DELETE PRODUCT DETAILS LIKE INVISIBLE
  updateWithDelete(String id) async {
    Products products = Products(productId: id, sync: 0);
    await productDetailsDBScript.deleteUpdateProductsTable(products);
    setState(() {
      assignProductList();
    });
  }

  sortByProductID() {
    try {
      setState(() {
        if (isProductIDDesc) {
          isProductIDDesc = false;
          filterProductList.sort((a, b) => a.productSaleId!.compareTo(b.productSaleId ?? 0));
        } else {
          isProductIDDesc = true;
          filterProductList.sort((a, b) => b.productSaleId!.compareTo(a.productSaleId ?? 0));
        }
      });
    } catch (ex) {}
  }

  sortByProductName() {
    try {
      setState(() {
        if (isProductNameDesc) {
          isProductNameDesc = false;
          filterProductList.sort((a, b) => a.name!.toLowerCase().compareTo(b.name!.toLowerCase()));
        } else {
          isProductNameDesc = true;
          filterProductList.sort((a, b) => b.name!.toLowerCase().compareTo(a.name!.toLowerCase()));
        }
      });
    } catch (ex) {}
  }

  sortByProductPrice() {
    try {
      setState(() {
        if (isProductPriceDesc) {
          isProductPriceDesc = false;
          filterProductList.sort((a, b) => a.price != null && a.price != ""
              ? double.parse(a.price ?? "").compareTo(b.price != null && b.price != "" ? double.parse(b.price ?? "") : 0)
              : 0.compareTo(b.price != null && b.price != "" ? double.parse(b.price ?? "") : 0));
        } else {
          isProductPriceDesc = true;
          filterProductList.sort((a, b) => b.price != null && b.price != ""
              ? double.parse(b.price ?? "").compareTo(a.price != null && a.price != "" ? double.parse(a.price ?? "") : 0)
              : 0.compareTo(a.price != null && a.price != "" ? double.parse(a.price ?? "") : 0));
        }
      });
    } catch (ex) {}
  }

  void filterProductsByName(String name) async {
    if (name != "") {
      setState(() {
        filterProductList.clear();
        for (Products product in ListUtility.productList) {
          if (product.name!.toLowerCase().contains(name.toLowerCase())) {
            filterProductList.add(product);
          }
        }
      });
    } else {
      setState(() {
        filterProductList.clear();
        filterProductList.addAll(ListUtility.productList);
      });
    }
  }

  //MESSAGE FOR PERMISSION
  // ignore: missing_return
  bool showMessage() {
    showToast("You don't have permission.");
    return false;
  }

  initPageDetails() async {
    await assignProductList();
    await getAllProductKey();
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  filterDialog(BuildContext context) async {
    selectCategory = selectCategory == null ? productKeyList.first : selectCategory;
    return showDialog(
        barrierDismissible: true,
        context: context,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
              title: Text(
                "Filter",
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
              ),
              content: Container(
                // width: double.maxFinite,
                height: height / 4,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SizedBox(
                          // width: width / 1.5,
                          child: InputDecorator(
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 1.0),
                              labelText: 'Category',
                              border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                            ),
                            child: DropdownButtonHideUnderline(
                              child: new DropdownButton<ProductKey>(
                                hint: Text("All"),
                                value: selectCategory,
                                items: productKeyList.map((ProductKey value) {
                                  return new DropdownMenuItem<ProductKey>(
                                    value: value,
                                    child: new Text(value.keywordName ?? ""),
                                  );
                                }).toList(),
                                onChanged: (ProductKey? value) async {
                                  if (!isLock) {
                                    isLock = true;
                                    setState(() {
                                      selectCategory = value;
                                    });
                                    isLock = false;
                                  }
                                },
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          height: 10,
                        ),
                      ],
                    ),
                    SizedBox(
                      height: 40,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        RoundedButton(
                          width: 100,
                          title: "Reset",
                          onPressed: () async {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              searchController.clear();
                              selectCategory = productKeyList.first;
                              await assignProductList();
                              FocusScope.of(context).unfocus();
                              isLock = false;
                            }
                          },
                        ),
                        RoundedButton(
                          width: 100,
                          title: "Filter",
                          onPressed: () async {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              searchController.clear();
                              if (selectCategory?.keywordName == "All") {
                                await assignProductList();
                              } else {
                                await queryAllProductDetailsByCategory(selectCategory?.productKeyId ?? "");
                              }
                              isLock = false;
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          });
        });
  }

  String sortType = "ID";

  sortDialog(BuildContext context) {
    return showDialog(
        context: context,
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            return AlertDialog(
                contentPadding: EdgeInsets.all(10),
                scrollable: true,
                content: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                        // width: double.maxFinite,
                        child: Column(
                      children: [
                        Row(
                          children: [
                            new Radio(
                              value: "ID",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value!;
                                });
                              },
                            ),
                            new Text('Sort By ID'),
                          ],
                        ),
                        Row(
                          children: [
                            new Radio(
                              value: "Name",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value!;
                                });
                              },
                            ),
                            new Text('Sort By Name'),
                          ],
                        ),
                        Row(
                          children: [
                            new Radio(
                              value: "Price",
                              groupValue: sortType,
                              onChanged: (value) {
                                setState(() {
                                  sortType = value!;
                                });
                              },
                            ),
                            new Text('Sort By Price'),
                          ],
                        )
                      ],
                    )),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        RoundedButton(
                          width: 100,
                          title: "Cancel",
                          onPressed: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                        ),
                        RoundedButton(
                          width: 100,
                          title: "Sort",
                          onPressed: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              if (sortType == "ID") {
                                sortByProductID();
                              }
                              if (sortType == "Name") {
                                sortByProductName();
                              }
                              if (sortType == "Price") {
                                sortByProductPrice();
                              }
                              isLock = false;
                            }
                          },
                        ),
                      ],
                    ),
                  ],
                ));
          });
        });
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: appThemeColor,
      resizeToAvoidBottomInset: false,
      appBar: BaseAppBar(
        title: text(
          "Products",
          textColor: secondaryTextColor,
          fontSize: textSizeLargeMedium,
        ),
        appBar: AppBar(),
        widgets: <Widget>[
          enableAddProduct
              ? Padding(
                  padding: const EdgeInsets.only(right: 20.0, top: 10.0),
                  child: productAddButton(),
                )
              : Container(
                  width: 0.0,
                  height: 0.0,
                  color: Colors.red,
                ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(0.0),
        child: Container(
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(0), color: backgroundColor),
          child: Padding(
            padding: const EdgeInsets.all(0.0),
            child: Column(
              children: [
                SizedBox(
                  height: 3,
                ),
                searchFilterSortFunction(),
                productListFunction(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// product list

  Widget productListFunction() {
    return Expanded(
      child: Align(
        alignment: Alignment.topCenter,
        child: ListView.builder(
            padding: EdgeInsets.symmetric(horizontal: 5),
            scrollDirection: Axis.vertical,
            itemCount: filterProductList.length,
            shrinkWrap: true,
            physics: ScrollPhysics(),
            itemBuilder: (context, index) {
              //DELETE ALERT DIALOG FOR PRODUCTS
              var item = filterProductList[index];
              return GestureDetector(
                onLongPress: () {
                  final bool res = PermissionFunctions.checkPermission(17);
                  if (PermissionFunctions.checkPermission(17)) {
                    showDialog(
                        context: context,
                        builder: (BuildContext context) {
                          return deleteAlertDialog(item);
                        });
                  }
                  // final bool res = PermissionFunctions.checkPermission(17)
                  //     ? showDialog(
                  //         context: context,
                  //         builder: (BuildContext context) {
                  //           return deleteAlertDialog(item);
                  //         })
                  //     : showMessage();
                  // return res;
                },
                child: Column(
                  children: [
                    Container(
                      margin: EdgeInsets.symmetric(vertical: 10),
                      // padding: EdgeInsets.symmetric(
                      //     horizontal: 5),
                      child: InkWell(
                        onTap: () async {
                          if (PermissionFunctions.checkPermission(15)) {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => AddProductDialog(
                                          id: item.productId ?? "",
                                        ))).then(
                              (value) async {
                                if (value ?? false) {
                                  await fnUtilities.getAllProductDetails();
                                  await assignProductList();
                                }
                              },
                            );

                            fnUtilities.getAllProductKey();

                            //await queryAllProductKeys();
                          }
                          // else {
                          //   showToast("you don't have a permission");
                          // }
                        },
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            /// serial number
                            Column(
                              children: [
                                Container(
                                  width: width * 0.15,
                                  alignment: Alignment.center,
                                  margin: EdgeInsets.only(right: 5),
                                  child: TextWidget(item.productSaleId.toString(), fontSize: textSizeLargeMedium, isCentered: true),
                                ),
                              ],
                            ),

                            /// product name , price , Category
                            Expanded(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Padding(
                                    padding: const EdgeInsets.only(right: 8),
                                    child: Container(
                                      child: TextWidget(item.name ?? "",
                                          textColor: primaryTextColor,
                                          fontSize: textSizeMedium,
                                          fontWeight: FontWeight.bold,
                                          isCentered: false,
                                          isLongText: true),
                                    ),
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: <Widget>[
                                      Container(
                                        width: width * 0.25,
                                        child: TextWidget(
                                            item.price == "" || item.price == null
                                                ? ""
                                                : "\u{20B9}" + double.parse(item.price ?? "").toStringAsFixed(2),
                                            textColor: primaryTextColor,
                                            fontSize: textSizeMedium,
                                            fontFamily: fontSemibold),
                                      ),
                                      // SizedBox(
                                      //   width: width * 0.1,
                                      // ),
                                      Flexible(
                                        child: Container(
                                          child: TextWidget("  ${item.productKeyName != null ? item.productKeyName.toString() : 'None'}",
                                              fontSize: textSizeMedium, textColor: greyTextColor, isCentered: true, maxLine: 2),
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),

                            /// right arrow
                            Column(
                              children: [
                                InkWell(
                                  child: SvgPicture.asset(
                                    leftArrowSVG,
                                    color: primaryTextColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    Divider(height: 1.0, color: t5ViewColor)
                  ],
                ),
              );
            }),
      ),
    );
  }

  /// delete alert dialog widget

  Widget deleteAlertDialog(item) {
    return AlertDialog(
      content: Text("Are you sure you want to delete ${item.name}?"),
      actions: <Widget>[
        Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            Padding(
              padding: const EdgeInsets.only(right: 5.0),
              child: RoundedButton(
                width: 80,
                title: "Yes",
                onPressed: () async {
                  if (!isLock) {
                    isLock = true;
                    DBFuctionsScript dbScript = DBFuctionsScript();
                    final syncNo = await productDetailsDBScript.queryGetProductSyncNo(item.productId);
                    if (syncNo == 1) {
                      await updateWithDelete(item.productId);
                    } else {
                      await dbScript.deleteFunction(db_productDetails, db_product_productId, item.productId);
                    }
                    await fnUtilities.getAllProductDetails();
                    setState(() {
                      assignProductList();
                    });
                    Navigator.pop(context);
                    isLock = false;
                  }
                },
              ),
            ),
            RoundedButton(
              width: 80,
              title: "Cancel",
              onPressed: () {
                if (!isLock) {
                  isLock = true;
                  Navigator.pop(context);
                  isLock = false;
                }
              },
            )
          ],
        )
      ],
    );
  }

  /// search filter sort widget

  Widget searchFilterSortFunction() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: Container(
            padding: const EdgeInsets.only(top: 7, left: 5),
            height: 50,
            child: TextField(
              onTapOutside: (event) {
                FocusManager.instance.primaryFocus?.unfocus();
              },
              controller: searchController,
              onChanged: filterProductsByName,
              style: TextStyle(color: Colors.black),
              autofocus: false,
              decoration: InputDecoration(
                labelText: 'Search ',
                border: InputBorder.none,
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: grey, width: 0.0),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(20),
                  borderSide: const BorderSide(color: t5ViewColor, width: 0.0),
                ),
                suffixIcon: InkWell(
                  onTap: () {
                    setState(() {
                      FocusScope.of(context).unfocus();
                      searchController.clear();
                      filterProductsByName("");
                    });
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: SvgPicture.asset(
                      searchIconSVG,
                      color: greyColor,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
        Container(
          padding: const EdgeInsets.only(top: 7, left: 10, right: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              InkWell(
                onTap: () => filterDialog(context),
                child: SvgPicture.asset(
                  filterIconSVG,
                  color: greyColor,
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(left: 5),
                child: InkWell(
                  onTap: () => sortDialog(context),
                  child: SvgPicture.asset(
                    sortIconSVG,
                    color: greyColor,
                  ),
                ),
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget productAddButton() {
    return InkWell(
        onTap: () async {
          if (!isLock) {
            isLock = true;
            if (PermissionFunctions.checkPermission(14)) {
              setState(() {
                enableAddProduct = false;
              });
              Navigator.push(context, MaterialPageRoute(builder: (context) => AddProductDialog())).then((value) async {
                await fnUtilities.getAllProductDetails();
                await assignProductList();
                getAllProductKey();
              });
              setState(() {
                enableAddProduct = true;
              });
            }
            // else {
            //   showToast("You don't have a permission");
            // }
            isLock = false;
          }
        },
        child: Container(
          width: 30.0,
          height: 30.0,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: backgroundColor,
          ),
          child: SvgPicture.asset(
            addIconSVG,
            color: appThemeColor,
          ),
        ));
  }
}
