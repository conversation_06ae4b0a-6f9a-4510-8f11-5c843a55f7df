import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/screens/products/productKeyword.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/constants/styles.dart';
import '../../utils/dbutils/product_key_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';

class ViewProductCategory extends StatefulWidget {
  const ViewProductCategory({Key? key}) : super(key: key);

  @override
  State<ViewProductCategory> createState() => _ViewProductCategoryState();
}

class _ViewProductCategoryState extends State<ViewProductCategory> {
  List<ProductKey> productKeyList = [];
  final dbHelper = DatabaseHelper.instance;
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  bool isLock = false;

  void initState() {
    super.initState();
    getAllProductKey();
  }

  //GET PRODUCT DETAILS BY Key
  void getAllProductKey() async {
    if (ListUtility.productKeyList.isNotEmpty) {
      setState(() {
        productKeyList.clear();
        productKeyList.addAll(ListUtility.productKeyList);
      });
    }
  }

  //DYNAMIC KEYWORD ALERT DIALOG
  keywordDeleteDialog(BuildContext context, String id) {
    double width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: double.maxFinite,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      child: TextWidget("Are you sure want to delete?",
                          textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            if (!isLock) {
                              isLock = true;
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        GestureDetector(
                          onTap: () async {
                            if (!isLock) {
                              isLock = true;
                              ProductKey model = ProductKey();
                              model.productKeyId = id;
                              model.rowStatus = 2;
                              model.workspaceId = MyApp.activeWorkspace.workspaceId;
                              model.keywordSync = 0;
                              await productKeyDBScript.updateProductsKeyDelete(model);
                              await fnUtilities.getAllProductKey();
                              Navigator.pop(context);
                              isLock = false;
                            }
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width / 3.5,
                            child: TextWidget("Delete", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: text(" Product Category ", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          IconButton(
            onPressed: () async {
              Navigator.push(context, MaterialPageRoute(builder: (context) => ProductKeyDetails())).then((value) {
                setState(() {
                  fnUtilities.getAllProductKey();
                  getAllProductKey();
                });
              });
            },
            icon: const Icon(
              Icons.add,
              color: whiteColor,
              size: 30.0,
            ),
          ),
        ],
      ),
      //drawer: BaseDrawer(),
      body: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 15.0),
        child: Column(
          children: [
            const SizedBox(
              height: 10,
            ),
            productListWidget()
          ],
        ),
      ),
    );
  }

  productListWidget() {
    double width = MediaQuery.of(context).size.width;
    return Expanded(
      child: productKeyList.isNotEmpty
          ? ListView.builder(
              itemCount: productKeyList.length,
              itemBuilder: (context, index) {
                var item = productKeyList[index];
                return Column(
                  children: [
                    InkWell(
                      onDoubleTap: () async {
                        Navigator.push(context, MaterialPageRoute(builder: (context) => ProductKeyDetails(productCategory: item))).then((value) {
                          fnUtilities.getAllProductKey();
                          getAllProductKey();
                        });
                      },
                      onLongPress: () async {
                        keywordDeleteDialog(context, item.productKeyId!);
                      },
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 5, vertical: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(width: width / 1.5, child: Text(item.keywordName ?? "")),
                          ],
                        ),
                      ),
                    ),
                    const Divider(
                      thickness: 1,
                    ),
                  ],
                );
              })
          : const Center(child: Text("No Product available")),
    );
  }
}
