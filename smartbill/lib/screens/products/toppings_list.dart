// ignore_for_file: sdk_version_ui_as_code
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';

import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/common_function/role_permissions_utilities.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:uuid/uuid.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import '../../../main.dart';
import '../../utils/common_widgets/text_widget.dart';
import '../../utils/dbutils/db_strings.dart';
import '../../utils/dbutils/toppings_db_script.dart';
import '../../utils/dbutils/toppings_groups_db_script.dart';
import '../../utils/common_widgets/header_footer.dart';
import '../../utils/roles_permissions/permission_functions.dart';

//PRODUCT LIST VIEW CLASS
class ToppingsList extends StatefulWidget {
  static var tag = "/Toppings";

  @override
  ToppingsListState createState() => ToppingsListState();
}

class ToppingsListState extends State<ToppingsList> {
  RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
  DBOperations dbOperations = new DBOperations();
  ToppingGroupsDBScript toppingGroupsDBScript = ToppingGroupsDBScript();
  ToppingsDBScript toppingsDBscript = ToppingsDBScript();
  Validator validator = new Validator();
  final dbHelper = DatabaseHelper.instance;
  List<ToppingsGroup> toppingGroupList = [];
  bool enableAddProduct = true;
  bool taxupdate = false;
  bool isLock = false;
  var width;
  var height;

  //GET ALL TOPPINGS DETAILS
  void getAllToppingsGroup() async {
    if (ListUtility.toppingGroupList.isNotEmpty) {
      setState(() {
        toppingGroupList.clear();
        toppingGroupList.addAll(ListUtility.toppingGroupList);
      });
    }
  }

  //DELETE PRODUCT Toppings DETAILS LIKE INVISIBLE
  updateWithDelete(String id) async {
    ToppingsGroup model1 = ToppingsGroup();
    model1.toppingGroupId = id;
    model1.toppingGroupSync = 0;
    model1.rowStatus = 2;
    await toppingGroupsDBScript.deleteUpdateProductsToppingsGroup(model1);
    Topping model = Topping();
    model.toppingGroupId = id;
    model.rowStatus = 2;
    model.toppingSync = 0;
    await toppingsDBscript.deleteUpdateProductsToppings(model);
    await fnUtilities.getAllToppingsGroup();
    getAllToppingsGroup();
  }

  //MESSAGE FOR PERMISSION
  bool showMessage() {
    showToast("You don't have permission.");
    return false;
  }

  @override
  void initState() {
    getAllToppingsGroup();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;

    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Toppings Group", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[],
      ),
      // drawer: BaseDrawer(),
      body: Column(
        children: [
          SizedBox(
            height: 15,
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    height: height / 1.3,
                    child: Column(
                      children: [
                        Expanded(
                          child: Align(
                            alignment: Alignment.topCenter,
                            child: toppingGroupList.length > 0
                                ? ListView.builder(
                                    padding: EdgeInsets.only(right: 0.0),
                                    scrollDirection: Axis.vertical,
                                    itemCount: toppingGroupList.length,
                                    shrinkWrap: true,
                                    physics: ScrollPhysics(),
                                    itemBuilder: (context, index) {
                                      //DELETE ALERT DIALOG FOR PRODUCTS
                                      var item = toppingGroupList[index];
                                      return GestureDetector(
                                        onLongPress: () {
                                          final bool res = PermissionFunctions.checkPermission(17,viewToast: false);
                                          if(res){

                                       
                                                showDialog(
                                                  context: context,
                                                  builder: (BuildContext context) {
                                                    return AlertDialog(
                                                      content: Text("Are you sure you want to delete ${item.toppingGroupName}?"),
                                                      actions: <Widget>[
                                                        GestureDetector(
                                                          onTap: () async {
                                                            if (!isLock) {
                                                              isLock = true;
                                                              DBFuctionsScript dbScript = DBFuctionsScript();
                                                              final syncNo = await toppingGroupsDBScript.queryGetProductToppingsSyncNo(item.toppingGroupId);
                                                              if (syncNo == 1) {
                                                                await updateWithDelete(item.toppingGroupId ?? "");
                                                              } else {
                                                                await dbScript.deleteFunction(db_toppingGroups, db_toppingGroups_toppingGroupId, item.toppingGroupId ?? "");
                                                                await dbScript.deleteFunction(db_toppings, db_toppingGroups_toppingGroupId, item.toppingGroupId??"");
                                                              }

                                                              setState(() {
                                                                fnUtilities.getAllToppingsGroup();
                                                                getAllToppingsGroup();
                                                              });
                                                              Navigator.pop(context);
                                                              isLock = false;
                                                            }
                                                          },
                                                          child: Container(
                                                            alignment: Alignment.center,
                                                            height: 40,
                                                            width: width / 5,
                                                            child: TextWidget("Yes", textColor: secondaryTextColor, isCentered: true),
                                                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                                          ),
                                                        ),
                                                        GestureDetector(
                                                          onTap: () {
                                                            if (!isLock) {
                                                              isLock = true;
                                                              Navigator.pop(context);
                                                              isLock = false;
                                                            }
                                                          },
                                                          child: Container(
                                                            alignment: Alignment.center,
                                                            height: 40,
                                                            width: width / 5,
                                                            child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
                                                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                                          ),
                                                        ),
                                                      ],
                                                    );
                                                  });
                                                  }else{
                                                    showMessage();}
                                         
                                        },
                                        child: Card(
                                          margin: EdgeInsets.all(0.0),
                                          child: Container(
                                            child: InkWell(
                                              onDoubleTap: () async {
                                                if (PermissionFunctions.checkPermission(15,)) {
                                                  Navigator.push(
                                                      context,
                                                      MaterialPageRoute(
                                                          builder: (context) => AddToppings(
                                                                id: item.toppingGroupId ?? "",
                                                                groupName: item.toppingGroupName ?? "",
                                                              ))).then((value) {
                                                    setState(() {
                                                      getAllToppingsGroup();
                                                    });
                                                  });
                                                } 
                                              },
                                              child: Column(
                                                children: <Widget>[
                                                  Container(
                                                    margin: EdgeInsets.only(top: 10, bottom: 10, right: 15, left: 15),
                                                    child: Row(
                                                      children: <Widget>[
                                                        Container(
                                                          // width: width / 2,
                                                          alignment: Alignment.centerLeft,
                                                          child: TextWidget(item.toppingGroupName ?? "",
                                                              textColor: primaryTextColor,
                                                              fontSize: textSizeMedium,
                                                              fontFamily: fontSemibold,
                                                              isCentered: false,
                                                              isLongText: true),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  Divider(height: 0.5, color: t5ViewColor)
                                                ],
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    })
                                : Container(),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      // bottomNavigationBar: BottomNavigation(),
      floatingActionButton: enableAddProduct
          ? FloatingActionButton(
              backgroundColor: buttonThemeColor,
              onPressed: () async {
                if (!isLock) {
                  isLock = true;
                  if (PermissionFunctions.checkPermission(15)) {
                    setState(() {
                      enableAddProduct = false;
                    });
                    Navigator.push(context, MaterialPageRoute(builder: (context) => AddToppings())).then((value) {
                      setState(() {
                        getAllToppingsGroup();
                      });
                    });
                    setState(() {
                      enableAddProduct = true;
                    });
                    //queryAllToppingsDetails();
                  } 
                  isLock = false;
                }
              },
              child: Icon(Icons.add),
            )
          : Container(
              width: 0.0,
              height: 0.0,
              color: Colors.red,
            ),
    );
  }
}

//ADD PRODUCT DIALOG CLASS
class AddToppings extends StatefulWidget {
  String? id;
  String? groupName;
  AddToppings({this.id, this.groupName});
  @override
  _AddToppingsState createState() => _AddToppingsState(id, groupName);
}

class _AddToppingsState extends State<AddToppings> {
  String? id;
  String? groupName;
  _AddToppingsState(this.id, this.groupName);

  final toppingGroupController = TextEditingController();
  final toppingNameController = TextEditingController();
  final toppingPriceController = TextEditingController();
  bool isToppingIsEmpty = false;
  bool isPriceIsEmpty = false;
  final dbHelper = DatabaseHelper.instance;
  ToppingGroupsDBScript toppingGroupsDBScript = ToppingGroupsDBScript();
  ToppingsDBScript toppingsDBscript = ToppingsDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  Validator? validator = new Validator();
  FocusNode topping = FocusNode();
  bool isUpdate = false;
  bool isLock = false;
  int? unitIndex;
  List<Topping> productToppingsList = [];
  var width;
  var height = 100.0;

  //GET PRODUCT UNIT DETAILS FROM DB
   getAllToppings(String groupId) async {
    if (ListUtility.toppingsList.isNotEmpty) {
      setState(() {
        productToppingsList.clear();
        for (Topping t in ListUtility.toppingsList) {
          if (t.toppingGroupId == groupId) {
            productToppingsList.add(t);
          }
        }
      });
    }
  }

  //CLEAR PRODUCT TEXT FILED FUNCTION
  clearTextField() {
    Future.delayed(Duration(milliseconds: 50), () {
      toppingGroupController.clear();
      toppingNameController.clear();
      toppingPriceController.clear();
    });
  }

  //SET PRODUCT TEXT FILED VALUE
  setTextFieldValues() async {
    await getAllToppings(id ?? "");
    toppingGroupController.text = groupName??"";
  }

  insertAndUpdateToppingGroup(ToppingsGroup model, String operation) async {
    var toppingsGroup;
    if (operation == "insert") {
      toppingsGroup = {
        db_toppingGroups_toppingGroupId: model.toppingGroupId,
        db_toppingGroups_toppingGroupName: model.toppingGroupName,
        db_workspace_workspaceId: model.workspaceId,
        db_toppingGroups_toppingGroupSync: model.toppingGroupSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.dbInsert(db_toppingGroups, toppingsGroup);
    } else {
      toppingsGroup = {
        db_toppingGroups_toppingGroupName: model.toppingGroupName,
        db_workspace_workspaceId: model.workspaceId,
        db_toppingGroups_toppingGroupSync: model.toppingGroupSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.updateModel(db_toppingGroups, toppingsGroup, db_toppingGroups_toppingGroupId, model.toppingGroupId);
    }
  }

  ///Insert and update the product toppings details
  productToppings(Topping model, String operation) async {
    var topping;
    if (operation == "insert") {
      topping = {
        db_toppings_toppingId: model.toppingId,
        db_toppingGroups_toppingGroupId: model.toppingGroupId,
        db_toppings_toppingName: model.toppingName,
        db_toppings_toppingPrice: model.toppingPrice,
        db_workspace_workspaceId: model.workspaceId,
        db_toppings_toppingSync: model.toppingSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.dbInsert(db_toppings, topping);
    } else {
      topping = {
        db_toppingGroups_toppingGroupId: model.toppingGroupId,
        db_toppings_toppingName: model.toppingName,
        db_toppings_toppingPrice: model.toppingPrice,
        db_workspace_workspaceId: model.workspaceId,
        db_toppings_toppingSync: model.toppingSync,
        db_product_rowStatus: model.rowStatus,
      };
      await dbScript.updateModel(db_toppings, topping, db_toppings_toppingId, model.toppingId);
    }
  }

  @override
  void initState() {
    if (id != null) {
      setTextFieldValues();
    } else {
      isUpdate = true;
    }
    super.initState();
  }

  onSaveToppings() async {
    if (!isLock) {
      isLock = true;
      if (productToppingsList.length > 0) {
        var uuid = new Uuid();
        String toppingGroupId = uuid.v4().toString();
        ToppingsGroup model1 = ToppingsGroup();
        model1.toppingGroupName = toppingGroupController.text;
        model1.workspaceId = MyApp.activeWorkspace.workspaceId;
        model1.toppingGroupSync = 0;
        if (id == null) {
          model1.toppingGroupId = toppingGroupId;
          model1.rowStatus = 0;
          //toppingGroupsDBScript.insertToppingsGroupDetails(model1);
          insertAndUpdateToppingGroup(model1, "insert");
        } else {
          final syncNo = await toppingGroupsDBScript.queryGetProductToppingsSyncNo(id);
          model1.toppingGroupId = id;
          model1.rowStatus = syncNo == 1 ? 1 : 0;
          //toppingGroupsDBScript.updateProductsToppingsGroup(model1);
          insertAndUpdateToppingGroup(model1, "update");
        }
        Topping model = Topping();
        for (Topping obj in productToppingsList) {
          if (obj.toppingId != null) {
            model.toppingId = obj.toppingId;
            model.toppingGroupId = obj.toppingGroupId;
            model.toppingName = obj.toppingName;
            model.toppingPrice = obj.toppingPrice;
            model.workspaceId = MyApp.activeWorkspace.workspaceId;
            model.rowStatus = obj.rowStatus;
            model.toppingSync = 0;
            productToppings(model, "update");
            await toppingsDBscript.updateProductsToppings(model);
          } else {
            if (id == null) {
              model.toppingGroupId = toppingGroupId;
            } else {
              model.toppingGroupId = id;
            }
            String toppingId = uuid.v4().toString();
            model.toppingId = toppingId;
            model.toppingName = obj.toppingName;
            model.toppingPrice = obj.toppingPrice;
            model.workspaceId = MyApp.activeWorkspace.workspaceId;
            model.toppingSync = 0;
            model.rowStatus = obj.rowStatus;
            productToppings(model, "insert");
            //await toppingsDBscript.insertToppingsDetails(model);
          }
        }
        setState(() {
          clearTextField();
        });
        await fnUtilities.getAllToppings();
        await fnUtilities.getAllToppingsGroup();
        Navigator.pop(context);
      } else {
        showToast("Please add toppings");
      }
      isLock = false;
    }
  }

  onSubmitToppings() async {
    if (toppingNameController.text != '' && toppingPriceController.text != '') {
      Topping model = Topping();
      model.toppingName = toppingNameController.text;
      model.toppingPrice = toppingPriceController.text;
      model.rowStatus = 0;
      if (productToppingsList.length != 0) {
        if (unitIndex == null) {
          productToppingsList.add(model);
        } else {
          if (productToppingsList[unitIndex??0].toppingId != null) {
            productToppingsList[unitIndex??0].rowStatus = 1;
          }
          productToppingsList[unitIndex??0].toppingName = toppingNameController.text;
          productToppingsList[unitIndex??0].toppingPrice = toppingPriceController.text;
          unitIndex = null;
        }
      } else {
        productToppingsList.add(model);
        unitIndex = null;
      }
      isToppingIsEmpty = false;
      isPriceIsEmpty = false;
      toppingNameController.clear();
      toppingPriceController.clear();
      FocusScope.of(context).requestFocus(topping);
    } else {
      setState(() {
        isToppingIsEmpty = toppingNameController.text.isEmpty ? true : false;
        isPriceIsEmpty = toppingPriceController.text.isEmpty ? true : false;
      });
    }
  }

  //EDIT WIDGET
  Widget editWidget() {
    return Padding(
      padding: EdgeInsets.all(5),
      child: IconButton(
        iconSize: 25,
        icon: Icon(Icons.edit),
        onPressed: () {
          setState(() {
            isUpdate = true;
          });
        },
        color: Colors.white,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    // height = 200.0;
    return Scaffold(
      appBar: BaseAppBar(
        title: text(id == null ? "Add Toppings" : "Update Toppings", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: id != null ? <Widget>[editWidget()] : null,
      ),
      body: Container(
        height: height,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 15.0, vertical: 10.0),
          child: SingleChildScrollView(
            child: Form(
              key: validator?.formkey,
              child: Column(
                children: <Widget>[
                  SizedBox(height: 10),
                  Container(
                    width: double.maxFinite,
                    child: EditText(
                      text: "Topping Group",
                      isPassword: false,
                      mController: toppingGroupController,
                      validator: validator?.validateTextField ?? false,
                      inputAction: TextInputAction.next,
                      enable: isUpdate,
                    ),
                  ),
                  SizedBox(height: 10),
                  Column(
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.only(bottom: 5),
                            width: width / 2.5,
                            child: EditText(
                              text: "Topping Name",
                              isPassword: false,
                              mController: toppingNameController,
                              errorText: isToppingIsEmpty ? "This field is required" : null,
                              focus: topping,
                              inputType: TextInputType.text,
                              inputAction: TextInputAction.next,
                              enable: isUpdate,
                            ),
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          Expanded(
                            child: Container(
                              padding: EdgeInsets.only(bottom: 5),
                              width: width / 3.3,
                              child: EditText(
                                text: "Price",
                                isPassword: false,
                                mController: toppingPriceController,
                                inputType: TextInputType.number,
                                inputFormate: [
                                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                                ],
                                errorText: isPriceIsEmpty ? "This field is required" : null,
                                inputAction: TextInputAction.done,
                                enable: isUpdate,
                                submit: (submit) {
                                  if (isUpdate) {
                                    onSubmitToppings();
                                  }
                                },
                              ),
                            ),
                          ),
                          SizedBox(
                            width: 5,
                          ),
                          InkWell(
                            onTap: () {
                              if (isUpdate) {
                                onSubmitToppings();
                              }
                            },
                            child: Container(padding: EdgeInsets.symmetric(horizontal: 20, vertical: 13), child: unitIndex == null ? Icon(Icons.add) : Icon(Icons.update)),
                          )
                        ],
                      ),
                      Row(
                        children: [
                          productToppingsList.length != 0
                              ? SizedBox(
                                  // width: width * 0.97,
                                  width: width > tabletWidth ? width * 0.97 : width * 0.88,
                                  child: SingleChildScrollView(
                                    child: ListView.builder(
                                        itemCount: productToppingsList.length,
                                        scrollDirection: Axis.vertical,
                                        physics: ScrollPhysics(),
                                        shrinkWrap: true,
                                        itemBuilder: (BuildContext context, int index) {
                                          var item = productToppingsList[index];
                                          return productToppingsList[index].rowStatus != 2
                                              ? Container(
                                                  height: 40,
                                                  decoration: BoxDecoration(
                                                    border: Border(
                                                      bottom: BorderSide(
                                                        color: Colors.grey,
                                                        width: 0.5,
                                                      ),
                                                    ),
                                                  ),
                                                  child: Row(
                                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                                    children: [
                                                      SizedBox(
                                                        width: width * 0.5882,
                                                        child: Row(
                                                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                                          children: [
                                                            Container(
                                                              width: width * 0.35174,
                                                              alignment: Alignment.centerLeft,
                                                              child: TextWidget(item.toppingName ?? "",
                                                                  textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                                                            ),
                                                            Container(
                                                              width: width * 0.2174,
                                                              alignment: Alignment.centerRight,
                                                              child: TextWidget(item.toppingPrice ?? "",
                                                                  textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                                      Row(
                                                        children: [
                                                          IconButton(
                                                              onPressed: () {
                                                                if (!isLock) {
                                                                  isLock = true;
                                                                  setState(() {
                                                                    if (isUpdate) {
                                                                      toppingNameController.text = item.toppingName ?? "";
                                                                      toppingPriceController.text = item.toppingPrice ?? "";
                                                                      unitIndex = index;
                                                                    }
                                                                  });
                                                                  isLock = false;
                                                                }
                                                              },
                                                              icon: Icon(Icons.edit)),
                                                          SizedBox(
                                                            width: 5,
                                                          ),
                                                          IconButton(
                                                              onPressed: () {
                                                                if (!isLock) {
                                                                  isLock = true;
                                                                  setState(() {
                                                                    if (isUpdate) {
                                                                      if (productToppingsList[index].toppingId != null) {
                                                                        productToppingsList[index].rowStatus = 2;
                                                                      } else {
                                                                        productToppingsList.removeAt(index);
                                                                      }
                                                                    }
                                                                  });
                                                                  isLock = false;
                                                                }
                                                              },
                                                              icon: Icon(Icons.delete))
                                                        ],
                                                      ),
                                                    ],
                                                  ),
                                                )
                                              : Container();
                                        }),
                                  ),
                                )
                              : Container(),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  GestureDetector(
                    onTap: () {
                      validator?.validate();
                      if (validator?.validate() ?? false) {
                        if (isUpdate) {
                          onSaveToppings();
                        }
                      }
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 50,
                      width: width / 1,
                      child: TextWidget(id == null ? "Add Toppings" : "Update Toppings ", textColor: secondaryTextColor, isCentered: true),
                      decoration: boxDecoration(bgColor: !isUpdate ? grey : buttonThemeColor, radius: 8.0),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
