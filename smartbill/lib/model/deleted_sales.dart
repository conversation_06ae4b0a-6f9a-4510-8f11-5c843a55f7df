// To parse this JSON data, do
//
//     final deletedSales = deletedSalesFromJson(jsonString);

import 'dart:convert';

List<DeletedSales> deletedSalesFromJson(String str) => List<DeletedSales>.from(
    json.decode(str).map((x) => DeletedSales.fromJson(x)));

String deletedSalesToJson(List<DeletedSales> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DeletedSales {
  DeletedSales({
    this.salesId,
    this.status,
    this.message,
    this.result,
  });

  String? salesId;
  int? status;
  String? message;
  int? result;

  DeletedSales.fromMapForDeletedSales(Map<String, dynamic> map) {
    salesId = map['removedReportsId'];
  }
  factory DeletedSales.fromJson(Map<String, dynamic> json) => DeletedSales(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : json["result"],
      );

  Map<String, dynamic> toJson() => {
        "salesID": salesId == null ? null : salesId,
      };
}
