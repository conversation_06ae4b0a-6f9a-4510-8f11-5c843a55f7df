// To parse this JSON data, do
//
//     final getSubscriptionPlans = getSubscriptionPlansFromJson(jsonString);

import 'dart:convert';

GetSubscriptionPlans getSubscriptionPlansFromJson(String str) =>
    GetSubscriptionPlans.fromJson(json.decode(str));

String getSubscriptionPlansToJson(GetSubscriptionPlans data) =>
    json.encode(data.toJson());

class GetSubscriptionPlans {
  GetSubscriptionPlans({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<SubscriptionPlans>? result;

  factory GetSubscriptionPlans.fromJson(Map<String, dynamic> json) =>
      GetSubscriptionPlans(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<SubscriptionPlans>.from(
                json["result"].map((x) => SubscriptionPlans.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class SubscriptionPlans {
  SubscriptionPlans({
    this.id,
    this.name,
    this.description,
    this.validity,
    this.priceText1,
    this.priceText2,
    this.amount,
    this.createdDate,
    this.moidfiedDate,
  });

  int? id;
  String? name;
  String? description;
  int? validity;
  String? priceText1;
  String? priceText2;
  double? amount;
  dynamic createdDate;
  dynamic moidfiedDate;

  factory SubscriptionPlans.fromJson(Map<String, dynamic> json) =>
      SubscriptionPlans(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
        description: json["description"] == null ? null : json["description"],
        validity: json["validity"] == null ? null : json["validity"],
        priceText1: json["priceText1"] == null ? null : json["priceText1"],
        priceText2: json["priceText2"] == null ? null : json["priceText2"],
        amount: json["amount"] == null ? null : json["amount"],
        createdDate: json["createdDate"],
        moidfiedDate: json["moidfiedDate"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "name": name == null ? null : name,
        "description": description == null ? null : description,
        "validity": validity == null ? null : validity,
        "priceText1": priceText1 == null ? null : priceText1,
        "priceText2": priceText2 == null ? null : priceText2,
        "amount": amount == null ? null : amount,
        "createdDate": createdDate,
        "moidfiedDate": moidfiedDate,
      };
}
