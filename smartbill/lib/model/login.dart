// class T5Category {
//   var name = "";
//   Color color;

//   var icon = "";
// }

// class T5Slider {
//   var image = "";
//   var balance = "";
//   var accountNo = "";
// }

// class T5Contact {
//   var img;
//   var name;
//   var isOnline = false;
//   var subject;
//   var contactno;
// }

// class LearnerCategoryModel {
//   var img = "";
//   var name = "";
// }

// class LearnerFeaturedModel {
//   var img = "";
//   var name = "";
//   var price = "";
//   var strikePrice = "";
//   var type;
// }

class Login {
  String? userName;
  String? password;
  Login({this.userName, this.password});
  factory Login.fromJSON(Map<String, dynamic> jsonMap) {
    return Login(
      userName: jsonMap['username'] != null ? jsonMap['username'] : '',
      password: jsonMap['password'] != null ? jsonMap['password'] : '',
    );
  }
  Map toMap() {
    var map = new Map<String, dynamic>();
    map["username"] = userName;
    map["password"] = password;
    return map;
  }

  @override
  String toString() {
    var map = this.toMap();
    return map.toString();
  }
}

// class Plans {
//   int id;
//   String planName;
//   String amount;
//   String validityDays;
//   Plans({this.id, this.planName, this.amount, this.validityDays});
// }

// class Applications {
//   String name;
//   int id;
//   String price;

//   Applications(this.name, this.id, this.price);
// }
