class PaymentCategoryModel {
  int? status;
  String? message;
  List<PaymentCategory>? result;

  PaymentCategoryModel({this.status, this.message, this.result});

  PaymentCategoryModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['result'] != null) {
      result = <PaymentCategory>[];
      json['result'].forEach((v) {
        result?.add(new PaymentCategory.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

// class PaymentCategory {
//   int id;
//   String paymentCategoryName;

//   PaymentCategory({this.id, this.paymentCategoryName});

//   PaymentCategory.fromJson(Map<String, dynamic> json) {
//     id = json['id'];
//     paymentCategoryName = json['paymentCategoryName'];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['id'] = this.id;
//     data['paymentCategoryName'] = this.paymentCategoryName;
//     return data;
//   }
// }

class PaymentCategory {
  int? id;
  int? paymentCategoryID; // Added field
  String? paymentCategoryName;
  int? status; // Added field

  PaymentCategory({this.id, this.paymentCategoryID, this.paymentCategoryName, this.status});

  PaymentCategory.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    paymentCategoryID = json['paymentCategoryID']; // Update field name
    paymentCategoryName = json['paymentCategoryName'];
    status = json['status']; // Update field name
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['paymentCategoryID'] = this.paymentCategoryID; // Update field name
    data['paymentCategoryName'] = this.paymentCategoryName;
    data['status'] = this.status; // Update field name
    return data;
  }
}
