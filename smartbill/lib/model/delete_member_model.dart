class DeleteMember {
  String? workspaceId;
  String? userName;
  int? status;
  String? message;

  DeleteMember({this.userName, this.workspaceId, this.status, this.message});

  factory DeleteMember.fromJson(Map<String, dynamic> json) => DeleteMember(
        userName: json["userName"] == null ? null : json["userName"],
        workspaceId: json["workspaceId"] == null ? null : json["workspaceId"],
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
      );

  Map<String, dynamic> toJson() => {
        "userName": userName == null ? null : userName,
        "workspaceId": workspaceId == null ? null : workspaceId,
      };
}
