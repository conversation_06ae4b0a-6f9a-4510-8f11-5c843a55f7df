// To parse this JSON data, do
//
//     final registerResponse = registerResponseFrom<PERSON>son(jsonString);

import 'dart:convert';

RegisterResponse registerResponseFromJson(String str) =>
    RegisterResponse.fromJson(json.decode(str));

String registerResponseTo<PERSON>son(RegisterResponse data) =>
    json.encode(data.toJson());

class RegisterResponse {
  RegisterResponse({
    this.status,
    this.message,
    this.response,
  });

  int? status;
  String? message;
  Response? response;

  factory RegisterResponse.fromJson(Map<String, dynamic> json) =>
      RegisterResponse(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        response: json["response"] == null
            ? null
            : Response.fromJson(json["response"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "response": response == null ? null : response?.toJson(),
      };
}

class Response {
  Response({
    this.username,
    this.status,
    this.message,
  });

  String? username;
  int? status;
  String? message;

  factory Response.fromJson(Map<String, dynamic> json) => Response(
        username: json["username"] == null ? null : json["username"],
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
      );

  Map<String, dynamic> toJson() => {
        "username": username == null ? null : username,
        "status": status == null ? null : status,
        "message": message == null ? null : message,
      };
}
