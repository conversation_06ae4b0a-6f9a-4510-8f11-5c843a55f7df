import 'package:smartbill/utils/dbutils/db_strings.dart';

class SalesType {
  int? id;
  String? salesTypeID;
  String? salesID;
  String? salesType;
  String? amount;
  String? customerID;
  String? paymentTypeID;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  //temp
  String? balance;

  SalesType(
      {this.id,
      this.salesTypeID,
      this.salesID,
      this.salesType,
      this.customerID,
      this.paymentTypeID,
      this.createdDate,
      this.amount,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      db_salesType_ID : id,
      db_salesType_salesTypeID : salesTypeID,
      db_salesType_FK_SalesID: salesID,
      db_salesType_Type: salesType,
      db_salesType_FK_CustomerID : customerID,
      db_salesType_FK_PaymentTypeID: paymentTypeID,
      db_common_createdDate: createdDate,
      db_salesType_Amount: amount,
      db_product_rowStatus: rowStatus,
      db_common_syncStatus: syncStatus,
      db_workspace_workspaceId: workspaceID,
    };
  }

  SalesType.fromMap(Map<String, dynamic> map) {
    id = map['ID'];
    salesTypeID = map['SalesTypeID'];
    salesID = map['FK_SalesID'];
    salesType = map['SalesType'];
    customerID = map['FK_CustomerID'];
    paymentTypeID = map['FK_PaymentTypeID'];
    createdDate = map['CreatedDate'];
    amount = map['Amount'];
    rowStatus = map['rowStatus'];
    syncStatus = map['SyncStatus'];
    workspaceID = map['workspaceId'];
  }

  factory SalesType.fromJson(Map<String, dynamic> json) => SalesType(
        id: json["id"],
        salesTypeID: json["salesTypeID"],
        salesID: json["salesID"],
        salesType: json["salesType"],
        customerID: json["customerID"],
        paymentTypeID: json["paymentTypeID"],
        createdDate: json["createdDate"],
        amount: json["amount"] != null ? json["amount"].toString() : null,
        rowStatus: json["status"],
        workspaceID: json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "salesTypeID": salesTypeID,
        "salesID": salesID,
        "salesType": salesType,
        "customerID": customerID,
        "paymentTypeID": paymentTypeID,
        "createdDate": createdDate,
        "amount": amount,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}
