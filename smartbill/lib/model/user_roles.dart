// To parse this JSON data, do
//
//     final getRoles = getRolesFromJson(jsonString);

import 'dart:convert';

GetRoles getRolesFromJson(String str) => GetRoles.fromJson(json.decode(str));

String getRolesToJson(GetRoles data) => json.encode(data.toJson());

class GetRoles {
  GetRoles({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Roles>? result;

  factory GetRoles.fromJson(Map<String, dynamic> json) => GetRoles(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<Roles>.from(json["result"].map((x) => Roles.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class Roles {
  Roles({
    this.id,
    this.name,
  });

  int? id;
  String? name;

  factory Roles.fromJson(Map<String, dynamic> json) => Roles(
        id: json["id"] == null ? null : json["id"],
        name: json["name"] == null ? null : json["name"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "name": name == null ? null : name,
      };
}
