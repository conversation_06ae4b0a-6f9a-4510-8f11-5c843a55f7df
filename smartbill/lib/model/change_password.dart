import 'dart:convert';

ChangePassword changePasswordFromJson(String str) =>
    ChangePassword.fromJson(json.decode(str));

String changePasswordToMap(ChangePassword data) => json.encode(data.toMap());

class ChangePassword {
  ChangePassword({
    this.message,
    this.id,
    this.oldPassword,
    this.newPassword,
  });

  String? oldPassword;
  String? newPassword;
  String? message;
  int? id;

  factory ChangePassword.fromJson(Map<String, dynamic> json) => ChangePassword(
        message: json["message"] != null ? json["message"] : "",
        id: json["id"],
      );
  Map<String, dynamic> toMap() => {
        "oldPassword": oldPassword,
        "newPassword": newPassword,
      };
}
