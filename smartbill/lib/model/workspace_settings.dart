// To parse this JSON data, do
//
//     final workspaceSettings = workspaceSettingsFromJson(jsonString);

import 'dart:convert';
import '../utils/dbutils/db_strings.dart';

AddWorkspaceSettings addWorkspaceSettingsFromJson(String str) =>
    AddWorkspaceSettings.fromJson(json.decode(str));

String addWorkspaceSettingsToJson(AddWorkspaceSettings data) =>
    json.encode(data.toJson());

class AddWorkspaceSettings {
  AddWorkspaceSettings({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<WorkspaceSettings>? result;

  factory AddWorkspaceSettings.fromJson(Map<String, dynamic> json) =>
      AddWorkspaceSettings(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<WorkspaceSettings>.from(
                json["result"].map((x) => WorkspaceSettings.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

List<WorkspaceSettings> workspaceSettingsFromJson(String str) =>
    List<WorkspaceSettings>.from(
        json.decode(str).map((x) => WorkspaceSettings.fromJson(x)));

String workspaceSettingsToJson(List<WorkspaceSettings> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class WorkspaceSettings {
  WorkspaceSettings(
      {this.settingKey,
      this.settingValue,
      this.workspaceId,
      this.userName,
      this.userId,
      this.sync});

  String? settingKey;
  String? settingValue;
  String? workspaceId;
  String? userName;
  int? sync;
  int? userId;

  factory WorkspaceSettings.fromJson(Map<String, dynamic> json) =>
      WorkspaceSettings(
        settingKey: json["keyName"] == null ? null : json["keyName"],
        settingValue: json["keyValue"] == null ? null : json["keyValue"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        userId: json["userID"] == null ? null : json["userID"],
      );

  Map<String, dynamic> toJson() => {
        "keyName": settingKey == null ? null : settingKey,
        "keyValue": settingValue == null ? null : settingValue,
        "workspaceID": workspaceId == null ? null : workspaceId,
      };
  Map<String, dynamic> toMap() {
    return {
      db_workspace_workspaceId: workspaceId,
      db_workspace_userName: userName,
      db_workSpacesSettings_settingKey: settingKey,
      db_workSpacesSettings_settingValue: settingValue,
      db_workSpacesSettings_settingSync: sync,
    };
  }

  WorkspaceSettings.fromMap(Map<String, dynamic> map) {
    workspaceId = map['workspaceId'];
    userName = map['userName'];
    settingKey = map['settingKey'];
    settingValue = map['settingValue'];
    sync = map['settingSync'];
  }

  static Map<String, dynamic> toMap1(WorkspaceSettings device) => {
        "settingKey": device.settingKey == null ? null : device.settingKey,
        "settingValue":
            device.settingValue == null ? null : device.settingValue,
      };

  static String encode(List<WorkspaceSettings> device) => json.encode(
        device
            .map<Map<String, dynamic>>(
                (device) => WorkspaceSettings.toMap1(device))
            .toList(),
      );

  static List<WorkspaceSettings> decode(String device) =>
      (json.decode(device) as List<dynamic>)
          .map<WorkspaceSettings>((item) => WorkspaceSettings.fromMap(item))
          .toList();
}
