import 'dart:convert';

import '../utils/dbutils/db_strings.dart';

class Payment {
  int? id;
  int? paymentId;
  String? paymentName;
  String? paymentNetworkImage;
  String? paymentImage;
  int? paymentIsActive;
  DateTime? createdOn;
  dynamic modifiedOn;
  String? workspaceId;
  Payment(
      {this.id,
      this.paymentId,
      this.paymentName,
      this.paymentNetworkImage,
      this.paymentImage,
      this.paymentIsActive,
      this.createdOn,
      this.modifiedOn,
      this.workspaceId});

  Map<String, dynamic> toMap() {
    return {
      db_payment_paymentId: paymentId,
      db_payment_paymentName: paymentName,
      db_payment_paymentNetworkImage: paymentNetworkImage,
      db_payment_paymentImage: paymentImage,
      db_payment_paymentIsActive: paymentIsActive,
      db_workspace_workspaceId: workspaceId,
    };
  }

  Payment.fromMap(Map<String, dynamic> map) {
    id = map['paymentDetailsId'];
    paymentId = map['paymentId'];
    paymentName = map['paymentName'];
    paymentNetworkImage = map['paymentNetworkImage'];
    paymentImage = map['paymentImage'];
    paymentIsActive = map['paymentIsActive'];
    workspaceId = map['workspaceId'];
  }

  factory Payment.fromJson(Map<String, dynamic> json) => Payment(
        paymentId: json["id"],
        paymentName: json["payment"],
        paymentNetworkImage: json["paymentImage"],
        createdOn: DateTime.parse(json["createdOn"]),
        modifiedOn: json["modifiedOn"],
      );

  Map<String, dynamic> toJson() => {
        "id": paymentId,
        "payment": paymentName,
        "paymentImage": paymentNetworkImage,
        "createdOn": createdOn?.toIso8601String(),
        "modifiedOn": modifiedOn,
      };
}

GetPayment getPaymentFromJson(String str) =>
    GetPayment.fromJson(json.decode(str));

String getPaymentToJson(GetPayment data) => json.encode(data.toJson());

class GetPayment {
  GetPayment({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Payment>? result;

  factory GetPayment.fromJson(Map<String, dynamic> json) => GetPayment(
        status: json["status"],
        message: json["message"],
        result:
            List<Payment>.from(json["result"].map((x) => Payment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}
