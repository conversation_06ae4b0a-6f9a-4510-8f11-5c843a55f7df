class MasterTableModel{
  int? id;
  String? workspaceID;
  String? key;
  String? value;
  int? masterTableSync;

  MasterTableModel({this.id,this.workspaceID, this.key, this.value, this.masterTableSync});

  MasterTableModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    workspaceID = json['workspaceID'];
    key = json['key'];
    value = json['value'];
    masterTableSync = json['masterTableSync'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['workspaceID'] = this.workspaceID;
    data['key'] = this.key;
    data['value'] = this.value;
    data['masterTableSync'] = this.masterTableSync;
    return data;
  }
}
