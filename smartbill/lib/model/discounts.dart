import '../utils/dbutils/db_strings.dart';

class Discounts {
  int? id;
  String? discountID;
  String? couponName;
  String? mode;
  String? formula;
  String? categoryID;
  String? createdDate;
  String? createdBy;
  int? discountActive;
  String? workspaceID;
  int? rowStatus;
  int? discountSync;
  bool? isActive;

  //formula model
  String? type;
  String? discountOn;
  String? discount;
  String? minDiscount;
  String? maxDiscount;
  DateTime? fromDate;
  DateTime? toDate;

  //for assign value
  String? discountAmount;

  Discounts(
      {this.id,
      this.discountID,
      this.couponName,
      this.mode,
      this.formula,
      this.categoryID,
      this.createdDate,
      this.createdBy,
      this.discountActive,
      this.rowStatus,
      this.discountSync,
      this.workspaceID,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      db_discount_categoryID: discountID,
      db_discount_couponName: couponName,
      db_discount_mode: mode,
      db_discount_formula: formula,
      db_discount_createdDate: createdDate,
      db_discount_createdBy: createdBy,
      db_discount_discountActive: discountActive,
      db_discount_discountSync: discountSync,
      db_product_rowStatus: rowStatus,
      db_workspace_workspaceId: workspaceID,
    };
  }

  Discounts.fromMap(Map<String, dynamic> map) {
    id = map['id'];
    discountID = map['discountID'];
    couponName = map['couponName'];
    mode = map['mode'];
    formula = map['formula'];
    categoryID = map['categoryID'];
    createdDate = map['createdDate'];
    createdBy = map['createdBy'];
    discountActive = map['discountActive'];
    rowStatus = map['rowStatus'];
    discountSync = map['discountSync'];
    workspaceID = map['workspaceId'];
  }

  factory Discounts.fromJson(Map<String, dynamic> json) => Discounts(
        discountID: json["discountID"],
        couponName: json["couponName"],
        mode: json["mode"],
        formula: json["formula"],
        categoryID: json["categoryID"],
        createdDate: json["createdDate"],
        createdBy: json["createdBy"],
        discountActive: json["discountActive"] == true ? 1 : 0,
        isActive: json["isActive"],
        workspaceID: json["workspaceID"],
      );

  Map<String, dynamic> toJson() => {
        "discountID": discountID,
        "couponName": couponName,
        "mode": mode,
        "formula": formula,
        "categoryID": categoryID,
        "createdDate": createdDate,
        "createdBy": createdBy,
        "discountActive": discountActive,
        "status": rowStatus,
        "workspaceId": workspaceID,
      };
}
