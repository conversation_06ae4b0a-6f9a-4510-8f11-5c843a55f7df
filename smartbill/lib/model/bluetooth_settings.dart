class PrinterSettingsDetails {
  int? id;
  String? printerName;
  String? printerAddress;
  int? size;
  String? format;
  int? feed;
  bool? paperCut;
  bool? multiPrint;
  String? printContent;
  String? mode;

  PrinterSettingsDetails(
      {this.id,
      this.printerName,
      this.printerAddress,
      this.size,
      this.format,
      this.feed,
      this.paperCut,
      this.multiPrint,
      this.printContent,
      this.mode});

  factory PrinterSettingsDetails.fromJson(Map<String, dynamic> json) =>
      PrinterSettingsDetails(
        id: json["id"],
        printerName: json["printerName"],
        printerAddress: json["printerAddress"],
        size: json["size"],
        format: json["format"],
        paperCut: json["paperCut"],
        multiPrint: json["multiPrint"],
        printContent: json["printContent"],
        mode: json["mode"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "printerName": printerName,
        "printerAddress": printerAddress,
        "size": size,
        "format": format,
        "paperCut": paperCut,
        "printContent": printContent,
        "mode": mode,
      };
}
