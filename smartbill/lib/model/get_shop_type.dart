class GetShopType {
  int? status;
  String? message;
  List<ShopTypeList>? result;

  GetShopType({this.status, this.message, this.result});

  GetShopType.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['result'] != null) {
      result = <ShopTypeList>[];
      json['result'].forEach((v) {
        result?.add(new ShopTypeList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ShopTypeList {
  int? id;
  String? shopType;
  String? imageUrl;

  ShopTypeList({this.id, this.shopType, this.imageUrl});

  ShopTypeList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    shopType = json['shopType'];
    imageUrl = json['imageUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['shopType'] = this.shopType;
    data['imageUrl'] = this.imageUrl;
    return data;
  }
}
