import 'dart:core';
import 'dart:io';
import 'package:smartbill/screens/products/add_product_page.dart';
import 'dart:convert';
import '../utils/dbutils/db_strings.dart';

List<Products> productsFromJson(String str) => List<Products>.from(json.decode(str).map((x) => Products.fromJson(x)));

String productsToMap(List<Products> data) => json.encode(List<dynamic>.from(data.map((x) => x.toMap())));

class Products {
  int? id;
  String? productId;
  int? productSaleId;
  String? name;
  String? image;
  String? price;
  String? mrp;
  String? productKeyName;
  String? toppingGroupId;
  int? priceType;
  String? cGst;
  String? sGst;
  int? sync;
  String? workspaceId;
  int? rowStatus;
  int? status;
  String? message;
  bool? isActive;
  String? productDate;
  int? dynamicQuantity;
  double? count;
  int? enableInventory;
  File? imagePath;
  String? keyID;
  String? parcelAmount;
  ProductFor? productFor;

  Products(
      {this.id,
      this.productId,
      this.name,
      this.price,
      this.mrp,
      this.productKeyName,
      this.toppingGroupId,
      this.priceType,
      this.cGst,
      this.sGst,
      this.image,
      this.sync,
      this.status,
      this.message,
      this.workspaceId,
      this.rowStatus,
      this.isActive,
      this.productDate,
      this.productSaleId,
      this.dynamicQuantity,
      this.count,
      this.enableInventory,
      this.imagePath,
      this.keyID,
      this.parcelAmount,
      this.productFor});

  Map<String, dynamic> toMap() {
    return {
      db_product_productId: productId,
      db_product_productName: name,
      db_product_productPrice: price,
      db_product_productMrp: mrp,
      db_product_productKeyName: productKeyName,
      db_toppingGroups_toppingGroupId: toppingGroupId,
      db_product_priceType: priceType,
      db_product_productSgst: sGst,
      db_product_productCgst: cGst,
      db_product_productImage: image,
      db_product_productSync: sync,
      db_workspace_workspaceId: workspaceId,
      db_product_rowStatus: rowStatus,
      db_product_productDate: productDate,
      db_product_dynamicQuantity: dynamicQuantity,
      db_product_productKeywordID: keyID,
      db_product_enableInventory: enableInventory,
      db_product_productFor: productForToString(productFor),
      //DatabaseHelper.productParcel: enableInventory
    };
  }

  Products.fromMap(Map<String, dynamic> map) {
    id = map['productDetailsId'];
    productId = map['productId'];
    productSaleId = map['productSaleId'];
    name = map['productName'];
    price = map['productPrice'];
    mrp = map['productMrp'];
    productKeyName = map['productKeyName'];
    toppingGroupId = map['toppingGroupId'];
    priceType = map['priceType'];
    cGst = map['productCgst'];
    sGst = map['productSgst'];
    image = map['productImage'];
    sync = map['productSync'];
    workspaceId = map['workspaceId'];
    rowStatus = map['rowStatus'];
    productDate = map['productDate'];
    dynamicQuantity = map['dynamicQuantity'];
    enableInventory = map['enableInventory'];
    keyID = map['productKeywordID'];
    parcelAmount = map['parcelAmount'];
    productFor = productForFromString(map['productFor'] ?? '');
  }

  factory Products.fromJson(Map<String, dynamic> json) => Products(
      id: json["id"] == null ? null : json["id"],
      productSaleId: json["productSalesID"] == null ? null : json["productSalesID"],
      productId: json["productID"] == null ? null : json["productID"],
      name: json["productName"] == null ? null : json["productName"],
      price: json["productPrice"] == null ? null : json["productPrice"].toString(),
      image: json["productImage"] == null ? null : json["productImage"],
      cGst: json["cgst"] == null ? null : json["cgst"].toString(),
      sGst: json["sgst"] == null ? null : json["sgst"].toString(),
      mrp: json["mrp"] == null ? null : json["mrp"].toString(),
      productKeyName: json["productKey"] == null ? null : json["productKey"],
      toppingGroupId: json["toppingGroupID"] == null ? null : json["toppingGroupID"],
      priceType: json["priceType"] == null ? null : json["priceType"],
      isActive: json["isActive"] == null ? null : json["isActive"],
      status: json["status"] == null ? null : json["status"],
      message: json["message"] == null ? null : json["message"],
      productDate: json["date"] == null ? null : json["date"],
      dynamicQuantity: json["dynamicQty"] == null ? null : json["dynamicQty"],
      enableInventory: json["enableInventory"] == null || json["enableInventory"] == false ? 0 : 1,
      keyID: json["keywordID"] == null ? null : json["keywordID"],
      parcelAmount: json["parcelAmount"] == null ? null : json["parcelAmount"].toString(),
      productFor: json["productFor"] == null ? null : productForFromString(json['productFor']),
      imagePath: json["imagePath"] == null ? null : json["imagePath"]);
  Map<String, dynamic> toJson() => {
        "productID": productId == null ? null : productId,
        "productSalesID": productSaleId == null ? null : productSaleId,
        "productName": name == null ? null : name,
        "productPrice": price == null ? null : price,
        "productImage": image == null ? null : image,
        "cgst": cGst == null ? null : cGst,
        "sgst": sGst == null ? null : sGst,
        "mrp": mrp == null ? null : mrp,
        "productKey": productKeyName == null ? null : productKeyName,
        "toppingGroupID": toppingGroupId == null ? null : toppingGroupId,
        "priceType": priceType == null ? null : priceType,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "status": rowStatus == null ? null : rowStatus,
        "date": productDate == null ? null : productDate,
        "dynamicQty": dynamicQuantity == null ? null : dynamicQuantity,
        "enableInventory": enableInventory == null || enableInventory == 0 ? false : true,
        "keywordID": keyID == null ? null : keyID,
        "parcelAmount": parcelAmount == null ? null : parcelAmount,
        "productFor": productFor == null ? null : productForToString(productFor),
        "imagePath": imagePath != null ? imagePath : null
      };
}

ProductFor? productForFromString(String value) {
  switch (value) {
    case 'purchase':
      return ProductFor.purchase;
    case 'sale':
      return ProductFor.sale;
    case 'both':
      return ProductFor.both;
    default:
      return null;
  }
}

productForToString(ProductFor? value) {
  return value.toString().split('.').last;
}

List<DeletedProducts> deletedProductsFromJson(String str) => List<DeletedProducts>.from(json.decode(str).map((x) => DeletedProducts.fromJson(x)));

String deletedProductsToJson(List<DeletedProducts> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class DeletedProducts {
  DeletedProducts({
    this.productId,
    this.status,
    this.message,
    this.result,
  });

  String? productId;
  int? status;
  String? message;
  int? result;

  DeletedProducts.fromMapForDeletedProducts(Map<String, dynamic> map) {
    productId = map['removedId'];
  }
  factory DeletedProducts.fromJson(Map<String, dynamic> json) => DeletedProducts(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : json["result"],
      );

  Map<String, dynamic> toJson() => {
        "productID": productId == null ? null : productId,
      };
}

GetProductsListFromApi getProductsListFromApiFromJson(String str) => GetProductsListFromApi.fromJson(json.decode(str));

String getProductsListFromApiToJson(GetProductsListFromApi data) => json.encode(data.toJson());

class GetProductsListFromApi {
  GetProductsListFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Products>? result;

  factory GetProductsListFromApi.fromJson(Map<String, dynamic> json) => GetProductsListFromApi(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : List<Products>.from(json["result"].map((x) => Products.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null ? null : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class ProductKey {
  int? id;
  String? productKeyId;
  String? keywordName;
  String? workspaceId;
  int? keywordSync;
  bool? isActive;
  int? rowStatus;

  ProductKey({this.id, this.productKeyId, this.keywordName, this.workspaceId, this.isActive, this.keywordSync, this.rowStatus});

  Map<String, dynamic> toMap() {
    return {
      db_userPermissions_keywordId: id,
      db_userPermissions_productKeyId: productKeyId,
      db_userPermissions_keywordName: keywordName,
      db_workspace_workspaceId: workspaceId,
      db_userPermissions_keywordSync: keywordSync,
      db_product_rowStatus: rowStatus,
    };
  }

  ProductKey.fromMap(Map<String, dynamic> map) {
    id = map['keywordId'];
    productKeyId = map['productKeyId'];
    keywordName = map['keywordName'];
    workspaceId = map['workspaceId'];
    keywordSync = map['keywordSync'];
    rowStatus = map['rowStatus'];
  }

  factory ProductKey.fromJson(Map<String, dynamic> json) => ProductKey(
        id: json["keywordId"] == null ? null : json["keywordId"],
        productKeyId: json["productKeyID"] == null ? null : json["productKeyID"],
        keywordName: json["keywordName"] == null ? null : json["keywordName"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        keywordSync: json["keywordSync"] == null ? null : json["keywordSync"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "keywordId": id == null ? null : id,
        "productKeyID": productKeyId == null ? null : productKeyId,
        "keywordName": keywordName == null ? null : keywordName,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "keywordSync": keywordSync == null ? null : keywordSync,
        "status": rowStatus == null ? null : rowStatus,
      };
}

class PriceUnitType {
  int? id;
  String? priceTypeId;
  String? productId;
  String? measurement;
  String? measurementPrice;
  String? workspaceId;
  bool? isActive;
  int? measurementSync;
  int? rowStatus;

  PriceUnitType(
      {this.id,
      this.priceTypeId,
      this.productId,
      this.measurement,
      this.measurementPrice,
      this.isActive,
      this.workspaceId,
      this.measurementSync,
      this.rowStatus});

  Map<String, dynamic> toMap() {
    return {
      db_priceTypes_priceId: id,
      db_priceTypes_priceTypeId: priceTypeId,
      db_product_productId: productId,
      db_priceTypes_measurement: measurement,
      db_priceTypes_measurementPrice: measurementPrice,
      db_workspace_workspaceId: workspaceId,
      db_priceTypes_measurementSync: measurementSync,
      db_product_rowStatus: rowStatus,
    };
  }

  PriceUnitType.fromMap(Map<String, dynamic> map) {
    id = map['priceId'];
    priceTypeId = map['priceTypeId'];
    productId = map['productId'];
    measurement = map['measurement'];
    measurementPrice = map['measurementPrice'];
    workspaceId = map['workspaceId'];
    measurementSync = map['measurementSync'];
    rowStatus = map['rowStatus'];
  }

  factory PriceUnitType.fromJson(Map<String, dynamic> json) => PriceUnitType(
        id: json["priceId"] == null ? null : json["priceId"],
        priceTypeId: json["priceTypeID"] == null ? null : json["priceTypeID"],
        productId: json["productID"] == null ? null : json["productID"],
        measurement: json["measurement"] == null ? null : json["measurement"],
        measurementPrice: json["price"] == null ? null : json["price"],
        workspaceId: json["workspaceId"] == null ? null : json["workspaceId"],
        measurementSync: json["measurementSync"] == null ? null : json["measurementSync"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "priceId": id == null ? null : id,
        "priceTypeID": priceTypeId == null ? null : priceTypeId,
        "productID": productId == null ? null : productId,
        "measurement": measurement == null ? null : measurement,
        "price": measurementPrice == null ? null : measurementPrice,
        "workspaceId": workspaceId == null ? null : workspaceId,
        "measurementSync": measurementSync == null ? null : measurementSync,
        "status": rowStatus == null ? null : rowStatus,
      };
}
