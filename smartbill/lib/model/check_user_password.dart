// To parse this JSON data, do
//
//     final checkUserPassword = checkUserPasswordFromJson(jsonString);

import 'dart:convert';

CheckUserPassword checkUserPasswordFromJson(String str) =>
    CheckUserPassword.fromJson(json.decode(str));

String checkUserPasswordToJson(CheckUserPassword data) =>
    json.encode(data.toJson());

class CheckUserPassword {
  CheckUserPassword({
    this.password,
    this.status,
    this.message,
  });

  String? password;
  int? status;
  String? message;

  factory CheckUserPassword.fromJson(Map<String, dynamic> json) =>
      CheckUserPassword(
        password: json["password"] == null ? null : json["password"],
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
      );

  Map<String, dynamic> toJson() => {
        "password": password == null ? null : password,
      };
}
