import '../utils/dbutils/db_strings.dart';

class ShopTable {
  int? id;
  String? shopTableId;
  String? shopTableName;
  String? workspaceId;
  int? shopTableSync;
  int? rowStatus;
  bool? isActive;

  ShopTable(
      {this.id,
      this.shopTableId,
      this.shopTableName,
      this.shopTableSync,
      this.rowStatus,
      this.workspaceId,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      db_shopTables_shopTableAutoId: id,
      db_shopTables_shopTableId: shopTableId,
      db_shopTables_shopTableName: shopTableName,
      db_workspace_workspaceId: workspaceId,
      db_shopTables_shopTableSync: shopTableSync,
      db_product_rowStatus: rowStatus,
    };
  }

  ShopTable.fromMap(Map<String, dynamic> map) {
    id = map['shopTableAutoId'];
    shopTableId = map['shopTableId'];
    shopTableName = map['shopTableName'];
    workspaceId = map['workspaceId'];
    shopTableSync = map['shopTableSync'];
    rowStatus = map['rowStatus'];
  }

  factory ShopTable.fromJson(Map<String, dynamic> json) => ShopTable(
        id: json["shopTableAutoId"] == null ? null : json["shopTableAutoId"],
        shopTableId: json["tableID"] == null ? null : json["tableID"],
        shopTableName: json["tableName"] == null ? null : json["tableName"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        shopTableSync: json["syncID"] == null ? null : json["syncID"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "shopTableAutoId": id == null ? null : id,
        "tableID": shopTableId == null ? null : shopTableId,
        "tableName": shopTableName == null ? null : shopTableName,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "syncID": shopTableSync == null ? null : shopTableSync,
        "status": rowStatus == null ? null : rowStatus,
      };
}
