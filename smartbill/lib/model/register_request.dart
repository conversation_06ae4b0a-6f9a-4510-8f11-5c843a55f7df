// To parse this JSON data, do
//
//     final registerRequest = registerRequestFromJson(jsonString);

import 'dart:convert';

RegisterRequest registerRequestFromJson(String str) =>
    RegisterRequest.fromJson(json.decode(str));

String registerRequestToMap(RegisterRequest data) => json.encode(data.toMap());

class RegisterRequest {
  RegisterRequest({
    this.firstName,
    this.lastName,
    this.username,
    this.password,
    this.mobile,
    this.email,
    this.deviceName,
    this.mobileId,
    this.applicationId,
  });

  String? firstName;
  String? lastName;
  String? username;
  String? password;
  String? mobile;
  String? email;
  String? deviceName;
  String? mobileId;
  int? applicationId;

  factory RegisterRequest.fromJson(Map<String, dynamic> json) =>
      RegisterRequest(
        firstName: json["firstName"],
        lastName: json["lastName"],
        username: json["username"],
        password: json["password"],
        mobile: json["mobile"],
        email: json["email"],
        deviceName: json["deviceName"],
        mobileId: json["mobileID"],
        applicationId: json["applicationID"],
      );

  Map<String, dynamic> toMap() => {
        "firstName": firstName,
        "lastName": lastName,
        "username": username,
        "password": password,
        "mobile": mobile,
        "email": email,
        "deviceName": deviceName,
        "mobileID": mobileId,
        "applicationID": applicationId,
      };
}
