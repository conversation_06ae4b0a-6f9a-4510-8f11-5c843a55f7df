class ActivatedKeys {
  int? id;
  String? key;
  String? workspaceId;
  ActivatedKeys({this.id, this.key, this.workspaceId});
  factory ActivatedKeys.fromJSON(Map<String, dynamic> jsonMap) {
    return ActivatedKeys(
      id: jsonMap['keysId'] != null ? jsonMap['keysId'] : '',
      key: jsonMap['key'] != null ? jsonMap['key'] : '',
    );
  }
  Map toMap() {
    var map = new Map<String, dynamic>();
    map["key"] = key;
    return map;
  }

  ActivatedKeys.fromMap(Map<String, dynamic> map) {
    key = map['key'];
    workspaceId = map['workspaceId'];
  }
}
