// To parse this JSON data, do
//
//     final getPrefixById = getPrefixByIdFromJson(jsonString);

import 'dart:convert';
import '../utils/dbutils/db_strings.dart';

GetPrefixById getPrefixByIdFromJson(String str) =>
    GetPrefixById.fromJson(json.decode(str));

String getPrefixByIdToJson(GetPrefixById data) => json.encode(data.toJson());

class GetPrefixById {
  GetPrefixById({
    this.result,
  });

  Prefix? result;

  factory GetPrefixById.fromJson(Map<String, dynamic> json) => GetPrefixById(
        result: json["result"] == null ? null : Prefix.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "result": result == null ? null : result?.toJson(),
      };
}

class Prefix {
  Prefix({
    this.id,
    this.workspaceId,
    this.userName,
    this.status,
    this.message,
    this.prefix,
  });

  int? id;
  String? workspaceId;
  String? userName;
  int? status;
  String? message;
  String? prefix;

  Map<String, dynamic> toMap() {
    return {
      db_invoicePrefix_prefixId: id,
      db_workspace_workspaceId: workspaceId,
      db_workspace_userName: userName,
      db_invoicePrefix_prefixName: prefix
    };
  }

  Prefix.fromMap(Map<String, dynamic> map) {
    id = map['prefixId'];
    workspaceId = map['workspaceId'];
    userName = map['userName'];
    prefix = map['prefixName'];
  }

  factory Prefix.fromJson(Map<String, dynamic> json) => Prefix(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        prefix: json["prefix"] == null ? null : json["prefix"],
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "prefix": prefix == null ? null : prefix,
      };
}
