// To parse this JSON data, do
//
//     final getPermissions = getPermissionsFromJson(jsonString);

import 'dart:convert';
import '../utils/dbutils/db_strings.dart';

GetPermissions getPermissionsFromJson(String str) =>
    GetPermissions.fromJson(json.decode(str));

String getPermissionsToJson(GetPermissions data) => json.encode(data.toJson());

class GetPermissions {
  GetPermissions({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Permissions>? result;

  factory GetPermissions.fromJson(Map<String, dynamic> json) => GetPermissions(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<Permissions>.from(
                json["result"].map((x) => Permissions.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class Permissions {
  Permissions(
      {this.id,
      this.keyName,
      this.permissionLevelId,
      this.isActive,
      this.roleId,
      this.userName,
      this.workspaceId});

  int? id;
  String? keyName;
  int? permissionLevelId;
  bool? isActive;
  int? roleId;
  String? userName;
  String? workspaceId;

  factory Permissions.fromJson(Map<String, dynamic> json) => Permissions(
        id: json["id"] == null ? null : json["id"],
        keyName: json["keyName"] == null ? null : json["keyName"],
        permissionLevelId: json["permissionLevelID"] == null
            ? null
            : json["permissionLevelID"],
        isActive: json["isActive"] == null ? null : json["isActive"],
        roleId: json["roleID"] == null ? null : json["roleID"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "keyName": keyName == null ? null : keyName,
        "permissionLevelID":
            permissionLevelId == null ? null : permissionLevelId,
        "isActive": isActive == null ? null : isActive,
        "roleID": roleId == null ? null : roleId,
      };

  Map<String, dynamic> toMap() {
    return {
      db_workspace_userName: userName,
      db_workspace_workspaceId: workspaceId,
       db_userPermissions_permissionName: keyName,
       db_userPermissions_permissionValue: permissionLevelId,
    };
  }

  Permissions.fromMap(Map<String, dynamic> map) {
    userName = map['userName'];
    workspaceId = map['workspaceId'];
    keyName = map['permissionName'];
    permissionLevelId = map['permissionValue'];
  }
}
