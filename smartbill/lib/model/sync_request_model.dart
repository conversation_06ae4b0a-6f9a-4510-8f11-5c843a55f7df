// To parse this JSON data, do
//
//     final syncRequest = syncRequestFromJson(jsonString);

import 'dart:convert';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/sales_payment_mapping.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/stocks.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_transactions_model.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/screens/expenses/model/expenses_model.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import '../screens/expenses/model/expense_category_model.dart';
import '../screens/masters/online_platforms/online_platform_model.dart';
import '../screens/salescategory/model/sales_category_model.dart';
import 'payment_type.dart';
import 'reports.dart';
import 'area.dart';
import 'customers.dart';
import 'discounts.dart';

SyncRequest syncRequestFromJson(String str) => SyncRequest.fromJson(json.decode(str));

String syncRequestToJson(SyncRequest data) => json.encode(data.toJson());

class SyncRequest {
  SyncRequest({
    this.lastCommitId,
    this.workspaceId,
    this.products,
    this.productKeywords,
    this.priceTypes,
    this.toppingsGroup,
    this.topping,
    this.sales,
    this.salesTransactions,
    this.workspaceSettings,
    this.shopDetails,
    this.shopTables,
    this.paymentType,
    this.stocksList,
    this.stockLogsList,
    this.discountList,
    this.workspaceUserSettings,
    this.customerList,
    this.areaList,
    this.salesTypeList,
    this.salesPaymentMapping,
    this.salesTransactionStagging,
    this.onlinePlatform,
    this.salesLog,
    this.salesTransactionsLog,
    this.expensesProduct,
    this.expenseCategory,
    this.purchase,
    this.purchaseTransaction,
    this.expenses,
    this.expenseTransaction,
    this.salesCategory,
    this.shiftWiseSales,
    this.advanceOrderNotes,
  });

  String? lastCommitId;
  String? workspaceId;
  List<Products>? products;
  List<ProductKey>? productKeywords;
  List<PriceUnitType>? priceTypes;
  List<ToppingsGroup>? toppingsGroup;
  List<Topping>? topping;
  List<Sales>? sales;
  List<Transactions>? salesTransactions;
  List<WorkspaceSettings>? workspaceSettings;
  List<ShopTable>? shopTables;
  List<OnlinePlatformModel>? onlinePlatform;
  ShopDeatails? shopDetails;
  List<PaymentType>? paymentType;
  List<Stocks>? stocksList;
  List<StockLogs>? stockLogsList;
  List<Discounts>? discountList;
  List<WorkspaceSettings>? workspaceUserSettings;
  List<Customer>? customerList;
  List<Area>? areaList;
  List<SalesType>? salesTypeList;
  List<SalesPaymentMapping>? salesPaymentMapping;
  List<Transactions>? salesTransactionStagging;
  List<Sales>? salesLog;
  List<Transactions>? salesTransactionsLog;
  List<ExpensesProduct>? expensesProduct;
  List<ExpensesCategoryModel>? expenseCategory;
  List<PurchaseModel>? purchase;
  List<PurchaseTransactionsModel>? purchaseTransaction;
  List<ExpensesModel>? expenses;
  List<ExpenseTransactionModel>? expenseTransaction;
  List<SalesCategoryModel>? salesCategory;
  List<ShiftwiseSalesModel>? shiftWiseSales;
  List<AdvanceOrderNoteModel>? advanceOrderNotes;
  factory SyncRequest.fromJson(Map<String, dynamic> json) => SyncRequest(
        lastCommitId: json["lastCommitID"] == null ? null : json["lastCommitID"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        products: json["products"] == null ? null : List<Products>.from(json["products"].map((x) => Products.fromJson(x))),
        productKeywords: json["productKeywords"] == null ? null : List<ProductKey>.from(json["productKeywords"].map((x) => ProductKey.fromJson(x))),
        priceTypes: json["priceTypes"] == null ? null : List<PriceUnitType>.from(json["priceTypes"].map((x) => PriceUnitType.fromJson(x))),
        toppingsGroup: json["toppingsGroup"] == null ? null : List<ToppingsGroup>.from(json["toppingsGroup"].map((x) => ToppingsGroup.fromJson(x))),
        topping: json["toppings"] == null ? null : List<Topping>.from(json["toppings"].map((x) => Topping.fromJson(x))),
        sales: json["sales"] == null ? null : List<Sales>.from(json["sales"].map((x) => Sales.fromJson(x))),
        salesTransactions:
            json["salesTransactions"] == null ? null : List<Transactions>.from(json["salesTransactions"].map((x) => Transactions.fromJson(x))),
        workspaceSettings: json["workspaceSettings"] == null
            ? null
            : List<WorkspaceSettings>.from(json["workspaceSettings"].map((x) => WorkspaceSettings.fromJson(x))),
        shopTables: json["shopTable"] == null ? null : List<ShopTable>.from(json["shopTable"].map((x) => ShopTable.fromJson(x))),
        paymentType: json["paymentType"] == null ? null : List<PaymentType>.from(json["paymentType"].map((x) => PaymentType.fromJson(x))),
        stocksList: json["stocks"] == null ? null : List<Stocks>.from(json["stocks"].map((x) => Stocks.fromJson(x))),
        stockLogsList: json["stockLogs"] == null ? null : List<StockLogs>.from(json["stockLogs"].map((x) => StockLogs.fromJson(x))),
        discountList: json["discount"] == null ? null : List<Discounts>.from(json["discount"].map((x) => Discounts.fromJson(x))),
        workspaceUserSettings: json["workspaceUserSettings"] == null
            ? null
            : List<WorkspaceSettings>.from(json["workspaceUserSettings"].map((x) => WorkspaceSettings.fromJson(x))),
        customerList: json["customer"] == null ? null : List<Customer>.from(json["customer"].map((x) => Customer.fromJson(x))),
        areaList: json["area"] == null ? null : List<Area>.from(json["area"].map((x) => Area.fromJson(x))),
        salesTypeList:
            json["customerTransaction"] == null ? null : List<SalesType>.from(json["customerTransaction"].map((x) => SalesType.fromJson(x))),
        salesTransactionStagging: json["salesTransactionStagging"] == null
            ? null
            : List<Transactions>.from(json["salesTransactionStagging"].map((x) => Transactions.fromJson(x))),
        salesPaymentMapping: json["salesPaymentMapping"] == null
            ? null
            : List<SalesPaymentMapping>.from(json["salesPaymentMapping"].map((x) => SalesPaymentMapping.fromJson(x))),
        onlinePlatform: json["onlinePlatform"] == null
            ? null
            : List<OnlinePlatformModel>.from(json["onlinePlatform"].map((x) => OnlinePlatformModel.fromJson(x))),
        salesLog: json["salesLog"] == null ? null : List<Sales>.from(json["salesLog"].map((x) => Sales.fromJson(x))),
        salesTransactionsLog:
            json["salesTransactionsLog"] == null ? null : List<Transactions>.from(json["salesTransactionsLog"].map((x) => Transactions.fromJson(x))),
        expensesProduct:
            json["expensesProduct"] == null ? null : List<ExpensesProduct>.from(json["expensesProduct"].map((x) => ExpensesProduct.fromJson(x))),
        expenseCategory: json["expenseCategory"] == null
            ? null
            : List<ExpensesCategoryModel>.from(json["expenseCategory"].map((x) => ExpensesCategoryModel.fromJson(x))),
        purchase: json["purchase"] == null ? null : List<PurchaseModel>.from(json["purchase"].map((x) => PurchaseModel.fromJson(x))),
        purchaseTransaction: json["purchaseTransaction"] == null
            ? null
            : List<PurchaseTransactionsModel>.from(json["purchaseTransaction"].map((x) => PurchaseTransactionsModel.fromJson(x))),
        expenses: json["expenses"] == null ? null : List<ExpensesModel>.from(json["expenses"].map((x) => ExpensesModel.fromJson(x))),
        expenseTransaction: json["expenseTransaction"] == null
            ? null
            : List<ExpenseTransactionModel>.from(json["expenseTransaction"].map((x) => ExpenseTransactionModel.fromJson(x))),
        salesCategory:
            json["salesCategory"] == null ? null : List<SalesCategoryModel>.from(json["salesCategory"].map((x) => SalesCategoryModel.fromJson(x))),
        shiftWiseSales: json["shiftWiseSales"] == null
            ? null
            : List<ShiftwiseSalesModel>.from(json["shiftWiseSales"].map((x) => ShiftwiseSalesModel.fromJson(x))),
        advanceOrderNotes: json["advanceOrderNotes"] == null
            ? null
            : List<AdvanceOrderNoteModel>.from(json["advanceOrderNotes"].map((x) => AdvanceOrderNoteModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "lastCommitID": lastCommitId == null ? null : lastCommitId,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "products": products == null ? null : List<dynamic>.from(products!.map((x) => x.toJson())),
        "productKeywords": productKeywords == null ? null : List<dynamic>.from(productKeywords!.map((x) => x.toJson())),
        "priceTypes": priceTypes == null ? null : List<dynamic>.from(priceTypes!.map((x) => x.toJson())),
        "toppingsGroup": toppingsGroup == null ? null : List<dynamic>.from(toppingsGroup!.map((x) => x.toJson())),
        "toppings": topping == null ? null : List<dynamic>.from(topping!.map((x) => x.toJson())),
        "sales": sales == null ? null : List<dynamic>.from(sales!.map((x) => x.toJson())),
        "salesTransactions": salesTransactions == null ? null : List<dynamic>.from(salesTransactions!.map((x) => x.toJson())),
        "workspaceSettings": workspaceSettings == null ? null : List<dynamic>.from(workspaceSettings!.map((x) => x.toJson())),
        "shopDetails": shopDetails == null ? null : shopDetails?.toJson(),
        "shopTable": shopTables == null ? null : List<dynamic>.from(shopTables!.map((x) => x.toJson())),
        "paymentType": paymentType == null ? null : List<dynamic>.from(paymentType!.map((x) => x.toJson())),
        "stocks": stocksList == null ? null : List<dynamic>.from(stocksList!.map((x) => x.toJson())),
        "stockLogs": stockLogsList == null ? null : List<dynamic>.from(stockLogsList!.map((x) => x.toJson())),
        "discount": discountList == null ? null : List<dynamic>.from(discountList!.map((x) => x.toJson())),
        "workspaceUserSettings": workspaceUserSettings == null ? null : List<dynamic>.from(workspaceUserSettings!.map((x) => x.toJson())),
        "customer": customerList == null ? null : List<dynamic>.from(customerList!.map((x) => x.toJson())),
        "area": areaList == null ? null : List<dynamic>.from(areaList!.map((x) => x.toJson())),
        "customerTransaction": salesTypeList == null ? null : List<dynamic>.from(salesTypeList!.map((x) => x.toJson())),
        "salesPaymentMapping": salesPaymentMapping == null ? null : List<dynamic>.from(salesPaymentMapping!.map((x) => x.toJson())),
        "salesTransactionStagging": salesTransactionStagging == null ? null : List<dynamic>.from(salesTransactionStagging!.map((x) => x.toJson())),
        "onlinePlatform": onlinePlatform == null ? null : List<dynamic>.from(onlinePlatform!.map((x) => x.toJson())),
        "salesLog": salesLog == null ? null : List<dynamic>.from(salesLog!.map((x) => x.toJson())),
        "salesTransactionsLog": salesTransactionsLog == null ? null : List<dynamic>.from(salesTransactionsLog!.map((x) => x.toJson())),
        "expensesProduct": expensesProduct == null ? null : List<dynamic>.from(expensesProduct!.map((x) => x.toJson())),
        "expenseCategory": expenseCategory == null ? null : List<dynamic>.from(expenseCategory!.map((x) => x.toJson())),
        "purchase": purchase == null ? null : List<dynamic>.from(purchase!.map((x) => x.toJson())),
        "purchaseTransaction": purchaseTransaction == null ? null : List<dynamic>.from(purchaseTransaction!.map((x) => x.toJson())),
        "expenses": expenses == null ? null : List<dynamic>.from(expenses!.map((x) => x.toJson())),
        "expenseTransaction": expenseTransaction == null ? null : List<dynamic>.from(expenseTransaction!.map((x) => x.toJson())),
        "salesCategory": salesCategory == null ? null : List<dynamic>.from(salesCategory!.map((x) => x.toJson())),
        "shiftWiseSales": shiftWiseSales == null ? null : List<dynamic>.from(shiftWiseSales!.map((x) => x.toJson())),
        "advanceOrderNotes": advanceOrderNotes == null ? null : List<dynamic>.from(advanceOrderNotes!.map((x) => x.toJson())),
      };
}
