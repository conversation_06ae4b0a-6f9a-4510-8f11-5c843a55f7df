import '../utils/dbutils/db_strings.dart';

class Balance {
  int? id;
  String? customerID;
  String? balance;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  Balance(
      {this.id,
      this.customerID,
      this.balance,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      db_balance_ID: id,
      db_balance_FK_CustomerID: customerID,
      db_balance_customerBalance: balance,
      db_product_rowStatus: rowStatus,
      db_common_syncStatus: syncStatus,
      db_workspace_workspaceId: workspaceID
    };
  }

  Balance.fromMap(Map<String, dynamic> map) {
    id = map['ID'];
    customerID = map['FK_CustomerID'];
    balance = map['CustomerBalance'];
    rowStatus = map['rowStatus'];
    syncStatus = map['SyncStatus'];
    workspaceID = map['workspaceId'];
  }

  factory Balance.fromJson(Map<String, dynamic> json) => Balance(
        id: json["id"],
        customerID: json["customerID"],
        balance: json["balance"] != null ? json["balance"].toString() : null,
        rowStatus: json["status"],
        workspaceID: json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerID": customerID,
        "balance": balance,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}
