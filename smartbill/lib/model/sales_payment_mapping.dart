class SalesPaymentMapping {
  int? id;
  String? paymentMappingId;
  String? workspaceId;
  String? salesId;
  String? paymentId;
  int? paymentCatId;
  double? amount;
  int? status;
  int? paymentSync;
  String? paymentName;
  bool? isActive;
  String? shiftId;
  String? shiftName;
  String? createdDate;

  SalesPaymentMapping({
    this.id,
    this.paymentMappingId,
    this.workspaceId,
    this.salesId,
    this.paymentId,
    this.paymentCatId,
    this.amount,
    this.status,
    this.paymentSync,
    this.paymentName,
    this.isActive,
    this.shiftId,
    this.shiftName,
    this.createdDate,
  });

  SalesPaymentMapping.fromJson(Map<String, dynamic> json) {
    id = json["id"];
    paymentMappingId = json["paymentMappingId"];
    workspaceId = json["workspaceId"];
    salesId = json["salesId"];
    paymentId = json["paymentId"];
    amount = double.tryParse(json["paymentAmount"]);
    status = json["rowStatus"];
    paymentSync = json["paymentSync"];
    paymentName = json["paymentName"];
    isActive = json["isActive"];
    shiftId = json["shiftId"];
    shiftName = json["shiftName"];
    createdDate = json["createdDate"];
    
  }
  SalesPaymentMapping.fromJson1(Map<String, dynamic> json) {
    id = json["id"];
    paymentMappingId = json["paymentMappingID"];
    workspaceId = json["workspaceID"];
    salesId = json["salesID"];
    paymentId = json["paymentID"];
    amount = json["amount"];
    status = json["rowStatus"];
    paymentSync = json["paymentSync"];
    paymentName = json["paymentName"];
    isActive = json["isActive"];
    shiftId = json["shiftId"];
    shiftName = json["shiftName"];
    createdDate = json["createdDate"];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> _data = <String, dynamic>{};
    _data["id"] = id;
    _data["paymentMappingID"] = paymentMappingId;
    _data["workspaceID"] = workspaceId;
    _data["salesID"] = salesId;
    _data["paymentID"] = paymentId;
    _data["amount"] = amount;
    _data["status"] = status;
    _data["paymentName"] = paymentName;
    _data["paymentSync"] = paymentSync;
    _data["shiftId"] = shiftId;
    _data["shiftName"] = shiftName;
    _data["createdDate"] = createdDate;

    return _data;
  }
  // List<PaymentMapping> salesPaymentMapping;

  // SalesPaymentMapping({this.salesPaymentMapping});

  // SalesPaymentMapping.fromJson(Map<String, dynamic> json) {
  //   salesPaymentMapping = json["salesPaymentMapping"] == null
  //       ? null
  //       : (json["salesPaymentMapping"] as List)
  //           .map((e) => PaymentMapping.fromJson(e))
  //           .toList();
  // }

  // Map<String, dynamic> toJson() {
  //   final Map<String, dynamic> _data = <String, dynamic>{};
  //   if (salesPaymentMapping != null) {
  //     _data["salesPaymentMapping"] =
  //         salesPaymentMapping?.map((e) => e.toJson()).toList();
  //   }
  //   return _data;
  // }
}

// class PaymentMapping {
//   int id;
//   String paymentMappingId;
//   String workspaceId;
//   String salesId;
//   String paymentId;
//   double amount;
//   int status;
//   int paymentSync;
//   String paymentName;

//   PaymentMapping({
//     this.id,
//     this.paymentMappingId,
//     this.workspaceId,
//     this.salesId,
//     this.paymentId,
//     this.amount,
//     this.status,
//     this.paymentSync,
//     this.paymentName,
//   });

//   PaymentMapping.fromJson(Map<String, dynamic> json) {
//     id = json["id"];
//     paymentMappingId = json["paymentMappingID"];
//     workspaceId = json["workspaceID"];
//     salesId = json["salesID"];
//     paymentId = json["paymentID"];
//     amount = json["amount"];
//     status = json["status"];
//     paymentSync = json["paymentSync"];
//     paymentName = json["paymentName"];
//   }

//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> _data = <String, dynamic>{};
//     _data["id"] = id;
//     _data["paymentMappingID"] = paymentMappingId;
//     _data["workspaceID"] = workspaceId;
//     _data["salesID"] = salesId;
//     _data["paymentID"] = paymentId;
//     _data["amount"] = amount;
//     _data["status"] = status;
//     _data["paymentName"] = paymentName;
//     _data["paymentSync"] = paymentSync;
//     return _data;
//   }
// }
