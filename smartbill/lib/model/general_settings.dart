import '../utils/dbutils/db_strings.dart';

class GeneralSetting {
  GeneralSetting({this.settingKey, this.settingValue});

  String? settingKey;
  String? settingValue;

  factory GeneralSetting.fromJson(Map<String, dynamic> json) => GeneralSetting(
        settingKey: json["keyName"] == null ? null : json["keyName"],
        settingValue: json["keyValue"] == null ? null : json["keyValue"],
      );

  Map<String, dynamic> toJson() => {
        "keyName": settingKey == null ? null : settingKey,
        "keyValue": settingValue == null ? null : settingValue,
      };
  Map<String, dynamic> toMap() {
    return {
      db_workSpacesSettings_settingKey: settingKey,
      db_workSpacesSettings_settingValue: settingValue,
    };
  }

  GeneralSetting.fromMap(Map<String, dynamic> map) {
    settingKey = map['settingKey'];
    settingValue = map['settingValue'];
  }
}
