// To parse this JSON data, do
//
//     final extendValidity = extendValidityFromJson(jsonString);

import 'dart:convert';

ExtendValidity extendValidityFromJson(String str) =>
    ExtendValidity.fromJson(json.decode(str));

String extendValidityToJson(ExtendValidity data) => json.encode(data.toJson());

class ExtendValidity {
  ExtendValidity({
    this.applicationId,
    this.mobileId,
    this.workspaceId,
    this.activationCode,
    this.response,
  });

  int? applicationId;
  String? mobileId;
  String? workspaceId;
  String? activationCode;
  ExtendValidityResponse? response;

  factory ExtendValidity.fromJson(Map<String, dynamic> json) => ExtendValidity(
        applicationId:
            json["applicationID"] == null ? null : json["applicationID"],
        mobileId: json["mobileID"] == null ? null : json["mobileID"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        activationCode:
            json["activationCode"] == null ? null : json["activationCode"],
        response: json["response"] == null
            ? null
            : ExtendValidityResponse.fromJson(json["response"]),
      );

  Map<String, dynamic> toJson() => {
        "applicationID": applicationId == null ? null : applicationId,
        "mobileID": mobileId == null ? null : mobileId,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "activationCode": activationCode == null ? null : activationCode,
        "response": response == null ? null : response?.toJson(),
      };
}

class ExtendValidityResponse {
  ExtendValidityResponse({
    this.workspaceId,
    this.message,
    this.expiryDate,
    this.status,
  });

  String? workspaceId;
  String? message;
  DateTime? expiryDate;
  int? status;

  factory ExtendValidityResponse.fromJson(Map<String, dynamic> json) =>
      ExtendValidityResponse(
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        message: json["message"] == null ? null : json["message"],
        expiryDate: json["expiryDate"] == null
            ? null
            : DateTime.parse(json["expiryDate"]),
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "workspaceID": workspaceId == null ? null : workspaceId,
        "message": message == null ? null : message,
        "expiryDate": expiryDate == null ? null : expiryDate?.toIso8601String(),
        "status": status == null ? null : status,
      };
}
