// To parse this JSON data, do
//
//     final getWorkspaceList = getWorkspaceList<PERSON><PERSON><PERSON><PERSON>(jsonString);
import 'package:smartbill/model/permissions.dart';
import 'dart:convert';
import 'package:smartbill/model/workspace_settings.dart';
import '../utils/dbutils/db_strings.dart';

GetWorkspaceList getWorkspaceList<PERSON>rom<PERSON>son(String str) =>
    GetWorkspaceList.fromJson(json.decode(str));

String getWorkspaceListToJson(GetWorkspaceList data) =>
    json.encode(data.toJson());

class GetWorkspaceList {
  GetWorkspaceList({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<WorkSpace>? result;

  factory GetWorkspaceList.fromJson(Map<String, dynamic> json) =>
      GetWorkspaceList(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<WorkSpace>.from(
                json["result"].map((x) => WorkSpace.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class WorkSpace {
  WorkSpace(
      {this.id,
      this.workspaceId,
      this.workspaceName,
      this.userId,
      this.userName,
      this.isActive,
      this.expiryDate,
      this.workspaceSettings,
      this.roleId,
      this.permissions,
      this.salesCount,
      this.status,
      this.message});

  int? id;
  String? workspaceId;
  String? workspaceName;
  int? userId;
  String? userName;
  bool? isActive;
  String? expiryDate;
  List<WorkspaceSettings>? workspaceSettings;
  int? roleId;
  List<Permissions>? permissions;
  int? salesCount;

  //workspace edit
  int? status;
  String? message;

  Map<String, dynamic> toMap() {
    return {
      db_workspace_workspaceId: workspaceId,
      db_workspace_workspaceName: workspaceName,
      db_workspace_userId: userId,
      db_workspace_userName: userName,
      db_workspace_isActive: isActive,
      db_workspace_expiryDate: expiryDate.toString(),
      db_workspace_roleId: roleId,
    };
  }

  WorkSpace.fromMap(Map<String, dynamic> map) {
    workspaceId = map['workspaceId'];
    workspaceName = map['workspaceName'];
    userId = map['userId'];
    userName = map['userName'];
    isActive = map['isActive'] == 0 ? false : true;
    expiryDate = map["expiryDate"] == null ? null : map["expiryDate"];
    roleId = map["roleID"] == null ? null : map["roleID"];
  }

  factory WorkSpace.fromJson(Map<String, dynamic> json) => WorkSpace(
      id: json["id"] == null ? null : json["id"],
      workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
      workspaceName:
          json["workspaceName"] == null ? null : json["workspaceName"],
      userId: json["userID"] == null ? null : json["userID"],
      userName: json["userName"] == null ? null : json["userName"],
      isActive: json["isActive"] == null ? null : json["isActive"],
      expiryDate: json["expiryDate"] == null ? null : json["expiryDate"],
      workspaceSettings: json["workspaceSettings"] == null
          ? null
          : List<WorkspaceSettings>.from(json["workspaceSettings"]
              .map((x) => WorkspaceSettings.fromJson(x))),
      roleId: json["roleID"] == null ? null : json["roleID"],
      permissions: json["permissions"] == null
          ? null
          : List<Permissions>.from(
              json["permissions"].map((x) => Permissions.fromJson(x))),
      salesCount: json["salesCount"] == null ? null : json["salesCount"],
      status: json["status"] == null ? null : json["status"],
      message: json["message"] == null ? null : json["message"]);

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "workspaceName": workspaceName == null ? null : workspaceName,
        "userName": userName == null ? null : userName,
        "userID": userId == null ? null : userId,
        "isActive": isActive == null ? null : isActive,
        "expiryDate": expiryDate == null ? null : expiryDate,
        "roleID": roleId == null ? null : roleId
      };
}
