class UpdateMember {
  int? roleId;
  int? status;
  String? message;

  UpdateMember({this.roleId, this.status, this.message});

  factory UpdateMember.fromJson(Map<String, dynamic> json) => UpdateMember(
        roleId: json["roleID"] == null ? null : json["roleID"],
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
      );

  Map<String, dynamic> toJson() => {
        "roleID": roleId == null ? null : roleId,
      };
}
