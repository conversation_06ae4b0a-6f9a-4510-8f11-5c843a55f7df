class PrivacyPolicyResponce {
  int? status;
  String? message;
  List<Privacypolicy>? result;

  PrivacyPolicyResponce({this.status, this.message, this.result});

  PrivacyPolicyResponce.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['result'] != null) {
      result = <Privacypolicy>[];
      json['result'].forEach((v) {
        result!.add(Privacypolicy.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Privacypolicy {
  int? id;
  String? keyName;
  String? keyValue;

  Privacypolicy({this.id, this.keyName, this.keyValue});

  Privacypolicy.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    keyName = json['keyName'];
    keyValue = json['keyValue'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['keyName'] = keyName;
    data['keyValue'] = keyValue;
    return data;
  }
}
