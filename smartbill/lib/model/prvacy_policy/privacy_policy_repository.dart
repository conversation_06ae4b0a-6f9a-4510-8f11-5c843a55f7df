import 'dart:convert';

import 'privacy_policy_model.dart';
import 'package:http/http.dart' as http;

import '../../Repositories/environment.dart';

// Future<GetSettings> getGeneralSettingsByKeys(String keys) async {
//   GetSettings responseModel = GetSettings();
//   final response = await apiHelper.commonGet("/api/v1/generalsettings/getgeneralsettingsbykeys?keyName=$keys");
//   if (response != null) {
//     responseModel = GetSettings.fromJson(response);
//   }
//   return responseModel;
// }

Future<PrivacyPolicyResponce> getGeneralSettingsByKeys(String keys) async {
  final response = await http.get(Uri.parse("$baseUrl/api/v1/generalsettings/getgeneralsettingsbykeys?keyName=$keys"));

  if (response.statusCode == 200) {
    // If the server returns a 200 OK response, parse the JSON
    return PrivacyPolicyResponce.fromJson(jsonDecode(response.body));
  } else {
    // If the server did not return a 200 OK response, throw an exception.
    throw Exception('Failed to load settings');
  }
}
