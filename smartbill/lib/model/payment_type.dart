import '../utils/dbutils/db_strings.dart';

class PaymentType {
  int? id;
  String? paymentTypeID;
  String? paymentName;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;
  int? paymentCategoryID;
  PaymentType({
    this.id,
    this.paymentTypeID,
    this.paymentName,
    this.createdDate,
    this.rowStatus,
    this.syncStatus,
    this.workspaceID,
    this.isActive,
    this.paymentCategoryID,
  });

  Map<String, dynamic> toMap() {
    return {
      db_paymentTypes_paymentTypeAutoId: id,
      db_paymentTypes_paymentTypeId: paymentTypeID,
      db_paymentTypes_paymentTypeName: paymentName,
      db_paymentTypes_createdDate: createdDate,
      db_product_rowStatus: rowStatus,
      db_paymentTypes_paymentTypeSync: syncStatus,
      db_workspace_workspaceId: workspaceID,
      db_Payment_category_ID: db_Payment_category_ID
    };
  }

  PaymentType.fromMap(Map<String, dynamic> map) {
    id = map['paymentTypeAutoId'];
    paymentTypeID = map['paymentTypeId'];
    paymentName = map['paymentTypeName'];
    createdDate = map['createdDate'];
    rowStatus = map['rowStatus'];
    syncStatus = map['paymentTypeSync'];
    workspaceID = map['workspaceId'];
    paymentCategoryID = map['paymentCategoryID'];
  }

  factory PaymentType.fromJson(Map<String, dynamic> json) => PaymentType(
      id: json["id"],
      paymentTypeID: json["paymentTypeID"],
      paymentName: json["paymentTypeName"],
      createdDate: json["createdDate"],
      rowStatus: json["status"],
      workspaceID: json["workspaceID"],
      isActive: json["isActive"],
      paymentCategoryID: json["paymentCategoryID"]);

  Map<String, dynamic> toJson() => {
        "id": id,
        "paymentTypeID": paymentTypeID,
        "paymentTypeName": paymentName,
        "createdDate": createdDate,
        "status": rowStatus,
        "workspaceID": workspaceID,
        "paymentCategoryID": paymentCategoryID
      };
}

class PaymentTypeReport {
  String? fromDate;
  String? toDate;
  List<PaymentTypeReportList>? paymentTypeReportList;
  String? totalAmount;
  String? totalQty;

  PaymentTypeReport({this.fromDate, this.toDate, this.paymentTypeReportList, this.totalAmount, this.totalQty});
}

class PaymentTypeReportList {
  String? paymentTypeID;
  String? paymentName;
  String? productId;
  String? productKeywordId;
  String? productKeywordName;
  String? qty;
  String? amount;
  String? name;
  String? price;

  PaymentTypeReportList(
      {this.paymentTypeID,
      this.paymentName,
      this.qty,
      this.amount,
      this.name,
      this.price,
      this.productId,
      this.productKeywordId,
      this.productKeywordName});

  PaymentTypeReportList.fromMap(Map<String, dynamic> map) {
    paymentTypeID = map['salesPaymentType'];
    paymentName = map['paymentTypeName'];
    qty = map['quantity'].toString();
    amount = map['amount'].toString();
    name = map['salesProductName'];
    price = map['price'];
    productId = map['salesProductId'];
    productKeywordId = map['productKeywordID'];
  }
}
