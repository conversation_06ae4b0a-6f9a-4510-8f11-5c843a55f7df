import 'dart:convert';

import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/model/sales_payment_mapping.dart';
import 'package:smartbill/model/transaction_model.dart';

GetTransactionsFromApi getTransactionsFromApiFromJson(String str) => GetTransactionsFromApi.fromJson(json.decode(str));

String getTransactionsFromApiToJson(GetTransactionsFromApi data) => json.encode(data.toJson());

class GetTransactionsFromApi {
  GetTransactionsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  GetTransactions? result;

  factory GetTransactionsFromApi.fromJson(Map<String, dynamic> json) => GetTransactionsFromApi(
        status: json["status"],
        message: json["message"],
        result: GetTransactions.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result?.toJson(),
      };
}

class GetTransactions {
  GetTransactions({
    this.totalSalesTransaction,
    this.totalQty,
    this.totalAmount,
    this.salesTransaction,
    this.salesPayments,
    this.advanceOrderNotes,
  });

  int? totalSalesTransaction;
  double? totalQty;
  double? totalAmount;
  List<Transactions>? salesTransaction;
  List<SalesPaymentMapping>? salesPayments;
  List<AdvanceOrderNoteModel>? advanceOrderNotes;

  factory GetTransactions.fromJson(Map<String, dynamic> json) => GetTransactions(
        // totalSalesTransaction: json["totalSalesTransaction"],
        // totalQty: json["totalQty"],
        // totalAmount: json["totalAmount"],
        salesTransaction: json["salesTransaction"] == null ? null : List<Transactions>.from(json["salesTransaction"].map((x) => Transactions.fromJson(x))),
        salesPayments: json["salesPayments"] == null ? null : List<SalesPaymentMapping>.from(json["salesPayments"].map((x) => SalesPaymentMapping.fromJson1(x))),
        advanceOrderNotes: json["advanceOrderNotes"] == null ? null : List<AdvanceOrderNoteModel>.from(json["advanceOrderNotes"].map((x) => AdvanceOrderNoteModel.fromJson(x))),

      );

  Map<String, dynamic> toJson() => {
        "totalSalesTransaction": totalSalesTransaction,
        "totalQty": totalQty,
        "totalAmount": totalAmount,
        "salesTransaction": List<dynamic>.from(salesTransaction!.map((x) => x.toJson())),
        "salesPayments": List<dynamic>.from(salesPayments!.map((x) => x.toJson())),
        "advanceOrderNotes": List<dynamic>.from(advanceOrderNotes!.map((x) => x.toJson())),
      };
}
