class SalesCalculateRequest {
  double qty = 0;
  double rate = 0;
  double cgst = 0;
  double sgst = 0;
  double discountAmount = 0;
  double parcelPerProduct = 0;
  bool withTax = false;
  bool includedTax = false;
  int paymentCategoryId = 0;
}

class SalesCalculateResponse {
  double qty = 0;
  double rate = 0;
  double rateAfterTax = 0;
  double cgst = 0;
  double sgst = 0;
  double cgstAmt = 0;
  double sgstAmt = 0;
  double discountAmount = 0;
  double totalAmt = 0;
  double totalAmtWithoutGst = 0;
  double parcelAmount = 0;
  double parcelAmountWithoutGst = 0;
}
