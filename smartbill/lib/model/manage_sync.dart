// To parse this JSON data, do
//
//     final manageSync = manageSyncFromMap(jsonString);

import 'dart:convert';

ManageSync manageSyncFromMap(String str) =>
    ManageSync.fromMap(json.decode(str));

String manageSyncToMap(ManageSync data) => json.encode(data.toMap());

class ManageSync {
  ManageSync({
    this.id,
    this.workspaceId,
    this.userName,
    this.commitId,
    this.commitDate,
  });

  int? id;
  String? workspaceId;
  String? userName;
  String? commitId;
  String? commitDate;

  factory ManageSync.fromMap(Map<String, dynamic> json) => ManageSync(
        id: json["ID"] == null ? null : json["ID"],
        workspaceId: json["workspaceId"] == null ? null : json["workspaceId"],
        userName: json["userName"] == null ? null : json["userName"],
        commitId: json["commitId"] == null ? null : json["commitId"],
        commitDate: json["commitDate"] == null ? null : json["commitDate"],
      );

  Map<String, dynamic> toMap() => {
        "ID": id == null ? null : id,
        "workspaceId": workspaceId == null ? null : workspaceId,
        "userName": userName == null ? null : userName,
        "commitId": commitId == null ? null : commitId,
        "commitDate": commitDate == null ? null : commitDate,
      };
}
