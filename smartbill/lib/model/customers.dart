import '../utils/dbutils/db_strings.dart';

class Customer {
  int? id;
  String? customerID;
  String? firstName;
  String? lastName;
  String? mobileNo;
  String? email;
  String? address1;
  String? address2;
  String? fkAreaID;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;
  int? isWhatsApp;
  String? gstNo;
  String? exitBalance;
  String? areaName;
  String? balance;

  Customer(
      {this.id,
      this.customerID,
      this.firstName,
      this.lastName,
      this.mobileNo,
      this.email,
      this.address1,
      this.address2,
      this.fkAreaID,
      this.createdDate,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive,
      this.isWhatsApp,
      this.gstNo,
      this.exitBalance});

  Map<String, dynamic> toMap() {
    return {
      db_customer_ID: id,
      db_customer_customerID: customerID,
      db_customer_firstName: firstName,
      db_customer_lastName: lastName,
      db_customer_mobileNo: mobileNo,
      db_customer_email: email,
      db_customer_address1: address1,
      db_customer_address2: address2,
      db_customer_isWhatsApp: isWhatsApp,
      db_customer_fK_areaID: fkAreaID,
      db_customer_GST_No: gstNo,
      db_customer_exist_balance: exitBalance,
      db_common_createdDate: createdDate,
      db_product_rowStatus: rowStatus,
      db_common_syncStatus: syncStatus,
      db_workspace_workspaceId: workspaceID
    };
  }

  Customer.fromMap(Map<String, dynamic> map) {
    id = map['ID'];
    customerID = map['CustomerID'];
    firstName = map['FirstName'];
    lastName = map['LastName'];
    email = map['Email'];
    mobileNo = map['MobileNo'];
    address1 = map['Address1'];
    address2 = map['Address2'];
    isWhatsApp = map['isWhatsApp'];
    fkAreaID = map['FK_AreaID'];
    gstNo = map['GST_No'];
    exitBalance = map['Exist_Balance'];
    createdDate = map['CreatedDate'];
    rowStatus = map['rowStatus'];
    syncStatus = map['SyncStatus'];
    workspaceID = map['workspaceId'];
  }

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        id: json["id"],
        customerID: json["customerID"],
        firstName: json["firstName"],
        lastName: json["lastName"],
        email: json["email"],
        mobileNo: json["mobileNo"],
        address1: json["address1"],
        address2: json["address2"],
        isWhatsApp: json["isWhatsApp"],
        fkAreaID: json["areaID"],
        gstNo: json["gst"],
        exitBalance: json["balance"],
        createdDate: json["createdDate"],
        rowStatus: json["status"],
        workspaceID: json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "customerID": customerID,
        "firstName": firstName,
        "lastName": lastName,
        "email": email,
        "mobileNo": mobileNo,
        "address1": address1,
        "address2": address2,
        "isWhatsApp": isWhatsApp,
        "areaID": fkAreaID,
        "gst": gstNo,
        "balance": exitBalance,
        "createdDate": createdDate,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };

  toLowerCase() {}
}
