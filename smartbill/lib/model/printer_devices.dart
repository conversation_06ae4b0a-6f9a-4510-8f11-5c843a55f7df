import 'dart:convert';

import 'package:smartbill/utils/common_function/printer_utilities.dart';

class PrinterDevices {
  String? printerName;
  String? customizeName;
  String? printerAddress;
  int? portNumber;
  String? printerType;
  PrinterFor? printerFor;
  int? paperSize;
  String? printerFormat;
  int? feedSize;
  bool? paperCut;
  // bool multiMobilePrint;

  PrinterDevices({
    this.printerName,
    this.customizeName,
    this.printerAddress,
    this.portNumber,
    this.printerType,
    this.printerFor,
    this.paperSize,
    this.printerFormat,
    this.feedSize,
    this.paperCut,
    // this.multiMobilePrint
  });

  PrinterDevices.fromJson(Map<String, dynamic> json) {
    printerName = json['printerName'];
    customizeName = json['customizeName'];
    printerAddress = json['printerAddress'];
    portNumber = json['portNumber'];
    printerType = json['printerType'];
    printerFor = printForFromString(json['printerFor']);
    paperSize = json['paperSize'];
    printerFormat = json['printerFormat'];
    feedSize = json['feedSize'];
    paperCut = json['paperCut'];
    // multiMobilePrint = json['multiMobilePrint'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['printerName'] = this.printerName;
    data['customizeName'] = this.customizeName;
    data['portNumber'] = this.portNumber;
    data['printerAddress'] = this.printerAddress;
    data['printerType'] = this.printerType;
    data['printerFor'] = this.printerFor;
    data['paperSize'] = this.paperSize;
    data['printerFormat'] = this.printerFormat;
    data['feedSize'] = this.feedSize;
    data['paperCut'] = this.paperCut;
    // data['multiMobilePrint'] = this.multiMobilePrint;
    return data;
  }

  static Map<String, dynamic> toJson1(PrinterDevices printerDevices) {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['printerName'] = printerDevices.printerName;
    data['customizeName'] = printerDevices.customizeName;
    data['printerAddress'] = printerDevices.printerAddress;
    data['portNumber'] = printerDevices.portNumber;
    data['printerType'] = printerDevices.printerType;
    data['printerFor'] = printerForToString(printerDevices.printerFor!);
    data['paperSize'] = printerDevices.paperSize;
    data['printerFormat'] = printerDevices.printerFormat;
    data['feedSize'] = printerDevices.feedSize;
    data['paperCut'] = printerDevices.paperCut;
    // data['multiMobilePrint'] = printerDevices.multiMobilePrint;
    return data;
  }

  static String encode(List<PrinterDevices> printerDevices) => json.encode(
        printerDevices
            .map<Map<String, dynamic>>(
                (printerDevice) => PrinterDevices.toJson1(printerDevice))
            .toList(),
      );

  static List<PrinterDevices> decode(String printerDevices) =>
      (json.decode(printerDevices) as List<dynamic>)
          .map<PrinterDevices>((item) => PrinterDevices.fromJson(item))
          .toList();

  PrinterFor? printForFromString(String value) {
    switch (value) {
      case 'bill':
        return PrinterFor.bill;
      case 'kot':
        return PrinterFor.kot;
      case 'both':
        return PrinterFor.both;
      default:
        return null;
    }
  }

  static printerForToString(PrinterFor value) {
    return value.toString().split('.').last;
  }
}
