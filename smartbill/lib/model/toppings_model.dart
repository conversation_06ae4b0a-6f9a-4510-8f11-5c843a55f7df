import 'package:smartbill/utils/dbutils/db_strings.dart';

class ToppingsGroup {
  int? id;
  String? toppingGroupId;
  String? toppingGroupName;
  String? workspaceId;
  bool? isActive;
  int? toppingGroupSync;
  int? rowStatus;

  ToppingsGroup(
      {this.id,
      this.toppingGroupId,
      this.toppingGroupName,
      this.isActive,
      this.workspaceId,
      this.toppingGroupSync,
      this.rowStatus});

  Map<String, dynamic> toMap() {
    return {
      db_toppingGroups_toppingGroupAutoId: id,
      db_toppingGroups_toppingGroupId: toppingGroupId,
      db_toppingGroups_toppingGroupName: toppingGroupName,
      db_workspace_workspaceId: workspaceId,
      db_toppingGroups_toppingGroupSync: toppingGroupSync,
      db_product_rowStatus: rowStatus,
    };
  }

  ToppingsGroup.fromMap(Map<String, dynamic> map) {
    id = map['toppingGroupAutoId'];
    toppingGroupId = map['toppingGroupId'];
    toppingGroupName = map['toppingGroupName'];
    workspaceId = map['workspaceId'];
    toppingGroupSync = map['toppingGroupSync'];
    rowStatus = map['rowStatus'];
  }

  factory ToppingsGroup.fromJson(Map<String, dynamic> json) => ToppingsGroup(
        id: json["toppingGroupAutoId"] == null
            ? null
            : json["toppingGroupAutoId"],
        toppingGroupId:
            json["toppingGroupID"] == null ? null : json["toppingGroupID"],
        toppingGroupName:
            json["toppingGroupName"] == null ? null : json["toppingGroupName"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        toppingGroupSync:
            json["toppingGroupSync"] == null ? null : json["toppingGroupSync"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "toppingGroupAutoId": id == null ? null : id,
        "toppingGroupID": toppingGroupId == null ? null : toppingGroupId,
        "toppingGroupName": toppingGroupName == null ? null : toppingGroupName,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "toppingGroupSync": toppingGroupSync == null ? null : toppingGroupSync,
        "status": rowStatus == null ? null : rowStatus,
      };
}

class Topping {
  int? id;
  String? toppingId;
  String? toppingGroupId;
  String? toppingName;
  String? toppingPrice;
  String? workspaceId;
  bool? isActive;
  int? toppingSync;
  int? rowStatus;

  Topping(
      {this.id,
      this.toppingId,
      this.toppingGroupId,
      this.toppingName,
      this.toppingPrice,
      this.isActive,
      this.workspaceId,
      this.toppingSync,
      this.rowStatus});

  Map<String, dynamic> toMap() {
    return {
      db_toppings_toppingAutoId: id,
      db_toppings_toppingId: toppingId,
      db_toppingGroups_toppingGroupId: toppingGroupId,
      db_toppings_toppingName: toppingName,
      db_toppings_toppingPrice: toppingPrice,
      db_workspace_workspaceId: workspaceId,
      db_toppings_toppingSync: toppingSync,
      db_product_rowStatus: rowStatus,
    };
  }

  Topping.fromMap(Map<String, dynamic> map) {
    id = map['toppingAutoId'];
    toppingId = map['toppingId'];
    toppingGroupId = map['toppingGroupId'];
    toppingName = map['toppingName'];
    toppingPrice = map['toppingPrice'];
    workspaceId = map['workspaceId'];
    toppingSync = map['toppingSync'];
    rowStatus = map['rowStatus'];
  }

  factory Topping.fromJson(Map<String, dynamic> json) => Topping(
        id: json["toppingAutoId"] == null ? null : json["toppingAutoId"],
        toppingId: json["toppingID"] == null ? null : json["toppingID"],
        toppingGroupId:
            json["toppingGroupID"] == null ? null : json["toppingGroupID"],
        toppingName: json["toppingName"] == null ? null : json["toppingName"],
        toppingPrice: json["toppingPrice"] == null
            ? null
            : json["toppingPrice"].toString(),
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        toppingSync: json["toppingSync"] == null ? null : json["toppingSync"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "toppingGroupAutoId": id == null ? null : id,
        "toppingID": toppingId == null ? null : toppingId,
        "toppingGroupID": toppingGroupId == null ? null : toppingGroupId,
        "toppingName": toppingName == null ? null : toppingName,
        "toppingPrice": toppingPrice == null ? null : toppingPrice,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "toppingSync": toppingSync == null ? null : toppingSync,
        "status": rowStatus == null ? null : rowStatus,
      };
}
