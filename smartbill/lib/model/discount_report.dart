class DiscountReports {
  String? salesID;
  String? invoiceID;
  String? totalAmount;
  String? discountAmount;
  String? discountFormula;
  String? productID;
  String? productName;

  DiscountReports(
      {this.salesID,
      this.invoiceID,
      this.totalAmount,
      this.discountAmount,
      this.discountFormula,
      this.productID,
      this.productName});

  DiscountReports.fromMap(Map<String, dynamic> map) {
    salesID = map['salesId'];
    invoiceID = map['invoiceId'];
    totalAmount =
        map['totalAmount'] != null ? map['totalAmount'].toString() : null;
    discountAmount =
        map['discountPrice'] != null ? map['discountPrice'].toString() : null;
    discountFormula = map['discountFormula'];
    productID = map['salesProductId'];
    productName = map['salesProductName'];
  }
}
