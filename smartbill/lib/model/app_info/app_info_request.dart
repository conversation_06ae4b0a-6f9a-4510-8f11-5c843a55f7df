class AppInfoRequest {
  String? mobileId;
  int? appVersion;
  String? androidVersion;
  String? iosVersion;
  String? fireBaseToken;
  bool? isFireBaseToken;
  int? userType;
  String? appName;

  AppInfoRequest(
      {this.mobileId,
      this.appVersion,
      this.androidVersion,
      this.iosVersion,
      this.fireBaseToken,
      this.isFireBaseToken,
      this.userType,
      this.appName});

  AppInfoRequest.fromJson(Map<String, dynamic> json) {
    mobileId = json['mobileId'];
    appVersion = json['appVersion'];
    androidVersion = json['androidVersion'];
    iosVersion = json['iosVersion'];
    fireBaseToken = json['fireBaseToken'];
    isFireBaseToken = json['isFireBaseToken'];
    userType = json['userType'];
    appName = json['appName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['mobileId'] = mobileId;
    data['appVersion'] = appVersion;
    data['androidVersion'] = androidVersion;
    data['iosVersion'] = iosVersion;
    data['fireBaseToken'] = fireBaseToken;
    data['isFireBaseToken'] = isFireBaseToken;
    data['userType'] = userType;
    data['appName'] = appName ?? '';
    return data;
  }
}
