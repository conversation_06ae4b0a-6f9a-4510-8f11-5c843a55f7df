class GetAppInfoResponse {
  int? status;
  String? message;
  AppInfoResponse? result;

  GetAppInfoResponse({this.status, this.message, this.result});

  GetAppInfoResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? AppInfoResponse.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (result != null) {
      data['result'] = result!.toJson();
    }
    return data;
  }
}

class AppInfoResponse {
  String? androidAppVersion;
  String? androidAppCriticalAppVersion;
  String? iosAppVersion;
  String? iosAppCriticalAppVersion;
  bool? isFirebaseTokenAdded;
  String? fireBaseToken;
  bool? isAuthorizedUser;
  int? roleId;
  bool? isEruthukattuAdmin;

  AppInfoResponse(
      {this.androidAppVersion,
      this.androidAppCriticalAppVersion,
      this.iosAppVersion,
      this.iosAppCriticalAppVersion,
      this.isFirebaseTokenAdded,
      this.fireBaseToken,
      this.isAuthorizedUser,
      this.roleId,
      this.isEruthukattuAdmin});

  AppInfoResponse.fromJson(Map<String, dynamic> json) {
    androidAppVersion = json['androidAppVersion'];
    androidAppCriticalAppVersion = json['androidAppCriticalAppVersion'];
    iosAppVersion = json['iosAppVersion'];
    iosAppCriticalAppVersion = json['iosAppCriticalAppVersion'];
    isFirebaseTokenAdded = json['isFirebaseTokenAdded'];
    fireBaseToken = json['fireBaseToken'];
    isAuthorizedUser = json['isAuthorizedUser'];
    roleId = json['roleId'];
    isEruthukattuAdmin = json['isEruthukattuAdmin'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['androidAppVersion'] = androidAppVersion;
    data['androidAppCriticalAppVersion'] = androidAppCriticalAppVersion;
    data['iosAppVersion'] = iosAppVersion;
    data['iosAppCriticalAppVersion'] = iosAppCriticalAppVersion;
    data['isFirebaseTokenAdded'] = isFirebaseTokenAdded;
    data['fireBaseToken'] = fireBaseToken;
    data['isAuthorizedUser'] = isAuthorizedUser;
    data['roleId'] = roleId;
    data['isEruthukattuAdmin'] = isEruthukattuAdmin;
    return data;
  }
}
