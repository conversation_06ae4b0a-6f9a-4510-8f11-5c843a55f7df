// To parse this JSON data, do
//
//     final addWorkSpaceMember = addWorkSpaceMemberFromJson(jsonString);

import 'dart:convert';

AddWorkSpaceMember addWorkSpaceMemberFromJson(String str) =>
    AddWorkSpaceMember.fromJson(json.decode(str));

String addWorkSpaceMemberToJson(AddWorkSpaceMember data) =>
    json.encode(data.toJson());

class AddWorkSpaceMember {
  AddWorkSpaceMember(
      {this.workspaceId,
      this.username,
      this.status,
      this.message,
      this.id,
      this.roleId});

  String? workspaceId;
  String? username;
  int? roleId;
  int? status;
  String? message;
  int? id;

  factory AddWorkSpaceMember.fromJson(Map<String, dynamic> json) =>
      AddWorkSpaceMember(
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        username: json["username"] == null ? null : json["username"],
        roleId: json["roleID"] == null ? null : json["roleID"],
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        id: json["id"] == null ? null : json["id"],
      );

  Map<String, dynamic> toJson() => {
        "workspaceID": workspaceId == null ? null : workspaceId,
        "username": username == null ? null : username,
        "roleID": roleId == null ? null : roleId,
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "id": id == null ? null : id,
      };
}
