// To parse this JSON data, do
//
//     final getWorkspaceMember = getWorkspaceMemberFromJson(jsonString);

import 'dart:convert';

GetWorkspaceMember getWorkspaceMemberFromJson(String str) =>
    GetWorkspaceMember.fromJson(json.decode(str));

String getWorkspaceMemberToJson(GetWorkspaceMember data) =>
    json.encode(data.toJson());

class GetWorkspaceMember {
  GetWorkspaceMember({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<WorkspaceMember>? result;

  factory GetWorkspaceMember.fromJson(Map<String, dynamic> json) =>
      GetWorkspaceMember(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<WorkspaceMember>.from(
                json["result"].map((x) => WorkspaceMember.fromJson(x))),
      );

  Map<String, dynamic> toJ<PERSON>() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class WorkspaceMember {
  WorkspaceMember({
    this.id,
    this.workspaceId,
    this.userId,
    this.userName,
    this.lastName,
    this.firstName,
    this.roleId,
    this.isActive,
  });

  int? id;
  String? workspaceId;
  int? userId;
  String? userName;
  String? lastName;
  String? firstName;
  int? roleId;
  bool? isActive;

  factory WorkspaceMember.fromJson(Map<String, dynamic> json) =>
      WorkspaceMember(
        id: json["id"] == null ? null : json["id"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        userId: json["userID"] == null ? null : json["userID"],
        userName: json["userName"] == null ? null : json["userName"],
        lastName: json["lastName"] == null ? null : json["lastName"],
        firstName: json["firstName"] == null ? null : json["firstName"],
        roleId: json["roleID"] == null ? null : json["roleID"],
        isActive: json["isActive"] == null ? null : json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "userID": userId == null ? null : userId,
        "userName": userName == null ? null : userName,
        "lastName": lastName == null ? null : lastName,
        "firstName": firstName == null ? null : firstName,
        "roleID": roleId == null ? null : roleId,
        "isActive": isActive == null ? null : isActive,
      };
}
