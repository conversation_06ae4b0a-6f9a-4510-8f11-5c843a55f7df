import 'dart:convert';

Activation1 activationFromJson(String str) =>
    Activation1.fromJson(json.decode(str));

String activationToMap(Activation1 data) => json.encode(data.toMap());

class Activation1 {
  Activation1({
    this.applicationId,
    this.mobileId,
    this.validityDays,
    this.activationCode,
    this.response,
  });

  int? applicationId;
  String? mobileId;
  int? validityDays;
  String? activationCode;
  ActivationResponse? response;

  factory Activation1.fromJson(Map<String, dynamic> json) => Activation1(
        response: ActivationResponse.fromJson(json["response"]),
      );

  Map<String, dynamic> toMap() => {
        "applicationID": applicationId,
        "mobileID": mobileId,
        "validityDays": validityDays,
        "activationCode": activationCode,
      };
}

class ActivationResponse {
  ActivationResponse({this.message, this.expiryDate, this.status});

  String? message;
  DateTime? expiryDate;
  int? status;

  factory ActivationResponse.fromJson(Map<String, dynamic> json) =>
      ActivationResponse(
        message: json["message"],
        expiryDate: DateTime.parse(json["expiryDate"]),
        status: json["status"],
      );

  Map<String, dynamic> toMap() => {
        "message": message,
        "expiryDate": expiryDate?.toIso8601String(),
      };
}
