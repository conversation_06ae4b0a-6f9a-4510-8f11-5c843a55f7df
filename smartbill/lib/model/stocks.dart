class Stocks {
  int? id;
  String? stocksID;
  String? productId;
  String? stocksQty;
  String? stocksSold;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  //Simple Model
  String? currentQty;
  String? productName;
  String? modifiedStocks;
  String? updatedQty;
  int? productSaleID;

  Stocks({this.id, this.stocksID, this.productId, this.stocksQty, this.stocksSold, this.rowStatus, this.syncStatus, this.workspaceID, this.isActive});

  Stocks.fromMap(Map<String, dynamic> map) {
    id = map['stocksAutoId'];
    stocksID = map['stocksId'];
    productId = map['productId'];
    stocksQty = map['stocksQty'];
    stocksSold = map['stocksSold'];
    rowStatus = map['rowStatus'];
    syncStatus = map['stocksSync'];
    workspaceID = map['workspaceId'];
  }

  factory Stocks.fromJson(Map<String, dynamic> json) => Stocks(
        stocksID: json["stockID"] == null ? null : json["stockID"],
        productId: json["productID"] == null ? null : json["productID"],
        stocksQty: json["stocksQty"] == null ? null : json["stocksQty"],
        stocksSold: json["stocksSold"] == null ? null : json["stocksSold"],
        workspaceID: json["workspaceID"] == null ? null : json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "stockID": stocksID,
        "productID": productId,
        "stocksQty": stocksQty,
        "stocksSold": stocksSold,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}

class StockLogs {
  int? id;
  String? stockLogsID;
  String? stocksID;
  String? modifiedStocks;
  String? createdBy;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  StockLogs(
      {this.id,
      this.stockLogsID,
      this.stocksID,
      this.modifiedStocks,
      this.createdBy,
      this.createdDate,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive});

  StockLogs.fromMap(Map<String, dynamic> map) {
    id = map['stockLogsAutoId'];
    stockLogsID = map['stockLogsId'];
    stocksID = map['stocksId'];
    modifiedStocks = map['modifiedStocks'];
    createdDate = map['createdDate'];
    createdBy = map['createdBy'];
    rowStatus = map['rowStatus'];
    syncStatus = map['stocksSync'];
    workspaceID = map['workspaceId'];
  }

  factory StockLogs.fromJson(Map<String, dynamic> json) => StockLogs(
        stockLogsID: json["stockLogsID"] == null ? null : json["stockLogsID"],
        stocksID: json["stocksID"] == null ? null : json["stocksID"],
        modifiedStocks: json["modifiedStocks"] == null ? null : json["modifiedStocks"],
        createdDate: json["createdDate"] == null ? null : json["createdDate"],
        createdBy: json["createdBy"] == null ? null : json["createdBy"],
        workspaceID: json["workspaceID"] == null ? null : json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "stockLogsID": stockLogsID,
        "stocksID": stocksID,
        "modifiedStocks": modifiedStocks,
        "createdDate": createdDate,
        "createdBy": createdBy,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}

class ModifyStocksDto {
  String? stocksID;
  String? productId;
  String? stocksQty;
  String? stocksSold;
  String? stockLogsID;
  String? modifiedStocks;

  ModifyStocksDto({this.stocksID, this.productId, this.stocksQty, this.stocksSold, this.stockLogsID, this.modifiedStocks});
}
