class Area {
  int? id;
  String? areaID;
  String? areaName;
  String? createdDate;
  int? rowStatus;
  int? syncStatus;
  String? workspaceID;
  bool? isActive;

  Area(
      {this.id,
      this.areaID,
      this.areaName,
      this.createdDate,
      this.rowStatus,
      this.syncStatus,
      this.workspaceID,
      this.isActive});

  Map<String, dynamic> toMap() {
    return {
      // DBScript.Area_ID: id,
      // DBScript.Area_AreaID: areaID,
      // DBScript.Area_AreaName: areaName,
      // DBScript.Common_CreatedDate: createdDate,
      // DBScript.Common_RowStatus: rowStatus,
      // DBScript.Common_SyncStatus: syncStatus,
      // DBScript.Common_WorkspaceID: workspaceID,
    };
  }

  Area.fromMap(Map<String, dynamic> map) {
    id = map['ID'];
    areaID = map['AreaID'];
    areaName = map['AreaName'];
    createdDate = map['CreatedDate'];
    rowStatus = map['rowStatus'];
    syncStatus = map['SyncStatus'];
    workspaceID = map['workspaceId'];
  }

  factory Area.fromJson(Map<String, dynamic> json) => Area(
        id: json["id"],
        areaID: json["areaID"],
        areaName: json["areaName"],
        createdDate: json["createdDate"],
        rowStatus: json["status"],
        workspaceID: json["workspaceID"],
        isActive: json["isActive"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "areaID": areaID,
        "areaName": areaName,
        "createdDate": createdDate,
        "status": rowStatus,
        "workspaceID": workspaceID,
      };
}
