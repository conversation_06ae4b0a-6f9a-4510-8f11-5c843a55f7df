class ExpenseDashboardModel {
  int? status;
  String? message;
  DashboardInfo? result;

  ExpenseDashboardModel({this.status, this.message, this.result});

  ExpenseDashboardModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null  ? new DashboardInfo.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.toJson();
    }
    return data;
  }
}

class DashboardInfo {
  int? totalExpense;
  int? totalExpenseQuantity;
  double? totalExpenseAmount;
  int? totalPurchase;
  int? totalPurchaseQuantity;
  double? totalPurchaseAmount;
  int? totalSales;
  int? totalSalesQuantity;
  double? totalSalesAmount;
  double? profit;

  DashboardInfo(
      {this.totalExpense,
      this.totalExpenseQuantity,
      this.totalExpenseAmount,
      this.totalPurchase,
      this.totalPurchaseQuantity,
      this.totalPurchaseAmount,
      this.totalSales,
      this.totalSalesQuantity,
      this.totalSalesAmount,
      this.profit});

  DashboardInfo.fromJson(Map<String, dynamic> json) {
    totalExpense = json['totalExpense'];
    totalExpenseQuantity = json['totalExpenseQuantity'];
    totalExpenseAmount = json['totalExpenseAmount'];
    totalPurchase = json['totalPurchase'];
    totalPurchaseQuantity = json['totalPurchaseQuantity'];
    totalPurchaseAmount = json['totalPurchaseAmount'];
    totalSales = json['totalSales'];
    totalSalesQuantity = json['totalSalesQuantity'];
    totalSalesAmount = json['totalSalesAmount'];
    profit = json['profit'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalExpense'] = this.totalExpense;
    data['totalExpenseQuantity'] = this.totalExpenseQuantity;
    data['totalExpenseAmount'] = this.totalExpenseAmount;
    data['totalPurchase'] = this.totalPurchase;
    data['totalPurchaseQuantity'] = this.totalPurchaseQuantity;
    data['totalPurchaseAmount'] = this.totalPurchaseAmount;
    data['totalSales'] = this.totalSales;
    data['totalSalesQuantity'] = this.totalSalesQuantity;
    data['totalSalesAmount'] = this.totalSalesAmount;
    data['profit'] = this.profit;
    return data;
  }
}