class GetStockReportFromAPI {
  int? status;
  String? message;
  List<StockReport>? result;

  GetStockReportFromAPI({this.status, this.message, this.result});

  GetStockReportFromAPI.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['result'] != null) {
      result = <StockReport>[];
      json['result'].forEach((v) {
        result!.add(new StockReport.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class StockReport {
  int? id;
  String? productID;
  String? productName;
  int? stock;
  int? stocksQty;
  int? stocksSold;

  StockReport({this.productID, this.productName, this.stock,this.id,this.stocksQty,
      this.stocksSold});

  StockReport.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    productID = json['productID'];
    productName = json['productName'];
    stock = json['stock'];
    stocksQty = json['stocksQty'];
    stocksSold = json['stocksSold'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['productID'] = this.productID;
    data['productName'] = this.productName;
    data['stock'] = this.stock;
    data['stocksQty'] = this.stocksQty;
    data['stocksSold'] = this.stocksSold;
    return data;
  }
}