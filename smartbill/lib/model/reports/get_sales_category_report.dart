import '../reports.dart';

class GetSalesCategoryReportsFromAPI {
  int? status;
  String? message;
  SalesCategoryReports? result;

  GetSalesCategoryReportsFromAPI({this.status, this.message, this.result});

  GetSalesCategoryReportsFromAPI.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result =
        json['result'] != null ? new SalesCategoryReports.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.toJson();
    }
    return data;
  }
}

class SalesCategoryReports {
  String? workspaceId;
  int? totalSales;
  double? totalAmount;
  List<Sales>? reportList;

  SalesCategoryReports(
      {this.workspaceId, this.totalSales, this.totalAmount, this.reportList});

  SalesCategoryReports.fromJson(Map<String, dynamic> json) {
    workspaceId = json['workspaceId'];
    totalSales = json['totalSales'];
    totalAmount = json['totalAmount'];
    if (json['reportList'] != null) {
      reportList = <Sales>[];
      json['reportList'].forEach((v) {
        reportList!.add(new Sales.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workspaceId'] = this.workspaceId;
    data['totalSales'] = this.totalSales;
    data['totalAmount'] = this.totalAmount;
    if (this.reportList != null) {
      data['reportList'] = this.reportList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}
