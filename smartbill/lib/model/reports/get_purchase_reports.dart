class GetPurchaseReports {
  int? status;
  String? message;
  PurchaseReportsResult? result;

  GetPurchaseReports({this.status, this.message, this.result});

  GetPurchaseReports.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? new PurchaseReportsResult.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.toJson();
    }
    return data;
  }
}

class PurchaseReportsResult {
  String? workspaceId;
  int? totalPurchase;
  int? totalQuantity;
  double? totalAmount;
  List<PurchaseTransactionReport>? reportList;

  PurchaseReportsResult({this.workspaceId, this.totalPurchase, this.totalQuantity, this.totalAmount, this.reportList});

  PurchaseReportsResult.fromJson(Map<String, dynamic> json) {
    workspaceId = json['workspaceId'];
    totalPurchase = json['totalPurchase'];
    totalQuantity = json['totalQuantity'];
    totalAmount = json['totalAmount'];
    if (json['reportList'] != null) {
      reportList = <PurchaseTransactionReport>[];
      json['reportList'].forEach((v) {
        reportList!.add(new PurchaseTransactionReport.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workspaceId'] = this.workspaceId;
    data['totalPurchase'] = this.totalPurchase;
    data['totalQuantity'] = this.totalQuantity;
    data['totalAmount'] = this.totalAmount;
    if (this.reportList != null) {
      data['reportList'] = this.reportList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class PurchaseTransactionReport {
  int? id;
  String? invoiceId;
  int? invoiceNo;
  int? quantity;
  String? createdDate;
  double? totalAmount;
  String? purchaseId;
  String? productKeywordId;
  String? productKeywordName;
  String? invoiceExtension;

  PurchaseTransactionReport({
    this.id,
    this.invoiceId,
    this.invoiceNo,
    this.quantity,
    this.createdDate,
    this.totalAmount,
    this.purchaseId,
    this.productKeywordId,
    this.productKeywordName,
    this.invoiceExtension,
  });

  PurchaseTransactionReport.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    invoiceId = json['invoiceID'];
    invoiceNo = json['invoiceNo'];
    quantity = json['quantity'];
    createdDate = json['createdDate'];
    totalAmount = json['totalAmount'];
    purchaseId = json['purchaseID'];
    productKeywordId = json['productKeywordID'];
    productKeywordName = json['productKeywordName'];
    invoiceExtension = json['invoiceExtension'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['invoiceID'] = this.invoiceId;
    data['invoiceNo'] = this.invoiceNo;
    data['quantity'] = this.quantity;
    data['createdDate'] = this.createdDate;
    data['totalAmount'] = this.totalAmount;
    data['purchaseID'] = this.purchaseId;
    data['productKeywordID'] = this.productKeywordId;
    data['productKeywordName'] = this.productKeywordName;
    data['invoiceExtension'] = this.invoiceExtension;
    return data;
  }
}
