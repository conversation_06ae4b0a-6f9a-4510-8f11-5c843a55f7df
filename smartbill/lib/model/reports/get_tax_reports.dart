class GetTaxReportsFromApi {
  int? status;
  String? message;
  TaxReportsResult? result;

  GetTaxReportsFromApi({this.status, this.message, this.result});

  GetTaxReportsFromApi.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result =
        json['result'] != null ? new TaxReportsResult.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.toJson();
    }
    return data;
  }
}

class TaxReportsResult {
  TaxAmounts? tax;
  List<TaxReports>? getTax;

  TaxReportsResult({this.tax, this.getTax});

  TaxReportsResult.fromJson(Map<String, dynamic> json) {
    tax = json['tax'] != null ? new TaxAmounts.fromJson(json['tax']) : null;
    if (json['getTax'] != null) {
      getTax = <TaxReports>[];
      json['getTax'].forEach((v) {
        getTax!.add(new TaxReports.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.tax != null) {
      data['tax'] = this.tax!.toJson();
    }
    if (this.getTax != null) {
      data['getTax'] = this.getTax!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TaxAmounts {
  double? cgst;
  double? sgst;
  double? gst;

  TaxAmounts({this.cgst, this.sgst, this.gst});

  TaxAmounts.fromJson(Map<String, dynamic> json) {
    cgst = json['cgst'];
    sgst = json['sgst'];
    gst = json['gst'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['cgst'] = this.cgst;
    data['sgst'] = this.sgst;
    data['gst'] = this.gst;
    return data;
  }
}

class TaxReports {
  bool? isGst;
  double? sgstAmount;
  double? cgstAmount;
  String? productID;
  String? productName;
  double? productPrice;
  double? quantity;
  double? amount;
  double? totalGst;

  TaxReports(
      {this.isGst,
      this.sgstAmount,
      this.cgstAmount,
      this.productID,
      this.productName,
      this.productPrice,
      this.quantity,
      this.amount,
      this.totalGst});

  TaxReports.fromJson(Map<String, dynamic> json) {
    isGst = json['isGst'];
    sgstAmount = json['sgstAmount'];
    cgstAmount = json['cgstAmount'];
    productID = json['productID'];
    productName = json['productName'];
    productPrice = json['productPrice'];
    quantity = json['quantity'];
    amount = json['amount'];
    totalGst = json['totalGst'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['isGst'] = this.isGst;
    data['sgstAmount'] = this.sgstAmount;
    data['cgstAmount'] = this.cgstAmount;
    data['productID'] = this.productID;
    data['productName'] = this.productName;
    data['productPrice'] = this.productPrice;
    data['quantity'] = this.quantity;
    data['amount'] = this.amount;
    data['totalGst'] = this.totalGst;
    return data;
  }
}
