class DownloadReportsRequest {
  int? month;
  int? year;
  int? nMonth;
  int? nYear;
  String? fromDate;
  String? toDate;
  String? workspaceID;
  String? paymentType;
  String? paymentCategoryID;
  String? shiftTypeID;
  String? salesCategoryId;
  String? customerID;

  DownloadReportsRequest(
      {this.month,
      this.year,
      this.nMonth,
      this.nYear,
      this.workspaceID,
      this.fromDate,
      this.toDate,
      this.paymentType,
      this.paymentCategoryID,
      this.shiftTypeID,
      this.salesCategoryId,
      this.customerID});

  DownloadReportsRequest.fromJson(Map<String, dynamic> json) {
    month = json['month'];
    year = json['year'];
    nMonth = json['nMonth'];
    nYear = json['nYear'];
    workspaceID = json['workspaceID'];
    fromDate = json['fromDate'];
    toDate = json['toDate'];
    paymentType = json['paymentType'];
    paymentCategoryID = json['paymentCategoryID'];
    shiftTypeID = json['shiftTypeID'];
    salesCategoryId = json['salesCategoryId'];
    customerID = json['customerID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['month'] = this.month;
    data['year'] = this.year;
    data['nMonth'] = this.nMonth;
    data['nYear'] = this.nYear;
    data['workspaceID'] = this.workspaceID;
    data['toDate'] = this.toDate;
    data['fromDate'] = this.fromDate;
    data['paymentType'] = this.paymentType;
    data['paymentCategoryID'] = this.paymentCategoryID;
    data['shiftTypeID'] = this.shiftTypeID;
    data['salesCategoryId'] = this.salesCategoryId;
    data['customerID'] = this.customerID;
    return data;
  }
}
