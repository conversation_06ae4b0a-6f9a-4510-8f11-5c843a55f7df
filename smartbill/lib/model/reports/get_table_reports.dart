import 'dart:convert';

GetTableReportsFromApi getAllProductDetailsbyTableIdFromJson(String str) =>
    GetTableReportsFromApi.fromJson(json.decode(str));

String getAllProductDetailsbyTableIdToJson(GetTableReportsFromApi data) =>
    json.encode(data.toJson());

class GetTableReportsFromApi {
  GetTableReportsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<TableReports>? result;

  factory GetTableReportsFromApi.fromJson(Map<String, dynamic> json) =>
      GetTableReportsFromApi(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<TableReports>.from(
                json["result"].map((x) => TableReports.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class TableReports {
  TableReports({
    this.tableID,
    this.tableName,
    this.salesID,
    this.productID,
    this.productName,
    this.keywordId,
    this.productPrice,
    this.quantity,
    this.amount,
  });

  String? tableID;
  String? tableName;
  String? salesID;
  String? productID;
  String? productName;
  dynamic keywordId;
  double? productPrice;
  double? quantity;
  double? amount;

  factory TableReports.fromJson(Map<String, dynamic> json) => TableReports(
        tableID: json["tableID"] == null ? null : json["tableID"],
        tableName: json["tableName"] == null ? null : json["tableName"],
        salesID: json["salesID"] == null ? null : json["salesID"],
        productID: json["productID"] == null ? null : json["productID"],
        productName: json["productName"] == null ? null : json["productName"],
        keywordId: json["keywordID"],
        productPrice:
            json["productPrice"] == null ? null : json["productPrice"],
        quantity: json["quantity"] == null ? null : json["quantity"],
        amount: json["amount"] == null ? null : json["amount"],
      );

  Map<String, dynamic> toJson() => {
        "tableID": tableID == null ? null : tableID,
        "tableName": tableName == null ? null : tableName,
        "salesID": salesID == null ? null : salesID,
        "productID": productID == null ? null : productID,
        "productName": productName == null ? null : productName,
        "keywordID": keywordId,
        "productPrice": productPrice == null ? null : productPrice,
        "quantity": quantity == null ? null : quantity,
        "amount": amount == null ? null : amount,
      };
}
