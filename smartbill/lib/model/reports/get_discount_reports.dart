class GetDiscountReports {
  int? status;
  String? message;
  DiscountReportResult? result;

  GetDiscountReports({this.status, this.message, this.result});

  GetDiscountReports.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result =
        json['result'] != null ? new DiscountReportResult.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result!.toJson();
    }
    return data;
  }
}

class DiscountReportResult {
  TotalDiscount? totalDiscount;
  List<ProductDiscounts>? productDiscounts;

  DiscountReportResult({this.totalDiscount, this.productDiscounts});

  DiscountReportResult.fromJson(Map<String, dynamic> json) {
    totalDiscount = json['totalDiscount'] != null
        ? new TotalDiscount.fromJson(json['totalDiscount'])
        : null;
    if (json['productDiscounts'] != null) {
      productDiscounts = <ProductDiscounts>[];
      json['productDiscounts'].forEach((v) {
        productDiscounts!.add(new ProductDiscounts.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.totalDiscount != null) {
      data['totalDiscount'] = this.totalDiscount!.toJson();
    }
    if (this.productDiscounts != null) {
      data['productDiscounts'] =
          this.productDiscounts!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TotalDiscount {
  double? totalDiscount;

  TotalDiscount({this.totalDiscount});

  TotalDiscount.fromJson(Map<String, dynamic> json) {
    totalDiscount = json['totalDiscount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalDiscount'] = this.totalDiscount;
    return data;
  }
}

class ProductDiscounts {
  String? invoiceID;
  String? discountFormula;
  double? discount;

  ProductDiscounts({this.invoiceID, this.discountFormula, this.discount});

  ProductDiscounts.fromJson(Map<String, dynamic> json) {
    invoiceID = json['invoiceID'];
    discountFormula = json['discountFormula'];
    discount = json['discount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['invoiceID'] = this.invoiceID;
    data['discountFormula'] = this.discountFormula;
    data['discount'] = this.discount;
    return data;
  }
}