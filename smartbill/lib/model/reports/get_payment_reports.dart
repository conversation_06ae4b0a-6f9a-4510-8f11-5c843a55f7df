// To parse this JSON data, do
//
//     final getAllPaymentSalesbyPaymentId = getAllPaymentSalesbyPaymentIdFromJson(jsonString);

import 'dart:convert';

import '../reports.dart';

GetPaymentReportsFromApi getAllPaymentSalesbyPaymentIdFromJson(String str) =>
    GetPaymentReportsFromApi.fromJson(json.decode(str));

String getAllPaymentSalesbyPaymentIdToJson(GetPaymentReportsFromApi data) =>
    json.encode(data.toJson());

class GetPaymentReportsFromApi {
  GetPaymentReportsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  PaymentReports? result;

  factory GetPaymentReportsFromApi.fromJson(Map<String, dynamic> json) =>
      GetPaymentReportsFromApi(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : PaymentReports.fromJson(json['result']),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null ? null : result!.toJson(),
      };
}

class PaymentReports {
  String? workspaceId;
  int? totalSales;
  double? totalAmount;
  List<Sales>? reportList;
  List<Sales>? customerSales;


  PaymentReports(
      {this.workspaceId, this.totalSales, this.totalAmount, this.reportList});

  PaymentReports.fromJson(Map<String, dynamic> json) {
    workspaceId = json['workspaceId'];
    totalSales = json['totalSales'];
    totalAmount = json['totalAmount'];
    if (json['reportList'] != null) {
      reportList = <Sales>[];
      json['reportList'].forEach((v) {
        reportList!.add(new Sales.fromJson(v));
      });
    }
     if (json['customerSales'] != null) {
      customerSales = <Sales>[];
      json['customerSales'].forEach((v) {
        customerSales!.add(new Sales.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workspaceId'] = this.workspaceId;
    data['totalSales'] = this.totalSales;
    data['totalAmount'] = this.totalAmount;
    if (this.reportList != null) {
      data['salesPaymentMapDto'] =
          this.reportList!.map((v) => v.toJson()).toList();
    }
    if (this.customerSales != null) {
      data['salesPaymentMapDto'] =
          this.customerSales!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SalesPaymentMapDto {
  int? id;
  String? invoiceID;
  String? invoiceNo;
  String? date;
  int? amount;

  SalesPaymentMapDto(
      {this.id, this.invoiceID, this.invoiceNo, this.date, this.amount});

  SalesPaymentMapDto.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    invoiceID = json['invoiceID'];
    invoiceNo = json['invoiceNo'];
    date = json['date'];
    amount = json['amount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['invoiceID'] = this.invoiceID;
    data['invoiceNo'] = this.invoiceNo;
    data['date'] = this.date;
    data['amount'] = this.amount;
    return data;
  }
}
