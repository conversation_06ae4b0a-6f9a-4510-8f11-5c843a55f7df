// To parse this JSON data, do
//
//     final getUserReports = getUserReportsFromJson(jsonString);

import 'dart:convert';

GetUserReportsFromApi getUserReportsFromJson(String str) =>
    GetUserReportsFromApi.fromJson(json.decode(str));

String getUserReportsToJson(GetUserReportsFromApi data) =>
    json.encode(data.toJson());

class GetUserReportsFromApi {
  GetUserReportsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<UserReports>? result;

  factory GetUserReportsFromApi.fromJson(Map<String, dynamic> json) =>
      GetUserReportsFromApi(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<UserReports>.from(
                json["result"].map((x) => UserReports.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class UserReports {
  UserReports({
    this.createdBy,
    this.userName,
    this.salesId,
    this.productId,
    this.productName,
    this.productPrice,
    this.quantity,
    this.amount,
  });

  String? createdBy;
  String? userName;
  String? salesId;
  String? productId;
  String? productName;
  double? productPrice;
  double? quantity;
  double? amount;

  factory UserReports.fromJson(Map<String, dynamic> json) => UserReports(
        createdBy: json["createdBy"] == null ? null : json["createdBy"],
        userName: json["userName"] == null ? null : json["userName"],
        salesId: json["salesID"] == null ? null : json["salesID"],
        productId: json["productID"] == null ? null : json["productID"],
        productName: json["productName"] == null ? null : json["productName"],
        productPrice:
            json["productPrice"] == null ? null : json["productPrice"],
        quantity: json["quantity"] == null ? null : json["quantity"],
        amount: json["amount"] == null ? null : json["amount"],
      );

  Map<String, dynamic> toJson() => {
        "createdBy": createdBy == null ? null : createdBy,
        "salesID": salesId == null ? null : salesId,
        "productID": productId == null ? null : productId,
        "productName": productName == null ? null : productName,
        "productPrice": productPrice == null ? null : productPrice,
        "quantity": quantity == null ? null : quantity,
        "amount": amount == null ? null : amount,
      };
}
