class GetExpenseReportFromApi {
  int? status;
  String? message;
  ExpenseReportModel? result;

  GetExpenseReportFromApi({this.status, this.message, this.result});

  GetExpenseReportFromApi.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? new ExpenseReportModel.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.toJson();
    }
    return data;
  }
}

class ExpenseReportModel {
  String? workspaceId;
  int? totalExpenses;
  int? totalQuantity;
  double? totalAmount;
  List<Expenses>? reportList;

  ExpenseReportModel({this.workspaceId, this.totalExpenses, this.totalAmount, this.reportList});

  ExpenseReportModel.fromJson(Map<String, dynamic> json) {
    workspaceId = json['workspaceId'];
    totalExpenses = json['totalExpenses'];
    totalQuantity = json['totalQuantity'];
    totalAmount = json['totalAmount'];
    if (json['reportList'] != null) {
      reportList = <Expenses>[];
      json['reportList'].forEach((v) {
        reportList?.add(new Expenses.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['workspaceId'] = this.workspaceId;
    data['totalExpenses'] = this.totalExpenses;
    data['totalQuantity'] = this.totalQuantity;
    data['totalAmount'] = this.totalAmount;
    if (this.reportList != null) {
      data['reportList'] = this.reportList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Expenses {
  int? id;
  String? invoiceId;
  String? invoiceNo;
  int? quantity;
  String? createdDate;
  double? totalAmount;
  String? expId;
  String? expCategoryId;
  String? expCategoryName;
  String? invoiceExtension;
  Expenses({this.id, this.invoiceId, this.invoiceNo, this.createdDate, this.totalAmount, this.expId, this.expCategoryId, this.expCategoryName, this.invoiceExtension});

  Expenses.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    invoiceId = json['invoiceID'];
    quantity = json['quantity'];
    invoiceNo = json['invoiceNo'].toString();
    createdDate = json['createdDate'];
    totalAmount = json['totalAmount'];
    expId = json['expID'];
    expCategoryId = json['expCategoryID'];
    expCategoryName = json['expCategoryName'];
    invoiceExtension = json['invoiceExtension'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['invoiceID'] = this.invoiceId;
    data['quantity'] = this.quantity;
    data['invoiceNo'] = this.invoiceNo;
    data['createdDate'] = this.createdDate;
    data['totalAmount'] = this.totalAmount;
    data['expID'] = this.expId;
    data['expCategoryID'] = this.expCategoryId;
    data['expCategoryName'] = this.expCategoryName;
    data['invoiceExtension'] = this.invoiceExtension;
    return data;
  }
}
