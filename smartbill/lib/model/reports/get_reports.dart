import 'dart:convert';

import 'package:smartbill/model/reports.dart';

GetReportsFromApi getReportsFromApiFromJson(String str) =>
    GetReportsFromApi.fromJson(json.decode(str));

String getReportsFromApiToJson(GetReportsFromApi data) =>
    json.encode(data.toJson());

class GetReportsFromApi {
  GetReportsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  GetReports? result;

  factory GetReportsFromApi.fromJson(Map<String, dynamic> json) =>
      GetReportsFromApi(
        status: json["status"],
        message: json["message"],
        result: GetReports.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": result!.toJson(),
      };
}

class GetReports {
  GetReports({
    this.workspaceId,
    this.totalSales,
    this.totalQty,
    this.totalAmount,
    this.totalDiscount,
    this.reportList,
  });

  String? workspaceId;
  int? totalSales = 0;
  double? totalQty = 0.0;
  double? totalAmount = 0.0;
  double? totalDiscount = 0.0;
  List<Sales>? reportList;

  factory GetReports.fromJson(Map<String, dynamic> json) => GetReports(
        workspaceId: json["workspaceID"],
        totalSales: json["totalSales"],
        totalQty: json["totalQty"],
        totalAmount: json["totalAmount"],
        totalDiscount: json["totalDiscount"],
        reportList:
            List<Sales>.from(json["sales"].map((x) => Sales.fromJson(x))),
      );
  factory GetReports.fromDBJson(Map<String, dynamic> json) => GetReports(
        workspaceId: json["workspaceID"],
        totalSales: json["totalSales"],
        totalQty: json["totalQty"],
        totalAmount: json["totalAmount"],
        totalDiscount: json["totalDiscount"],
        reportList:
            List<Sales>.from(json["sales"].map((x) => Sales.fromMap(x))),
      );

  Map<String, dynamic> toJson() => {
        "workspaceID": workspaceId,
        "totalSales": totalSales,
        "totalQty": totalQty,
        "totalAmount": totalAmount,
        "totalDiscount": totalDiscount,
        "sales": List<dynamic>.from(reportList!.map((x) => x.toJson())),
      };
}
