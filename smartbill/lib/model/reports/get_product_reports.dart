// To parse this JSON data, do
//
//     final getAllProductDetailsbyProductId = getAllProductDetailsbyProductIdFromJson(jsonString);

import 'dart:convert';

GetProductReportsFromApi getAllProductDetailsbyProductIdFromJson(String str) =>
    GetProductReportsFromApi.fromJson(json.decode(str));

String getAllProductDetailsbyProductIdToJson(GetProductReportsFromApi data) =>
    json.encode(data.toJson());

class GetProductReportsFromApi {
  GetProductReportsFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<ProductReports>? result;

  factory GetProductReportsFromApi.fromJson(Map<String, dynamic> json) =>
      GetProductReportsFromApi(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null
            ? null
            : List<ProductReports>.from(
                json["result"].map((x) => ProductReports.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null
            ? null
            : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class ProductReports {
  ProductReports({
    this.productId,
    this.productName,
    this.keywordId,
    this.productPrice,
    this.quantity,
    this.amount,
  });

  String? productId;
  String? productName;
  dynamic keywordId;
  double? productPrice;
  double? quantity;
  double? amount;

  factory ProductReports.fromJson(Map<String, dynamic> json) => ProductReports(
        productId: json["productID"] == null ? null : json["productID"],
        productName: json["productName"] == null ? null : json["productName"],
        keywordId: json["keywordID"],
        productPrice:
            json["productPrice"] == null ? null : json["productPrice"],
        quantity: json["quantity"] == null ? null : json["quantity"],
        amount: json["amount"] == null ? null : json["amount"],
      );

  Map<String, dynamic> toJson() => {
        "productID": productId == null ? null : productId,
        "productName": productName == null ? null : productName,
        "keywordID": keywordId,
        "productPrice": productPrice == null ? null : productPrice,
        "quantity": quantity == null ? null : quantity,
        "amount": amount == null ? null : amount,
      };
}
