// // To parse this JSON data, do
// //
// //     final getTransactionsFromApi = getTransactionsFromApiFromJson(jsonString);

// import 'dart:convert';

// import '../transaction_model.dart';

// GetTransactionsFromApi getTransactionsFromApiFromJson(String str) =>
//     GetTransactionsFromApi.fromJson(json.decode(str));

// String getTransactionsFromApiToJson(GetTransactionsFromApi data) =>
//     json.encode(data.toJson());

// class GetTransactionsFromApi {
//   GetTransactionsFromApi({
//     this.status,
//     this.message,
//     this.result,
//   });

//   int status;
//   String message;
//   GetTransactions result;

//   factory GetTransactionsFromApi.fromJson(Map<String, dynamic> json) =>
//       GetTransactionsFromApi(
//         status: json["status"] == null ? null : json["status"],
//         message: json["message"] == null ? null : json["message"],
//         result: json["result"] == null
//             ? null
//             : GetTransactions.fromJson(json["result"]),
//       );

//   Map<String, dynamic> toJson() => {
//         "status": status == null ? null : status,
//         "message": message == null ? null : message,
//         "result": result == null ? null : result.toJson(),
//       };
// }

// class GetTransactions {
//   GetTransactions({
//     this.totalQty,
//     this.totalTransaction,
//     this.totalAmount,
//     this.totalDiscount,
//     this.salesTransaction,
//   });

//   int totalQty;
//   int totalTransaction;
//   int totalAmount;
//   int totalDiscount;
//   List<Transactions> salesTransaction;

//   factory GetTransactions.fromJson(Map<String, dynamic> json) =>
//       GetTransactions(
//         totalQty: json["totalQty"] == null ? null : json["totalQty"],
//         totalTransaction:
//             json["totalTransaction"] == null ? null : json["totalTransaction"],
//         totalAmount: json["totalAmount"] == null ? null : json["totalAmount"],
//         totalDiscount:
//             json["totalDiscount"] == null ? null : json["totalDiscount"],
//         salesTransaction: json["salesTransaction"] == null
//             ? null
//             : List<Transactions>.from(
//                 json["salesTransaction"].map((x) => Transactions.fromJson(x))),
//       );

//   Map<String, dynamic> toJson() => {
//         "totalQty": totalQty == null ? null : totalQty,
//         "totalTransaction": totalTransaction == null ? null : totalTransaction,
//         "totalAmount": totalAmount == null ? null : totalAmount,
//         "totalDiscount": totalDiscount == null ? null : totalDiscount,
//         "salesTransaction": salesTransaction == null
//             ? null
//             : List<dynamic>.from(salesTransaction.map((x) => x.toJson())),
//       };
// }
