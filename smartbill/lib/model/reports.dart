import 'dart:convert';

List<Sales> salesFromJson(String str) => List<Sales>.from(json.decode(str).map((x) => Sales.fromJson(x)));

String salesToJson(List<Sales> data) => json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Sales {
  int? id;
  String? holdSalesName;
  String? salesId;
  String? invoiceId;
  String? date;
  String? totalAmount;
  String? paymentType;
  int? taxMode;
  int? sync;
  int? status;
  String? message;
  String? workspaceId;
  int? rowStatus;
  bool? isActive;
  String? customerName;
  int? isActiveSale;
  String? fkTableID;
  String? tableName;
  String? createdBy;
  String? discountFormula;
  String? discountPrice;
  String? invoiceNo;
  String? customerID;
  String? mobileNo;
  String? invoiceExtension;
  String? transactionQty;
  int? orderType;
  String? onlinePlatformID;
  String? onlineRefNo;
  String? paymentTypeName;
  String? paymentID;
  String? paymentInvoice;
  int? paymentCategoryID;
  String? salesCategoryId;
  String? deletedBy;
  String? deletedDate;
  String? deletedReason;
  String? deletedUserName;
  String? salesCatName;
  String? shiftId;
  String? shiftName;
  String? firstName;

  String? deliveryDateAndTime;
  //Model only
  String? paymentName;
  double? returnAmount;
  String? givenAmount;
  bool? isIGST;
  String? balance;
  String? parcelAmount;

  ///1 ===== Completed
  ///2 ===== Not completed
  int? salesStatus;
  Sales(
      {this.id,
      this.holdSalesName,
      this.salesId,
      this.invoiceId,
      this.date,
      this.totalAmount,
      this.paymentType,
      this.sync,
      this.status,
      this.taxMode,
      this.message,
      this.workspaceId,
      this.rowStatus,
      this.isActive,
      this.customerName,
      this.fkTableID,
      this.isActiveSale,
      this.tableName,
      this.createdBy,
      this.discountFormula,
      this.discountPrice,
      this.invoiceNo,
      this.customerID,
      this.mobileNo,
      this.invoiceExtension,
      this.transactionQty,
      this.parcelAmount,
      this.orderType,
      this.onlinePlatformID,
      this.paymentTypeName,
      this.paymentID,
      this.onlineRefNo,
      this.salesStatus,
      this.paymentInvoice,
      this.paymentCategoryID,
      this.deletedBy,
      this.deletedDate,
      this.deletedReason,
      this.deletedUserName,
      this.salesCategoryId,
      this.salesCatName,
      this.shiftId,
      this.shiftName,
      this.firstName,
      this.deliveryDateAndTime});

  Sales.fromMap(Map<String, dynamic> map) {
    id = map['salesDetailsId'];
    holdSalesName = map['holdSalesName'];
    salesId = map['salesId'];
    date = map['date'];
    totalAmount = map['totalAmount'].toString();
    paymentType = map['salesPaymentType'] != null ? map['salesPaymentType'].toString() : null;
    paymentName = map['paymentName'] != null ? map['paymentName'] : null;
    taxMode = map['salesTaxMode'];
    sync = map['salesSync'];
    workspaceId = map['workspaceId'];
    rowStatus = map['rowStatus'];
    invoiceId = map['invoiceId'];
    customerName = map['customerName'];
    isActiveSale = map['isActiveSale'];
    fkTableID = map['fkTableID'];
    createdBy = map['createdBy'];
    discountFormula = map['discountFormula'];
    discountPrice = map['discountPrice'];
    invoiceNo = map['invoiceNo'];
    customerID = map['FK_CustomerID'];
    mobileNo = map['CustomerMobileNo'];
    transactionQty = map["transactionQty"] == null ? null : map["transactionQty"].toString();
    parcelAmount = map["parcelAmount"] == null ? null : map["parcelAmount"].toString();
    orderType = map['orderType'];
    onlinePlatformID = map['onlinePlatformID'];
    onlineRefNo = map['onlineRefNo'];
    paymentTypeName = map['paymentTypeName'];
    paymentID = map['paymentID'];
    paymentInvoice = map["paymentInvoice"];
    paymentCategoryID = map["paymentCategoryID"];
    salesStatus = map["salesStatus"];
    deletedBy = map["deletedBy"];
    deletedDate = map["deletedDate"];
    deletedReason = map["deletedReason"];
    deletedUserName = map["deletedUserName"];
    invoiceExtension = map["invoiceExtension"];
    salesCategoryId = map["salesCategoryId"];
    salesCatName = map["salesCatName"];
    shiftId = map["shiftId"];
    shiftName = map["shiftName"];
    firstName = map["firstName"];
    deliveryDateAndTime = map["deliveryDateAndTime"];
  }

  factory Sales.fromJson(Map<String, dynamic> json) => Sales(
        salesId: json["salesID"] == null ? null : json["salesID"],
        invoiceId: json["invoiceID"] == null ? null : json["invoiceID"],
        date: json["date"] == null ? null : json["date"],
        totalAmount: json["totalAmount"] == null ? null : json["totalAmount"].toString(),
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        taxMode: json["taxMode"] == null ? null : json["taxMode"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        isActive: json["isActive"] == null ? null : json["isActive"],
        customerName: json["customerName"] == null ? null : json["customerName"],
        isActiveSale: json["isActiveSale"] == null ? null : json["isActiveSale"],
        fkTableID: json["tableID"] == null ? null : json["tableID"],
        paymentType: json["paymentType"] == null ? null : json["paymentType"].toString(),
        createdBy: json["createdBy"] == null ? null : json["createdBy"],
        discountFormula: json["discountFormula"] == null ? null : json["discountFormula"],
        discountPrice: json["discountPrice"] == null ? null : json["discountPrice"],
        invoiceNo: json["invoiceNo"] == null ? null : json["invoiceNo"].toString(),
        customerID: json["customerID"] == null ? null : json["customerID"],
        mobileNo: json["mobileNo"] == null ? null : json["mobileNo"],
        invoiceExtension: json["invoiceExtension"] == null ? null : json["invoiceExtension"].toString(),
        transactionQty: json["transactionQty"] == null ? null : json["transactionQty"].toString(),
        parcelAmount: json["parcelAmount"] == null ? null : json["parcelAmount"].toString(),
        orderType: json["orderType"] == null ? null : json["orderType"],
        onlinePlatformID: json["onlinePlatformID"] == null ? null : json["onlinePlatformID"],
        onlineRefNo: json["onlineRefNo"] == null ? null : json["onlineRefNo"],
        paymentTypeName: json["paymentTypeName"] == null ? null : json["paymentTypeName"],
        paymentID: json["paymentID"] == null ? null : json["paymentID"],
        paymentInvoice: json["paymentInvoice"] == null ? null : json["paymentInvoice"],
        paymentCategoryID: json["paymentCategoryID"] == null ? null : json["paymentCategoryID"],
        salesStatus: json["salesStatus"] == null ? null : json["salesStatus"],
        deletedBy: json["deletedBy"] == null ? null : json["deletedBy"],
        deletedDate: json["deletedDate"] == null ? null : json["deletedDate"],
        deletedReason: json["deletedReason"] == null ? null : json["deletedReason"],
        deletedUserName: json["deletedUserName"] == null ? null : json["deletedUserName"],
        salesCategoryId: json["salesCategoryId"] == null ? null : json["salesCategoryId"],
        salesCatName: json["salesCatName"] == null ? null : json["salesCatName"],
        shiftId: json["shiftId"] == null ? null : json["shiftId"],
        shiftName: json["shiftName"] == null ? null : json["shiftName"],
        firstName: json["firstName"] == null ? null : json["firstName"],
        deliveryDateAndTime: json["deliveryDateAndTime"] == null ? null : json["deliveryDateAndTime"],
      );

  Map<String, dynamic> toJson() => {
        "salesID": salesId == null ? null : salesId,
        "invoiceID": invoiceId == null ? null : invoiceId,
        "date": date == null ? null : date,
        "totalAmount": totalAmount == null ? null : totalAmount,
        "paymentType": paymentType == null ? null : paymentType.toString(),
        "taxMode": taxMode == null ? null : taxMode,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "status": rowStatus == null ? null : rowStatus,
        "customerName": customerName == null ? null : customerName,
        "isActiveSale": isActiveSale == null ? null : isActiveSale,
        "tableID": fkTableID == null ? null : fkTableID,
        "createdBy": createdBy == null ? null : createdBy,
        "discountFormula": discountFormula == null ? null : discountFormula,
        "discountPrice": discountPrice == null ? null : discountPrice,
        "customerID": customerID == null ? null : customerID,
        "mobileNo": mobileNo == null ? null : mobileNo,
        "parcelAmount": parcelAmount == null ? null : parcelAmount,
        "orderType": orderType == null ? null : orderType,
        "onlinePlatformID": onlinePlatformID == null ? null : onlinePlatformID,
        "onlineRefNo": onlineRefNo == null ? null : onlineRefNo,
        "paymentTypeName": paymentTypeName == null ? null : paymentTypeName,
        "paymentID": paymentID == null ? null : paymentID,
        "paymentInvoice": paymentInvoice == null ? null : paymentInvoice,
        "paymentCategoryID": paymentCategoryID == null ? null : paymentCategoryID,
        "salesStatus": salesStatus == null ? null : salesStatus,
        "deletedBy": deletedBy == null ? null : deletedBy,
        "deletedDate": deletedDate == null ? null : deletedDate,
        "deletedReason": deletedReason == null ? null : deletedReason,
        "deletedUserName": deletedUserName == null ? null : deletedUserName,
        "salesCategoryId": salesCategoryId == null ? null : salesCategoryId,
        "salesCatName": salesCatName == null ? null : salesCatName,
        "shiftId": shiftId == null ? null : shiftId,
        "firstName": firstName == null ? null : firstName,
        "deliveryDateAndTime": deliveryDateAndTime == null ? null : deliveryDateAndTime,
      };
}

GetSalesListFromApi getSalesListFromApiFromJson(String str) => GetSalesListFromApi.fromJson(json.decode(str));

String getSalesListFromApiToJson(GetSalesListFromApi data) => json.encode(data.toJson());

class GetSalesListFromApi {
  GetSalesListFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Sales>? result;

  factory GetSalesListFromApi.fromJson(Map<String, dynamic> json) => GetSalesListFromApi(
        status: json["status"],
        message: json["message"],
        result: List<Sales>.from(json["result"].map((x) => Sales.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}
