// To parse this JSON data, do
//
//     final addWorkspace = addWorkspace<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';

import 'package:smartbill/model/workspace.dart';

AddWorkspace addWorkspaceFromJson(String str) =>
    AddWorkspace.fromJson(json.decode(str));

String addWorkspaceTo<PERSON>son(AddWorkspace data) => json.encode(data.toJson());

class AddWorkspace {
  AddWorkspace({this.workspaceName, this.shopTypeId, this.status, this.message, this.result});

  String? workspaceName;
  int? shopTypeId;
  int? status;
  String? message;
  WorkSpace? result;

  factory AddWorkspace.fromJson(Map<String, dynamic> json) => AddWorkspace(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result:
            json["result"] == null ? null : WorkSpace.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() =>
      {
      "workspaceName": workspaceName == null ? null : workspaceName,
       "shopTypeId": shopTypeId == null ? null : shopTypeId,
      };
}
