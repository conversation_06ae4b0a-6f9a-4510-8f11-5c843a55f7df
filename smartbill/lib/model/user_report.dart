class UserReport {
  String? salesId;
  String? createdBy;
  String? prodId;
  String? name;
  String? price;
  String? amount;
  String? qty;
  String? userName;

  UserReport.fromMap(Map<String, dynamic> map) {
    salesId = map['fkSalesId'];
    prodId = map['salesProductId'];
    name = map['salesProductName'];
    qty = map['quantity'].toString();
    price = map['price'];
    amount = map['amount'].toString();
    salesId = map['salesId'];
    createdBy = map['createdBy'];
  }
}
