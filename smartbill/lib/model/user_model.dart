// To parse this JSON data, do
//
//     final loginResponse = loginResponse<PERSON>rom<PERSON><PERSON>(jsonString);

import 'dart:convert';

import 'package:smartbill/model/workspace.dart';

LoginResponse loginResponseFromJson(String str) =>
    LoginResponse.fromJson(json.decode(str));

String loginResponseToJson(LoginResponse data) => json.encode(data.toJson());

User userFromJson(String str) => User.fromJson(json.decode(str));

String userToJson(User data) => json.encode(data.toJson());

class LoginResponse {
  LoginResponse({
    this.status,
    this.message,
    this.response,
  });

  int? status;
  String? message;
  User? response;

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        response:
            json["response"] == null ? null : User.fromJson(json["response"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "response": response == null ? null : response?.toJson(),
      };
}

class User {
  User({
    this.id,
    this.username,
    this.firstName,
    this.lastName,
    this.mobile,
    this.token,
    this.email,
    this.salesCount,
    this.workspaceDetails,
  });

  int? id;
  String? username;
  String? firstName;
  String? lastName;
  String? mobile;
  String? token;
  String? email;
  int? salesCount;
  List<WorkSpace>? workspaceDetails;

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json["id"] == null ? null : json["id"],
        username: json["username"] == null ? null : json["username"],
        firstName: json["firstName"] == null ? null : json["firstName"],
        lastName: json["lastName"] == null ? null : json["lastName"],
        mobile: json["mobile"] == null ? null : json["mobile"],
        token: json["token"] == null ? null : json["token"],
        email: json["email"] == null ? null : json["email"],
        salesCount: json["salesCount"] == null ? null : json["salesCount"],
        workspaceDetails: json["workspaceDetails"] == null
            ? null
            : List<WorkSpace>.from(
                json["workspaceDetails"].map((x) => WorkSpace.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "username": username == null ? null : username,
        "firstName": firstName == null ? null : firstName,
        "lastName": lastName == null ? null : lastName,
        "mobile": mobile == null ? null : mobile,
        "token": token == null ? null : token,
        "email": email == null ? null : email,
        "salesCount": salesCount == null ? null : salesCount,
        "workspaceDetails": workspaceDetails == null
            ? null
            : List<dynamic>.from(workspaceDetails!.map((x) => x.toJson())),
      };

  static Map<String, dynamic> toMap(User user) => {
        "id": user.id == null ? null : user.id,
        "username": user.username == null ? null : user.username,
        "firstName": user.firstName == null ? null : user.firstName,
        "lastName": user.lastName == null ? null : user.lastName,
        "mobile": user.mobile == null ? null : user.mobile,
        "token": user.token == null ? null : user.token,
        "email": user.email == null ? null : user.email,
        "salesCount": user.salesCount == null ? null : user.salesCount,
        "workspaceDetails": user.workspaceDetails == null
            ? null
            : List<dynamic>.from(user.workspaceDetails!.map((x) => x.toJson())),
      };
  static String encode(List<User> users) => json.encode(
        users.map<Map<String, dynamic>>((user) => User.toMap(user)).toList(),
      );

  static List<User> decode(String users) =>
      (json.decode(users) as List<dynamic>)
          .map<User>((item) => User.fromJson(item))
          .toList();
}

// class WorkspaceDetail {
//   WorkspaceDetail({
//     this.id,
//     this.workspaceId,
//     this.workspaceName,
//     this.userId,
//     this.isActive,
//   });
//
//   int id;
//   String workspaceId;
//   String workspaceName;
//   int userId;
//   bool isActive;
//
//   factory WorkspaceDetail.fromJson(Map<String, dynamic> json) =>
//       WorkspaceDetail(
//         id: json["id"] == null ? null : json["id"],
//         workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
//         workspaceName:
//             json["workspaceName"] == null ? null : json["workspaceName"],
//         userId: json["userID"] == null ? null : json["userID"],
//         isActive: json["isActive"] == null ? null : json["isActive"],
//       );
//
//   Map<String, dynamic> toJson() => {
//         "id": id == null ? null : id,
//         "workspaceID": workspaceId == null ? null : workspaceId,
//         "workspaceName": workspaceName == null ? null : workspaceName,
//         "userID": userId == null ? null : userId,
//         "isActive": isActive == null ? null : isActive,
//       };
// }

class SubscriptionDetail {
  SubscriptionDetail({
    this.applicationId,
    this.deviceName,
    this.mobileId,
    this.activations,
  });

  int? applicationId;
  String? deviceName;
  String? mobileId;
  List<Activation>? activations;

  factory SubscriptionDetail.fromJson(Map<String, dynamic> json) =>
      SubscriptionDetail(
        applicationId:
            json["applicationID"] == null ? null : json["applicationID"],
        deviceName: json["deviceName"] == null ? null : json["deviceName"],
        mobileId: json["mobileID"] == null ? null : json["mobileID"],
        activations: json["activations"] == null
            ? null
            : List<Activation>.from(
                json["activations"].map((x) => Activation.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "applicationID": applicationId == null ? null : applicationId,
        "deviceName": deviceName == null ? null : deviceName,
        "mobileID": mobileId == null ? null : mobileId,
        "activations": activations == null
            ? null
            : List<dynamic>.from(activations!.map((x) => x.toJson())),
      };
}

class Activation {
  Activation({
    this.activeDate,
    this.expiryDate,
  });

  String? activeDate;
  String? expiryDate;

  factory Activation.fromJson(Map<String, dynamic> json) => Activation(
        activeDate: json["activeDate"] == null ? null : json["activeDate"],
        expiryDate: json["expiryDate"] == null ? null : json["expiryDate"],
      );

  Map<String, dynamic> toJson() => {
        "activeDate": activeDate == null ? null : activeDate,
        "expiryDate": expiryDate == null ? null : expiryDate,
      };
}

class ProfileUpdateResponse {
  ProfileUpdateResponse({this.status, this.message, this.result});

  int? status;
  String? message;
  int? result;

  factory ProfileUpdateResponse.fromJson(Map<String, dynamic> json) =>
      ProfileUpdateResponse(
          status: json["status"] == null ? null : json["status"],
          message: json["message"] == null ? null : json["message"],
          result: json["result"] == null ? null : json["result"]);
}
