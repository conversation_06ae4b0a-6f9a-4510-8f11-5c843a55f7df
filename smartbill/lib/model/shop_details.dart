import '../utils/dbutils/db_strings.dart';

class ShopDeatails {
  int? id;
  String? name;
  String? address1;
  String? address2;
  String? address3;
  String? footerText;
  String? footerText2;
  String? gstNo;
  String? workspaceId;
  int? shopSync;
  int? status;
  String? imageID;

  ShopDeatails(
      {this.id,
      this.name,
      this.address1,
      this.address2,
      this.address3,
      this.gstNo,
      this.footerText,
      this.footerText2,
      this.workspaceId,
      this.shopSync,
      this.status,
      this.imageID});
  Map<String, dynamic> toMap() {
    return {
      db_shop_shopId: id,
      db_shop_shopName: name,
      db_shop_shopAddress1: address1,
      db_shop_shopAddress2: address2,
      db_shop_shopAddress3: address3,
      db_shop_shopGstNo: gstNo,
      db_shop_shopFooterText: footerText,
      db_shop_shopFooterText2: footerText2,
      db_workspace_workspaceId: workspaceId,
      db_shop_shopSync: shopSync,
      db_product_rowStatus: status,
      db_shop_shopImageID: imageID
    };
  }

  ShopDeatails.fromMap(Map<String, dynamic> map) {
    id = map['shopId'];
    name = map['shopName'];
    address1 = map['shopAddress1'];
    address2 = map['shopAddress2'];
    address3 = map['shopAddress3'];
    gstNo = map['shopGstNo'];
    footerText = map['shopfooterText'];
    footerText2 = map['shopFooterText2'];
    workspaceId = map['workspaceId'];
    shopSync = map['shopSync'];
    status = map['rowStatus'];
    imageID = map['shopImageID'];
  }

  factory ShopDeatails.fromJson(Map<String, dynamic> json) => ShopDeatails(
      name: json["shopName"] == null ? null : json["shopName"],
      address1: json["shopAddress"] == null ? null : json["shopAddress"],
      address2: json["shopAddressMoreInfo"] == null
          ? null
          : json["shopAddressMoreInfo"],
      address3: json["shopAddress3"] == null ? null : json["shopAddress3"],
      gstNo: json["gstNo"] == null ? null : json["gstNo"],
      footerText:
          json["shopFooterText"] == null ? null : json["shopFooterText"],
      footerText2:
          json["shopFooterText1"] == null ? null : json["shopFooterText1"],
      workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
      status: json["status"] == null ? null : json["status"],
      imageID: json["imageID"] == null ? null : json["imageID"]);

  Map<String, dynamic> toJson() => {
        "shopName": name == null ? null : name,
        "shopAddress": address1 == null ? null : address1,
        "shopAddressMoreInfo": address2 == null ? null : address2,
        "shopAddress3": address3 == null ? null : address3,
        "gstNo": gstNo == null ? null : gstNo,
        "shopFooterText": footerText == null ? null : footerText,
        "shopFooterText1": footerText2 == null ? null : footerText2,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "status": status == null ? null : status,
        "imageID": imageID == null ? null : imageID
      };
}
