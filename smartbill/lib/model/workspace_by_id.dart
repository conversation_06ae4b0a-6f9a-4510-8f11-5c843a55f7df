// To parse this JSON data, do
//
//     final workspaceById = workspaceByIdFrom<PERSON>son(jsonString);

import 'dart:convert';

WorkspaceById workspaceByIdFromJson(String str) =>
    WorkspaceById.fromJson(json.decode(str));

String workspaceByIdToJson(WorkspaceById data) => json.encode(data.toJson());

class WorkspaceById {
  WorkspaceById({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  Result? result;

  factory WorkspaceById.fromJson(Map<String, dynamic> json) => WorkspaceById(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : Result.fromJson(json["result"]),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null ? null : result?.toJson(),
      };
}

class Result {
  Result({
    this.id,
    this.workspaceId,
    this.workspaceName,
    this.isActive,
    this.expiryDate,
  });

  int? id;
  String? workspaceId;
  String? workspaceName;
  bool? isActive;
  DateTime? expiryDate;

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        id: json["id"] == null ? null : json["id"],
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        workspaceName:
            json["workspaceName"] == null ? null : json["workspaceName"],
        isActive: json["isActive"] == null ? null : json["isActive"],
        expiryDate: json["expiryDate"] == null
            ? null
            : DateTime.parse(json["expiryDate"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "workspaceName": workspaceName == null ? null : workspaceName,
        "isActive": isActive == null ? null : isActive,
        "expiryDate": expiryDate == null ? null : expiryDate?.toIso8601String(),
      };
}
