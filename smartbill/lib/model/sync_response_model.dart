import 'dart:convert';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/screens/expenses/model/expenses_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_transactions_model.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import '../screens/expenses/model/expense_category_model.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/sales_payment_mapping.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import '../screens/salescategory/model/sales_category_model.dart';
import 'general_settings.dart';
import 'stocks.dart';
import 'toppings_model.dart';
import 'area.dart';
import 'customers.dart';
import 'discounts.dart';

GetSyncResponse syncResponseFromJson(String str) => GetSyncResponse.fromJson(json.decode(str));

class GetSyncResponse {
  GetSyncResponse({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  SyncResponse? result;

  factory GetSyncResponse.fromJson(Map<String, dynamic> json) => GetSyncResponse(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : SyncResponse.fromJson(json["result"]),
      );
}

class SyncResponse {
  SyncResponse(
      {this.workspaceId,
      this.commits,
      this.products,
      this.productKeywords,
      this.priceTypes,
      this.toppingsGroup,
      this.topping,
      this.sales,
      this.salesTransactions,
      this.salesTransactionStagging,
      this.workspaceSettings,
      this.shopDetails,
      this.shopTables,
      this.salesCount,
      this.paymentType,
      this.generalSettings,
      this.stocksList,
      this.stockLogsList,
      this.discountList,
      this.workspaceUserSettings,
      this.customerList,
      this.areaList,
      this.balanceList,
      this.salesTypeList,
      this.salesPaymentMappingList,
      this.onlinePlatform,
      this.expiryDate,
      this.pageCount,
      this.expenseProduct,
      this.expenseCategory,
      this.expenses,
      this.expenseTransaction,
      this.purchase,
      this.purchaseTransaction,
      this.isResetDemoWorkspace,
      this.salesCategory,
      this.shiftWiseSales,
      this.advanceOrderNotes});

  String? workspaceId;
  List<Commit>? commits;
  List<Products>? products;
  List<ProductKey>? productKeywords;
  List<PriceUnitType>? priceTypes;
  List<ToppingsGroup>? toppingsGroup;
  List<Topping>? topping;
  List<Sales>? sales;
  List<Transactions>? salesTransactions;
  List<Transactions>? salesTransactionStagging;
  List<WorkspaceSettings>? workspaceSettings;
  List<ShopTable>? shopTables;
  ShopDeatails? shopDetails;
  int? salesCount;
  List<PaymentType>? paymentType;
  List<GeneralSetting>? generalSettings;
  List<Stocks>? stocksList;
  List<StockLogs>? stockLogsList;
  List<Discounts>? discountList;
  List<WorkspaceSettings>? workspaceUserSettings;
  List<Customer>? customerList;
  List<Area>? areaList;
  List<Balance>? balanceList;
  List<SalesType>? salesTypeList;
  List<SalesPaymentMapping>? salesPaymentMappingList;
  List<OnlinePlatformModel>? onlinePlatform;
  String? expiryDate;
  int? pageCount;
  List<ExpensesProduct>? expenseProduct;
  List<ExpensesCategoryModel>? expenseCategory;
  List<ExpensesModel>? expenses;
  List<ExpenseTransactionModel>? expenseTransaction;
  List<PurchaseModel>? purchase;
  List<PurchaseTransactionsModel>? purchaseTransaction;
  List<SalesCategoryModel>? salesCategory;
  // List<ShiftwiseSalesModel> shiftSales;
  bool? isResetDemoWorkspace;
  List<ShiftwiseSalesModel>? shiftWiseSales;
  List<AdvanceOrderNoteModel>? advanceOrderNotes;

  factory SyncResponse.fromJson(Map<String, dynamic> json) => SyncResponse(
        workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
        salesCount: json["salesCount"] == null ? null : json["salesCount"],
        isResetDemoWorkspace: json["isResetDemoWorkspace"] == null ? null : json["isResetDemoWorkspace"],
        commits: json["commits"] == null ? null : List<Commit>.from(json["commits"].map((x) => Commit.fromJson(x))),
        products: json["products"] == null ? null : List<Products>.from(json["products"].map((x) => Products.fromJson(x))),
        productKeywords: json["productKeywords"] == null ? null : List<ProductKey>.from(json["productKeywords"].map((x) => ProductKey.fromJson(x))),
        priceTypes: json["priceTypes"] == null ? null : List<PriceUnitType>.from(json["priceTypes"].map((x) => PriceUnitType.fromJson(x))),
        toppingsGroup: json["toppingsGroup"] == null ? null : List<ToppingsGroup>.from(json["toppingsGroup"].map((x) => ToppingsGroup.fromJson(x))),
        topping: json["toppings"] == null ? null : List<Topping>.from(json["toppings"].map((x) => Topping.fromJson(x))),
        sales: json["sales"] == null ? null : List<Sales>.from(json["sales"].map((x) => Sales.fromJson(x))),
        salesTransactions:
            json["salesTransactions"] == null ? null : List<Transactions>.from(json["salesTransactions"].map((x) => Transactions.fromJson(x))),
        salesTransactionStagging: json["salesTransactionStagging"] == null
            ? null
            : List<Transactions>.from(json["salesTransactionStagging"].map((x) => Transactions.fromJson(x))),
        workspaceSettings: json["workspaceSettings"] == null
            ? null
            : List<WorkspaceSettings>.from(json["workspaceSettings"].map((x) => WorkspaceSettings.fromJson(x))),
        shopDetails: json["shopDetails"] == null ? null : ShopDeatails.fromJson(json["shopDetails"]),
        shopTables: json["shopTable"] == null ? null : List<ShopTable>.from(json["shopTable"].map((x) => ShopTable.fromJson(x))),
        paymentType: json["paymentType"] == null ? null : List<PaymentType>.from(json["paymentType"].map((x) => PaymentType.fromJson(x))),
        generalSettings:
            json["generalSetting"] == null ? null : List<GeneralSetting>.from(json["generalSetting"].map((x) => GeneralSetting.fromJson(x))),
        stocksList: json["stocks"] == null ? null : List<Stocks>.from(json["stocks"].map((x) => Stocks.fromJson(x))),
        stockLogsList: json["stockLogs"] == null ? null : List<StockLogs>.from(json["stockLogs"].map((x) => StockLogs.fromJson(x))),
        discountList: json["discount"] == null ? null : List<Discounts>.from(json["discount"].map((x) => Discounts.fromJson(x))),
        workspaceUserSettings: json["workspaceUserSettings"] == null
            ? null
            : List<WorkspaceSettings>.from(json["workspaceUserSettings"].map((x) => WorkspaceSettings.fromJson(x))),
        customerList: json["customer"] == null ? null : List<Customer>.from(json["customer"].map((x) => Customer.fromJson(x))),
        areaList: json["area"] == null ? null : List<Area>.from(json["area"].map((x) => Area.fromJson(x))),
        balanceList: json["customerBalance"] == null ? null : List<Balance>.from(json["customerBalance"].map((x) => Balance.fromJson(x))),
        salesTypeList:
            json["customerTransaction"] == null ? null : List<SalesType>.from(json["customerTransaction"].map((x) => SalesType.fromJson(x))),
        salesPaymentMappingList: json["salesPaymentMapping"] == null
            ? null
            : List<SalesPaymentMapping>.from(json["salesPaymentMapping"].map((x) => SalesPaymentMapping.fromJson1(x))),
        onlinePlatform: json["onlinePlatform"] == null
            ? null
            : List<OnlinePlatformModel>.from(json["onlinePlatform"].map((x) => OnlinePlatformModel.fromJson(x))),
        expiryDate: json["expiryDate"] == null ? null : json["expiryDate"],
        pageCount: json["pageCount"] == null ? null : json["pageCount"],
        expenseProduct:
            json["expenseProduct"] == null ? null : List<ExpensesProduct>.from(json["expenseProduct"].map((x) => ExpensesProduct.fromJson(x))),
        expenseCategory: json["expenseCategory"] == null
            ? null
            : List<ExpensesCategoryModel>.from(json["expenseCategory"].map((x) => ExpensesCategoryModel.fromJson(x))),
        expenses: json["expenses"] == null ? null : List<ExpensesModel>.from(json["expenses"].map((x) => ExpensesModel.fromJson(x))),
        expenseTransaction: json["expenseTransaction"] == null
            ? null
            : List<ExpenseTransactionModel>.from(json["expenseTransaction"].map((x) => ExpenseTransactionModel.fromJson(x))),
        purchase: json["purchase"] == null ? null : List<PurchaseModel>.from(json["purchase"].map((x) => PurchaseModel.fromJson(x))),
        purchaseTransaction: json["purchaseTransaction"] == null
            ? null
            : List<PurchaseTransactionsModel>.from(json["purchaseTransaction"].map((x) => PurchaseTransactionsModel.fromJson(x))),
        salesCategory:
            json["salesCategory"] == null ? null : List<SalesCategoryModel>.from(json["salesCategory"].map((x) => SalesCategoryModel.fromJson(x))),
        shiftWiseSales: json["shiftWiseSales"] == null
            ? null
            : List<ShiftwiseSalesModel>.from(json["shiftWiseSales"].map((x) => ShiftwiseSalesModel.fromJson(x))),
        advanceOrderNotes: json["advanceOrderNotes"] == null
            ? null
            : List<AdvanceOrderNoteModel>.from(json["advanceOrderNotes"].map((x) => AdvanceOrderNoteModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "workspaceID": workspaceId == null ? null : workspaceId,
        // "resetWorkSpace": resetWorkSpace == null ? null : resetWorkSpace,
        "commits": commits == null ? null : List<dynamic>.from(commits!.map((x) => x.toJson())),
        "products": products == null ? null : List<dynamic>.from(products!.map((x) => x.toJson())),
        "productKeywords": productKeywords == null ? null : List<dynamic>.from(productKeywords!.map((x) => x.toJson())),
        "priceTypes": priceTypes == null ? null : List<dynamic>.from(priceTypes!.map((x) => x.toJson())),
        "toppingsGroup": toppingsGroup == null ? null : List<dynamic>.from(toppingsGroup!.map((x) => x.toJson())),
        "toppings": topping == null ? null : List<dynamic>.from(topping!.map((x) => x.toJson())),
        "sales": sales == null ? null : List<dynamic>.from(sales!.map((x) => x.toJson())),
        "salesTransactions": salesTransactions == null ? null : List<dynamic>.from(salesTransactions!.map((x) => x.toJson())),
        "salesTransactionStagging": salesTransactionStagging == null ? null : List<dynamic>.from(salesTransactionStagging!.map((x) => x.toJson())),
        "shopTable": shopTables == null ? null : List<dynamic>.from(shopTables!.map((x) => x.toJson())),
        "paymentType": paymentType == null ? null : List<dynamic>.from(paymentType!.map((x) => x.toJson())),
        "stocks": stocksList == null ? null : List<dynamic>.from(stocksList!.map((x) => x.toJson())),
        "stockLogs": stockLogsList == null ? null : List<dynamic>.from(stockLogsList!.map((x) => x.toJson())),
        "discount": discountList == null ? null : List<dynamic>.from(discountList!.map((x) => x.toJson())),
        "customer": customerList == null ? null : List<dynamic>.from(customerList!.map((x) => x.toJson())),
        "area": areaList == null ? null : List<dynamic>.from(areaList!.map((x) => x.toJson())),
        "customerTransaction": salesTypeList == null ? null : List<dynamic>.from(salesTypeList!.map((x) => x.toJson())),
        "salesPaymentMapping": salesPaymentMappingList == null ? null : List<dynamic>.from(salesPaymentMappingList!.map((x) => x.toJson())),
        "onlinePlatform": salesTypeList == null ? null : List<dynamic>.from(onlinePlatform!.map((x) => x.toJson())),
        "expenseProduct": expenseProduct == null ? null : List<dynamic>.from(expenseProduct!.map((x) => x.toJson())),
        "expenseCategory": expenseCategory == null ? null : List<dynamic>.from(expenseCategory!.map((x) => x.toJson())),
        "expenses": expenses == null ? null : List<dynamic>.from(expenses!.map((x) => x.toJson())),
        "expenseTransaction": expenseTransaction == null ? null : List<dynamic>.from(expenseTransaction!.map((x) => x.toJson())),
        "purchase": purchase == null ? null : List<dynamic>.from(purchase!.map((x) => x.toJson())),
        "purchaseTransaction": purchaseTransaction == null ? null : List<dynamic>.from(purchaseTransaction!.map((x) => x.toJson())),
        "salesCategory": salesCategory == null ? null : List<dynamic>.from(salesCategory!.map((x) => x.toJson())),
        "shiftWiseSales": shiftWiseSales == null ? null : List<ShiftwiseSalesModel>.from(shiftWiseSales!.map((x) => x.toJson())),
        "advanceOrderNotes": advanceOrderNotes == null ? null : List<AdvanceOrderNoteModel>.from(advanceOrderNotes!.map((x) => x.toJson())),
      };
}

class Commit {
  Commit({
    this.commitId,
    this.commitDate,
  });

  String? commitId;
  String? commitDate;

  factory Commit.fromJson(Map<String, dynamic> json) => Commit(
        commitId: json["commitID"] == null ? null : json["commitID"],
        commitDate: json["commitDate"] == null ? null : json["commitDate"],
      );

  Map<String, dynamic> toJson() => {
        "commitID": commitId == null ? null : commitId,
        "commitDate": commitDate == null ? null : commitDate,
      };
}
