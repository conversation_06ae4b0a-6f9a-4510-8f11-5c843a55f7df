import 'dart:convert';

List<Transactions> transactionsFromJson(String str) => List<Transactions>.from(
    json.decode(str).map((x) => Transactions.fromJson(x)));

String transactionsToJson(List<Transactions> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Transactions {
  int? id;
  String? transactId;
  String? salesId;
  String? prodId;
  String? name;
  String? price;
  String? actualPrice;
  String? amount;
  String? amountWithoutGst;
  String? qty;
  String? productType;
  String? toppingId;
  String? cGst;
  String? cGstAmount;
  String? sGst;
  String? sGstAmount;
  int? sync;
  int? status;
  String? message;
  String? workspaceId;
  int? rowStatus;
  bool? isActive;
  int? isActiveSale;
  String? gst;
  String? fkTableID;
  String? tableName;
  String? categoryID;
  String? discountPrice;
  String? discountFormula;
  bool? isIGST;
  String? parcelAmount;
  String? parcelAmountWithoutGst;
  String? notes;
  String? priceTypeId;

  // temp
  //String tempPrice;
  Transactions(
      {this.id,
      this.transactId,
      this.salesId,
      this.prodId,
      this.name,
      this.price,
      this.actualPrice,
      this.amount,
      this.amountWithoutGst,
      this.qty,
      this.productType,
      this.toppingId,
      this.cGst,
      this.cGstAmount,
      this.sGst,
      this.sGstAmount,
      this.sync,
      this.status,
      this.message,
      this.workspaceId,
      this.rowStatus,
      this.isActive,
      this.isActiveSale,
      this.fkTableID,
      this.discountFormula,
      this.discountPrice,
      this.isIGST,
      this.parcelAmount,
      this.notes,
      this.parcelAmountWithoutGst,
      this.priceTypeId,
      });

  Transactions.fromMap(Map<String, dynamic> map) {
    id = map[' '];
    transactId = map['transactId'];
    salesId = map['fkSalesId'];
    prodId = map['salesProductId'];
    productType = map['productType'];
    toppingId = map['toppingId'];
    name = map['salesProductName'];
    qty = map['quantity'].toString();
    price = map['price'];
    actualPrice = map['actualPrice'];
    cGst = map["salesCGst"];
    cGstAmount = map["salesCGstAmount"].toString();
    sGst = map["salesSGst"];
    sGstAmount = map["salesSGstAmount"].toString();
    amount = map['amount'].toString();
    amountWithoutGst = map['amountWithoutGst'].toString();
    sync = map['salesTransactionSync'];
    workspaceId = map['workspaceId'];
    rowStatus = map['rowStatus'];
    isActiveSale = map['isActiveSale'];
    fkTableID = map['fkTableID'];
    discountFormula = map['discountFormula'];
    discountPrice = map['discountPrice'];
    isIGST = map['isIGST'] == 1 ? true : false;
    parcelAmount = map['parcelAmount'];
    parcelAmountWithoutGst = map['parcelAmountWithoutGst'];
    notes = map['notes'];
    priceTypeId= map['priceTypeId'];
  }
  factory Transactions.fromJson(Map<String, dynamic> json) => Transactions(
      transactId: json["transactionID"] == null ? null : json["transactionID"],
      salesId: json["salesID"] == null ? null : json["salesID"],
      prodId: json["productID"] == null ? null : json["productID"],
      productType: json["productType"] == null ? null : json["productType"],
      toppingId: json["toppingID"] == null ? null : json["toppingID"],
      name: json["productName"] == null ? null : json["productName"],
      qty: json["qty"] == null ? null : json["qty"].toString(),
      price:
          json["productPrice"] == null ? null : json["productPrice"].toString(),
      amount: json["amount"] == null ? null : json["amount"].toString(),
      actualPrice:
          json["actualPrice"] == null ? null : json["actualPrice"].toString(),
      cGst: json["cgst"] == null ? null : json["cgst"].toString(),
      cGstAmount:
          json["cgstAmount"] == null ? null : json["cgstAmount"].toString(),
      sGst: json["sgst"] == null ? null : json["sgst"].toString(),
      sGstAmount:
          json["sgstAmount"] == null ? null : json["sgstAmount"].toString(),
      amountWithoutGst: json["amountWithoutGST"] == null
          ? null
          : json["amountWithoutGST"].toString(),
      workspaceId: json["workspaceID"] == null ? null : json["workspaceID"],
      isActive: json["isActive"] == null ? null : json["isActive"],
      isActiveSale: json["isActiveSale"] == null ? null : json["isActiveSale"],
      discountFormula:
          json["discountFormula"] == null ? null : json["discountFormula"],
      discountPrice:
          json["discountPrice"] == null ? null : json["discountPrice"],
      isIGST: json["isGst"] == null ? null : json["isGst"],
      notes: json["notes"] == null ? null : json["notes"],
      parcelAmount:
          json["parcelAmount"] == null ? null : json["parcelAmount"].toString(),
      parcelAmountWithoutGst: json["parcelAmountWithoutGst"] == null
          ? null
          : json["parcelAmountWithoutGst"],
       priceTypeId : json["priceTypeId"] == null ? null : json["priceTypeId"].toString(),
          );
        

  Map<String, dynamic> toJson() => {
        "transactionID": transactId == null ? null : transactId,
        "salesID": salesId == null ? null : salesId,
        "productID": prodId == null ? null : prodId,
        "productType": productType == null ? null : productType,
        "toppingID": toppingId == null ? null : toppingId,
        "productName": name == null ? null : name,
        "qty": qty == null ? null : double.parse(qty ?? ""),
        "productPrice": price == null ? null : price,
        "amount": amount == null ? null : amount,
        "actualPrice": actualPrice == null ? null : actualPrice,
        "cgst": cGst == null ? null : cGst,
        "cgstAmount": cGstAmount == null ? null : cGstAmount,
        "sgst": sGst == null ? null : sGst,
        "sgstAmount": sGstAmount == null ? null : sGstAmount,
        "amountWithoutGST": amountWithoutGst == null ? null : amountWithoutGst,
        "workspaceID": workspaceId == null ? null : workspaceId,
        "status": rowStatus == null ? null : rowStatus,
        "isActiveSale": isActiveSale == null ? null : isActiveSale,
        "discountFormula": discountFormula == null ? null : discountFormula,
        "discountPrice": discountPrice == null ? null : discountPrice,
        "isGst": isIGST,
        "parcelAmount": parcelAmount == null ? null : parcelAmount,
        "notes": notes == null ? null : notes,
        "parcelAmountWithoutGst":
            parcelAmountWithoutGst == null ? null : parcelAmountWithoutGst,
        "priceTypeId" : priceTypeId == null ? null : priceTypeId
      };
}

GetTransactionListFromApi getTransactionListFromApiFromJson(String str) =>
    GetTransactionListFromApi.fromJson(json.decode(str));

String getTransactionListFromApiToJson(GetTransactionListFromApi data) =>
    json.encode(data.toJson());

class GetTransactionListFromApi {
  GetTransactionListFromApi({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<Transactions>? result;

  factory GetTransactionListFromApi.fromJson(Map<String, dynamic> json) =>
      GetTransactionListFromApi(
        status: json["status"],
        message: json["message"],
        result: List<Transactions>.from(
            json["result"].map((x) => Transactions.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "result": List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class TransactionGetModel {
  TransactionGetModel({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  TransactionListModel? result;

  factory TransactionGetModel.fromJson(Map<String, dynamic> json) =>
      TransactionGetModel(
        status: json["status"],
        message: json["message"],
        result: json["result"] == null
            ? null
            : TransactionListModel.fromJson(json["result"]),
      );

  // Map<String, dynamic> toJson() => {
  //   "status": status,
  //   "message": message,
  //   "result": List<dynamic>.from(result.map((x) => x.toJson())),
  // };
}

class TransactionListModel {
  TransactionListModel({
    this.transactionList,
  });

  List<Transactions>? transactionList;

  factory TransactionListModel.fromJson(Map<String, dynamic> json) =>
      TransactionListModel(
        transactionList: List<Transactions>.from(
            json["salesTransaction"].map((x) => Transactions.fromJson(x))),
      );

  // Map<String, dynamic> toJson() => {
  //   "result": List<dynamic>.from(result.map((x) => x.toJson())),
  // };
}
