class SendOtp {
  String? mobile;

  SendOtp({this.mobile});

  SendOtp.fromJson(Map<String, dynamic> json) {
    mobile = json['mobile'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['mobile'] = this.mobile;
    return data;
  }
}

class VerifyOTP {
  String? mobile ;
  String? otp;

  VerifyOTP({this.mobile, this.otp});

  VerifyOTP.fromJson(Map<String, dynamic> json) {
    mobile = json['mobile'];
    otp = json['otp'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['mobile'] = this.mobile;
    data['otp'] = this.otp;
    return data;
  }
}

class ResetPassword {
  String? encryptedText ;
  String? newPassword;

  ResetPassword({this.encryptedText, this.newPassword});

  ResetPassword.fromJson(Map<String, dynamic> json) {
    encryptedText = json['encryptedText'];
    newPassword = json['newPassword'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['encryptedText'] = this.encryptedText;
    data['newPassword'] = this.newPassword;
    return data;
  }
}

class VerifyLoginOTP {
  String? otp;
  String? mobile;
  String? mobileID;

  VerifyLoginOTP({this.otp, this.mobile, this.mobileID});

  VerifyLoginOTP.fromJson(Map<String, dynamic> json) {
    otp = json['otp'];
    mobile = json['mobile'];
    mobileID = json['mobileID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['otp'] = otp;
    data['mobile'] = mobile;
    data['mobileID'] = mobileID;

    return data;
  }
}

class ForgotPasswordResult {
  int? status;
  String? message;

  ForgotPasswordResult({this.status, this.message});

  ForgotPasswordResult.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    return data;
  }
}