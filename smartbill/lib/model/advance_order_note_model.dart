class AdvanceOrderNoteModel {
  int? id;
  String? workspaceId;
  String? salesId;
  String? note;
  String? createdDate;
  String? updatedDate;
  int? status;
  bool? isActive;
  int? sync;

  AdvanceOrderNoteModel({this.id, this.salesId, this.note, this.status, this.sync, this.isActive, this.createdDate, this.updatedDate});

  AdvanceOrderNoteModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    workspaceId = json['workspaceId'];
    salesId = json['salesId'];
    note = json['note'];
    createdDate = json['createdDate'];
    updatedDate = json['updatedDate'];
    status = json['status'];
    sync = json['sync'];
    isActive = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['workspaceId'] = this.workspaceId;
    data['salesId'] = this.salesId;
    data['note'] = this.note;
    data['createdDate'] = this.createdDate;
    data['updatedDate'] = this.updatedDate;
    data['status'] = this.status;
    data['sync'] = this.sync;
    data['isActive'] = this.isActive;
    return data;
  }
}
