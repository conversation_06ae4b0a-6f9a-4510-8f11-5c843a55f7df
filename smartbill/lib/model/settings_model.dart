//
// class SettingsDetails {
//   String workspaceId;
//   String userId;
//   String settingKey;
//   String settingValue;
//   SettingsDetails(
//       {this.workspaceId, this.userId, this.settingKey, this.settingValue});
//   Map<String, dynamic> toMap() {
//     return {
//       DatabaseHelper.workspaceId: workspaceId,
//       DatabaseHelper.userId: userId,
//       DatabaseHelper.settingKey: settingKey,
//       DatabaseHelper.settingValue: settingValue,
//     };
//   }
//
//   SettingsDetails.fromMap(Map<String, dynamic> map) {
//     workspaceId = map['workspaceId'];
//     userId = map['userId'];
//     settingKey = map['settingKey'];
//     settingValue = map['settingValue'];
//   }
// }
