import 'dart:convert';

List<Validity> bannerFromJson(String str) =>
    List<Validity>.from(json.decode(str).map((x) => Validity.fromJSON(x)));

class Validity {
  String? deviceId;
  String? deviceName;
  String? fromDate;
  String? toDate;
  String? message;
  String? token;
  Validity(
      {this.deviceId,
      this.deviceName,
      this.fromDate,
      this.toDate,
      this.message,
      this.token});
  factory Validity.fromJSON(Map<String, dynamic> jsonMap) {
    return Validity(
      deviceId: jsonMap['deviceID'] != null ? jsonMap['deviceID'] : '',
      deviceName: jsonMap['deviceName'] != null ? jsonMap['deviceName'] : '',
      fromDate: jsonMap['activeDate'] != null ? jsonMap['activeDate'] : '',
      toDate: jsonMap['expiryDate'] != null ? jsonMap['expiryDate'] : '',
      message: jsonMap['message'] != null ? jsonMap['message'] : '',
    );
  }
  Map toMap() {
    var map = new Map<String, dynamic>();
    map["deviceID"] = deviceId;
    map["deviceName"] = deviceName;
    map["activeDate"] = fromDate;
    map["expiryDate"] = toDate;
    map["token"] = token;
    return map;
  }

  @override
  String toString() {
    var map = this.toMap();
    return map.toString();
  }
}
