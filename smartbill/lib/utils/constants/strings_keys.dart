/// Default
const String appName = 'NembooBill';

/// Printer Type
const String ptNetwork = 'network';
const String ptBluetooth = 'bluetooth';
const String ptUSB = 'usb';
// const String printerBillTypeKOT = 'kot';
// const String printerBillTypeBill = 'bill';
// const String printerBillTypeBoth = 'both';

/// Multi printer
const String multiPrinter = 'MultiPrint';
const String showTodaySalesKey = "ShowTodaySales";
const String showEmployeeNamesInKOT = "ShowEmployeeNameInKOT";
const String enableOnlineOrderKey = "EnableOnlineOrder";
const String splitInvoiceByPaymentKey = "splitInvoiceByPayment";

// const String appLogo = 'appLogo';
const String logo = 'logo';
const String applogo = "applogo";

///WorkSpace Settings Page

const String key_isCustomer = "IsCustomer";
const String key_isCustomerNameMandatory = "IsCustomerNameMandatory";
const String key_taxEnable = "TaxEnable";
const String key_kotIndividualEnable = "KOTIndividualEnable";
const String key_dynamicQuantity = "DynamicQuantity";
const String key_paymentType = "PaymentType";
// const String key_enableInventory = "EnableInventory";
// const String key_inventoryType = "InventoryType";
const String key_allowNegativeSales = "AllowNegativeSales";
const String key_returnAmountPrint = "ReturnAmountPrint";
const String key_enableDiscount = "EnableDiscount";
const String key_saleLayout = "SaleLayout";
const String key_discountType = "DiscountType";
const String key_successDialogOnSales = "SuccessDialogOnSales";
const String key_additionalKOTButton = "AdditionalKOTButton";
const String key_enableMeasurement = "ShowMeasurement";
const String key_enableDynamicQty = "ShowDynamicQty";
const String key_enableToppings = "ShowToppings";
const String key_applyTaxFor = "ApplyTaxFor";
const String key_parcelEnable = "ParcelEnable";
const String key_applyParcelFor = "ApplyParcelFor";
const String key_isFavorite = "FavoriteProduct";
const String key_resetInvoiceNo = "ResetInvoiceNo";
const String key_enableTableOrders = "EnableTableOrders";
const String key_taxMode = "TaxMode";
const String key_overAllTax = "OverAllTax";
const String key_overAllParcelCharge = "OverAllParcelCharge";
const String key_eatAndPay = "EatAndPay";
const String key_kotEnable = "KOTEnable";
const String key_imagePrint = "ImagePrint";
const String key_invoiceExtension = "InvoiceExtension";
const String key_invoicePaymentType = "InvInvoicePaymentTypeoiceExtension";
const String key_deleteBy2Days = "DeleteBy2Days";
const String key_takeAwayTitle = "TakeAwayTitle";
const String key_eatAndPayTitle = "EatAndPayTitle";
const String key_tableOrderTitle = "TableOrderTitle";
const String key_printerAdEnable = "PrinterAdEnable";
const String key_alwaysOnDisplay = "AlwaysOnDisplay";
const String key_roleId = "RoleId";
const String key_autoSync = "AutoSync";
const String key_versionCode = "VersionCode";
const String key_expense = "EnableExpense";
const String key_shiftBasedSales = "ShiftBasedSales";

const String key_purchase = "EnablePurchase";
// show images in sales page
const String key_showproductImages = "ShowImagesInSales";

///workspace device setting key name
const String key_panelProductListview = "PanelProductListview";
const String key_panelProductTapOnClear = "PanelProductTapOnClear";
const String key_panelType = "PanelType";
const String key_favoriteProduct = "FavoriteProduct";
const String key_multiPrint = "MultiPrint";
const String key_printFormat = "PrinterFormat";
const String key_printerPaperSize = "PrinterPaperSize";
const String key_panelCustomerName = "PanelCustomerName";

///DashBoard
const String key_takeAwayName = "TakeAwayName";
const String key_eatAndPayName = "EatAndPayName";
const String key_tableOrderName = "TableOrderName";

const String expense = "EXPENSES";
const String purchase = "PURCHASES";

const String isOpenInitial = 'isOpenInitial';
const String key_sales_billPad = "SalesBillPad";

const String key_sales_category = "SalesCategory";
const String key_store_customerDetails = "StoreCustomerDetails";
const String key_demoaccountexpiryDate = "demoAccountExpiryDate";

const String key_isDemoWorkSpace = "isDemoWorkSpace";

const String key_salesCategory = "SalesCategory";
const String key_advanceOrder = "AdvanceOrder";
