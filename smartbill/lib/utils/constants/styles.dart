/*fonts*/
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/colors.dart';

const fontRegular = 'Regular';
const fontMedium = 'Medium';
const fontSemibold = 'Semibold';
const fontBold = 'Bold';

/* font sizes*/
const textSizeSmall = 13.0;
const textSizeSMedium = 14.0;
const textSizeMedium = 16.0;
const textSizeLargeMedium = 18.0;
const textSizeNormal = 20.0;
const textSizeLarge = 24.0;
const textSizeXLarge = 28.0;
const textSizeXXLarge = 30.0;

const double maxScreenWidth = 1550;
const double mobileWidth = 450;
const double tabletWidth = 800;
const double desktopWidth = 1280;

const Color primaryArrowColor = Color(0xff003d00);

BoxDecoration boxdecoration({double radius = 2, Color color = Colors.transparent, Color? bgColor, var showShadow = false}) {
  return BoxDecoration(
    color: bgColor ?? secondaryTextColor,
    boxShadow: showShadow ? defaultBoxShadow(shadowColor: shadowColorGlobal) : [BoxShadow(color: Colors.transparent)],
    border: Border.all(color: color),
    borderRadius: BorderRadius.all(Radius.circular(radius)),
  );
}

TextStyle headingFontStyle1 = TextStyle(color: secondaryTextColor, fontSize: 36, fontWeight: FontWeight.bold);

TextStyle headingFontStyle2 = TextStyle(color: buttonThemeColor, fontSize: 24, fontWeight: FontWeight.bold);

TextStyle buttonTextStyle = const TextStyle(fontSize: 20, color: secondaryTextColor, fontWeight: FontWeight.bold);

TextStyle headingTextStyle3 = const TextStyle(
  color: secondaryTextColor,
  fontSize: 18,
  fontWeight: FontWeight.bold,
);
TextStyle red18w700 = const TextStyle(
  color: Colors.red,
  fontSize: 18,
  fontWeight: FontWeight.bold,
);
TextStyle red14w300 = const TextStyle(
  color: Color(0xFFFF3F3F),
  fontSize: 14,
  fontWeight: FontWeight.w400,
);
TextStyle greyTextStyle = const TextStyle(
  color: greyColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle textStyle18 = const TextStyle(
  color: secondaryTextColor,
  fontSize: 18,
  //fontWeight: FontWeight.w500,
);
TextStyle black13RegularTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 13,
  fontWeight: FontWeight.w400,
);
TextStyle white14BoldTextStyle = const TextStyle(
  color: secondaryTextColor,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle black14RegularTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 14,
  fontWeight: FontWeight.w400,
);

TextStyle black14BoldTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 14,
  fontWeight: FontWeight.w700,
);

TextStyle black16BoldTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 16,
  fontWeight: FontWeight.w700,
);
TextStyle black16w500 = const TextStyle(
  color: primaryTextColor,
  fontSize: 16,
  fontWeight: FontWeight.w500,
);
TextStyle black18w700 = const TextStyle(
  color: primaryTextColor,
  fontSize: 18,
  fontWeight: FontWeight.bold,
);
TextStyle black15BoldTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 15,
  fontWeight: FontWeight.w700,
);

TextStyle grey12RegularTextStyle = const TextStyle(
  color: Colors.grey,
  fontSize: 12,
  fontWeight: FontWeight.w400,
);
TextStyle grey14w500 = const TextStyle(
  color: Colors.grey,
  fontSize: 14,
  fontWeight: FontWeight.w500,
);
TextStyle black14SemiBoldTextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 14,
  fontWeight: FontWeight.w600,
);

TextStyle black16TextStyle = const TextStyle(
  color: primaryTextColor,
  fontSize: 16,
  //fontWeight: FontWeight.w700,
);

// TextStyle white16BoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 16,
//   fontWeight: FontWeight.w700,
// );

// TextStyle primaryColor16BlackTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 16,
//   fontWeight: FontWeight.w900,
// );

// TextStyle primaryColor16BoldTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 16,
//   fontWeight: FontWeight.w700,
// );

// TextStyle black16SemiBoldTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 16,
//   fontWeight: FontWeight.w600,
// );



// TextStyle red15BoldTextStyle = const TextStyle(
//   color: Colors.red,
//   fontSize: 15,
//   fontWeight: FontWeight.w700,
// );

// TextStyle black15SemiBoldTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 15,
//   fontWeight: FontWeight.w600,
// );

// TextStyle primaryColor15BoldTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 15,
//   fontWeight: FontWeight.w700,
// );

// TextStyle white15BoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 15,
//   fontWeight: FontWeight.w700,
// );

// TextStyle grey15RegularTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 15,
//   fontWeight: FontWeight.w400,
// );

// TextStyle white14BoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w700,
// );

// TextStyle white14SemiBoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w600,
// );


// TextStyle blue14BoldTextStyle = const TextStyle(
//   color: Colors.blue,
//   fontSize: 14,
//   fontWeight: FontWeight.w700,
// );



// TextStyle primaryColor14SemiBoldTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w600,
// );

// TextStyle grey14BoldTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w700,
// );

// TextStyle grey14SemiBoldTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w600,
// );

// TextStyle grey14RegularTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 14,
//   fontWeight: FontWeight.w400,
// );

// TextStyle black13BoldTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w700,
// );

// TextStyle primaryColor13BlackTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w900,
// );

// TextStyle grey13SemiBoldTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w600,
// );

// TextStyle white13SemiBoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w600,
// );

// TextStyle black13SemiBoldTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w600,
// );

// TextStyle grey13RegularTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w400,
// );

// TextStyle white13RegularTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w400,
// );

// TextStyle black13RegularTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w400,
// );

// TextStyle black13MediumTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w500,
// );

// TextStyle primaryColor13RegularTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 13,
//   fontWeight: FontWeight.w400,
// );

// TextStyle black11SemiBoldTextStyle = const TextStyle(
//   color: primaryBlackColor,
//   fontSize: 11,
//   fontWeight: FontWeight.w600,
// );

// TextStyle white12RegularTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 12,
//   fontWeight: FontWeight.w400,
// );

// TextStyle grey12SemiBoldTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 12,
//   fontWeight: FontWeight.w600,
// );

// TextStyle white12BoldTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 12,
//   fontWeight: FontWeight.w700,
// );

// TextStyle primaryColor12BlackTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 12,
//   fontWeight: FontWeight.w900,
// );

// TextStyle grey12BlackTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 12,
//   fontWeight: FontWeight.w900,
// );

// TextStyle primaryColor11BlackTextStyle = const TextStyle(
//   color: primaryColor,
//   fontSize: 11,
//   fontWeight: FontWeight.w900,
// );

// TextStyle grey11SemiBoldTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 11,
//   fontWeight: FontWeight.w600,
// );

// TextStyle grey11RegularTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 11,
//   fontWeight: FontWeight.w400,
// );

// TextStyle white10BlackTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 10,
//   fontWeight: FontWeight.w900,
// );

// TextStyle white9BlackTextStyle = const TextStyle(
//   color: primaryWhiteColor,
//   fontSize: 9,
//   fontWeight: FontWeight.w900,
// );

// TextStyle grey9RegularTextStyle = const TextStyle(
//   color: primaryGreyColor,
//   fontSize: 9,
//   fontWeight: FontWeight.w400,
// );
