import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/reports/get_reports.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/dbutils/advance_order_db_scripts.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart'; 
import 'package:smartbill/utils/dbutils/master_db_script.dart';
import 'package:smartbill/utils/dbutils/reports_db_script.dart';
import 'package:smartbill/utils/dbutils/workspace_db_script.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../Repositories/reports_repository.dart';
import '../../main.dart';
import '../../model/payment_category_model.dart';
import '../../model/products.dart';
import '../../screens/expenses/model/expense_category_model.dart';
import '../../screens/salescategory/model/sales_category_model.dart';
import '../dbutils/db_crud_script.dart';
import '../dbutils/db_strings.dart';
import '../dbutils/device_setting_db_script.dart';
import '../dbutils/expenses_db_script.dart';
import '../dbutils/payment_type_db_script.dart';
import '../dbutils/price_type_db_script.dart';
import '../dbutils/product_details_db_script.dart';
import '../dbutils/product_key_db_script.dart';
import '../dbutils/shop_tables_db_script.dart';
import '../dbutils/toppings_db_script.dart';
import '../dbutils/toppings_groups_db_script.dart';
import '../dbutils/workspace_settings_db_script.dart';
import '../printer_utilities/share_pdf.dart';
import '../roles_permissions/permission_functions.dart';

class FnUtilities {
  bool isLoading = true;
  User user = new User();
  DateTime now = DateTime.now(); 
  CommonDB commonDB = CommonDB();
  BalanceDBScript balanceDB = BalanceDBScript();
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  DeviceSettingsDBScript deviceSettingsDBScript = DeviceSettingsDBScript();
  PaymentTypeDBScript paymentTypeDBScript = PaymentTypeDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  WorkSpaceSettingsDB workSpaceSettingsDB = WorkSpaceSettingsDB();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  DBFuctionsScript dbScript = DBFuctionsScript();
  List<PaymentCategory> paymentCategoryList = [];
  MasterTableDBScript masterTableDBScript = MasterTableDBScript();
  AdvanceOrdersDBScripts advanceOrdersDBScripts = AdvanceOrdersDBScripts();

  //ExpensesDBScript expensesDBScript = ExpensesDBScript();
  //CONVERT DATE INTO STRING
  String convertDate(originalDate) {
    var date = DateFormat("dd-MM-yyyy").format(DateTime.parse("$originalDate"));
    return date;
  }

  String convertDateMonthYear(originalDate) {
    var date = DateFormat("MMMM-yyyy").format(DateTime.parse("$originalDate"));
    return date;
  }

  //QTY
  checkQtyDoubleValue(String value) {
    try {
      if ((double.parse(value) % 1) == 0) {
        var firstValue = value.split('.');
        return firstValue[0];
      } else {
        return value;
      }
    } catch (e) {
      // ignore: avoid_print
    }
  }

  getExpieryDate() async {
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // user = prefs.getString("userDetails") != null
    //     ? User.fromJson(json.decode(prefs.getString("userDetails")))
    //     : null;
    // if (user != null) {
    //   prefs.setInt("salesCount", user.salesCount);
    //   MyApp.activeUser.username = user.username;
    //   MyApp.firstLastName = "${user.firstName} ${user.lastName}";
    //   MyApp.activeUser.token = user.token;
    //   // ListUtility.subscriptionDetails = user.subscriptionDetails;
    //   // for (SubscriptionDetail s in ListUtility.subscriptionDetails) {
    //   //   if (MyApp.deviceId == s.mobileId) {
    //   //     ListUtility.validityList = s.activations;
    //   //     for (Activation a in ListUtility.validityList) {
    //   //       if (DateTime.parse(a.expiryDate).compareTo(now) > 0) {
    //   //         MyApp.expieryDate = DateTime.parse(a.expiryDate);
    //   //       }
    //   //     }
    //   //   }
    //   // }
    // } else {
    //   MyApp.expieryDate = prefs.getString("expieryDate") != null
    //       ? DateTime.parse(prefs.getString("expieryDate"))
    //       : null;
    //   prefs.setInt("salesCount", 20);
    // }
    getActiveWorkspaceAndUser();
  }

  updateExpieryDate() async {
    // SharedPreferences prefs = await SharedPreferences.getInstance();
    // User user = new User();
    // user = prefs.getString("userDetails") != null
    //     ? User.fromJson(json.decode(prefs.getString("userDetails")))
    //     : null;
    // user.subscriptionDetails
    // for (int i = 0; i < user.subscriptionDetails.length; i++) {
    //   if (MyApp.deviceId == user.subscriptionDetails[i].mobileId) {
    //     for (int j = 0;
    //         j < user.subscriptionDetails[i].activations.length;
    //         j++) {
    //       if (DateTime.parse(
    //                   user.subscriptionDetails[i].activations[j].expiryDate)
    //               .compareTo(now) >
    //           0) {
    //         //MyApp.expieryDate = DateTime.parse(a.expiryDate);
    //         user.subscriptionDetails[i].activations[j].expiryDate =
    //             MyApp.expieryDate.toString();
    //         prefs.setString("userDetails", json.encode(user));
    //       }
    //     }
    //   }
    // }
  }

  BuildContext? dialogContext;

  void onLoading(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        dialogContext = context;
        return AlertDialog(
          backgroundColor: secondaryTextColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          contentPadding: EdgeInsets.all(0.0),
          insetPadding: EdgeInsets.symmetric(horizontal: 100),
          content: Padding(
            padding: EdgeInsets.only(top: 20, bottom: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Please wait....", style: primaryTextStyle(color: primaryTextColor)),
              ],
            ),
          ),
        );
      },
    );
  }

  onStopping() {
    if (dialogContext != null) {
      Navigator.pop(dialogContext!,true);
      dialogContext = null; // Reset dialogContext after dismissing the dialog
    }
  }

  setUserType(bool isAdmin) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setBool("isAdmin", isAdmin);
  }

  getPanelDetails() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    MyApp.isFilterEnable = prefs.getBool("isFilterEnable") != null ? prefs.getBool("isFilterEnable")! : false;
    MyApp.isListViewEnable = prefs.getBool("isListViewEnable") != null ? prefs.getBool("isListViewEnable")! : false;
  }

  /// Get Grid count for products
  getProductsGridCount(double width, {double sideCardWidth = 0.0}) {
    double finalWidth = width - sideCardWidth;
    if (finalWidth <= 200) {
      return 1;
    } else if (finalWidth > 200 && finalWidth <= 300) {
      return 2;
    } else if (finalWidth > 300 && finalWidth <= 400) {
      return 3;
    } else if (finalWidth > 400 && finalWidth <= 600) {
      return 4;
    } else if (finalWidth > 600 && finalWidth <= 800) {
      return 5;
    } else if (finalWidth > 800 && finalWidth <= 1000) {
      return 6;
    } else {
      return 6;
    }
  }

  commonNormalAlertDialog(BuildContext context, ShowText) {
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) {
          //deviceAlertDialogContext = context;
          return AlertDialog(
            content: Container(
              // color: Colors.amber,
              width: 200,
              height: 100,
              // width: MediaQuery.of(context).size.width / 1.2,
              child: Text(
                " ${ShowText}",
                style: TextStyle(color: primaryTextColor, fontSize: 15.0),
              ),
            ),
            actions: [
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  GestureDetector(
                    onTap: () {
                      Navigator.pop(context);
                    },
                    child: Container(
                      alignment: Alignment.center,
                      height: 40,
                      width: 60,
                      child: text("Close", textColor: secondaryTextColor, isCentered: true),
                      decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                    ),
                  ),
                ],
              ),
            ],
          );
        });
  }

  internetTurnOnAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          child: AlertDialog(
            content: Container(
              child: Text(
                "Please turn on your data or wifi for connection.",
                style: TextStyle(color: primaryTextColor, fontSize: 18.0),
              ),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 40,
                  width: 40,
                  child: text("OK", fontSize: textSizeMedium, textColor: buttonThemeColor, isCentered: true),
                  decoration: boxDecoration(radius: 8.0),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  initiateDeviceSettings() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    if (prefs.getString("deviceSettings") == null || prefs.getString("deviceSettings") == '') {
      if (await deviceSettingsDBScript.queryDeviceWorkspaceCount() == 0) {
        settingsDetail.settingKey = "PrinterPaperSize";
        settingsDetail.settingValue = "58";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PrinterFormat";
        settingsDetail.settingValue = "Default";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PrinterFeedSize";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PrinterPaperCut";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PanelType";
        settingsDetail.settingValue = "Normal";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PanelProductListview";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
        settingsDetail.settingKey = "PanelProductTapOnClear";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
      }
      if (await deviceSettingsDBScript.queryCustomerSettingWorkspaceCount() == 0) {
        settingsDetail.settingKey = "PanelCustomerName";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
      }
      if (await deviceSettingsDBScript.queryMultiPrintSettingWorkspaceCount() == 0) {
        settingsDetail.settingKey = "MultiPrint";
        settingsDetail.settingValue = "0";
        insertDeviceSettingDetails(settingsDetail);
      }
    } else {
      if (await deviceSettingsDBScript.queryDeviceWorkspaceCount() == 0) {
        ListUtility.settingDeviceList = WorkspaceSettings.decode(await prefs.getString("deviceSettings") ?? "");
        print(ListUtility.settingDeviceList.length);
        for (WorkspaceSettings s in ListUtility.settingDeviceList) {
          settingsDetail.settingKey = s.settingKey;
          settingsDetail.settingValue = s.settingValue;
          insertDeviceSettingDetails(settingsDetail);
        }
      }
    }
  }

  insertDeviceSettingDetails(WorkspaceSettings settingsDetail) async {
    Map<String, dynamic> row = {
      db_workSpacesSettings_settingKey: settingsDetail.settingKey,
      db_workSpacesSettings_settingValue: settingsDetail.settingValue,
    };
    WorkspaceSettings setting = WorkspaceSettings.fromMap(row);
    await deviceSettingsDBScript.insertDeviceSettingDetails(setting);

    //showToast("Successfully Created${id1}");
  }

  updateDeviceSettingDetails(WorkspaceSettings settingsDetail) async {
    int count = await deviceSettingsDBScript.checkDeviceKeyExists(settingsDetail.settingKey ?? "");
    if (count > 0) {
      await deviceSettingsDBScript.updateDeviceSettings(settingsDetail);
    } else {
      await deviceSettingsDBScript.insertDeviceSettingDetails(settingsDetail);
    }
    await getAllWorkspaceDeviceSettings();
  }

  startInsertSettingDetails(List<WorkspaceSettings> settingList, syncStatus) {
    for (WorkspaceSettings settingsDetail in settingList) {
      insertSettingDetails(settingsDetail, syncStatus);
    }
  }

  insertSettingDetails(WorkspaceSettings settingsDetail, int syncStatus, {String tableType = "WorkSpaceSettings"}) async {
    Map<String, dynamic> row = {
      db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
      db_workspace_userName: MyApp.activeUser.username,
      db_workSpacesSettings_settingKey: settingsDetail.settingKey,
      db_workSpacesSettings_settingValue: settingsDetail.settingValue,
      db_workSpacesSettings_settingSync: syncStatus,
    };
    WorkspaceSettings setting = WorkspaceSettings.fromMap(row);
    await workSpaceSettingsDB.insertSettingDetails(setting, table: tableType);
  }

  updateSettingDetails(WorkspaceSettings settingsDetail, {String tableType = "WorkSpaceSettings"}) async {
    int keyExists = await workSpaceSettingsDB.checkWorkspaceKeyExists(settingsDetail.settingKey ?? "", table: tableType);
    if (keyExists != 0) {
      WorkspaceSettings settingsDetails = WorkspaceSettings(
        workspaceId: MyApp.activeWorkspace.workspaceId,
        settingKey: settingsDetail.settingKey,
        settingValue: settingsDetail.settingValue,
        sync: 0,
      );
      await workSpaceSettingsDB.updateSettings(settingsDetails, table: tableType);
    } else {
      insertSettingDetails(settingsDetail, 0, tableType: tableType);
    }
    await getAllWorkspaceSetting();
    await getAllWorkspaceUserSettings();
  }

// GET SETTINGS DETAILS BASED ON KEYNAME FROM DIFFERENT TABLES

// FROM WORKSPACE SETTINGS TABLE
  getWorkspaceSetValue(keyname, {String table = "WorkSpaceSettings"}) async {
    var keyValue =
        await commonDB.getSettingValueDynamic('$table', 'settingKey', keyname, 'settingValue', workspaceId: MyApp.activeWorkspace.workspaceId);
    return keyValue;
  }

  ///Workspace settings value assign function
  workSpaceSetValues(keyname, {String table = "WorkSpaceSettings"}) {
    WorkspaceSettings settings = ListUtility.settingList.firstWhere(
        (element) => element.settingKey == keyname && element.workspaceId == MyApp.activeWorkspace.workspaceId,
        orElse: () => WorkspaceSettings());
    return settings.settingValue;
  }

  ///Workspace user settings value assign function
  workSpaceUserSetValue(keyName) {
    WorkspaceSettings settings = ListUtility.userSettingList.firstWhere(
        (element) => element.settingKey == keyName && element.workspaceId == MyApp.activeWorkspace.workspaceId,
        orElse: () => WorkspaceSettings());
    return settings.settingValue;
  }

  ///Workspace device setting value assign function
  workSpaceDeviceSetValue(keyName) {
    WorkspaceSettings settings =
        ListUtility.settingDeviceList.firstWhere((element) => element.settingKey == keyName, orElse: () => WorkspaceSettings());
    return settings.settingValue;
  }

// FROM DEVICE SETTINGS TABLE
  getDeviceSetValue(keyname) async {
    var keyValue = await commonDB.getSettingValueDynamic('DeviceSettings', 'settingKey', keyname, 'settingValue');
    return keyValue;
  }

// FROM GENERAL SETTINGS TABLE
  getGeneralSetValue(String keyName) async {
    var keyValue = await commonDB.getSettingValueDynamic('GeneralSettings', 'settingKey', keyName, 'settingValue');
    return keyValue;
  }

  setActiveWorkspaceAndUser(WorkSpace item) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    MyApp.activeWorkspace = item;
    MyApp.expiryDate = DateTime.parse(item.expiryDate ?? "");
    MyApp.activeUser = ListUtility.userList.firstWhere((element) => element.username == item.userName);
    prefs.setString("activeWorkspace", jsonEncode(MyApp.activeWorkspace));
  }

  getActiveWorkspaceAndUser() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    final String userList = await prefs.getString('userList') != null ? prefs.getString('userList')! : "";
    ListUtility.userList = userList != "" ? User.decode(userList) : [];

    WorkSpace workSpace =
        await prefs.getString("activeWorkspace") != null ? WorkSpace.fromJson(jsonDecode(prefs.getString("activeWorkspace") ?? "")) : WorkSpace();
    if (workSpace.workspaceId != null) {
      MyApp.activeWorkspace = workSpace;
      MyApp.activeUser = ListUtility.userList.firstWhere((element) => element.username == workSpace.userName, orElse: () => User());
      MyApp.expiryDate = DateTime.parse(MyApp.activeWorkspace.expiryDate ?? "");
    }
  }

  Future<File?> readFileFromLocal(fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    if (await File('${directory.path}/$fileName.png').exists()) {
      final File file = File('${directory.path}/$fileName.png');
      return file;
    } else {
      return null;
    }
  }

  Future<String?> readFilePathFromLocal(fileName) async {
    final Directory directory = await getApplicationDocumentsDirectory();
    if (await File('${directory.path}/$fileName.png').exists()) {
      final String filePath = '${directory.path}/$fileName.png';
      return filePath;
    } else {
      return null;
    }
  }

  createDiscountFormula(Discounts model) {
    String formula;
    //short format FOR TYPE
    String type = model.type == "Sales" ? "S" : "P";
    //short format FOR MODE
    String mode = model.mode == "Auto" ? "A" : "C";
    //short format FOR DISCOUNT ON
    String discountOn = model.discountOn == "Percentage" ? "P" : "F";
    formula =
        "${type}_${mode}_${discountOn}_${model.discount}_${model.minDiscount}_${model.maxDiscount != null ? model.maxDiscount : ""}_${model.fromDate}_${model.toDate}";
    return formula;
  }

  splitValueFromFormula(Discounts model) {
    var splitFormula = model.formula!.split("_");
    model.type = splitFormula[0];
    model.mode = splitFormula[1];
    model.discountOn = splitFormula[2];
    model.discount = splitFormula[3];
    model.minDiscount = splitFormula[4];
    model.maxDiscount = splitFormula[5];
    model.fromDate = DateTime.parse(splitFormula[6]);
    model.toDate = DateTime.parse(splitFormula[7]);
    return model;
  }

  salesSuccessDialog(BuildContext context, String salesId) {
    var width = MediaQuery.of(context).size.width;
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                // width: double.maxFinite,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Container(
                      child: Image(
                        height: 100,
                        image: AssetImage("assets/images/successfull.png"),
                      ),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: <Widget>[
                        GestureDetector(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width > tabletWidth ? width * 0.18 : width * 0.3,
                            child: text("Cancel", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                        SizedBox(
                          width: 20,
                        ),
                        GestureDetector(
                          onTap: () async {
                            pdfReportBuildFunction(salesId);
                            Navigator.pop(context);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            height: 50,
                            width: width > tabletWidth ? width * 0.18 : width * 0.3,
                            child: text("Share", textColor: secondaryTextColor, isCentered: true),
                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }
  //------------------------------------------------Start PDFBuild Function-------------------------------------------------//

  pdfReportBuildFunction(String? salesId) async {
    // PrinterUtilities printerUtilities = PrinterUtilities();
    SharePDF sharePDF = SharePDF();
    if (!salesId.isEmptyOrNull) {
      String fileName = "Sales_Report";
      List<Transactions> transactionList = [];
      CommonDB commonDB = CommonDB();
      final salesRow = await commonDB.getSingleDataBasedOneColumn('SalesDetails', 'salesId', salesId);
      Sales? salesReport;
      salesReport = salesRow != null ? Sales.fromMap(salesRow) : null;
      if (salesReport?.fkTableID != null) {
        final tableRow = await commonDB.getSingleDataBasedOneColumn('ShopTables', 'shopTableId', salesReport?.fkTableID);
        salesReport?.tableName = salesRow != null ? tableRow!["shopTableName"] : "";
      }
      final transactListRows = await commonDB.getAllListDataBasedOneColumn('SalesTransactions', 'fkSalesId', salesId);
      transactListRows.forEach((row) => transactionList.add(Transactions.fromMap(row)));

      sharePDF.savePDFFormat(sharePDF.pdfCommonSalesReportBodyBuildFunction(salesReport, transactionList), fileName, "");
    }
  }

//------------------------------------------------End PDFBuild Function-------------------------------------------------//

  insertOrUpdateBalance(Balance balance, String type, {isUpdate = false}) async {
    var keyExists = await balanceDB.getBalanceByCustomerID(balance.customerID ?? "");
    balance.workspaceID = MyApp.activeWorkspace.workspaceId;
    if (keyExists != null) {
      if (type == "credit") {
        balance.balance = (double.parse(keyExists['CustomerBalance']) - double.parse(balance.balance ?? "0")).toString();
      } else {
        balance.balance = (double.parse(keyExists['CustomerBalance']) + double.parse(balance.balance ?? "0")).toString();
      }
      balance.rowStatus = 1;
      await balanceDB.updateBalance(balance);
    } else {
      if (type == "credit") {
        balance.balance = "-${balance.balance}";
      } else {
        balance.balance = balance.balance;
      }
      balance.rowStatus = 0;
      balanceDB.insertBalance(balance);
    }
  }

  Future<String> getTodaySalesAmount() async {
    String todaySalesAmount = '0.0';
    var now = new DateTime.now();

    var fromDate = now
        .subtract(Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));
    var toDate = now
        .subtract(Duration(hours: now.hour, minutes: now.minute, seconds: now.second, milliseconds: now.millisecond, microseconds: now.microsecond));

    var result = await ReportDetailsDB.getAllSalesByDateToDateForReport(fromDate, toDate, paymentId: "");
    GetReports? getReports = result != null ? GetReports.fromDBJson(result) : null;
    if (getReports != null) {
      todaySalesAmount = getReports.totalAmount.toString();
    }
    return todaySalesAmount;
  }

  requestNotificationPermission() async {
    if (!Platform.isWindows) {
      var status = await Permission.notification.status;
      if (!status.isGranted) {
        await Permission.notification.request();
      }
    }
  }

  requestStoragePermission() async {
    if (!Platform.isWindows) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        await Permission.storage.request();
      }
    }
  }

  //GET PRODUCT DETAILS FORM DB
  getAllProductDetails() async {
    ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
    var FavoriteProduct = await getDeviceSetValue("FavoriteProduct");
    bool isFavorite = FavoriteProduct == "1" ? true : false;
    try {
      var allRows;
      if (isFavorite) {
        allRows = await productDetailsDBScript.queryallFavoriteProducts();
      } else {
        allRows = await productDetailsDBScript.queryallRowsProductDeatails();
      }

      ListUtility.productList.clear();
      allRows.forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
      //await queryAllProductKeys();
      ListUtility.productList = await assignFilePath(ListUtility.productList);
    } on Exception catch (e) {
      print(e);
    }
  }

  ///Get all workspace settings from db
  getAllWorkspaceSetting() async {
    final allRows = await workSpaceSettingsDB.queryAllGetSetting();
    ListUtility.settingList.clear();
    allRows.forEach((row) => ListUtility.settingList.add(WorkspaceSettings.fromMap(row)));
  }

  ///Get all workspace user settings from db
  getAllWorkspaceUserSettings() async {
    final allRows = await workSpaceSettingsDB.queryAllGetUserSetting();
    ListUtility.userSettingList.clear();
    allRows.forEach((row) => ListUtility.userSettingList.add(WorkspaceSettings.fromMap(row)));
  }

  ///Get all workspace device settings from db
  getAllWorkspaceDeviceSettings() async {
    final allRows = await deviceSettingsDBScript.queryAllGetDeviceSetting();
    ListUtility.settingDeviceList.clear();
    allRows.forEach((row) {
      ListUtility.settingDeviceList.add(WorkspaceSettings.fromMap(row));
    });
  }

  ///Get all payment type from db
  getAllPaymentType() async {
    final allRows = await paymentTypeDBScript.selectAllPaymentType();
    ListUtility.paymentTypeList.clear();
    allRows.forEach((row) {
      ListUtility.paymentTypeList.add(PaymentType.fromMap(row));
    });
  }

  ///Get all payment category from db
  getAllPaymentCategoryFromLocaldb() async {
    final allRows = await paymentTypeDBScript.selectAllPaymentCategory();
    ListUtility.paymentCatList.clear();
    allRows.forEach((row) {
      ListUtility.paymentCatList.add(PaymentCategory.fromJson(row));
    });
  }

  insertPaymentCategory(paymentCategoryList) async {
    var paymentCategoryMap;
    for (PaymentCategory model in paymentCategoryList) {
      paymentCategoryMap = {db_Payment_category_ID: model.id, db_payment_category_name: model.paymentCategoryName, db_status: model.status};
      await dbScript.dbInsert(db_payment_category, paymentCategoryMap);
    }
  }

  getPaymentCategory() async {
    try {
      // LoadingAlertWidget.onLoading(context);;
      await getAllPaymentCategoryAPI().then((value) async {
        if (value.status == 1) {
          paymentCategoryList = value.result ?? [];
          await fnUtilities.insertPaymentCategory(paymentCategoryList);
        }
      });
      // LoadingAlertWidget.onStopping();
    } catch (e) {
      log("Error in getPaymentCategory: $e");

      // LoadingAlertWidget.onStopping();
    }
  }

  ///Get all product key word name from db
  getAllProductKey() async {
    final allRows = await productKeyDBScript.queryallRowsProductKeyTable();
    ListUtility.productKeyList.clear();
    allRows.forEach((row) => ListUtility.productKeyList.add(ProductKey.fromMap(row)));
  }

  ///Get all product key word name from db
  getAllExpensesCategoryList() async {
    ExpensesDBScript expensesDBScript = ExpensesDBScript();
    final allRows = await expensesDBScript.queryallRowsExpensesCategoryList();
    ListUtility.expensesCategoryList.clear();
    allRows.forEach((row) => ListUtility.expensesCategoryList.add(ExpensesCategoryModel.fromMap(row)));
  }

  ///Get all measurement from db
  getAllUnits() async {
    PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
    final allRows = await priceTypeDBScript.queryAllGetProductsUnitId();
    ListUtility.unitList.clear();
    allRows.forEach((row) {
      ListUtility.unitList.add(PriceUnitType.fromMap(row));
    });
  }

  ///Get all table for shops from db
  getAllShopTables() async {
    ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
    final allRows = await shopTableDbScript.queryGetShopTables();
    ListUtility.shopTableList.clear();
    allRows.forEach((row) {
      ListUtility.shopTableList.add(ShopTable.fromMap(row));
    });
  }

  ///Get all toppings from db
  getAllToppings() async {
    ToppingsDBScript toppingsDBscript = ToppingsDBScript();
    final allRows = await toppingsDBscript.queryAllGetToppings();
    ListUtility.toppingsList.clear();
    allRows.forEach((row) {
      ListUtility.toppingsList.add(Topping.fromMap(row));
    });
  }

  ///Get all toppings group from db
  getAllToppingsGroup() async {
    ToppingGroupsDBScript toppingGroupsDBScript = ToppingGroupsDBScript();
    final allRows = await toppingGroupsDBScript.queryallRowsToppingsDetails();
    ListUtility.toppingGroupList.clear();
    allRows.forEach((row) {
      ListUtility.toppingGroupList.add(ToppingsGroup.fromMap(row));
    });
  }

  getAllExpenses() async {
    ExpensesDBScript expensesDBScript = ExpensesDBScript();
    final allRows = await expensesDBScript.queryAllExpenses();
    ListUtility.expensesItemList.clear();
    allRows.forEach((row) {
      ListUtility.expensesItemList.add(ExpensesProduct.fromJson(row));
    });
  }

  ///Get sales category data from the master table
  ///based on key and workspace id.
  getAllSalesCategory() async {
    String? result = await masterTableDBScript.getValueByKeyAndWorkspaceId(key_salesCategory);
    List<dynamic> valueList = jsonDecode(result ?? '');
    ListUtility.salesCategoryList = valueList.map((e) => SalesCategoryModel.fromJson(e)).toList();
  }

  ///Get shift details data from the master table

  getAllShiftDetails() async {
    String? result = await masterTableDBScript.getValueByKeyAndWorkspaceId(key_shiftBasedSales);
    List<dynamic> valueList = jsonDecode(result ?? '');
    ListUtility.shiftSalesList = valueList.map((e) => ShiftwiseSalesModel.fromJson(e)).toList();
  }

   getAllOrderNotes() async {
    AdvanceOrdersDBScripts advanceOrdersDBScripts = AdvanceOrdersDBScripts();
    final allRows = await advanceOrdersDBScripts.getAdvanceOrderNoteById();
    ListUtility.orderNotesList.clear();
    allRows.forEach((row) {
      ListUtility.orderNotesList.add(AdvanceOrderNoteModel.fromJson(row));
    });
  }  

  getMasterInfoBasedOnWorkspace() async {
    await getAllProductDetails();
    await getAllWorkspaceSetting();
    await getAllWorkspaceUserSettings();
    await getAllWorkspaceDeviceSettings();
    await getAllPaymentType();
    await getAllProductKey();
    await getAllUnits();
    await getAllShopTables();
    await getAllToppings();
    await getAllToppingsGroup();
    await getAllExpensesCategoryList();
    await getAllExpenses();
    await getAllSalesCategory();
    await getAllShiftDetails();
    await getAllOrderNotes();
  }

  Future<List<Products>> assignFilePath(List<Products> productList) async {
    for (Products p in productList) {
      if (!p.image.isEmptyOrNull) {
        p.imagePath = await readFileFromLocal(p.image);
      }
    }
    return productList;
  }

  requestContactPermission() async {
    if (!Platform.isWindows) {
      var status = await Permission.contacts.status;
      if (!status.isGranted) {
        await Permission.contacts.request();
      }
    }
  }

  //SIGN OUT FUNCTION
  Future<void> removeActiveWorkspaceAndUser(BuildContext context, WorkSpace item, {bool isRemoveUser = false}) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();

    List<WorkSpace> signedWorkspaceByUser = ListUtility.signedWorkspaceList.where((element) => element.userName == item.userName).toList();

    if (isRemoveUser) {
      ListUtility.userList.removeWhere((element) => element.username == item.userName);
      await prefs.setString('userList', User.encode(ListUtility.userList));
    } else {
      for (WorkSpace model in signedWorkspaceByUser) {
        ListUtility.signedWorkspaceList.removeWhere((element) => element.workspaceId == item.workspaceId);
        await workspaceDBScript.deleteSignedWorkspaceOnId(item.workspaceId);
      }
    }

    // ListUtility.signedWorkspaceList = List.from(signedWorkspaceByUser);

    if (ListUtility.signedWorkspaceList.isNotEmpty) {
      if (MyApp.activeWorkspace.workspaceId == item.workspaceId) {
        onLoading(context);
        MyApp.activeWorkspace = ListUtility.signedWorkspaceList.last;
        MyApp.activeUser = ListUtility.userList.firstWhere((element) => element.username == MyApp.activeWorkspace.userName);
        await prefs.setString("activeWorkspace", jsonEncode(MyApp.activeWorkspace));
        await PermissionFunctions.getPermisionsFromLocal();
        // onStopping();
        Navigator.pop(context);

        Navigator.of(context).pushNamedAndRemoveUntil("bottombar", (Route<dynamic> route) => false);
      } else {
        Navigator.pop(context);
      }
    } else {
      MyApp.activeWorkspace = WorkSpace();
      // MyApp.activeUser = User();
      prefs.remove("activeWorkspace");
      // prefs.remove("userList");

      Navigator.of(context).pushNamedAndRemoveUntil('bottombar', (Route<dynamic> route) => false);
    }
  }

  bool checkIsPossibletoModifyRecord(dynamic reportDate, int days) {
    try {
      var difference = DateTime.now().difference(DateTime.parse(reportDate ?? DateTime.now().toString()));
      return difference.inHours <= (days * 24);
    } catch (e) {
      return false; // Handle invalid date format or null reportDate
    }
  }

  /// convert Date Month Year Format
  String? convertDateMonthYearFormat(String inputDateString, {bool isShowTime = false, bool isTimeInRow = false}) {
    if (inputDateString != "" && inputDateString.isNotEmpty) {
      DateTime inputDate = DateTime.parse(inputDateString);
      DateFormat dateFormat = DateFormat('dd-MM-yyyy');
      String dateFormatted = dateFormat.format(inputDate);

      if (isShowTime) {
        DateFormat timeFormat = DateFormat('hh.mm a');
        String timeFormatted = timeFormat.format(inputDate);
        if (isTimeInRow) {
          return '${dateFormatted + timeFormatted}';
        } else
          return '$dateFormatted\n$timeFormatted';
      } else {
        return dateFormatted;
      }
    }
    return null;
  }

    static String? concatenateDateTime(String? dateString, String? timeString) {
    List<String> dateParts = dateString!.split('/');
    String formattedDate = "${dateParts[2]}-${dateParts[1]}-${dateParts[0]}";

    DateTime date = DateTime.parse(formattedDate);
    DateTime time = DateFormat.jm().parse(timeString!);

    DateTime combinedDateTime = DateTime(
      date.year,
      date.month,
      date.day,
      time.hour,
      time.minute,
    );
    DateFormat desiredFormat = DateFormat("yyyy-MM-dd'T'HH:mm");
    String formattedDateTime = desiredFormat.format(combinedDateTime);
    // String formattedDateTime = DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(combinedDateTime.toUtc());
    return formattedDateTime;
  }

   String getTime(originalDate) {
    var date = DateFormat.jm().format(DateTime.parse(originalDate));
    return date;
  }

  String getDate(originalDate) {
    var date = DateFormat.yMMMd().format(DateTime.parse(originalDate));
    return date;
  }

  /// Function to open app in PlayStore
  openAppInPlayStore() async {
    Uri url = Uri.parse("https://play.google.com/store/apps/details?id=com.cloudstier.smartbill");
    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      toast('Could not launch $url');
    }
  }

  RichText paymentInvoicewidget(BuildContext context, String invoiceFullNo) {
    List<String> parts = invoiceFullNo.split('/');
    return RichText(
      text: TextSpan(
        // style: DefaultTextStyle.of(context).style,
        children: <TextSpan>[
          TextSpan(
            text: parts[0],
            style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontWeight: FontWeight.w500),
          ),
          // ignore: sdk_version_ui_as_code
          if (parts.length > 1 && parts[1].isNotEmpty)
            TextSpan(
              text: ' / ' + parts[1],
              style: TextStyle(color: primaryTextColor, fontWeight: FontWeight.normal, fontSize: textSizeMedium),
            )
        ],
      ),
    );
  }

  String getPaymentInvoiceNo(int index, Sales? model) {
    String invoiceFullNo = "-";
    if (index < 2 && model?.paymentInvoice != null) {
      invoiceFullNo = model?.paymentInvoice ?? "";
      if (model?.invoiceExtension != null) {
        invoiceFullNo = invoiceFullNo + (model?.invoiceExtension ?? "");
      }
    } else if (index == 2 && model?.paymentInvoice != null && model?.paymentInvoice?.isNotEmpty == true) {
      invoiceFullNo = model?.paymentInvoice ?? "";
      if (model?.invoiceExtension != null) {
        invoiceFullNo = invoiceFullNo + (model?.invoiceExtension ?? "");
      }
    }
    return invoiceFullNo;
  }
}
