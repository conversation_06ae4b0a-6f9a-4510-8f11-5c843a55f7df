// ignore_for_file: avoid_init_to_null

import 'dart:convert';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/Repositories/workspace_repository.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/screens/expenses/model/expenses_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_model.dart';
import 'package:smartbill/screens/expenses/model/purchase_transactions_model.dart';
import 'package:smartbill/screens/manage_shifts/functions/manage_shifts_function.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';
import 'package:smartbill/screens/salescategory/functions/sales_category_function.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:smartbill/utils/dbutils/advance_order_db_scripts.dart';
import '../../Repositories/image_repository.dart';
import '../../screens/expenses/db_script/purchase_db_script.dart';
import '../../screens/expenses/model/expense_category_model.dart';
import 'package:smartbill/model/sales_payment_mapping.dart'; 
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import 'package:smartbill/utils/common_function/background_sync.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/version_update_task.dart';
import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/general_settings.dart';
import 'package:smartbill/model/invoice_prefix_model.dart';
import 'package:smartbill/model/manage_sync.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/stocks.dart';
import 'package:smartbill/model/sync_request_model.dart';
import 'package:smartbill/model/sync_response_model.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/model/customers.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/dbutils/area_db_script.dart';
import 'package:smartbill/utils/dbutils/balance_db_script.dart';
import 'package:smartbill/utils/dbutils/customers_db_script.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:smartbill/utils/dbutils/online_platform_db_script.dart';
import 'package:smartbill/utils/dbutils/salestype_db_script.dart';
import 'package:smartbill/utils/dbutils/shop_details_db_script.dart';
import '../../Repositories/sync_repository.dart';
import '../../main.dart';
import '../../screens/users/demo_workspace.dart';
import '../dbutils/db_strings.dart';
import '../dbutils/discount_db_script.dart';
import '../dbutils/expences_transactions_db_script.dart';
import '../dbutils/expenses_db_script.dart';
import '../dbutils/general_setting_db_script.dart';
import '../dbutils/payment_type_db_script.dart';
import '../dbutils/price_type_db_script.dart';
import '../dbutils/product_details_db_script.dart';
import '../dbutils/product_key_db_script.dart';
import '../dbutils/sales_details_db_script.dart';
import '../dbutils/sales_payment_mapping_db_script.dart';
import '../dbutils/sales_trans_db_script.dart';
import '../dbutils/shop_tables_db_script.dart';
import '../dbutils/stocks_db_script.dart';
import '../dbutils/sync_db_script.dart';
import '../dbutils/toppings_db_script.dart';
import '../dbutils/toppings_groups_db_script.dart';
import '../dbutils/workspace_db_script.dart';
import '../dbutils/workspace_settings_db_script.dart';
import '../dbutils/common_db_script.dart';
import 'common_master_table_function.dart';
import 'value_notifier.dart';

// Define the SyncStatusInfo class
class SyncStatusInfo {
  int syncStatus;
  int syncPercentage;

  SyncStatusInfo(this.syncStatus, this.syncPercentage);
}

class SyncUtility {
  final dbHelper = DatabaseHelper.instance;
  FnUtilities fnUtilities = FnUtilities();
  CommonDB commonDB = CommonDB();
  StocksDBScript stocksDBScript = StocksDBScript();
  DiscountDB discountDB = DiscountDB();
  DBOperations dbOperation = DBOperations();
  VersionUpdateTask versionTask = VersionUpdateTask();
  CustomersDBScript customerDB = CustomersDBScript();
  AreaDBScript areaDB = AreaDBScript();
  BalanceDBScript balanceDB = BalanceDBScript();
  SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
  SalesPaymentMappingDB paymentMappingDb = SalesPaymentMappingDB();
  OnlinePlatformDBScript onlinePlatformDB = OnlinePlatformDBScript();
  ShopDetailsDBScript shopDetailsDBScript = ShopDetailsDBScript();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  SyncDBScript syncDBScript = SyncDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();
  ToppingGroupsDBScript toppingGroupsDBScript = ToppingGroupsDBScript();
  ToppingsDBScript toppingsDBscript = ToppingsDBScript();
  ShopTablesDBScript shopTableDbScript = ShopTablesDBScript();
  PaymentTypeDBScript paymentTypeDBScript = PaymentTypeDBScript();
  GeneralSettingDBScript generalSettingDBScript = GeneralSettingDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  WorkSpaceSettingsDB workSpaceSettingsDB = WorkSpaceSettingsDB();
  ExpensesDBScript expensesDBScript = ExpensesDBScript();
  PurchaseDBScript purchasesDBScript = PurchaseDBScript();
  MasterTableFunction masterTableFunction = MasterTableFunction();
  SalesCategoryFunction salesCategoryFunction = SalesCategoryFunction();
  ManageShiftsFunction manageShiftsFunction = ManageShiftsFunction();
  AdvanceOrdersDBScripts advanceOrdersDBScripts = AdvanceOrdersDBScripts();
  final ValueNotifier<int> apiCallPercentage = ValueNotifier<int>(0);
  DBFuctionsScript dbFuctionsScript = DBFuctionsScript();

  /// SYNC FUNCTION STARTED FROM THE BACK TASK ///

  Future<void> startSync({String? syncType, bool isSigning = false, bool isSalesOnly = false, bool isPullSync = false}) async {
    try {
      int currentPage = 1;
      final syncCheckResult = await commonDB.checkSync(MyApp.activeWorkspace);
      final filterType = isSigning ? "all" : "syncrecords";
      while (currentPage <= (isSalesOnly ? salesPageCount : 1)) {
        if ((syncCheckResult != 0 && !isSigning && !isSalesOnly) && !isPullSync) {
          //showToast('Auto post sync requested');
          await generateRequestModel(syncType: syncType ?? "", pageNo: isSalesOnly ? currentPage : 0);
          //showToast('Auto post sync completed');
        } else {
          final commitID = await queryGetCommitId();
          //showToast('Auto get sync requested');
          await startGetSync(filterType, syncType: syncType ?? "", lastCommitID: commitID, pageNo: isSalesOnly ? currentPage : 0);
          //showToast('Auto get sync completed');
        }
        // apiCallPercentage.value = (((currentPage / salesPageCount) * 100) ?? 0.0).round() - 1;
        int syncPercentage = (((salesPageCount != 0 ? currentPage / salesPageCount : 0) * 100)).round();
        apiCallPercentage.value = syncPercentage > 1 ? syncPercentage - 1 : 0;
        // bottomBarKey?.currentState?.updateSyncPercentage(apiCallPercentage.value);
        ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(2, apiCallPercentage.value);
        currentPage++;
      }
      int nonSyncCount = await commonDB.checkSync(MyApp.activeWorkspace) ?? 0;
      if (nonSyncCount > 0 && !isPullSync && isSalesOnly) {
        triggerManualSync();
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "startSync - SyncUtility");
    }
  }

  checkSyncStatus() async {
    int nonSyncCount = await commonDB.checkSync(MyApp.activeWorkspace) ?? 0;
    ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(nonSyncCount != 0 ? 0 : 1, 0);
  }

  /// MANUALLY TRIGGER THE BACK SYNC FUNCTION ///
  triggerManualSync({bool isSigning = false, bool isPullSync = false}) async {
    await BackGroundSync.startBackTasks('com.cloudstier.manualsync', isSigning: isSigning, isPullSync: isPullSync);
  }

  /// GENERATE REQUEST MODEL FOR POST SYNC API ///
  generateRequestModel({String? syncType, int pageNo = 0}) async {
    SyncRequest requestModel = new SyncRequest();
    requestModel.salesTypeList = await getNonSyncSalesTypeList();
    requestModel.customerList = await getNonSyncCustomerList();
    requestModel.areaList = await getNonSyncAreaList();
    requestModel.discountList = await getNonSyncDiscountList();
    requestModel.stocksList = await getNonSyncStackList();
    requestModel.stockLogsList = await getNonSyncStackLogList();
    requestModel.products = await getNonSyncProductList();
    requestModel.paymentType = await getNonSyncPaymentTypeList();
    requestModel.productKeywords = await getNonSyncProductKeyList();
    requestModel.priceTypes = await getNonSyncProductUnitList();
    requestModel.toppingsGroup = await getNonSyncToppingsGroupList();
    requestModel.topping = await getNonSyncToppingsList();
    requestModel.sales = await getNonSyncSalesList(salesCount: 15);
    List<String>? salesIds = requestModel.sales!.map((e) => e.salesId).cast<String>().toList();
    requestModel.salesTransactions = await getNonSyncTransactionsList(salesIds);
    requestModel.salesTransactionStagging = await getNonSyncTransactionsStagingList(salesIds);

    requestModel.salesPaymentMapping = await getNonSyncPaymentMappingDetails();
    requestModel.workspaceSettings = await getNonSyncSettingsList();
    requestModel.workspaceUserSettings = await getNonSyncUserSettingsList();
    requestModel.shopDetails = await getNonSyncShopDetails();
    requestModel.shopTables = await getNonSyncTablesList();
    requestModel.lastCommitId = await queryGetCommitId();
    requestModel.workspaceId = MyApp.activeWorkspace.workspaceId;
    requestModel.onlinePlatform = await getNonSyncOnlinePlatformList();
    requestModel.expensesProduct = await getNonSyncExpenseItemList();
    requestModel.expenseCategory = await getNonSyncExpenseCategoriesList();
    requestModel.expenses = await getNonSyncExpenseList();
    requestModel.expenseTransaction = await getNonSyncexpenseTransaction();
    requestModel.purchase = await getNonSyncPurchasesList();
    requestModel.purchaseTransaction = await getNonSyncPurchaseTransactionsList();
    requestModel.salesCategory = await salesCategoryFunction.getNonSyncSalesCategoryList();
    requestModel.shiftWiseSales = await manageShiftsFunction.getNonSyncManageShiftList();
    requestModel.advanceOrderNotes = await getNonSyncAdvanceOrderList();
    requestModel.salesLog = [];
    requestModel.salesTransactionsLog = [];
    await startUploadImageSync("products");
    await startUploadImageSync("shops");
    if (requestModel.workspaceSettings!.length > 0 || requestModel.workspaceUserSettings!.length > 0) {
      syncType = '';
    }
    await startPostSync(syncType ?? "", requestModel, pageNo: pageNo);
  }

  /// CALL THE GET SYNC API ///
  startGetSync(String filter, {String? syncType, String? lastCommitID, int pageNo = 0}) async {
    try {
      await getSyncApi(filter, syncType, lastCommitID, pageNo: pageNo).then((value) async {
        await syncResponse(value, pageNo: pageNo);
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "startGetSync - SyncUtility");
    }
  }

  /// CALL THE POST SYNC API ///
  startPostSync(String syncType, SyncRequest model, {int pageNo = 0}) async {
    await postSyncApi(syncType, model, pageNo: pageNo).then((value) async {
      await syncResponse(value, pageNo: pageNo);
    });
  }

  /// GET THE SYNC RESPONSE FOR BOTH GET AND POST API ///

  syncResponse(GetSyncResponse value, {int pageNo = 0}) async {
    // return;
    try {
      if (value.status == 1 && value.result != null) {
        if (value.result?.isResetDemoWorkspace ?? false) {
          if (value.result?.isResetDemoWorkspace ?? false) {
            Demoworkspace demo = Demoworkspace();
            await demo.resetWorkSpace(true);
          }
        }

        /// sales page count
        if (pageNo > 0) {
          if (value.result?.sales != null && value.result?.sales?.length != 0) {
            await syncSalesFromApi(value.result?.sales ?? []);
          }
          if (value.result?.salesTransactions != null && value.result?.salesTransactions?.length != 0) {
            await syncTransactionsFromApi(value.result?.salesTransactions ?? []);
          }
          if (value.result?.salesTransactionStagging != null && value.result?.salesTransactionStagging?.length != 0) {
            await syncTransactionsStagingFromApi(value.result?.salesTransactionStagging ?? []);
          }
          if (value.result?.salesPaymentMappingList != null && value.result?.salesPaymentMappingList?.length != 0) {
            await syncSalesPaymentMappingFromApi(value.result?.salesPaymentMappingList ?? []);
          }
        } else {
          salesPageCount = value.result?.pageCount ?? 0;
          log(salesPageCount.toString());
          // salesPageCount = 1;
          if (value.result?.generalSettings != null && value.result?.generalSettings?.length != 0) {
            await syncGeneralSettingsFromApi(value.result?.generalSettings ?? []);
          }
          // if (value.result.workspaceUserSettings != null && value.result.workspaceUserSettings.length != 0) {
          //   String roleID =
          //       value.result?.workspaceUserSettings?.singleWhere((element) => element.settingKey == "RoleId", orElse: () => null)?.settingValue;
          //   // dbOperation.getPermissionDetails(roleID);
          // }
          if (value.result?.workspaceSettings != null && value.result?.workspaceSettings?.length != 0) {
            await syncSettingsFromApi(value.result?.workspaceSettings ?? []);
          }
          if (value.result?.workspaceUserSettings != null && value.result?.workspaceUserSettings?.length != 0) {
            await syncSettingsFromApi(value.result?.workspaceUserSettings ?? [], table: "WorkSpaceUserSettings");
          }
          if (value.result?.products != null && value.result?.products?.length != 0) {
            await syncProductsFromApi(value.result?.products ?? []);
          }
          if (value.result?.productKeywords != null && value.result?.productKeywords?.length != 0) {
            await syncProductsKeyFromApi(value.result?.productKeywords ?? []);
          }
          if (value.result?.priceTypes != null && value.result?.priceTypes?.length != 0) {
            await syncProductsUnitFromApi(value.result?.priceTypes ?? []);
          }
          if (value.result?.toppingsGroup != null && value.result?.toppingsGroup?.length != 0) {
            await syncToppingsGroupFromApi(value.result?.toppingsGroup ?? []);
          }
          if (value.result?.topping != null && value.result?.topping?.length != 0) {
            await syncToppingsFromApi(value.result?.topping ?? []);
          }
          if (value.result?.shopDetails != null && value.result?.shopDetails != null) {
            await syncShopDetailsFromApi(value.result?.shopDetails);
          }
          if (value.result?.shopTables != null && value.result?.shopTables?.length != 0) {
            await syncTablesFromApi(value.result?.shopTables ?? []);
          }
          if (value.result?.salesCount != null) {
            SharedPreferences prefs = await SharedPreferences.getInstance();
            prefs.setInt("salesCount", value.result!.salesCount!);
          }
          if (value.result?.paymentType != null && value.result?.paymentType?.length != 0) {
            await syncPaymentTypeFromApi(value.result?.paymentType ?? []);
          }
          if (value.result?.stocksList != null && value.result?.stocksList?.length != 0) {
            await syncStockFromApi(value.result?.stocksList ?? []);
          }
          if (value.result?.stockLogsList != null && value.result?.stockLogsList?.length != 0) {
            await syncStockLogFromApi(value.result?.stockLogsList ?? []);
          }
          if (value.result?.discountList != null && value.result?.discountList?.length != 0) {
            await syncDiscountFromApi(value.result?.discountList ?? []);
          }
          if (value.result?.customerList != null && value.result?.customerList?.length != 0) {
            await syncCustomerFromApi(value.result?.customerList ?? []);
          }
          if (value.result?.areaList != null && value.result?.areaList?.length != 0) {
            await syncAreaFromApi(value.result?.areaList ?? []);
          }
          if (value.result?.balanceList != null && value.result?.balanceList?.length != 0) {
            await syncBalanceFromApi(value.result?.balanceList ?? []);
          }
          if (value.result?.salesTypeList != null && value.result?.salesTypeList?.length != 0) {
            await syncSalesTypeFromApi(value.result?.salesTypeList ?? []);
          }
          if (value.result?.onlinePlatform != null && value.result?.onlinePlatform?.length != 0) {
            await syncOnlinePlatformFromApi(value.result?.onlinePlatform ?? []);
          }
          if (value.result?.commits != null && value.result?.commits?.length != 0) {
            //await insertCommitID(value.result.commits);
            commitIdList = value.result?.commits ?? [];
          }
          if (value.result?.expenseProduct?.length != null && value.result?.expenseProduct?.length != 0) {
            await syncExpenseProductFromApi(value.result?.expenseProduct ?? []);
          }
          if (value.result?.expenseCategory?.length != null && value.result?.expenseCategory?.length != 0) {
            await syncExpenseCategoryFromApi(value.result?.expenseCategory ?? []);
          }

          if (value.result?.expenses?.length != null && value.result?.expenses?.length != 0) {
            await syncExpenseFromApi(value.result?.expenses ?? []);
          }

          if (value.result?.expenseTransaction?.length != null && value.result?.expenseTransaction?.length != 0) {
            await syncExpenseTransactionFromApi(value.result?.expenseTransaction ?? []);
          }

          if (value.result?.purchase?.length != null && value.result?.purchase?.length != 0) {
            await syncPurchaseFromApi(value.result?.purchase ?? []);
          }

          if (value.result?.purchaseTransaction?.length != null && value.result?.purchaseTransaction?.length != 0) {
            await syncPurchaseTransactionsFromApi(value.result?.purchaseTransaction ?? []);
          }

          await salesCategoryFunction.syncSalesCategoryFromApi(value.result?.salesCategory ?? []);
          await manageShiftsFunction.syncManageShiftsFromApi(value.result?.shiftWiseSales ?? []);

          if (value.result?.advanceOrderNotes?.length != null && value.result?.advanceOrderNotes?.length != 0) {
            await syncAdvanceOrderFromApi(value.result?.advanceOrderNotes ?? []);
          }

          await versionTask.versionBasedUpdateFunction();
          if (MyApp.isDemoAccount ?? false) {
            Demoworkspace demo = Demoworkspace();
            var expiryDate = await demo.updateDemoWorkSpaceExpiryDate();
            updateExpDateForWorkspace(expiryDate.toString());
          } else {
            updateExpDateForWorkspace(value.result?.expiryDate ?? "");
          }
        }
        if (pageNo == salesPageCount) {
          if (commitIdList.length != 0) {
            List<Commit> commitIdTempList = [];
            commitIdTempList.addAll(commitIdList);
            await insertCommitID(commitIdTempList);
            commitIdList.clear();
            //commitIdList = value.result.commits;
          }
        }
      } else {
        salesPageCount = 0;
        if (value.status == 0) {
          showToast(value.message);
        }
        else if (value.status == 2){
          isSyncUpToDate = true;
        }
      }
    } catch (e) {
      salesPageCount = 0;
      print(e);
      throw e;
    }
  }

  /// GET ALL NOT SYNC RECORDS FROM ALL TABLES ///
  Future<List<Products>> getNonSyncProductList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('ProductDetails', 'productSync', 0);
    List<Products> productsList = [];
    allRows.forEach((row) {
      productsList.add(Products.fromMap(row));
    });
    return productsList;
  }

  Future<List<Stocks>> getNonSyncStackList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('Stocks', 'stocksSync', 0);
    List<Stocks> stockList = [];
    allRows.forEach((row) {
      stockList.add(Stocks.fromMap(row));
    });
    return stockList;
  }

  Future<List<StockLogs>> getNonSyncStackLogList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('StockLogs', 'stockLogsSync', 0);
    List<StockLogs> stockLongList = [];
    allRows.forEach((row) {
      stockLongList.add(StockLogs.fromMap(row));
    });
    return stockLongList;
  }

  Future<List<PaymentType>> getNonSyncPaymentTypeList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('PaymentTypes', 'paymentTypeSync', 0);
    List<PaymentType> paymentTypeList = [];
    allRows.forEach((row) {
      paymentTypeList.add(PaymentType.fromMap(row));
    });
    return paymentTypeList;
  }

  Future<List<ProductKey>> getNonSyncProductKeyList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('ProductKeyword', 'keywordSync', 0);
    List<ProductKey> productsKeyList = [];
    allRows.forEach((row) {
      productsKeyList.add(ProductKey.fromMap(row));
    });
    return productsKeyList;
  }

  Future<List<PriceUnitType>> getNonSyncProductUnitList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('PriceTypes', 'measurementSync', 0);
    List<PriceUnitType> productsUnitList = [];
    allRows.forEach((row) {
      productsUnitList.add(PriceUnitType.fromMap(row));
    });
    return productsUnitList;
  }

  Future<List<ToppingsGroup>> getNonSyncToppingsGroupList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('ToppingGroups', 'toppingGroupSync', 0);
    List<ToppingsGroup> toppingsGroupList = [];
    allRows.forEach((row) {
      toppingsGroupList.add(ToppingsGroup.fromMap(row));
    });
    return toppingsGroupList;
  }

  Future<List<Topping>> getNonSyncToppingsList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('Toppings', 'toppingSync', 0);
    List<Topping> toppingsList = [];
    allRows.forEach((row) {
      toppingsList.add(Topping.fromMap(row));
    });
    return toppingsList;
  }

  ///payment Mapping details
  Future<List<SalesPaymentMapping>> getNonSyncPaymentMappingDetails() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('SalesPaymentMappingDetails', 'paymentSync', 0);
    List<SalesPaymentMapping> paymentMappingList = [];
    allRows.forEach((row) {
      paymentMappingList.add(SalesPaymentMapping.fromJson(row));
    });
    return paymentMappingList;
  }

  Future<List<Sales>> getNonSyncSalesList({int salesCount = 0}) async {
    // final allRows = await dbHelper.queryForNonSyncSalesData();
    final allRows = await commonDB.getAllListDataBasedOneColumn('SalesDetails', 'salesSync', 0, limit: salesCount);
    List<Sales> salesList = [];
    allRows.forEach((row) {
      salesList.add(Sales.fromMap(row));
    });
    return salesList;
  }

  Future<List<Transactions>> getNonSyncTransactionsList(List<String>? salesIds) async {
    final allRows = await commonDB.getAllListDataBasedOnColumnAndSalesId('SalesTransactions', 'salesTransactionSync', 0, salesIds ?? []);
    List<Transactions> transactionsList = [];
    allRows.forEach((row) {
      transactionsList.add(Transactions.fromMap(row));
    });
    return transactionsList;
  }

  Future<List<Transactions>> getNonSyncTransactionsStagingList(List<String> salesIds) async {
    final allRows = await commonDB.getAllListDataBasedOnColumnAndSalesId('SalesTransactionsStaging', 'salesTransactionSync', 0, salesIds);
    List<Transactions> transactionsList = [];
    allRows.forEach((row) {
      transactionsList.add(Transactions.fromMap(row));
    });
    return transactionsList;
  }

  Future<List<WorkspaceSettings>> getNonSyncSettingsList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('WorkSpaceSettings', 'settingSync', 0);
    List<WorkspaceSettings> settingsList = [];
    allRows.forEach((row) {
      settingsList.add(WorkspaceSettings.fromMap(row));
    });
    return settingsList;
  }

  Future<List<WorkspaceSettings>> getNonSyncUserSettingsList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('WorkSpaceUserSettings', 'settingSync', 0);
    List<WorkspaceSettings> settingsList = [];
    allRows.forEach((row) {
      settingsList.add(WorkspaceSettings.fromMap(row));
    });
    return settingsList;
  }

  Future<ShopDeatails?> getNonSyncShopDetails() async {
    final allRows = await commonDB.getSingleDataBasedOneColumn('ShopDetails', 'shopSync', 0);
    ShopDeatails? model;
    model = allRows != null ? ShopDeatails.fromMap(allRows) : null;
    return model;
  }

  Future<List<ShopTable>> getNonSyncTablesList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('ShopTables', 'shopTableSync', 0);
    List<ShopTable> shopTablesList = [];
    allRows.forEach((row) {
      shopTablesList.add(ShopTable.fromMap(row));
    });
    return shopTablesList;
  }

  Future<List<Discounts>> getNonSyncDiscountList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('Discount', 'discountSync', 0);
    List<Discounts> discountList = [];
    allRows.forEach((row) {
      discountList.add(Discounts.fromMap(row));
    });
    return discountList;
  }

  Future<List<Customer>> getNonSyncCustomerList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('CUSTOMERS', 'SyncStatus', 0);
    List<Customer> customerList = [];
    allRows.forEach((row) {
      customerList.add(Customer.fromMap(row));
    });
    return customerList;
  }

  Future<List<Area>> getNonSyncAreaList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('AREAS', 'SyncStatus', 0);
    List<Area> areaList = [];
    allRows.forEach((row) {
      areaList.add(Area.fromMap(row));
    });
    return areaList;
  }

  Future<List<SalesType>> getNonSyncSalesTypeList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn('SALESTYPE', 'SyncStatus', 0);
    List<SalesType> salesTypeList = [];
    allRows.forEach((row) {
      salesTypeList.add(SalesType.fromMap(row));
    });
    return salesTypeList;
  }

  Future<List<OnlinePlatformModel>> getNonSyncOnlinePlatformList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_salesPaymentMapping_onlinePlatform, db_common_syncStatus, 0);
    List<OnlinePlatformModel> onlinePlatformList = [];
    allRows.forEach((row) {
      onlinePlatformList.add(OnlinePlatformModel.fromJsonDB(row));
    });
    return onlinePlatformList;
  }

  Future<List<ExpensesProduct>> getNonSyncExpenseItemList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_expensesProduct, db_expProductSync, 0);
    List<ExpensesProduct> expenseItemList = [];
    allRows.forEach((row) {
      expenseItemList.add(ExpensesProduct.fromJson(row));
    });
    return expenseItemList;
  }

  Future<List<ExpensesCategoryModel>> getNonSyncExpenseCategoriesList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_expensesCategory, db_expensesCategory_expCategorySync, 0);
    List<ExpensesCategoryModel> expenseCategoriesList = [];
    allRows.forEach((row) {
      expenseCategoriesList.add(ExpensesCategoryModel.fromJson(row));
    });
    return expenseCategoriesList;
  }

  Future<List<ExpensesModel>> getNonSyncExpenseList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_expenses, db_expSync, 0);
    List<ExpensesModel> expenseList = [];
    allRows.forEach((row) {
      expenseList.add(ExpensesModel.fromJson(row));
    });
    return expenseList;
  }

  Future<List<ExpenseTransactionModel>> getNonSyncexpenseTransaction() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_expenseTransactions, db_expTransSync, 0);
    List<ExpenseTransactionModel> expenseTransactionList = [];
    allRows.forEach((row) {
      expenseTransactionList.add(ExpenseTransactionModel.fromJson(row));
    });
    return expenseTransactionList;
  }

  Future<List<PurchaseModel>> getNonSyncPurchasesList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_purchase, db_purchaseSync, 0);
    List<PurchaseModel> purchaseList = [];
    allRows.forEach((row) {
      purchaseList.add(PurchaseModel.fromJson(row));
    });
    return purchaseList;
  }

  Future<List<PurchaseTransactionsModel>> getNonSyncPurchaseTransactionsList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_purchaseTransaction, db_purchaseTransSync, 0);
    List<PurchaseTransactionsModel> purchaseTransactionsList = [];
    allRows.forEach((row) {
      purchaseTransactionsList.add(PurchaseTransactionsModel.fromJson(row));
    });
    return purchaseTransactionsList;
  }

  Future<List<ShiftwiseSalesModel>> getNonSyncShiftSalesList() async {
    if (ListUtility.shiftSalesList.isNotEmpty) {
      List<ShiftwiseSalesModel> shiftSalesList = [];
      shiftSalesList.clear();
      shiftSalesList.addAll(ListUtility.shiftSalesList.where((element) => element.sync == 0));
      return shiftSalesList;
    }
    return [];
  }

  Future<List<AdvanceOrderNoteModel>> getNonSyncAdvanceOrderList() async {
    final allRows = await commonDB.getAllListDataBasedOneColumn(db_order_notes, db_note_sync, 0);
    List<AdvanceOrderNoteModel> advanceOrderList = [];
    allRows.forEach((row) {
      advanceOrderList.add(AdvanceOrderNoteModel.fromJson(row));
    });
    return advanceOrderList;
  }

  Future<String?> queryGetCommitId() async {
    final count = await syncDBScript.queryGetCommitIdCount();
    final allRows = count != 0 ? await syncDBScript.queryGetLastCommitId() : null;
    return allRows;
  }

  /// CHECK THE TABLE AND ROW DYNAMICALLY HAS VALUES OR NOT FOR INSERT OR UPDATE THE DATA ///
  Future<int> checkValueExists(String tName, {String? cName, String? cValue}) async {
    if (!tName.isEmptyOrNull && !cName.isEmptyOrNull) {
      return await commonDB.checkValueExistsOneColumn(tName, cName ?? "", cValue ?? "") ?? 0;
    } else {
      return await commonDB.checkTableisEmpty(tName) ?? 0;
    }
  }

  /// UPDATE OR INSERT ALL RECORDS FROM THE SYNC API ///

  syncProductsFromApi(List<Products> productsList) async {
    if (await checkValueExists('ProductDetails') > 0) {
      for (Products t in productsList) {
        if (await checkValueExists('ProductDetails', cName: 'productId', cValue: t.productId ?? "") > 0) {
          int affected = await updateProductsDetails(t);
          if (affected > 0 && !t.image.isEmptyOrNull) {
            downloadImage(t.image, "products").then((value) {});
          }
        } else {
          int affected = await insertProducts(t);
          if (affected > 0 && !t.image.isEmptyOrNull) {
            downloadImage(t.image, "products").then((value) {});
          }
        }
      }
    } else {
      for (Products t in productsList) {
        int affected = await insertProducts(t);
        if (affected > 0 && !t.image.isEmptyOrNull) {
          downloadImage(t.image, "products").then((value) {});
        }
      }
    }
  }

  syncProductsKeyFromApi(List<ProductKey> productsKeyList) async {
    if (await checkValueExists('ProductKeyword') > 0) {
      for (ProductKey t in productsKeyList) {
        if (await checkValueExists('ProductKeyword', cName: 'productKeyId', cValue: t.productKeyId ?? "") > 0) {
          updateProductsKeyDetails(t);
        } else {
          insertProductsKey(t);
        }
      }
    } else {
      for (ProductKey t in productsKeyList) {
        insertProductsKey(t);
      }
    }
  }

  syncProductsUnitFromApi(List<PriceUnitType> productsUnitList) async {
    if (await checkValueExists('PriceTypes') > 0) {
      for (PriceUnitType t in productsUnitList) {
        if (await checkValueExists('PriceTypes', cName: 'priceTypeId', cValue: t.priceTypeId ?? "") > 0) {
          updateProductsUnitDetails(t);
        } else {
          insertProductsUnit(t);
        }
      }
    } else {
      for (PriceUnitType t in productsUnitList) {
        insertProductsUnit(t);
      }
    }
  }

  syncToppingsGroupFromApi(List<ToppingsGroup> toppingGroupList) async {
    if (await checkValueExists('ToppingGroups') > 0) {
      for (ToppingsGroup t in toppingGroupList) {
        if (await checkValueExists('ToppingGroups', cName: 'toppingGroupId', cValue: t.toppingGroupId ?? "") > 0) {
          updateToppingGroupDetails(t);
        } else {
          insertToppingsGroup(t);
        }
      }
    } else {
      for (ToppingsGroup t in toppingGroupList) {
        insertToppingsGroup(t);
      }
    }
  }

  syncToppingsFromApi(List<Topping> toppingsList) async {
    if (await checkValueExists('Toppings') > 0) {
      for (Topping t in toppingsList) {
        if (await checkValueExists('Toppings', cName: 'toppingId', cValue: t.toppingId ?? "") > 0) {
          await updateToppingDetails(t);
        } else {
          await insertToppings(t);
        }
      }
    } else {
      for (Topping t in toppingsList) {
        await insertToppings(t);
      }
    }
  }

  syncTablesFromApi(List<ShopTable> tablesList) async {
    if (await checkValueExists('ShopTables') > 0) {
      for (ShopTable s in tablesList) {
        if (await checkValueExists('ShopTables', cName: 'shopTableId', cValue: s.shopTableId ?? "") > 0) {
          updateTablesDetails(s);
        } else {
          insertTablesDetails(s);
        }
      }
    } else {
      for (ShopTable s in tablesList) {
        insertTablesDetails(s);
      }
    }
  }

  syncPaymentTypeFromApi(List<PaymentType> paymentTypeList) async {
    if (await checkValueExists('PaymentTypes') > 0) {
      for (PaymentType s in paymentTypeList) {
        s.workspaceID = MyApp.activeWorkspace.workspaceId;
        s.syncStatus = 1;
        if (await checkValueExists('PaymentTypes', cName: 'paymentTypeId', cValue: s.paymentTypeID ?? "") > 0) {
          s.rowStatus = s.isActive == true ? 1 : 2;
          paymentTypeDBScript.updatePaymentType(s);
        } else {
          s.rowStatus = s.isActive == true ? 0 : 2;
          paymentTypeDBScript.insertPaymentType(s);
        }
      }
    } else {
      for (PaymentType s in paymentTypeList) {
        s.workspaceID = MyApp.activeWorkspace.workspaceId;
        s.syncStatus = 1;
        s.rowStatus = s.isActive == true ? 0 : 2;
        paymentTypeDBScript.insertPaymentType(s);
      }
    }
  }

  syncDiscountFromApi(List<Discounts> discountList) async {
    if (await checkValueExists('Discount') > 0) {
      for (Discounts obj in discountList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.discountSync = 1;
        if (await checkValueExists('Discount', cName: 'discountID', cValue: obj.discountID ?? "") > 0) {
          obj.rowStatus = obj.isActive == true ? 1 : 2;
          await discountDB.updateDiscount(obj);
        } else {
          obj.rowStatus = obj.isActive == true ? 0 : 2;
          await discountDB.insertDiscount(obj);
        }
      }
    } else {
      for (Discounts obj in discountList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.discountSync = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await discountDB.insertDiscount(obj);
      }
    }
  }

  syncSalesFromApi(List<Sales> reportListFromApi) async {
    try {
      if (await checkValueExists('SalesDetails') > 0) {
        for (Sales t in reportListFromApi) {
          if (await checkValueExists('SalesDetails', cName: 'salesId', cValue: t.salesId ?? "") > 0) {
            t = await checkSyncPriority(t);
            if (isPullSyncProgress) {
              await salesUpdate(t);
            } else {
              var salesDetailsMap;
              // t.rowStatus = t.isActive ?? false ? 1 : 2;
              salesDetailsMap = {
                db_sales_invoiceNo: t.invoiceNo,
                db_sales_invoice_Extension: t.invoiceExtension,
                db_sales_salesSync: t.sync,
                db_product_rowStatus: t.rowStatus,
                db_sales_payment_Invoice: t.paymentInvoice
              };
              await dbFuctionsScript.updateModel(db_salesDetails, salesDetailsMap, db_sales_salesId, t.salesId!);
            }
          } else {
            await salesInsert(t);
          }
        }
      } else {
        for (Sales t in reportListFromApi) {
          await salesInsert(t);
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "syncSalesFromApi - SyncUtility");
    }
  }

  syncTransactionsFromApi(List<Transactions> transactionsListFromApi) async {
    try {
      if (await checkValueExists('SalesTransactions') > 0) {
        for (Transactions t in transactionsListFromApi) {
          if (await checkValueExists('SalesTransactions', cName: 'transactId', cValue: t.transactId ?? "") > 0) {
            if (isPullSyncProgress) {
              await transactionsUpdate(t);
            } else {
              t.sync = 1;
              t.rowStatus = t.isActive ?? false ? 1 : 2;
              await dbFuctionsScript.commonUpdateForSyncAndStatus(db_salesTransactions, db_salesTrans_salesTransactionSync, t.sync!,
                  db_product_rowStatus, t.rowStatus!, db_salesTrans_transactId, t.transactId!,
                  cWorkSpaceId: db_workspace_workspaceId);
            }
          } else {
            await transactionInsert(t);
          }
        }
      } else {
        for (Transactions t in transactionsListFromApi) {
          await transactionInsert(t);
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "syncTransactionsFromApi - SyncUtility");
    }
  }

  syncTransactionsStagingFromApi(List<Transactions> transactionsListFromApi) async {
    try {
      if (await checkValueExists('SalesTransactionsStaging') > 0) {
        for (Transactions t in transactionsListFromApi) {
          if (await checkValueExists('SalesTransactionsStaging', cName: 'transactId', cValue: t.transactId ?? "") > 0) {
            if (isPullSyncProgress) {
              await transactionsUpdate(t, transactionType: 'staging');
            } else {
              t.sync = 1;
              t.rowStatus = t.isActive ?? false ? 1 : 2;
              await dbFuctionsScript.commonUpdateForSyncAndStatus(db_salesTrans_salesTransactionsStaging, db_salesTrans_salesTransactionSync, t.sync!,
                  db_product_rowStatus, t.rowStatus!, db_salesTrans_transactId, t.transactId!,
                  cWorkSpaceId: db_workspace_workspaceId);
            }
          } else {
            await transactionInsert(t, transactionType: 'staging');
          }
        }
      } else {
        for (Transactions t in transactionsListFromApi) {
          await transactionInsert(t, transactionType: 'staging');
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "syncTransactionsStagingFromApi - SyncUtility");
    }
  }

  syncSettingsFromApi(List<WorkspaceSettings> setingListFromApi, {String table = "WorkSpaceSettings"}) async {
    if (await checkValueExists(table) > 0) {
      for (WorkspaceSettings model in setingListFromApi) {
        if (await checkValueExists('$table', cName: 'settingKey', cValue: model.settingKey ?? "") > 0) {
          updateSettingDetails(model, tableType: table);
        } else {
          fnUtilities.insertSettingDetails(model, 1, tableType: table);
        }
      }
    } else {
      for (WorkspaceSettings model in setingListFromApi) {
        fnUtilities.insertSettingDetails(model, 1, tableType: table);
      }
    }
  }

  syncShopDetailsFromApi(ShopDeatails? model) async {
    if (model != null) {
      if (await shopDetailsDBScript.queryShopDetailExists() == 0) {
        model.shopSync = 1;
        model.status = 0;
        await shopDetailsDBScript.insertShopDetails(model);
        if (!model.imageID.isEmptyOrNull) {
          final String saveDirectory = (await getApplicationDocumentsDirectory()).path;
          String savePath = '$saveDirectory/${model.imageID}.png';
          bool fileExists = await File(savePath).exists();
          if (!fileExists) {
            await downloadImage(model.imageID, "shops").then((value) {});
          }
        }
      } else {
        model.shopSync = 1;
        model.status = 1;
        await shopDetailsDBScript.updateShopDetails(model);
        if (!model.imageID.isEmptyOrNull) {
          final String saveDirectory = (await getApplicationDocumentsDirectory()).path;
          String savePath = '$saveDirectory/${model.imageID}.png';
          bool fileExists = await File(savePath).exists();
          if (!fileExists) {
            await downloadImage(model.imageID, "shops").then((value) {});
          }
        }
      }
    }
  }

  syncGeneralSettingsFromApi(List<GeneralSetting> generalSettingsList) async {
    List<dynamic> generalSettingsNameList = await getAllGeneralSettingsList();
    if (generalSettingsNameList.isNotEmpty) {
      for (GeneralSetting obj in generalSettingsList) {
        if (generalSettingsNameList.contains(obj.settingKey)) {
          await generalSettingDBScript.updateGeneralSettings(obj);
        } else {
          await generalSettingDBScript.insertGeneralSettings(obj);
        }
      }
    } else {
      for (GeneralSetting obj in generalSettingsList) {
        await generalSettingDBScript.insertGeneralSettings(obj);
      }
    }
  }

  syncStockFromApi(List<Stocks> stocksList) async {
    if (await checkValueExists('Stocks') > 0) {
      for (Stocks obj in stocksList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        if (await checkValueExists('Stocks', cName: 'stocksId', cValue: obj.stocksID ?? "") > 0) {
          obj.rowStatus = obj.isActive == true ? 1 : 2;
          await stocksDBScript.updateStocks(obj);
        } else {
          obj.rowStatus = obj.isActive == true ? 0 : 2;
          await stocksDBScript.insertStocks(obj);
        }
      }
    } else {
      for (Stocks obj in stocksList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await stocksDBScript.insertStocks(obj);
      }
    }
  }

  syncStockLogFromApi(List<StockLogs> stocksLogList) async {
    if (await checkValueExists('StockLogs') > 0) {
      for (StockLogs obj in stocksLogList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        if (await checkValueExists('StockLogs', cName: 'stockLogsId', cValue: obj.stockLogsID ?? "") > 0) {
          obj.rowStatus = obj.isActive == true ? 1 : 2;
          await stocksDBScript.updateStocksLog(obj);
        } else {
          obj.rowStatus = obj.isActive == true ? 0 : 2;
          await stocksDBScript.insertStockLogs(obj);
        }
      }
    } else {
      for (StockLogs obj in stocksLogList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await stocksDBScript.insertStockLogs(obj);
      }
    }
  }

  syncCustomerFromApi(List<Customer> customersList) async {
    if (await checkValueExists('CUSTOMERS') > 0) {
      for (Customer model in customersList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.syncStatus = 1;
        if (await checkValueExists('CUSTOMERS', cName: 'CustomerID', cValue: model.customerID ?? "") > 0) {
          model.rowStatus = model.isActive == true ? 1 : 2;
          await customerDB.updateCustomer(model);
        } else {
          model.rowStatus = model.isActive == true ? 0 : 2;
          await customerDB.insertCustomer(model);
        }
      }
    } else {
      for (Customer obj in customersList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await customerDB.insertCustomer(obj);
      }
    }
  }

  syncAreaFromApi(List<Area> areasList) async {
    if (await checkValueExists('AREAS') > 0) {
      for (Area model in areasList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.syncStatus = 1;
        if (await checkValueExists('AREAS', cName: 'AreaID', cValue: model.areaID ?? "") > 0) {
          model.rowStatus = model.isActive == true ? 1 : 2;
          await areaDB.updateArea(model);
        } else {
          model.rowStatus = model.isActive == true ? 0 : 2;
          await areaDB.insertArea(model);
        }
      }
    } else {
      for (Area obj in areasList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await areaDB.insertArea(obj);
      }
    }
  }

  syncBalanceFromApi(List<Balance> balanceList) async {
    if (await checkValueExists('BALANCE') > 0) {
      for (Balance model in balanceList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        if (await checkValueExists('BALANCE', cName: 'FK_CustomerID', cValue: model.customerID ?? "") > 0) {
          model.rowStatus = 1;
          await balanceDB.updateBalance(model);
        } else {
          model.rowStatus = 0;
          await balanceDB.insertBalance(model);
        }
      }
    } else {
      for (Balance obj in balanceList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.rowStatus = 0;
        await balanceDB.insertBalance(obj);
      }
    }
  }

  syncSalesTypeFromApi(List<SalesType> salesTypeList) async {
    if (await checkValueExists('SALESTYPE') > 0) {
      for (SalesType model in salesTypeList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.syncStatus = 1;
        if (await checkValueExists('SALESTYPE', cName: 'SalesTypeID', cValue: model.salesTypeID ?? "") > 0) {
          model.rowStatus = model.isActive == true ? 1 : 2;
          await salesTypeDB.updateSalesTypeSync(model);
        } else {
          model.rowStatus = model.isActive == true ? 0 : 2;
          await salesTypeDB.insertSalesType(model);
        }
      }
    } else {
      for (SalesType obj in salesTypeList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await salesTypeDB.insertSalesType(obj);
      }
    }
  }

  syncOnlinePlatformFromApi(List<OnlinePlatformModel> salesTypeList) async {
    if (await checkValueExists(db_salesPaymentMapping_onlinePlatform) > 0) {
      for (OnlinePlatformModel model in salesTypeList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.createdDate = DateTime.now().toString();
        model.syncStatus = 1;
        if (await checkValueExists(db_salesPaymentMapping_onlinePlatform,
                cName: db_salesPaymentMapping_onlinePlatformId, cValue: model.onlinePlatformId ?? "") >
            0) {
          model.rowStatus = model.isActive == true ? 1 : 2;
          await onlinePlatformDB.updateOnlinePlatform(model);
        } else {
          model.rowStatus = model.isActive == true ? 0 : 2;
          await onlinePlatformDB.insertOnlinePlatform(model);
        }
      }
    } else {
      for (OnlinePlatformModel obj in salesTypeList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.createdDate = DateTime.now().toString();
        obj.syncStatus = 1;
        obj.rowStatus = obj.isActive == true ? 0 : 2;
        await onlinePlatformDB.insertOnlinePlatform(obj);
      }
    }
  }

  ///Sync Payment Mapping From API
  syncSalesPaymentMappingFromApi(List<SalesPaymentMapping> salesMappingList) async {
    try {
      if (await checkValueExists('SalesPaymentMappingDetails') > 0) {
        for (SalesPaymentMapping model in salesMappingList) {
          model.workspaceId = MyApp.activeWorkspace.workspaceId;
          model.paymentSync = 1;
          if (await checkValueExists('SalesPaymentMappingDetails', cName: 'paymentMappingId', cValue: model.paymentMappingId ?? "") > 0) {
            model.status = model.isActive == true ? 1 : 2;
            if (isPullSyncProgress) {
              await paymentMappingDb.updatePaymentMappingSync(model);
            } else {
              await dbFuctionsScript.commonUpdateForSyncAndStatus(db_salesPaymentMappingDetails, db_salesPaymentMapping_paymentSync,
                  model.paymentSync!, db_product_rowStatus, model.status!, db_salesPaymentMapping_paymentMappingId, model.paymentMappingId!,
                  cWorkSpaceId: db_workspace_workspaceId);
            }
          } else {
            model.status = model.isActive == true ? 0 : 2;
            await paymentMappingDb.insertSalesPaymentMapping(model);
          }
        }
      } else {
        for (SalesPaymentMapping obj in salesMappingList) {
          obj.workspaceId = MyApp.activeWorkspace.workspaceId;
          obj.paymentSync = 1;
          obj.status = obj.isActive == true ? 0 : 2;
          await paymentMappingDb.insertSalesPaymentMapping(obj);
        }
      }
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "syncSalesPaymentMappingFromApi - SyncUtility");
    }
  }

  ///Sync Expense Categories From API
  syncExpenseCategoryFromApi(List<ExpensesCategoryModel> expCategoriesList) async {
    if (await checkValueExists(db_expensesCategory) > 0) {
      for (ExpensesCategoryModel model in expCategoriesList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.expCategorySync = 1;
        if (await checkValueExists(db_expensesCategory, cName: db_expensesCategory_expCategoryID, cValue: model.expCategoryID ?? "") > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await expensesDBScript.upsetExpenseCategory(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await expensesDBScript.upsetExpenseCategory(model, "insert");
        }
      }
    } else {
      for (ExpensesCategoryModel obj in expCategoriesList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.expCategorySync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await expensesDBScript.upsetExpenseCategory(obj, "insert");
      }
    }
  }

  ///sync ExpenseItem from API
  syncExpenseProductFromApi(List<ExpensesProduct> expItemList) async {
    ExpensesDBScript expensesDBScript = ExpensesDBScript();
    if (await checkValueExists(db_expensesProduct) > 0) {
      for (ExpensesProduct model in expItemList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_expensesProduct, cName: db_expProductID, cValue: model.expProductID ?? "") > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await expensesDBScript.upsetExpenseItem(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await expensesDBScript.upsetExpenseItem(model, "insert");
        }
      }
    } else {
      for (ExpensesProduct obj in expItemList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await expensesDBScript.upsetExpenseItem(obj, "insert");
      }
    }
  }

  ///sync Expense  from API
  syncExpenseFromApi(List<ExpensesModel> expensesList) async {
    ExpensesTransactionDBScript expensesDBScript = ExpensesTransactionDBScript();
    if (await checkValueExists(db_expenses) > 0) {
      for (ExpensesModel model in expensesList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_expenses, cName: db_expID, cValue: model.expID ?? "") > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await expensesDBScript.upsetExpense(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await expensesDBScript.upsetExpense(model, "insert");
        }
      }
    } else {
      for (ExpensesModel obj in expensesList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await expensesDBScript.upsetExpense(obj, "insert");
      }
    }
  }

  ///sync Expense Transaction from API
  syncExpenseTransactionFromApi(List<ExpenseTransactionModel> expensesTransactionList) async {
    ExpensesTransactionDBScript expensesTransactionDBScript = ExpensesTransactionDBScript();
    if (await checkValueExists(db_expenseTransactions) > 0) {
      for (ExpenseTransactionModel model in expensesTransactionList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_expenseTransactions, cName: db_expTransactionId, cValue: model.expTransactionID) > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await expensesTransactionDBScript.upsetExpensesTransaction(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await expensesTransactionDBScript.upsetExpensesTransaction(model, "insert");
        }
      }
    } else {
      for (ExpenseTransactionModel obj in expensesTransactionList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await expensesTransactionDBScript.upsetExpensesTransaction(obj, "insert");
      }
    }
  }

  ///sync Expense Transaction from API
  syncPurchaseFromApi(List<PurchaseModel> purchaseList) async {
    if (await checkValueExists(db_purchase) > 0) {
      for (PurchaseModel model in purchaseList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_purchase, cName: db_purchaseID, cValue: model.purchaseID) > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await purchasesDBScript.upsetPurchase(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await purchasesDBScript.upsetPurchase(model, "insert");
        }
      }
    } else {
      for (PurchaseModel obj in purchaseList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await purchasesDBScript.upsetPurchase(obj, "insert");
      }
    }
  }

  ///sync Expense Transaction from API
  syncPurchaseTransactionsFromApi(List<PurchaseTransactionsModel> purchaseTransactionsList) async {
    if (await checkValueExists(db_purchaseTransaction) > 0) {
      for (PurchaseTransactionsModel model in purchaseTransactionsList) {
        model.workspaceID = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_purchaseTransaction, cName: db_purchaseTransactionID, cValue: model.purchaseTransactionID) > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await purchasesDBScript.upsetPurchaseTransaction(model, "update");
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await purchasesDBScript.upsetPurchaseTransaction(model, "insert");
        }
      }
    } else {
      for (PurchaseTransactionsModel obj in purchaseTransactionsList) {
        obj.workspaceID = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await purchasesDBScript.upsetPurchaseTransaction(obj, "insert");
      }
    }
  }

  syncAdvanceOrderFromApi(List<AdvanceOrderNoteModel> advanceOrderList) async {
    AdvanceOrdersDBScripts advanceOrdersDBScripts = AdvanceOrdersDBScripts();
    if (await checkValueExists(db_order_notes) > 0) {
      for (AdvanceOrderNoteModel model in advanceOrderList) {
        model.workspaceId = MyApp.activeWorkspace.workspaceId;
        model.sync = 1;
        if (await checkValueExists(db_order_notes, cName: db_sales_salesId, cValue: model.salesId) > 0) {
          model.status = model.isActive == true ? 1 : 2;
          await advanceOrdersDBScripts.updateOrderNote(model);
        } else {
          model.status = model.isActive == true ? 0 : 2;
          await advanceOrdersDBScripts.insertOrderNote(model);
        }
      }
    } else {
      for (AdvanceOrderNoteModel obj in advanceOrderList) {
        obj.workspaceId = MyApp.activeWorkspace.workspaceId;
        obj.sync = 1;
        obj.status = obj.isActive == true ? 0 : 2;
        await advanceOrdersDBScripts.insertOrderNote(obj);
      }
    }
  }

  insertProducts(Products products) async {
    products.sync = 1;
    products.rowStatus = products.isActive ?? false ? 0 : 2;
    products.workspaceId = MyApp.activeWorkspace.workspaceId;
    products.productDate = DateTime.now().toString();
    final id1 = await productDetailsDBScript.insertProductDetails(products);
    return id1;
  }

  insertProductsKey(ProductKey model) async {
    model.keywordSync = 1;
    model.rowStatus = model.isActive ?? false ? 0 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await productKeyDBScript.insertKeyword(model);
  }

  insertProductsUnit(PriceUnitType model) async {
    model.measurementSync = 1;
    model.rowStatus = model.isActive ?? false ? 0 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await priceTypeDBScript.insertUnits(model);
  }

  insertToppingsGroup(ToppingsGroup model) async {
    model.toppingGroupSync = 1;
    model.rowStatus = model.isActive ?? false ? 0 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await toppingGroupsDBScript.insertToppingsGroupDetails(model);
  }

  insertToppings(Topping model) async {
    model.toppingSync = 1;
    model.rowStatus = model.isActive ?? false ? 0 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await toppingsDBscript.insertToppingsDetails(model);
  }

  insertTablesDetails(ShopTable model) async {
    model.shopTableSync = 1;
    model.rowStatus = model.isActive ?? false ? 0 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await shopTableDbScript.insertShopTable(model);
  }

  updateProductsDetails(Products product) async {
    product.sync = 1;
    product.rowStatus = product.isActive ?? false ? 1 : 2;
    final affectedRow = await productDetailsDBScript.updateProductsTable(product);
    return affectedRow;
  }

  updateProductsKeyDetails(ProductKey model) async {
    model.keywordSync = 1;
    model.rowStatus = model.isActive ?? false ? 1 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await productKeyDBScript.updateProductsKeyTable(model);
  }

  updateProductsUnitDetails(PriceUnitType model) async {
    model.measurementSync = 1;
    model.rowStatus = model.isActive ?? false ? 1 : 2;
    await priceTypeDBScript.updateUnitTable(model);
  }

  updateToppingGroupDetails(ToppingsGroup model) async {
    model.toppingGroupSync = 1;
    model.rowStatus = model.isActive ?? false ? 1 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await toppingGroupsDBScript.updateProductsToppingsGroup(model);
  }

  updateToppingDetails(Topping model) async {
    model.toppingSync = 1;
    model.rowStatus = model.isActive ?? false ? 1 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await toppingsDBscript.updateProductsToppings(model);
  }

  updateTablesDetails(ShopTable model) async {
    model.shopTableSync = 1;
    model.rowStatus = model.isActive ?? false ? 1 : 2;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    await shopTableDbScript.updateShopTable(model);
  }

  updateSettingDetails(WorkspaceSettings settingsDetail, {String tableType = "WorkSpaceSettings"}) async {
    settingsDetail.sync = 1;
    settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
    await workSpaceSettingsDB.updateSettings(settingsDetail, table: tableType);
  }

  insertCommitID(List<Commit> commitList) async {
    ManageSync model = new ManageSync();
    model.commitId = commitList.last.commitId;
    model.commitDate = commitList.last.commitDate;
    model.workspaceId = MyApp.activeWorkspace.workspaceId;
    model.userName = MyApp.activeUser.username;
    if (await checkValueExists('Sync') > 0) {
      await syncDBScript.updateSyncDetails(model);
    } else {
      await syncDBScript.insertSyncDetails(model);
    }
  }

  salesInsert(Sales sales) async {
    sales.sync = 1;
    sales.workspaceId = MyApp.activeWorkspace.workspaceId;
    sales.rowStatus = sales.isActive ?? false ? 0 : 2;
    await salesDetailsDBScript.insertSalesDetails(sales);
  }

  salesUpdate(Sales sales) async {
    // sales.rowStatus = sales.isActive ?? false ? 1 : 2;
    await salesDetailsDBScript.updateSalesTable(sales);
  }

  transactionInsert(Transactions transactions, {String? transactionType}) async {
    transactions.sync = 1;
    transactions.rowStatus = transactions.isActive ?? false ? 0 : 2;
    await salesTransDBScript.insertSalesTransactions(transactions, transactionType: transactionType);
  }

  transactionsUpdate(Transactions transactions, {String? transactionType}) async {
    transactions.sync = 1;
    transactions.rowStatus = transactions.isActive ?? false ? 1 : 2;
    await salesTransDBScript.updateTransactionsTable(transactions, transactionType: transactionType);
  }

  Future<List> getAllGeneralSettingsList() async {
    final allRows = await generalSettingDBScript.getAllGeneralSettingName();
    var generalSettingsList = [];
    for (var row in allRows) {
      generalSettingsList.add(row["settingKey"]);
    }
    return generalSettingsList;
  }

  // UPDATE EXPIERY DATE FOR WORKSPACE
  updateExpDateForWorkspace(String expDate) async {
    if (!expDate.isEmptyOrNull) {
      MyApp.activeWorkspace.expiryDate = expDate;
      SharedPreferences prefs = await SharedPreferences.getInstance();
      prefs.setString("activeWorkspace", jsonEncode(MyApp.activeWorkspace));
      await workspaceDBScript.updateExpiryDate(expDate, MyApp.activeWorkspace.workspaceId);
    }
  }

  /// FOR IMAGE SYNC ///

  Future<List> queryAllProductImagesID() async {
    final allRows = await productDetailsDBScript.queryAllGetProductsImageId();
    var productImageIdList = [];
    allRows.forEach((row) => productImageIdList.add(row["productImage"]));
    return productImageIdList;
  }

  Future<List> generateRequestForImageSync() async {
    var imageIdList = await queryAllProductImagesID();
    List<File> fileList = [];
    for (int i = 0; i < imageIdList.length; i++) {
      File? file = await fnUtilities.readFileFromLocal(imageIdList[i]);
      if (file != null) {
        fileList.add(file);
      }
    }
    return fileList;
  }

  Future<List> generateRequestForImageSyncShop() async {
    final allRows = await shopDetailsDBScript.queryAllNonSyncShopDetails();
    List<File> fileList = [];
    if (allRows != null) {
      File? file = await fnUtilities.readFileFromLocal(ShopDeatails.fromMap(allRows).imageID);
      if (file != null) {
        fileList.add(file);
      }
    }
    return fileList;
  }

  startUploadImageSync(String category) async {
    var pathList = category == "products" ? await generateRequestForImageSync() : await generateRequestForImageSyncShop();
    if (pathList.length > 0) {
      await uploadMultipleImages(pathList, category).then((value) {});
    } else {}
  }

  startDownloadImageSync(category) async {
    var productIdList = await queryAllProductImagesID();
    if (productIdList.length > 0) {
      for (String id in productIdList)
        if (!id.isEmptyOrNull) {
          await downloadImage(id, category).then((value) {});
        }
    } else {
      showToast("No images for sync");
    }
  }

// SHOW SNACKBAR
  showSnackBar(String text) {
    final SnackBar snackBar = SnackBar(
      backgroundColor: Colors.grey[600],
      behavior: SnackBarBehavior.floating,
      content: Container(
          child: Row(
        children: [
          Container(width: 20, height: 20, child: CircularProgressIndicator()),
          SizedBox(
            width: 10,
          ),
          // Text('${apiCallPercentage}$text'),
          ValueListenableBuilder<int>(
            builder: (BuildContext context, int value, Widget? child) {
              // This builder will only get called when the _counter
              // is updated.
              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: <Widget>[
                  Text('$value% Completed,  $text'),
                ],
              );
            },
            valueListenable: apiCallPercentage,
            // The child parameter is most helpful if the child is
            // expensive to build and does not depend on the value from
            // the notifier.
            // child: child,
          )
        ],
      )),
      duration: Duration(minutes: 5),
    );
    var snackMessangerController = snackbarKey.currentState?.showSnackBar(snackBar);
    snackMessangerController?.closed.whenComplete(() {
      apiCallPercentage.value = 0;
    });
  }

// HIDE SNACKBAR
  hideSnackBar() {
    snackbarKey.currentState?.hideCurrentSnackBar();
    // snackbarKey.c
    // _controller.closed.whenComplete(() {
    //   snackMsg.value = _initText;
    //   sc.count = 0;
    // });
  }

// GET PRFIX NUMBER BASED ON WORKSPACE FROM API
  getPrefixForWorkspace() async {
    var workspaceId = MyApp.activeWorkspace.workspaceId;
    await getPrefixById(workspaceId).then((value) {
      if (value.status == 1) {
        Prefix prefix = new Prefix();
        prefix.prefix = value.prefix;
        prefix.workspaceId = workspaceId;
        prefix.userName = MyApp.activeUser.username;
        dbOperation.insertInvoicePrefix(prefix);
      } else {
        showToast("PrefixID not found");
      }
    });
  }

  Future<Sales> checkSyncPriority(Sales sale) async {
    var sales = await commonDB.getSingleDataBasedOneColumn('SalesDetails', 'salesId', sale.salesId ?? '');
    var saleStatus = (sales![db_sales_status] == 1 && sale.salesStatus == 2) || (sales[db_sales_status] == 1 && sale.salesStatus == 3);
    var isActiveSale = sales[db_sales_isActiveSale] == null && sale.isActiveSale != null;
    var isRowStatus = sales[db_product_rowStatus] == 2 && sale.isActive == true;
    sale.sync = saleStatus || isActiveSale || isRowStatus ? 0 : 1;
    sale.isActiveSale = isActiveSale ? sales[db_sales_isActiveSale] : sale.isActiveSale;
    sale.salesStatus = saleStatus ? sales[db_sales_status] : sale.salesStatus;
    sale.rowStatus = isRowStatus
        ? sales[db_product_rowStatus]
        : sale.isActive ?? false
            ? 1
            : 2;
    return sale;
  }
}
