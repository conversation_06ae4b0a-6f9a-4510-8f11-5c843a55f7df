import 'package:collection/collection.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/model/sales_payment_mapping.dart';
import 'package:smartbill/model/sales_tax_calculate.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:tuple/tuple.dart';
import 'package:uuid/uuid.dart';

import '../../main.dart';
import '../../model/payment_type.dart';
import '../../model/products.dart';
import '../../model/reports.dart';
import '../constants/strings_keys.dart';
import '../dbutils/database_helper.dart';
import '../dbutils/db_operations_utility.dart';
import '../dbutils/sales_details_db_script.dart';
import '../dbutils/sales_payment_mapping_db_script.dart';
import '../dbutils/sales_trans_db_script.dart';
import '../dbutils/sales_trans_staging.dart';
import 'list_class.dart';
import 'sync_utility.dart';

class SalesFunctions {
  List<SalesPaymentMapping> paymentMappingList = [];
  final dbHelper = DatabaseHelper.instance;
  SyncUtility syncUtility = new SyncUtility();
  SalesPaymentMappingDB paymentMappingDb = SalesPaymentMappingDB();

  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  DBOperations dbOperations = new DBOperations();
  FnUtilities fnUtilities = new FnUtilities();

  List<Sales> activeTablesList = [];
  List<Transactions> saleProductList = [];
  List<Transactions> salesTransactionList = [];
  List<Sales> notCompletedList = [];

  //CALCULATE TAX FUNCTION
  Future<Tuple2<int, List<Transactions>>> startCalculateTax(
      Sales sales, List<Transactions> transactionList, PaymentType selectPaymentType, String saleDiscountPrice, String saleType) async {
    SalesCalculateRequest calculateRequest = new SalesCalculateRequest();
    int taxMode = 1;
    if (await fnUtilities.workSpaceSetValues(key_taxEnable) == "1") {
      calculateRequest.withTax = true;
      if (await fnUtilities.workSpaceSetValues(key_taxMode) == "1") {
        taxMode = 2;
        calculateRequest.includedTax = true;
      } else {
        taxMode = 3;
        calculateRequest.includedTax = false;
      }
    } else {
      taxMode = 1;
      calculateRequest.withTax = false;
    }
    var overallTax = await fnUtilities.workSpaceSetValues(key_overAllTax) ?? "0";
    if (sales.salesId != null) {
      //For Update we need to find old transaction tax mode so we
      final reportDetail = ListUtility.reportList.firstWhereOrNull((element) => element.salesId == sales.salesId);
      if (reportDetail != null) {
        if (reportDetail.taxMode == 1) {
          calculateRequest.withTax = false;
          taxMode = 1;
        } else if (reportDetail.taxMode == 2) {
          taxMode = 2;
          calculateRequest.withTax = true;
          calculateRequest.includedTax = true;
        } else if (reportDetail.taxMode == 3) {
          taxMode = 3;
          calculateRequest.withTax = true;
          calculateRequest.includedTax = false;
        }
      }
    }

    /// Parcel Amount Settings
    bool isEnableParcel = false;
    double parcelPerProduct = 0.0;
    bool applyParcelForAll = false;
    if (await fnUtilities.workSpaceSetValues(key_parcelEnable) == "1" && (saleType != "SO" && saleType != "T")) {
      isEnableParcel = true;
      if (await fnUtilities.workSpaceSetValues(key_applyParcelFor) == "All") {
        applyParcelForAll = true;
        var overAllParcelCharge = await fnUtilities.workSpaceSetValues(key_overAllParcelCharge);
        parcelPerProduct = double.tryParse(overAllParcelCharge) ?? 0;
      }
    }
    double totAmount = 0.0;
    transactionList.forEach((element) {
      totAmount += element.actualPrice != null ? double.tryParse(element.actualPrice ?? "0")! * double.tryParse(element.qty ?? "0")! : 0.0;
    });

    for (Transactions transactions in transactionList) {
      Products products = await getProductInfoById(transactions.prodId) ?? Products();
      calculateRequest.qty = transactions.qty.toDouble();
      calculateRequest.rate = double.parse(transactions.actualPrice ?? "0");

      double pricePerRupee = double.parse(saleDiscountPrice) / totAmount;
      double discountPerProduct = double.parse(transactions.actualPrice ?? "0") * pricePerRupee;
      calculateRequest.discountAmount = discountPerProduct;
      if (transactions.discountPrice != null) {
        calculateRequest.discountAmount = double.parse(transactions.discountPrice ?? "0");
      }

      if (await fnUtilities.workSpaceSetValues(key_applyTaxFor) == "All") {
        calculateRequest.cgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
        calculateRequest.sgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
      } else {
        calculateRequest.cgst = products.cGst == "" || products.cGst == null ? 0 : double.parse(products.cGst ?? "0");
        calculateRequest.sgst = products.sGst == "" || products.sGst == null ? 0 : double.parse(products.sGst ?? "0");
      }

      /// Get Parcel Amount per product
      if (isEnableParcel) {
        if (!applyParcelForAll) {
          parcelPerProduct = (products.parcelAmount.isEmptyOrNull ? 0 : double.tryParse(products.parcelAmount ?? "0"))!;
        }
      }
      calculateRequest.paymentCategoryId = selectPaymentType.paymentCategoryID ?? 0;
      calculateRequest.parcelPerProduct = parcelPerProduct;
      SalesCalculateResponse calculateResponse = calculateTax(calculateRequest);
      transactions.price = calculateResponse.rate.toString();
      transactions.discountPrice = calculateResponse.discountAmount.toString();
      transactions.cGst = calculateResponse.cgst.toString();
      transactions.cGstAmount = calculateResponse.cgstAmt.toString();
      transactions.sGst = calculateResponse.sgst.toString();
      transactions.sGstAmount = calculateResponse.sgstAmt.toString();
      transactions.amountWithoutGst = calculateResponse.totalAmtWithoutGst.toString();
      transactions.amount = calculateResponse.totalAmt.toString();
      transactions.parcelAmountWithoutGst = calculateResponse.parcelAmountWithoutGst.toString();
      transactions.parcelAmount = calculateResponse.parcelAmount.toString();
    }
    return Tuple2(taxMode, transactionList);
  }

  //SALES CALCULATION RESPONSE
  SalesCalculateResponse calculateTax(SalesCalculateRequest model) {
    var saleProduct = SalesCalculateResponse();
    double qty = model.qty;
    double discountAmount = model.discountAmount;
    double productPrice = model.rate;
    double amountBeforeTax = 0;
    double totalTax = 0;
    double taxAmount = 0;
    double parcelAmountBeforeTax = 0.0;
    double parcelTaxAmount = 0.0;
    if (model.withTax) {
      saleProduct.cgst = model.cgst;
      saleProduct.sgst = model.sgst;
      totalTax = model.cgst + model.sgst;
      if (model.includedTax) {
        amountBeforeTax = qty * (productPrice - discountAmount) / (1 + (totalTax / 100));

        taxAmount = amountBeforeTax * (totalTax / 100);
        saleProduct.cgstAmt = taxAmount / 2;
        saleProduct.sgstAmt = taxAmount / 2;
        saleProduct.rate = (amountBeforeTax / qty) + discountAmount;
        saleProduct.totalAmt = amountBeforeTax + taxAmount;

        /// Tax calculation (include tax) for parcel charge
        parcelAmountBeforeTax = (model.parcelPerProduct * qty) / (1 + (totalTax / 100));
        parcelTaxAmount = parcelAmountBeforeTax * (totalTax / 100);
        saleProduct.cgstAmt = saleProduct.cgstAmt + (parcelTaxAmount / 2);
        saleProduct.sgstAmt = saleProduct.sgstAmt + (parcelTaxAmount / 2);
        saleProduct.rate = saleProduct.rate + (parcelAmountBeforeTax / qty);
        saleProduct.totalAmt = saleProduct.totalAmt + (parcelAmountBeforeTax + parcelTaxAmount);
      } else {
        amountBeforeTax = qty * (productPrice - discountAmount);
        taxAmount = qty * ((productPrice - discountAmount) * (totalTax / 100));
        saleProduct.cgstAmt = taxAmount / 2;
        saleProduct.sgstAmt = taxAmount / 2;
        saleProduct.rate = (amountBeforeTax / qty) + discountAmount;
        saleProduct.totalAmt = amountBeforeTax + taxAmount;

        /// Tax calculation (exclude tax)for parcel charge
        parcelAmountBeforeTax = model.parcelPerProduct * qty;
        parcelTaxAmount = (model.parcelPerProduct * qty) * (totalTax / 100);
        saleProduct.cgstAmt = saleProduct.cgstAmt + (parcelTaxAmount / 2);
        saleProduct.sgstAmt = saleProduct.sgstAmt + (parcelTaxAmount / 2);
        saleProduct.rate = saleProduct.rate + model.parcelPerProduct;
        saleProduct.totalAmt = saleProduct.totalAmt + (parcelAmountBeforeTax + parcelTaxAmount);
      }
    } else {
      amountBeforeTax = qty * (productPrice - discountAmount);
      parcelAmountBeforeTax = model.parcelPerProduct * model.qty;
      parcelTaxAmount = parcelAmountBeforeTax;
      saleProduct.cgstAmt = 0;
      saleProduct.sgstAmt = 0;
      saleProduct.rate = productPrice + model.parcelPerProduct;
      saleProduct.totalAmt = amountBeforeTax + parcelAmountBeforeTax;
    }
    saleProduct.qty = qty;
    saleProduct.discountAmount = discountAmount * qty;
    saleProduct.totalAmtWithoutGst = (amountBeforeTax + (discountAmount * qty)) + parcelAmountBeforeTax;
    saleProduct.parcelAmountWithoutGst = parcelAmountBeforeTax;
    saleProduct.parcelAmount = parcelTaxAmount;
    return saleProduct;
  }

  //GET PRODUCT DETAILS BY ID FROM Product List
  Products? getProductInfoById(String? prodId) {
    for (Products p in ListUtility.productList) {
      if (p.productId == prodId) {
        return p;
      }
    }
    return null;
  }

  ///Sales Payment Mapping
  Future<List<SalesPaymentMapping>> salesPaymentMapping(
      PaymentType? paymentType, List<SalesPaymentMapping> paymentMapList, int status, int index, String paymentAmount) async {
    bool isPaymentTypeNotExist;
    SalesPaymentMapping paymentMapping = SalesPaymentMapping();
    paymentMappingList = paymentMapList;
    if (paymentType != null) {
      paymentMapping.paymentName = paymentType.paymentName;
      paymentMapping.paymentId = paymentType.paymentTypeID;
      paymentMapping.amount = double.tryParse(paymentAmount) ?? 0;
    }

    if (status == 0) {
      paymentMappingList.add(paymentMapping);
    } else {
      if (status == 1) {
        if (index != -1) {
          paymentMapping.salesId = paymentMappingList[index].salesId != null ? paymentMappingList[index].salesId : null;
          paymentMapping.paymentMappingId = paymentMapList[index].paymentMappingId != null ? paymentMapList[index].paymentMappingId : null;
        }
        if (paymentMappingList[index].paymentName == paymentType?.paymentName && paymentMappingList[index].amount != paymentMapping.amount) {
          paymentMappingList[index] = paymentMapping;
        } else {
          isPaymentTypeNotExist = checkPaymentTypeExist(paymentType);
          if (isPaymentTypeNotExist) {
            paymentMappingList[index] = paymentMapping;
          }
        }
      } else {
        if (paymentMappingList[index].status == 0 || paymentMappingList[index].status == 1) {
          paymentMappingList[index].status = 2;
        } else {
          paymentMappingList.removeAt(index);
        }
      }
    }
    return paymentMappingList;
  }

  ///check if the payment already exist or not
  checkPaymentTypeExist(PaymentType? paymentType) {
    if (paymentType != null) {
      for (int i = 0; i < paymentMappingList.length; i++) {
        if (paymentMappingList[i].paymentId == paymentType.paymentTypeID) {
          toast("Payment type already exist");
          return false;
        }
      }
      return true;
    } else {
      return false;
    }
  }

  ///Sales Payment to assign in a sales payment mapping
  salesPaymentMappingMethod(List<SalesPaymentMapping> paymentMappingList,
      {String? saleId, int? status, bool enableDuplicationPayment = false}) async {
    String salesId = saleId ?? "";
    SalesPaymentMapping paymentMapping = SalesPaymentMapping();
    var uuid = new Uuid();
    if (paymentMappingList.isNotEmpty) {
      for (SalesPaymentMapping paymentMapModel in paymentMappingList) {
        paymentMapping.paymentId = paymentMapModel.paymentId;
        paymentMapping.amount = paymentMapModel.amount;
        paymentMapping.salesId = salesId;
        paymentMapping.paymentSync = 0;
        paymentMapping.workspaceId = MyApp.activeWorkspace.workspaceId;

        if (await syncUtility.checkValueExists("SalesPaymentMappingDetails", cName: 'paymentMappingId', cValue: paymentMapModel.paymentMappingId) >
                0 &&
            !enableDuplicationPayment) {
          paymentMapping.paymentMappingId = paymentMapModel.paymentMappingId;
          if (paymentMapModel.status == 2) {
            paymentMapping.status = paymentMapModel.status;
          } else {
            paymentMapping.status = 1;
            paymentMapping.createdDate = paymentMapModel.createdDate;
          }
          await paymentMappingDb.updateSalesPaymentMapping(paymentMapping);
        } else {
          paymentMapping.paymentMappingId = uuid.v4().toString();

          paymentMapping.status = 0;
          paymentMapping.createdDate = DateTime.now().toString();

          await paymentMappingDb.SalesPaymentMapInsert(paymentMapping);
        }
      }
    }
  }

  getAllActiveSales(String flag) async {
    final allRows = await salesDetailsDBScript.query01(flag);
    activeTablesList.clear();
    allRows.forEach((row) => activeTablesList.add(Sales.fromMap(row)));
    for (Sales item in activeTablesList) {
      if (item.discountPrice != null) {
        item.totalAmount = (double.parse(item.totalAmount ?? "0") - double.parse(item.discountPrice ?? "0")).toString();
      }
    }
    return activeTablesList;
  }

  ///get not completed sales from db based on status 2
  getAllNotCompletedSale() async {
    final allRows = await salesDetailsDBScript.getNotCompletedSale();
    notCompletedList.clear();
    allRows.forEach((row) => notCompletedList.add(Sales.fromMap(row)));
    for (Sales item in notCompletedList) {
      if (item.discountPrice != null) {
        item.totalAmount = (double.parse(item.totalAmount ?? "0") - double.parse(item.discountPrice ?? "0")).toString();
      }
    }
    return notCompletedList;
  }

  getAdvanceOrderSalesProducts() async {
    final allRows = await salestransStagingDBScript.queryAllSalesFromTransaction();
    saleProductList.clear();
    allRows.forEach((row) => saleProductList.add(Transactions.fromMap(row)));
    return saleProductList;
  }

  getAllAddvanceOrder() async {
    List<Sales> advanceOrderSalesList = [];
    final allRows = await salesDetailsDBScript.getAdvanceOrderSale();
    advanceOrderSalesList.clear();
    allRows.forEach((row) => advanceOrderSalesList.add(Sales.fromMap(row)));
    for (Sales item in advanceOrderSalesList) {
      // if (item.discountPrice != null) {
      // item.totalAmount = (double.tryParse(item.totalAmount ?? "0")!).toString();

      // }
    }
    return advanceOrderSalesList;
  }

  ///Get sales transaction details from sales transaction table
  getSalesTransactionDetails(String saleId) async {
    List<Transactions> tempList = [];
    final allRows = await salesDetailsDBScript.getSalesFromSalesTransaction(saleId);
    allRows.forEach((row) => tempList.add(Transactions.fromMap(row)));
    print(tempList.length);
    return tempList;
  }

  getSalesProducts() async {
    final allRows = await salestransStagingDBScript.queryAllSalesTransaction();
    saleProductList.clear();
    allRows.forEach((row) => saleProductList.add(Transactions.fromMap(row)));
    return saleProductList;
  }

  getAllReportsWithProductsByDateToDate(String saleID) async {
    List<Transactions> isActiveSaleList = [];
    final allRows = await salestransStagingDBScript.queryActiveTransactionList(saleID);
    isActiveSaleList.clear();
    allRows.forEach((row) => isActiveSaleList.add(Transactions.fromMap(row)));
    return isActiveSaleList;
  }

  getAllAdvanceOrderTransaction(String saleID) async {
    List<Transactions> isActiveSaleList = [];
    final allRows = await salestransStagingDBScript.advanceOrderTransactionList(saleID);
    isActiveSaleList.clear();
    allRows.forEach((row) => isActiveSaleList.add(Transactions.fromMap(row)));
    return isActiveSaleList;
  }

  getAllTransaction(id) async {
    var allRows;
    allRows = await salestransStagingDBScript.getAllStagingSalesById(id);
    salesTransactionList.clear();
    allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
    return salesTransactionList;
  }

  activeTableUpdateTransaction(Sales sales) async {
    SalesTransDBScript salesTransDBScript = SalesTransDBScript();
    final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(sales.salesId, tableName: "staging", isActiveSaleNo: sales.isActiveSale);

    salesTransactionList.clear();
    allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
    return salesTransactionList;
  }

  getSalesDetails(id, flag, {String? salesFlag}) async {
    var allRows;
    if (flag != "Sale") {
      if (flag != "Sale" && flag != "KOT") {
        allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id, tableName: "staging", isActiveSaleNo: int.parse(flag));
      } else if (flag == "KOT") {
        allRows = await salestransStagingDBScript.getAllStagingSalesById(id);
      }
      salesTransactionList.clear();
      allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
    } else if (salesFlag == "NotCompleted") {
      allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id, tableName: "");
      salesTransactionList.clear();
      allRows.forEach((row) => salesTransactionList.add(Transactions.fromMap(row)));
    }
    return salesTransactionList;
  }

  //GET A INVOICE ID FUNCTION
  getMaxInvoiceId() async {
    String prefix = await dbOperations.queryPrefixName();
    bool isResetSalesNo = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
    String prefixMax = await salesDetailsDBScript.queryGetMaxInvoiceId(prefix, isResetSalesNo);
    if (prefixMax == "0") {
      return prefix + 1.toString();
    } else {
      return prefix + (int.parse(prefixMax.substring(prefix.length)) + 1).toString();
    }
  }

  Map<String, dynamic> assignSalesPaymentMappingList(List<SalesPaymentMapping> paymentMapList) {
    List<SalesPaymentMapping> paymentMappingList = [];

    if (paymentMapList.isNotEmpty) {
      for (var e in paymentMapList) {
        e.paymentCatId = ListUtility.paymentTypeList
            .firstWhereOrNull((element) => element.paymentTypeID == e.paymentId && element.rowStatus != 2)
            ?.paymentCategoryID;
      }
      if (paymentMapList.length == 1 && paymentMapList[0].paymentCatId == 4) {
        paymentMappingList = List.from(paymentMapList);
        return {'paymentMappingList': paymentMappingList, 'isComplementory': true};
      } else {
        paymentMappingList = List.from(paymentMapList);
        return {'paymentMappingList': paymentMappingList, 'isComplementory': false};
      }
    }

    return {'paymentMappingList': [], 'isComplementory': false};
  }

  int setPaymentCategoryId(List<SalesPaymentMapping> paymentMapList, bool splitInvoiceByPayment) {
    List<SalesPaymentMapping> temPaymentMapList = [];
    temPaymentMapList = paymentMapList.where((element) => element.status != 2).toList();
    for (var e in temPaymentMapList) {
      e.paymentCatId = ListUtility.paymentTypeList
          .firstWhereOrNull((element) => element.paymentTypeID == e.paymentId && element.rowStatus != 2)
          ?.paymentCategoryID;
    }
    if (temPaymentMapList.isNotEmpty) {
      SalesPaymentMapping? paymentMapModel;
      paymentMapModel = temPaymentMapList.firstWhereOrNull((element) => element.status == null || element.status! < 2);
      if (paymentMapModel?.paymentCatId == 4) {
        return 4;
      } else if (splitInvoiceByPayment && temPaymentMapList.length > 1) {
        int result = getPaymentCategoryID(temPaymentMapList);
        return result;
      } else if (!splitInvoiceByPayment) {
        return 3;
      } else {
        return paymentMapModel?.paymentCatId ?? -1;
      }
    } else {
      return 3;
    }
  }

  int getPaymentCategoryID(List<SalesPaymentMapping> paymentList) {
    bool hasPaymentCategory1 = paymentList.any((item) => item.paymentCatId == 1);
    bool hasPaymentCategory2 = paymentList.any((item) => item.paymentCatId == 2);
    if (hasPaymentCategory1 && hasPaymentCategory2) {
      return 3;
    } else if (hasPaymentCategory2) {
      return 2;
    } else if (hasPaymentCategory1) {
      return 1;
    }

    return 3; // Return 0 if none of the conditions are met
  }
}
