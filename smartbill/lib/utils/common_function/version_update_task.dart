import 'dart:convert';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import '../../main.dart';
import '../constants/strings_keys.dart';
import '../dbutils/price_type_db_script.dart';
import '../dbutils/product_details_db_script.dart';
import '../dbutils/sync_db_script.dart';
import 'fn_utilities.dart';
import 'list_class.dart';

class VersionUpdateTask {
  final dbHelper = DatabaseHelper.instance;
  FnUtilities fnUtilities = new FnUtilities();

  List<PriceUnitType> productUnitList = [];
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  SyncDBScript syncDBScript = SyncDBScript();
  PriceTypeDBScript priceTypeDBScript = PriceTypeDBScript();

  versionBasedUpdateFunction() async {
    String? current = await fnUtilities.workSpaceUserSetValue(key_versionCode);
    if (current != null) {
      if (int.parse(current) < MyApp.versionCode) {
        int currentVersion = int.parse(current);
        await commonVersionFunction(currentVersion, false);
      }
    } else {
      await commonVersionFunction(null, true);
    }
  }

  commonVersionFunction(int? version, bool isEmpty) async {
    //if check for the version 60
    // if(version <= 60 || isEmpty){
    if (isEmpty) {
      version = 60;
      await versionMigration(version);
    } else if (version == 68) {
      await versionMigration(version);
    } else if (version == 83) {
      await versionMigration(version);
    }

    WorkspaceSettings settings = new WorkspaceSettings();
    settings.settingKey = "VersionCode";
    settings.settingValue = MyApp.versionCode.toString();
    settings.sync = 0;
    await fnUtilities.updateSettingDetails(settings,
        tableType: "WorkSpaceUserSettings");
  }

  versionMigration(int? versionCode) async {
    if(versionCode != null){
      switch (versionCode) {
      case 60:
        {
          await _queryAllProductDetails();
          for (Products p in ListUtility.productList) {
            if (p.priceType == null) {
              await queryAllProductUnitsDetails(p.productId ?? "");
              if (productUnitList.isNotEmpty && p.price == null) {
                p.priceType = 2;
              } else if (p.price == null && productUnitList.isEmpty) {
                p.priceType = 0;
              } else {
                p.priceType = 1;
              }
              final syncNo = await productDetailsDBScript
                  .queryGetProductSyncNo(p.productId);
              p.rowStatus = syncNo == 0 ? 0 : 1;
              p.sync = 0;
              await productDetailsDBScript.updateProductsTable(p);
            }
          }
          break;
        }
      case 68:
        {
          var lastCommitId = await syncDBScript.getGetLastSyncId();
          if (lastCommitId != null && lastCommitId > 0) {
            syncDBScript.deleteAllSyncExceptLast();
          }
          break;
        }
      case 83:
        {
          List<String> invoicePaymentType = ['All'];
          WorkspaceSettings settings = new WorkspaceSettings();
          settings.settingKey = "InvoicePaymentType";
          settings.settingValue = jsonEncode(invoicePaymentType);
          settings.sync = 0;
          settings.workspaceId = MyApp.activeWorkspace.workspaceId;
          fnUtilities.updateSettingDetails(settings);
          break;
        }
    }
    }
  }

  //COMMON FUNCTIONS

  //GET ALL PRODUCT DETAILS
  _queryAllProductDetails() async {
    final allRows = await productDetailsDBScript.queryallRowsProductDeatails();
    ListUtility.productList.clear();
    allRows
        .forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
  }

  queryAllProductUnitsDetails(String productId) async {
    final allRows = await priceTypeDBScript.queryGetAllForUnit(productId);
    productUnitList.clear();
    allRows.forEach((row) => productUnitList.add(PriceUnitType.fromMap(row)));
  }
}
