import 'package:smartbill/utils/common_function/sync_utility.dart';

import '../../main.dart';
import '../../model/hive_model/master_table_model.dart';
import '../dbutils/db_strings.dart';
import '../dbutils/master_db_script.dart';

class MasterTableFunction {
  Future<int> upsertMasterTable(String key, String value) async {
    var result;
    MasterTableDBScript masterTableDBScript = MasterTableDBScript();
    SyncUtility syncUtility = SyncUtility();
    MasterTableModel masterTable = MasterTableModel(
      workspaceID: MyApp.activeWorkspace.workspaceId,
      key: key,
      value: value,
      masterTableSync: 0,
    );

    if (await syncUtility.checkValueExists(db_master_table, cName: db_key, cValue: key) > 0) {
      result = await masterTableDBScript.updateMasterTable(masterTable);
    } else {
      result = await masterTableDBScript.insertMasterTable(masterTable);
    }
    return result;
  }
}
