import 'package:background_fetch/background_fetch.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import '../../model/sync_response_model.dart';
import '../error_logs/error_logs_function.dart';
import 'fn_utilities.dart';
import 'value_notifier.dart';

bool isSyncProgress = false;
bool isPullSyncProgress = false;
bool isSyncUpToDate = false;
int salesPageCount = 0;
List<Commit> commitIdList = [];

class BackGroundSync {
  static int status = 0;
  static FnUtilities fnUtilities = new FnUtilities();

  static Future<void> initTasks() async {
    try {
      var status = await BackgroundFetch.configure(
          BackgroundFetchConfig(
            minimumFetchInterval: 100,
          ),
          startBackTasks,
          backTaskTimeout);
      status = status;

      BackgroundFetch.scheduleTask(TaskConfig(
          taskId: "com.cloudstier.manualsync",
          delay: 20000,
          periodic: false,
          startOnBoot: false,
          stopOnTerminate: true,
          enableHeadless: false,
          requiredNetworkType: NetworkType.ANY));

      BackgroundFetch.scheduleTask(TaskConfig(
          taskId: "com.cloudstier.autosync",
          delay: 15000,
          periodic: false,
          startOnBoot: false,
          stopOnTerminate: true,
          enableHeadless: false,
          requiredNetworkType: NetworkType.ANY));
    } on Exception catch (e) {
      //print("[BackgroundFetch] configure ERROR: $e");
    }
    //if (!mounted) return;
  }

  static startBackTasks(String taskId, {bool isSigning = false, bool isPullSync = false}) async {
    try {
      if (MyApp.activeWorkspace.workspaceId != null) {
        if (await InternetConnectionChecker().hasConnection) {
          SyncUtility syncUtility = new SyncUtility();
          var autoSync = fnUtilities.workSpaceUserSetValue(key_autoSync);
          bool isAutoSync = autoSync == null || autoSync == "1" ? true : false;
          if ((taskId == 'com.cloudstier.autosync') && (!isSyncProgress) && (isAutoSync)) {
            //showToast("Auto sync statred");
            isSyncProgress = true;
            isSyncUpToDate = false;

            /// PULL CHANGES STARTED///
            await startPullSync(syncUtility, "autosync", isPullSync);

            /// PULL CHANGES COMPLTED ///

            ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(2, 0);
            await syncUtility.startSync(syncType: 'autosync');
            if (!isSyncUpToDate) {
              /// get sales from API
              await syncUtility.startSync(syncType: 'autosync', isSalesOnly: true);
            }

            salesPageCount = 0;
            isSyncProgress = false;
            // bottomBarKey?.currentState?.checkSync();
            syncUtility.checkSyncStatus();
            startErrorSync();
            return;
          } else if (taskId == 'com.cloudstier.manualsync' && !isSyncProgress) {
            isSyncProgress = true;
            isSyncUpToDate = false;
            if (!isSigning) {
              /// PULL CHANGES STARTED///
              await startPullSync(syncUtility, "", isPullSync);

              /// PULL CHANGES COMPLTED ///
            }
            ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(2, 0);
            await syncUtility.startSync(isSigning: isSigning);
            await fnUtilities.getMasterInfoBasedOnWorkspace();
            if (!isSyncUpToDate) {
              /// get sales from API
              await syncUtility.startSync(isSigning: isSigning, isSalesOnly: true);
            }

            salesPageCount = 0;
            isSyncProgress = false;
            if (isSigning) {
              bottomBarKey.currentState?.updateBottmBars();
            }
            // syncUtility.hideSnackBar();
            // bottomBarKey?.currentState?.checkSync();
            syncUtility.checkSyncStatus();
            startErrorSync();
            return;
          } else if (taskId == 'com.cloudstier.manualsync' && isSyncProgress) {
            // showToast("Sync in progress");
          }
          await Future.delayed(Duration(milliseconds: 500));
        }
      }

      if (taskId == "flutter_background_fetch") {
        BackgroundFetch.scheduleTask(TaskConfig(
            taskId: "com.cloudstier.manualsync",
            delay: 20000,
            periodic: false,
            startOnBoot: false,
            stopOnTerminate: true,
            enableHeadless: false,
            requiredNetworkType: NetworkType.ANY));
        BackgroundFetch.scheduleTask(TaskConfig(
            taskId: "com.cloudstier.autosync",
            delay: 15000,
            periodic: false,
            startOnBoot: false,
            stopOnTerminate: true,
            enableHeadless: false,
            requiredNetworkType: NetworkType.ANY));
        return;
      }
      BackgroundFetch.finish(taskId);
    } catch (ex) {
      isSyncProgress = false;
      ErrorLogsFunction.insertErrorLogs(ex, "Sync Function");
      startErrorSync();
      throw ex;
    }
  }

  static startPullSync(SyncUtility syncUtility, String syncType, bool isPullSync) async {
    if (isPullSync) {
      isPullSyncProgress = true;
      ValueNotifiers.instance.syncStatusNotifier.value = SyncStatusInfo(2, 0);
      await syncUtility.startSync(syncType: syncType, isPullSync: true);
      if (!isSyncUpToDate) {
        await syncUtility.startSync(syncType: syncType, isSalesOnly: true, isPullSync: true);
      }
      salesPageCount = 0;
      isPullSyncProgress = false;
      isSyncUpToDate = false;
    }
  }

  static void backTaskTimeout(String taskId) {
    BackgroundFetch.finish(taskId);
  }

  static void enableorDisableBackTask(enabled) {
    if (enabled) {
      BackgroundFetch.start().then((status) {}).catchError((e) {});
    } else {
      BackgroundFetch.stop().then((status) {});
    }
  }

  static startErrorSync() async {
    await ErrorLogsFunction.generateErrorFileAndUploadAPI();
  }
}
