// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:pub_semver/pub_semver.dart';

import '../../main.dart';
import '../../model/app_info/app_info_response.dart';
import '../common_widgets/update_dialog_widget.dart';

/// check if the availabe version is critical version or not.
bool checkIsForceUpdate(String currentVersion, String storeVersion) {
  try {
    if (storeVersion.isNotEmpty &&
        Version.parse(currentVersion) < Version.parse(storeVersion)) {
      return true;
    }
    return false;
  } catch (e) {
    print('upgrader: updateVersionInfo could not parse version info $e');
    return false;
  }
}

/// check app update is availabe
/// 
Future<bool> checkAppUpdate(
    BuildContext context, AppInfoResponse? appInfoResponse) async {
  String installedAndroidVersion = MyApp.androidVersionCode;
  String installedWindowsVersion = MyApp.windowsVersionCode;
  bool isForceUpdate = false;

  if (appInfoResponse != null) {
    if (Platform.isAndroid) {
      if (Version.parse(installedAndroidVersion) <
          Version.parse(appInfoResponse.androidAppVersion ?? "")) {
        isForceUpdate = checkIsForceUpdate(installedAndroidVersion,
            appInfoResponse.androidAppCriticalAppVersion ?? "");
      } else {
        return true;
      }
    } 
    else {
      if (Version.parse(installedWindowsVersion) <
          Version.parse(appInfoResponse.iosAppVersion ?? "")) {
        isForceUpdate = checkIsForceUpdate(
            installedWindowsVersion, appInfoResponse.iosAppCriticalAppVersion ?? "");
      } else {
        return true;
      }
    }
  }

  await Future.delayed(Duration(seconds: 1));
  bool result = await versionUpdateAlert(
      context,
      isForceUpdate,
      isForceUpdate
          ? "There is a newer version of the app available, Please update it now."
          : "Please update to continue using the app");
  return result;
}

Future<bool> versionUpdateAlert(
    BuildContext context, isMandatory, description) async {
  bool result = await UpdateDailogue.appUpdateAlert(context,
      title: "New update available !",
      description: description,
      isMandatory: isMandatory);
  return result;
}
