import 'dart:convert';
import 'dart:io';
import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/services.dart';
import 'package:geolocator/geolocator.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:share/share.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/screens/settings/printer_settings.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_operations_utility.dart';
import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../model/sales_payment_mapping.dart';
import '../common_widgets/common_alert_dialogue/permission_dialog.dart';
import '../common_widgets/text_widget.dart';
import '../constants/strings_keys.dart';
import '../constants/styles.dart';
import '../error_logs/error_logs_function.dart';
import '../local_notifications/local_notification_service.dart';
import '../printer_utilities/printer_commands.dart';
import '../push_notifications/firebase_mesage_service.dart';
import 'fn_utilities.dart';
import 'list_class.dart';
import 'package:pdf/widgets.dart' as pw;

enum PrinterFor { bill, kot, both }

class PrinterUtilities {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  FnUtilities fnUtilities = new FnUtilities();
  DBOperations dbOperations = new DBOperations();
  final dbHelper = DatabaseHelper.instance;
  BuildContext? progressContext;
  //bool paperCut = false;
  bool connected = false;
  bool kotPrint = false;
  bool? isIndividualKOT;
  bool isReturnAmount = false;
  String? shopName;
  String? shopAddress1;
  String? shopAddress2;
  String? shopAddress3;
  String? shopFooterText;
  String? shopFooterText2;
  String? shopGstNo;
  String? orderTitle;
  bool isResetInvoice = false;
  String? shopImageID;
  PrinterDevices? reportPrinter;
  bool isMultiPrint = false;
  bool isBluetoothConected = false;

//  PrinterUtilities() {
//     getPrinterDetails();
//  }
  //GET PRINTER SETTING DETAILS
  getPrinterDetails() async {
    if (await fnUtilities.workSpaceSetValues(key_kotEnable) == "1") {
      kotPrint = true;
    } else {
      kotPrint = false;
    }
    if (await fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1") {
      isReturnAmount = true;
    } else {
      isReturnAmount = false;
    }
    if (await fnUtilities.workSpaceSetValues(key_kotIndividualEnable) == "1") {
      isIndividualKOT = true;
    } else {
      isIndividualKOT = false;
    }
    fnUtilities.workSpaceDeviceSetValue(key_printFormat);
    if (fnUtilities.workSpaceDeviceSetValue(key_multiPrint) == "0") {
      isMultiPrint = false;
    } else {
      isMultiPrint = true;
    }
  }

  /// Get Printer Info
  PrinterDevices? printerForBill;
  PrinterDevices? printerForKOT;

  getPrinters() {
    /// Get Printer Informations
    printerForBill = getCurrentPrinter(PrinterFor.bill);
    printerForKOT = getCurrentPrinter(PrinterFor.kot);
  }

  ///
  getReportPrinter() {
    reportPrinter = getCurrentPrinter(PrinterFor.bill);
    return reportPrinter;
  }

  //ASSIGN SHOP DETAILS FROM LIST
  getShopDetails({String? orderType}) async {
    await dbOperations.queryAllShopDeatails();
    for (ShopDeatails r in ListUtility.shopDetailsList) {
      shopName = r.name != null ? r.name : appName;
      shopAddress1 = r.address1;
      shopAddress2 = r.address2;
      shopAddress3 = r.address3;
      shopGstNo = r.gstNo;
      shopFooterText = r.footerText;
      shopFooterText2 = r.footerText2;
      shopImageID = r.imageID;
    }
    var takeAwayTitle = fnUtilities.workSpaceSetValues(key_takeAwayTitle);
    var eatAndPayTitle = fnUtilities.workSpaceSetValues(key_eatAndPayTitle);
    var tableOrderTitle = fnUtilities.workSpaceSetValues(key_tableOrderTitle);
    if (orderType == "T") {
      orderTitle = tableOrderTitle != "" ? tableOrderTitle : null;
    } else if (orderType == "SO") {
      orderTitle = eatAndPayTitle != "" ? eatAndPayTitle : null;
    } else {
      orderTitle = takeAwayTitle != "" ? takeAwayTitle : null;
    }
    isResetInvoice = fnUtilities.workSpaceSetValues(key_resetInvoiceNo) == "1";
  }

  commonOrderTitleFunction(Sales? sale, {String? saleType}) async {
    if (sale?.fkTableID != null) {
      saleType = "T";
    }
    await getShopDetails(orderType: saleType ?? "");
  }

  //GET PRINTER DETAILS
  Future<PrinterDevices?> getPrinterInfo({PrinterFor printFor = PrinterFor.both}) async {
    PrinterDevices? printerDevice;
    SharedPreferences prefs = await SharedPreferences.getInstance();
    var printerDevicesString = prefs.getString("PrinterDevices");
    if (printerDevicesString != null) {
      ListUtility.printerDevicesList = printerDevicesString != "" ? PrinterDevices.decode(printerDevicesString) : [];
      printerDevice =
          ListUtility.printerDevicesList.firstWhereOrNull((element) => element.printerFor == printFor || element.printerFor == PrinterFor.both);
      // device = BluetoothDevice(
      //     printerDevice.printerName, printerDevice.printerAddress);
    }
    return printerDevice;
  }

  Future<bool> setPrinterInfo(List<PrinterDevices> printerDevices) async {
    try {
      ListUtility.printerDevicesList.clear();
      SharedPreferences prefs = await SharedPreferences.getInstance();
      ListUtility.printerDevicesList.addAll(printerDevices);

      final String printerDevicesString = PrinterDevices.encode(ListUtility.printerDevicesList);
      await prefs.setString('PrinterDevices', printerDevicesString);
      return true;
    } catch (e) {
      print(e);
      return false;
    }
  }

  Future<PrinterDevices?> initPrint(PrinterDevices? printerDevice) async {
    //PrinterDevices printerDevice = await getPrinterInfo(printFor: printFor);

    try {
      if (printerDevice?.printerAddress != null) {
        if (printerDevice?.printerType == ptNetwork) {
          bluetooth.setPrinterType(ptNetwork);
          await bluetooth.connectNetworkPrinter(printerDevice?.printerAddress ?? "", printerDevice?.portNumber ?? 9100);
        } else if (printerDevice?.printerType == ptUSB) {
          bluetooth.setPrinterType(ptUSB);
          await bluetooth
              .connect(BluetoothDevice(
            printerDevice?.printerName,
            printerDevice?.printerAddress,
          ))
              .catchError((error) {
            connected = false;
          });
          await Future.delayed(Duration(milliseconds: 1200));
        } else {
          bluetooth.setPrinterType(ptBluetooth);
          await connect(BluetoothDevice(
            printerDevice?.printerName,
            printerDevice?.printerAddress,
          ));
          await Future.delayed(Duration(milliseconds: 1200));
        }
      }
    } on Exception catch (ex) {
      log(ex.toString());
      ErrorLogsFunction.insertErrorLogs(ex, "initPrint()");
    }
    return printerDevice;
  }

  //GET BLUETOOTH DEVICE DETAILS
  // getBlutoothDevice() async {
  //   SharedPreferences prefs = await SharedPreferences.getInstance();
  //   MyApp.printerAddress = prefs.getString("printerAddress1");
  //   MyApp.printerName = prefs.getString("printerName1");
  // }

  //CHECK PRINTER CONNECTION
  Future<void> connect(BluetoothDevice? _device) async {
    if (_device == null) {
    } else {
      try {
        await bluetooth.isConnected.then((isConnected) async {
          if (!(isConnected ?? false)) {
            await bluetooth.connect(_device).catchError((error) {
              connected = false;
            });
            connected = true;
          }
        });
      } catch (e) {}
    }
  }

  //PROGRESS ALERT DIALOG FOR WAITING
  ProgressAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        progressContext = context;
        return Center(child: new CircularProgressIndicator());
      },
    );
  }

  //BLUETOOTH TURN ON ALERT DIALOG
  bluetoothTurnOnAlertDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return Container(
          height: 200,
          child: AlertDialog(
            content: Container(
              child: Text(
                "Turn on bluetooth, which uses printer service.",
                style: TextStyle(color: primaryTextColor, fontSize: 18.0),
              ),
            ),
            actions: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 40,
                  width: 40,
                  child: TextWidget("OK", fontSize: textSizeMedium, textColor: buttonThemeColor, isCentered: true),
                  decoration: boxDecoration(radius: 8.0),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  //DEVICE CONNECTION ALERT DIALOG
  deviceNotConnectedAlertDialog(BuildContext context) async {
    PrinterDevices? printerDevice = await getPrinterInfo();
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) {
          return PermisionAlertDialog(
            content: "Device not connected or someone already connected",
            buttonNameOne: "Reconnect",
            onTabButtonOne: () {
              Navigator.pop(context);
              initPrint(printerDevice);
            },
            buttonNameTwo: "OK",
            onTabButtonTwo: () {
              Navigator.of(context).pop();
            },
          );
        });
  }

  //SET DEVICE ALERT DIALOG
  deviceAlertDialog(BuildContext context) {
    return showDialog(
        barrierDismissible: false,
        context: context,
        builder: (context) {
          return PermisionAlertDialog(
            content: "No device available, set default printer in settings",
            buttonNameOne: "NO,THANKS",
            onTabButtonOne: () {
              Navigator.pop(context);
            },
            buttonNameTwo: "OK",
            onTabButtonTwo: () {
              Navigator.push(context, MaterialPageRoute(builder: (context) => printerSettingsPage())).then((value) {
                if (ListUtility.printerDevicesList.isNotEmpty) {
                  Navigator.pop(context);
                }
              });
            },
          );
        });
  }

  //TURN ON LOCATION FOR BLUETOOTH CONNECTION
  createAlertDialogLocationPermisssion(BuildContext buildContext) {
    return showDialog(
        context: buildContext,
        builder: (context) {
          return PermisionAlertDialog(
            content: "Turn on device location, which uses printer service.",
            buttonNameOne: "NO,THANKS",
            onTabButtonOne: () {
              Navigator.pop(context);
            },
            buttonNameTwo: "OK",
            onTabButtonTwo: () async {
              Navigator.pop(context, 'true');
              await Geolocator.openLocationSettings();
            },
          );
        });
  }

  printPaymentDetails(List<SalesPaymentMapping>? paymentMappingList, String? totalAmount, {int? paperSize}) {
    double givenAmount = 0.0;
    double returnAmount = 0.0;
    double totalPayedAmount = 0;
    double total = double.parse(totalAmount ?? '0');
    List<int> printBytes = [];
    if (paymentMappingList != null && isReturnAmount) {
      for (int i = 0; i < paymentMappingList.length; i++) {
        totalPayedAmount = totalPayedAmount + paymentMappingList[i].amount!.toDouble();
        givenAmount = totalPayedAmount;
      }
      returnAmount = totalPayedAmount - total;

      printBytes += printCustom("Paid Amount:".padRight(5) + "${givenAmount.toStringAsFixed(2)}".padLeft(20), 0, 2);
      printBytes += printCustom("Paid By:".padRight(5) + "".padLeft(20), 0, 2);
      for (int i = 0; i < paymentMappingList.length; i++) {
        printBytes +=
            printCustom("${paymentMappingList[i].paymentName}".padRight(5) + "${paymentMappingList[i].amount?.toStringAsFixed(2)}".padLeft(20), 0, 2);
      }

      printBytes += printCustom("Net Amount:".padRight(5) + "${total.round().toStringAsFixed(2)}".padLeft(20), 0, 2);
      if (returnAmount >= 1) {
        printBytes += printCustom("Return Amount:".padRight(5) + "${returnAmount.toStringAsFixed(2)}".padLeft(20), 0, 2);
      }
      if (paperSize == 58) {
        printBytes += printCustom(returnDashedLines(42), 0, 0);
      } else {
        printBytes += printCustom(returnDashedLines(64), 0, 0);
      }
    }
    return printBytes;
  }

  List<String> splitStringByLength(String str, int length) {
    List<String> stringList = [];
    int i = 0;
    while (i < str.length) {
      if (str.length - i > length - 1) {
        var data = str.substring(i, i + length);
        stringList.add(data);
      } else {
        var data = str.substring(i);
        stringList.add(data);
      }
      i = i + length;
    }
    return stringList;
  }

  List<String> splitStringByLengthandSpace(String name, int length) {
    //58mm - 16
    //80mm - 24
    var str = name.trim();
    List<String> stringList = [];
    if (str.length > length) {
      var splitList = str.split(" ");
      String tempString = "";

      int i = 0;
      for (String data in splitList) {
        if (data.length > 0) {
          if (data.length < length) {
            var tempString1 = tempString + data + " ";
            if (tempString1.length < length) {
              tempString = tempString1;
              if (i == splitList.length - 1) {
                stringList.add(tempString.substring(0, tempString1.length - 1));
                tempString = "";
              }
            } else {
              stringList.add(tempString1.substring(0, tempString.length - 1));
              tempString = data + " ";
              if (i == splitList.length - 1) {
                stringList.add(tempString.substring(0, tempString.length - 1));
                tempString = "";
              }
            }
          } else {
            stringList.add(data);
          }
        } else {
          if (i == splitList.length - 1) {
            stringList.add(tempString.substring(0, tempString.length - 1));
            tempString = "";
          }
        }
        i++;
      }
    } else {
      stringList.add(str);
    }
    return stringList;
  }

  returnDashedLines(int size) {
    String dash = "";
    for (int i = 0; i < size; i++) {
      dash += "-";
    }
    return dash;
  }

  checkQtyDoubleValue(String value) {
    if ((double.parse(value) % 1) == 0) {
      var firstValue = value.split('.');
      return firstValue[0];
    } else {
      return value;
    }
  }

  //PRINTER DISCONNECTION
  disconnect() async {
    try {
      await bluetooth.disconnect();
    } catch (e) {
      print(e);
    }
  }

  //REPORT LONG PRINTER DEVICE FUNCTION
  commonReportPrint(BuildContext context, String printLines, {String? develop}) async {
    List<int> printBytes = [];
    if (await checkPrinterConnection(context)) {
      await getReportPrinter();
      await getPrinterDetails();

      /// connect printer
      //if (isMultiPrint) {
      await initPrint(reportPrinter);
      //  }
      printBytes += await commonPrint(printLines, develop: develop ?? '');
      bluetooth.writeBytes(printBytes);
      if (isMultiPrint) {
        await Future.delayed(Duration(milliseconds: 500));
        await disconnect();
      }
    }
  }

  List<int> commonShopName(String? shopName, int length) {
    List<int> printBytes = [];
    var splittedShopname = splitStringByLengthandSpace(shopName ?? '', length);
    for (int i = 0; i < splittedShopname.length; i++) {
      if (i == 0) {
        printBytes += printCustom("${splittedShopname[i]}", 2, 1);
      } else {
        printBytes += printCustom("${splittedShopname[i]}", 2, 1);
      }
    }
    return printBytes;
  }

  shopNameWidget() {
    List<int> printBytes = [];
    if (shopName != "" && shopName != null) {
      printBytes += printCustom("${shopName?.toUpperCase()}", 2, 1);
    } else {
      printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
    }
    return printBytes;
  }

  List<int> commonShopAddress() {
    List<int> printBytes = [];
    if (shopAddress1 != "" && shopAddress1 != null) {
      printBytes += printCustom("$shopAddress1", 1, 1);
    }

    if (shopAddress2 != "" && shopAddress2 != null) {
      printBytes += printCustom("$shopAddress2", 1, 1);
    }
    if (shopAddress3 != "" && shopAddress3 != null) {
      printBytes += printCustom("$shopAddress3", 1, 1);
    }
    if (shopGstNo != "" && shopGstNo != null) {
      printBytes += printCustom("GSTIN: ${shopGstNo?.toUpperCase()}", 1, 1);
    }
    return printBytes;
  }

  List<int> commonShopFooterText(bool isPrintnewline) {
    List<int> printBytes = [];
    if (shopFooterText != "" && shopFooterText != null) {
      isPrintnewline ? printBytes += printNewLine() : null;
      printBytes += printCustom("$shopFooterText", 1, 1);
    }
    if (shopFooterText2 != "" && shopFooterText2 != null) {
      printBytes += printCustom("$shopFooterText2", 1, 1);
    }
    return printBytes;
  }

  printKOTNotes(
    String? notes,
  ) {
    List<int> printBytes = [];
    if (notes != null && notes.trim().isNotEmpty) {
      printBytes += printCustom("(${notes})", 1, 0);
    }
    return printBytes;
  }

  printerHeader(PrinterDevices? printerInfo) async {
    List<int> printBytes = [];
    await getShopDetails();
    if (shopName != "" && shopName != null) {
      if (printerInfo?.paperSize == 58) {
        printBytes += commonShopName(shopName, 16);
      } else {
        printBytes += commonShopName(shopName, 24);
      }
    } else {
      printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
    }
    printBytes += commonShopAddress();
    return printBytes;
  }

  Future<List<int>> printerFooter(PrinterDevices? printerInfo, String? develop) async {
    List<int> printBytes = [];
    printBytes += printNewLine();
    if (develop != null) {
      printBytes += commonShopFooterText(false);

      if (develop == "Yes") {
        printBytes += await printDevelopedBy();
      } else if (develop == "Yes80MM") {
        printBytes += await printDevelopedBy(is80MM: true);
      }
    }
    for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
      printBytes += printNewLine();
    }
    if (printerInfo?.paperCut ?? false) {
      printBytes += paperCut();
    }
    return printBytes;
  }

  Future<List<int>> printDevelopedBy({bool is80MM = false}) async {
    List<int> printBytes = [];
    if ((await fnUtilities.workSpaceSetValues(key_printerAdEnable, table: "WorkSpaceSettings") == "1")) {
      printBytes += printNewLine();
      if (is80MM) {
        printBytes += printCustom("by $appName                          contact: +91 9487674499", 0, 1);
      } else {
        printBytes += printCustom("by $appName     contact: +91 9487674499", 0, 1);
      }
    }
    return printBytes;
  }

  Future<List<int>> commonPrint(String printLines, {String? develop}) async {
    List<int> printBytes = [];
    final List<dynamic> dataList = jsonDecode(printLines);
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        printBytes += await printerHeader(reportPrinter);
        for (dynamic p in dataList) {
          printBytes += printCustom("${p['text']}", int.parse(p['size'].toString()), int.parse(p['alignment'].toString()));
        }
        printBytes += await printerFooter(reportPrinter, develop);
      }
    });
    return printBytes;
  }
  // commonPrintWIFI(String printLines, {String develop}) async {
  //   toast("WIFI Print");
  //   bluetooth.setPrinterType("network");
  //   final List<dynamic> dataList = jsonDecode(printLines);
  //   String printerIp = "*************";
  //   int printerPort = 9100;
  //   bluetooth.connectNetworkPrinter(printerIp, printerPort);

  //   // await bluetooth.isConnected.then((isConnected) async {
  //   //   if (isConnected) {
  //   await printerHeader();
  //   for (dynamic p in dataList) {
  //     // bluetooth.printNetwork("${p['text']}");
  //     await bluetooth.printCustom(
  //         "${p['text']}",
  //         int.parse(p['size'].toString()),
  //         int.parse(p['alignment'].toString()));
  //   }
  //   await printerFooter(develop);
  //   //   }
  //   // });
  // }

// Share--------------------------------------------------
  shareCommonReport(String body, String type) async {
    await getShopDetails();
    String data = "";
    if (shopName != "" && shopName != null) {
      data += "\n${shopName?.toUpperCase()}";
    } else {
      data += appName.toUpperCase();
    }
    if (shopAddress1 != "" && shopAddress1 != null) {
      data += "\n$shopAddress1";
    }
    if (shopAddress2 != "" && shopAddress2 != null) {
      data += "\n$shopAddress2";
    }
    if (shopAddress3 != "" && shopAddress3 != null) {
      data += "\n$shopAddress3";
    }
    if (shopGstNo != "" && shopGstNo != null) {
      data += "\nGSTIN: ${shopGstNo?.toUpperCase()}";
    }
    data += "\n";
    data += body;
    data += "\n";
    if (shopFooterText != "" && shopFooterText != null) {
      data += type == "Sale" ? "\n$shopFooterText" : "";
    }
    if (shopFooterText2 != "" && shopFooterText2 != null) {
      data += type == "Sale" ? "\n$shopFooterText2" : "";
    }
    data += type == "Sale" ? "\n" : "";
    if ((await fnUtilities.workSpaceSetValues(key_printerAdEnable, table: "WorkSpaceSettings") == "1")) {
      data += type == "Sale" ? "\nby $appName" : "";
      data += type == "Sale" ? "\ncontact:  +91 9487674499" : "";
    }
    Share.share(data);
  }

  //PDF----------------------------------------------------
  File? imageFile;
  getShopImage() async {
    if (!shopImageID.isEmptyOrNull) {
      var filepath = await fnUtilities.readFileFromLocal(shopImageID);
      imageFile = filepath;
    }
  }

  pw.Widget showImage() {
    return !shopImageID.isEmptyOrNull && imageFile != null && imageFile!.existsSync()
        ? pw.Container(
            width: 80,
            height: 80,
            alignment: pw.Alignment.center,
            child: pw.Image(pw.MemoryImage(
              imageFile!.readAsBytesSync(),
            )))
        : pw.Container();
  }

  showSimpleNotification(String fileName, var path) async {
    // var androidDetails = const AndroidNotificationDetails('id', 'channel', priority: Priority.high, importance: Importance.max, icon: "@mipmap/ic_launcher");
    // var iOSDetails = const IOSNotificationDetails();
    // var platformDetails = NotificationDetails(android: androidDetails, iOS: iOSDetails);
    // await flutterLocalNotificationsPlugin.show(0, "Downloaded Successfully", '', platformDetails, payload: path);
    var payload = FCM.buidNotificationPayload({"path": "filedownload", "filelocation": path}, notifyType: "LOCAL");
    RemoteNotification notification = RemoteNotification(title: "Downloaded Successfully", body: payload);
    LocalNotification.showNotification(notification, payload);
  }

  Future<List<int>> printShopImage() async {
    List<int> printBytes = [];
    if (await fnUtilities.workSpaceSetValues(key_imagePrint, table: "WorkSpaceSettings") == "1") {
      if (!shopImageID.isEmptyOrNull) {
        var filepath = await fnUtilities.readFileFromLocal(shopImageID);
        if (filepath != null && filepath.path != "") {
          if (BlueThermalPrinter.printerType == "bluetooth") {
            await bluetooth.printImage(filepath.path);
          } else {
            printBytes += await printImage(filepath.path);
          }
          printBytes += printNewLine();
        }
      }
    }
    return printBytes;
  }

  printLogoImage() async {
    List<int> printBytes = [];
    var filepath = await fnUtilities.readFileFromLocal(applogo);
    if (filepath?.path != null && filepath?.path != "") {
      printBytes += printNewLine();
      printBytes += await printImage(filepath!.path);
      printBytes += printNewLine();
    }
    return printBytes;
  }

  PrinterDevices getCurrentPrinter(PrinterFor printFor) {
    if (ListUtility.printerDevicesList.isNotEmpty) {
      if (ListUtility.printerDevicesList.length == 1) {
        return ListUtility.printerDevicesList[0];
      } else {
        // if (printFor == PrinterFor.bill || printFor == PrinterFor.both) {
        //   return ListUtility.printerDevicesList[0];
        // }
        // if (printFor == PrinterFor.kot || printFor == PrinterFor.kot) {
        //   return ListUtility.printerDevicesList[1];
        // }
        return ListUtility.printerDevicesList.firstWhere((element) => element.printerFor == printFor || element.printerFor == PrinterFor.both);
      }
    }
    return PrinterDevices();
  }

  Future<bool> checkPrinterConnection(BuildContext context, {PrinterFor printFor = PrinterFor.bill}) async {
    if (ListUtility.printerDevicesList.isNotEmpty) {
      PrinterDevices currentPrinter = getCurrentPrinter(printFor);
      if (currentPrinter.printerType == ptBluetooth) {
        if (await bluetooth.isOn ?? false) {
          bool isLocationServiceEnabled = await Geolocator.isLocationServiceEnabled();
          if (isLocationServiceEnabled) {
            // if (currentPrinter.multiMobilePrint == false &&
            //     ListUtility.printerDevicesList.length == 1) {
            //await initPrint(currentPrinter);

            // }
            await getPrinterDetails();
            if (isMultiPrint) {
              return true;
            } else {
              // await initPrint(currentPrinter);
              bool isConnected = await bluetooth.isConnected ?? false;

              if (isConnected) {
                return true;
              } else {
                deviceNotConnectedAlertDialog(context);
                return false;
              }
            }
          } else {
            await createAlertDialogLocationPermisssion(context);
            return false;
          }
        } else {
          await bluetoothTurnOnAlertDialog(context);
          return false;
        }
      } else {
        return true;
      }
    } else {
      await deviceAlertDialog(context);
      return false;
    }
  }

  /// test printer function
  testPrinterMethod(PrinterDevices testPrintDevice) async {
    try {
      List<int> printBytes = [];
      await getShopDetails(orderType: "");
      await initPrint(testPrintDevice);
      await Future.delayed(Duration(milliseconds: 10));
      if (!shopImageID.isEmptyOrNull) {
        var filepath = await fnUtilities.readFileFromLocal(shopImageID);
        if (filepath != null && filepath.path != "") {
          //printBytes += await printImage(filepath.path);
          // printBytes1 += printNewLine();
          // await bluetooth.printImage(filepath.path);
          Uint8List printBytesContent = Uint8List.fromList(printBytes);
          //await bluetooth.printImageBytes(printBytesContent);
        }
        // Uint8List printBytesContent = Uint8List.fromList(printBytes1);
        // await bluetooth.writeBytes(printBytesContent);
      }
      printBytes += printCustom("${appName}", 2, 1);
      printBytes += printNewLine();
      printBytes += printCustom("${"  Printer has been successfully established with the $appName application"}", 1, 1);
      printBytes += printNewLine();
      printBytes += printNewLine();
      printBytes += printCustom("${'Developed by Cloudstier Solution Pvt Ltd'}", 1, 1);

      for (int i = 0; i < (testPrintDevice.feedSize ?? 0); i++) {
        printBytes += printNewLine();
      }
      if (testPrintDevice.paperCut ?? false) {
        printBytes += paperCut();
      }
      await bluetooth.writeBytes(printBytes).then((value) {
        log(value);
      });
      await Future.delayed(Duration(milliseconds: 1200));
      await disconnect();
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "testPrinterMethod - PrinterUtlities");
    }
  }

  List<int> printOnlineRefNo(Sales? sales, {bool isKOT = false}) {
    List<int> printBytes = [];
    if (sales?.onlineRefNo != null && sales?.onlineRefNo != "") {
      if (isKOT) {
        printBytes += printCustom("(${sales?.onlineRefNo})", 2, 1);
      } else {
        printBytes += printCustom("Online No: ${sales?.onlineRefNo}", 0, 0);
      }
    }
    return printBytes;
  }

  ///Date Format function
  String formatDate(String date) {
    return DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(date));
  }

  ///To return current date and time
  String currentDateTime() {
    return DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse(DateTime.now().toString()));
  }
}
