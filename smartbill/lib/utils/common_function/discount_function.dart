import 'package:nb_utils/src/extensions/string_extensions.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/common_function/sales_function.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/sales_tax_calculate.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/discounts.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import '../constants/strings_keys.dart';
import '../dbutils/discount_db_script.dart';
import '../dbutils/product_details_db_script.dart';
import '../dbutils/product_key_db_script.dart';
import '../dbutils/sales_trans_staging.dart';
import 'fn_utilities.dart';
import 'list_class.dart';

class DiscountFunction {
  final dbHelper = DatabaseHelper.instance;
  DiscountDB discountDB = DiscountDB();
  FnUtilities fnUtilities = new FnUtilities();
  SalesFunctions salesFun = SalesFunctions();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  ProductKeyDBScript productKeyDBScript = ProductKeyDBScript();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();

  calculateDiscount(String mode, {Discounts? discount}) async {
    for (Transactions d in ListUtility.saleList) {
      if (d.toppingId == null) {
        d = await categoryWithAmount(d, mode, discount: discount);
      }
    }
  }

  getCategoryID(Products item) async {
    String? categoryID;
    if (item.productKeyName != null) {
      categoryID = await productKeyDBScript.getProductKeyID(item.productKeyName.toString());
    }
    return categoryID;
  }

  categoryWithAmount(Transactions item, String mode, {Discounts? discount}) async {
    if (item.prodId != null) {
      item.categoryID = await productKeyDBScript.getCategoryIDUsingProductID(item.prodId.toString());
      if (item.categoryID != null) {
        if (discount != null) {
          List categoryIDList = categoryLists(discount);
          if (categoryIDList.contains(item.categoryID)) {
            item = await finalCategoryDiscountPrice(item, mode, discounts: discount);
          }
        } else {
          item = await finalCategoryDiscountPrice(item, mode);
        }
      }
    }
    return item;
  }

  categoryLists(Discounts discounts) {
    var splitCategory = discounts.categoryID?.split(",");
    List categoryList = [];
    for (String s in splitCategory ?? []) {
      categoryList.add(s);
    }
    return categoryList;
  }

  getAllValidDiscountFunction(String? categoryID) async {
    final allRows = categoryID != null ? await discountDB.selectCategoryDiscount(categoryID) : await discountDB.selectAllDiscount();
    List<Discounts> discountList = [];
    allRows.forEach((row) => discountList.add(Discounts.fromMap(row)));
    return discountList;
  }

  returnDiscountList(String mode, {Transactions? sale, double? saleAmount, bool? list}) async {
    List<Discounts> discountsList = await getAllValidDiscountFunction(sale?.categoryID);
    List<Discounts> newDiscountsList = [];
    if (discountsList.isNotEmpty) {
      for (Discounts d in discountsList) {
        Discounts model = await fnUtilities.splitValueFromFormula(d);
        String type = sale?.categoryID != null ? "P" : "S";
        if ((model.type == type && model.mode == "A" && list == null) || (list != null && model.mode == "C")) {
          newDiscountsList.add(model);
        }
      }
    }
    return newDiscountsList;
  }

  foundPercentageIntoAmountDiscount(String mode, {Transactions? sale, double? saleAmount}) async {
    List<Discounts> availableDiscount = await returnDiscountList(mode, sale: sale, saleAmount: saleAmount);
    String? amount = sale != null ? sale.actualPrice : saleAmount.toString();
    if (availableDiscount.isNotEmpty) {
      for (Discounts d in availableDiscount) {
        if (d.discountOn == "P") {
          d.discountAmount = (((double.parse(amount.toString()) * double.parse(d.discount.toString())) / 100)).toString();
        } else {
          d.discountAmount = d.discount;
        }
      }
    }
    return availableDiscount;
  }

  checkValidDateDiscount(String mode, {Transactions? sale, double? saleAmount, bool? list}) async {
    List<Discounts> availableDiscount = [];
    if (mode == "C" && (list ?? false)) {
      availableDiscount = await returnDiscountList(mode, list: list);
    } else if (sale == null && saleAmount == null) {
      availableDiscount = await returnDiscountList(mode, sale: sale, saleAmount: saleAmount);
    } else {
      availableDiscount = await foundPercentageIntoAmountDiscount(mode, sale: sale, saleAmount: saleAmount);
    }
    List<Discounts> availableDiscountWithDate = [];
    if (availableDiscount.isNotEmpty) {
      for (Discounts d in availableDiscount) {
        DateTime now = DateTime.now();
        if (d.fromDate!.subtract(const Duration(days: 1)).isBefore(now) && d.toDate!.add(const Duration(days: 1)).isAfter(now)) {
          availableDiscountWithDate.add(d);
        }
      }
    }
    return availableDiscountWithDate;
  }

  findLowDiscount(String mode, {Transactions? sale, double? saleAmount}) async {
    List<Discounts> availableDiscount = await checkValidDateDiscount(mode, sale: sale, saleAmount: saleAmount);
    Discounts? dis;
    if (availableDiscount.isNotEmpty) {
      dis = availableDiscount.reduce((min, element) {
        if (double.parse(min.discountAmount.toString()) < double.parse(element.discountAmount.toString())) {
          return min;
        } else {
          return min;
        }
      });
    }
    return dis;
  }

  finalCategoryDiscountPrice(Transactions sale, String mode, {Discounts? discounts}) async {
    Discounts? discount;
    if (discounts == null) {
      discount = await findLowDiscount(mode, sale: sale);
    } else if (discounts != null) {
      discount = await percentageIntoAmountDiscountModel(discounts, sale: sale);
    }
    if (discount != null) {
      String discountPrice = (double.parse(sale.qty.toString()) * double.parse(discount.discountAmount.toString())).toString();
      if (double.parse(discountPrice) > double.parse(discount.minDiscount.toString()) &&
          double.parse(discountPrice) < double.parse(discount.maxDiscount.toString())) {
        sale.discountPrice = discountPrice;
        sale.discountFormula = discount.formula;
        sale.amount = ((double.parse(sale.qty.toString()) * double.parse(sale.actualPrice.toString())) - double.parse(sale.discountPrice.toString()))
            .toString();
      } else if (double.parse(discountPrice) > double.parse(discount.minDiscount.toString()) &&
          double.parse(discountPrice) > double.parse(discount.maxDiscount.toString())) {
        sale.discountPrice = discount.maxDiscount;
        sale.discountFormula = discount.formula;
        sale.amount =
            ((double.parse(sale.qty.toString()) * double.parse(sale.actualPrice.toString())) - double.parse(discount.maxDiscount.toString()))
                .toString();
      }
    }
    return sale;
  }

  salesCommonDiscount1(String mode, {Discounts? discount}) async {
    double totalAmount = 0.0;
    double totalQty = 0.0;
    for (Transactions d in ListUtility.saleList) {
      totalAmount += (double.parse(d.qty.toString()) * double.parse(d.actualPrice.toString()));
      totalQty += double.parse(d.qty.toString());
    }
    if (discount == null) {
      discount = await findLowDiscount(mode, saleAmount: totalAmount);
    } else {
      discount = await percentageIntoAmountDiscountModel(discount, saleAmount: totalAmount);
    }
    // double commonDiscount;
    if (discount != null) {
      String? discountPrice = discount.discountAmount;
      discount.discountAmount = null;
      if (totalAmount > double.parse(discount.minDiscount.toString()) && totalAmount < double.parse(discount.maxDiscount.toString())) {
        discount.discountAmount = discountPrice;
        // commonDiscount = await salesDiscountCalculation(discount.discountAmount,
        //     totalQty.toString(), totalAmount.toString());
        // if (commonDiscount != null) {
        //   for (Transactions d in ListUtility.saleList) {
        //     d.discountPrice =
        //         commonDiscount != null ? commonDiscount.toString() : null;
        //     d.amount = await productBasedSaleDiscount(d, commonDiscount);
        //   }
        // }
      } else {
        discount = null;
      }
      // else if (totalAmount > double.parse(discount.minDiscount) &&
      //     totalAmount > double.parse(discount.maxDiscount)) {
      //   discount.discountAmount = discountPrice;
      // commonDiscount = await salesDiscountCalculation(
      //     discount.discountAmount, totalQty.toString(), discount.maxDiscount);
      // if (commonDiscount != null) {
      //   for (Transactions d in ListUtility.saleList) {
      //     d.discountPrice =
      //         commonDiscount != null ? commonDiscount.toString() : null;
      //     d.amount = await productBasedSaleDiscount(d, commonDiscount);
      //   }
      // }
      // }
    }
    return discount;
  }

  salesDiscountCalculation(String discountAmount, String qty, String saleAmount) {
    double commonDiscount = double.parse(discountAmount) / ListUtility.saleList.length;
    return commonDiscount;
  }

  productBasedSaleDiscount(Transactions item, double commonAmount) {
    return ((double.parse(item.qty.toString()) * double.parse(item.actualPrice.toString())) - (commonAmount)).toString();
  }

  percentageIntoAmountDiscountModel(Discounts discount, {Transactions? sale, double? saleAmount}) async {
    String? amount = sale != null ? sale.actualPrice : saleAmount.toString();
    if (discount != null) {
      if (discount.discountOn == "P") {
        discount.discountAmount = (((double.parse(amount.toString()) * double.parse(discount.discount.toString())) / 100)).toString();
      } else {
        discount.discountAmount = discount.discount;
      }
    }
    return discount;
  }

  //========SALE DISCOUNT============

  saleDiscount(String mode, {Discounts? discount, String? saleID, int? saleNum}) async {
    double totalAmount = 0.0;
    var total = await salestransStagingDBScript.getStagingTotalAmount(saleID ?? "", saleNum: saleNum ?? 0);
    if (total != null) {
      totalAmount = total[0]['amount'] != null ? double.parse(total[0]['amount'].toString()) : 0;
    }
    for (Transactions d in ListUtility.saleList) {
      totalAmount += (double.parse(d.qty ?? "0") * double.parse(d.actualPrice ?? "0"));
    }
    List<Discounts> validDiscount = [];
    if (discount == null) {
      validDiscount = await checkValidDateDiscount(mode);
    } else {
      validDiscount.add(discount);
    }
    List<Discounts> finalDiscountList = [];
    if (validDiscount.isNotEmpty) {
      for (Discounts dis in validDiscount) {
        if (dis.discountOn == "P" && double.parse(dis.minDiscount ?? "0") <= totalAmount && double.parse(dis.maxDiscount ?? "0") >= totalAmount) {
          dis.discountAmount = (((totalAmount * double.parse(dis.discount ?? "0")) / 100)).toString();
          finalDiscountList.add(dis);
        } else if (dis.discountOn == "P" &&
            double.parse(dis.minDiscount ?? "0") <= totalAmount &&
            double.parse(dis.maxDiscount ?? "0") <= totalAmount) {
          dis.discountAmount = (((double.parse(dis.maxDiscount ?? "0") * double.parse(dis.discount ?? "0")) / 100)).toString();
          finalDiscountList.add(dis);
        } else if (dis.discountOn == "F") {
          dis.discountAmount = dis.discount;
        }
      }
      List<Discounts> fixedDiscountList =
          validDiscount.where((element) => element.discountOn == "F" && double.parse(element.minDiscount ?? "0") <= totalAmount).toList();
      if (fixedDiscountList.isNotEmpty) {
        Discounts? dis = await maxDiscount(fixedDiscountList);
        if (dis != null) {
          finalDiscountList.add(dis);
        }
      }
    }
    return finalDiscountList;
  }

  salesCommonDiscount(String mode, {Discounts? discount, String? saleID, int? saleNum}) async {
    List<Discounts> availableDiscount = await saleDiscount(mode, discount: discount, saleID: saleID, saleNum: saleNum);
    Discounts? dis = await minDiscount(availableDiscount);
    return dis;
  }

  minDiscount(List<Discounts> availableDiscount) {
    Discounts? dis;
    if (availableDiscount.isNotEmpty) {
      final valueList = Set();
      availableDiscount.retainWhere((x) => valueList.add(x.discountAmount));
      // availableDiscount.sort((a, b) {
      //   return a.discountAmount.compareTo(b.discountAmount);
      // });
      dis = availableDiscount.reduce((min, element) {
        if (double.parse(min.discountAmount.toString()) < double.parse(element.discountAmount.toString())) {
          return min;
        } else {
          return element;
        }
      });
      // dis = availableDiscount.first;
    }
    return dis;
  }

  maxDiscount(List<Discounts> availableDiscount) async {
    Discounts? dis;
    if (availableDiscount.isNotEmpty) {
      final valueList = Set();
      availableDiscount.retainWhere((x) => valueList.add(x.discountAmount));
      // availableDiscount.sort((a, b) {
      //   return a.discountAmount.compareTo(b.discountAmount);
      // });
      dis = availableDiscount.reduce((max, element) {
        if (double.parse(max.discountAmount.toString()) > double.parse(element.discountAmount.toString())) {
          return max;
        } else {
          return element;
        }
      });
      // dis = availableDiscount.last;
    }
    return dis;
  }

  //CALCULATE TAX FUNCTION
  callCalculate(Sales? sales) async {
    try {
      if (sales != null) {
        SalesCalculateRequest calculateRequest = new SalesCalculateRequest();
        calculateRequest.withTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
        calculateRequest.includedTax = fnUtilities.workSpaceSetValues(key_taxMode) == "1";
        var overallTax = fnUtilities.workSpaceSetValues(key_overAllTax) ?? "0";
        if (sales.salesId != null) {
          //For Update we need to find old transaction tax mode so we
          final reportDetail = ListUtility.reportList.firstWhere((element) => element.salesId == sales.salesId, orElse: () {
            return Sales();
          });
          if (reportDetail.taxMode != null) {
            if (reportDetail.taxMode == 1) {
              calculateRequest.withTax = false;
            } else if (reportDetail.taxMode == 2) {
              calculateRequest.withTax = true;
              calculateRequest.includedTax = true;
            } else if (reportDetail.taxMode == 3) {
              calculateRequest.withTax = true;
              calculateRequest.includedTax = false;
            }
          }
        }
        for (Transactions transactions in ListUtility.saleList) {
          var product = await productDetailsDBScript.getProductDetailsByID(transactions.prodId ?? "0");
          Products products = Products.fromMap(product!);
          if (transactions.discountPrice != null) {
            calculateRequest.discountAmount = double.parse(transactions.discountPrice ?? "0");
          }
          calculateRequest.qty = transactions.qty.toDouble();
          calculateRequest.rate = double.parse(transactions.actualPrice ?? "0");
          if (await fnUtilities.workSpaceSetValues(key_applyTaxFor) == "All") {
            calculateRequest.cgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
            calculateRequest.sgst = overallTax == "" || overallTax == null ? 0 : double.parse(overallTax) / 2;
          } else {
            calculateRequest.cgst = products.cGst == "" || products.cGst == null ? 0 : double.parse(products.cGst ?? "0");
            calculateRequest.sgst = products.sGst == "" || products.sGst == null ? 0 : double.parse(products.sGst ?? "0");
          }

          SalesCalculateResponse calculateResponse = salesFun.calculateTax(calculateRequest);
          transactions.price = calculateResponse.rate.toString();
          transactions.discountPrice = calculateResponse.discountAmount.toString();
          transactions.cGst = calculateResponse.cgst.toString();
          transactions.cGstAmount = calculateResponse.cgstAmt.toString();
          transactions.sGst = calculateResponse.sgst.toString();
          transactions.sGstAmount = calculateResponse.sgstAmt.toString();
          transactions.amountWithoutGst = calculateResponse.totalAmtWithoutGst.toString();
          transactions.amount = calculateResponse.totalAmt.toString();
        }
      }
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "callCalculate - DiscountFunction");
    }
  }
}
