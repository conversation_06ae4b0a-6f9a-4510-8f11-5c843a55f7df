// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:smartbill/utils/common_function/list_class.dart';
// import '../../model/hive_model/master_table_model.dart';
// import '../../model/hive_model/sales_category_model.dart';
// import '../constants/box_name.dart';

// class HiveBoxFunctions {
//   static initHive() async {
//     await Hive.initFlutter();
//     Hive.registerAdapter(MastersAdapter());
//     Hive.registerAdapter(SalesCategoryModelAdapter());
//   }

//   ///====================================================================================
//   /// SalesCategory Box
//   static Future<Box<SalesCategoryModel>> openSalesCategoryBox() async {
//     return await Hive.openBox<SalesCategoryModel>(BoxNames.salesCategory);
//   }

//   /// To store the Sales category
//   static storeSalesCategoryItem(List<SalesCategoryModel> salesCategoryList) async {
//     var box = await openSalesCategoryBox();
//     box.clear();
//     await box.addAll(salesCategoryList);
//     await box.close();
//   }

//   /// To get the Sales category
//   static Future<List<SalesCategoryModel>> getAllSalesCategoryFromBox() async {
//     var box = await openSalesCategoryBox();
//     var salesCategoryList = box.values.toList();
//     ListUtility.salesCategoryList.clear();
//     ListUtility.salesCategoryList.addAll(salesCategoryList);
//     await box.close();
//     return salesCategoryList;
//   }

//   ///========================================================================================
//   ///Master Table Box
//   static Future<Box<MasterTableModel>> openMasterTableBox() async {
//     return await Hive.openBox<MasterTableModel>(BoxNames.masterTable);
//   }

//   /// To store master table list in the master table box
//   static storeMasterTable(List<MasterTableModel> masterTableList) async {
//     var box = await openMasterTableBox();
//     box.clear();
//     await box.addAll(masterTableList);
//     await box.close();
//   }

//   /// To get the Sales category
//   static Future<List<MasterTableModel>> getAllMasterTableItemFromBox() async {
//     var box = await openMasterTableBox();
//     var masterTableList = box.values.toList();
//     ListUtility.masterTableList.clear();
//     ListUtility.masterTableList.addAll(masterTableList);
//     await box.close();
//     return masterTableList;
//   }
// }
