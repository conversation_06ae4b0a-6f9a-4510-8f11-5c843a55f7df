import 'package:smartbill/model/add_shop_table.dart';
import 'package:smartbill/model/advance_order_note_model.dart';
import 'package:smartbill/model/hive_model/master_table_model.dart';
import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/printer_devices.dart';
import 'package:smartbill/model/toppings_model.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/screens/manage_shifts/model/manage_shifts_model.dart';

import '../../model/payment_category_model.dart';

import '../../screens/expenses/model/expense_category_model.dart';
import '../../screens/salescategory/model/sales_category_model.dart';
import '../roles_permissions/model/receiving/get_all_permisions.dart';

class ListUtility {
  static List<Products> productList = [];
  static List<Sales> reportList = [];
  static List<Transactions> saleList = [];
  static List<Sales> holdReportList = [];
  static List<Transactions> holdSaleList = [];
  static List<ShopDeatails> shopDetailsList = [];
  static List<SubscriptionDetail> subscriptionDetails = [];
  static List<Activation> validityList = [];
  // static List<Plans> viewPlans = [];
  static List<WorkSpace> signedWorkspaceList = [];
  static List<WorkSpace> nonSignedWorkspaceList = [];
  static List<User> userList = [];
  // static List<Permissions> permissionList = [];
  static List<WorkspaceSettings> settingList = [];
  static List<WorkspaceSettings> settingDeviceList = [];
  static List<PrinterDevices> printerDevicesList = [];
  static List<WorkspaceSettings> userSettingList = [];
  static List<PaymentType> paymentTypeList = [];
  static List<ProductKey> productKeyList = [];
  static List<PriceUnitType> unitList = [];
  static List<ShopTable> shopTableList = [];
  static List<Topping> toppingsList = [];
  static List<ToppingsGroup> toppingGroupList = [];
  static List<ExpensesCategoryModel> expensesCategoryList = [];
  static List<ExpensesProduct> expensesItemList = [];
  static List<ExpenseTransactionModel> expenseTransList = [];
  static List<PaymentCategory> paymentCatList = [];
  static List<Permisions> permisionsList = [];
  // static List<ShiftModel> shiftWiseList = [];
  static List<SalesCategoryModel> salesCategoryList = [];
  static List<MasterTableModel> masterTableList = [];
  static List<ShiftwiseSalesModel> shiftSalesList = [];
  static List<AdvanceOrderNoteModel> orderNotesList = [];


  // Method to clear all static lists
  static clearAllLists() {
    productList.clear();
    reportList.clear();
    saleList.clear();
    holdReportList.clear();
    holdSaleList.clear();
    shopDetailsList.clear();
    subscriptionDetails.clear();
    validityList.clear();
    // viewPlans.clear();
    signedWorkspaceList.clear();
    nonSignedWorkspaceList.clear();
    settingList.clear();
    settingDeviceList.clear();
    printerDevicesList.clear();
    userSettingList.clear();
    paymentTypeList.clear();
    productKeyList.clear();
    unitList.clear();
    shopTableList.clear();
    toppingsList.clear();
    toppingGroupList.clear();
    expensesCategoryList.clear();
    expensesItemList.clear();
    expenseTransList.clear();
    paymentCatList.clear();
    salesCategoryList.clear();
    masterTableList.clear();
    shiftSalesList.clear();
    orderNotesList.clear();
  }
}
