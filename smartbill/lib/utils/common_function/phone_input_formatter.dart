import 'package:flutter/services.dart';

///Phone Number Validation For New Request
class PhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String formattedText = newValue.text;
    // Remove "+91" and spaces
    formattedText = formattedText.replaceAll('+91', '').replaceAll(' ', '');

    // Remove leading "0" if present
    if (formattedText.startsWith('0')) {
      formattedText = formattedText.substring(1);
    }
    // Get the range of characters deleted
    int deletedTextLength = oldValue.text.length - newValue.text.length;
    int cursorPosition = newValue.selection.baseOffset;

    // Adjust cursor position if it was affected by deletion
    if (deletedTextLength > 0 && cursorPosition > 0) {
      cursorPosition = cursorPosition - deletedTextLength + 1;
    }

    return TextEditingValue(
      text: formattedText,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}