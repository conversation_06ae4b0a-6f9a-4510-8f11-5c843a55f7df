// Singleton class to hold the ValueNotifier instance
import 'package:flutter/material.dart';

import 'sync_utility.dart';

class ValueNotifiers {
  // Private constructor to prevent instantiation
  ValueNotifiers._privateConstructor();

  // Singleton instance
  static final ValueNotifiers _instance = ValueNotifiers._privateConstructor();

  // Getter for accessing the singleton instance
  static ValueNotifiers get instance => _instance;

  // Value notifiers variables
  final ValueNotifier<SyncStatusInfo> syncStatusNotifier = ValueNotifier<SyncStatusInfo>(SyncStatusInfo(0, 0));
}
