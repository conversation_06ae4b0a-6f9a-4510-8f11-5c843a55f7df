// ignore_for_file: sdk_version_ui_as_code
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/item_layouts/widgets/main_page_alert_widget.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_function/sales_function.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/common_function/validation_utility.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import 'package:uuid/uuid.dart';
import '../../main.dart';
import '../../model/balance_model.dart';
import '../../model/customers.dart';
import '../../model/discounts.dart';
import '../../model/payment_type.dart';
import '../../model/printer_devices.dart';
import '../../model/reports.dart';
import '../../model/sales_payment_mapping.dart';
import '../../model/sales_type.dart';
import '../../model/transaction_model.dart';
import '../../screens/customers/customer_details_handler.dart';
import '../../screens/customers/customers_page.dart';
import '../../screens/salescategory/model/sales_category_model.dart';
import '../common_widgets/common_alert_dialogue/coupon_discount_dialog.dart';
import '../common_widgets/common_alert_dialogue/custom_discount_dialog.dart';
import '../common_widgets/custom_dropdown.dart';
import '../common_widgets/text_field_box_widget.dart';
import '../common_widgets/widgets.dart';
import '../constants/colors.dart';
import '../constants/strings_keys.dart';
import '../constants/styles.dart';
import '../dbutils/db_strings.dart';
import '../dbutils/sales_details_db_script.dart';
import '../dbutils/sales_trans_db_script.dart';
import '../dbutils/sales_trans_staging.dart';
import '../dbutils/salestype_db_script.dart';
import '../printer_utilities/sales_print.dart';
import 'discount_function.dart';
import 'list_class.dart';

enum SalesStatus { processbill, completeorder, completebill, advanceOrder }

class CommonSalesFunction {
  Customer? selectCustomer;
  PaymentType? selectPaymentType;
  StateSetter? saveAlertSetState;
  FnUtilities fnUtilities = new FnUtilities();

  /// Get Printer Info
  PrinterDevices? printerForBill;
  PrinterDevices? printerForKOT;
  Validator validator = new Validator();
  DiscountFunction discount = DiscountFunction();
  SalesFunctions salesFun = SalesFunctions();
  PrinterUtilities printerUtilities = new PrinterUtilities();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  SalestransStagingDBScript salestransStagingDBScript = SalestransStagingDBScript();
  SalesStatus? status;
  // List<Transactions> salesTransactionList = [];
  List<Customer> customerList = [];
  List<SalesPaymentMapping> paymentMappingList = [];
  CustomerDetailsHandler customerDetailsHandler = CustomerDetailsHandler();

  double returnAmount = 0.0;
  double total = 0.0;
  double paidAmount = 0.0;
  double width = 0.0;
  double height = 0.0;
  double balanceAmount = 0.0;

  int paymentMappingIndex = -1;

  bool isBigLayout = false;
  bool isLock = false;
  bool multiPrint = false;
  bool isDiscount = false;
  bool isPaymentType = false;
  bool isCustomer = false;
  bool isReturnAmount = false;
  bool successDialogOnSales = false;
  bool addTax = false;
  bool isLoading = false;
  bool isIGST = false;
  bool isCollectBillLock = false;
  bool isCollectBillPrintLock = false;
  bool isPaymentTypeIsEmpty = false;
  bool isPaymentAmountEmpty = false;
  bool isPaymentMappingListExist = false;
  bool isSalesBillPadEnable = false;
  bool customerNameMandatory = false;
  bool isComplementory = false;
  bool isStoreCustomerDetails = false;
  bool splitInvoiceByPayment = false;
  bool isPaymentDiable = false;

  ///Sales Category
  bool isSalesCategoryEnabled = false;
  bool isSalesCategorEmpty = false;
  List<SalesCategoryModel> salesCategoryList = [];
  List<DropDown> dropDownList = [];
  DropDown? selectedSalesCategory;
  String? discountType;
  String? customerId;
  String? customerName;

  final customerNameController = new TextEditingController();
  final givenAmountController = new TextEditingController();
  final mobileNoController = new TextEditingController();
  final paymentAmountController = new TextEditingController();
  bool isEnableAdvanceOrder = false;
  double totalRemainingAmount = 0.0;
  double totalPaidAmount = 0.0;
  List<PaymentType> paymentTypeList = [];
  List<SalesPaymentMapping> paymentList = [];
  AlertWidgets alertWidget = AlertWidgets();
  bool userHasManuallyChangedPaymentAmount = false;

  initSettings() async {
    getPrinterInfo();
    getSalesCategory();

    ///workspace settings
    multiPrint = fnUtilities.workSpaceSetValues(key_multiPrint) == "1";
    isDiscount = fnUtilities.workSpaceSetValues(key_enableDiscount) == "1";
    discountType = fnUtilities.workSpaceSetValues(key_discountType);
    isPaymentType = fnUtilities.workSpaceSetValues(key_paymentType) == "1";
    isCustomer = fnUtilities.workSpaceSetValues(key_isCustomer) == "1";
    addTax = fnUtilities.workSpaceSetValues(key_taxEnable) == "1";
    isReturnAmount = fnUtilities.workSpaceSetValues(key_returnAmountPrint) == "1";
    successDialogOnSales = fnUtilities.workSpaceSetValues(key_successDialogOnSales) == "1";
    isSalesBillPadEnable = fnUtilities.workSpaceSetValues(key_sales_billPad) == "1";
    customerNameMandatory = fnUtilities.workSpaceSetValues(key_isCustomerNameMandatory) == "1";
    isStoreCustomerDetails = fnUtilities.workSpaceSetValues(key_store_customerDetails) == "1";
    isSalesCategoryEnabled = fnUtilities.workSpaceSetValues(key_sales_category) == "1";
    splitInvoiceByPayment = fnUtilities.workSpaceSetValues(splitInvoiceByPaymentKey) == "1";
    isEnableAdvanceOrder = fnUtilities.workSpaceSetValues(key_advanceOrder) == "1";
  }

  commonDiscountFunction(Sales sales) async {
    List<Transactions> salesTransactionList = [];
    salesTransactionList = await assignTransactionList(sales.salesId);
    if (isDiscount) {
      for (Transactions d in salesTransactionList) {
        d.discountPrice = null;
        d.discountFormula = null;
      }
      if (discountType == "Product") {
        await discount.calculateDiscount(
          "A",
        );
      } else {
        Discounts? discountModel = await discount.salesCommonDiscount("A", saleID: null, saleNum: null);
        sales.discountFormula = discountModel?.formula;
        sales.discountPrice = discountModel?.discountAmount;
      }
    }
    await discount.callCalculate(null);
    findTotal(sales, salesTransactionList);
  }

  getPrinterInfo() {
    /// Get Printer Informations
    printerForBill = printerUtilities.getCurrentPrinter(PrinterFor.bill);
    printerForKOT = printerUtilities.getCurrentPrinter(PrinterFor.kot);
  }

  ///Get sales category list
  getSalesCategory() {
    if (ListUtility.salesCategoryList.isNotEmpty) {
      salesCategoryList.clear();
      salesCategoryList.addAll(ListUtility.salesCategoryList.where((element) => element.status != 2 && element.salesCatName != null));
      dropDownList = salesCategoryList.map((e) => DropDown(id: '${e.salesCategoryId}', name: e.salesCatName)).toList();
    }
  }

  //CALCULATE TOTAL AMOUNT
  findTotal(Sales sales, List<Transactions> salesTransactionList) {
    double totalAmount = 0.0;
    for (Transactions p in salesTransactionList) {
      if (isComplementory) {
        total += double.parse(p.amountWithoutGst.toString()).round();
      } else {
        totalAmount += double.parse(p.amount.toString()).round();
      }
    }
    if (!isComplementory) {
      if (sales.discountPrice != null) {
        totalRemainingAmount -= double.parse(sales.discountPrice.toString());
        total = totalAmount - double.parse(sales.discountPrice.toString());
        print(total);
      }
      sales.totalAmount = totalAmount.toString();
    } else {
      sales.totalAmount = total.toString();
    }
  }

  assignTransactionList(id) async {
    List<Transactions> salesTransactionList = [];
    var tempTransactionList = await salesFun.getAllTransaction(id);
    salesTransactionList.clear();
    salesTransactionList.addAll(tempTransactionList);
    return salesTransactionList;
  }

  ///check payment
  bool checkPaymentType() {
    bool isConditionOk;
    if (isPaymentType) {
      if (isSalesBillPadEnable || isEnableAdvanceOrder) {
        if (status == SalesStatus.processbill) {
          return true;
        } else if (isPaymentDiable && status == SalesStatus.advanceOrder) {
          return true;
        } else {
          isConditionOk = checkPaymentSection();
          return isConditionOk;
        }
      } else {
        if (isPaymentDiable && status == SalesStatus.advanceOrder) {
          return true;
        } else if (isPaymentType) {
          isConditionOk = checkPaymentSection();
          return isConditionOk;
        } else {
          return true;
        }
      }
    } else {
      return true;
    }
  }

  ///check sales category
  bool checkSalesCategory() {
    if (!isSalesCategoryEnabled ||
        selectedSalesCategory != null ||
        status == SalesStatus.completebill ||
        status == SalesStatus.completeorder ||
        salesCategoryList.isEmpty) {
      return true;
    } else if (isPaymentDiable || status == SalesStatus.advanceOrder) {
      return true;
    } else {
      return false;
    }
  }

  Future<bool> checkPayments(BuildContext context) async {
    if (paymentMappingList.isEmpty) {
      return true;
    } else {
      var checkPaidAmountValue;
      if (isEnableAdvanceOrder) {
        checkPaidAmountValue = paymentMappingList.fold(0.0, (total, element) => total + element.amount!) + totalPaidAmount;
      } else {
        checkPaidAmountValue = paymentMappingList.fold(0.0, (total, element) => total + element.amount!);
      }

      if (checkPaidAmountValue >= total) {
        return true;
      } else {
        bool? paymetAlertValue = await alertWidget.paymentAlertWidgets(context, "Incomplete payment. Do you want to continue?");
        return paymetAlertValue!;
      }
    }
  }

  ///check the sales
  Future<bool> checkBillStartCondition(BuildContext context) async {
    validator.validate();
    var isPayment = await checkPaymentType();
    var isSalesCategory = checkSalesCategory();
    var isPaymentValid = await checkPayments(context);
    if (isPayment && isSalesCategory && isPaymentValid) {
      return true;
    } else {
      if (!isSalesCategory) {
        saveAlertSetState!(() {
          isSalesCategorEmpty = true;
        });
      }
      if (!isPayment) {
        saveAlertSetState!(() {
          isPaymentTypeIsEmpty = selectPaymentType == null ? true : false;
          isPaymentAmountEmpty = paymentAmountController.text.isEmpty;
        });
      }
      return false;
    }
  }

  checkPaymentSection() {
    if (selectPaymentType != null && paymentAmountController.text.isNotEmpty) {
      saveAlertSetState?.call(() {
        isPaymentTypeIsEmpty = false;
        isPaymentAmountEmpty = false;
      });
      addPaymentMapping(selectPaymentType, paymentAmountController.text, index: paymentMappingIndex, paymentStatus: 0);
      return true;
    } else if (paymentMappingList.isNotEmpty) {
      return true;
    } else {
      if (selectPaymentType == null || paymentAmountController.text.isEmpty) {
        return false;
      } else {
        return false;
      }
    }
  }

  getSalesDetails(String salesId, String flag, {String? saleFlag}) async {
    List<Transactions> salesTransactionList = [];
    var tempSalesTransactionList = await salesFun.getSalesDetails(salesId, flag, salesFlag: saleFlag);
    salesTransactionList.clear();
    salesTransactionList.addAll(tempSalesTransactionList);
    return salesTransactionList;
  }

  Future<int> onPressPrint(BuildContext context, Sales model, String flag, {SalesStatus? salesStatus}) async {
    try {
      LoadingAlertWidget.onLoading(context);
      List<Transactions> salesTransactionList = [];
      status = salesStatus;
      int printResult = 0;
      if (status != SalesStatus.processbill && isSalesBillPadEnable) {
        salesTransactionList.clear();
        salesTransactionList = await getSalesDetails(model.salesId ?? "", flag, saleFlag: "NotCompleted");
      } else {
        salesTransactionList = await getSalesDetails(model.salesId ?? "", flag, saleFlag: "Completed");
      }
      getPrinterInfo();

      if (multiPrint == true) {
        printResult = await startReportPrint(context, model, flag, salesTransactionList);
        LoadingAlertWidget.onStopping();
        return printResult;
      } else {
        printResult = await startReportPrint(context, model, flag, salesTransactionList);
        LoadingAlertWidget.onStopping();
        return printResult;
      }
    } catch (e) {
      LoadingAlertWidget.onStopping();
      ErrorLogsFunction.insertErrorLogs(e, "onPressPrint - CommonSalesFunction");
      return -1;
    }
  }

  Future<int> startReportPrint(BuildContext context, Sales sales, String flag, List<Transactions> salesTransactionList) async {
    SalesPrint salesPrint = SalesPrint();
    List<Transactions> printSaleList = [];
    printSaleList.addAll(salesTransactionList);
    if (!await printerUtilities.checkPrinterConnection(context)) {
      return 0;
    } else {
      await printerUtilities.getPrinterDetails();
      if (paymentList.isNotEmpty) {
        paymentMappingList.addAll(paymentList);
      }
      if (flag == "Sale") {
        await onPressSave(sales, printSaleList);
      }

      if (flag == "Sale") {
        if (printerForBill?.printerFormat == "Default") {
          if (printerForBill?.paperSize == 58) {
            await salesPrint.salesPrint(sales, "SO", printSaleList, paymentMappingList,
                salesStatus: status, isComplementory: isComplementory, isAdvanceOrder: isEnableAdvanceOrder);
          } else {
            await salesPrint.salesPrint80MM(sales, "SO", printSaleList, paymentMappingList,
                salesStatus: status, isComplementory: isComplementory, isAdvanceOrder: isEnableAdvanceOrder);
          }
        } else {
          if (printerForBill?.paperSize == 58) {
            await salesPrint.salesPrintTvs(sales, "SO", printSaleList, paymentMappingList,
                salesStatus: status, isComplementory: isComplementory, isAdvanceOrder: isEnableAdvanceOrder);
          } else {
            await salesPrint.salesPrintTvs80MM(sales, "SO", printSaleList, paymentMappingList,
                salesStatus: status, isComplementory: isComplementory, isAdvanceOrder: isEnableAdvanceOrder);
          }
        }
      } else {
        if (printerForKOT?.printerFormat == "Default") {
          if (printerForKOT?.paperSize == 58) {
            await salesPrint.salesPrintKOT(sales, sales.tableName, printerForKOT, printSaleList);
          } else {
            await salesPrint.salesPrintKOT80MM(sales, sales.tableName ?? "", printerForKOT, printSaleList);
          }
        } else {
          if (printerForKOT?.paperSize == 58) {
            await salesPrint.salesPrintKOTTVS(sales, sales.tableName, printerForKOT, printSaleList);
          } else {
            await salesPrint.salesPrintKOTTvs80MM(sales, sales.tableName, printerForKOT, printSaleList);
          }
        }
      }
      //  if (!printerUtilities.isMultiPrint) {
      //   await Future.delayed(Duration(milliseconds: 500));
      //   await printerUtilities.disconnect();
      // }
      return 1;
    }
  }

  onPressSave(Sales model, List<Transactions> salesTransactionList) async {
    try {
      model.invoiceId = model.invoiceId;
      model.isActiveSale = null;
      model.sync = 0;
      model.date = DateTime.now().toString();
      model.paymentType = null;
      model.customerName = isCustomer
          ? customerNameController.text != ""
              ? customerNameController.text
              : customerName
          : model.customerName;
      model.customerID = selectCustomer?.customerID ?? customerId;
      model.paymentName = selectPaymentType?.paymentName;
      model.returnAmount = returnAmount;
      model.givenAmount = givenAmountController.text != "" ? givenAmountController.text : null;
      model.mobileNo = mobileNoController.text != "" ? mobileNoController.text : null;
      model.isIGST = isIGST;
      model.salesStatus = (status == SalesStatus.processbill && isSalesBillPadEnable) ? 2 : 1;
      model.rowStatus = (status == SalesStatus.processbill && isSalesBillPadEnable) ? model.rowStatus : (isSalesBillPadEnable ? 1 : model.rowStatus);
      model.paymentCategoryID = salesFun.setPaymentCategoryId(paymentMappingList, splitInvoiceByPayment);
      if (selectedSalesCategory != null) {
        model.salesCategoryId = isSalesCategoryEnabled ? selectedSalesCategory?.id : null;
      }
      await salesDetailsDBScript.updateSalesActiveTable(model);
      if (status == SalesStatus.completeorder || status == SalesStatus.completebill || status == SalesStatus.advanceOrder) {
        await salesFun.salesPaymentMappingMethod(paymentMappingList, saleId: model.salesId);
      }
      if (isCustomer && model.customerID != null) {
        await insertSalesTypeFunction(model);
      }
      if (status == SalesStatus.processbill || status == SalesStatus.completeorder || status == SalesStatus.advanceOrder) {
        await saveTransactions(model.salesId, salesTransactionList);
      }

      selectPaymentType = null;
      mobileNoController.clear();
      selectCustomer = null;
      return 1;
    } on Exception catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "onPressSave - CommonSalesFunction");
      return 0;
    }
  }

  insertSalesTypeFunction(Sales reports) async {
    SalesTypeDBScript salesTypeDB = SalesTypeDBScript();
    var uuid = new Uuid();
    SalesType model = SalesType();
    model.salesTypeID = uuid.v4().toString();
    model.salesID = reports.salesId;
    model.customerID = reports.customerID;
    model.createdDate = reports.date;
    model.salesType = "debit";
    model.syncStatus = 0;
    model.rowStatus = 0;
    model.workspaceID = MyApp.activeWorkspace.workspaceId;
    model.amount = reports.totalAmount;
    await salesTypeDB.insertSalesType(model);
    Balance balance = Balance();
    balance.customerID = reports.customerID;
    balance.balance = reports.totalAmount;
    await fnUtilities.insertOrUpdateBalance(balance, "debit");
    if (givenAmountController.text != "0" && givenAmountController.text != "") {
      model.salesTypeID = uuid.v4().toString();
      model.salesType = "credit";
      model.paymentTypeID = null;
      model.amount = givenAmountController.text;
      await salesTypeDB.insertSalesType(model);
      balance.customerID = reports.customerID;
      balance.balance = reports.totalAmount;
      await fnUtilities.insertOrUpdateBalance(balance, "credit");
    }
  }

  saveTransactions(id, List<Transactions> salesTransactionList) async {
    for (Transactions transaction in salesTransactionList) {
      transaction.isIGST = isIGST;
      transaction.sync = 0;
      await upsertSalesTransaction(transaction);
      await salestransStagingDBScript.deleteSalesTransactionStaging(transaction.transactId);
    }
  }

  ///The checkSalesTransactionExist method to check the transaction id exist or not
  ///In the salestransaction table. If exist it to be update the sync and row status.
  ///Or else to insert the new transaction in salestransaction table.
  upsertSalesTransaction(Transactions salesTransactionModel) async {
    SyncUtility syncUtility = new SyncUtility();
    if (await syncUtility.checkValueExists(db_salesTransactions, cName: db_salesTrans_transactId, cValue: salesTransactionModel.transactId) > 0) {
      salesTransactionModel.rowStatus = 1;
      await salesTransDBScript.updateTransactionsTable(salesTransactionModel);
    } else {
      await salesTransDBScript.insertSalesTransactions(salesTransactionModel);
    }
  }

  customDiscountDialog(BuildContext context1, Sales sales, List<Transactions> salesTransactionList) async {
    String selectedDiscountOn = "Percentage";
    bool isDiscountEmpty = false;
    bool isDiscountLock = false;
    final discountController = new TextEditingController();
    return showDialog(
        barrierDismissible: true,
        context: context1,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return CustomDiscountDialog(
              selectedDiscountOn: selectedDiscountOn,
              isDiscountEmpty: isDiscountEmpty,
              discountController: discountController,
              onPressItem: (value, selectedDiscount) async {
                if (!isDiscountLock) {
                  isDiscountLock = true;
                  if (selectedDiscount != null) {
                    if (discountController.text != "") {
                      setState(() {
                        isDiscountEmpty = false;
                      });
                      double tAmount = 0.0;
                      for (Transactions d in salesTransactionList) {
                        tAmount += double.parse(d.actualPrice.toString());
                        d.discountPrice = null;
                        d.discountFormula = null;
                      }
                      String discountPrice;
                      if (selectedDiscountOn == "Percentage") {
                        discountPrice = (((tAmount * double.parse(discountController.text)) / 100)).toString();
                      } else {
                        discountPrice = discountController.text;
                      }
                      sales.discountFormula = "Manual_Discount";
                      sales.discountPrice = discountPrice;
                      Navigator.pop(context1);
                      await discount.callCalculate(null);
                    } else {
                      setState(() {
                        isDiscountEmpty = true;
                      });
                    }
                  }
                  isDiscountLock = false;
                }
              },
            );
          });
        });
  }

  couponDialog(BuildContext context1, Sales sales, {String? discountType}) async {
    List<Discounts> discountList = await discount.checkValidDateDiscount("C", list: true);
    bool isDialogLock = false;
    return showDialog(
        barrierDismissible: true,
        context: context1,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context, setState) {
            return CouponDiscountDialog(
              discountList: discountList,
              discountType: discountType,
              onPressItem: (value) async {
                if (!isDialogLock) {
                  isDialogLock = true;
                  for (Transactions d in ListUtility.saleList) {
                    d.discountPrice = null;
                    d.discountFormula = null;
                  }
                  if (discountType == "Product") {
                    await discount.calculateDiscount("C", discount: value);
                  } else {
                    Discounts discountModel =
                        await discount.salesCommonDiscount("C", discount: value, saleID: sales.salesId, saleNum: sales.isActiveSale);
                    sales.discountFormula = discountModel.formula;
                    sales.discountPrice = discountModel.discountAmount;
                  }
                  Navigator.pop(context1);
                  await discount.callCalculate(null);
                  isDialogLock = false;
                }
              },
            );
          });
        });
  }

  double remainingAmount = 0.0;

  //Payment Type DIALOG
  Future<int> saveDialog(BuildContext context, Sales obj, String flag, bool isBigLayout,
      {SalesStatus? saleStatus, List<SalesPaymentMapping>? paymentMapList}) async {
    await initSettings();
    // log(paymentList);
    List<Transactions> salesTransactionList = [];
    paymentTypeList.clear();
    paymentTypeList.addAll(ListUtility.paymentTypeList);
    paymentList.clear();
    paymentList.addAll(paymentMapList ?? []);
    width = MediaQuery.of(context).size.width;
    status = saleStatus;
    isComplementory = false;
    if (status == SalesStatus.processbill || status == SalesStatus.completeorder) {
      salesTransactionList = await assignTransactionList(obj.salesId);
      ListUtility.saleList = salesTransactionList;
      await commonDiscountFunction(obj);
    } else {
      salesTransactionList = await getSalesDetails(obj.salesId ?? "", "Sale", saleFlag: "NotCompleted");
    }
    bool isSaveDialogLock = false;
    try {
      selectCustomer = customerList.firstWhereOrNull((element) => element.customerID == obj.customerID);
    } catch (ex) {
      selectCustomer = null;
    }
    returnAmount = 0.0;
    paidAmount = 0.0;
    customerNameController.text = obj.customerName != null ? obj.customerName.toString() : "";
    mobileNoController.text = obj.mobileNo != null ? (obj.mobileNo ?? "") : "";
    givenAmountController.clear();
    isPaymentTypeIsEmpty = false;
    isPaymentAmountEmpty = false;
    isSalesCategorEmpty = false;
    balanceAmount = 0.0;
    isPaymentMappingListExist = false;
    paymentMappingList.clear();
    paymentAmountController.clear();
    selectPaymentType = null;
    selectedSalesCategory = null;
    isIGST = false;
    int printResult = 0;
    total = double.parse(obj.totalAmount.toString());
    totalRemainingAmount = 0.0;
    totalPaidAmount = 0.0;
    if (status == SalesStatus.advanceOrder) {
      paymentList.forEach((element) {
        totalRemainingAmount += element.amount ?? 0;
      });
      totalPaidAmount = totalRemainingAmount;
      totalRemainingAmount = total - totalRemainingAmount;
      totalPaidAmount += paidAmount;
    }
    paymentAmountController.text = total.round().toString();
    isPaymentDiable = totalPaidAmount > total || totalPaidAmount >= total;
    userHasManuallyChangedPaymentAmount = false;
    return showModalBottomSheet(
        isScrollControlled: true,
        isDismissible: true,
        context: context,
        backgroundColor: Colors.transparent,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
        builder: (context) {
          return StatefulBuilder(builder: (context, setState) {
            saveAlertSetState = setState;
            return Padding(
              padding: EdgeInsets.only(
                  left: width > tabletWidth ? width * 0.3 : 0.0,
                  right: width > tabletWidth ? width * 0.3 : 0.0,
                  top: 8.0,
                  bottom: MediaQuery.of(context).viewInsets.bottom),
              child: Container(
                padding: EdgeInsets.all(12.0),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(16.0),
                    topRight: Radius.circular(16.0),
                  ),
                ),
                child: SingleChildScrollView(
                    child: Form(
                  key: validator.formkey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisSize: MainAxisSize.max,
                    children: [
                      if ((status == SalesStatus.processbill && isSalesBillPadEnable && status != SalesStatus.advanceOrder) ||
                          status == SalesStatus.completeorder) ...[
                        isDiscount
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Container(
                                    width: width / 1.2,
                                    alignment: Alignment.bottomLeft,
                                    child: Text("Discount: "),
                                  ),
                                  SizedBox(
                                    height: 3,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      if (PermissionFunctions.checkPermission(117, viewToast: false)) ...[
                                        Expanded(
                                          child: GestureDetector(
                                            onTap: () async {
                                              var isLock = false;
                                              if (!isLock) {
                                                isLock = true;
                                                await customDiscountDialog(context, obj, salesTransactionList);
                                                saveAlertSetState?.call(() {
                                                  findTotal(obj, salesTransactionList);
                                                });
                                                isLock = false;
                                              }
                                            },
                                            child: Container(
                                              alignment: Alignment.center,
                                              height: 40,
                                              child: text("Manual", textColor: secondaryTextColor, isCentered: true),
                                              decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                            ),
                                          ),
                                        ),
                                      ],
                                      SizedBox(
                                        width: 10.0,
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () async {
                                            var isLock = false;
                                            if (!isLock) {
                                              isLock = true;
                                              await couponDialog(context, obj);
                                              setState(() {
                                                findTotal(obj, salesTransactionList);
                                              });
                                              isLock = false;
                                            }
                                          },
                                          child: Container(
                                            alignment: Alignment.center,
                                            height: 40,
                                            child: text("Coupon", textColor: secondaryTextColor, isCentered: true),
                                            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                  SizedBox(
                                    height: 5,
                                  ),
                                  Row(
                                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        "Discount: ",
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                      Text(
                                        "Rs.${obj.discountPrice != null ? double.parse(obj.discountPrice.toString()).toStringAsFixed(2) : 0}",
                                        style: TextStyle(fontWeight: FontWeight.bold),
                                      ),
                                    ],
                                  ),
                                ],
                              )
                            : Container(),
                        SizedBox(
                          height: 10,
                        ),
                        if (isCustomer) ...[
                          Container(
                            alignment: Alignment.centerLeft,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Choose customer: ",
                                ),
                                SizedBox(
                                  height: 3,
                                ),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () async {
                                          var isLock = false;
                                          if (!isLock) {
                                            isLock = true;
                                            setState(() {
                                              if (selectCustomer == null) {
                                                customerNameController.text = obj.customerName != null ? (obj.customerName ?? "") : '';
                                                mobileNoController.text = obj.mobileNo != null ? (obj.mobileNo ?? "") : "";
                                              } else {
                                                selectCustomer = null;
                                                customerNameController.clear();
                                                mobileNoController.clear();
                                              }
                                            });
                                            isLock = false;
                                          }
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          height: 40,
                                          child: text("Guest", textColor: secondaryTextColor, isCentered: true),
                                          decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                        ),
                                      ),
                                    ),
                                    SizedBox(
                                      width: 10.0,
                                    ),
                                    Expanded(
                                      child: GestureDetector(
                                        onTap: () async {
                                          var isLock = false;
                                          if (!isLock) {
                                            isLock = true;
                                            Navigator.push(
                                                context,
                                                MaterialPageRoute(
                                                    builder: (context) => CustomerPage(
                                                          pageType: "Sale",
                                                        ))).then((value) async {
                                              setState(() {
                                                try {
                                                  if (value == null) {
                                                    selectCustomer = null;
                                                    customerNameController.clear();
                                                    mobileNoController.clear();
                                                  } else {
                                                    selectCustomer = value;
                                                    customerNameController.text =
                                                        (selectCustomer?.firstName ?? "") + " " + (selectCustomer?.lastName ?? "");
                                                    mobileNoController.text = selectCustomer?.mobileNo ?? "";
                                                  }
                                                } catch (e) {
                                                  selectCustomer = null;
                                                }
                                              });
                                            });
                                            isLock = false;
                                          }
                                        },
                                        child: Container(
                                          alignment: Alignment.center,
                                          height: 40,
                                          child: text("Customers", textColor: secondaryTextColor, isCentered: true),
                                          decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  child: EditText(
                                    mController: customerNameController,
                                    inputType: TextInputType.text,
                                    isPassword: false,
                                    text: "Customer Name",
                                    readOnly: selectCustomer == null ? false : true,
                                    validator: customerNameMandatory ? validator.validateName : null,
                                  ),
                                ),
                                SizedBox(
                                  height: 10,
                                ),
                                Container(
                                  child: EditText(
                                    mController: mobileNoController,
                                    isPassword: false,
                                    text: "Mobile No",
                                    inputType: TextInputType.phone,
                                    inputFormate: [
                                      FilteringTextInputFormatter.digitsOnly,
                                    ],
                                    maxLength: 10,
                                    validator: customerNameMandatory ? validator.validateMobile : null,
                                    readOnly: selectCustomer == null ? false : true,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                        if (isSalesCategoryEnabled && salesCategoryList.isNotEmpty) ...[
                          Padding(padding: EdgeInsets.symmetric(vertical: 10.0), child: salesCategoryWidget(context)),
                          SizedBox(
                            height: 10.0,
                          ),
                          if (isSalesCategorEmpty) ...[
                            Container(
                              alignment: Alignment.centerLeft,
                              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 8),
                              child: const Text(
                                "This field is required",
                                style: TextStyle(color: Colors.red, fontSize: 11.5),
                              ),
                            ),
                            SizedBox(
                              height: 10.0,
                            )
                          ]
                        ],
                        if (isSalesBillPadEnable) ...[
                          SizedBox()
                        ] else ...[
                          paymentWidget(
                            context,
                            flag,
                            obj,
                            salesTransactionList,
                            paymentList: paymentList,
                            saleStatus: saleStatus,
                          )
                        ],
                        if (addTax && !isComplementory) ...[
                          CheckboxListTile(
                              dense: false,
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: EdgeInsets.zero,
                              value: isIGST,
                              title: text("Other state tax",
                                  textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isLongText: true),
                              onChanged: (bool? newValue) {
                                var isLock = false;
                                if (!isLock) {
                                  isLock = true;
                                  saveAlertSetState?.call(() {
                                    isIGST = newValue!;
                                  });
                                  isLock = false;
                                }
                              })
                        ] else ...[
                          SizedBox()
                        ]
                      ] else ...[
                        paymentWidget(
                          context,
                          flag,
                          obj,
                          salesTransactionList,
                          paymentList: paymentList,
                          saleStatus: saleStatus,
                        )
                      ],
                      SizedBox(
                        height: 5,
                      ),
                      flag != "save"
                          ? SizedBox(
                              height: 20,
                            )
                          : Container(),
                      Text(flag == "save" ? "Click Ok to collect bill" : "Click Ok to collect bill and print"),
                      SizedBox(
                        height: 20,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          Expanded(
                            child: GestureDetector(
                              onTap: () async {
                                if (!isSaveDialogLock) {
                                  isSaveDialogLock = true;

                                  if (await checkBillStartCondition(context)) {
                                    if (validator.validate()) {
                                      if (isStoreCustomerDetails && selectCustomer == null) {
                                        var customerMapValue =
                                            await customerDetailsHandler.addCustomerDetails(customerNameController.text, mobileNoController.text);
                                        if (customerMapValue.isNotEmpty) {
                                          customerId = customerMapValue['customerId'];
                                          customerName = customerMapValue['customerName'];
                                          mobileNoController.text = customerMapValue['customerMobile'];
                                        }
                                      }
                                      if (flag == "save") {
                                        printResult = await onPressSave(obj, salesTransactionList);
                                        if (printResult == 1) {
                                          Navigator.pop(context);
                                        }
                                        isSaveDialogLock = false;
                                        return Future.value(printResult);
                                      } else {
                                        printResult = await onPressPrint(context, obj, "Sale", salesStatus: saleStatus);
                                        //printerUtilities.disconnect();
                                        if (printResult == 1) {
                                          Navigator.pop(context);
                                        }
                                        isSaveDialogLock = false;
                                        return Future.value(printResult);
                                      }
                                    }
                                  } else {
                                    validator.validate();
                                  }

                                  isSaveDialogLock = false;
                                }
                              },
                              child: Container(
                                alignment: Alignment.center,
                                height: 45,
                                child: text(flag == "save" ? "Ok" : "Ok & Print", textColor: secondaryTextColor, isCentered: true),
                                decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
                              ),
                            ),
                          ),
                        ],
                      ),
                      SizedBox(
                        height: 15,
                      )
                    ],
                  ),
                )),
              ),
            );
          });
        }).then((val) {
      saveAlertSetState?.call(() {
        findTotal(obj, salesTransactionList);
      });
      return printResult;
    });
  }

  paymentInputFieldWidget(BuildContext context, bool isBigLayout, double width,
      {Sales? salesModel, List<Transactions>? salesTransctList, double? remainingAmount}) {
    isPaymentMappingListExist = false;
    if (status == SalesStatus.advanceOrder && remainingAmount != null && !userHasManuallyChangedPaymentAmount) {
      remainingAmount = total - remainingAmount;
      paymentAmountController.text = remainingAmount >= 0 ? fnUtilities.checkQtyDoubleValue(remainingAmount.round().toString()) : "0.0";
    }

    return Column(
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: <Widget>[
            Expanded(
              child: Container(
                child: InputDecorator(
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.symmetric(horizontal: 3.0, vertical: 1.0),
                    labelText: 'Payment Type',
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                  ),
                  child: paymentTypeList.length != 0
                      ? DropdownButtonHideUnderline(
                          child: Container(
                            height: 40,
                            width: isBigLayout ? width * 0.20 : width * 0.35,
                            child: DropdownButton<PaymentType>(
                              hint: Text(
                                "Payment Type",
                                style: TextStyle(fontSize: 14),
                              ),
                              value: selectPaymentType,
                              items: paymentTypeList.map((PaymentType value) {
                                return DropdownMenuItem<PaymentType>(
                                  value: value,
                                  child: Text(value.paymentName ?? ""),
                                );
                              }).toList(),
                              onChanged: (PaymentType? val) async {
                                if (val?.paymentCategoryID == 4 && paymentMappingList.length > 0) {
                                  showToast("Remove other payments");
                                } else {
                                  saveAlertSetState!(() {
                                    if (val?.paymentCategoryID == 4) {
                                      isComplementory = true;
                                      selectPaymentType = val;
                                      total = 0.0;
                                      findTotal(salesModel!, salesTransctList!);
                                      paymentAmountController.text = total.round().toString();
                                    } else {
                                      saveAlertSetState!(() {
                                        isComplementory = false;
                                        isPaymentTypeIsEmpty = false;
                                        FocusScope.of(context).requestFocus(FocusNode());
                                        selectPaymentType = val;
                                        findTotal(salesModel!, salesTransctList!);
                                        total = double.parse(salesModel.totalAmount.toString());
                                        if (status == SalesStatus.advanceOrder) {
                                          remainingAmount = total - remainingAmount!;
                                          if (!userHasManuallyChangedPaymentAmount) {
                                            paymentAmountController.text = remainingAmount.toString();
                                          }
                                        } else {
                                          paymentAmountController.text = total.round().toString();
                                        }
                                      });
                                    }
                                  });
                                }
                              },
                            ),
                          ),
                        )
                      : Container(),
                ),
              ),
            ),
            if (!isComplementory) ...[
              SizedBox(
                width: 5,
              ),
              Expanded(
                child: TextFieldBoxWidget(
                  text: "Rs",
                  isPassword: false,
                  mController: paymentAmountController,
                  onChange: (value) {
                    userHasManuallyChangedPaymentAmount = true;
                  },
                  // validator: validator.validateTextField,
                  inputFormate: [
                    FilteringTextInputFormatter.allow(RegExp(r'[0-9]')),
                  ],
                  inputType: TextInputType.number,
                  inputAction: TextInputAction.done,
                ),
              ),
              SizedBox(
                width: 5,
              ),
              InkWell(
                onTap: () {
                  checkValidationForPayment();
                },
                child: SvgPicture.asset(
                  addIconSVG,
                  color: black,
                ),
              )
            ],
          ],
        ),
        SizedBox(
          height: 10,
        ),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            isPaymentTypeIsEmpty
                ? Container(
                    width: isBigLayout ? width * 0.20 : width * 0.33,
                    alignment: Alignment.centerLeft,
                    padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                    child: const Text(
                      "This field is required",
                      style: TextStyle(color: Colors.red, fontSize: 11.5),
                    ),
                  )
                : SizedBox(
                    width: isBigLayout ? width * 0.20 : width * 0.33,
                  ),
            isPaymentAmountEmpty
                ? Expanded(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 48),
                      child: const Text(
                        "This field is required",
                        style: TextStyle(color: Colors.red, fontSize: 11.5),
                      ),
                    ),
                  )
                : SizedBox.shrink(),
          ],
        ),
        isPaymentMappingListExist
            ? Container(
                alignment: Alignment.centerLeft,
                padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 8),
                child: const Text(
                  "Atleast add one payment type using the add button",
                  style: TextStyle(color: Colors.red, fontSize: 11.5),
                ),
              )
            : Container(),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (paymentMappingList.length != 0) ...[
              SizedBox(
                width: isBigLayout ? width * 0.40 : width * 0.633,

                ///   units list widget
                child: paymentListWidget(context, width, isBigLayout),
              )
            ] else ...[
              SizedBox.shrink(),
            ]
          ],
        ),
      ],
    );
  }

  // payment list
  Widget paymentListWidget(BuildContext context, double width, bool isBigLayout) {
    return SingleChildScrollView(
      child: ListView.builder(
          itemCount: paymentMappingList.length,
          scrollDirection: Axis.vertical,
          physics: ScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (BuildContext context, int index) {
            var item = paymentMappingList[index];
            return paymentMappingList[index].status != 2
                ? Container(
                    height: 40,
                    decoration: BoxDecoration(
                      border: Border(
                        bottom: BorderSide(
                          color: Colors.grey,
                          width: 0.5,
                        ),
                      ),
                    ),
                    child: Row(
                      children: [
                        SizedBox(
                          child: Row(
                            children: [
                              Container(
                                // color: blueViolet,
                                // width: width * 0.1571,
                                width: width > tabletWidth ? width * 0.15 : width * 0.22,
                                child: text(item.paymentName ?? "",
                                    textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                              ),
                              Container(
                                width: width > tabletWidth ? width * 0.16 : width * 0.22,
                                child: text(fnUtilities.checkQtyDoubleValue(item.amount != null ? item.amount.toString() : "0"),
                                    textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                              ),
                              Row(
                                children: [
                                  InkWell(
                                      onTap: () {
                                        var isLock = false;
                                        if (!isLock) {
                                          isLock = true;

                                          saveAlertSetState?.call(() {
                                            for (PaymentType model in paymentTypeList) {
                                              if (model.paymentTypeID == item.paymentId) {
                                                paymentAmountController.text = paymentMappingList[index].amount?.toStringAsFixed(0) ?? '';
                                                selectPaymentType = model;
                                                paymentMappingIndex = index;
                                              }
                                            }
                                          });
                                          FocusScope.of(context).requestFocus(FocusNode());

                                          isLock = false;
                                        }
                                      },
                                      child: Icon(Icons.edit)),
                                  SizedBox(
                                    width: isBigLayout ? width * 0.05 : width * 0.05,
                                  ),
                                  InkWell(
                                      onTap: () {
                                        var isLock = false;
                                        if (!isLock) {
                                          isLock = true;
                                          saveAlertSetState?.call(() {
                                            paymentMappingIndex = index;
                                            addPaymentMapping(
                                              selectPaymentType,
                                              paymentAmountController.text,
                                              index: paymentMappingIndex,
                                              paymentStatus: 2,
                                            );

                                            FocusScope.of(context).requestFocus(FocusNode());
                                          });

                                          isLock = false;
                                        }
                                      },
                                      child: Icon(
                                        Icons.delete,
                                        color: redColor,
                                      )),
                                ],
                              )
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : SizedBox.shrink();
          }),
    );
  }

  void addPaymentMapping(PaymentType? paymentType, String paymentAmount, {paymentStatus = 0, index = 0}) async {
    List<SalesPaymentMapping> temPaymentMappingList = [];
    var deletedPaymentAmt = 0.0;
    if (status == SalesStatus.advanceOrder && paymentStatus == 2) {
      deletedPaymentAmt = paymentMappingList[index].amount ?? 0.0;
      paidAmount = paidAmount - deletedPaymentAmt;
      totalPaidAmount = totalPaidAmount - deletedPaymentAmt;
      totalRemainingAmount = total - totalPaidAmount;
      returnAmount = totalPaidAmount - total;
    } else if (paymentStatus == 2) {
      deletedPaymentAmt = paymentMappingList[index].amount ?? 0.0;
      paidAmount = paidAmount - deletedPaymentAmt;
      balanceAmount = total - paidAmount;
    }
    temPaymentMappingList = await salesFun.salesPaymentMapping(paymentType, paymentMappingList, paymentStatus, index, paymentAmount);
    paymentMappingList = temPaymentMappingList;
    paymentMappingIndex = -1;
    saveAlertSetState?.call(() {
      isPaymentMappingListExist = false;
      isPaymentAmountEmpty = false;
      isPaymentTypeIsEmpty = false;
      selectPaymentType = null;
      paymentAmountController.clear();
    });
    if (paymentMappingList.isEmpty) {
      saveAlertSetState?.call(() {
        returnAmount = 0.0;
        balanceAmount = 0.0;
        if (status == SalesStatus.advanceOrder && paymentList.isNotEmpty) {
          totalPaidAmount = 0.0;
          paidAmount = 0.0;
          totalRemainingAmount = 0.0;

          paymentList.forEach((element) {
            totalRemainingAmount += element.amount ?? 0;
          });
          totalPaidAmount = totalRemainingAmount;
          totalRemainingAmount = total - totalRemainingAmount;
          totalPaidAmount += paidAmount;
        }
      });
    }
    if (paymentStatus != 2) {
      calculateTotalAmount();
    }
  }

  calculateTotalAmount({
    List<SalesPaymentMapping>? paymentList,
  }) {
    double totalPayedAmount = 0;
    double lastPaidAmount = 0;
    paidAmount = 0;
    if (paymentMappingList.isNotEmpty) {
      for (int i = 0; i < paymentMappingList.length; i++) {
        totalPayedAmount = totalPayedAmount + (paymentMappingList[i].amount ?? 0.0);
        if (i == paymentMappingList.length - 1) {
          lastPaidAmount = paymentMappingList[i].amount ?? 0.0;
        }
      }
      saveAlertSetState?.call(() {
        returnAmount = totalPayedAmount - total;
        paidAmount = totalPayedAmount;
        balanceAmount = total - totalPayedAmount;
        if (status == SalesStatus.advanceOrder) {
          totalPaidAmount = lastPaidAmount + totalPaidAmount;

          totalRemainingAmount = total - totalPaidAmount;
          returnAmount = totalPaidAmount - total;
        }
      });
    }

    //FocusScope.of(context).requestFocus(FocusNode());
  }

  ///Check Validation for payment
  checkValidationForPayment() {
    if (paymentAmountController.text.isNotEmpty || selectPaymentType != null) {
      if (selectPaymentType != null) {
        if (paymentAmountController.text.isNotEmpty && (paymentAmountController.text.toDouble() > 0)) {
          saveAlertSetState!(() {
            isPaymentTypeIsEmpty = false;
            isPaymentAmountEmpty = false;
            if (paymentMappingIndex == -1) {
              addPaymentMapping(selectPaymentType!, paymentAmountController.text, index: paymentMappingIndex, paymentStatus: 0);
            } else {
              addPaymentMapping(selectPaymentType!, paymentAmountController.text, index: paymentMappingIndex, paymentStatus: 1);
            }
            //FocusScope.of(context).requestFocus(FocusNode());
          });
        } else {
          saveAlertSetState!(() {
            isPaymentAmountEmpty = true;
            isPaymentTypeIsEmpty = false;
          });
        }
      } else {
        saveAlertSetState!(() {
          isPaymentTypeIsEmpty = true;
          isPaymentAmountEmpty = false;
        });
      }
    } else {
      saveAlertSetState!(() {
        isPaymentTypeIsEmpty = true;
        isPaymentAmountEmpty = true;
        //FocusScope.of(context).requestFocus(FocusNode());
      });
    }
  }

  paymentWidget(
    BuildContext context,
    String flag,
    Sales salesModel,
    List<Transactions> salesTransctList, {
    List<SalesPaymentMapping>? paymentList,
    SalesStatus? saleStatus,
  }) {
    return Column(
      children: [
        if (isPaymentType) ...[
          if (!isPaymentDiable) ...[
            paymentInputFieldWidget(context, isBigLayout, width,
                salesModel: salesModel, salesTransctList: salesTransctList, remainingAmount: totalPaidAmount)
          ]
        ],
        SizedBox(
          height: isPaymentType ? 20 : 0,
        ),
        if (paymentMappingList.isNotEmpty) ...[
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 5.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Paid Amount: ",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  "Rs.${fnUtilities.checkQtyDoubleValue(paidAmount.toStringAsFixed(2))}",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ),
        ],
        if (status == SalesStatus.advanceOrder) ...{
          if (totalRemainingAmount >= 0) ...[
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Pending Amount",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
                Text(
                  "Rs.${fnUtilities.checkQtyDoubleValue(totalRemainingAmount.round().toStringAsFixed(2))}",
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
          ],
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Total Paid Amount",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                "Rs.${fnUtilities.checkQtyDoubleValue(totalPaidAmount.round().toStringAsFixed(2))}",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
          // if (salesModel.discountPrice != null)
          //   Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Text(
          //         "Discount",
          //         style: TextStyle(fontWeight: FontWeight.bold),
          //       ),
          //       Text(
          //         "Rs.${fnUtilities.checkQtyDoubleValue(salesModel.discountPrice ?? '')}",
          //         style: TextStyle(fontWeight: FontWeight.bold),
          //       ),
          //     ],
          //   ),
        },
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              "Net Total",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
            Text(
              "Rs.${fnUtilities.checkQtyDoubleValue(total.toStringAsFixed(2))}",
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ],
        ),
        if (balanceAmount > 0 && status == SalesStatus.completeorder) ...[
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Oustanding Balance: ",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              Text(
                "Rs.${balanceAmount.toStringAsFixed(2)}",
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ],
        isReturnAmount && flag != "save"
            ? Column(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  if (returnAmount >= 0) ...[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Return Amount: ",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                        Text(
                          "Rs.${fnUtilities.checkQtyDoubleValue(returnAmount.toStringAsFixed(2))}",
                          style: TextStyle(fontWeight: FontWeight.bold),
                        ),
                      ],
                    ),
                  ]
                ],
              )
            : SizedBox.shrink(),
      ],
    );
  }

  ///Sales category widget
  Widget salesCategoryWidget(BuildContext context) {
    return CommonDropDown(
      hintText: 'Select sales category',
      dropDownList: dropDownList,
      dropDown: selectedSalesCategory,
      isExpanded: true,
      isDense: true,
      canvasColor: Colors.white,
      onChanged: (DropDown? newValue) async {
        saveAlertSetState!(() {
          FocusScope.of(context).requestFocus(FocusNode());
          selectedSalesCategory = newValue;
          isSalesCategorEmpty = false;
        });
      },
    );
  }
}
