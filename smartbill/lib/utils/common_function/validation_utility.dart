import 'package:flutter/material.dart';

class Validator {
  final formkey = GlobalKey<FormState>();
  String? validateTextField(String? value) {
    if (value?.isEmpty ?? false)
      return 'This field is required';
    else
      return null;
  }

  String? validateName(String? value) {
    if (value!.isEmpty)
      return 'This field is required';
    else if (value.length < 2)
      return 'Name must be more than 1 character';
    else
      return null;
  }

  String? validateMobile(String? values) {
// Indian Mobile number are of 10 digit only
    String pattern = r'([0-9]{10}$)';
    RegExp regExp = new RegExp(pattern);
    if (values!.isEmpty)
      return 'This field is required';
    else if (values.length != 10)
      return 'Mobile number must be of 10 digit';
    else if (!regExp.hasMatch(values)) {
      return 'Enter valid mobile number';
    } else
      return null;
  }

  String? validateEmail(String? value) {
    Pattern pattern =
        r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
    RegExp regex = new RegExp(pattern.toString());
    if (value!.isEmpty)
      return 'This field is required';
    else if (!regex.hasMatch(value.trim()))
      return 'Enter valid email';
    else
      return null;
  }

  String? validateAddress(String value) {
    if (value.isEmpty)
      return 'This field is required';
    else
      return null;
  }

  String? validatePassword(String? value) {
    if (value!.isEmpty)
      return 'This field is required';
    else if (value.length < 4)
      return 'Password must be more than 4 charater';
    else
      return null;
  }

  String? validateZipCode(String? values) {
// Indian Mobile number are of 10 digit only
    if (values!.isEmpty)
      return 'This field is required';
    else if (values.length != 6)
      return 'Zip code must be of 6 digit';
    else
      return null;
  }

  String? validatePasswordMatch(String pass1, String pass2) {
    if (pass1 != pass2)
      return 'Password mismatch';
    else
      return null;
  }

  String? validateQuantity(String? value) {
    {
      if (value == "" || int.tryParse(value ?? "0")! <= 0) {
        return "Quantity must be greater than zero";
      } else {
        return null;
      }
    }
  }

  String? validateMinValue(String? value) {
    {
      if (int.tryParse(value ?? "0")! <= 0 && value!.isNotEmpty) {
        return "Min Limit must be greater than zero";
      } else {
        return null;
      }
    }
  }

  String? validateAmount(String? value) {
    {
      if (int.tryParse(value ?? "0")! <= 0 && value!.isNotEmpty) {
        return "Amount must be greater than zero";
      } else {
        return null;
      }
    }
  }

  String? validateMaxValue(String value, String value2) {
    {
      if (int.tryParse(value)! <= 0 && value.isNotEmpty) {
        return "Max must be greater than zero";
      } else if (int.tryParse(value)! <= int.tryParse(value2)!) {
        return "Max limit must be greater than min limit";
      } else {
        return null;
      }
    }
  }

  String? validateTime(String value) {
    // Regular expression to match time in 12-hour format (hh:mm am/pm)
    String pattern = r'^([1-9]|1[0-2]):([0-5][0-9])\s?(AM|PM|am|pm)?$';
    RegExp regex = RegExp(pattern);

    if (value.isEmpty) {
      return 'This field is required';
    } else if (!regex.hasMatch(value.trim())) {
      return 'Enter valid time (hh:mm am/pm)';
    } else {
      return null;
    }
  }

  bool validate() {
    if (formkey.currentState!.validate()) {
      return true;
    } else {
      return false;
    }
  }
}
