import 'dart:async';
import 'dart:developer';

import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:flutter/material.dart';

class AwesomeNotificationService {
  static StreamSubscription? notificationStreamSubscription1;
  static StreamSubscription? notificationStreamSubscription2;
  static bool isNotificationOnTap = false;
  final awesomeNotifications = AwesomeNotifications();


  static initAwesomeNotification() {
    AwesomeNotifications().initialize(null, [
      NotificationChannel(
          channelKey: "alert_channel",
          channelName: "Alert Channel",
          channelDescription: "Show Alert Notification",
          ledColor: Colors.white,
          importance: NotificationImportance.Max,
          channelShowBadge: true,
          locked: true,
          defaultRingtoneType: DefaultRingtoneType.Alarm),
      NotificationChannel(
          channelKey: "notification_channel",
          channelName: "Notification Channel",
          channelDescription: "Show Notification",
          ledColor: Colors.white,
          importance: NotificationImportance.Max,
          channelShowBadge: true,
          defaultRingtoneType: DefaultRingtoneType.Notification)
    ]);
  }

  static showAlertNotification(String title, String body, String payload) async {
    try {
      AwesomeNotifications().createNotification(
          content: NotificationContent(
            id: 01,
            channelKey: "alert_channel",
            title: title,
            body: body,
            category: NotificationCategory.Alarm,
            wakeUpScreen: true,
            fullScreenIntent: true,
            autoDismissible: true,
            criticalAlert: true,
          ),
          actionButtons: [
            NotificationActionButton(key: "ACCEPT", label: "Dismiss, I will check", autoDismissible: true),
          ]);
      notificationStreamSubscription1?.cancel();
      //notificationStreamSubscription1 = AwesomeNotifications().actionStream.listen((event) {
        // if (isNotificationOnTap) {
        //   if (event.buttonKeyPressed == "ACCEPT") {
        //     log("Alert Accepted");
        //   } else {
        //     log("Clicked on Notification");
        //   }
        // }
      //});
      isNotificationOnTap = true;
    } catch (e) {
      log(e.toString());
    }
  }

  static showNormalNotification(String title, String body, String payload) async {
    try {
      AwesomeNotifications().createNotification(
        content: NotificationContent(id: 02, channelKey: "notification_channel", title: title, body: body, wakeUpScreen: true),
      );
      notificationStreamSubscription2?.cancel();
      // notificationStreamSubscription2 = AwesomeNotifications().actionStream.listen((event) {
      //   if (isNotificationOnTap) {
      //     NotificationResponse notificationResponse =
      //         NotificationResponse(notificationResponseType: NotificationResponseType.selectedNotification, payload: payload);
      //     // LocalNotification.onSelectNotification(notificationResponse);
      //   }
      // });
    } catch (e) {
      log(e.toString());
    }
  }
}
