// ignore_for_file: unnecessary_null_comparison

import 'dart:convert';

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:open_filex/open_filex.dart';

class LocalNotification {
  static final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  //Initialization Settings for Android
  static const AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');

//Initialization Settings for iOS
  static const DarwinInitializationSettings initializationSettingsIOS = DarwinInitializationSettings(
    requestSoundPermission: false,
    requestBadgePermission: false,
    requestAlertPermission: false,
  );

//InitializationSettings for initializing settings for both platforms (Android & iOS)
  static const InitializationSettings initializationSettings = InitializationSettings(android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

  static initLocalNotification() async {
    await flutterLocalNotificationsPlugin.initialize(initializationSettings, onDidReceiveNotificationResponse: onSelectNotification);
  }

  static Future<void> onSelectNotification(NotificationResponse notification) async {
    // Handle the notification payload here
    String payload = notification.payload ?? '';
    if (payload != null) {
      try {
        Map<String, dynamic> payloadMap = jsonDecode(payload);
        String path = payloadMap["path"];
        switch (path) {
          // case '/reportsscreen':
          //   {
          //     routePageWithContext(path, arguments: payloadMap["params"]);
          //     break;
          //   }
          case 'filedownload':
            {
              var filelocation = payloadMap['filelocation'];
                await OpenFilex.open(filelocation);
              }
              break;
            
          default:
      
            break;
        }
      } catch (e) {
        print("Error parsing payload: $e");
      }
    }
  }

  static showNotification(RemoteNotification notification, var payload) async {
    try {
      var androidDetails = const AndroidNotificationDetails('id', 'channel', priority: Priority.high, importance: Importance.max, icon: "@mipmap/ic_launcher");
      var iOSDetails = const DarwinNotificationDetails();
      var notificationDetails = NotificationDetails(android: androidDetails, iOS: iOSDetails);

      await flutterLocalNotificationsPlugin.show(0, notification.title, notification.body, notificationDetails, payload: payload);
    } catch (e) {
      print(e);
    }
  }
}
