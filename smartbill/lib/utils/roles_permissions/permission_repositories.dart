
import 'package:smartbill/model/common_api_response.dart';
import 'package:smartbill/utils/roles_permissions/model/receiving/get_all_permisions.dart';

import '../../Repositories/helper/api_helper.dart';
import '../../main.dart';
import 'model/receiving/get_all_categories.dart';
import 'model/receiving/get_roles_model.dart';
import 'model/sending/add_customer_role.dart';
import 'model/sending/add_permission_custom_role.dart';


Future<ResponseBody> addCustomRole(AddCustomRole addCustomRole) async {
  ResponseBody responseModel = ResponseBody();
  Map<String, String>? queryParameters = {
    'workspaceID': MyApp.activeWorkspace.workspaceId ?? "",
  };
  final response = await apiHelper.commonPost("/api/v1/customroles/addcustomrole", queryParams: queryParameters, requestBody: addCustomRole.toJson());
  if (response != null) {
    responseModel = ResponseBody.fromJson(response);
  }
  return responseModel;
}

Future<GetAllCategories> getAllCategories() async {
  GetAllCategories responseModel = GetAllCategories();
  final response = await apiHelper.commonGet("/api/v1/customroles/getallcategories");
  if (response != null) {
    responseModel = GetAllCategories.fromJson(response);
  }
  return responseModel;
}

/// Get all categories by custom role id
Future<GetAllCategories> getAllCategoriesByIdApi(String workspaceId, int roleID) async {
  GetAllCategories responseModel = GetAllCategories();
  final response = await apiHelper.commonGet("/api/v1/customroles/getallcategoriesbyroleid?workspaceId=$workspaceId&RoleID=$roleID");
  if (response != null) {
    responseModel = GetAllCategories.fromJson(response);
  }
  return responseModel;
}

Future<GetRolesModel> getAllRolesApi() async {
  GetRolesModel responseModel = GetRolesModel();
  Map<String, String>? queryParameters = {
    'workspaceID': MyApp.activeWorkspace.workspaceId ?? "",
  };
  final response = await apiHelper.commonGet("/api/v1/customroles/getallroles", queryParams: queryParameters);
  if (response != null) {
    responseModel = GetRolesModel.fromJson(response);
  }
  return responseModel;
}

Future<AddPermissionsReponseModel> addPermissionsCustomRole(int roleId, List<AddPermissionsForCustomRole> customPermissionList) async {
  AddPermissionsReponseModel responseModel = AddPermissionsReponseModel();
  List<Map<String, dynamic>> customList = customPermissionList.map((model) => model.toJson()).toList();

  final response = await apiHelper.commonPost(
    "/api/v1/customroles/addpermissionsforcustomrole?workspaceId=${MyApp.activeWorkspace.workspaceId}&RoleId=${roleId}",
    isArrayRequest: true, arrayRequestBody: customList,
  );
  if (response != null) {
    responseModel = AddPermissionsReponseModel.fromJson(response);
  }
  return responseModel;
}

Future<GetPermisions> getPermissionsFromApi() async {
  GetPermisions responseModel = GetPermisions();
  final response = await apiHelper.commonGet(
      "/api/v1/customroles/getallsubcategoryroles?workspaceId=${MyApp.activeWorkspace.workspaceId}&RoleID=${MyApp.activeWorkspace.roleId}");
  if (response != null) {
    responseModel = GetPermisions.fromJson(response);
  }
  return responseModel;
}

Future<CommonAPIResponse> deleteCustomRoleApi(int roleID ) async {
  CommonAPIResponse responseModel = CommonAPIResponse();
  final response = await apiHelper.commonPut("/api/v1/customroles/removecustomroles?workspaceID=${MyApp.activeWorkspace.workspaceId}&roleID=${roleID}", );
  if (response != null) {
    responseModel = CommonAPIResponse.fromJson(response);
  }
  return responseModel;
}
