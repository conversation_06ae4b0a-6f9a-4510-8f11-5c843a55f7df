// ignore_for_file: sdk_version_ui_as_code, missing_return

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/roles_permissions/screens/add_new_role.dart';

import '../../common_function/validation_utility.dart';
import '../../common_widgets/header_footer.dart';
import '../../common_widgets/rounded_button.dart';
import '../../common_widgets/widgets.dart';
import '../../constants/colors.dart';
import '../../constants/styles.dart';
import '../../error_logs/error_logs_function.dart';
import '../model/receiving/get_all_categories.dart';
import '../model/receiving/get_roles_model.dart';
import '../model/sending/add_permission_custom_role.dart';
import '../permission_repositories.dart';
import '../widgets/permission_dropdown_widget.dart';
import '../widgets/permission_list_widget.dart';

// ignore: must_be_immutable
class ManegeRolesScreen extends StatefulWidget {
  ManegeRolesScreen({Key? key, this.roleID}) : super(key: key);
  int? roleID;

  @override
  State<ManegeRolesScreen> createState() => _ManegeRolesScreenState();
}

class _ManegeRolesScreenState extends State<ManegeRolesScreen> {
  double width = 0.0;
  TextEditingController roleTextController = TextEditingController();
  Validator validator = new Validator();
  List<Categories> categoriesList = [];
  List<AddPermissionsForCustomRole> permissionList = [];
  List<RolesModel> rolesList = [];
  RolesModel? selectedRole;
  bool isPermissionsLoading = false;
  bool isRoleLoading = false;
  bool showPermission = false;
  int? roleId;
  bool isLock = false;

  /// get all categories from api by role custom id
  getAllCategoriesById(int roleID) async {
    setState(() {
      isPermissionsLoading = true;
    });
    try {
      await getAllCategoriesByIdApi(MyApp.activeWorkspace.workspaceId ?? "", roleID).then(
        (value) {
          if (value.status == 1) {
            setState(() {
              categoriesList.clear();
              categoriesList = value.result ??[];
            });
          }
        },
      );
      setState(() {
        isPermissionsLoading = false;
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "GetAllCategories - ManageRoles screen");
      setState(() {
        isPermissionsLoading = false;
      });
    }
  }

  getAllRoles() async {
    try {
      setState(() {
        isRoleLoading = true;
      });
      await getAllRolesApi().then((value) {
        if (value.status == 1) {
          setState(() {
            rolesList = value.result ??[];
          });
        }
      });
      setState(() {
        isRoleLoading = false;
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "GetAllRoles - ManageRoles screen");
      setState(() {
        isRoleLoading = false;
      });
    }
  }

  initPageDetails() async {
    await getAllRoles();
    if (widget.roleID != null) {
      selectedRole = rolesList.firstWhere((element) => element.roleId == widget.roleID);
      roleId = widget.roleID;
      getAllCategoriesById(widget.roleID ??0);
    }
  }

  @override
  void initState() {
    initPageDetails();
    super.initState();
  }

  addPermissionCustomRole(int roleId, List<AddPermissionsForCustomRole> permissionsList) async {
    setState(() {
      isPermissionsLoading = true;
    });
    try {
      await addPermissionsCustomRole(roleId, permissionsList).then((value) {
        if (value.status == 1) {
          // successDialog(context, "Permission updated sucessfully");
          toast(value.message);
        } else {
          toast(value.message);
        }
      });
      setState(() {
        isPermissionsLoading = false;
      });
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "addPermissionCustomRole - ManageRoles screen");
      setState(() {
        isPermissionsLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      backgroundColor: secondaryTextColor,
      appBar: BaseAppBar(
        title: text("Roles & Permissions", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(),
        widgets: <Widget>[
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: InkWell(
                onTap: () {
                  widget.roleID = null;
                  selectedRole = null ;
                  categoriesList.clear();
                  Navigator.push(context, MaterialPageRoute(builder: (context) => AddNewRoleScreen())).then((value)async {
                    // if(value){
                       await initPageDetails();
                    // }
                  });
                },
                child: SvgPicture.asset(addMember)),
          ),
        ],
      ),
      body: Padding(
        padding: EdgeInsets.only(left: 15.0,right:15.0, bottom: 20.0,top:10),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              /// Roles
              Text(
                "Roles",
                style: black16w500.copyWith(fontSize: 18),
              ),

              /// Permission Drop down widget
              if (isRoleLoading) ...[
                SizedBox(
                  height: 50,
                  child: Align(
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(buttonThemeColor),
                    ),
                  ),
                )
              ] else ...[
                permissionsDropDownWidget(),
              ],

              if (categoriesList.isNotEmpty) ...[
                SizedBox(
                  height: 10,
                ),
                Text(
                  "Permissions",
                  style: black16w500.copyWith(fontSize: 18),
                ),
              ],

              ///Permissions
              if (isPermissionsLoading) ...[
                SizedBox(
                  height: 50,
                  child: Align(
                    alignment: Alignment.center,
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(buttonThemeColor),
                    ),
                  ),
                )
              ] else ...[
                if (categoriesList.isNotEmpty) ...[
                  permissionsListWidget(categoriesList),
                  selectedRole?.isDefault ?? false
                      ? SizedBox.shrink()
                      : Align(
                          alignment: Alignment.center,
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 8.0),
                            child: RoundedButton(
                                width: 200,
                                title: "Apply",
                                onPressed: () async {
                                  confirmationDialog(context);
                                }),
                          ),
                        ),
                ] else ...[
                  // SizedBox(
                  //   height: 300,
                  //   child: Align(
                  //     alignment: Alignment.center,
                  //     child: Text(
                  //       "Permissions not found",
                  //       style: black14BoldTextStyle.copyWith(fontSize: 16),
                  //     ),
                  //   ),
                  // )
                ]
              ]
            ],
          ),
        ),
      ),
    );
  }

  /// Permission list widget
  permissionsListWidget(List<Categories> categoriesList) {
    return Padding(
      padding: const EdgeInsets.only(top: 8.0),
      child: Wrap(
          children: List.generate(categoriesList.length, (index) {
        var item = categoriesList[index];
        return SizedBox(
          width: width > tabletWidth ? width * 0.3 : width,
          child: PermissionListWidget(
            categoriesModel: item,
            onPress: ((value) {
              if (value != null) {
                this.categoriesList[index] = value;
              }
            }),
            // isEdit: true,
            isEdit: selectedRole?.isDefault ??false ? false : true,
          ),
        );
      })),
    );
  }

  /// Confirmation dialog
  confirmationDialog(
    BuildContext context,
  ) {
    return showDialog(
        context: context,
        builder: (context) {
          return Center(
            child: SingleChildScrollView(
              child: AlertDialog(
                  content: Container(
                width: width > tabletWidth ? width * 0.4 : width * 0.9,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisSize: MainAxisSize.max,
                  children: [
                    Text(
                      "Are you sure?",
                      style: buttonTextStyle.copyWith(color: primaryTextColor),
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Text(
                      "Are you sure you want to grant these permissions",
                      style: black16TextStyle,
                    ),
                    SizedBox(
                      height: 20,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: RoundedButton(
                              title: "Cancel ",
                              isEnableBorder: true,
                              onPressed: () {
                                Navigator.pop(context);
                              }),
                        ),
                        SizedBox(
                          width: 10,
                        ),
                        Expanded(
                          child: RoundedButton(
                              title: "Confirm ",
                              isEnableBorder: false,
                              onPressed: () async {
                                if (!isLock) {
                                  setState(() {
                                    isLock = true;
                                  });
                                  permissionList = await generateListForAddCustomPermission(categoriesList);
                                  addPermissionCustomRole(roleId ?? 0, permissionList);
                                  Navigator.pop(context);
                                  setState(() {
                                    isLock = false;
                                  });
                                }
                              }),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
            ),
          );
        });
  }

  /// Permission dropdown widget
  permissionsDropDownWidget() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 8.0),
          child: PermissionDropdown(
            rolesList: rolesList,
            selectedRole: selectedRole,
            onChange: (value) {
              setState(() {
                selectedRole = value;
                roleId = selectedRole?.roleId;
                getAllCategoriesById(roleId ?? 0);
                FocusScope.of(context).requestFocus(FocusNode());
                showPermission = true;
              });
            },
          ),
        ),
      ],
    );
  }

  List<AddPermissionsForCustomRole> generateListForAddCustomPermission(List<Categories> categoriesList) {
    List<AddPermissionsForCustomRole> permissionList = [];
    for (Categories categories in categoriesList) {
      for (SubCategories subCategories in categories.subCategories!) {
        if (subCategories.access ?? false) {
          permissionList.add(AddPermissionsForCustomRole(subCategoryId: subCategories.subcategoryID, access: subCategories.access));
        }
      }
    }
    return permissionList;
  }
}
