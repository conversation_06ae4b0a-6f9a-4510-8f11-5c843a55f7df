import "package:flutter/material.dart";
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/utils/common_widgets/common_alert_dialogue/confirmation_alert_dialog.dart';
import 'package:smartbill/utils/common_widgets/loading_widget.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/roles_permissions/screens/manage_roles.dart';
import '../../common_function/validation_utility.dart';
import '../../common_widgets/header_footer.dart';
import '../../common_widgets/rounded_button.dart';
import '../../common_widgets/text_field_widget.dart';
import '../../common_widgets/widgets.dart';
import '../../constants/colors.dart';
import '../../constants/styles.dart';
import '../../error_logs/error_logs_function.dart';
import '../model/receiving/get_roles_model.dart';
import '../model/sending/add_customer_role.dart';
import '../permission_repositories.dart';

class AddNewRoleScreen extends StatefulWidget {
  const AddNewRoleScreen({Key? key}) : super(key: key);

  @override
  State<AddNewRoleScreen> createState() => _AddNewRoleScreenState();
}

class _AddNewRoleScreenState extends State<AddNewRoleScreen> {
  double width = 0.0;
  TextEditingController roleTextController = TextEditingController();
  Validator validator = new Validator();
  bool isRoleLoading = false;
  List<RolesModel> rolesList = [];
  bool isLock = false;

  /// create custom role api
  createRole(String role, BuildContext context) async {
    LoadingAlertWidget.onLoading(context);;
    AddCustomRole addCustomRoleModel = AddCustomRole(role: role);
    try {
      if (!isLock) {
          isLock = true;
        await addCustomRole(addCustomRoleModel).then((value) async {
          if (value.status == 1) {
            await getAllRoles();
          } else {
            showToast(value.message ?? "");
          }
        });
        
        LoadingAlertWidget.onStopping();
        isLock = false;
      }
    } catch (ex) {
      LoadingAlertWidget.onStopping();
      isLock = false;
      ErrorLogsFunction.insertErrorLogs(ex, "Create Role - Add new role screen");
    }
  }

  /// getvall roles from api Cutom roles and default roles
  getAllRoles() async {
    try {
      setState(() {
        isRoleLoading = true;
      });
      await getAllRolesApi().then((value) {
        if (value.status == 1) {
          setState(() {
            rolesList = value.result ?? [];
          });
        }
      });
      setState(() {
        isRoleLoading = false;
      });
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "Get All Roles API - Add new role screen");
      setState(() {
        isRoleLoading = false;
      });
      isLock = false;
    }
  }

  /// delete role api
  deleteRole(int roleId) async {
    try {
      if (!isLock) {
          isLock = true;
        await deleteCustomRoleApi(roleId).then((value) {
          if (value.status == 1) {
            getAllRoles();
          } else {
            showToast(value.message ?? "");
          }
        });
      }
        isLock = false;
    } catch (ex) {
      ErrorLogsFunction.insertErrorLogs(ex, "DeleteRole - Add new role screen");
    }
  }

  @override
  void initState() {
    
    initPageDetails();
    super.initState();
  }

  initPageDetails() async {
    await getAllRoles();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: BaseAppBar(
        title: text("Add Roles", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium),
        appBar: AppBar(
          leading: InkWell(
            onTap: () {
              Navigator.pop(context,true);
            },
            child: Icon(Icons.arrow_back_rounded),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(10.0),
        child: Form(
          key: validator.formkey,
          child: SingleChildScrollView(
            child: Column(children: [
              Align(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    "Create Role",
                    style: black14BoldTextStyle.copyWith(fontSize: 18),
                  )),
              SizedBox(
                height: 8,
              ),
              Row(
                children: [
                  Flexible(
                    child: Row(
                      // mainAxisSize: MainAxisSize.min,
                      children: [
                        Expanded(
                          child: TextFieldWidget(
                              // text: "Role Name",
                              isPassword: false,
                              mController: roleTextController,
                              inputType: TextInputType.text,
                              inputFormate: [
                                LengthLimitingTextInputFormatter(200),
                              ],
                              validator: validator.validateTextField,
                              inputAction: TextInputAction.done,
                              isEnabledBorder: true,
                              submit: (submit) {}),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 8.0),
                          child: RoundedButton(
                              width: 100,
                              title: "Create",
                              onPressed: () {
                                validator.validate();
                                if (validator.validate() ?? false) {
                                  createRole(roleTextController.text, context);
                                  FocusScope.of(context).unfocus();
                                  roleTextController.clear();
                                }
                              }),
                        ),
                      ],
                    ),
                  )
                ],
              ),
              SizedBox(
                height: 16,
              ),
              Align(
                  alignment: Alignment.bottomLeft,
                  child: Text(
                    "Roles List",
                    style: black14BoldTextStyle.copyWith(fontSize: 18),
                  )),
              SizedBox(
                height: 8,
              ),
              // ignore: sdk_version_ui_as_code
              if (isRoleLoading) ...[
                CircularProgressIndicator()
              ] else ...[
                rolesListWidget(),
              ]
            ]),
          ),
        ),
      ),
    );
  }

  /// Custom roles list widget
  rolesListWidget() {
    return Container(
      child: ListView.builder(
          itemCount: rolesList.length,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          itemBuilder: (context, index) {
            var item = rolesList[index];
            return Card(
              child: Container(
                height: 45,
                padding: EdgeInsets.only(left: 20),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "${item.role.toString()}",
                      style: black16w500.copyWith(fontSize: 16),
                    ),
                    // ignore: sdk_version_ui_as_code
                    if (!item.isDefault!) ...[
                      Row(
                        children: [
                          Container(
                            height: 35,
                            width: 35,
                            padding: const EdgeInsets.only(right: 8.0),
                            child: InkWell(
                                onTap: () {
                                  Navigator.push(
                                      context,
                                      MaterialPageRoute(
                                          builder: (context) => ManegeRolesScreen(
                                                roleID: item.roleId ?? 0,
                                              )));
                                },
                                child: SvgPicture.asset(editIconSVG)),
                          ),
                          Container(
                            height: 35,
                            width: 35,
                            padding: const EdgeInsets.only(right: 8.0),
                            child: InkWell(
                                onTap: () {
                                   showDialog(
                                      context: context,
                                      builder: ((context) {
                                        return ConfirmationAlertDialog(
                                          content: "Are you sure to delete this role",
                                          buttonNameOne: 'No',
                                          buttonNameTwo: 'Yes',
                                          ontabButtonOne: () {
                                            Navigator.pop(context);
                                          },
                                          ontabButtonTwo: () {
                                            deleteRole(item.roleId ?? 0);
                                            Navigator.pop(context);
                                          },
                                        );
                                      }));
                                },
                                child: SvgPicture.asset(deleteIconSVG)),
                          )
                        ],
                      )
                    ]
                  ],
                ),
              ),
            );
          }),
    );
  }
}
