import 'dart:convert';

import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/roles_permissions/permission_repositories.dart';
import '../../main.dart';
import '../common_function/list_class.dart';
import '../common_widgets/widgets.dart';
import 'model/receiving/get_all_permisions.dart';

class PermissionFunctions {
  static bool checkPermission(int permissionId, {bool viewToast = true}) {
    for (Permisions p in ListUtility.permisionsList) {
      if (p.subCategoryID == permissionId) {
        if (viewToast && !p.access!) {
          showToast("You don't have permission");
        }
        return p.access!;
      }
    }
    if (viewToast) {
      showToast("You don't have permission");
    }
    return false;
  }

  /// Get Permission from Api
  static getPermisionsFromAPI() async {
    ListUtility.permisionsList.clear();
    await getPermissionsFromApi().then((value) {
      if (value.status == 1) {
        ListUtility.permisionsList = value.result ??[];
      } else {
        toast(value.message);
      }
    });
    await storePermissionInLocal(ListUtility.permisionsList, MyApp.activeWorkspace.workspaceId ??"");
  }

  /// Get Permission from Local
  static getPermisionsFromLocal() async {
    ListUtility.permisionsList.clear();
    await getPermissionsFromLocal(MyApp.activeWorkspace.workspaceId??"").then((value) {
      ListUtility.permisionsList = value;
    });
  }

  static String permissionsToJson(List<Permisions> permissions) {
    List<Map<String, dynamic>> permissionList = permissions.map((perm) => perm.toJson()).toList();
    return json.encode(permissionList);
  }

  static storePermissionInLocal(List<Permisions> permissions, String workspaceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String permissionKey = workspaceId + "permissionsList";
    String permissionsJson = permissionsToJson(permissions);
    await prefs.setString(permissionKey, permissionsJson);
  }

  static Future<List<Permisions>> getPermissionsFromLocal(String workspaceId) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String permissionKey = workspaceId + "permissionsList";
    String permissionsJson = prefs.getString(permissionKey)??"";
    if (permissionsJson == null) {
      return [];
    }
    List<dynamic> permissionsList = json.decode(permissionsJson);
    return permissionsList.map((permJson) => Permisions.fromJson(permJson)).toList();
  }
}
