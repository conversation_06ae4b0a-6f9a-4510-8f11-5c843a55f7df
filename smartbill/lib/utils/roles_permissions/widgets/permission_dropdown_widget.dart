import 'package:flutter/material.dart';
import 'package:smartbill/main.dart';
import '../model/receiving/get_roles_model.dart';

class PermissionDropdown extends StatefulWidget {
  PermissionDropdown({
    Key? key,
    this.rolesList,
    this.selectedRole,
    this.onChange,
  }) : super(key: key);

  List<RolesModel>? rolesList = [];
  RolesModel? selectedRole;
  FunctionCallbackParam? onChange;

  @override
  State<PermissionDropdown> createState() => _PermissionDropdownState();
}

class _PermissionDropdownState extends State<PermissionDropdown> {
  double width = 0.0;
  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Row(
      children: [
        Expanded(
          child: Card(
            elevation:2,
            shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0), // Adjust the radius as needed
        ),
            child: InputDecorator(
              decoration: InputDecoration(
                contentPadding: EdgeInsets.symmetric(horizontal: 20.0, vertical: 1.0),
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(10.0), borderSide: BorderSide.none,),
              ),
              child: DropdownButtonHideUnderline(
                child: DropdownButton<RolesModel>(
                  menuMaxHeight: 250,
                  hint: Text("Select role"),
                  value: widget.selectedRole,
                  items: widget.rolesList?.map((RolesModel value) {
                    return new DropdownMenuItem<RolesModel>(
                      value: value,
                      child: new Text(value.role ?? ""),
                    );
                  }).toList(),
                  onChanged: (RolesModel? value) async {
                    widget.onChange!(value);
                  },
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
