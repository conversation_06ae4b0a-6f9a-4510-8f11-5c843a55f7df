// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import '../../../main.dart';
import '../../constants/images.dart';
import '../../constants/styles.dart';
import '../model/receiving/get_all_categories.dart';

class PermissionListWidget extends StatefulWidget {
  PermissionListWidget({Key? key, this.categoriesModel, this.onPress, this.isEdit}) : super(key: key);
  Categories? categoriesModel;
  FunctionCallbackParam? onPress;
  bool? isEdit;

  @override
  State<PermissionListWidget> createState() => _PermissionListWidget(categoriesModel!);
}

class _PermissionListWidget extends State<PermissionListWidget> {
  double width = 0.0;
  Categories categoriesModel;
  List<SubCategories> subCategoriesList = [];
  bool isExpanded = false;
  bool isChecked = false;

  _PermissionListWidget(this.categoriesModel);

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Column(
      children: [
        InkWell(
          onTap: () {
            setState(() {
              isExpanded = !isExpanded;
            });
          },
          child: Padding(
            padding: const EdgeInsets.only(right: 8.0,top: 10,bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  categoriesModel.category ?? "",
                  style: black16BoldTextStyle.copyWith(fontWeight: FontWeight.w700),
                ),
                SizedBox(
                  width: 15,
                  height: 15,
                  child: InkWell(
                    child: !isExpanded ? SvgPicture.asset(arrRightSVG) : SvgPicture.asset(arrDownSVG),
                  ),
                )
              ],
            ),
          ),
        ),
        if (isExpanded) ...[subCategoriesListWidget(categoriesModel.subCategories!)]
      ],
    );
  }

  subCategoriesListWidget(List<SubCategories> subCategoriesList) {
    return Container(
      color: Colors.grey[100],
      child: ListView.builder(
          itemCount: subCategoriesList.length,
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          scrollDirection: Axis.vertical,
          itemBuilder: (context, index) {
            var item = subCategoriesList[index];
            isChecked = item.access ?? false;
            return Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10.0),
              child: InkWell(
                onTap: (){
                   if (widget.isEdit ?? false) {
                    setState(() {
                      // isChecked = !isChecked;
                      subCategoriesList[index].access = !subCategoriesList[index].access!;
                    });
                    if (widget.onPress != null) {
                      Categories categories = widget.categoriesModel!;
                      categories.subCategories = subCategoriesList;
                      widget.onPress!(categories);
                    }
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: width > tabletWidth ? width * 0.2 : width * 0.65,
                      child: Text(
                        item.subCategory ?? "",
                        style: black16TextStyle,
                      ),
                    ),
                    
                    Checkbox(
                      value: isChecked,
                      onChanged: (bool? value) {
                        if (widget.isEdit ?? false) {
                          setState(() {
                            // isChecked = value;
                            subCategoriesList[index].access = value;
                          });
                          if (widget.onPress != null) {
                            Categories categories = widget.categoriesModel! ;
                            categories.subCategories = subCategoriesList;
                            widget.onPress!(categories);
                          }
                        }
                      },
                    )
                  ],
                ),
              ),
            );
          }),
    );
  }
}
