class AddPermissionsForCustomRole {
  int? subCategoryId;
  bool? access;

  AddPermissionsForCustomRole({
    this.subCategoryId,
    this.access,
  });

  factory AddPermissionsForCustomRole.fromJson(Map<String, dynamic> json) => AddPermissionsForCustomRole(
        subCategoryId: json["subCategoryId"],
        access: json["access"],
      );

  Map<String, dynamic> toJson() => {
        "subCategoryId": subCategoryId,
        "access": access,
      };
}

class AddPermissionsReponseModel {
  int? status;
  String? message;
  int? result;

  AddPermissionsReponseModel({this.status, this.message, this.result});

  AddPermissionsReponseModel.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['result'] = this.result;
    return data;
  }
}
