class GetAllCategories {
  int? status;
  String? message;
  List<Categories>? result;

  GetAllCategories({this.status, this.message, this.result});

  GetAllCategories.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    if (json['result'] != null) {
      result = <Categories>[];
      json['result'].forEach((v) {
        result?.add(new Categories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    if (this.result != null) {
      data['result'] = this.result?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Categories {
  int? id;
  String? category;
  List<SubCategories>? subCategories;

  Categories({this.id, this.category, this.subCategories});

  Categories.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    category = json['category'];
    if (json['subCategories'] != null) {
      subCategories = <SubCategories>[];
      json['subCategories'].forEach((v) {
        subCategories?.add(new SubCategories.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['category'] = this.category;
    if (this.subCategories != null) {
      data['subCategories'] = this.subCategories?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class SubCategories {
  String? subCategory;
  int? status;
  int? subcategoryID;
  bool? access;

  SubCategories({this.subCategory, this.status, this.subcategoryID, this.access});

  SubCategories.fromJson(Map<String, dynamic> json) {
    subCategory = json['subCategory'];
    status = json['status'];
    subcategoryID = json['subcategoryID'];
    access = json['access'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subCategory'] = this.subCategory;
    data['status'] = this.status;
    data['subcategoryID'] = this.subcategoryID;
    data['access'] = this.access;
    return data;
  }
}
