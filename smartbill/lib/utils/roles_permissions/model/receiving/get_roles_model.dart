import 'dart:convert';

GetRolesModel getRolesModelFromJson(String str) => GetRolesModel.fromJson(json.decode(str));

String getRolesModelToJson(GetRolesModel data) => json.encode(data.toJson());

class GetRolesModel {
  int? status;
  String? message;
  String? workspaceId;
  List<RolesModel>? result;

  GetRolesModel({
    this.status,
    this.message,
    this.workspaceId,
    this.result,
  });

  factory GetRolesModel.fromJson(Map<String, dynamic> json) => GetRolesModel(
        status: json["status"],
        message: json["message"],
        workspaceId: json["workspaceId"],
        result: json["result"] == null ? [] : List<RolesModel>.from(json["result"].map((x) => RolesModel.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status,
        "message": message,
        "workspaceId": workspaceId,
        "result": result == null ? [] : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class RolesModel {
  int? id;
  String? workspaceId;
  int? roleId;
  String? role;
  int? roleTypeId;
  DateTime? createdOn;
  int? createdBy;
  dynamic modifiedOn;
  dynamic modifiedBy;
  int? status;
  bool? isDefault;

  RolesModel({
    this.id,
    this.workspaceId,
    this.roleId,
    this.role,
    this.roleTypeId,
    this.createdOn,
    this.createdBy,
    this.modifiedOn,
    this.modifiedBy,
    this.status,
    this.isDefault
  });

  factory RolesModel.fromJson(Map<String, dynamic> json) => RolesModel(
        id: json["id"],
        workspaceId: json["workspaceID"],
        roleId: json["roleID"],
        role: json["role"],
        roleTypeId: json["roleTypeID"],
        createdOn: json["createdOn"] == null ? null : DateTime.parse(json["createdOn"]),
        createdBy: json["createdBy"],
        modifiedOn: json["modifiedOn"],
        modifiedBy: json["modifiedBy"],
        status: json["status"],
        isDefault: json["isDefault"]
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "workspaceID": workspaceId,
        "roleID": roleId,
        "role": role,
        "roleTypeID": roleTypeId,
        "createdOn": createdOn?.toIso8601String(),
        "createdBy": createdBy,
        "modifiedOn": modifiedOn,
        "modifiedBy": modifiedBy,
        "status": status,
        "isDefault" : isDefault,
      };
}
