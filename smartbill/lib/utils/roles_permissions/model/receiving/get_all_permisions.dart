class GetPermisions {
  int? status;
  String? message;
  String? workspaceId;
  int? roleID;
  List<Permisions>? result;

  GetPermisions({this.status, this.message, this.workspaceId, this.roleID, this.result});

  GetPermisions.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    workspaceId = json['workspaceId'];
    roleID = json['roleID'];
    if (json['result'] != null) {
      result = <Permisions>[];
      json['result'].forEach((v) {
        result?.add(Permisions.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['status'] = this.status;
    data['message'] = this.message;
    data['workspaceId'] = this.workspaceId;
    data['roleID'] = this.roleID;
    if (this.result != null) {
      data['result'] = this.result?.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Permisions {
  int? subCategoryID;
  String? subCategoryName;
  int? categoryID;
  String? categoryName;
  bool? access;

  Permisions({this.subCategoryID, this.subCategoryName, this.categoryID, this.categoryName, this.access});

  Permisions.fromJson(Map<String, dynamic> json) {
    subCategoryID = json['subCategoryID'];
    subCategoryName = json['subCategoryName'];
    categoryID = json['categoryID'];
    categoryName = json['categoryName'];
    access = json['access'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['subCategoryID'] = this.subCategoryID;
    data['subCategoryName'] = this.subCategoryName;
    data['categoryID'] = this.categoryID;
    data['categoryName'] = this.categoryName;
    data['access'] = this.access;
    return data;
  }
}
