import 'package:flutter/material.dart';
import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../model/products.dart';
import '../grid_add_new_product.dart';
import '../list_add_new_product.dart';
import '../product_card_widget.dart';
import '../product_list_view_widget.dart';

class NormalPanelWidget extends StatefulWidget {
  NormalPanelWidget({
    Key? key,
    this.filterProductList,
    this.isBigLayout,
    this.onTapItem,
    this.ontabAddNewProduct,
    // this.ontabGridAddProduct,
    this.isShowImages,
    this.isSaleLayoutTwo,
    this.onLongPressItem,
  }) : super(key: key);

  List<Products>? filterProductList = [];
  bool? isBigLayout;
  FunctionCallback2Param? onTapItem;
  FunctionCallbackParam? onLongPressItem;
  final VoidCallback? ontabAddNewProduct;
  //final VoidCallback ontabGridAddProduct;
  bool? isShowImages;
  bool? isSaleLayoutTwo;

  @override
  State<NormalPanelWidget> createState() => _NormalPanelWidgetState();
}

class _NormalPanelWidgetState extends State<NormalPanelWidget> {
  bool isOnTapItemLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return MyApp.isListViewEnable == true
        ? Expanded(
            child: Container(
              width: width,
              alignment: Alignment.topCenter,
              child: Container(
                padding: EdgeInsets.all(5.0),
                width: width,
                child: ListView.builder(
                    padding: EdgeInsets.only(top: 5.0, bottom: 20),
                    scrollDirection: Axis.vertical,
                    physics: ScrollPhysics(),
                    itemCount: widget.filterProductList?.length,
                    shrinkWrap: true,
                    itemBuilder: (BuildContext context, int index) {
                      var item = widget.filterProductList?[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3.0,
                        ),
                        child: item?.productId == null
                            ? ListAddNewProduct(
                                ontab: widget.ontabAddNewProduct!,
                              )
                            : ProductListViewCard(
                                item: item!,
                                onTab: () async {
                                  try {
                                    if (!isOnTapItemLock) {
                                      isOnTapItemLock = true;
                                       widget.onTapItem!(item, index);
                                      isOnTapItemLock = false;
                                    }
                                  } on Exception catch (e) {
                                    isOnTapItemLock = false;
                                  }
                                },
                                onLongPress: () async {
                                   widget.onLongPressItem!(item);
                                },
                              ),
                      );
                    }),
              ),
            ),
          )
        : Expanded(
            child: (widget.filterProductList?.isNotEmpty)?? false
                ? Container(
                    padding: EdgeInsets.all(5.0),
                    child: GridView.builder(
                        padding: EdgeInsets.only(bottom: 15),
                        scrollDirection: Axis.vertical,
                        physics: ScrollPhysics(),
                        itemCount: widget.filterProductList?.length,
                        shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: fnUtilities.getProductsGridCount(widget.isBigLayout?? false ? width * 0.5 : width),
                            crossAxisSpacing: 2,
                            mainAxisSpacing: 2,
                            childAspectRatio: 0.9),
                        itemBuilder: (BuildContext context, int index) {
                          var item = widget.filterProductList?[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 3.0),
                            child: item?.productId == null
                                ? GridAddNewProduct(
                                    onTab: widget.ontabAddNewProduct!,
                                  )
                                : ProductCardWidget(
                                    item: item!,
                                    showImages: widget.isShowImages ?? false,
                                    isSaleLayoutTwo: widget.isSaleLayoutTwo ?? false,
                                    onTab: () async {
                                      if (!isOnTapItemLock) {
                                        isOnTapItemLock = true;
                                         widget.onTapItem!(item, index);
                                        isOnTapItemLock = false;
                                      }
                                    },
                                    onLongPress: () async {
                                       widget.onLongPressItem!(item);
                                    },
                                  ),
                          );
                          //productCardWidget(item, index);
                        }),
                  )
                : Center(child: Text("Products not available")));
  }
}
