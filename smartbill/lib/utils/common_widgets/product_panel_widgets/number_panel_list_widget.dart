import 'package:flutter/material.dart';
import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../model/products.dart';
import '../grid_add_new_product.dart';
import '../list_add_new_product.dart';
import '../number_panel_widget.dart';
import '../product_card_widget.dart';
import '../product_list_view_widget.dart';

class NumberPanelListWidget extends StatefulWidget {
  NumberPanelListWidget({
    Key? key,
    this.filterProductList,
    this.isBigLayout,
    this.onTapItem,
    this.ontabAddNewProduct,
    this.onTabProductId,
    this.isShowImages,
    this.isSaleLayoutTwo,
    this.onLongPressItem,
    this.ontabCancel,
    this.prodId,
  }) : super(key: key);

  String? prodId;
  List<Products>? filterProductList = [];
  bool? isBigLayout;
  FunctionCallback2Param? onTapItem;
  FunctionCallbackParam? onLongPressItem;
  FunctionCallbackParam? onTabProductId;
  final VoidCallback? ontabAddNewProduct;
  final VoidCallback? ontabCancel;
  bool? isShowImages;
  bool? isSaleLayoutTwo;

  @override
  State<NumberPanelListWidget> createState() => _NumberPanelListWidgetState();
}

class _NumberPanelListWidgetState extends State<NumberPanelListWidget> {
  bool isOnTapItemLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    return (widget.filterProductList?.isNotEmpty) ?? false
        ? MyApp.isListViewEnable == true
            ? Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 3.0),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        NumberPanelWidget(
                            isListview: true,
                            prodID: widget.prodId ?? "",
                            onTabButton: (value) async {
                               widget.onTabProductId!(value);
                            },
                            onTabCancel: widget.ontabCancel!),
                        Container(
                          height: widget.isSaleLayoutTwo?? false ? height * 0.65 : (widget.isBigLayout?? false ? height : height / 3.3),
                          //width: width * 0.9,
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Expanded(
                                child: ListView.builder(
                                    itemCount: widget.filterProductList?.length,
                                    scrollDirection: Axis.vertical,
                                    physics: ScrollPhysics(),
                                    shrinkWrap: true,
                                    padding: EdgeInsets.only(top: 5.0, bottom: 20.0),
                                    itemBuilder: (BuildContext context, int index) {
                                      var item = widget.filterProductList?[index];

                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 3.0,
                                        ),
                                        child: item?.productId == null
                                            ? ListAddNewProduct(
                                                ontab: widget.ontabAddNewProduct!,
                                              )
                                            : ProductListViewCard(
                                                item: item!,
                                                onTab: () async {
                                                  try {
                                                    if (!isOnTapItemLock) {
                                                      isOnTapItemLock = true;
                                                       widget.onTapItem!(item, index);
                                                      isOnTapItemLock = false;
                                                    }
                                                  } on Exception catch (e) {
                                                    isOnTapItemLock = false;
                                                  }
                                                },
                                                onLongPress: () async {
                                                   widget.onLongPressItem!(item);
                                                },
                                              ),
                                      );
                                    }),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )
            : Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: widget.isBigLayout?? false ? 150 : 130,
                      child: SingleChildScrollView(
                        physics: AlwaysScrollableScrollPhysics(),
                        child: Column(
                          children: [
                            NumberPanelWidget(
                                isListview: false,
                                prodID: widget.prodId ?? "",
                                onTabButton: (value) async {
                                   widget.onTabProductId!(value);
                                },
                                onTabCancel: widget.ontabCancel!),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Container(
                          // width: width * 0.4,
                          // height: widget.isBigLayout ? null : height / 0.4,
                          child: GridView.builder(
                              padding: EdgeInsets.zero,
                              scrollDirection: Axis.vertical,
                              physics: ScrollPhysics(),
                              itemCount: widget.filterProductList?.length,
                              shrinkWrap: true,
                              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: widget.isSaleLayoutTwo?? false
                                      ? fnUtilities.getProductsGridCount(width, sideCardWidth: width / 3)
                                      : fnUtilities.getProductsGridCount(
                                          widget.isBigLayout?? false ? width * 0.5 : width,
                                          sideCardWidth: widget.isBigLayout?? false ? 150 : 120,
                                        ),
                                  crossAxisSpacing: 2,
                                  mainAxisSpacing: 2,
                                  childAspectRatio: 0.8),
                              itemBuilder: (BuildContext context, int index) {
                                var item = widget.filterProductList?[index];
                                return Padding(
                                  padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 3.0),
                                  child: item?.productId == null
                                      ? GridAddNewProduct(
                                          onTab: widget.ontabAddNewProduct!,
                                        )
                                      : ProductCardWidget(
                                          item: item!,
                                          showImages: widget.isShowImages ?? false,
                                          isSaleLayoutTwo: widget.isSaleLayoutTwo ?? false,
                                          onTab: () async {
                                            if (!isOnTapItemLock) {
                                              isOnTapItemLock = true;
                                              widget.onTapItem!(item, index);
                                              isOnTapItemLock = false;
                                            }
                                          },
                                          onLongPress: () async {
                                           widget.onLongPressItem!(item);
                                          },
                                        ),
                                );
                              }),
                        ),
                      ),
                    ),
                  ],
                ),
              )
        : Expanded(child: Center(child: Text("Products not available")));
  }
}
