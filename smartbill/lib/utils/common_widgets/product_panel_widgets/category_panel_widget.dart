import 'package:flutter/material.dart';

import '../../../Repositories/image_repository.dart';
import '../../../main.dart';
import '../../../model/products.dart';
import '../../constants/styles.dart';
import '../category_card_widget.dart';
import '../grid_add_new_product.dart';
import '../list_add_new_product.dart';
import '../product_card_widget.dart';
import '../product_list_view_widget.dart';

class CategoryPanelWidget extends StatefulWidget {
  CategoryPanelWidget({
    Key? key,
    this.productKeyList,
    this.filterProductList,
    this.isBigLayout,
    this.onTapItem,
    this.ontabListAddProduct,
    this.onPressCategory,
    this.isShowImages,
    this.isSaleLayoutTwo,
    this.onLongPressItem,
    this.categoryBoxHeight,
    this.prodKey,
  }) : super(key: key);

  List<ProductKey>? productKeyList = [];
  List<Products>? filterProductList = [];
  FunctionCallback2Param? onTapItem;
  FunctionCallbackParam? onLongPressItem;
  FunctionCallbackParam? onPressCategory;
  final VoidCallback? ontabListAddProduct;
  double? categoryBoxHeight;
  bool? isSaleLayoutTwo;
  bool? isBigLayout;
  bool? isShowImages;
  String? prodKey;

  @override
  State<CategoryPanelWidget> createState() => _CategoryPanelWidgetState();
}

class _CategoryPanelWidgetState extends State<CategoryPanelWidget> {
  bool isOnTapItemLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    return (widget.productKeyList?.isNotEmpty) ?? false
        ? MyApp.isListViewEnable == true
            ? Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Padding(
                        padding: EdgeInsets.all(3.0),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            SizedBox(
                              height: 80,
                              width: widget.isSaleLayoutTwo ?? false
                                  ? width
                                  : (width > maxScreenWidth
                                      ? width * 0.08
                                      : widget.isBigLayout?? false
                                          ? width * 0.12
                                          : width),
                              child: GridView.builder(
                                  padding: EdgeInsets.symmetric(horizontal: 15),
                                  scrollDirection: Axis.horizontal,
                                  physics: ScrollPhysics(),
                                  itemCount: widget.productKeyList?.length,
                                  shrinkWrap: true,
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount: 2,
                                          crossAxisSpacing: 10,
                                          mainAxisSpacing: 16,
                                          childAspectRatio: 0.3),
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    var item = widget.productKeyList?[index];
                                    return CategoryCardWidget(
                                      buttonWidth: widget.isSaleLayoutTwo?? false
                                          ? width
                                          : (width > maxScreenWidth
                                              ? width * 0.1
                                              : widget.isBigLayout?? false
                                                  ? width * 0.12
                                                  : width * 0.33),
                                      productKeyList: item!,
                                      onTab: () async {
                                         widget.onPressCategory!(item);
                                      },
                                    );
                                  }),
                            ),
                            Container(
                              height: widget.isSaleLayoutTwo ?? false
                                  ? height * 0.7
                                  : (widget.isBigLayout?? false ? height : height / 3),
                              width: width / 1.1,
                              child: Column(
                                children: [
                                  Expanded(
                                    child: ListView.builder(
                                        itemCount:
                                            widget.filterProductList?.length,
                                        scrollDirection: Axis.vertical,
                                        physics: ScrollPhysics(),
                                        shrinkWrap: true,
                                        padding: EdgeInsets.only(top: 5.0),
                                        itemBuilder:
                                            (BuildContext context, int index) {
                                          var item =
                                              widget.filterProductList?[index];
                                          return Container(
                                            child: item?.productId == null
                                                ? ListAddNewProduct(
                                                    ontab: widget
                                                        .ontabListAddProduct!,
                                                  )
                                                : ProductListViewCard(
                                                    item: item!,
                                                    onTab: () async {
                                                      try {
                                                        if (!isOnTapItemLock) {
                                                          isOnTapItemLock =
                                                              true;
                                                           widget
                                                              .onTapItem!(
                                                                  item, index);
                                                          isOnTapItemLock =
                                                              false;
                                                        }
                                                      } on Exception catch (e) {
                                                        isOnTapItemLock = false;
                                                      }
                                                    },
                                                    onLongPress: () async {
                                                       widget
                                                          .onLongPressItem!(
                                                              item);
                                                    },
                                                  ),
                                          );
                                        }),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Expanded(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      width: widget.isSaleLayoutTwo?? false
                          ? width / 3.3
                          : (widget.isBigLayout?? false ? 150 : 120),
                      height: widget.categoryBoxHeight,
                      child: SingleChildScrollView(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          children: [
                            SizedBox(
                              height: widget.categoryBoxHeight,
                              child: GridView.builder(
                                  padding: EdgeInsets.zero,
                                  scrollDirection: Axis.vertical,
                                  physics: ScrollPhysics(),
                                  itemCount: widget.productKeyList?.length,
                                  shrinkWrap: true,
                                  gridDelegate:
                                      SliverGridDelegateWithFixedCrossAxisCount(
                                          crossAxisCount:widget.isSaleLayoutTwo?? false?fnUtilities
                                                .getProductsGridCount(
                                                    width / 3.3): 1,
                                          crossAxisSpacing: 4,
                                          mainAxisSpacing: 13,
                                          childAspectRatio: 3),
                                  itemBuilder:
                                      (BuildContext context, int index) {
                                    var item = widget.productKeyList?[index];
                                    return CategoryCardWidget(
                                      //  buttonWidth:  width > maxScreenWidth
                                      //     ? width * 0.1
                                      //     : isBigLayout
                                      //         ? width * 0.12
                                      //         : width * 0.33,
                                      productKeyList: item!,
                                      onTab: () async {
                                         widget.onPressCategory!(item);
                                      },
                                    );
                                  }),
                            ),
                          ],
                        ),
                      ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: EdgeInsets.only(left: 8.0),
                        child: Container(
                          width: widget.isSaleLayoutTwo?? false ? width : width * 0.4,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 5.0),
                            child: ( widget.filterProductList?.isEmpty)!
                                ? Center(child: Text("Products not available"))
                                : GridView.builder(
                                    padding: EdgeInsets.zero,
                                    scrollDirection: Axis.vertical,
                                    physics: ScrollPhysics(),
                                    itemCount: widget.filterProductList?.length,
                                    shrinkWrap: true,
                                    gridDelegate:
                                        SliverGridDelegateWithFixedCrossAxisCount(
                                            crossAxisCount: widget.isSaleLayoutTwo?? false
                                                ? fnUtilities.getProductsGridCount(width,
                                                        sideCardWidth: width / 3.3)
                                                : fnUtilities
                                                    .getProductsGridCount(
                                                    widget.isBigLayout?? false
                                                        ? width * 0.5
                                                        : width,
                                                    sideCardWidth:
                                                        widget.isBigLayout?? false
                                                            ? 150
                                                            : 120,
                                                  ),
                                            crossAxisSpacing: 2,
                                            mainAxisSpacing: 2,
                                            childAspectRatio: 0.8),
                                    itemBuilder:
                                        (BuildContext context, int index) {
                                      var item =
                                          widget.filterProductList?[index];
                                      return Padding(
                                        padding: const EdgeInsets.symmetric(
                                            horizontal: 3.0, vertical: 3.0),
                                        child: item?.productId == null
                                            ? GridAddNewProduct(
                                                onTab:
                                                    widget.ontabListAddProduct!,
                                              )
                                            : ProductCardWidget(
                                                item: item!,
                                                showImages: widget.isShowImages ?? false,
                                                isSaleLayoutTwo:
                                                    widget.isSaleLayoutTwo!,
                                                onTab: () async {
                                                  if (!isOnTapItemLock) {
                                                    isOnTapItemLock = true;
                                                     widget.onTapItem!(
                                                        item, index);
                                                    isOnTapItemLock = false;
                                                  }
                                                },
                                                onLongPress: () async {
                                                   widget
                                                      .onLongPressItem!(item);
                                                },
                                              ),
                                      );
                                    }),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              )
        : Expanded(child: Center(child: Text("Categories are not available")));
  }
}
