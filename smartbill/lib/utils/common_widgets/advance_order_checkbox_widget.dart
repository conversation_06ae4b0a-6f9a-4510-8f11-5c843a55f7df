import 'package:flutter/material.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../constants/colors.dart';

import 'widgets.dart';

class EnableAdvanceOrderCheckBoxWidget extends StatelessWidget {
  final bool isExpandAdvanceOrderWidget;
  final bool? isEnableDeliveryButton;
  final String? deliveryDateAndTime;
  final VoidCallback? onPressedCheckBox;
  final VoidCallback? onSetDelivaeryDate;

  const EnableAdvanceOrderCheckBoxWidget({
    Key? key,
    required this.isExpandAdvanceOrderWidget,
    this.isEnableDeliveryButton,
    this.deliveryDateAndTime,
    this.onPressedCheckBox,
    this.onSetDelivaeryDate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 3.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            'Enable Order :',
            style: black16w500,
          ),
          Checkbox(
            activeColor: buttonThemeColor,
            value: isExpandAdvanceOrderWidget,
            onChanged: (bool? value) {
              if (deliveryDateAndTime != null) {
                showToast("While update the order unable to disable !");
              } else {
                onPressedCheckBox?.call();
              }
            },
          ),
        ],
      ),
    );
  }
}
