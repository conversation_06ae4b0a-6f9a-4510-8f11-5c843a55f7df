import 'package:flutter/material.dart';

class CommonSearchComponent extends StatefulWidget {
  //final List<String> data;
  final Function(String)? onSearch;
  final Function(String)? onTap;

  CommonSearchComponent(
      {
      //this.data,
      this.onSearch,
      this.onTap});

  @override
  State<CommonSearchComponent> createState() => _CommonSearchComponentState();
}

class _CommonSearchComponentState extends State<CommonSearchComponent> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      child: TextField(
        onTapOutside: (event) {
          FocusManager.instance.primaryFocus?.unfocus();
        },
        onTap: () {
          widget.onTap;
        },
        onChanged: (value) {
          widget.onSearch!(value);
        },
        decoration: InputDecoration(
          labelText: 'Search',
          hintText: 'Search...',
          suffixIcon: Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.all(Radius.circular(40.0)),
          ),
        ),
      ),
    );
  }
}
