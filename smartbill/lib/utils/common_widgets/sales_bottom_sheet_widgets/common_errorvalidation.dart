import 'package:flutter/material.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../constants/colors.dart';

class CommonErrorValidation extends StatefulWidget {
  String? message;
  String? buttonName;
  final VoidCallback? onPress;
  double width;
  CommonErrorValidation({super.key, this.message, this.onPress, this.width = 0.0, this.buttonName});

  @override
  State<CommonErrorValidation> createState() => _CommonErrorValidationState();
}

class _CommonErrorValidationState extends State<CommonErrorValidation> {
  double width = 0.0;
  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Padding(
      padding: EdgeInsetsDirectional.symmetric(horizontal: 10.0, vertical: 5.0),
      child: Container(
        padding: EdgeInsetsDirectional.symmetric(horizontal: 10),
        width: widget.width != 0.0 ? widget.width : width,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(3),
          boxShadow: [
            BoxShadow(
              color: greyColor,
              spreadRadius: 1,
              // blurRadius: 2,
              // offset: Offset(0, 2),
            ),
          ],
          color: Color(0xFFF6F6F6),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              widget.message ?? '',
              style: red18w700.copyWith(fontWeight: FontWeight.w400),
            ),
            TextButton(
                onPressed: widget.onPress,
                child: Text(
                  widget.buttonName ?? 'Dismiss',
                  style: red18w700.copyWith(fontWeight: FontWeight.w400, color: appThemeColor),
                ))
          ],
        ),
      ),
    );
  }
}
