import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../model/payment_type.dart';
import '../../constants/styles.dart';

class PaymentTypeAlertDialog extends StatefulWidget {
  final List<PaymentType> paymentTypeList;
  PaymentType? selectedRadioPaymentType;

  PaymentTypeAlertDialog({required this.paymentTypeList, this.selectedRadioPaymentType});

  @override
  _PaymentTypeAlertDialogState createState() => _PaymentTypeAlertDialogState();
}

class _PaymentTypeAlertDialogState extends State<PaymentTypeAlertDialog> {
  double width = 0.0;
  PaymentType? paymentType;
  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return AlertDialog(
      title: Text('Payment Type', style: TextStyle(fontSize: 18, fontWeight: FontWeight.w700)),
      content: Container(
        height: MediaQuery.of(context).size.height * 0.4,
        width: width > tabletWidth ? width * 0.45 : double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Expanded(
              child: ListView.builder(
                physics: AlwaysScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: widget.paymentTypeList.length,
                itemBuilder: (BuildContext context, int index) {
                  var paymentType = widget.paymentTypeList[index];
                  return InkWell(
                    onTap: () {
                      widget.selectedRadioPaymentType = widget.paymentTypeList.firstWhere((element) => element.id == paymentType?.id);

                      Navigator.pop(context, widget.selectedRadioPaymentType);
                    },
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(
                          "${index + 1}. ",
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                        ), // Serial Number
                        Expanded(
                          child: Text(
                            paymentType?.paymentName ?? "",
                            overflow: TextOverflow.ellipsis,
                            softWrap: true,
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                          ),
                        ), // Payment Name
                        // Spacer(), // Add space to align radio button to the right
                        Radio(
                          value: paymentType,
                          groupValue: widget.selectedRadioPaymentType ?? widget.paymentTypeList.first,
                          activeColor: appThemeColor,
                          onChanged: (value) {
                            widget.selectedRadioPaymentType = value;

                            Navigator.pop(context, widget.selectedRadioPaymentType);
                          },
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
            RoundedButton(
              width: width * 0.35,
              isEnableBorder: true,
              onPressed: () {
                Navigator.pop(context);
              },
              title: 'Close',
            )
          ],
        ),
      ),
    );
  }
}
