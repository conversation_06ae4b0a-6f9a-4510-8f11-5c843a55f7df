import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../../main.dart';
import '../../../model/customers.dart';
import '../../common_function/phone_input_formatter.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/images.dart';
import '../widgets.dart';
import 'Rounded_button_with_icon.dart';
import 'bottom_sheet_widget_controllers/cusromer_widget_controller.dart';

class CustomerWidget extends StatefulWidget {
  final TextEditingController? customerNameController;
  final TextEditingController? mobileNoController;

  final List<Customer>? customerList;

  final VoidCallback? onPressedChooseCustomer;

  final FunctionCallbackParam? onTabSelectCustomer;
  FunctionCallbackParam? onChageName;
  FunctionCallbackParam? onChageNumber;
  double width = 0.0;
  FocusNode? customerFocusNode;
  FocusNode? mobileNumberFocusNode;
  CustomerController controller;
  bool enableMobileNumberFocuse;

  CustomerWidget({
    Key? key,
    this.customerNameController,
    this.mobileNoController,
    this.onTabSelectCustomer,
    this.onPressedChooseCustomer,
    this.onChageNumber,
    this.onChageName,
    this.customerFocusNode,
    this.customerList,
    required this.controller,
    this.mobileNumberFocusNode,
    this.enableMobileNumberFocuse = false,
  }) : super(key: key);

  @override
  State<CustomerWidget> createState() => _CustomerWidgetState();
}

class _CustomerWidgetState extends State<CustomerWidget> {
  double width = 0.0;
  Validator validator = new Validator();

  @override
  void initState() {
    initFunction();
    super.initState();
  }

  @override
  void dispose() {
    widget.controller.detach();
    super.dispose();
  }

  initFunction() {
    //if (widget.enableMobileNumberFocuse) {
    widget.controller.mobileAttach(updateMobileNumberFocus);
    //} else {
    widget.controller.attach(updateCustomerNameFocus);
    //}
  }

  void updateCustomerNameFocus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(widget.customerFocusNode);
    });
  }

  void updateMobileNumberFocus() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      FocusScope.of(context).requestFocus(widget.mobileNumberFocusNode);
    });
  }

  clearFields() async {
    if (widget.customerNameController!.text.isNotEmpty || widget.mobileNoController!.text.isNotEmpty) {
      widget.customerNameController?.clear();
      widget.mobileNoController?.clear();
    }
    // setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Card(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Form(
            key: validator.formkey,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 3, vertical: 2),
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 2, vertical: 2),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.grey.withOpacity(0.5),
                      // spreadRadius: 1,
                      // blurRadius: 2,
                      // offset: Offset(0, 2),
                    ),
                  ],
                  color: secondaryTextColor,
                ),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 5.0, vertical: 3.0),
                  child: Column(mainAxisSize: MainAxisSize.min, children: [
                    SizedBox(
                      height: 3,
                    ),
                    Row(
                      children: [
                        Flexible(
                            child: Autocomplete<Customer>(
                          optionsBuilder: (TextEditingValue textEditingValue) {
                            if (textEditingValue.text.isEmpty) {
                              return const Iterable<Customer>.empty();
                            }
                            // textEditingValue = widget.customerNameController;
                            return widget.customerList!.where((Customer customer) {
                              return customer.firstName!.toLowerCase().contains(textEditingValue.text.toLowerCase());
                            });
                          },
                          displayStringForOption: (Customer option) => option.firstName ?? '',
                          fieldViewBuilder: (
                            BuildContext context,
                            TextEditingController textEditingController,
                            FocusNode focusNode,
                            VoidCallback onFieldSubmitted,
                          ) {
                            return EditText(
                              mController: widget.customerNameController,
                              focus: widget.customerFocusNode,
                              autofocus: widget.enableMobileNumberFocuse ? false : true,
                              onTapOutside: (event) {
                                print('onTapOutside');
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                              text: "Customer Name",
                              inputType: TextInputType.text,
                              isPassword: false,
                              onChange: (value) {
                                widget.onChageName!(value);
                              },
                              suffixIcon: InkWell(
                                  onTap: () {
                                    clearFields();
                                    textEditingController.clear();
                                  },
                                  child: Icon(Icons.clear)),
                              // submit: (String value) {
                              //   // Handle onFieldSubmitted
                              //   // Find the customer based on the entered text
                              //   Customer? customer = widget.customerList!.firstWhere(
                              //     (Customer customer) => customer.firstName!.toLowerCase() == value.toLowerCase(),
                              //     orElse: () => Customer(firstName: value, mobileNo: ''),
                              //   );
                              //   // Call the provided callback
                              //   widget.onTabSelectCustomer!(customer);
                              //   onFieldSubmitted();
                              // },
                            );
                          },
                          optionsViewBuilder: (BuildContext context, AutocompleteOnSelected<Customer> onSelected, Iterable<Customer> options) {
                            return Align(
                              alignment: Alignment.topLeft,
                              child: Material(
                                elevation: 4,
                                color: backgroundColor, // Change the background color of the options list
                                child: Container(
                                  decoration: BoxDecoration(
                                    color: backgroundColor, // Change the background color of the options list
                                    borderRadius: BorderRadius.circular(8.0), // Optional: Add border radius to the options list
                                  ),
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: ListTile.divideTiles(
                                      context: context,
                                      tiles: options.map((Customer customer) {
                                        final isFirst = options.first == customer;
                                        return Container(
                                          color: isFirst ? lightGreyColor : Colors.transparent,
                                          child: ListTile(
                                            title: Text(
                                              '${customer.firstName!} \n${customer.mobileNo}',
                                              style:
                                                  black15BoldTextStyle.copyWith(fontWeight: FontWeight.w500), // Change the text color of the options
                                            ),
                                            onTap: () {
                                              onSelected(customer);
                                              widget.onTabSelectCustomer!(customer);
                                            },
                                          ),
                                        );
                                      }),
                                    ).toList(),
                                  ),
                                ),
                              ),
                            );
                          },
                        )),

                        // Expanded(
                        //   child: EditText(
                        //       mController: widget.customerNameController,
                        //       inputType: TextInputType.text,
                        //       isPassword: false,
                        //       text: "Customer Name",
                        //       readOnly: widget.isEnableChooseButton ? false : false,
                        //       validator: (value) {
                        //         return null;
                        //       },
                        //       suffixIcon: InkWell(
                        //           onTap: () {
                        //             clearFields();
                        //           },
                        //           child: Icon(Icons.update)),
                        //       onChange: (value) {
                        //         widget.onChage!(value);
                        //       },
                        //       isEnableErrorBorder: true,
                        //       errorBorderColor: primaryTextColor),
                        // ),
                        SizedBox(
                          width: 10,
                        ),

                        RoundedButtonWithIcon(width: width * 0.2, buttonIcon: chooseCustomerSVG, onPressed: widget.onPressedChooseCustomer),
                      ],
                    ),
                    SizedBox(
                      height: 10,
                    ),
                    Container(
                      width: width,
                      child: EditText(
                          mController: widget.mobileNoController,
                          isPassword: false,
                          readOnly: false,
                          focus: widget.mobileNumberFocusNode,
                          autofocus: widget.enableMobileNumberFocuse ? true : false,
                          text: "Mobile No",
                          inputType: TextInputType.phone,
                          inputFormate: [
                            PhoneInputFormatter(),
                            FilteringTextInputFormatter.deny(RegExp(r"\s")),
                            FilteringTextInputFormatter.digitsOnly,
                            LengthLimitingTextInputFormatter(10)
                          ],
                          validator: (value) {
                            return null;
                          },
                          onChange: (value) {
                            if (widget.onChageNumber != null) {
                              widget.onChageNumber!(value);
                            }
                          },
                          isEnableErrorBorder: true,
                          errorBorderColor: primaryTextColor),
                    ),
                  ]),
                ),
              ),
            )));
  }
}
