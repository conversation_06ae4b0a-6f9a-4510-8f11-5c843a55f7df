import 'package:flutter/material.dart';

import '../../constants/colors.dart';

class CommonTabBarSection extends StatefulWidget {
  Widget tab1;
  Widget tab2;

  String? tabOneName;
  String? tabTwoName;
  double? height;
  final Function(int)? onChangeTabEvent;

  int? initialTabIndex;
  CommonTabBarSection({
    Key? key,
    required this.tab1,
    required this.tab2,
    this.tabOneName,
    this.height,
    this.onChangeTabEvent,
    this.tabTwoName,
    this.initialTabIndex,
  });
  @override
  _CommonTabBarSectionState createState() => _CommonTabBarSectionState();
}

class _CommonTabBarSectionState extends State<CommonTabBarSection> with SingleTickerProviderStateMixin {
  TabController? _tabController;
  // int index = 0;
  @override
  void initState() {
    super.initState();
    initFunction();
  }

  initFunction() {
    setState(() {
      _tabController = TabController(length: 2, vsync: this, initialIndex: widget.initialTabIndex ?? 0);
      _tabController?.addListener(() {
        widget.initialTabIndex = _tabController?.index;
        print(widget.initialTabIndex);
        if (widget.onChangeTabEvent != null) {
          widget.onChangeTabEvent!(widget.initialTabIndex!);
        }
      });
    });
  }

  _handleTabChange() {
    setState(() {
      widget.initialTabIndex = _tabController?.index;
      print(widget.initialTabIndex);
      if (widget.onChangeTabEvent != null) {
        widget.onChangeTabEvent!(widget.initialTabIndex!);
      }
    });
  }

  double height = 0.0;

  @override
  Widget build(BuildContext context) {
    height = MediaQuery.of(context).size.height;
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0, horizontal: 1.50),
      child: Card(
        semanticContainer: false,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        child: Container(
          // constraints: BoxConstraints(maxHeight: widget.height != null ? widget.height : widget.height * 0.35, minHeight: 0.30),
          height: widget.height != null ? widget.height : height * 0.35,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.5),
                // spreadRadius: 1,
                // blurRadius: 2,
                // offset: Offset(0, 2),
              ),
            ],
            color: secondaryTextColor,
          ),
          child: DefaultTabController(
            length: 2,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Color(0xFFF5EEFF),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(10),
                      topRight: Radius.circular(10),
                    ),
                  ),
                  child: TabBar(
                    controller: _tabController,
                    indicator: BoxDecoration(
                      color: secondaryTextColor,
                      borderRadius: BorderRadius.only(
                        topLeft: Radius.circular(10),
                        topRight: Radius.circular(10),
                      ),
                    ),
                    labelStyle: TextStyle(fontSize: 18),
                    unselectedLabelColor: Colors.grey,
                    labelColor: Colors.black,
                    tabs: [
                      Tab(
                        text: widget.tabOneName ?? 'Guest',
                      ),
                      Tab(
                        text: widget.tabTwoName ?? 'Members',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: TabBarView(
                    controller: _tabController,
                    children: [widget.tab1, widget.tab2],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
