import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

class RoundedButtonWithIcon extends StatefulWidget {
  RoundedButtonWithIcon(
      {Key? key, this.title, @required this.onPressed, this.width, this.isEnableBorder, this.height, this.buttonIcon, this.iconColor});
  String? title;
  final VoidCallback? onPressed;
  double? width;
  bool? isEnableBorder = false;
  double? height;
  String? buttonIcon;
  Color? iconColor;

  @override
  State<RoundedButtonWithIcon> createState() => _RoundedButtonState();
}

class _RoundedButtonState extends State<RoundedButtonWithIcon> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPressed,
      child: Container(
          alignment: Alignment.center,
          width: widget.width,
          height: widget.height != null ? widget.height : 45,
          decoration: widget.isEnableBorder ?? false
              ? BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: appThemeColor,
                    width: 2.0,
                  ),
                )
              : boxDecoration(bgColor: appThemeColor, radius: 8.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (widget.buttonIcon != null) ...[
                SvgPicture.asset(
                  widget.buttonIcon ?? "",
                  // ignore: deprecated_member_use
                  color: widget.iconColor ?? backgroundColor,
                ),
                SizedBox(
                  height: 5,
                )
              ],
              if (widget.title != null)
                Text(widget.title ?? "", style: widget.isEnableBorder ?? false ? buttonTextStyle.copyWith(color: appThemeColor) : buttonTextStyle),
            ],
          )),
    );
  }
}
