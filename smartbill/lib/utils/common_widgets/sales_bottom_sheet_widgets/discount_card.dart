import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../common_function/validation_utility.dart';
import '../../constants/images.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class DiscountCard extends StatefulWidget {
  DiscountCard(
      {Key? key,
      this.discountController,
      this.isDiscountEmpty,
      this.selectedDiscountOn,
      this.onPressItem,
      this.onPressClose,
      this.updateDiscountOption})
      : super(key: key);
  final VoidCallback? onPressClose;

  TextEditingController? discountController = TextEditingController();
  bool? isDiscountEmpty;
  String? selectedDiscountOn;
  final FunctionCallbackParam? updateDiscountOption;
  final void Function(dynamic, dynamic)? onPressItem; // Update the callback type

  @override
  State<DiscountCard> createState() => _DiscountCardState();
}

class _DiscountCardState extends State<DiscountCard> {
  Validator validator = new Validator();
  bool isLock = false;
  // final discountController = TextEditingController();
  bool isControllerEmpty = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return StatefulBuilder(builder: (context, setState) {
      return Container(
        width: width > tabletWidth ? width * 0.4 : width * 0.9,
        padding: const EdgeInsets.symmetric(horizontal: 10.0),
        child: Form(
          key: validator.formkey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Row(
                    children: [
                      Text(
                        'Percentage(%)',
                        style: black16BoldTextStyle,
                      ),
                      Radio(
                        value: 'Percentage',
                        groupValue: widget.selectedDiscountOn,
                        onChanged: (value) {
                          if (!isLock) {
                            isLock = true;
                            setState(() {
                              widget.selectedDiscountOn = value.toString();
                              widget.updateDiscountOption!(value);
                            });
                            isLock = false;
                          }
                        },
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        'Amount(₹)',
                        style: black16BoldTextStyle,
                      ),
                      Radio(
                        value: 'Fixed Amount',
                        groupValue: widget.selectedDiscountOn,
                        onChanged: (value) {
                          if (!isLock) {
                            isLock = true;
                            setState(() {
                              widget.selectedDiscountOn = value.toString();
                              widget.updateDiscountOption!(value);
                            });
                            isLock = false;
                          }
                        },
                      ),
                    ],
                  ),
                ],
              ),
              SizedBox(
                height: 10,
              ),
              widget.selectedDiscountOn != null
                  ? Column(
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: EditText(
                                  text: widget.selectedDiscountOn == "Percentage" ? "Percentage (%)" : "Amount (Rs)",
                                  isPassword: false,
                                  mController: widget.discountController,
                                  inputType: TextInputType.number,
                                  onTapOutside: (event) {
                                    FocusManager.instance.primaryFocus?.unfocus();
                                  },
                                  inputFormate: [
                                    FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                                  ],
                                  onChange: (value) {
                                    setState(() {
                                      value;
                                      isControllerEmpty = value.isEmpty;
                                    });
                                  },
                                  validator: (value) {
                                    return null;
                                  },
                                  inputAction: TextInputAction.next,
                                  isEnableErrorBorder: true,
                                  errorBorderColor: primaryTextColor),
                            ),
                            SizedBox(
                              width: 5.0,
                            ),
                            RoundedButton(
                              title: "Apply",
                              width: width > tabletWidth ? width * 0.18 : width * 0.2,
                              onPressed: () {
                                if (widget.discountController!.text.isNotEmpty) {
                                  widget.onPressItem!(widget.discountController?.text, widget.selectedDiscountOn);
                                } else {
                                  setState(() {
                                    isControllerEmpty = true;
                                  });
                                }
                              },
                            ),
                          ],
                        ),
                        SizedBox(
                          height: isControllerEmpty ? 0.0 : 15.0,
                        ),
                        if (isControllerEmpty) ...[
                          Padding(
                            padding: const EdgeInsets.only(top: 2.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                SvgPicture.asset(
                                  errorSVG,
                                ),
                                SizedBox(
                                  width: 4.0,
                                ),
                                Text(
                                  'Please enter ${widget.selectedDiscountOn == "Percentage" ? 'percentage' : 'amount'} ',
                                  style: red14w300,
                                )
                              ],
                            ),
                          )
                        ],
                      ],
                    )
                  : Container(
                      height: 30,
                    ),
            ],
          ),
        ),
      );
    });
  }
}
