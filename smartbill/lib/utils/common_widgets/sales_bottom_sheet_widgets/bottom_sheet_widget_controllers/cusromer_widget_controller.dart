class CustomerController {
  Function? _updateTextFocus;
  Function? _updateMobileNumberFocus;

  void attach(Function updateFocusCallback) {
    _updateTextFocus = updateFocusCallback;
  }

  void mobileAttach(Function updateFocusCallback) {
    _updateMobileNumberFocus = updateFocusCallback;
  }

  void detach() {
    _updateTextFocus = null;
    _updateMobileNumberFocus = null;
  }

  void focusCustomerField() {
    if (_updateTextFocus != null) {
      _updateTextFocus!();
    } else {
      print('Error: updateTextFocus callback is not attached.');
    }
  }

  void focusMobileNumberField() {
    if (_updateMobileNumberFocus != null) {
      _updateMobileNumberFocus!();
    } else {
      print('Error: _updateMobileNumberFocus callback is not attached.');
    }
  }
}
