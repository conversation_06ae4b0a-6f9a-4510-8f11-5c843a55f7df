class BillingCardController {
  Function()? _updateSelectedIndex;

  Function()? _selectPreOrderCard;
  Function()? _updateDiscountCard;

  void attach({
    required Function() updateSelectedIndexCallback,
    required Function() selectPreOrderCardCallback,
    required Function() changeDiscountCardCallback,
  }) {
    _updateSelectedIndex = updateSelectedIndexCallback;

    _selectPreOrderCard = selectPreOrderCardCallback;
    _updateDiscountCard = changeDiscountCardCallback;
  }

  void selectCard() {
    if (_updateSelectedIndex != null) {
      _updateSelectedIndex!();
    } else {
      print('Error: updateSelectedIndex callback is not attached.');
    }
  }

  void selectPreOrderCard() {
    if (_selectPreOrderCard != null) {
      _selectPreOrderCard!();
    } else {
      print('Error: selectPreOrderCard callback is not attached.');
    }
  }

  void selectDiscountCard() {
    if (_updateDiscountCard != null) {
      _updateDiscountCard!();
    } else {
      print('Error: selectPreOrderCard callback is not attached.');
    }
  }
}
