import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../main.dart';
import '../../../model/discounts.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class CouponDiscountCard extends StatefulWidget {
  CouponDiscountCard({Key? key, this.discountList, this.discountType, this.onPressItem, this.selectedCoupon}) : super(key: key);

  List<Discounts>? discountList;
  String? discountType;
  FunctionCallbackParam? onPressItem;
  int? selectedCoupon;

  @override
  State<CouponDiscountCard> createState() => _CouponDiscountDialogState();
}

class _CouponDiscountDialogState extends State<CouponDiscountCard> {
  bool isLock = false;
  int? selectedRadio = -1;
  @override
  void initState() {
    inipageDtails();
    super.initState();
  }

  inipageDtails() {
    if (widget.selectedCoupon != null) {
      selectedRadio = widget.discountList?.firstWhere((element) => element.id == widget.selectedCoupon).id;
    }
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return Container(
      width: width > tabletWidth ? width * 0.4 : width * 0.9,
      child: SingleChildScrollView(
        physics: AlwaysScrollableScrollPhysics(),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 180,
              child: (widget.discountList?.length)! > 0
                  ? ListView.builder(
                      shrinkWrap: true,
                      itemCount: widget.discountList?.length,
                      itemBuilder: (BuildContext context, int index) {
                        var item = widget.discountList?[index];
                        String type = widget.discountType == "Sales" ? "S" : "P";

                        return type == item?.type
                            ? Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    padding: EdgeInsets.only(left: 3.0),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: <Widget>[
                                        TextWidget((index + 1).toString(), // Serial number
                                            textColor: primaryTextColor,
                                            fontFamily: fontBold,
                                            fontSize: textSizeMedium),
                                        InkWell(
                                          onTap: () {
                                            setState(() {
                                              // Update selectedRadio to the item's ID
                                              selectedRadio = item?.id;
                                            });
                                            widget.onPressItem!(item);
                                          },
                                          child: Row(
                                            children: [
                                              Container(
                                                width: width > tabletWidth ? width * 0.28 : width * 0.45,
                                                child: TextWidget(item?.couponName != null ? item?.couponName ?? "" : "",
                                                    textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                              ),
                                              Container(
                                                width: width > tabletWidth ? width * 0.1 : width * 0.15,
                                                child: TextWidget("${item?.discount} ${item?.discountOn == "P" ? "%" : ""}",
                                                    textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                                              ),
                                              Radio(
                                                activeColor: appThemeColor,
                                                value: item?.id, // Use item's ID as the value
                                                groupValue: selectedRadio, // Compare with selectedRadio
                                                onChanged: (value) async {
                                                  setState(() {
                                                    // Update selectedRadio to the selected item's ID
                                                    selectedRadio = value;
                                                  });
                                                  // Call onPressItem with the selected item
                                                  widget.onPressItem!(item);
                                                },
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  divider(),
                                ],
                              )
                            : Container();
                      },
                    )
                  : Text("Coupon Not Available"),
            ),
            // Row(
            //   mainAxisAlignment: MainAxisAlignment.end,
            //   children: [
            //     RoundedButton(
            //       title: "Close",
            //       width: width > tabletWidth ? width * 0.18 : width * 0.3,
            //       onPressed: () {
            //         if (!isLock) {
            //           isLock = true;
            //           Navigator.pop(context);
            //           isLock = false;
            //         }
            //       },
            //     ),
            //   ],
            // ),
          ],
        ),
      ),
    );
  }
}
