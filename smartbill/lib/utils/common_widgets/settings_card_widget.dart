// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_widgets/divider_widget.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';

class SettingsCardWidget extends StatelessWidget {
  String? title;
  String? titleIcon;
  bool? enableDivider = true;
  IconData? rightSideicon;
  final VoidCallback? onPress;
  TextStyle? titleStyle;
  Color? rightSideiconColor;
  Color? bgColor;
  SettingsCardWidget(
      {Key? key,
      this.title,
      this.titleIcon,
      this.onPress,
      this.enableDivider,
      this.rightSideicon,
      this.titleStyle,
      this.rightSideiconColor,
      this.bgColor})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: enableDivider ?? true
          ? null
          : BoxDecoration(
              color: bgColor ?? backgroundColor,
              border: Border.all(
                color: appThemeColor, // Replace with your desired border color
                width: 2.0, // Replace with your desired border width
              ),
              borderRadius: BorderRadius.circular(20),
              // boxShadow: enableDivider ?? true ? null : [BoxShadow(blurRadius: 10, color: Colors.grey)],
            ),
      child: InkWell(
        onTap: onPress,
        child: Padding(
          padding: EdgeInsets.only(top: 10.0, right: 10.0, left: 10.0, bottom: enableDivider ?? true ? 0 : 10.0),
          child: Column(
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: <Widget>[
                  Row(
                    children: [
                      titleIcon != null
                          ? Container(
                              height: 40,
                              width: 40,
                              padding: EdgeInsets.all(10.0),
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(25), color: appThemeColor),
                              child: SvgPicture.asset(
                                titleIcon ?? "",
                                color: white,
                              ),
                            )
                          : SizedBox.shrink(),
                      SizedBox(
                        width: 10,
                      ),
                      enableDivider ?? true
                          ? TextWidget(title ?? "", textColor: primaryTextColor, fontFamily: fontMedium, fontSize: textSizeLargeMedium)
                          : Text(
                              title ?? "",
                              style: titleStyle,
                            )
                    ],
                  ),
                  rightSideicon != null
                      ? Icon(
                          rightSideicon,
                          color: rightSideiconColor ?? buttonThemeColor,
                          size: 30,
                        )
                      : iconWidget(),
                ],
              ),
              enableDivider ?? true
                  ? Padding(
                      padding: EdgeInsets.only(top: 5),
                      child: DividerWidget(),
                    )
                  : SizedBox.shrink()
            ],
          ),
        ),
      ),
    );
  }

  Widget iconWidget() {
    return SvgPicture.asset(
      leftArrowSVG,
      height: 25,
    );
  }
}
