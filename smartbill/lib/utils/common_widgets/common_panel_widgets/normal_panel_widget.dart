import 'package:flutter/material.dart';
import 'package:smartbill/model/card_models/card_model.dart';
import 'package:smartbill/utils/common_widgets/common_panel_widgets/add_new_list_item_widget.dart';
import 'package:smartbill/utils/common_widgets/common_panel_widgets/item_card_widget.dart';
import 'package:smartbill/utils/common_widgets/common_panel_widgets/list_view_card_widget.dart';
import '../../../../Repositories/image_repository.dart';
import '../../../../main.dart';
import 'add_grid_new_item_widget.dart';

class NormalPanelWidget extends StatefulWidget {
  NormalPanelWidget({
    Key? key,
    this.cardItemList,
    this.isBigLayout,
    this.onTapItem,
    this.onTabAddNewItem,
    this.isSaleLayoutTwo,
    this.onLongPressItem,
  }) : super(key: key);

  List<CardModel>? cardItemList = [];
  bool? isBigLayout;
  FunctionCallback2Param? onTapItem;
  FunctionCallbackParam? onLongPressItem;
  final VoidCallback? onTabAddNewItem;
  bool? isSaleLayoutTwo;

  @override
  State<NormalPanelWidget> createState() => _NormalPanelWidgetState();
}

class _NormalPanelWidgetState extends State<NormalPanelWidget> {
  bool isOnTapItemLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return MyApp.isListViewEnable == true
        ? Expanded(
            child: Container(
              width: width,
              alignment: Alignment.topCenter,
              child: Container(
                padding: EdgeInsets.all(5.0),
                width: width,
                child: ListView.builder(
                    padding: EdgeInsets.only(top: 5.0, bottom: 20),
                    scrollDirection: Axis.vertical,
                    physics: ScrollPhysics(),
                    itemCount: widget.cardItemList?.length,
                    shrinkWrap: true,
                    itemBuilder: (BuildContext context, int index) {
                      var item = widget.cardItemList?[index];
                      return Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 3.0,
                        ),
                        child: item?.guidId == null
                            ? AddNewListItemWidget(
                                ontab: widget.onTabAddNewItem,
                              )
                            : ListViewCardWidget(
                                item: item,
                                onTab: () async {
                                  try {
                                    if (!isOnTapItemLock) {
                                      isOnTapItemLock = true;
                                       widget.onTapItem!(item, index);
                                      isOnTapItemLock = false;
                                    }
                                  } on Exception catch (e) {
                                    isOnTapItemLock = false;
                                  }
                                },
                                onLongPress: () async {
                                   widget.onLongPressItem!(item);
                                },
                              ),
                      );
                    }),
              ),
            ),
          )
        : Expanded(
            child: widget.cardItemList?.isNotEmpty ?? false
                ? Container(
                    padding: EdgeInsets.all(5.0),
                    child: GridView.builder(
                        padding: EdgeInsets.only(bottom: 15),
                        scrollDirection: Axis.vertical,
                        physics: ScrollPhysics(),
                        itemCount: widget.cardItemList?.length,
                        shrinkWrap: true,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: fnUtilities.getProductsGridCount(widget.isBigLayout ?? false ? width * 0.5 : width),
                            crossAxisSpacing: 2,
                            mainAxisSpacing: 2,
                            childAspectRatio: 0.9),
                        itemBuilder: (BuildContext context, int index) {
                          var item = widget.cardItemList?[index];
                          return Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 3.0),
                            child: item?.guidId == null
                                ? GridAddNewItemWidget(
                                    onTab: widget.onTabAddNewItem,
                                  )
                                : ItemCardWidget(
                                    item: item,
                                    showImages: item?.isShowImage ?? false,
                                    isSaleLayoutTwo: widget.isSaleLayoutTwo,
                                    onTab: () async {
                                      if (!isOnTapItemLock) {
                                        isOnTapItemLock = true;
                                         widget.onTapItem!(item, index);
                                        isOnTapItemLock = false;
                                      }
                                    },
                                    onLongPress: () async {
                                       widget.onLongPressItem!(item);
                                    },
                                  ),
                          );
                        }),
                  )
                : Center(child: Text("Items not available")));
  }
}
