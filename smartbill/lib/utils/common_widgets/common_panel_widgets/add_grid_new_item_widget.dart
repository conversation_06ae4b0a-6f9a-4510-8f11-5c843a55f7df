import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import '../../roles_permissions/permission_functions.dart';

class GridAddNewItemWidget extends StatefulWidget {
  const GridAddNewItemWidget({Key? key, this.onTab}) : super(key: key);
  final VoidCallback? onTab;

  @override
  State<GridAddNewItemWidget> createState() => _GridAddNewItemWidgetState();
}

class _GridAddNewItemWidgetState extends State<GridAddNewItemWidget> {
  @override
  Widget build(BuildContext context) {
    return PermissionFunctions.checkPermission(104, viewToast: false) ? InkWell(
      onTap: widget.onTab,
      child: Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        decoration: boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              padding: EdgeInsets.all(5),
              child: Icon(Icons.add, color: secondaryTextColor),
            ),
            SizedBox(height: 2),
            TextWidget("Add New Item", textColor: secondaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)
          ],
        ),
      ),
    ): SizedBox.shrink();
  }
}
