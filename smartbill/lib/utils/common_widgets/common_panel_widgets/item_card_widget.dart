// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:smartbill/model/card_models/card_model.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../../Repositories/image_repository.dart';

class ItemCardWidget extends StatefulWidget {
  ItemCardWidget({Key? key, this.item, this.showImages, this.isSaleLayoutTwo, this.onTab, this.onLongPress}) : super(key: key);

  CardModel? item;
  bool? showImages;
  bool? isSaleLayoutTwo = false;
  final VoidCallback? onTab;
  final VoidCallback? onLongPress;

  @override
  State<ItemCardWidget> createState() => _ItemCardWidgetState();
}

class _ItemCardWidgetState extends State<ItemCardWidget> {
  Alignment alignmentCenter = Alignment.center;
  BoxDecoration normalDecoration = boxDecoration(radius: 10, showShadow: true, bgColor: buttonThemeColor);
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return InkWell(
      onTap: widget.onTab,
      onLongPress: widget.onLongPress,
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.only(left: 0, right: 0),
            decoration: widget.showImages ?? false
                ? widget.item?.imagePath  != null
                    ? BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(10.0)),
                        image: DecorationImage(
                          image: FileImage(widget.item!.imagePath!),
                          fit: BoxFit.cover,
                        ),
                      )
                    : normalDecoration
                : normalDecoration,
            child: Column(
              mainAxisAlignment: (widget.showImages) ?? false
                  ? widget.item?.imagePath != null
                      ? MainAxisAlignment.spaceBetween
                      : MainAxisAlignment.center
                  : MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                    width: width,
                    padding: EdgeInsets.only(right: 5),
                    alignment: widget.showImages ?? false ? (widget.item?.imagePath != null ? Alignment.topRight : alignmentCenter) : alignmentCenter,
                    child: widget.showImages ?? false
                        ? widget.item?.imagePath != null
                            ? textWithStroke(widget.item?.id.toString() ??"", textColor: primaryTextColor, fontSize: textSizeNormal, fontFamily: fontBold, isCentered: true)
                            : TextWidget(widget.item?.id.toString() ?? "", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontBold, isCentered: true)
                        : TextWidget(widget.item?.id.toString() ?? "", textColor: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontBold, isCentered: true)),
                SizedBox(height: 2),
                Column(
                  children: [
                    if (widget.item?.name != null) ...[
                      Container(
                          child: widget.showImages ?? false
                              ? (widget.item?.imagePath != null
                                  ? textWithStroke(widget.item?.name ?? "", textColor: primaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)
                                  : TextWidget(widget.item?.name ?? "", textColor: secondaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true))
                              : TextWidget(widget.item?.name ?? "", textColor: secondaryTextColor, fontSize: textSizeSmall, fontFamily: fontBold, isLongText: true, isCentered: true)),
                    ],
                    SizedBox(height: 2),
                    widget.item?.price == "" || widget.item?.price == null
                        ? TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
                        : widget.showImages ?? false
                            ? (widget.item?.imagePath != null
                                ? textWithStroke("Rs.${fnUtilities.checkQtyDoubleValue(widget.item?.price ?? "")}",
                                    textColor: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: true)
                                : TextWidget("Rs.${fnUtilities.checkQtyDoubleValue(widget.item?.price ?? "")}",
                                    textColor: secondaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: true))
                            : TextWidget("Rs.${fnUtilities.checkQtyDoubleValue(widget.item?.price ?? "")}",
                                textColor: secondaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold, isCentered: true)
                  ],
                ),
              ],
            ),
          ),
          if (widget.isSaleLayoutTwo ?? false) ...[
            widget.item?.count != null && ((widget.item?.count)! > 0)
                ? Container(
                    width: width,
                    decoration: BoxDecoration(color: Colors.black26, borderRadius: BorderRadius.all(Radius.circular(10.0))),
                    alignment: Alignment.center,
                    child: TextWidget(fnUtilities.checkQtyDoubleValue(widget.item?.count.toString() ?? ""),
                        textColor: secondaryTextColor, fontSize: textSizeLarge, fontFamily: fontSemibold, isCentered: true),
                  )
                : Container()
          ]
        ],
      ),
    );
  }
}
