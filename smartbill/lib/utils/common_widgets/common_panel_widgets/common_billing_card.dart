import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../../../main.dart';
import '../../constants/styles.dart';
import '../sales_bottom_sheet_widgets/bottom_sheet_widget_controllers/common_billing_card_controller.dart';

class CommonBillingCard extends StatefulWidget {
  final List<Map<String, dynamic>>? cardList;
  final FunctionCallbackParam? onTap;
  double? width;
  final BillingCardController controller;

  CommonBillingCard({Key? key, this.cardList, this.onTap, this.width, required this.controller}) : super(key: key);

  @override
  _CommonBillingCardState createState() => _CommonBillingCardState();
}

class _CommonBillingCardState extends State<CommonBillingCard> {
  int selectedIndex = -1; // Initialize with an invalid index
  double width = 0.0;
  @override
  void initState() {
    super.initState();
    initFunction();
  }

  initFunction() {
    widget.controller.attach(
        updateSelectedIndexCallback: selectCustomersCard,
        selectPreOrderCardCallback: selectPreOrderCard,
        changeDiscountCardCallback: selectDiscountCard);
  }

  void selectDiscountCard() {
    // Find the index of the "Customers" card in the card list
    int discountIndex = widget.cardList!.indexWhere((card) => card['name'] == 'Discount');
    if (discountIndex != -1) {
      setState(() {
        selectedIndex = -1;
      });
    }
  }

  void selectCustomersCard() {
    // Find the index of the "Customers" card in the card list
    int customersIndex = widget.cardList!.indexWhere((card) => card['name'] == 'Customers');
    if (customersIndex != -1) {
      setState(() {
        selectedIndex = customersIndex;
      });
    }
  }

  void selectPreOrderCard() {
    // Find the index of the "Pre Order" card in the card list
    int preOrderIndex = widget.cardList!.indexWhere((card) => card['name'] == 'Pre Order');
    if (preOrderIndex != -1) {
      setState(() {
        selectedIndex = preOrderIndex;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    return Stack(
      children: [
        Row(
          children: [
            SizedBox(
              width: widget.width != null ? widget.width : width * 0.90,
              child: SingleChildScrollView(
                scrollDirection: Axis.horizontal,
                child: Wrap(
                  direction: Axis.horizontal,
                  alignment: WrapAlignment.start,
                  children: widget.cardList!.asMap().entries.map((entry) {
                    final index = entry.key;
                    final card = entry.value;
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          selectedIndex = selectedIndex == index ? -1 : index;
                        });
                        if (widget.onTap != null) {
                          widget.onTap!(
                            card['name'],
                          );
                        }
                      },
                      child: SizedBox(
                        height: 75,
                        width: 115,
                        child: Card(
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          color: selectedIndex == index ? appThemeColor : null,
                          semanticContainer: true,
                          child: Padding(
                            padding: const EdgeInsets.all(8.0),
                            child: Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SvgPicture.asset(
                                  card['img'] ?? '',
                                  // ignore: deprecated_member_use
                                  color: selectedIndex == index ? secondaryTextColor : appThemeColor,
                                ),
                                SizedBox(
                                  height: 8,
                                ),
                                Text(
                                  card['name'] ?? '',
                                  style: grey14w500.copyWith(
                                      color: selectedIndex == index ? secondaryTextColor : primaryTextColor), // Change text color based on selection
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    );
                  }).toList(),
                ),
              ),
            ),
          ],
        ),
        Positioned(
          top: 5,
          right: 3,
          child: InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Icon(
              Icons.clear,
              color: greyTextColor,
              size: 25,
            ),
          ),
        ),
      ],
    );
  }
}
