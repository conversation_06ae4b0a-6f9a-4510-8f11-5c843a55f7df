import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';

class AddNewListItemWidget extends StatefulWidget {
  const AddNewListItemWidget({Key? key, this.ontab}) : super(key: key);

  final VoidCallback? ontab;

  @override
  State<AddNewListItemWidget> createState() => _AddNewListItemWidgetState();
}

class _AddNewListItemWidgetState extends State<AddNewListItemWidget> {
  @override
  Widget build(BuildContext context) {
    return PermissionFunctions.checkPermission(104, viewToast: false) ?
    InkWell(
      onTap: widget.ontab,
      child: Container(
        margin: const EdgeInsets.all(2.0),
        padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
        decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Icon(Icons.add),
            SizedBox(width: 5),
            TextWidget("Add New Item", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
          ],
        ),
      ),
    ) : SizedBox.shrink();
  }
}
