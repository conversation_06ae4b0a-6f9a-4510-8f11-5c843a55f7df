// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';

import '../constants/colors.dart';
import '../constants/styles.dart';

class NumberPanelWidget extends StatefulWidget {
  NumberPanelWidget({
    Key? key,
    this.isListview,
    this.containerWidth,
    this.prodID,
    this.onTabButton,
    this.onTabCancel,
  }) : super(key: key);

  bool? isListview;
  double? containerWidth;
  String? prodID;
  ParentFunctionCallback? onTabButton;
  final VoidCallback? onTabCancel;

  @override
  State<NumberPanelWidget> createState() => _NumberPanelWidgetState();
}

class _NumberPanelWidgetState extends State<NumberPanelWidget> {
  var sizedBox5 = SizedBox(
    height: 5,
  );
  @override
  Widget build(BuildContext context) {
    return widget.isListview ?? false
        ? Container(
            width: widget.containerWidth,
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    numberButtonWidget(1),
                    numberButtonWidget(2),
                    numberButtonWidget(3),
                    numberButtonWidget(4),
                    numberButtonWidget(5),
                  ],
                ),
                sizedBox5,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    numberButtonWidget(6),
                    numberButtonWidget(7),
                    numberButtonWidget(8),
                    numberButtonWidget(9),
                    numberButtonWidget(0),
                  ],
                ),
                numberPanelShowWidget(),
              ],
            ),
          )
        : Container(
            child: Column(
            children: [
              numberPanelShowWidget(),
              for (int i = 1; i < 8; i += 2)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    numberButtonWidget(i),
                    numberButtonWidget(i + 1),
                  ],
                ),
              sizedBox5,
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  numberButtonWidget(9),
                  numberButtonWidget(0),
                ],
              ),
            ],
          ));
  }

  Widget numberButtonWidget(int number) {
    return Container(
      child: OutlinedButton(
        onPressed: () {
          widget.onTabButton!(number);
        },
        child: Text(number.toString()),
        style: OutlinedButton.styleFrom(
          foregroundColor: buttonThemeColor, // Sets the button's text color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
        ),
      ),
    );
  }

  numberPanelShowWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Container(
          width: 60,
          alignment: Alignment.center,
          child: TextWidget(widget.prodID ?? "", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeLargeMedium, isCentered: true),
        ),
        InkWell(
          onTap: widget.onTabCancel,
          child: Container(
            alignment: Alignment.center,
            padding: EdgeInsets.all(5.0),
            width: 40,
            child: Icon(Icons.close),
          ),
        )
      ],
    );
  }
}
