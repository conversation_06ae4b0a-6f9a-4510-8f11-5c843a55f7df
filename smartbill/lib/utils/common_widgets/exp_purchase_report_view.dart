// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/screens/expenses/db_script/purchase_db_script.dart';
import 'package:smartbill/screens/reports/expense_purchase_report_view.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';
import '../../model/reports/get_expenses_report.dart';
import '../../model/reports/get_purchase_reports.dart';
import '../constants/colors.dart';
import '../constants/styles.dart';
import '../dbutils/expences_transactions_db_script.dart';
import 'common_alert_dialogue/confirmation_alert_dialog.dart';

class ReportViewWidget extends StatefulWidget {
  List<Expenses>? expenseList = [];
  List<PurchaseTransactionReport>? purchaseReportList = [];
  String? flag;
  ReportViewWidget({this.expenseList, this.purchaseReportList, this.flag});
  @override
  State<ReportViewWidget> createState() => _ReportViewWidgetState();
}

class _ReportViewWidgetState extends State<ReportViewWidget> {
  double width = 0.0;
  double height = 0.0;

  ///Update with delete function for
  ///Expenses and ExpenseTransaction
  ///Purchase and PurchaseTransaction
  updateWithDelete(String? flag, {Expenses? expenses, PurchaseTransactionReport? purchases}) async {
    if (flag == expense) {
      ExpensesTransactionDBScript expenseDBScript = ExpensesTransactionDBScript();
      await expenseDBScript.updateExpenseDelete(expenses?.expId);
      await expenseDBScript.updateExpenseTransactionDelete(expenses?.expId);
      setState(() {
        widget.expenseList?.removeWhere((element) => element.expId == expenses?.expId);
      });
    } else {
      PurchaseDBScript purchaseDBScript = PurchaseDBScript();
      await purchaseDBScript.updatePurchaseDelete(purchases?.purchaseId);
      await purchaseDBScript.updatePurchaseTransactionDelete(purchases?.purchaseId);
      setState(() {
        widget.purchaseReportList?.removeWhere((element) => element.purchaseId == purchases?.purchaseId);
      });
    }
  }

  //MESSAGE FOR PERMISSION
  // bool showMessage() {
  //   showToast("You don't have permission.");
  // }

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return Padding(
      padding: const EdgeInsets.all(2.0),
      child: Container(
        alignment: Alignment.topLeft,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Card(
              child: Container(
                color: Colors.white10,
                width: width,
                height: 40,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Container(
                      width: width * 0.22,
                      alignment: Alignment.topLeft,
                      child: TextWidget("No", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                    Container(
                      alignment: Alignment.topLeft,
                      width: width * 0.23,
                      child: TextWidget("ID", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                    Container(
                      alignment: Alignment.topLeft,
                      width: width * 0.25,
                      child: TextWidget("Date", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                    Container(
                      alignment: Alignment.topRight,
                      width: width * 0.25,
                      child: TextWidget("Amount(Rs)", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                    ),
                  ],
                ),
              ),
            ),
            if (widget.flag == expense) ...[
              reportListWidget(expReportList: widget.expenseList)
            ] else ...[
              reportListWidget(purchaseReportList: widget.purchaseReportList)
            ]
          ],
        ),
      ),
    );
  }

  reportListWidget({List<Expenses>? expReportList, List<PurchaseTransactionReport>? purchaseReportList}) {
    if (expReportList != null && expReportList.isNotEmpty) {
      return Expanded(
        child: Align(
          alignment: Alignment.topCenter,
          child: ListView.builder(
            padding: EdgeInsets.only(right: 0),
            itemCount: expReportList.length,
            physics: AlwaysScrollableScrollPhysics(),
            shrinkWrap: true,
            itemBuilder: (context, index) {
              var item = expReportList[index];
              var dateFormat = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${item.createdDate ?? DateTime.now().toString()}"));
              return buildRowWidget(item, dateFormat);
            },
          ),
        ),
      );
    } else if (purchaseReportList != null && purchaseReportList.isNotEmpty) {
      return Expanded(
        child: Align(
          alignment: Alignment.topCenter,
          child: ListView.builder(
            padding: EdgeInsets.only(right: 0),
            itemCount: purchaseReportList.length,
            shrinkWrap: true,
            physics: AlwaysScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              var item = purchaseReportList[index];
              var dateFormat = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${item.createdDate ?? DateTime.now().toString()}"));
              return buildRowWidget(item, dateFormat);
            },
          ),
        ),
      );
    } else {
      return SizedBox();
    }
  }

  buildRowWidget(var item, String? dateFormat) {
    return GestureDetector(
      onLongPress: () async {
        if (item.createdDate != null && fnUtilities.checkIsPossibletoModifyRecord(item.createdDate!, 1)) {
          bool res = PermissionFunctions.checkPermission(256, viewToast: false) || PermissionFunctions.checkPermission(258, viewToast: false);

          if (res) {
            await showDialog(
                context: context,
                builder: (BuildContext context) {
                  return ConfirmationAlertDialog(
                    content: "Are you sure you want to delete report ${item.invoiceId.toString()} ?",
                    buttonNameOne: "No",
                    ontabButtonOne: () {
                      Navigator.pop(context);
                    },
                    buttonNameTwo: "Yes",
                    ontabButtonTwo: () async {
                      bool isLock = false;
                      if (!isLock) {
                        isLock = true;
                        if (widget.flag == expense) {
                          await updateWithDelete(widget.flag, expenses: item);
                        } else {
                          await updateWithDelete(widget.flag, purchases: item);
                        }

                        Navigator.of(context).pop();
                        //Navigator.push(context, MaterialPageRoute(builder: (context) => ReportsPage()));
                        isLock = false;
                      }
                    },
                  );
                });
          } else {
            showToast("You don't have permission.");
          }
        }
      },
      child: InkWell(
        onTap: () async {
          var isLock = false;
          if (!isLock) {
            isLock = true;
            if (widget.flag == expense) {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => ExpenseReportViewPage(
                            reports: item,
                            flag: expense,
                          )));
            } else {
              Navigator.push(
                  context,
                  MaterialPageRoute(
                      builder: (context) => ExpenseReportViewPage(
                            pReports: item,
                            flag: purchase,
                          )));
            }
            isLock = false;
          }
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Container(
              margin: EdgeInsets.only(top: 10, bottom: 10),
              height: 50,
              child: Row(
                children: <Widget>[
                  SizedBox(
                    width: 4,
                  ),
                  Container(
                    width: width * 0.22,
                    alignment: Alignment.topLeft,
                    child: TextWidget(getInvoiceNo(item), fontSize: textSizeSMedium),
                  ),
                  Container(
                    width: width * 0.23,
                    alignment: Alignment.topLeft,
                    child: TextWidget(item.invoiceId != null ? item.invoiceId.toString() : "-", fontSize: textSizeSMedium),
                  ),
                  if (dateFormat != null) ...[
                    Flexible(
                      child: Container(
                        width: width * 0.25,
                        alignment: Alignment.topLeft,
                        child: TextWidget(
                          dateFormat,
                          textColor: textSecondaryColor,
                          fontSize: textSizeSmall,
                          fontFamily: fontSemibold,
                          maxLine: 2,
                        ),
                      ),
                    ),
                  ],
                  if (item.totalAmount != null) ...[
                    Container(
                      width: width * 0.25,
                      alignment: Alignment.topRight,
                      child: TextWidget(fnUtilities.checkQtyDoubleValue(item.totalAmount.toString()),
                          textColor: primaryTextColor, fontSize: textSizeSMedium, fontFamily: fontSemibold),
                    )
                  ],
                ],
              ),
            ),
            Divider(height: 0.5, color: t5ViewColor),
          ],
        ),
      ),
    );
  }
}

String getInvoiceNo(
  dynamic model,
) {
  String invoiceFullNo = "-";
  if (model.invoiceNo != null) {
    invoiceFullNo = model.invoiceNo.toString();
  } else if (model.invoiceNo != null) {
    invoiceFullNo = model.invoiceNo.toString();
    if (model.invoiceExtension != null) {
      invoiceFullNo = invoiceFullNo + model.invoiceExtension;
    }
  }
  return invoiceFullNo;
}
