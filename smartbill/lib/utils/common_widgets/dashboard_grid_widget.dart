import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

import 'text_widget.dart';

dashBoardRowGrid(double contentWidth, String title, String iconUrl, VoidCallback onPress) {
  return InkWell(
    onTap: onPress,
    child: Padding(
      padding: EdgeInsets.only(top: 5.0, bottom: 5.0),
      child: Container(
        width: contentWidth * 0.3,
        height: 100,
        alignment: Alignment.center,
        decoration: boxDecoration(radius: 10, showShadow: true, bgColor: appThemeColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Container(
              child: SvgPicture.asset(
                iconUrl,
                color: secondaryTextColor,
              ),
            ),
            Container(
              // alignment: Alignment.center,
              width: contentWidth * 0.27,
              child: TextWidget(
                title,
                maxLine: 2,
                textColor: secondaryTextColor,
                fontSize: textSizeMedium,
                isCentered: true,
              ),
            )
          ],
        ),
      ),
    ),
  );
}

dashBoardColumnGrid(double contentWidth, String title, String iconUrl, VoidCallback onPress) {
  return InkWell(
    onTap: onPress,
    child: Padding(
      padding: EdgeInsets.only(top: 5.0, bottom: 5.0),
      child: Container(
        width: contentWidth * 0.7,
        height: 100,
        alignment: Alignment.center,
        decoration: boxDecoration(radius: 10, showShadow: true, bgColor: appThemeColor),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            Container(
              child: SvgPicture.asset(
                iconUrl,
                color: secondaryTextColor,
                height: 50,
                width: 50,
              ),
            ),
            SizedBox(
              width: 20,
            ),
            Container(
              width: contentWidth * 0.4,
              child: TextWidget(
                title,
                maxLine: 2,
                textColor: secondaryTextColor,
                fontSize: textSizeMedium,
                isCentered: true,
              ),
            )
          ],
        ),
      ),
    ),
  );
}

totalSalesCardWidget({String amount = '0.0'}) {
  double parsedAmount = double.tryParse(amount)??0.0;
  String formattedAmount = parsedAmount.toStringAsFixed(2);
  return Padding(
    padding: EdgeInsets.all(20.0),
    child: Container(
      width: double.infinity,
      height: 100,
      alignment: Alignment.center,
      decoration: boxDecoration(radius: 10, showShadow: true, bgColor: purpleColor),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          TextWidget(
            "Today Sales : ₹ $formattedAmount",
            textColor: secondaryTextColor,
            fontSize: textSizeLargeMedium,
            fontFamily: fontMedium,
            fontWeight: FontWeight.bold,
          )
        ],
      ),
    ),
  );
}
