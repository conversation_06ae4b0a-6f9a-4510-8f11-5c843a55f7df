// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

class TextFieldBoxWidget extends StatefulWidget {
  var isPassword;
  var isSecure;
  var fontSize;
  var textColor;
  var fontFamily;
  var text;
  var hint;
  var maxLine;
  var maxLength;
  var inputType;
  var inputFormate;
  var suffixIcon;
  var errorText;
  var validator;
  var autofocus;
  var focus;
  var readOnly;
  var enable;
  var inputAction;
  var suffix;
  Function(String)? submit;
  Function(String)? onChange;
  Function()? onTabClear;
  TextEditingController? mController;
  var textAlign;
  void Function(PointerDownEvent)? onTapOutside;

  TextFieldBoxWidget(
      {this.fontSize = textSizeNormal,
      this.textColor = primaryTextColor,
      this.fontFamily = fontRegular,
      this.isPassword = true,
      this.hint = "",
      this.isSecure = false,
      this.text = "",
      this.mController,
      this.maxLine = 1,
      this.enable = true,
      this.maxLength,
      this.inputType,
      this.suffixIcon,
      this.errorText,
      this.validator,
      this.focus,
      this.inputFormate,
      this.inputAction,
      this.submit,
      this.onChange,
      this.autofocus = false,
      this.readOnly = false,
      this.suffix = false,
      this.textAlign,
      this.onTabClear,
      this.onTapOutside});

  @override
  State<TextFieldBoxWidget> createState() => _TextFieldBoxWidgetState();
}

class _TextFieldBoxWidgetState extends State<TextFieldBoxWidget> {
  VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      autofocus: widget.autofocus,
      readOnly: widget.readOnly,
      controller: widget.mController,
      obscureText: widget.isPassword,
      maxLength: widget.maxLength,
      focusNode: widget.focus,
      enabled: widget.enable,
      keyboardType: widget.inputType,
      inputFormatters: widget.inputFormate,
      validator: widget.validator,
      textInputAction: widget.inputAction,
      onFieldSubmitted: widget.submit,
      onChanged: widget.onChange,
      onTapOutside: widget.onTapOutside,
      textAlign: widget.textAlign == null ? TextAlign.start : widget.textAlign,
      style: TextStyle(color: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontRegular),
      decoration: InputDecoration(
        prefixText: widget.maxLength == 10 ? "+91" : "",
        errorText: widget.errorText,
        labelText: widget.text,
        suffixIcon: widget.isSecure
            ? GestureDetector(
                onTap: () {
                  setState(() {
                    widget.isPassword = !widget.isPassword;
                  });
                },
                child: new Icon(
                  widget.isPassword ? Icons.visibility_off : Icons.visibility,
                  color: appThemeColor,
                ),
              )
            : null,
        suffix: widget.suffix
            ? Padding(
                padding: const EdgeInsets.only(
                  top: 15.0,
                ),
                child: GestureDetector(
                  onTap: widget.onTabClear,
                  child: new Icon(
                    Icons.close,
                    color: appThemeColor,
                  ),
                ),
              )
            : null,
        contentPadding: EdgeInsets.fromLTRB(16, 10, 16, 10),
        hintText: widget.hint,
        hintStyle: TextStyle(color: greyTextColor),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: greyTextColor, width: 0.0),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: appThemeColor, width: 0.0),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: redColor, width: 0.0),
        ),
        focusedErrorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(4),
          borderSide: const BorderSide(color: redColor, width: 0.0),
        ),
      ),
    );
  }
}
