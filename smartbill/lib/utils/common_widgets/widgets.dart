import 'dart:io';

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../main.dart';
import '../constants/colors.dart';

class EditText extends StatefulWidget {
  var isPassword;
  var isSecure;
  var showcursor;
  var fontSize;
  var textColor;
  var fontFamily;
  var text;
  var hint;
  var maxLine;
  var maxLength;
  var inputType;
  var inputFormate;
  var suffixIcon;
  var errorText;
  var validator;
  var autofocus;
  var focus;
  var readOnly;
  var enable;
  var inputAction;
  Function(String)? submit;
  Function(String)? onChange;
  TextEditingController? mController;
  var textAlign;
  VoidCallback? onTap;
  VoidCallback? onPressed;
  Color? borderColor;
  double? focusBorderWidth;
  bool isEnableErrorBorder;
  Color? errorBorderColor;
  void Function(PointerDownEvent)? onTapOutside;
  EditText(
      {var this.fontSize = textSizeNormal,
      var this.textColor = primaryTextColor,
      var this.fontFamily = fontRegular,
      var this.isPassword = true,
      var this.showcursor = true,
      var this.hint = "",
      var this.isSecure = false,
      var this.text = "",
      var this.mController,
      var this.maxLine = 1,
      var this.enable = true,
      this.maxLength,
      this.inputType,
      this.suffixIcon,
      this.errorText,
      this.validator,
      this.focus,
      this.inputFormate,
      this.inputAction,
      this.submit,
      this.onChange,
      this.onTap,
      var this.autofocus = false,
      var this.readOnly = false,
      this.textAlign,
      this.onPressed,
      this.borderColor,
      this.focusBorderWidth,
      this.errorBorderColor,
      this.isEnableErrorBorder = false,
      this.onTapOutside});

  @override
  State<StatefulWidget> createState() {
    return EditTextState();
  }
}

class EditTextState extends State<EditText> {
  @override
  Widget build(BuildContext context) {
    return TextFormField(
        onTapOutside: widget.onTapOutside,
        autofocus: widget.autofocus,
        readOnly: widget.readOnly,
        controller: widget.mController,
        obscureText: widget.isPassword,
        maxLength: widget.maxLength,
        maxLines: widget.maxLine,
        focusNode: widget.focus,
        enabled: widget.enable,
        keyboardType: widget.inputType,
        inputFormatters: widget.inputFormate,
        validator: widget.validator,
        textInputAction: widget.inputAction,
        onFieldSubmitted: widget.submit,
        onChanged: widget.onChange,
        onTap: widget.onTap,
        textAlign: widget.textAlign == null ? TextAlign.start : widget.textAlign,
        style: TextStyle(color: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontRegular),
        decoration: InputDecoration(
            prefixText: widget.maxLength == 10 ? "+91" : "",
            errorText: widget.errorText,
            labelText: widget.text,
            suffixIcon: widget.isSecure
                ? GestureDetector(
                    onTap: () {
                      setState(() {
                        widget.isPassword = !widget.isPassword;
                      });
                    },
                    child: new Icon(
                      widget.isPassword ? Icons.visibility_off : Icons.visibility,
                      color: primaryTextColor,
                    ),
                  )
                : widget.suffixIcon,
            contentPadding: EdgeInsets.fromLTRB(16, 10, 16, 10),
            hintText: widget.hint,
            hintStyle: TextStyle(color: primaryTextColor),
            alignLabelWithHint: true,
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: widget.borderColor ?? t5ViewColor, width: 0.0),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(4),
              borderSide: BorderSide(color: t5ViewColor, width: widget.focusBorderWidth ?? 0.0),
            ),
            errorBorder: widget.isEnableErrorBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: widget.errorBorderColor ?? Colors.red, width: widget.focusBorderWidth ?? 0.0))
                : null,
            focusedErrorBorder: widget.isEnableErrorBorder
                ? OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4),
                    borderSide: BorderSide(color: widget.errorBorderColor ?? Colors.red, width: widget.focusBorderWidth ?? 0.0),
                  )
                : null));
  }

  @override
  State<StatefulWidget>? createState() {
    return null;
  }
}

Widget divider() {
  return Divider(
    height: 0.5,
    color: t5ViewColor,
  );
}

class CustomTheme extends StatelessWidget {
  final Widget? child;

  CustomTheme({@required this.child});

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData.light(),
      //  appStore.isDarkModeOn
      //     ? ThemeData.light()
      //     : ThemeData.dark().copyWith(
      //         accentColor: appColorPrimary,
      //         backgroundColor: secondaryTextColor,
      //       ),
      child: child!,
    );
  }
}

text(
  String text, {
  var fontSize = textSizeLargeMedium,
  Color? textColor,
  var fontFamily,
  var isCentered = false,
  var maxLine = 1,
  FontWeight? fontWeight,
  var latterSpacing = 0.5,
  bool textAllCaps = false,
  var isLongText = false,
  bool lineThrough = false,
}) {
  return Text(
    textAllCaps ? text.toUpperCase() : text,
    textAlign: isCentered ? TextAlign.center : TextAlign.start,
    maxLines: isLongText ? null : maxLine,
    overflow: TextOverflow.clip,
    style: TextStyle(
      fontFamily: fontFamily ?? null,
      fontSize: fontSize,
      color: textColor ?? primaryTextColor,
      fontWeight: fontWeight,
      height: 1.5,
      letterSpacing: latterSpacing,
      decoration: lineThrough ? TextDecoration.lineThrough : TextDecoration.none,
    ),
  );
}

Widget textWithStroke(
  String text, {
  var fontSize = textSizeLargeMedium,
  Color? textColor,
  var fontFamily,
  var isCentered = false,
  var maxLine = 1,
  var latterSpacing = 0.5,
  bool textAllCaps = false,
  var isLongText = false,
  bool lineThrough = false,
}) {
  return Stack(
    children: [
      Text(
        textAllCaps ? text.toUpperCase() : text,
        textAlign: isCentered ? TextAlign.center : TextAlign.start,
        maxLines: isLongText ? null : maxLine,
        overflow: TextOverflow.clip,
        style: TextStyle(
          fontFamily: fontFamily ?? null,
          fontSize: fontSize,
          height: 1.5,
          letterSpacing: latterSpacing,
          decoration: lineThrough ? TextDecoration.lineThrough : TextDecoration.none,
          foreground: Paint()
            ..style = PaintingStyle.stroke
            ..strokeWidth = 3
            ..color = Colors.white,
        ),
      ),
      Text(
        textAllCaps ? text.toUpperCase() : text,
        textAlign: isCentered ? TextAlign.center : TextAlign.start,
        maxLines: isLongText ? null : maxLine,
        overflow: TextOverflow.clip,
        style: TextStyle(
          fontFamily: fontFamily ?? null,
          fontSize: fontSize,
          height: 1.5,
          letterSpacing: latterSpacing,
          color: textColor ?? primaryTextColor,
          decoration: lineThrough ? TextDecoration.lineThrough : TextDecoration.none,
        ),
      )
    ],
  );
}

BoxDecoration boxDecoration({double radius = 2, Color color = Colors.transparent, Color? bgColor, var showShadow = false}) {
  return BoxDecoration(
    color: bgColor ?? secondaryTextColor,
    boxShadow: showShadow ? defaultBoxShadow(shadowColor: shadowColorGlobal) : [BoxShadow(color: Colors.transparent)],
    border: Border.all(color: color),
    borderRadius: BorderRadius.all(Radius.circular(radius)),
  );
}

Widget showTo(
  String text, {
  var fontSize = textSizeLargeMedium,
  Color? textColor,
  var fontFamily,
  var isCentered = false,
  var maxLine = 1,
  FontWeight? fontWeight,
  var latterSpacing = 0.5,
  bool textAllCaps = false,
  var isLongText = false,
  bool lineThrough = false,
}) {
  return Text(
    textAllCaps ? text.toUpperCase() : text,
    textAlign: isCentered ? TextAlign.center : TextAlign.start,
    maxLines: isLongText ? null : maxLine,
    overflow: TextOverflow.clip,
    style: TextStyle(
      fontFamily: fontFamily ?? null,
      fontSize: fontSize,
      color: textColor ?? primaryTextColor,
      fontWeight: fontWeight,
      height: 1.5,
      letterSpacing: latterSpacing,
      decoration: lineThrough ? TextDecoration.lineThrough : TextDecoration.none,
    ),
  );
}

void showToast(
  String? text, {
  ToastGravity? gravity,
  dynamic length = Toast.LENGTH_SHORT,
  Color? bgColor,
  Color? textColor,
  bool print = false,
}) {
  if (Platform.isWindows) {
    // Show snackbar Instead of toast
    bool isBigLayout = false;
    double width = MediaQuery.of(snackbarKey.currentState!.context).size.width;
    isBigLayout = width > tabletWidth;
    final SnackBar snackBar = SnackBar(
      width: isBigLayout ? width * 0.5 : width,
      backgroundColor: Colors.grey[600],
      behavior: SnackBarBehavior.floating,
      content: Container(
          // constraints: BoxConstraints(minWidth: ),
          child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(text ?? ""),
        ],
      )),
      duration: Duration(seconds: 2),
    );
    snackbarKey.currentState?.showSnackBar(snackBar);
  } else {
    toast(text ?? "");
  }
}
