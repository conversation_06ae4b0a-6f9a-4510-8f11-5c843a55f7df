import 'package:flutter/material.dart';
import '../../constants/colors.dart';
import '../widgets.dart';

class DeleteDialog extends StatefulWidget {
  String? message;
  String? buttonNameOne;
  String? buttonNameTwo;
  final VoidCallback? onPressOne;
  final VoidCallback? onPressTwo;

  DeleteDialog({this.message, this.onPressOne, this.onPressTwo, this.buttonNameOne, this.buttonNameTwo});

  @override
  State<DeleteDialog> createState() => _DeleteDialogState();
}

class _DeleteDialogState extends State<DeleteDialog> {
  double width = 0.0;
  double height = 0.0;
  bool isBigLayout = false;

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return AlertDialog(
      content: Text(widget.message ?? ''),
      actions: <Widget>[
        GestureDetector(
          onTap: widget.onPressOne,
          child: Container(
            alignment: Alignment.center,
            height: 40,
            width: width / 5,
            child: text(widget.buttonNameOne ?? '', textColor: secondaryTextColor, isCentered: true),
            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
          ),
        ),
        GestureDetector(
          onTap: widget.onPressTwo,
          child: Container(
            alignment: Alignment.center,
            height: 40,
            width: width / 5,
            child: text(widget.buttonNameTwo ?? '', textColor: secondaryTextColor, isCentered: true),
            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
          ),
        ),
      ],
    );
  }
}
