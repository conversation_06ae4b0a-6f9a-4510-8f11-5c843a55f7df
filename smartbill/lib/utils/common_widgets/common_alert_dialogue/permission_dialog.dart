import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../constants/styles.dart';
import '../widgets.dart';

class PermisionAlertDialog extends StatefulWidget {
  PermisionAlertDialog({
    this.content,
    this.buttonNameOne,
    this.buttonNameTwo,
    this.onTabButtonOne,
    this.onTabButtonTwo,
    Key? key,
  }) : super(key: key);

  String? content;
  String? buttonNameOne;
  String? buttonNameTwo;
  final VoidCallback? onTabButtonOne;
  final VoidCallback? onTabButtonTwo;

  @override
  State<PermisionAlertDialog> createState() => _PermisionAlertDialogState();
}

class _PermisionAlertDialogState extends State<PermisionAlertDialog> {
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return AlertDialog(
      content: Container(
        width: width > tabletWidth ? width * 0.25 : width * 0.7,
        height: 100,
        child: Column(
          children: [
            Text(
              widget.content ?? "",
              style: TextStyle(
                color: primaryTextColor,
                fontSize: 18.0,
              ),
            ),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                GestureDetector(
                  onTap: widget.onTabButtonOne,
                  child: Container(
                    alignment: Alignment.center,
                    // height: 50,
                    // width: 150,
                    child: TextWidget(widget.buttonNameOne ??"",
                        fontSize: textSizeMedium,
                        textColor: buttonThemeColor,
                        isCentered: true),
                    decoration: boxDecoration(radius: 8.0),
                  ),
                ),
                SizedBox(
                  width: 40,
                ),
                GestureDetector(
                  onTap: widget.onTabButtonTwo,
                  child: Container(
                    alignment: Alignment.center,
                    // height: 50,
                    // width: 40,
                    child: TextWidget(widget.buttonNameTwo ?? "",
                        textColor: buttonThemeColor, isCentered: true),
                    decoration: boxDecoration(radius: 8.0),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
