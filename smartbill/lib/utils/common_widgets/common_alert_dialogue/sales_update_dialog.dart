import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';

import '../../../main.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class SaleUpdateDialog extends StatefulWidget {
  SaleUpdateDialog({
    Key? key,
    this.item,
    this.qtyController,
    this.priceController,
    this.notesController,
    this.onPressUpdate,
  }) : super(key: key);

  Transactions? item;
  TextEditingController? qtyController = TextEditingController();
  TextEditingController? priceController = TextEditingController();
  TextEditingController? notesController = TextEditingController();
  FunctionCallback5Param? onPressUpdate;

  @override
  State<SaleUpdateDialog> createState() => _SaleUpdateDialogState();
}

class _SaleUpdateDialogState extends State<SaleUpdateDialog> {
  Validator validator = new Validator();
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Center(
      child: SingleChildScrollView(
        child: AlertDialog(
          content: Container(
            width: width > tabletWidth ? width * 0.4 : width * 0.8,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: <Widget>[
                Container(
                  child: Text(
                    "${widget.item?.name}",
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                SizedBox(height: 20.0),
                Form(
                  key: validator.formkey,
                  child: Column(
                    children: [
                      Container(
                        child: EditText(
                          mController: widget.qtyController!,
                          inputType: TextInputType.number,
                          inputFormate: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9.]')),
                          ],
                          validator: validator.validateQuantity,
                          isPassword: false,
                          text: "Quantity",
                          enable: PermissionFunctions.checkPermission(112)? true : false ,
                        ),
                      ),
                      SizedBox(height: 20.0),
                      Container(
                        child: EditText(
                          mController: widget.priceController!,
                          inputType: TextInputType.number,
                          inputFormate: [
                            FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9.]')),
                          ],
                          isPassword: false,
                          text: "Price",
                          enable: PermissionFunctions.checkPermission(113,viewToast: false)? true : false ,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                      Container(
                        child: EditText(
                          mController: widget.notesController!,
                          isPassword: false,
                          text: "Notes",
                          maxLine: 3,
                          inputType: TextInputType.multiline,
                          enable: PermissionFunctions.checkPermission(114)? true : false ,
                        ),
                      ),
                      SizedBox(
                        height: 20,
                      ),
                    ],
                  ),
                ),
                SizedBox(
                  height: 40,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    RoundedButton(
                      title: "Cancel",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () {
                        if (!isLock) {
                          isLock = true;
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                    ),
                    RoundedButton(
                      title: "Update",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () async {
                        widget.onPressUpdate!(
                          widget.item,
                          widget.qtyController?.text,
                          widget.priceController?.text,
                          widget.notesController?.text,
                          validator,
                        );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
