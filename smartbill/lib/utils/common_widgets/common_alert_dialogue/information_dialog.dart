import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../constants/styles.dart';

class InformationDialog extends StatefulWidget {
  InformationDialog({
    Key? key,
    this.content,
  }) : super(key: key);
  String? content;

  @override
  State<InformationDialog> createState() => _InformationDialogState();
}

class _InformationDialogState extends State<InformationDialog> {
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Container(
      width: width > tabletWidth ? width * 0.4 : width * 0.8,
      child: AlertDialog(
        content: Container(
          // width: 200,
          // height: 20,
          child: Text(
            widget.content ?? "",
            style: TextStyle(color: primaryTextColor, fontSize: 18.0),
          ),
        ),
        actions: [
          Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              RoundedButton(
                  title: "Close",
                  width: width > tabletWidth ? width * 0.18 : width * 0.3,
                  onPressed: () {
                    if (!isLock) {
                      isLock = true;
                      Navigator.pop(context);
                      isLock = false;
                    }
                  }),
            ],
          ),
        ],
      ),
    );
  }
}
