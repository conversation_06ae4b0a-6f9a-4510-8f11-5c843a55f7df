import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';

import '../../../main.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class DynamicQtyDialog extends StatefulWidget {
  DynamicQtyDialog({
    Key? key,
    this.qtyController,
    this.priceController,
    this.onTabOK,
  }) : super(key: key);

  TextEditingController? qtyController = TextEditingController();
  TextEditingController? priceController = TextEditingController();
  FunctionCallback2Param? onTabOK;

  @override
  State<DynamicQtyDialog> createState() => _DynamicQtyDialogState();
}

class _DynamicQtyDialogState extends State<DynamicQtyDialog> {
  Validator validator = new Validator();

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return AlertDialog(
        content: Container(
      width: width > tabletWidth ? width * 0.4 : width * 0.8,
      // height: 220,
      child: Form(
        key: validator.formkey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Container(
              child: EditText(
                mController: widget.qtyController!,
                inputType: TextInputType.number,
                inputFormate: [
                  FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                ],
                isPassword: false,
                text: "Qty",
                validator: validator.validateQuantity,
                autofocus: true,
              ),
            ),
            SizedBox(
              height: 10,
            ),
            widget.priceController?.text == ""
                ? Container(
                    child: EditText(
                      mController: widget.priceController!,
                      inputType: TextInputType.number,
                      inputFormate: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                      ],
                      isPassword: false,
                      text: "Price",
                      validator: validator.validateTextField,
                      autofocus: true,
                    ),
                  )
                : Container(),
            SizedBox(
              height: 20,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                RoundedButton(
                  title: "Cancel",
                  width: width > tabletWidth ? width * 0.18 : width * 0.3,
                  onPressed: () {
                    widget.qtyController?.clear();
                    widget.priceController?.clear();
                    Navigator.pop(context);
                  },
                ),
                RoundedButton(
                  title: "Ok",
                  width: width > tabletWidth ? width * 0.18 : width * 0.3,
                  onPressed: () {
                    validator.validate();
                    if (validator.validate() ?? false) {
                      widget.onTabOK!(widget.qtyController?.text, widget.priceController?.text);
                    }
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    ));
  }
}
