import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../main.dart';
import '../../../model/discounts.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class CouponDiscountDialog extends StatefulWidget {
  CouponDiscountDialog(
      {Key? key, this.discountList, this.discountType, this.onPressItem})
      : super(key: key);

  List<Discounts>? discountList;
  String? discountType;
  FunctionCallbackParam? onPressItem;

  @override
  State<CouponDiscountDialog> createState() => _CouponDiscountDialogState();
}

class _CouponDiscountDialogState extends State<CouponDiscountDialog> {
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return StatefulBuilder(builder: (context, setState) {
      return Container(
        child: AlertDialog(
          title:
              Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Container(
              child: TextWidget("Coupons",
                  textColor: primaryTextColor,
                  fontFamily: fontBold,
                  fontSize: textSizeLarge),
            ),
          ]),
          content: Container(
            width: width > tabletWidth ? width * 0.4 : width * 0.9,
            height: 300,
            child: Column(
              children: [
                Container(
                  height: 250,
                  child: (widget.discountList?.length)! > 0
                      ? ListView.builder(
                          shrinkWrap: true,
                          itemCount: widget.discountList?.length,
                          itemBuilder: (BuildContext context, int index) {
                            var item = widget.discountList?[index];
                            String type =
                                widget.discountType == "Sales" ? "S" : "P";
                            return type == item?.type
                                ? Container(
                                    height: 40,
                                    child: Column(
                                      children: [
                                        Padding(
                                          padding: const EdgeInsets.all(4.0),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: <Widget>[
                                              InkWell(
                                                onTap: () {
                                                   widget.onPressItem!(item);
                                                },
                                                child: Row(
                                                  children: [
                                                    Container(
                                                      width: width > tabletWidth
                                                          ? width * 0.28
                                                          : width * 0.45,
                                                      child: TextWidget(
                                                          item?.couponName !=
                                                                  null
                                                              ? item?.couponName ?? ""
                                                              : "",
                                                          textColor:
                                                              primaryTextColor,
                                                          fontFamily: fontBold,
                                                          fontSize:
                                                              textSizeMedium),
                                                    ),
                                                    Container(
                                                      width: width > tabletWidth
                                                          ? width * 0.1
                                                          : width * 0.15,
                                                      child: TextWidget(
                                                          "${item?.discount} ${item?.discountOn == "P" ? "%" : ""}",
                                                          textColor:
                                                              primaryTextColor,
                                                          fontFamily: fontBold,
                                                          fontSize:
                                                              textSizeMedium),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        divider(),
                                      ],
                                    ),
                                  )
                                : Container();
                          },
                        )
                      : Text("Coupon Not Available"),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    RoundedButton(
                      title: "Close",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () {
                        if (!isLock) {
                          isLock = true;
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
