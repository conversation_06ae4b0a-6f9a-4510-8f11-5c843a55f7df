import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../model/toppings_model.dart';
import '../../constants/styles.dart';
import '../text_widget.dart';
import '../widgets.dart';

class ToppingDialog extends StatefulWidget {
  ToppingDialog({
    Key? key,
    this.productToppingList,
    this.productToppingNewList,
    this.onTabok,
    this.isBigLayout,
  }) : super(key: key);

  List<Topping>? productToppingList;
  List<Topping>? productToppingNewList;
  final VoidCallback? onTabok;
  bool? isBigLayout;

  @override
  State<ToppingDialog> createState() => _ToppingDialogState();
}

class _ToppingDialogState extends State<ToppingDialog> {
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return StatefulBuilder(builder: (context, setState) {
      return AlertDialog(
          contentPadding: EdgeInsets.all(10),
          scrollable: true,
          content: SingleChildScrollView(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: width > tabletWidth ? width * 0.4 : width * 0.9,
                  height: 300,
                  child: ListView.builder(
                      itemCount: widget.productToppingList?.length,
                      padding: EdgeInsets.zero,
                      shrinkWrap: true,
                      itemBuilder: (BuildContext context, int index) {
                        var item = widget.productToppingList?[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 5.0),
                          child: Container(
                            margin: const EdgeInsets.all(0),
                            padding: EdgeInsets.zero,
                            decoration: boxDecoration(
                                radius: 10,
                                showShadow: true,
                                bgColor: secondaryTextColor),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Container(
                                  width: widget.isBigLayout ?? false
                                      ? width * 0.35
                                      : width * 0.7,
                                  child: new CheckboxListTile(
                                      dense: false,
                                      controlAffinity:
                                          ListTileControlAffinity.leading,
                                      contentPadding: EdgeInsets.zero,
                                      value: item?.isActive,
                                      title: Row(
                                        children: [
                                          Container(
                                            width: widget.isBigLayout ?? false
                                                ? width * 0.2
                                                : width * 0.35,
                                            alignment: Alignment.centerLeft,
                                            child: TextWidget(item?.toppingName ?? "",
                                                textColor: primaryTextColor,
                                                fontSize: textSizeMedium,
                                                fontFamily: fontSemibold,
                                                isLongText: true),
                                          ),
                                          Container(
                                            width: widget.isBigLayout ?? false
                                                ? width * 0.1
                                                : width * 0.18,
                                            alignment: Alignment.centerRight,
                                            child: item?.toppingPrice != ""
                                                ? TextWidget(
                                                    "Rs.${item?.toppingPrice}",
                                                    textColor: primaryTextColor,
                                                    fontSize: textSizeMedium,
                                                    fontFamily: fontSemibold)
                                                : TextWidget("",
                                                    textColor: primaryTextColor,
                                                    fontSize:
                                                        textSizeLargeMedium,
                                                    fontFamily: fontSemibold),
                                          ),
                                        ],
                                      ),
                                      onChanged: (bool? newValue) {
                                        if (!isLock) {
                                          isLock = true;
                                          setState(() {
                                            item?.isActive = newValue;
                                            if (item?.isActive == true) {
                                              widget.productToppingNewList!
                                                  .add(item!);
                                            } else {
                                              widget.productToppingNewList!
                                                  .removeWhere((element) =>
                                                      element.toppingId ==
                                                      item?.toppingId);
                                            }
                                          });
                                          isLock = false;
                                        }
                                      }),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    RoundedButton(
                      title: "Cancel",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () {
                        if (!isLock) {
                          isLock = true;
                          widget.productToppingNewList?.clear();
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                    ),
                    RoundedButton(
                      title: "Ok",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: widget.onTabok!,
                    ),
                  ],
                ),
              ],
            ),
          ));
    });
  }
}
