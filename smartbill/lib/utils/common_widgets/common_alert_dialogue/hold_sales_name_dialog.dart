import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';

import '../../../main.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class HoldSalesNameDialog extends StatefulWidget {
  HoldSalesNameDialog({
    Key? key,
    this.holdSalesController,
    this.onPressOk,
  }) : super(key: key);

  TextEditingController? holdSalesController = TextEditingController();
  FunctionCallbackParam? onPressOk;

  @override
  State<HoldSalesNameDialog> createState() => _HoldSalesNameDialogState();
}

class _HoldSalesNameDialogState extends State<HoldSalesNameDialog> {
  Validator validator = new Validator();
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Center(
      child: SingleChildScrollView(
        child: AlertDialog(
            content: Container(
          // width: double.maxFinite,
          width: width > tabletWidth ? width * 0.4 : width * 0.9,
          child: Form(
            key: validator.formkey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  child: EditText(
                    mController: widget.holdSalesController!,
                    inputType: TextInputType.name,
                    isPassword: false,
                    text: "Sales Name",
                    validator: validator.validateTextField,
                    autofocus: true,
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    RoundedButton(
                      title: "Cancel",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                    ),
                    RoundedButton(
                      title: "Ok",
                      width: width > tabletWidth ? width * 0.18 : width * 0.3,
                      onPressed: () async {
                        widget.onPressOk!(validator);
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        )),
      ),
    );
  }
}
