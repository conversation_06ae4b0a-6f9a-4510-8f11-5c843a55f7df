import 'package:flutter/material.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../../../main.dart';
import '../../../model/products.dart';
import '../text_widget.dart';
import '../widgets.dart';

class UnitMeasurementDialog extends StatefulWidget {
  UnitMeasurementDialog({
    Key? key,
    this.isBigLayout,
    this.productUnitList,
    this.onTabItem,
    this.isSaleLayoutTwo,
  }) : super(key: key);

  bool? isBigLayout;
  List<PriceUnitType>? productUnitList;
  FunctionCallbackParam? onTabItem;
  bool? isSaleLayoutTwo;

  @override
  State<UnitMeasurementDialog> createState() => _UnitMeasurementDialogState();
}

class _UnitMeasurementDialogState extends State<UnitMeasurementDialog> {
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return AlertDialog(
        scrollable: true,
        content: Container(
          width: width > tabletWidth ? width * 0.4 : width * 0.8,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: widget.isSaleLayoutTwo ?? false ? width * 0.9 : (widget.isBigLayout ?? false ? width * 0.35 : width * 0.9),
                height: widget.isBigLayout ?? false ? 300 : 250,
                // width: double.maxFinite,
                child: ListView.builder(
                    itemCount: widget.productUnitList?.length,
                    shrinkWrap: true,
                    itemBuilder: (BuildContext context, int index) {
                      var item = widget.productUnitList?[index];
                      return InkWell(
                        onTap: () async {
                           widget.onTabItem!(item);
                        },
                        child: Container(
                          margin: const EdgeInsets.all(2.0),
                          padding: EdgeInsets.only(left: 5, right: 5, top: 10, bottom: 10),
                          decoration: boxDecoration(radius: 10, showShadow: true, bgColor: secondaryTextColor),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceAround,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Container(
                                width: widget.isBigLayout ?? false? width * 0.2 : width * 0.4,
                                alignment: Alignment.centerLeft,
                                child: TextWidget(item?.measurement ?? "", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold, isLongText: true),
                              ),
                              SizedBox(height: 1),
                              Container(
                                width: widget.isBigLayout ?? false ? width * 0.1 : width * 0.2,
                                alignment: Alignment.centerRight,
                                child: item?.measurementPrice != "" && item?.measurementPrice != null
                                    ? TextWidget("Rs.${item?.measurementPrice}", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold)
                                    : TextWidget("", textColor: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontSemibold),
                              ),
                            ],
                          ),
                        ),
                      );
                    }),
              ),
            ],
          ),
        ));
  }
}
