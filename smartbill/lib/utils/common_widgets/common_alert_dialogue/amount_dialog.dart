import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';

import '../../../main.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class AmountAlertDialog extends StatefulWidget {
  AmountAlertDialog({Key? key, this.priceController, this.onPresOk})
      : super(key: key);

  TextEditingController? priceController = TextEditingController();
  FunctionCallbackParam? onPresOk;

  @override
  State<AmountAlertDialog> createState() => _AmountAlertDialogState();
}

class _AmountAlertDialogState extends State<AmountAlertDialog> {
  Validator validator = new Validator();
  bool isLock = false;

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return Center(
      child: SingleChildScrollView(
        child: AlertDialog(
            content: Container(
          width: width > tabletWidth ? width * 0.4 : width * 0.8,
          child: Form(
            key: validator.formkey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Container(
                  child: EditText(
                    mController: widget.priceController!,
                    inputType: TextInputType.number,
                    inputFormate: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                    ],
                    isPassword: false,
                    text: "Price",
                    validator: validator.validateTextField,
                  ),
                ),
                SizedBox(
                  height: 20,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: <Widget>[
                    RoundedButton(
                      title: "Cancel",
                      width: width > tabletWidth ? width * 0.15 : width * 0.3,
                      onPressed: () {
                        if (!isLock) {
                          isLock = true;
                          Navigator.pop(context);
                          isLock = false;
                        }
                      },
                    ),
                    RoundedButton(
                      title: "Ok",
                      width: width > tabletWidth ? width * 0.15 : width * 0.3,
                      onPressed: () async {
                        if (!isLock) {
                          isLock = true;
                          widget.onPresOk!(validator);
                          isLock = false;
                        }
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        )),
      ),
    );
  }
}
