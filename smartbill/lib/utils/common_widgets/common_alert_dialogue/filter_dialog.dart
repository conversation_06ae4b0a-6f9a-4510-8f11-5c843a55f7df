import 'package:flutter/src/widgets/container.dart';
import 'package:flutter/src/widgets/framework.dart';

class FilterDialog extends StatefulWidget {
  String? title;
  VoidCallback? onPressReset;
  VoidCallback? onPressFilter;
  FilterDialog({this.title, this.onPressReset, this.onPressFilter});

  @override
  State<FilterDialog> createState() => _FilterDialogState();
}

class _FilterDialogState extends State<FilterDialog> {
  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
