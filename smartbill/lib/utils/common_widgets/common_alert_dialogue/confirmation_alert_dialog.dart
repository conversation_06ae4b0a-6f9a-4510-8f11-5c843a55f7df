import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import '../../constants/colors.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class ConfirmationAlertDialog extends StatefulWidget {
  ConfirmationAlertDialog(
      {Key? key,
      this.content,
      this.buttonNameOne,
      this.buttonNameTwo,
      this.ontabButtonOne,
      this.ontabButtonTwo,
      this.reasenController,
      this.isDeletedReport = false,
      this.focus,
      this.isDeletedReasenIsEmpty})
      : super(key: key);

  String? content;
  String? buttonNameOne;
  String? buttonNameTwo;
  bool? isDeletedReport;
  TextEditingController? reasenController;
  FocusNode? focus;
  bool? isDeletedReasenIsEmpty;
  final VoidCallback? ontabButtonOne;
  final VoidCallback? ontabButtonTwo;

  @override
  State<ConfirmationAlertDialog> createState() => _ConfirmationAlertDialogState();
}

class _ConfirmationAlertDialogState extends State<ConfirmationAlertDialog> {
  FocusNode reasenFocusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return AlertDialog(
        content: Container(
      width: width > tabletWidth ? width * 0.4 : width * 0.8,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            child: Text(widget.content ?? ""),
          ),
          SizedBox(
            height: 20,
          ),
          // ignore: sdk_version_ui_as_code
          if (widget.isDeletedReport ?? false) ...[
            EditText(
              // hint: "Reasen for Delete the Record",
              // text: "Username",
              text: "Please provide a reason for deleting the record.",
              focus: widget.focus ?? false,
              isPassword: false,
              borderColor: primaryTextColor,
              focusBorderWidth: 1.5,
              mController: widget.reasenController,
              isEnableErrorBorder: true,
              errorText: widget.isDeletedReasenIsEmpty! ? "This field is required" : null,
              inputType: TextInputType.text,
              maxLine: 4,
              inputAction: TextInputAction.next,
            ),
            SizedBox(
              height: 10,
            ),
          ],
          SizedBox(
            height: 10,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: <Widget>[
              RoundedButton(
                title: widget.buttonNameOne ?? "",
                width: width > tabletWidth ? width * 0.18 : width * 0.3,
                onPressed: widget.ontabButtonOne!,
              ),
              RoundedButton(
                title: widget.buttonNameTwo ?? "",
                width: width > tabletWidth ? width * 0.18 : width * 0.3,
                onPressed: widget.ontabButtonTwo!,
              ),
            ],
          ),
        ],
      ),
    ));
  }
}
