import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';

import '../../../main.dart';
import '../../common_function/validation_utility.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class CustomDiscountDialog extends StatefulWidget {
  CustomDiscountDialog({
    Key? key,
    this.discountController,
    this.isDiscountEmpty,
    this.selectedDiscountOn,
    this.onPressItem,
  }) : super(key: key);

  TextEditingController? discountController = TextEditingController();
  bool? isDiscountEmpty;
  String? selectedDiscountOn;
 final void Function(dynamic, dynamic)? onPressItem; // Update the callback type


  @override
  State<CustomDiscountDialog> createState() => _CustomDiscountDialogState();
}

class _CustomDiscountDialogState extends State<CustomDiscountDialog> {
  Validator validator = new Validator();
  bool isLock = false;

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;

    return StatefulBuilder(builder: (context, setState) {
      return AlertDialog(
        title: Text(
          "Manual Discount",
          style: TextStyle(fontWeight: FontWeight.bold, fontSize: 15),
        ),
        content: Container(
          width: width > tabletWidth ? width * 0.4 : width * 0.9,
          height: 200,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    child: InputDecorator(
                      decoration: InputDecoration(
                        contentPadding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 1.0),
                        labelText: 'Payment Type',
                        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
                      ),
                      child: DropdownButtonHideUnderline(
                        child: new DropdownButton<String>(
                          hint: Text("Discount On"),
                          value: widget.selectedDiscountOn,
                          items: <String>['Percentage', 'Fixed Amount'].map((String value) {
                            return new DropdownMenuItem<String>(
                              value: value,
                              child: new Text(value),
                            );
                          }).toList(),
                          onChanged: (String? value) async {
                            if (!isLock) {
                              isLock = true;
                              setState(() {
                                widget.selectedDiscountOn = value;
                              });
                              isLock = false;
                            }
                          },
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    height: 10,
                  ),
                  widget.selectedDiscountOn != null
                      ? Column(
                          children: [
                            EditText(
                              text: widget.selectedDiscountOn == "Percentage" ? "Percentage (%)" : "Amount (Rs)",
                              isPassword: false,
                              mController: widget.discountController!,
                              inputType: TextInputType.number,
                              inputFormate: [
                                FilteringTextInputFormatter.allow(RegExp(r'[0-9.]')),
                              ],
                              onChange: (value) {
                                setState(() {
                                  // ignore: unnecessary_statements
                                  value;
                                });
                              },
                              validator: validator.validateTextField,
                              inputAction: TextInputAction.next,
                            ),
                            widget.isDiscountEmpty ?? false
                                ? Container(
                                    width: width > tabletWidth ? width * 0.2 : width * 0.3,
                                    alignment: Alignment.centerLeft,
                                    padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
                                    child: const Text(
                                      "This field is required",
                                      style: TextStyle(color: Colors.red, fontSize: 11.5),
                                    ),
                                  )
                                : Container()
                          ],
                        )
                      : Container(
                          height: 30,
                        ),
                ],
              ),
              SizedBox(
                height: 40,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  RoundedButton(
                    title: "Cancel",
                    width: width > tabletWidth ? width * 0.18 : width * 0.3,
                    onPressed: () {
                      if (!isLock) {
                        isLock = true;
                        Navigator.pop(context);
                        isLock = false;
                      }
                    },
                  ),
                  RoundedButton(
                    title: "Apply",
                    width: width > tabletWidth ? width * 0.18 : width * 0.3,
                    onPressed: ()  {
                       widget.onPressItem!(widget.discountController?.text , widget.selectedDiscountOn);
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
