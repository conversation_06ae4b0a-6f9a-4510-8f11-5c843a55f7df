import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../../main.dart';
import '../../common_function/list_class.dart';
import '../../constants/styles.dart';
import '../widgets.dart';

class HoldSalesAlertDialog extends StatefulWidget {
  HoldSalesAlertDialog({
    Key? key,
    this.onPressItem,
    this.onTabDeleteIcon,
    this.onTabDeleteAllIcon,
  }) : super(key: key);

  final VoidCallback? onTabDeleteAllIcon;
  FunctionCallbackParam? onTabDeleteIcon;
  FunctionCallbackParam? onPressItem;

  @override
  State<HoldSalesAlertDialog> createState() => _HoldSalesAlertDialogState();
}

class _HoldSalesAlertDialogState extends State<HoldSalesAlertDialog> {
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return StatefulBuilder(builder: (context, setState) {
      return AlertDialog(
        title:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          Container(
            child: TextWidget("Hold Sales",
                textColor: primaryTextColor,
                fontFamily: fontBold,
                fontSize: textSizeLarge),
          ),
          IconButton(
              onPressed: widget.onTabDeleteAllIcon,
              icon: Icon(
                Icons.delete,
                color: ListUtility.holdReportList.length != 0
                    ? buttonThemeColor
                    : Colors.grey,
              ))
        ]),
        content: Container(
          // width: double.maxFinite,
          width: width > tabletWidth ? width * 0.4 : width * 0.8,
          height: 300,
          child: Column(
            children: [
              Container(
                height: 250,
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: ListUtility.holdReportList.length,
                  itemBuilder: (BuildContext context, int index) {
                    var item = ListUtility.holdReportList[index];
                    return Container(
                      height: 40,
                      child: Column(
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(4.0),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: <Widget>[
                                InkWell(
                                  onTap: () async {
                                    widget.onPressItem!(item);
                                  },
                                  child: Container(
                                    width: width > tabletWidth
                                        ? width * 0.3
                                        : width * 0.55,
                                    child: TextWidget(item.holdSalesName ?? "",
                                        textColor: primaryTextColor,
                                        fontFamily: fontBold,
                                        fontSize: textSizeMedium),
                                  ),
                                ),
                                Container(
                                  child: InkWell(
                                      onTap: () {
                                        widget.onTabDeleteIcon!(item);
                                      },
                                      child: Icon(
                                        Icons.delete,
                                        color: Colors.red,
                                      )),
                                ),
                              ],
                            ),
                          ),
                          divider(),
                        ],
                      ),
                    );
                  },
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  RoundedButton(
                    title: "Close",
                    width: width > tabletWidth ? width * 0.18 : width * 0.3,
                    onPressed: () {
                      if (!isLock) {
                        isLock = true;
                        Navigator.pop(context);
                        isLock = false;
                      }
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}
