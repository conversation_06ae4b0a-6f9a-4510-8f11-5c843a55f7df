import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import '../constants/colors.dart';
import '../constants/styles.dart';

class ListAddNewProduct extends StatefulWidget {
  const ListAddNewProduct({Key? key,this.ontab}) : super(key: key);

  final VoidCallback? ontab;

  @override
  State<ListAddNewProduct> createState() => _ListAddNewProductState();
}

class _ListAddNewProductState extends State<ListAddNewProduct> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
              onTap: widget.ontab,
              child: Container(
                margin: const EdgeInsets.all(2.0),
                padding: EdgeInsets.symmetric(horizontal: 0, vertical: 10),
                decoration: boxDecoration(
                    radius: 10,
                    showShadow: true,
                    bgColor: secondaryTextColor),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: <Widget>[
                    Icon(Icons.add),
                    SizedBox(width: 5),
                    TextWidget("New Product",
                        textColor: primaryTextColor,
                        fontSize: textSizeLargeMedium,
                        fontFamily: fontSemibold,
                        isLongText: true),
                  ],
                ),
              ),
            );
  }
}