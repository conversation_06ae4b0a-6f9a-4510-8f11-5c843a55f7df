import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import '../constants/styles.dart';

class ConfirmationDialogEidget extends StatelessWidget {
  ConfirmationDialogEidget({this.title, this.description, this.button1Text, this.button1OnTap, this.button2Text, this.button2OnTap});
  String? title;
  String? description;
  String? button1Text;
  VoidCallback? button1OnTap;
  String? button2Text;
  VoidCallback? button2OnTap;

  bool isButton1Lock = false;
  bool isButton2Lock = false;
  double width = 0.0;
  double height = 0.0;
  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    height = MediaQuery.of(context).size.height;
    return AlertDialog(
      content: Text(
        description ??"",
        style: TextStyle(color: primaryTextColor, fontFamily: fontBold, fontSize: 12),
      ),
      actions: <Widget>[
        InkWell(
          onTap: () async {
            if (!isButton1Lock) {
              isButton1Lock = true;
               button1OnTap!();
              isButton1Lock = false;
            }
          },
          child: Container(
            alignment: Alignment.center,
            height: 40,
            width: width * 0.2,
            child: TextWidget(button1Text ?? "", textColor: secondaryTextColor, isCentered: true),
            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
          ),
        ),
        InkWell(
          onTap: () async {
            if (!isButton2Lock) {
              isButton2Lock = true;
              button2OnTap!();
              isButton2Lock = false;
            }
          },
          child: Container(
            alignment: Alignment.center,
            height: 40,
            width: width * 0.2,
            child: TextWidget(button2Text ?? "", textColor: secondaryTextColor, isCentered: true),
            decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
          ),
        ),
      ],
    );
  }
}
