import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import 'package:smartbill/utils/constants/colors.dart';

class PinInputField extends StatefulWidget {
  PinInputField(
      {this.pinLength = 6,
      this.validator,
      this.pinController,
      this.isWrong = false});

  final int? pinLength;
  final TextEditingController? pinController;
  final bool isWrong;
  final String? Function(String?)? validator;
  @override
  State<PinInputField> createState() => _PinInputFieldState();
}

class _PinInputFieldState extends State<PinInputField> {
  final focusNode = FocusNode();

  final defaultPinTheme = PinTheme(
    width: 50,
    height: 50,
    textStyle: const TextStyle(
      letterSpacing: 25.0,
      fontSize: 20,
      fontWeight: FontWeight.bold,
    ),
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(6),
      border: Border.all(color: buttonThemeColor),
    ),
  );

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Directionality(
          // Specify direction if desired
          textDirection: TextDirection.ltr,
          child: Pinput(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              length: widget.pinLength ?? 6,
              controller: widget.pinController,
              autofocus: true,
              focusNode: focusNode,
              validator: widget.validator!,
              autofillHints: const [AutofillHints.oneTimeCode],
              androidSmsAutofillMethod:
                  AndroidSmsAutofillMethod.smsUserConsentApi,
              listenForMultipleSmsOnAndroid: true,
              defaultPinTheme: defaultPinTheme,
              pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
              hapticFeedbackType: HapticFeedbackType.lightImpact,
              errorText:"Please enter a valid otp" ,
              onCompleted: (pin) {
                if (pin.length == 6 && focusNode.hasFocus) {
                  focusNode.unfocus();
                }
              },
              onChanged: (value) {
                if (value.length == 6 && focusNode.hasFocus) {
                  focusNode.unfocus();
                }
              },
              cursor: Column(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Container(
                    margin: const EdgeInsets.only(bottom: 9),
                    width: 22,
                    height: 1,
                  ),
                ],
              ),
              focusedPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration?.copyWith(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: buttonThemeColor),
                ),
              ),
              submittedPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration?.copyWith(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: buttonThemeColor),
                ),
              ),
              forceErrorState: widget.isWrong ,
              errorPinTheme: defaultPinTheme.copyWith(
                decoration: defaultPinTheme.decoration?.copyWith(
                  borderRadius: BorderRadius.circular(6),
                  border: Border.all(color: Colors.red),
                ),
              )),
        ),
      ],
    );
  }
}
