import 'package:flutter/material.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

class TextFieldWidget extends StatefulWidget {
  var isPassword;
  var isSecure;
  var fontSize;
  var textColor;
  var fontFamily;
  var text;
  var hint;
  var maxLine;
  var maxLength;
  var inputType;
  var inputFormate;
  var suffixIcon;
  var errorText;
  var validator;
  var autofocus;
  var focus;
  var readOnly;
  var enable;
  var inputAction;
  Function(String)? submit;
  Function(String)? onChange;
  Function()? onTap;
  TextEditingController? mController;
  var textAlign;

  bool? isEnabledBorder;

  VoidCallback? onPressed;

  TextFieldWidget(
      {var this.fontSize = textSizeNormal,
      var this.textColor = greyTextFieldColor,
      var this.fontFamily = fontRegular,
      var this.isPassword = true,
      var this.hint = "",
      var this.isSecure = false,
      var this.text = "",
      var this.mController,
      var this.maxLine = 1,
      var this.enable = true,
      this.maxLength,
      this.inputType,
      this.suffixIcon,
      this.errorText,
      this.validator,
      this.focus,
      this.inputFormate,
      this.inputAction,
      this.submit,
      this.onChange,
      this.onTap,
      var this.autofocus = false,
      var this.readOnly = false,
      this.textAlign,
      this.isEnabledBorder,
      this.onPressed});

  @override
  State<StatefulWidget> createState() {
    return TextFieldWidgetState();
  }
}

class TextFieldWidgetState extends State<TextFieldWidget> {
  @override
  Widget build(BuildContext context) {
    return TextFormField(
      onTap: widget.onTap,
      autofocus: widget.autofocus,
      readOnly: widget.readOnly,
      controller: widget.mController,
      obscureText: widget.isPassword,
      maxLength: widget.maxLength,
      focusNode: widget.focus,
      enabled: widget.enable,
      keyboardType: widget.inputType,
      inputFormatters: widget.inputFormate,
      validator: widget.validator,
      textInputAction: widget.inputAction,
      onFieldSubmitted: widget.submit,
      onChanged: widget.onChange,
      textAlign: widget.textAlign == null ? TextAlign.start : widget.textAlign,
      style: TextStyle(color: primaryTextColor, fontSize: textSizeLargeMedium, fontFamily: fontRegular),
      decoration: InputDecoration(
        filled: widget.isEnabledBorder ?? false,
        prefixText: widget.maxLength == 10 ? " +91 " : "",
        errorText: widget.errorText,
        labelText: widget.text,
        fillColor: widget.isEnabledBorder ?? false ? Color.fromARGB(15, 25, 20, 20) : Colors.transparent,
        border: widget.isEnabledBorder ?? false
            ? OutlineInputBorder(
                borderRadius: BorderRadius.circular(8.0),
                borderSide: BorderSide.none,
              )
            : null,
        suffixIcon: widget.isSecure
            ? GestureDetector(
                onTap: () {
                  setState(() {
                    widget.isPassword = !widget.isPassword;
                  });
                },
                child: new Icon(
                  widget.isPassword ? Icons.visibility_off : Icons.visibility,
                  color: primaryTextColor,
                ),
              )
            : null,
        contentPadding: widget.isEnabledBorder ?? false ? EdgeInsets.fromLTRB(10, 0, 16, 15) : EdgeInsets.fromLTRB(0, 6, 16, 6),
        hintText: widget.hint,
        hintStyle: TextStyle(color: greyTextFieldColor),
      ),
    );
  }
}
