// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';

class ReportCardWidget extends StatelessWidget {
  String? title;
  String? titleIcon;
  String? trailIcon;
  bool? isEnableTrailIcon;
  double? height;
  final VoidCallback? onPress;
  ReportCardWidget(
      {Key? key,
      this.title,
      this.titleIcon,
      this.trailIcon,
      this.isEnableTrailIcon = false,
      this.height,
      this.onPress})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPress,
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: 10.0,
        ),
        child: Card(
          color: secondaryTextColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.0),
          ),
          child: Container(
            height: height,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 10.0, vertical: 10.0),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Row(
                        children: [
                          Container(
                            height: 50,
                            width: 50,
                            padding: EdgeInsets.all(15.0),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(10.0),
                                color: lightGreyColor),
                            child: SvgPicture.asset(
                              titleIcon ?? "",
                              color: appThemeColor,
                            ),
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          TextWidget(title ?? "",
                              textColor: primaryTextColor,
                              fontFamily: fontMedium,
                              fontSize: textSizeLargeMedium),
                        ],
                      ),
                      iconWidget(),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget iconWidget() {
    if (isEnableTrailIcon ?? false) {
      return SvgPicture.asset(
        trailIcon ?? leftArrowSVG,
        height: 25,
      );
    } else {
      return SizedBox();
    }
  }
}
