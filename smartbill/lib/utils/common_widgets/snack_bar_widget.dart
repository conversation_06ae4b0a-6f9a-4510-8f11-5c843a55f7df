import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/rounded_button.dart';
import '../constants/colors.dart';
import '../constants/styles.dart';

void showCommonSnackBar(BuildContext context, VoidCallback? onPressOpen) {
  ScaffoldMessenger.of(context).showSnackBar(
    SnackBar(
      backgroundColor: Colors.white,
      duration: Duration(seconds: 7),
      shape: RoundedRectangleBorder(
        side: BorderSide(color: appThemeColor, width: 1.5),
        borderRadius: BorderRadius.all(Radius.circular(5)),
      ),
      behavior: SnackBarBehavior.fixed,
      content: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Expanded(
            child: Text(
              'Do you want to open this file?',
              overflow: TextOverflow.clip,
              softWrap: true,
              style: black16w500,
            ),
          ),
          
          Row(
            children: [
              SizedBox(
                height: 40,
                child: RoundedButton(
                  width: MediaQuery.of(context).size.width * 0.18,
                  onPressed: () {
                    ScaffoldMessenger.of(context).hideCurrentSnackBar();
                  },
                  isEnableBorder: true,
                  title: ' Close ',
                  buttonTextStyle: black16w500.copyWith(color: appThemeColor),
                ),
              ),
              SizedBox(
                width: 5.0,
              ),
              SizedBox(
                height: 40,
                child: RoundedButton(
                  width: MediaQuery.of(context).size.width * 0.18,
                  onPressed: onPressOpen,
                  title: ' Open ',
                  buttonTextStyle: black16w500.copyWith(color: secondaryTextColor),
                ),
              ),
            ],
          ),
        ],
      ),
    ),
  );
}
