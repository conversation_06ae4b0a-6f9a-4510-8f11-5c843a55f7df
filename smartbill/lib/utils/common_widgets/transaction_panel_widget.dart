// ignore_for_file: sdk_version_ui_as_code

import 'package:flutter/material.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import '../../Repositories/image_repository.dart';
import '../../model/card_models/transaction_card_model.dart';
import '../constants/colors.dart';
import '../constants/styles.dart';

class TransactionPanelWidget extends StatefulWidget {
  TransactionPanelWidget({Key? key, this.transactionFor, this.transactionCardList, this.onLongPress, this.onTapPress, this.isBigLayout}) : super(key: key);
  String? transactionFor;
  List<TransactionCardModel>? transactionCardList = [];
  final FunctionCallback2Param? onTapPress;
  final FunctionCallbackParam? onLongPress;
  bool? isBigLayout;

  @override
  State<TransactionPanelWidget> createState() => _TransactionPanelWidgetState();
}

class _TransactionPanelWidgetState extends State<TransactionPanelWidget> {
  @override
  Widget build(BuildContext context) {
    return Expanded(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.all(Radius.circular(20)),
          boxShadow: <BoxShadow>[BoxShadow(color: Colors.black12, blurRadius: 5.0, offset: Offset(0.75, 0.75))],
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          physics: ScrollPhysics(),
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(5.0),
                child: Card(
                  child: Container(
                    color: Colors.white10,
                    height: 40,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Container(
                          padding: EdgeInsets.only(left: 10),
                          width: widget.isBigLayout ??false ? 200 : 100,
                          alignment: Alignment.centerLeft,
                          child: TextWidget(widget.transactionFor ?? "", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                        ),
                        Container(
                          width: widget.isBigLayout??false ? 100 : 80,
                          alignment: Alignment.centerLeft,
                          child: TextWidget("Qty", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                        ),
                        Container(
                          width: widget.isBigLayout??false ? 120 : 100,
                          alignment: Alignment.center,
                          child: TextWidget("Amount(Rs)", textColor: primaryTextColor, fontFamily: fontBold, fontSize: textSizeMedium),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              Column(
                children: [
                  ListView.builder(
                    scrollDirection: Axis.vertical,
                    itemCount: widget.transactionCardList?.length,
                    shrinkWrap: true,
                    padding: EdgeInsets.only(right: 0),
                    physics: ScrollPhysics(),
                    itemBuilder: (context, index) {
                      var item = widget.transactionCardList?[index];
                      return InkWell(
                        onTap: () {
                          widget.onTapPress!(item, index);
                        },
                        onLongPress: () {
                          widget.onLongPress!(item?.guidId);
                        },
                        child: Card(
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 5),
                            child: Column(
                              children: [
                                if (item?.name != null && item?.name != "") ...[
                                  Padding(
                                    padding: const EdgeInsets.only(left: 5, right: 5),
                                    child: Row(
                                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                      children: [
                                        Container(
                                          child: Text(
                                            item?.name ?? "",
                                            textAlign: TextAlign.left,
                                          ),
                                          width: widget.isBigLayout??false ? 200 : 100,
                                        ),
                                        Container(
                                          child: Text(
                                            fnUtilities.checkQtyDoubleValue(item?.qty ?? ""),
                                            textAlign: TextAlign.left,
                                          ),
                                          width: widget.isBigLayout??false ? 100 : 80,
                                        ),
                                        Container(
                                          child: Text(
                                            fnUtilities.checkQtyDoubleValue(double.parse(item?.totalAmount ?? '0').toStringAsFixed(2)),
                                            textAlign: TextAlign.right,
                                          ),
                                          width: widget.isBigLayout??false ? 120 : 80,
                                        ),
                                      ],
                                    ),
                                  ),
                                ]
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
