// ignore_for_file: must_be_immutable, prefer_typing_uninitialized_variables, sdk_version_ui_as_code

import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';

class DropDown {
  String? id;
  String? name;
  DropDown({this.id, this.name});
}

class CommonDropDown extends StatefulWidget {
  CommonDropDown(
      {Key? key,
      this.dropDown,
      this.dropDownList,
      this.hintText,
      this.isDense,
      this.isExpanded,
      this.style,
      this.icon,
      this.onChanged,
      this.canvasColor,
      this.borderColor})
      : super(key: key);

  DropDown? dropDown;
  List<DropDown>? dropDownList = [];
  String? hintText = '';
  final ValueChanged<DropDown?>? onChanged;
  var style;
  var icon;
  var isDense;
  var isExpanded;
  var canvasColor;
  Color? borderColor;

  @override
  State<StatefulWidget> createState() {
    return _CommonDropDownState();
  }
}

class _CommonDropDownState extends State<CommonDropDown> {
  @override
  Widget build(BuildContext context) {
    return InputDecorator(
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(horizontal: 3.0, vertical: 1.0),
        labelText: widget.hintText,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(5.0)),
        enabledBorder: OutlineInputBorder(
          borderSide: BorderSide(color: widget.borderColor ?? Colors.grey), // Change to your desired border color
          borderRadius: BorderRadius.circular(5.0),
        ),
      ),
      child: SingleChildScrollView(
        child: DropdownButtonHideUnderline(
          child: Theme(
            data: Theme.of(context).copyWith(
              canvasColor: widget.canvasColor ?? Colors.blue.shade50,
              highlightColor: Colors.white,
            ),
            child: DropdownButton2(
              items: filterOptionDropdown(widget.dropDownList),
              isExpanded: widget.isExpanded,
              isDense: widget.isDense,
              buttonStyleData: ButtonStyleData(height: 35, width: 140, padding: EdgeInsets.all(5)),
              menuItemStyleData: const MenuItemStyleData(
                height: 30,
                padding: EdgeInsets.symmetric(horizontal: 3),
              ),
              // menuMaxHeight: 500,
              hint: Text(widget.hintText ?? "Please Select"),
              value: widget.dropDown,
              style: widget.style,
              onChanged: widget.onChanged,
              dropdownStyleData: const DropdownStyleData(
                  offset: Offset(0, -2),
                  maxHeight: 200,
                  scrollbarTheme: ScrollbarThemeData(
                    thumbVisibility: MaterialStatePropertyAll(true),
                    trackVisibility: MaterialStatePropertyAll(true),
                    thumbColor: MaterialStatePropertyAll(Colors.grey),
                    trackColor: MaterialStatePropertyAll(Colors.white),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 3, horizontal: 10)
                  // padding: const EdgeInsets.only(left: 10, right: 10),
                  ),
            ),
          ),
        ),
      ),
    );
  }

  List<DropdownMenuItem<DropDown>> filterOptionDropdown(List<DropDown>? items) {
    List<DropdownMenuItem<DropDown>> _menuItems = [];
    if (items != null) {
      for (DropDown item in items) {
        _menuItems.addAll([
          DropdownMenuItem<DropDown>(
            value: item,
            child: Text(
              item.name.toString(),
              style: const TextStyle(
                fontSize: 16,
              ),
            ),
          ),
          //If it's last item, we will not add Divider after it.
          // if (item != items.last)
          const DropdownMenuItem<DropDown>(
            enabled: false,
            child: Divider(
              thickness: 1,
            ),
          ),
        ]);
      }
    }
    return _menuItems;
  }
}
