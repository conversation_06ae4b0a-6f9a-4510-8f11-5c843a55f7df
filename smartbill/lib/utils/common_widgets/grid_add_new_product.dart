import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

class GridAddNewProduct extends StatefulWidget {
  const GridAddNewProduct({Key? key, this.onTab}) : super(key: key);
  final VoidCallback? onTab;

  @override
  State<GridAddNewProduct> createState() => _GridAddNewProductState();
}

class _GridAddNewProductState extends State<GridAddNewProduct> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTab,
      child: Container(
        padding: EdgeInsets.only(left: 0, right: 0),
        decoration: boxDecoration(
            radius: 10, showShadow: true, bgColor: buttonThemeColor),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Container(
              padding: EdgeInsets.all(5),
              child: Icon(Icons.add, color: secondaryTextColor),
            ),
            SizedBox(height: 2),
            TextWidget("New Product",
                textColor: secondaryTextColor,
                fontSize: textSizeSmall,
                fontFamily: fontBold,
                isLongText: true,
                isCentered: true)
          ],
        ),
      ),
    );
  }
}