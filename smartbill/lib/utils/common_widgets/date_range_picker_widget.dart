import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../Repositories/image_repository.dart';
import '../../main.dart';

class DateRangePickerWidget extends StatefulWidget {
  bool isBigLayout;
  DateTime fromDate;
  DateTime toDate;
  FunctionCallbackParam onPressFromDate;
  FunctionCallbackParam onPressToDate;

  DateRangePickerWidget({Key? key,required this.isBigLayout,required this.fromDate,required this.toDate,required this.onPressFromDate,required this.onPressToDate}) : super(key: key);

  @override
  State<DateRangePickerWidget> createState() => _DateRangePickerWidgetState(fromDate, toDate, onPressFromDate, onPressToDate,isBigLayout);
}

class _DateRangePickerWidgetState extends State<DateRangePickerWidget> {
  DateTime fromDate;
  DateTime toDate;
  FunctionCallbackParam onPressFromDate;
  FunctionCallbackParam onPressToDate;
    bool isBigLayout;
  _DateRangePickerWidgetState(this.fromDate, this.toDate, this.onPressFromDate, this.onPressToDate,this.isBigLayout);
  
  //DATE FUNCTION
  Future<void> selectDate(BuildContext context, String dateType) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return CustomTheme(
            child: child,
          );
        },
        initialDate: dateType == 'fromDate' ? fromDate : toDate,
        firstDate: DateTime(2015, 8),
        lastDate: DateTime(2101));
    if (picked != null) {
      if (dateType == 'fromDate') {
        if (picked.isAfter(toDate)) {
          showToast('From date cannot be greater than To date');
        } else {
          setState(() {
            fromDate = picked.subtract(Duration(
                hours: picked.hour,
                minutes: picked.minute,
                seconds: picked.second,
                milliseconds: picked.millisecond,
                microseconds: picked.microsecond));
             onPressFromDate(fromDate);

          });
        }
      } else if (dateType == 'toDate') {
        if (picked.isBefore(fromDate)) {
          showToast('To date cannot be less than From date');
        } else {
          setState(() {
            toDate = picked;
            onPressToDate(toDate);
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: isBigLayout ? MainAxisAlignment.start : MainAxisAlignment.spaceBetween,
      children: [
        InkWell(
          onTap: () {
            selectDate(context, 'fromDate');
           
          },
          child: Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "${fnUtilities.convertDate(fromDate.toString())}",
                    style: secondaryTextStyle(),
                  ),
                  SizedBox(
                    width: 15,
                  ),
                  Icon(Icons.calendar_today)
                ],
              ),
            ),
          ),
        ),
        InkWell(
          onTap: () {
            selectDate(context, 'toDate');
            
          },
          child: Card(
            elevation: 4,
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 18),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    fnUtilities.convertDate(toDate.toString()),
                    style: secondaryTextStyle(),
                  ),
                  SizedBox(
                    width: 15,
                  ),
                  Icon(Icons.calendar_today)
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}
