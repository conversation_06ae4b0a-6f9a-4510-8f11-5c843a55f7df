import 'package:flutter/material.dart';

import '../../model/products.dart';
import '../constants/colors.dart';

class CategoryCardWidget extends StatefulWidget {
  CategoryCardWidget(
      {Key? key,
      this.buttonWidth,
      this.productKeyList,
      this.onTab})
      : super(key: key);

  ProductKey? productKeyList;
  double? buttonWidth;
  final VoidCallback? onTab;

  @override
  State<CategoryCardWidget> createState() =>
      _CategoryCardWidgetState();
}

class _CategoryCardWidgetState
    extends State<CategoryCardWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.buttonWidth,
      // ignore: deprecated_member_use
      child: OutlinedButton(
        onPressed: widget.onTab,
        child: Text(widget.productKeyList?.keywordName ?? ""),
        style: OutlinedButton.styleFrom(
          primary: buttonThemeColor, // Sets the button's text color
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(5.0),
          ),
        ),
      ),
    );
  }
}
