import 'package:flutter/material.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/model/workspace_settings.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';
import 'package:smartbill/utils/roles_permissions/permission_functions.dart';

// ignore: must_be_immutable
class CommonSettingsWidget extends StatefulWidget {
  final String title;
  bool value;
  final String settingKey;
  int? permissionValue;
  
// 
  CommonSettingsWidget({
    Key? key,
    required this.title,
    required this.value,
    required this.settingKey,
    this.permissionValue,
  }) : super(key: key);

  @override
  _CommonSettingsWidgetState createState() => _CommonSettingsWidgetState();
}

class _CommonSettingsWidgetState extends State<CommonSettingsWidget> {
  WorkspaceSettings settingsDetail = new WorkspaceSettings();
  FnUtilities fnUtilities = new FnUtilities();

  @override
  Widget build(BuildContext context) {
    final width = MediaQuery.of(context).size.width;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 5.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: <Widget>[
              Container(
                width: width * 0.79,
                child: Text(
                  widget.title,
                  style: TextStyle(color: primaryTextColor, fontSize: textSizeMedium, fontFamily: fontSemibold),
                ),
              ), 
              Switch(
                value: widget.value,
                onChanged: (value) async {
                  if (widget.permissionValue == null || PermissionFunctions.checkPermission(widget.permissionValue ?? 0)) {
                    setState(() {
                      widget.value = value;
                      settingsDetail.settingKey = widget.settingKey;
                      settingsDetail.settingValue = value ? "1" : "0";
                      settingsDetail.workspaceId = MyApp.activeWorkspace.workspaceId;
                      settingsDetail.sync = 0;
                      fnUtilities.updateSettingDetails(settingsDetail);
                    });
                  }
                },
                activeTrackColor: buttonThemeColor,
                activeColor: buttonThemeColor,
              ),
            ],
          ),
        ),
        divider(),
      ],
    );
  }
}
