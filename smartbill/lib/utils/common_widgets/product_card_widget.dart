import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';

import '../../Repositories/image_repository.dart';
import '../../model/products.dart';
import '../constants/colors.dart';
import '../constants/styles.dart';

class ProductCardWidget extends StatefulWidget {

  ProductCardWidget({Key? key,
   this.item,
   this.showImages, 
   this.isSaleLayoutTwo,
   this.onTab,
   this.onLongPress})
      : super(key: key);

  Products? item;
  bool? showImages;
  bool? isSaleLayoutTwo = false;
  final VoidCallback? onTab;
  final VoidCallback? onLongPress;

  @override
  State<ProductCardWidget> createState() => _ProductCardWidgetState();
}

class _ProductCardWidgetState extends State<ProductCardWidget> {
  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    return InkWell(
      onTap:widget.onTab,
      onLongPress: widget.onLongPress,
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.only(left: 0, right: 0),
            decoration:   widget.item?.imagePath != null && (widget.showImages ?? false)
                ? BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(10.0)),
                    image: DecorationImage(
                      image: FileImage(widget.item!.imagePath!),
                      fit: BoxFit.cover,
                    ),
                  )
                : boxDecoration(
                    radius: 10, showShadow: true, bgColor: buttonThemeColor),
            child: Column(
              mainAxisAlignment:
                  ((widget.showImages ?? false) && widget.item?.imagePath != null)
                      ? MainAxisAlignment.spaceBetween
                      : MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                    width: width,
                    padding: EdgeInsets.only(right: 5),
                    alignment:
                        widget.item?.imagePath != null && (widget.showImages ?? false)
                            ? Alignment.topRight
                            : Alignment.center,
                    child: ( (widget.showImages ?? false) && widget.item?.imagePath != null)
                        ? textWithStroke(widget.item?.productSaleId.toString() ??"",
                            textColor: primaryTextColor,
                            fontSize: textSizeNormal,
                            fontFamily: fontBold,
                            isCentered: true)
                        : TextWidget(widget.item?.productSaleId.toString() ?? "",
                            textColor: secondaryTextColor,
                            fontSize: textSizeNormal,
                            fontFamily: fontBold,
                            isCentered: true)),
                SizedBox(height: 2),
                Column(
                  children: [
                    Container(
                        child:
                            ( (widget.showImages ?? false) && widget.item?.imagePath != null)
                                ? textWithStroke(widget.item?.name ?? "",
                                    textColor: primaryTextColor,
                                    fontSize: textSizeSmall,
                                    fontFamily: fontBold,
                                    isLongText: true,
                                    isCentered: true)
                                : TextWidget(widget.item?.name ?? "",
                                    textColor: secondaryTextColor,
                                    fontSize: textSizeSmall,
                                    fontFamily: fontBold,
                                    isLongText: true,
                                    isCentered: true)),
                    SizedBox(height: 2),
                    widget.item?.price == "" || widget.item?.price == null
                        ? TextWidget("",
                            textColor: primaryTextColor,
                            fontSize: textSizeLargeMedium,
                            fontFamily: fontSemibold)
                        : ( (widget.showImages ?? false) && widget.item?.imagePath != null)
                            ? textWithStroke("Rs.${widget.item?.price}",
                                textColor: primaryTextColor,
                                fontSize: textSizeMedium,
                                fontFamily: fontSemibold,
                                isCentered: true)
                            : TextWidget("Rs.${widget.item?.price}",
                                textColor: secondaryTextColor,
                                fontSize: textSizeMedium,
                                fontFamily: fontSemibold,
                                isCentered: true)
                  ],
                ),
              ],
            ),
          ),
          // ignore: sdk_version_ui_as_code
          if (widget.isSaleLayoutTwo ?? false) ...[
            widget.item?.count != null && (widget.item?.count)! > 0
                ? Container(
                    width: width,
                    decoration: BoxDecoration(
                        color: Colors.black26,
                        borderRadius: BorderRadius.all(Radius.circular(10.0))),
                    alignment: Alignment.center,
                    child: TextWidget(
                        fnUtilities
                            .checkQtyDoubleValue(widget.item?.count.toString()?? ""),
                        textColor: secondaryTextColor,
                        fontSize: textSizeLarge,
                        fontFamily: fontSemibold,
                        isCentered: true),
                  )
                : Container()
          ]
        ],
      ),
    );
  }
}
