import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:intl/intl.dart';

import 'package:smartbill/utils/common_function/validation_utility.dart';

import '../../main.dart';
import '../../screens/manage_shifts/functions/manage_shifts_function.dart';
import '../common_function/fn_utilities.dart';
import '../constants/colors.dart';
import '../constants/images.dart';
import '../constants/styles.dart';
import 'widgets.dart';

class AdvanceOrderWidget extends StatefulWidget {
  static List<String> splitDeliveryDateTime(String dateTimeString) {
    List<String> splitDateTime = [];
    if (dateTimeString.contains("T")) {
      splitDateTime = dateTimeString.split("T");
    } else {
      splitDateTime = dateTimeString.split(" ");
    }
    String date = splitDateTime[0];

    List<String> splitTime = splitDateTime[1].split(":");
    int hours = int.parse(splitTime[0]);
    String ampm = (hours < 12) ? "AM" : "PM";
    hours = (hours > 12) ? hours - 12 : hours; // Convert 24-hour format to 12-hour format
    String time = "$hours:${splitTime[1]}"; // Combine hours and minutes

    return [date, time, ampm];
  }

  static String convertDateTime(String dateTimeString, {bool hideLabel = false}) {
    // Parse the date and time string
    DateTime dateTime = DateTime.parse(dateTimeString);

    // Format the date to "yyyy-MM-dd" format
    String formattedDate = DateFormat('dd-MM-yyyy').format(dateTime);

    // Format the time to 12-hour format with AM/PM
    String formattedTime = DateFormat('h:mm a').format(dateTime);

    // Combine formatted date and time
    String result;
    if (hideLabel) {
      result = '$formattedDate & Time: $formattedTime';
    } else {
      result = 'Delivery Date: $formattedDate & Time: $formattedTime';
    }
    return result;
  }

  final String? buttonName1;
  final String? buttonName2;
  final FunctionCallback3Param? onSubmit;
  final VoidCallback? onPressClose;

  final TextEditingController? dateController;
  final TextEditingController? timeController;
  final TextEditingController? noteController;
  FunctionCallbackParam? isAdvancePayment;
  FunctionCallbackParam? dateOnChange;
  FunctionCallbackParam? timeOnChange;
  FunctionCallbackParam? noteOnChange;
  FunctionCallbackParam? noAdvanceOnTab;
  FunctionCallback3Param? onTabCheckBox;
  bool isAdvanceSelected;
  bool paymentTypeEnabled;

  AdvanceOrderWidget(
      {Key? key,
      this.dateController,
      this.timeController,
      this.noteController,
      this.onSubmit,
      this.buttonName1,
      this.buttonName2,
      this.onPressClose,
      this.isAdvancePayment,
      this.isAdvanceSelected = false,
      this.dateOnChange,
      this.timeOnChange,
      this.noteOnChange,
      this.noAdvanceOnTab,
      this.onTabCheckBox,
      this.paymentTypeEnabled = false})
      : super(key: key);

  @override
  State<AdvanceOrderWidget> createState() => _AdvanceOrderWidgetState();
}

class _AdvanceOrderWidgetState extends State<AdvanceOrderWidget> {
  FnUtilities fnUtilities = FnUtilities();

  ManageShiftsFunction manageShiftsFunction = ManageShiftsFunction();

  Validator validator = Validator();

  double width = 0.0;
  double hight = 0.0;
  // bool isAdvanceSelected = false;

  clearTextField() {
    setState(() {
      widget.dateController?.clear();
      widget.noteController?.clear();
      widget.timeController?.clear();
    });
  }

  assignUpdatedDateTime() {
    String dateString = widget.dateController!.text;

    // Extract the time string from widget.timeController
    String timeString = widget.timeController!.text;

    // Parse the date string
    DateTime parsedDate = DateTime.parse(dateString);

    // Parse the time string separately
    TimeOfDay parsedTime = _parseTimeOfDay(timeString);

    // Assign the parsed date and time to deliveryDate and deliveryTime respectively
    setState(() {
      deliveryDate = parsedDate;
      deliveryTime = parsedTime;
    });
    print(deliveryTime);
    print(deliveryDate);
  }

  TimeOfDay _parseTimeOfDay(String timeString) {
    // Parse the time string into hours and minutes
    List<String> parts = timeString.split(':');
    int hours = int.parse(parts[0]);
    int minutes = int.parse(parts[1].split(' ')[0]);

    // Check if it's AM or PM
    bool isPM = parts[1].contains('PM');

    // Adjust hours for PM time
    if (isPM && hours != 12) {
      hours += 12;
    }

    // Create a TimeOfDay object
    return TimeOfDay(hour: hours, minute: minutes);
  }

  @override
  void initState() {
    super.initState();
  }

  DateTime? deliveryDate = DateTime.now();
  TimeOfDay? deliveryTime = TimeOfDay.now();

  @override
  Widget build(BuildContext context) {
    width = MediaQuery.of(context).size.width;
    // assignUpdatedDateTime();
    return Card(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      child: Form(
        key: validator.formkey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 3.0, vertical: 3.0),
          child: Container(
            width: width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(10),
              boxShadow: [
                BoxShadow(
                  color: Colors.grey.withOpacity(0.5),
                ),
              ],
              color: secondaryTextColor,
            ),
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 5.0),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                   
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Column(
                            children: [
                              EditText(
                                  text: "Delivery Date",
                                  readOnly: true,
                                  isPassword: false,
                                  validator: validator.validateTextField,
                                  suffixIcon: Container(
                                    color: lightGreyColor,
                                    padding: EdgeInsets.all(11.0),
                                    child: SvgPicture.asset(
                                      calendarSVG,
                                      height: 10,
                                      width: 10,
                                    ),
                                  ),
                                  mController: widget.dateController,
                                  onTap: () async {
                                    await selectDate(context).then((value) {
                                      if (value != null) {
                                        setState(() {
                                          deliveryDate = value;
                                          widget.dateController?.text = DateFormat('yyyy-MM-dd').format(deliveryDate!);
                                        });

                                        widget.dateOnChange!(widget.dateController?.text);
                                      }
                                    });
                                  },
                                  isEnableErrorBorder: true,
                                  errorBorderColor: primaryTextColor),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Expanded(
                          child: Column(
                            children: [
                              EditText(
                                  mController: widget.timeController,
                                  text: "Delivery Time",
                                  readOnly: true,
                                  isPassword: false,
                                  validator: validator.validateTextField,
                                  suffixIcon: Container(
                                    padding: EdgeInsets.all(11.0),
                                    color: lightGreyColor,
                                    child: SvgPicture.asset(
                                      clockSVG,
                                    ),
                                  ),
                                  onTap: () async {
                                    await timePicker(
                                      context,
                                      isOnlyTime: true,
                                      pickedDate: deliveryDate,
                                      isFrom: true,
                                      selectedTimeValue:
                                          deliveryTime != null ? TimeOfDay(hour: deliveryTime!.hour, minute: deliveryTime!.minute) : null,
                                    ).then((value) {
                                      if (value != null && value != deliveryTime) {
                                        setState(() {
                                          widget.timeController?.text = DateFormat('h:mm a').format(value);
                                          deliveryDate = value;
                                          deliveryTime = TimeOfDay(hour: value.hour, minute: value.minute);
                                        });
                                        widget.timeOnChange!(widget.timeController?.text);
                                      }
                                    });
                                  },
                                  isEnableErrorBorder: true,
                                  errorBorderColor: primaryTextColor),
                            ],
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 15.0,
                    ),

                    Container(
                      child: EditText(
                        text: 'Note',
                        inputType: TextInputType.text,
                        mController: widget.noteController,
                        maxLine: 2,
                        isPassword: false,
                        maxLength: 250,
                        onTapOutside: (event) {
                          print('onTapOutside');
                          FocusManager.instance.primaryFocus?.unfocus();
                        },
                        onChange: (value) {
                          widget.noteOnChange!(
                            widget.noteController?.text = value,
                          );
                        },
                      ),
                    ),
                    if (widget.paymentTypeEnabled)
                      Row(children: [
                        Row(
                          children: [
                            Text(
                              'Advance',
                              style: black16BoldTextStyle,
                            ),
                            Radio(
                              activeColor: appThemeColor,
                              value: false,
                              groupValue: widget.isAdvanceSelected,
                              onChanged: (value) {
                                setState(() {
                                  widget.isAdvanceSelected = value!;
                                  widget.isAdvancePayment!(value!);
                                });
                              },
                            ),
                          ],
                        ),
                        Row(
                          children: [
                            Text(
                              'No Advance',
                              style: black16BoldTextStyle,
                            ),
                            Radio(
                              activeColor: appThemeColor,
                              value: true,
                              groupValue: widget.isAdvanceSelected,
                              onChanged: (value) {
                                setState(() {
                                  if (widget.timeController!.text.isNotEmpty && widget.dateController!.text.isNotEmpty) {
                                    // widget.isAdvancePayment!(value);
                                    widget.noAdvanceOnTab!(value);
                                  } else {
                                    showToast('Please enter delivery date and time first');
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 5.0),
                          child: TextButton(
                              onPressed: () {
                                 setState(() {
                                  
                                  widget.dateController?.clear();
                                  widget.timeController?.clear();
                                  widget.noteController?.clear();
                          
                                  widget.onTabCheckBox!(
                                     widget.dateController?.text, widget.timeController?.text, widget.noteController?.text);
                                });
                              },
                              child: Text(
                                'Clear',
                                style: TextStyle(
                                  decoration: TextDecoration.underline,
                                  color: appThemeColor,
                                  fontSize: 18 // Optional: change the text color
                                ),
                              )),
                        )
                      ]),
                    // if (widget.paymentwidget != null) widget.paymentwidget!,

                    SizedBox(
                      height: 5.0,
                    )
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<DateTime?> selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
        helpText: 'Select date',
        cancelText: 'Cancel',
        confirmText: "Ok",
        fieldLabelText: 'Selected Date',
        fieldHintText: 'Month/Date/Year',
        errorFormatText: 'Enter valid date',
        errorInvalidText: 'Enter date in valid range',
        context: context,
        builder: (BuildContext context, Widget? child) {
          return Theme(
            data: ThemeData.light(), // You can set your theme data here
            child: child!,
          );
        },
        firstDate: DateTime.now(),
        lastDate: DateTime(2100));

    if (picked != null) {
      deliveryDate = picked;
      return deliveryDate;
    } else
      return null;
  }

  Future<DateTime?> timePicker(BuildContext context,
      {bool isOnlyTime = true, DateTime? pickedDate, bool isFrom = true, DateTime? fromDate, TimeOfDay? selectedTimeValue}) async {
    DateTime? selectedDateTime;
    TimeOfDay? picked = await showTimePicker(
      context: context,
      initialTime: selectedTimeValue ?? TimeOfDay.now(),
    );

    if (picked != null) {
      selectedDateTime = DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day,
        picked.hour,
        picked.minute,
      );
      if (isOnlyTime && pickedDate != null) {
        selectedDateTime = DateTime(
          pickedDate.year,
          pickedDate.month,
          pickedDate.day,
          picked.hour,
          picked.minute,
        );
      }
      if (isFrom) {
        return selectedDateTime;
      } else {
        if (fromDate != null && selectedDateTime.isAfter(fromDate)) {
          return selectedDateTime;
        } else {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text("Please select a time later than the from date."),
            ),
          );
        }
      }
    } else {
      return null;
    }
    return null;
  }

  String? formatTime(TimeOfDay time, {bool? isFrom = true, DateTime? fromDateTime}) {
    int hour = time.hourOfPeriod;
    int minute = time.minute;
    String period = time.period == DayPeriod.am ? 'AM' : 'PM';

    return '$hour:${minute.toString().padLeft(2, '0')} $period';
  }
}
