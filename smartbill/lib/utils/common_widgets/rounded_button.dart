import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/styles.dart';

class RoundedButton extends StatefulWidget {
  RoundedButton({Key? key, @required this.title, @required this.onPressed, this.width, this.isEnableBorder, this.height, this.buttonTextStyle});
  String? title;
  final VoidCallback? onPressed;
  double? width;
  bool? isEnableBorder = false;
  double? height;
  TextStyle? buttonTextStyle;

  @override
  State<RoundedButton> createState() => _RoundedButtonState();
}

class _RoundedButtonState extends State<RoundedButton> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onPressed,
      child: Container(
          alignment: Alignment.center,
          width: widget.width,
          height: widget.height != null ? widget.height : 45,
          decoration: widget.isEnableBorder ?? false
              ? BoxDecoration(
                  color: backgroundColor,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(
                    color: appThemeColor,
                    width: 2.0,
                  ),
                )
              : boxDecoration(bgColor: appThemeColor, radius: 8.0),
          child: Text(widget.title ?? "",
              textAlign: TextAlign.center,
              style: widget.buttonTextStyle != null
                  ? widget.buttonTextStyle
                  : widget.isEnableBorder ?? false
                      ? buttonTextStyle.copyWith(color: appThemeColor)
                      : buttonTextStyle)),
    );
  }
}
