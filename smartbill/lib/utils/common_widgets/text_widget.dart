// ignore_for_file: must_be_immutable

import 'package:flutter/material.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../constants/styles.dart';

class TextWidget extends StatelessWidget {
  TextWidget(this.text,
      {this.fontSize = textSizeLargeMedium,
      this.textColor,
      this.fontFamily,
      this.isCentered = false,
      this.maxLine = 1,
      this.latterSpacing = 0.5,
      this.textAllCaps = false,
      this.isLongText = false,
      this.lineThrough = false,
      this.fontWeight,
      this.overflow,
      Key? key})
      : super(key: key);

  String text;
  var fontSize = textSizeLargeMedium;
  Color? textColor;
  var fontFamily;
  var isCentered = false;
  var maxLine = 1;
  var latterSpacing = 0.5;
  bool textAllCaps = false;
  var isLongText = false;
  bool lineThrough = false;
  FontWeight? fontWeight;
  TextOverflow? overflow;
  @override
  Widget build(BuildContext context) {
    return Text(
      textAllCaps ? text.toUpperCase() : text,
      textAlign: isCentered ? TextAlign.center : TextAlign.start,
      maxLines: isLongText ? null : maxLine,
      overflow: overflow ?? TextOverflow.clip,
      style: TextStyle(
          fontFamily: fontFamily ?? null,
          fontSize: fontSize,
          color: textColor ?? primaryTextColor,
          height: 1.5,
          letterSpacing: latterSpacing,
          decoration: lineThrough ? TextDecoration.lineThrough : TextDecoration.none,
          fontWeight: fontWeight),
    );
  }
}
