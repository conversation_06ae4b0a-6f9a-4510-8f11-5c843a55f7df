import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/colors.dart';

class LoadingAlertWidget {
  static late BuildContext dialogContext;

  static onLoading(BuildContext context) {
    dialogContext = context;
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: secondaryTextColor,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(10.0))),
          contentPadding: EdgeInsets.all(0.0),
          insetPadding: EdgeInsets.symmetric(horizontal: 100),
          content: Padding(
            padding: EdgeInsets.only(top: 20, bottom: 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                Text("Please wait....", style: primaryTextStyle(color: primaryTextColor)),
              ],
            ),
          ),
        );
      },
    );
  }

  static onStopping() {
    Navigator.pop(dialogContext, true);
  }
}
