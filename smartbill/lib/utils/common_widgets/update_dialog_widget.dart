// ignore_for_file: use_build_context_synchronously

import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';
import '../common_function/fn_utilities.dart';
import '../constants/colors.dart';

class UpdateDailogue {
  static Future<bool> appUpdateAlert(
    BuildContext context, {
    double? appVersion,
    String title = "",
    String description = "",
    bool isMandatory = false,
  }) async {
    // Save the last alert opened time and check the condiction for difference between
    // lost alert opened time and current time .

    SharedPreferences prefs = await SharedPreferences.getInstance();
    int differenceInDays = 0;
    DateTime currentTime;
    int updateLaterTimestamp = prefs.getInt('updateLaterTimestamp') ?? 0;
    DateTime updateLaterTime;
    if (updateLaterTimestamp > 0) {
      updateLaterTime = DateTime.fromMillisecondsSinceEpoch(updateLaterTimestamp);
      currentTime = DateTime.now();
      Duration difference = currentTime.difference(updateLaterTime);
      differenceInDays = difference.inDays;
    } else {
      differenceInDays = 1;
      currentTime = DateTime.now();
    }
    double width = MediaQuery.of(context).size.width;
    Dialog updateDialog = Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12.0),
      ),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20.0),
        ),
        height: 450,
        width: width * 0.9,
        padding: const EdgeInsets.all(10.0),
        child: Stack(
          children: <Widget>[
            SizedBox(
              width: MediaQuery.of(context).size.width,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.stretch,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Image.asset(updateAppPNG, height: 150),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const SizedBox(
                        height: 20,
                      ),
                      Container(
                        alignment: Alignment.center,
                        child: Text(
                          title,
                          textAlign: TextAlign.start,
                          style: black15BoldTextStyle.copyWith(fontSize: 24, color: appThemeColor),
                        ),
                      ),
                      const SizedBox(
                        height: 20,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(13.0),
                        child: Container(
                          alignment: Alignment.center,
                          color: whiteColor,
                          child: Text(
                            description,
                            textAlign: TextAlign.center,
                            style: headingFontStyle2.copyWith(fontSize: 18, fontWeight: FontWeight.w500),
                          ),
                        ),
                      )
                    ],
                  ),
                  const SizedBox(
                    height: 10,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      !isMandatory
                          ? Expanded(
                              child: GestureDetector(
                                  onTap: () {
                                    Navigator.pop(context, true);
                                  },
                                  child: Container(
                                    padding: const EdgeInsets.all(10),
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: appThemeColor,
                                      borderRadius: BorderRadius.circular(20),
                                    ),
                                    child: Text(
                                      "Later",
                                      style: white14BoldTextStyle.copyWith(fontSize: 18),
                                    ),
                                  )),
                            )
                          : Container(),
                      const SizedBox(width: 10),
                      Expanded(
                        child: GestureDetector(
                          onTap: () {
                            FnUtilities fnUtilities = FnUtilities();
                            fnUtilities.openAppInPlayStore();
                          },
                          child: Container(
                            padding: const EdgeInsets.all(10),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: buttonThemeColor,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Text(
                              "Update",
                              style: white14BoldTextStyle.copyWith(fontSize: 18),
                            ),
                          ),
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
          ],
        ),
      ),
    );
// check the Condiction for one day passed or not
    if (differenceInDays >= 1 || isMandatory) {
      bool result = await showDialog(
          barrierDismissible: false,
          context: context,
          builder: (BuildContext context) {
            return WillPopScope(onWillPop: () => Future.value(false), child: updateDialog);
          });
      prefs.setInt('updateLaterTimestamp', currentTime.millisecondsSinceEpoch);
      return result;
    } else {
      return true;
    }
  }
}
