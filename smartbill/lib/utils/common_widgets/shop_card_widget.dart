import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/utils/constants/colors.dart';
import 'package:smartbill/utils/constants/images.dart';
import 'package:smartbill/utils/constants/styles.dart';

class ShopCardWidget extends StatefulWidget {
  ShopCardWidget(
      {Key? key,
      this.isItemSelected,
      this.workspace,
      this.ontap,
      this.isShowRadioButtons,
      this.isShowPopUp,
      this.popupMenuBuilder})
      : super(key: key);
  bool? isItemSelected = false;
  bool? isShowRadioButtons = false;
  WorkSpace? workspace;
  VoidCallback? ontap;
  bool? isShowPopUp = false;
  final PopupMenuButton Function(int index)? popupMenuBuilder;

  @override
  State<ShopCardWidget> createState() => _ShopCardWidgetState();
}

class _ShopCardWidgetState extends State<ShopCardWidget> {
  bool isLock = false;
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 5, bottom: 5, left: 16, right: 16),
      child: Container(
        decoration: boxdecoration(
            bgColor: backgroundColor, showShadow: true, radius: 16),
        padding: EdgeInsets.all(20),
        child: Row(
          children: [
            /// Images Field
            Column(
              children: [
                widget.isItemSelected ?? false
                    ? Icon(
                        Icons.cloud_done_outlined,
                        color: primaryTextColor,
                        size: 50,
                      )
                    : Icon(
                        Icons.cloud_circle,
                        color: primaryTextColor,
                        size: 50,
                      ),
              ],
            ),
            SizedBox(
              width: 10,
            ),

            ///Shop Details Column
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(widget.workspace?.workspaceName ?? "",
                      style: headingTextStyle3.apply(color: primaryTextColor)),
                  Text('SID : ' +( widget.workspace?.workspaceId ??""),
                      style: greyTextStyle),
                  if (widget.workspace?.userName != null) ...[
                    Text('UID  : ' +( widget.workspace?.userName ?? ""),
                        style: greyTextStyle),
                  ],
                ],
              ),
            ),

            ///Popup Menu Column
            Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (widget.isShowPopUp ?? false) ...[
                  buildPopupMenuButton(),
                  // InkWell(
                  //   onTap: widget.ontap,
                  //   child: SvgPicture.asset(popUpDotSVG),
                  // ),
                  //widget.popupMenuBuilder(index),
                ],
                if (widget.isShowRadioButtons ?? false) ...[
                  widget.isItemSelected?? false
                      ? SvgPicture.asset(radioSelectedSVG)
                      : SvgPicture.asset(radioDefaultSVG)
                ]
              ],
            )
          ],
        ),
      ),
    );
  }

  PopupMenuButton<String> buildPopupMenuButton() {
    return PopupMenuButton<String>(
      itemBuilder: (BuildContext context) {
        return <PopupMenuEntry<String>>[
          PopupMenuItem<String>(
            value: 'option1',
            onTap: widget.ontap,
            child: Text("Sign out"),
          ),
        ];
      },
    );
  }
}
