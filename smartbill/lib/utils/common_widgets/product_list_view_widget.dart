import 'package:flutter/material.dart';
import 'package:smartbill/utils/common_widgets/text_widget.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../../model/products.dart';
import '../constants/colors.dart';
import '../constants/styles.dart';

class ProductListViewCard extends StatefulWidget {
  ProductListViewCard({Key? key,this.item,this.onTab,this.onLongPress}) : super(key: key);
  
  Products? item;
  final VoidCallback? onTab;
  final VoidCallback? onLongPress;


  @override
  State<ProductListViewCard> createState() => _ProductListViewCardState();
}

class _ProductListViewCardState extends State<ProductListViewCard> {
  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: widget.onTab,
      onLongPress: widget.onLongPress,
      child: Container(
        margin: const EdgeInsets.all(2.0),
        padding: EdgeInsets.only(left: 16, right: 16, top: 2, bottom: 2),
        decoration: boxDecoration(
            radius: 10, showShadow: true, bgColor: secondaryTextColor),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Container(
              width: 80,
              alignment: Alignment.centerLeft,
              child: TextWidget(widget.item?.productSaleId.toString() ??"",
                  textColor: primaryTextColor,
                  fontSize: textSizeLargeMedium,
                  fontFamily: fontSemibold,
                  isCentered: true),
            ),
            SizedBox(width: 5),
            Expanded(
              child: Container(
                alignment: Alignment.centerLeft,
                child: TextWidget(widget.item?.name ??"",
                    textColor: primaryTextColor,
                    fontSize: textSizeLargeMedium,
                    fontFamily: fontSemibold,
                    isLongText: true),
              ),
            ),
            SizedBox(width: 5),
            Container(
              width: 100,
              alignment: Alignment.centerRight,
              child: widget.item?.price == "" || widget.item?.price == null
                  ? TextWidget("",
                      textColor: primaryTextColor,
                      fontSize: textSizeLargeMedium,
                      fontFamily: fontSemibold)
                  : TextWidget("Rs.${widget.item?.price}",
                      textColor: primaryTextColor,
                      fontSize: textSizeLargeMedium,
                      fontFamily: fontSemibold),
            ),
          ],
        ),
      ),
    );
  }
}
