import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/colors.dart';
// int selectedIndex = 0;

class BottomNavigation extends StatefulWidget {
  @override
  _BottomNavigationState createState() => _BottomNavigationState();
}

class _BottomNavigationState extends State<BottomNavigation> {
  //NAVIGATION ROUTER FOR NAVIGATION BAR
  rootNavigationBasedOnIndex(BuildContext context) {
    // var route = ModalRoute.of(context);
    // if (selectedIndex == 0 && route.settings.name != "home") {
    //   Navigator.of(context)
    //       .pushNamedAndRemoveUntil('home', (Route<dynamic> route) => false);
    // }
    // if (selectedIndex == 1 && route.settings.name != "billdashboard") {
    //   Navigator.of(context).pushNamed(
    //     "billdashboard",
    //   );
    // }
    // if (selectedIndex == 2 && route.settings.name != "settings") {
    //   Navigator.of(context).pushNamed(
    //     "settings",
    //   );
    // }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      child: BottomNavigationBar(
        backgroundColor: appThemeColor,
        selectedLabelStyle: TextStyle(color: Colors.white),
        unselectedLabelStyle: TextStyle(color: Colors.white),
        type: BottomNavigationBarType.fixed,
        currentIndex: 1,
        selectedItemColor: white,
        unselectedItemColor: white,
        items: [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard, color: Colors.white),
            label: "Options",
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.receipt, color: Colors.white),
            label: "Bill",
          ),
          BottomNavigationBarItem(icon: Icon(Icons.settings, color: Colors.white), label: "Settings")
        ],
        onTap: (value) {
          setState(() {
            // selectedIndex = value;
          });
          rootNavigationBasedOnIndex(context);
        },
      ),
    );
  }
}

class BaseAppBar extends StatelessWidget implements PreferredSizeWidget {
  final Color backgroundColor = appThemeColor;
  final Text? title;
  final AppBar? appBar;
  final List<Widget>? widgets;
  final bool automaticallyImplementing;

  /// you can add more fields that meet your needs

  BaseAppBar({
    this.title,
    this.appBar,
    this.widgets,
    this.automaticallyImplementing = true,
  });

  @override
  Widget build(BuildContext context) {
    return AppBar(
      title: title,
      automaticallyImplyLeading: automaticallyImplementing,
      backgroundColor: backgroundColor,
      actions: widgets,
      elevation: 0,
      iconTheme: IconThemeData(color: secondaryTextColor),
    );
  }

  @override
  Size get preferredSize => new Size.fromHeight(appBar!.preferredSize.height);
}

// class BaseDrawer extends StatefulWidget {
//   @override
//   State<BaseDrawer> createState() => _BaseDrawerState();
// }

// class _BaseDrawerState extends State<BaseDrawer> {
//   FnUtilities fnUtilities = new FnUtilities();
//   DBOperations dbOperations = new DBOperations();
//   RolePermissionsUtilities rolePermissions = new RolePermissionsUtilities();
//   WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
//   CommonDB commonDB = CommonDB();
//   final dbHelper = DatabaseHelper.instance;
//   bool isLock = false;

//   //SIGN OUT FUNCTION
//   removeActiveWorkspaceAndUser(WorkSpace item) async {
//     SharedPreferences prefs = await SharedPreferences.getInstance();
//     setState(() {
//       ListUtility.signedWorkspaceList.removeWhere((element) => element.workspaceId == item.workspaceId);
//       deleteSignedWorkspace(item.workspaceId);
//     });
//     if (ListUtility.signedWorkspaceList.length >= 1) {
//       Navigator.pop(context);
//       Navigator.pop(context);
//       if (MyApp.activeWorkspace.workspaceId == item.workspaceId) {
//         setState(() {
//           MyApp.activeWorkspace = ListUtility.signedWorkspaceList.last;
//           MyApp.activeUser = ListUtility.userList.firstWhere((element) => element.username == MyApp.activeWorkspace.userName);
//         });
//         prefs.setString("activeWorkspace", jsonEncode(MyApp.activeWorkspace));
//         Navigator.pushReplacementNamed(context, "home");
//       }
//     } else {
//       MyApp.activeWorkspace = WorkSpace();
//       MyApp.activeUser = User();
//       prefs.remove("activeWorkspace");
//       prefs.remove("userList");
//       Navigator.of(context).pushNamedAndRemoveUntil('signIn', (Route<dynamic> route) => false);
//     }
//   }

//   //DELETE ALL RECODES BASED ON WORKSPACE
//   deleteAllRecordsBasedOnWorkspace(int index) async {
//     await commonDB.deleteAllRecordsBasedOnWorkspace(ListUtility.signedWorkspaceList[index].workspaceId);
//   }

//   //CHECK DATA SYNCED OR NOT
//   checkAlertSync(int index) async {
//     if (await commonDB.checkSync(ListUtility.signedWorkspaceList[index]) != 0) {
//       alertSync(context, index);
//     } else {
//       await deleteAllRecordsBasedOnWorkspace(index);
//       removeActiveWorkspaceAndUser(ListUtility.signedWorkspaceList[index]);
//     }
//   }

//   //SYNC ALERT DIALOG
//   alertSync(BuildContext context, int index) {
//     var width = MediaQuery.of(context).size.width;
//     return showDialog(
//         context: context,
//         builder: (context) {
//           return StatefulBuilder(builder: (context, setState) {
//             return Center(
//               child: SingleChildScrollView(
//                 child: AlertDialog(
//                     content: Container(
//                   width: double.maxFinite,
//                   child: Form(
//                     child: Column(
//                       mainAxisAlignment: MainAxisAlignment.start,
//                       crossAxisAlignment: CrossAxisAlignment.center,
//                       mainAxisSize: MainAxisSize.max,
//                       children: [
//                         Text("Some records not synced, Please process to sync else you will loose you unsynced data"),
//                         SizedBox(
//                           height: 20,
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           crossAxisAlignment: CrossAxisAlignment.end,
//                           children: <Widget>[
//                             GestureDetector(
//                               onTap: () async {
//                                 await deleteAllRecordsBasedOnWorkspace(index);
//                                 removeActiveWorkspaceAndUser(ListUtility.signedWorkspaceList[index]);
//                               },
//                               child: Container(
//                                 alignment: Alignment.center,
//                                 height: 45,
//                                 width: width / 3.5,
//                                 child: TextWidget("Ok", textColor: secondaryTextColor, isCentered: true),
//                                 decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                               ),
//                             ),
//                             GestureDetector(
//                               onTap: () async {
//                                 Navigator.pop(context);
//                                 Navigator.pop(context);
//                                 Navigator.pop(context);
//                               },
//                               child: Container(
//                                 alignment: Alignment.center,
//                                 height: 45,
//                                 width: width / 3.5,
//                                 child: TextWidget("Cancel", textColor: secondaryTextColor, isCentered: true),
//                                 decoration: boxDecoration(bgColor: buttonThemeColor, radius: 8.0),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ),
//                   ),
//                 )),
//               ),
//             );
//           });
//         });
//   }

//   //DELETE SIGNED WORKSPACE
//   deleteSignedWorkspace(id) async {
//     DBFuctionsScript dbScript = DBFuctionsScript();
//     await dbScript.deleteFunction(db_workspace, db_workspace_workspaceId, id);
//   }

//   @override
//   Widget build(BuildContext context) {
//     double width = MediaQuery.of(context).size.width;
//     double height = MediaQuery.of(context).size.height;
//     return Drawer(
//       child: Container(
//         color: secondaryTextColor,
//         child: Column(
//           children: <Widget>[
//             Expanded(
//               child: Column(children: <Widget>[
//                 Container(
//                   color: appThemeColor,
//                   child: Column(
//                     children: [
//                       SizedBox(
//                         height: 30,
//                       ),
//                       ListTile(title: Text("Shops", style: TextStyle(color: secondaryTextColor, fontSize: textSizeNormal, fontFamily: fontMedium))),
//                     ],
//                   ),
//                 ),
//                 Expanded(
//                   child: ListView.builder(
//                       itemCount: ListUtility.signedWorkspaceList.length,
//                       itemBuilder: (BuildContext context, int index) {
//                         var item = ListUtility.signedWorkspaceList[index];
//                         return ListTile(
//                           leading: (item.workspaceId == MyApp.activeWorkspace.workspaceId)
//                               ? Icon(
//                                   Icons.cloud_done_outlined,
//                                   color: primaryTextColor,
//                                   size: 50,
//                                 )
//                               : Icon(
//                                   Icons.cloud_circle,
//                                   color: primaryTextColor,
//                                   size: 50,
//                                 ),
//                           title: Text(item.workspaceName, style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
//                           subtitle: Column(
//                             mainAxisAlignment: MainAxisAlignment.start,
//                             children: [
//                               Text('SID : ' + item.workspaceId, style: TextStyle(fontFamily: fontMedium, fontSize: 12)),
//                               Text('UID  : ' + item.userName, style: TextStyle(fontFamily: fontMedium, fontSize: 12)),
//                             ],
//                           ),
//                           onTap: () async {
//                             if (!isLock) {
//                               isLock = true;
//                               setState(() {
//                                 MyApp.activeWorkspace = item;
//                               });
//                               await fnUtilities.setActiveWorkspaceAndUser(item);
//                               await dbOperations.queryAllShopDeatails();
//                               selectedIndex = 1;
//                               Navigator.of(context).pushNamedAndRemoveUntil('bottombar', (Route<dynamic> route) => false);
//                               isLock = false;
//                             }
//                           },
//                           trailing: PopupMenuButton(
//                             itemBuilder: (BuildContext context) {
//                               return <PopupMenuEntry>[
//                                 PopupMenuItem(
//                                   child: ListTile(
//                                     title: Text("Sign out"),
//                                     leading: Icon(Icons.exit_to_app),
//                                     onTap: () async {
//                                       if (!isLock) {
//                                         isLock = true;
//                                         await checkAlertSync(index);
//                                         isLock = false;
//                                       }
//                                     },
//                                   ),
//                                 ),
//                               ];
//                             },
//                           ),
//                         );
//                       }),
//                 ),
//               ]),
//             ),
//             Container(
//                 alignment: Alignment.centerLeft,
//                 padding: EdgeInsets.fromLTRB(20, 0, 20, 20),
//                 child: Align(
//                     alignment: FractionalOffset.bottomCenter,
//                     child: Column(
//                       children: <Widget>[
//                         Divider(
//                           thickness: 1,
//                         ),
//                         SizedBox(
//                           height: 5,
//                         ),
//                         InkWell(
//                           onTap: () {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               Navigator.push(context, MaterialPageRoute(builder: (context) => WorkspacePage()));
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             padding: EdgeInsets.symmetric(vertical: 8),
//                             width: width / 1.3,
//                             child: Row(
//                               children: [
//                                 Icon(
//                                   Icons.add_circle_outline,
//                                   color: primaryTextColor,
//                                   size: 20,
//                                 ),
//                                 SizedBox(
//                                   width: 20,
//                                 ),
//                                 Text('Manage Shops', style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
//                               ],
//                             ),
//                           ),
//                         ),
//                         InkWell(
//                           onTap: () async {
//                             if (!isLock) {
//                               isLock = true;
//                               Navigator.pop(context);
//                               if (rolePermissions.isViewPermissions(dbOperations.returnPermission("Member")) == true) {
//                                 Navigator.push(context, MaterialPageRoute(builder: (context) => AddMember()));
//                               } else {
//                                 showToast("You don't have a permission");
//                               }
//                               isLock = false;
//                             }
//                           },
//                           child: Container(
//                             padding: EdgeInsets.symmetric(vertical: 8),
//                             width: width / 1.3,
//                             child: Row(
//                               children: [
//                                 Icon(Icons.people_rounded, color: primaryTextColor, size: 20),
//                                 SizedBox(
//                                   width: 20,
//                                 ),
//                                 Text('Manage User', style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
//                               ],
//                             ),
//                           ),
//                         ),
//                         Row(
//                           mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                           children: [
//                             InkWell(
//                               onTap: () {
//                                 if (!isLock) {
//                                   isLock = true;
//                                   Navigator.pop(context);
//                                   Navigator.push(context, MaterialPageRoute(builder: (context) => Support()));
//                                   isLock = false;
//                                 }
//                               },
//                               child: Container(
//                                 padding: EdgeInsets.symmetric(vertical: 8),
//                                 child: Row(
//                                   children: [
//                                     Icon(Icons.help, color: primaryTextColor, size: 20),
//                                     SizedBox(
//                                       width: 20,
//                                     ),
//                                     Text('Help', style: TextStyle(color: primaryTextColor, fontFamily: fontMedium)),
//                                   ],
//                                 ),
//                               ),
//                             ),
//                             ClipRRect(
//                               borderRadius: BorderRadius.circular(10.0),
//                               child: Container(
//                                 padding: EdgeInsets.all(5),
//                                 alignment: Alignment.centerRight,
//                                 child: Text('Version : ${MyApp.androidVersionCode}',
//                                     textAlign: TextAlign.right, style: TextStyle(color: Colors.lightBlue, fontFamily: fontMedium, fontStyle: FontStyle.italic, fontSize: 12)),
//                               ),
//                             ),
//                           ],
//                         ),
//                       ],
//                     ))),
//           ],
//         ),
//       ),
//     );
//   }
// }
