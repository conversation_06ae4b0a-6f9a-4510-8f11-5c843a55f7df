// ignore_for_file: avoid_print

import 'package:flutter/material.dart'; 
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import 'package:smartbill/utils/razorpay/model/generate_order_id.dart';
import 'package:smartbill/utils/razorpay/model/payment_success_callback_response.dart';
import 'package:smartbill/utils/razorpay/razorpay_package.dart';
import '../../Repositories/subscription_repository.dart';
import '../../main.dart';
import 'model/payment_model.dart';
import 'model/razorpay_request.dart';
import 'pages/payment_failed.dart';
import 'pages/payment_success.dart';

class RazorPayClass {
  FnUtilities? fnUtilities = FnUtilities();
  Razorpay? _razorpay;
  BuildContext? paymentContext;
  Payment? paymentDetails;
  ParentFunctionCallback? parentRefreshFun;
  RazorPayClass(BuildContext context, {ParentFunctionCallback? refreshFun}) {
    _razorpay = Razorpay();
    paymentContext = context;
    parentRefreshFun = refreshFun;
    _razorpay?.on(Razorpay.EVENT_PAYMENT_SUCCESS, _handlePaymentSuccess);
    _razorpay?.on(Razorpay.EVENT_PAYMENT_ERROR, _handlePaymentError);
    _razorpay?.on(Razorpay.EVENT_EXTERNAL_WALLET, _handleExternalWallet);
  }
  void openCheckout(Payment? payment, String? orderId) async {
    paymentDetails = payment;
    var options = {
      'key': 'rzp_test_Kp6Pbb0pNUlJdp',
      "order_id": orderId,
      'amount': 1 * 100,
      'currency': 'INR',
      'name': 'Cloudstier Solutions Pvt. Ltd.',
      'description': 'SmartBill payment trasaction',
      'retry': {'enabled': true, 'max_count': 1},
      'send_sms_hash': true,
      'prefill': {
        'name': '${MyApp.activeUser.firstName}',
        'contact': '${MyApp.activeUser.mobile}',
        'email': MyApp.activeUser.email ?? ""
      },
      'external': {
        'wallets': ['paytm']
      },
      'notes': {
        'address': 'Razorpay Corporate Office',
      },
      'theme': {
        'color': '#3399cc',
      },
    };
    try {
      _razorpay?.open(options);
    } catch (e) {
      debugPrint('Error: e');
    }
  }

  void startPayment(RazorPayRequest item) {
    try {
      fnUtilities?.onLoading(paymentContext!);
      GenerateOrderID model = GenerateOrderID();
      model.planID = item.planId ?? 0;
      model.workspaceID = MyApp.activeWorkspace.workspaceId ?? "";
      generateOrderId(model).then((value) {
        fnUtilities?.onStopping();
        if (value.status == 1) {
          //reqId = item.requestId;
          openCheckout(item.payment, value.result?.orderID);
        } else {
          showToast(value.message);
        }
      });
    } catch (e) {
      fnUtilities?.onStopping();
      showToast("Payment process failed");
    }
  }

  void _handlePaymentSuccess(PaymentSuccessResponse response) {
    PaymentSuccessCallBack model = PaymentSuccessCallBack();
    model.razorpayOrderId = response.orderId;
    model.razorpayPaymentId = response.paymentId;
    model.razorpaySignature = response.signature;
    paymentSuccessCallBack(model).then((value) {
      if (value.status == 1) {
        value.amount = paymentDetails?.remainingAmount ?? 0.0;
        clearInstance();
        Navigator.push(
            paymentContext!,
            MaterialPageRoute(
                builder: (context) => PaymentSuccessPage(
                      payment: value,
                    ))).then((value) {
          if (parentRefreshFun != null) {
            pageRefresh(1);
          }
        });
      } else {
        showToast(value.message);
      }
    });
  }

  void _handlePaymentError(PaymentFailureResponse response) {
    print('Error Response: $response');
    Navigator.push(paymentContext!,
            MaterialPageRoute(builder: (context) => PaymentFailedPage()))
        .then((value) {
      if (parentRefreshFun != null) {
        pageRefresh(0);
      }
    });
  }

  void _handleExternalWallet(ExternalWalletResponse response) {
    print('External SDK Response: $response');
  }

  void clearInstance() {
    _razorpay?.clear();
  }

  Future<void> pageRefresh(int requestIndex) async {
    parentRefreshFun!(requestIndex);
  }
}
