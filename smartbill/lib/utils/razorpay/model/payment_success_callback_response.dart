class PaymentSuccessCallBack {
  String? razorpayPaymentId;
  String? razorpayOrderId;
  String? razorpaySignature;

  PaymentSuccessCallBack(
      {this.razorpayPaymentId, this.razorpayOrderId, this.razorpaySignature});

  PaymentSuccessCallBack.fromJson(Map<String, dynamic> json) {
    razorpayPaymentId = json['razorpay_payment_id'];
    razorpayOrderId = json['razorpay_order_id'];
    razorpaySignature = json['razorpay_signature'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['razorpay_payment_id'] = razorpayPaymentId;
    data['razorpay_order_id'] = razorpayOrderId;
    data['razorpay_signature'] = razorpaySignature;
    return data;
  }
}
