class Payment {
  double? amount;
  double? remainingAmount;
  String? createdDate;
  String? modifiedDate;
  List<PaymentDetails>? paymentDetails;

// Temp Variable

  String? orderId;

  Payment(
      {this.amount,
      this.remainingAmount,
      this.createdDate,
      this.modifiedDate,
      this.paymentDetails});

  Payment.fromJson(Map<String, dynamic> json) {
    amount = json['amount'];
    remainingAmount = json['remainingAmount'];
    createdDate = json['createdDate'];
    modifiedDate = json['modifiedDate'];
    if (json['paymentDetails'] != null) {
      paymentDetails = <PaymentDetails>[];
      json['paymentDetails'].forEach((v) {
        paymentDetails?.add(PaymentDetails.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['amount'] = amount;
    data['remainingAmount'] = remainingAmount;
    data['createdDate'] = createdDate;
    data['modifiedDate'] = modifiedDate;
    if (paymentDetails != null) {
      data['paymentDetails'] = paymentDetails?.map((v) => v.toJson()).toList();
    }

    return data;
  }
}

class PaymentDetails {
  int? id;
  int? requestPaymentID;
  double? amount;
  int? paymentModeID;
  String? paymentModeName;
  String? createdDate;
  String? modifiedDate;

  PaymentDetails(
      {this.id,
      this.requestPaymentID,
      this.amount,
      this.paymentModeID,
      this.paymentModeName,
      this.createdDate,
      this.modifiedDate});

  PaymentDetails.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    requestPaymentID = json['requestPaymentID'];
    amount = json['amount'];
    paymentModeID = json['paymentModeID'];
    paymentModeName = json['paymentModeName'];
    createdDate = json['createdDate'];
    modifiedDate = json['modifiedDate'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['id'] = id;
    data['requestPaymentID'] = requestPaymentID;
    data['amount'] = amount;
    data['paymentModeID'] = paymentModeID;
    data['paymentModeName'] = paymentModeName;
    data['createdDate'] = createdDate;
    data['modifiedDate'] = modifiedDate;
    return data;
  }
}
