class GenerateOrderID {
  String? workspaceID;
  int? planID;

  GenerateOrderID({this.workspaceID, this.planID});

  GenerateOrderID.fromJson(Map<String, dynamic> json) {
    workspaceID = json['workspaceID'];
    planID = json['planID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['workspaceID'] = workspaceID;
    data['planID'] = planID;
    return data;
  }
}

class GenerateOrderIDResponse {
  int? status;
  String? message;
  Order? result;

  GenerateOrderIDResponse({this.status, this.message, this.result});

  GenerateOrderIDResponse.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    result = json['result'] != null ? Order.fromJson(json['result']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    if (result != null) {
      data['result'] = result?.toJson();
    }
    return data;
  }
}

class Order {
  String? orderID;

  Order({this.orderID});

  Order.fromJson(Map<String, dynamic> json) {
    orderID = json['orderID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['orderID'] = orderID;
    return data;
  }
}
