class PaymentSuccessCallBackRes {
  int? status;
  String? message;
  int? paymentId;

  // Temp Variable

  double? amount;

  PaymentSuccessCallBackRes({this.status, this.message, this.paymentId});

  PaymentSuccessCallBackRes.fromJson(Map<String, dynamic> json) {
    status = json['status'];
    message = json['message'];
    paymentId = json['paymentId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['status'] = status;
    data['message'] = message;
    data['paymentId'] = paymentId;
    return data;
  }
}
