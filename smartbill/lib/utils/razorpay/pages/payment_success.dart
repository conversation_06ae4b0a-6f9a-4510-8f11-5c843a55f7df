import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/razorpay/model/payment_success_callback.dart';

class PaymentSuccessPage extends StatefulWidget {
  PaymentSuccessCallBackRes? payment;

  PaymentSuccessPage({Key? key, this.payment}) : super(key: key);

  @override
  State<PaymentSuccessPage> createState() => _PaymentSuccessPageState();
}

class _PaymentSuccessPageState extends State<PaymentSuccessPage> {
  double width = 0.0;
  double height = 0.0;
  FnUtilities comFun = FnUtilities();

  @override
  Widget build(BuildContext context) {
    height = MediaQuery.of(context).size.height;
    width = MediaQuery.of(context).size.width;
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Align(
            alignment: Alignment.center,
            child: Image(
              image: AssetImage("assets/images/successful.png"),
              height: 330,
            ),
          ),
          Center(
            child: Text(
              "Payment Successful",
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: greenColor, fontSize: 35, fontWeight: FontWeight.w700),
            ),
          ),
          const SizedBox(
            height: 20,
          ),
          Center(
            child: Text(
              "Payment ID : ${widget.payment?.paymentId}",
              textAlign: TextAlign.center,
              style: const TextStyle(
                  color: Colors.black,
                  fontSize: 17,
                  fontWeight: FontWeight.w400),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          const Center(
            child: Text(
              "Amount paid",
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: Colors.black,
                  fontSize: 16,
                  fontWeight: FontWeight.w400),
            ),
          ),
          const SizedBox(
            height: 10,
          ),
          Center(
            child: Text(
              "Rs.${comFun.checkQtyDoubleValue(widget.payment?.amount.toString() ?? "")}",
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: greenColor, fontSize: 17, fontWeight: FontWeight.w600),
            ),
          ),

          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Image(
                  width: width / 4,
                  image: const AssetImage("assets/images/checkmark.png"),
                ),
              ),
            ],
          )
          // GestureDetector(
          //   onTap: () {
          //     Navigator.pop(context);
          //   },
          //   child: Container(
          //     alignment: Alignment.center,
          //     height: 45,
          //     width: 150,
          //     child: Text(
          //       "Back",
          //       style: white14BoldTextStyle,
          //     ),
          //     decoration: BoxDecoration(
          //       borderRadius: BorderRadius.circular(5),
          //       color: buttonThemeColor,
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
