import 'package:flutter/material.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/screens/settings/support.dart';
import 'package:smartbill/utils/constants/colors.dart';

import '../../constants/styles.dart';

class PaymentFailedPage extends StatefulWidget {
  PaymentFailedPage({Key? key}) : super(key: key);

  @override
  State<PaymentFailedPage> createState() => _PaymentFailedPageState();
}

class _PaymentFailedPageState extends State<PaymentFailedPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Align(
            alignment: Alignment.center,
            child: Image(
              image: AssetImage("assets/images/not_found.png"),
              height: 330,
              // width: 320,
            ),
          ),
          Center(
            child: Text(
              "Payment Failed",
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: redColor, fontSize: 30, fontWeight: FontWeight.w500),
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          Container(
            padding: const EdgeInsets.only(left: 20, right: 20),
            child: Text(
              "Your transaction has been failed,  please try again",
              textAlign: TextAlign.center,
              style: TextStyle(
                  color: grey, fontSize: 20, fontWeight: FontWeight.w400),
            ),
          ),
          const SizedBox(
            height: 30,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 45,
                  width: 150,
                  child: Text(
                    "Back",
                    style: white14BoldTextStyle,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: buttonThemeColor,
                  ),
                ),
              ),
              const SizedBox(
                width: 20,
              ),
              GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                  Navigator.push(context,
                      MaterialPageRoute(builder: (context) => Support()));
                },
                child: Container(
                  alignment: Alignment.center,
                  height: 45,
                  width: 150,
                  child: Text(
                    "Contact Us",
                    style: white14BoldTextStyle,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(5),
                    color: buttonThemeColor,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
