// ignore_for_file: unnecessary_brace_in_string_interps
import 'package:smartbill/model/balance_model.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';

class BalanceDBScript {
  Future<int> insertBalance(Balance model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_balance, {
     db_balance_FK_CustomerID: model.customerID,
     db_balance_customerBalance: model.balance,
      db_product_rowStatus: model.rowStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllBalance() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_balance} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_balance_ID}");
    return result;
  }

  Future<int> updateBalance(Balance model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_balance} 
    SET ${db_balance_customerBalance} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_balance_FK_CustomerID} = ?
    ''', [model.balance, model.rowStatus, model.workspaceID, model.customerID]);
    return result;
  }

  Future<Map<String, dynamic>?> getBalanceByCustomerID(String customerID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_balance} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}' AND ${db_balance_FK_CustomerID}='$customerID'");
    if (result.isEmpty) {
      return null;
    }
    return result.first;
  }
}
