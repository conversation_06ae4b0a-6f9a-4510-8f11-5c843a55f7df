import 'package:intl/intl.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/reports.dart';

class SalesDetailsDBScript {
  //SALES INSERT
  Future<int> insertSalesDetails(Sales reports) async {
    String invoiceno = '';
    if (reports.invoiceNo != null) {
      invoiceno = invoiceno + reports.invoiceNo!;
      if (reports.invoiceExtension != null) {
        invoiceno = invoiceno + reports.invoiceExtension!;
      }
    }

    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_salesDetails, {
      db_sales_salesId: reports.salesId,
      db_sales_invoiceId: reports.invoiceId,
      db_sales_salesDate: reports.date,
      db_sales_salesTotalAmount: reports.totalAmount,
      db_sales_salesPaymentType: reports.paymentType,
      db_sales_salesTaxMode: reports.taxMode,
      db_sales_salesSync: reports.sync,
      db_workspace_workspaceId: reports.workspaceId,
      db_product_rowStatus: reports.rowStatus,
      db_sales_customerName: reports.customerName,
      db_sales_isActiveSale: reports.isActiveSale,
      db_sales_fkTableID: reports.fkTableID,
      db_sales_createdBy: reports.createdBy,
      db_sales_discountFormula: reports.discountFormula,
      db_sales_discountPrice: reports.discountPrice,
      db_sales_invoiceNo: invoiceno.isNotEmpty ? invoiceno : null,
      db_sales_fK_CustomerID: reports.customerID,
      db_sales_sales_MobileNo: reports.mobileNo,
      db_sales_sales_OrderType: reports.orderType,
      db_sales_sales_OnlinePlatformID: reports.onlinePlatformID,
      db_sales_sales_OnlineRefNo: reports.onlineRefNo,
      db_sales_status: reports.salesStatus,
      db_sales_payment_Invoice: reports.paymentInvoice,
      db_sales_invoice_Extension: reports.invoiceExtension,
      db_Payment_category_ID: reports.paymentCategoryID,
      db_salesCategoryId: reports.salesCategoryId,
      db_shift_Id: reports.shiftId,
      db_delivery_date_time: reports.deliveryDateAndTime
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsSalesDetails() async {
    Database db = await DatabaseHelper.instance.database;
    return await db
        .query(db_salesDetails, where: '$db_workspace_workspaceId=? and $db_product_rowStatus<2', whereArgs: [MyApp.activeWorkspace.workspaceId]);
  }

  Future<List<Map<String, dynamic>>> queryallRowsSalesId() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_salesDetails, where: '$db_workspace_workspaceId=?', whereArgs: [MyApp.activeWorkspace.workspaceId]);
  }

  Future<List<Map<String, dynamic>>> queryRowsByCustomerID(customerID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId.toString()}' AND $db_product_rowStatus < 2 AND $db_sales_fK_CustomerID = '$customerID' ORDER BY DATE($db_sales_salesDate)");
    return result;
  }

  Future<Map<String, dynamic>?> getSalesDetailsBySalesID(String salesID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId.toString()}' AND $db_product_rowStatus < 2 AND $db_sales_salesId = '$salesID'");
    if (result.isNotEmpty) {
      return result.first;
    }
    return null;
  }

  Future<List<Map<String, dynamic>>> queryAllGetSalesId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_sales_salesId FROM $db_salesDetails WHERE $db_workspace_workspaceId=? AND $db_product_rowStatus='${0}' OR  $db_product_rowStatus='${1}'",
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int?> queryGetSalesSyncNo(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT $db_sales_salesSync FROM $db_salesDetails WHERE $db_sales_salesId=? AND $db_workspace_workspaceId=?',
        [id, MyApp.activeWorkspace.workspaceId]));
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncSalesData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $db_sales_salesSync=${0} AND $db_workspace_workspaceId=? AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL)',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsByDateToDate(startDate, endDate, {String paymentId = "All"}) async {
    var startingDayofMonth = '${startDate}';
// Find the last day of the month.
    var endingDayofMonth = '${endDate}';

    Database db = await DatabaseHelper.instance.database;
    var sqlQuery;
    if (paymentId == "All") {
      sqlQuery =
          "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' ORDER BY DateTime($db_sales_salesDate) DESC";
    } else {
      sqlQuery =
          "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' ORDER BY DateTime($db_sales_salesDate) DESC";
    }

    var result = await db.rawQuery(sqlQuery);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsByMonthly(startingDayofMonth, endingDayofMonth, {String paymentId = "All"}) async {
    Database db = await DatabaseHelper.instance.database;

    var sqlQuery;
    if (paymentId == "All") {
      sqlQuery =
          "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND  $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' ORDER BY DateTime($db_sales_salesDate) DESC";
    } else {
      sqlQuery =
          "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND  $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' ORDER BY DateTime($db_sales_salesDate) DESC";
    }

    var result = await db.rawQuery(sqlQuery);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsByDate({String? roleId, String paymentId = "All"}) async {
    var now = new DateTime.now();
    var startingDayofMonth = '${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')}';
    Database db = await DatabaseHelper.instance.database;
    var beginningNextMonth = (now.month < 12) ? new DateTime(now.year, now.month + 1, 1) : new DateTime(now.year + 1, 1, 1);
    var lastDay = beginningNextMonth.subtract(new Duration(days: 1)).day;
    var endingDayofMonth = '${now.year}-${now.month.toString().padLeft(2, '0')}-${lastDay}';
    var sqlQuery;
    if (roleId == "3") {
      if (paymentId == "All") {
        sqlQuery =
            "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_createdBy = '${MyApp.activeUser.username}' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND date(date) = date('${startingDayofMonth}') ORDER BY DateTime($db_sales_salesDate) DESC";
      } else {
        sqlQuery =
            "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_createdBy = '${MyApp.activeUser.username}' AND $db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND date(date) = date('${startingDayofMonth}') ORDER BY DateTime($db_sales_salesDate) DESC";
      }
    } else {
      if (paymentId == "All") {
        sqlQuery =
            "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND date(date) = date('${startingDayofMonth}') ORDER BY DateTime($db_sales_salesDate) DESC";
      } else {
        sqlQuery =
            "SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND date(date) = date('${startingDayofMonth}') ORDER BY DateTime($db_sales_salesDate) DESC";
      }
    }

    return await db.rawQuery(sqlQuery);
  }

  Future findSalesNoBasedOnSalesId(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_sales_invoiceNo, $db_sales_invoiceId FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_salesId=?",
        [id]);
    return result;
  }

  Future<String> queryGetMaxInvoiceId(prefixName, bool resetSalesNo) async {
    Database db = await DatabaseHelper.instance.database;
    var count;
    if (resetSalesNo) {
      count = Sqflite.firstIntValue(await db.rawQuery(
          "SELECT COUNT($db_sales_salesDetailsId) FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND date($db_sales_salesDate) = date('${DateTime.now().toString()}') AND $db_sales_invoiceId LIKE '$prefixName%'"));
    } else {
      count = Sqflite.firstIntValue(await db.rawQuery(
          "SELECT COUNT($db_sales_salesDetailsId) FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_invoiceId LIKE '$prefixName%'"));
    }
    if (count == 0) {
      return "0";
    } else {
      var result = await db.rawQuery(
          "SELECT $db_sales_invoiceId,$db_sales_salesDetailsId FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_sales_invoiceId LIKE '$prefixName%' ORDER BY $db_sales_salesDetailsId");
      return result.last["invoiceId"].toString();
    }
  }

  Future<List<Map<String, dynamic>>> queryRowsSalesByPayment(startDate, endDate, id) async {
    var startingDayofMonth = '${startDate}';
    var result;
// Find the last day of the month.
    var endingDayofMonth = '${endDate}';

    Database db = await DatabaseHelper.instance.database;
    if (id != 0) {
      result = await db.rawQuery(
          "SELECT COUNT($db_sales_salesId) as $db_sales_salesDetailsId,$db_sales_salesId,$db_sales_salesDate,SUM(CAST($db_sales_salesTotalAmount as DOUBLE)) as $db_sales_salesTotalAmount,$db_sales_salesPaymentType FROM $db_salesDetails WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus='${0}' OR  $db_product_rowStatus='${1}' AND $db_sales_salesPaymentType=? AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' GROUP BY $db_sales_salesPaymentType",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT COUNT($db_sales_salesId) as $db_sales_salesDetailsId,$db_sales_salesId,$db_sales_salesDate,SUM(CAST($db_sales_salesTotalAmount as DOUBLE)) as $db_sales_salesTotalAmount,$db_sales_salesPaymentType FROM $db_salesDetails WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus='${0}' OR  $db_product_rowStatus='${1}' AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' GROUP BY $db_sales_salesPaymentType");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForActiveSales() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $db_sales_fkTableID IS NULL AND $db_product_rowStatus < 2 AND $db_sales_isActiveSale > 0 AND $db_workspace_workspaceId = ?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int?> nonSyncSaleByID(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT($db_sales_salesId) FROM $db_salesDetails WHERE $db_sales_salesId = ? AND $db_workspace_workspaceId = ? AND $db_sales_salesSync = ${0}",
        [id, MyApp.activeWorkspace.workspaceId]));
    return count;
  }

  Future<List<Map<String, dynamic>>> queryForActiveTableSales() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $db_sales_fkTableID NOT NULL AND $db_product_rowStatus < 2 AND $db_sales_isActiveSale > 0 AND $db_workspace_workspaceId = ?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> query01(String flag) async {
    Database db = await DatabaseHelper.instance.database;
    var tableOrderCondition = flag == "SO" ? '$db_sales_fkTableID IS NULL' : '$db_sales_fkTableID NOT NULL';
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $tableOrderCondition AND $db_product_rowStatus < 2 AND $db_sales_isActiveSale > 0 AND $db_workspace_workspaceId = ?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  ///Query for to select not completed sale
  Future<List<Map<String, dynamic>>> getNotCompletedSale() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db
        .rawQuery('SELECT * FROM $db_salesDetails WHERE $db_sales_status = 2 AND $db_workspace_workspaceId = ?', [MyApp.activeWorkspace.workspaceId]);

    return result;
  }

  Future<List<Map<String, dynamic>>> getAdvanceOrderSale() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $db_sales_status = 3 AND $db_workspace_workspaceId = ? AND $db_product_rowStatus < 2',
        [MyApp.activeWorkspace.workspaceId]);

    return result;
  }

  ///Query for to select sales transaction details from sales transaction table
  Future<List<Map<String, dynamic>>> getSalesFromSalesTransaction(String saleId) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_salesTransactions WHERE $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2 AND $db_workspace_workspaceId = ?',
        [saleId, MyApp.activeWorkspace.workspaceId]);

    return result;
  }

  Future<int?> queryActiveTableRowCount(id) async {
    Database db = await DatabaseHelper.instance.database;
    return Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($db_sales_fkTableID) FROM $db_salesDetails WHERE $db_sales_fkTableID = ? AND $db_product_rowStatus < 2 AND $db_sales_isActiveSale = ${1} ',
        [id]));
  }

  Future<List<Map<String, dynamic>>> queryRowsProductsSalesByDateToDate(startDate, endDate, id) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    if (id != "All") {
      result = await db.rawQuery(
          "SELECT $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,$db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND  $db_salesTrans_salesProductId=? AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_salesProductId, $db_salesTrans_price",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,$db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_salesProductId, $db_salesTrans_price");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsUserSalesByDateToDate(startDate, endDate, id) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    if (id != "All") {
      result = await db.rawQuery(
          "SELECT $db_sales_createdBy, $db_sales_salesId, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, $db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND  $db_sales_createdBy=? AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_salesProductId, $db_salesTrans_price,$db_toppings_toppingId",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT $db_sales_createdBy, $db_sales_salesId, $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,$db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND $db_sales_createdBy IS NOT NULL AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_sales_createdBy,$db_salesTrans_salesProductId, $db_salesTrans_price,$db_toppings_toppingId");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsPaymentTypeSalesByDateToDate(startDate, endDate, id) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    if (id != "All") {
      result = await db.rawQuery(
          "SELECT $db_sales_salesPaymentType, $db_salesTrans_salesProductName, $db_salesTrans_salesProductId, $db_productDetails.$db_product_productKeywordID, $db_salesTrans_price, SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId INNER JOIN $db_productDetails ON $db_salesTransactions.$db_salesTrans_salesProductId=$db_productDetails.$db_product_productId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND  $db_sales_salesPaymentType=? AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND ($db_salesDetails.$db_sales_salesPaymentType IS NOT NULL AND $db_salesDetails.$db_sales_salesPaymentType != ${1})  AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_salesProductId, $db_salesTrans_price, $db_sales_salesPaymentType,$db_productDetails.$db_product_productKeywordID",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT $db_sales_salesPaymentType, $db_salesTrans_salesProductName, $db_salesTrans_salesProductId, $db_productDetails.$db_product_productKeywordID, $db_salesTrans_price, SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId INNER JOIN $db_productDetails ON $db_salesTransactions.$db_salesTrans_salesProductId=$db_productDetails.$db_product_productId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR  $db_salesDetails.$db_sales_isActiveSale IS NULL) AND ($db_salesDetails.$db_sales_salesPaymentType IS NOT NULL AND $db_salesDetails.$db_sales_salesPaymentType != ${1}) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY  $db_salesTrans_salesProductId, $db_salesTrans_price, $db_sales_salesPaymentType,$db_productDetails.$db_product_productKeywordID");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsTableSalesByDateToDate(startDate, endDate, id) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    if (id != "All") {
      result = await db.rawQuery(
          "SELECT $db_sales_fkTableID, $db_sales_salesId, $db_salesTrans_salesProductName, $db_salesTrans_price, SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND  $db_salesDetails.$db_sales_fkTableID = ? AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_salesDetails.$db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesDetails.$db_sales_fkTableID, $db_salesTrans_salesProductId, $db_salesTrans_price",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT $db_sales_fkTableID, $db_salesTrans_fkSalesId, $db_salesTrans_salesProductName, $db_salesTrans_price, SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND $db_salesDetails.$db_sales_fkTableID IS NOT NULL  AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_salesDetails.$db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesDetails.$db_sales_fkTableID, $db_salesTrans_salesProductId, $db_salesTrans_price");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsTaxSalesByDateToDate(startDate, endDate, id) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    result = await db.rawQuery(
        "SELECT $db_salesTransactions.$db_salesTrans_isIGST, $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_salesCGstAmount as DOUBLE)) as $db_salesTrans_salesCGstAmount,SUM(CAST($db_salesTrans_salesSGstAmount as DOUBLE)) as $db_salesTrans_salesSGstAmount, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' AND CAST($db_salesTrans_salesCGstAmount as DOUBLE) > 0  GROUP BY $db_salesTrans_salesProductId, $db_salesTrans_price");

    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsDiscountByDateToDate(startDate, endDate, String type) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    if (type == "Product") {
      result = await db.rawQuery(
          "SELECT $db_sales_salesId, $db_salesTransactions.$db_sales_discountFormula, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, SUM(CAST($db_salesTransactions.$db_sales_discountPrice as DOUBLE)) as $db_sales_discountPrice, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus<2 AND $db_salesTransactions.$db_product_rowStatus<2 AND $db_salesTransactions.$db_sales_discountPrice IS NOT NULL AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' AND CAST($db_salesTrans_salesCGstAmount as DOUBLE) > 0  GROUP BY $db_salesTrans_salesProductId, $db_sales_salesId");
    } else {
      result = await db.rawQuery(
          "SELECT $db_sales_salesId ,$db_sales_discountFormula, $db_sales_invoiceId,SUM(CAST($db_sales_discountPrice as DOUBLE)) as $db_sales_discountPrice, SUM(CAST($db_sales_salesTotalAmount as DOUBLE)) as $db_sales_salesTotalAmount FROM $db_salesDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus<2 AND ($db_sales_isActiveSale != ${1} OR $db_sales_isActiveSale IS NULL) AND $db_sales_discountPrice IS NOT NULL AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate'  GROUP BY $db_sales_salesId");
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsSalesByDateToDate(startDate, endDate, {String? roleId, String paymentId = "All"}) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery;
    if (roleId == "3") {
      if (paymentId == "All") {
        sqlQuery =
            "SELECT $db_salesTrans_fkSalesId, $db_sales_fkTableID, $db_sales_salesPaymentType, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, $db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, $db_salesDetails.$db_sales_discountPrice, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId = $db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_sales_createdBy = '${MyApp.activeUser.username}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_price";
      } else {
        sqlQuery =
            "SELECT $db_salesTrans_fkSalesId, $db_sales_fkTableID, $db_sales_salesPaymentType, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, $db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, $db_salesDetails.$db_sales_discountPrice, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId = $db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_sales_createdBy = '${MyApp.activeUser.username}'AND $db_salesDetails.$db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_price";
      }
    } else {
      if (paymentId == "All") {
        sqlQuery =
            "SELECT $db_salesTrans_fkSalesId, $db_sales_fkTableID, $db_sales_salesPaymentType, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, $db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, $db_salesDetails.$db_sales_discountPrice, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId = $db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_price";
      } else {
        sqlQuery =
            "SELECT $db_salesTrans_fkSalesId, $db_sales_fkTableID, $db_sales_salesPaymentType, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName, $db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity, $db_salesDetails.$db_sales_discountPrice, SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId = $db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_salesDetails.$db_sales_salesPaymentType = '$db_payment_paymentId' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND ($db_salesDetails.$db_sales_isActiveSale != ${1} OR $db_salesDetails.$db_sales_isActiveSale IS NULL) AND $db_sales_salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_price";
      }
    }
    result = await db.rawQuery(sqlQuery);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryRowsPaymentsSalesByDateToDate(startDate, endDate, id) async {
    var startingDayofMonth = '${startDate}';
    var result;
// Find the last day of the month.
    var endingDayofMonth = '${endDate}';

    Database db = await DatabaseHelper.instance.database;
    if (id != 0) {
      result = await db.rawQuery(
          "SELECT $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,$db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus='${0}' OR  $db_product_rowStatus='${1}' AND $db_sales_salesPaymentType=? AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' GROUP BY $db_sales_salesPaymentType",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT $db_salesTrans_salesProductId,$db_salesTrans_salesProductName,$db_salesTrans_price,SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity,SUM(CAST($db_salesTrans_amount as DOUBLE)) as $db_salesTrans_amount FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus='${0}' OR  $db_product_rowStatus='${1}' AND $db_sales_salesDate BETWEEN '${startingDayofMonth}' AND '${endingDayofMonth}' GROUP BY $db_sales_salesPaymentType");
    }
    return result;
  }

  Future<int> updateSalesTable(Sales sales) async {
    String invoiceno = '';
    if (sales.invoiceNo != null) {
      invoiceno = invoiceno + sales.invoiceNo!;
      if (sales.invoiceExtension != null) {
        invoiceno = invoiceno + sales.invoiceExtension!;
      }
    }

    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_sales_salesTotalAmount = ?,$db_sales_salesTaxMode = ?,$db_sales_salesSync = ?,$db_product_rowStatus = ?,$db_sales_customerName = ?, $db_sales_isActiveSale = ?, $db_sales_invoiceId = ?, $db_sales_salesPaymentType = ?, 
    $db_sales_discountPrice = ?, $db_sales_discountFormula = ?, $db_sales_invoiceNo = ?, $db_sales_fK_CustomerID = ?, $db_sales_sales_MobileNo = ?, $db_sales_sales_OrderType = ?, $db_sales_sales_OnlinePlatformID = ?, $db_sales_sales_OnlineRefNo = ?, $db_sales_status = ?,$db_sales_payment_Invoice = ?, $db_sales_invoice_Extension = ?, $db_shift_Id =?, $db_salesCategoryId = ?, $db_Payment_category_ID = ? ,$db_delivery_date_time=?
    WHERE $db_sales_salesId = ?
    ''', [
      sales.totalAmount,
      sales.taxMode,
      sales.sync,
      sales.rowStatus,
      sales.customerName,
      sales.isActiveSale,
      sales.invoiceId,
      sales.paymentType,
      sales.discountPrice,
      sales.discountFormula,
      invoiceno,
      sales.customerID,
      sales.mobileNo,
      sales.orderType,
      sales.onlinePlatformID,
      sales.onlineRefNo,
      sales.salesStatus,
      sales.paymentInvoice,
      sales.invoiceExtension,
      sales.shiftId,
      sales.salesCategoryId,
      sales.paymentCategoryID,
      sales.deliveryDateAndTime,
      sales.salesId
    ]);
    return result;
  }

  Future<int> updateSalesActiveTable(Sales sales) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_sales_salesDate = ?,$db_sales_salesTotalAmount = ?,$db_sales_salesTaxMode = ?,$db_sales_salesSync = ?,$db_product_rowStatus = ?,$db_sales_customerName = ?, $db_sales_isActiveSale = ?, $db_sales_invoiceId = ?, $db_sales_salesPaymentType = ?, $db_sales_discountPrice = ?, $db_sales_discountFormula = ?, $db_sales_fK_CustomerID = ?, $db_sales_sales_MobileNo = ?, $db_sales_status = ? , $db_sales_fkTableID = ?, $db_salesCategoryId = ?, $db_Payment_category_ID = ?
    WHERE $db_sales_salesId = ?
    ''', [
      sales.date,
      sales.totalAmount,
      sales.taxMode,
      sales.sync,
      sales.rowStatus,
      sales.customerName,
      sales.isActiveSale,
      sales.invoiceId,
      sales.paymentType,
      sales.discountPrice,
      sales.discountFormula,
      sales.customerID,
      sales.mobileNo,
      sales.salesStatus,
      sales.fkTableID,
      sales.salesCategoryId,
      sales.paymentCategoryID,
      sales.salesId,
    ]);
    return result;
  }

  Future<int> updateSalesSync(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_sales_salesSync = ${1} 
    WHERE $db_sales_salesId = ?
    ''', [id]);
    return result;
  }

  Future<int> updateSalesDelete(String? id, {Sales? sales}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_product_rowStatus = ${2},
    $db_sales_salesSync = ${0},
    $db_deleted_by = ${sales?.deletedBy},
    $db_deleted_date = '${sales?.deletedDate}',
    $db_deleted_reason = '${sales?.deletedReason}',
    $db_deleted_user_name = '${sales?.deletedUserName}'
    WHERE $db_sales_salesId = ?
    ''', [id]);
    return result;
  }

  Future<int> revertSalesDeleted(String? id, {Sales? sales}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_product_rowStatus = ${1},
    $db_sales_salesSync = ${0},
    $db_deleted_by = ${sales?.deletedBy},
    $db_deleted_date = '${sales?.deletedDate}',
    $db_deleted_reason = '${sales?.deletedReason}',
    $db_deleted_user_name = '${sales?.deletedUserName}'
    WHERE $db_sales_salesId = ?
    ''', [id]);
    return result;
  }

  Future<int> deleteSales(String saleID) async {
    await deleteReportsBasedSalesId(saleID);
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_product_rowStatus = ?,$db_sales_salesSync = ? WHERE $db_sales_salesId = ?
    ''', [2, 0, saleID]);
    return result;
    // return await db
    //     .delete(SalesDetails, where: '$salesId = ?', whereArgs: [saleID]);
  }

  Future<int> deleteReportsBasedSalesId(id) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_salesTransactions, where: '$db_salesTrans_fkSalesId = ?', whereArgs: [id]);
  }

  //DELETE ALL RECORDS BASED ON WORKSPACE ID
  Future<int> deleteAllSalesAndTransactionBasedOnWorkspace(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.delete(db_salesTransactions, where: '$db_workspace_workspaceId = ?', whereArgs: [id]);
    result = await db.delete(db_salesDetails, where: '$db_workspace_workspaceId = ?', whereArgs: [id]);
    return result;
  }

  Future<int> deleteAllBefOneMonthRecords(workspaceID) async {
    var date = DateTime.now().subtract(Duration(days: 1));
    var date1 = date.toString();
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawQuery(
        'SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId = $workspaceID AND $db_sales_salesSync = 0 AND CAST($db_sales_salesDate AS DATE) <= ?',
        [date1]);
    result = await db.rawQuery(
        'SELECT * FROM $db_salesTransactions WHERE $db_workspace_workspaceId = $workspaceID AND $db_salesTrans_salesTransactionSync = 0 AND $db_salesTrans_fkSalesId IN (SELECT $db_sales_salesId FROM $db_salesDetails WHERE $db_workspace_workspaceId = $workspaceID AND $db_sales_salesSync = 0 AND CAST($db_sales_salesDate AS DATE) <= ?)',
        [date1]);
    // result = await db
    //     .delete(db_salesTransactions, where: '$db_workspace_workspaceId = ?', whereArgs: [id]);
    // result = await db
    //     .delete(SalesDetails, where: '$db_workspace_workspaceId = ?', whereArgs: [id]);
    return 1;
  }

  Future<int> deleteAllBefOneDayRecords(workspaceID, days) async {
    String startDate = DateFormat("yyyy-MM-dd").format(DateTime.now());
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawQuery(
      'SELECT * FROM $db_salesDetails WHERE $db_workspace_workspaceId = $workspaceID AND $db_sales_salesSync = 0 AND date($db_sales_salesDate) < date("$startDate")',
    );
    // result = await db.rawQuery(
    //   'SELECT $salesId FROM $db_salesDetails WHERE $db_workspace_workspaceId = $workspaceID AND $salesSync = 0 AND date($salesDate) < date("${startingDayofMonth}")',
    // );
    result = await db.rawQuery(
      'SELECT * FROM $db_salesTransactions WHERE $db_workspace_workspaceId = $workspaceID AND $db_salesTrans_salesTransactionSync = 0 AND $db_salesTrans_fkSalesId IN (SELECT $db_sales_salesId FROM $db_salesDetails WHERE $db_workspace_workspaceId = $workspaceID AND $db_sales_salesSync = 0 AND date($db_sales_salesDate) < date("$startDate"))',
    );
    return 1;
  }

  Future<int> updateCustomerNameInSalesDetails(String customerId, String customerName) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesDetails 
    SET $db_sales_customerName = ?, $db_sales_salesSync = ${0}, $db_product_rowStatus = ${1} 
    WHERE $db_sales_fK_CustomerID = ?
    ''', [customerName, customerId]);
    return result;
  }
}
