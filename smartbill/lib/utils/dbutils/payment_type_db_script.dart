import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/payment_type.dart';
import 'database_helper.dart';

class PaymentTypeDBScript {
  Future<int> insertPaymentType(PaymentType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_paymentTypes, {
      db_paymentTypes_paymentTypeId: model.paymentTypeID,
      db_paymentTypes_paymentTypeName: model.paymentName,
      db_paymentTypes_createdDate: model.createdDate,
      db_product_rowStatus: model.rowStatus,
      db_paymentTypes_paymentTypeSync: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID,
      db_Payment_category_ID: model.paymentCategoryID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetPaymentTypeID() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_paymentTypes_paymentTypeId FROM $db_paymentTypes WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncPaymentTypeData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_paymentTypes WHERE $db_paymentTypes_paymentTypeSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllPaymentType() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_paymentTypes WHERE $db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus < 2 ORDER BY $db_paymentTypes_paymentTypeAutoId");
    return result;
  }

  Future<int> updatePaymentType(PaymentType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_paymentTypes 
    SET $db_paymentTypes_paymentTypeName = ?, $db_product_rowStatus = ?, $db_paymentTypes_paymentTypeSync = ? ,$db_Payment_category_ID = ? WHERE $db_workspace_workspaceId = ? AND $db_paymentTypes_paymentTypeId = ?
    ''', [model.paymentName, model.rowStatus, model.syncStatus, model.paymentCategoryID, model.workspaceID, model.paymentTypeID]);
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllPaymentCategory() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery("SELECT * FROM $db_payment_category");
    return result;
  }
}
