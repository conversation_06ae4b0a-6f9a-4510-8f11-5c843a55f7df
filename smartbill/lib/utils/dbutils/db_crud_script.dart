import 'package:smartbill/main.dart';
import 'package:smartbill/utils/error_logs/error_logs_function.dart';
import 'package:sqflite/sqflite.dart';
import 'database_helper.dart';

class DBFuctionsScript {
  ///Common insert function
  Future<int> dbInsert(String tName, dynamic values) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(tName, values);
  }

  ///common update function
  Future<int?> updateModel(String tableName, Map<String, dynamic> updateFields, String idField, dynamic idValue) async {
    Database db = await DatabaseHelper.instance.database;

    // Build the SET clause dynamically based on the updateFields map
    String setClause = updateFields.keys.map((field) => '$field = ?').join(', ');

    ///create empty list for add updateFields value
    List<dynamic> setValues = [];

    ///Add updateFields value in setValues list
    setValues.addAll(updateFields.values);

    // Prepare the values for the update
    List<dynamic> values = [idValue];

    ///To add setValues list in values list at index of 0 position
    values.insertAll(0, setValues);

    try {
      // Build and execute the update query
      var result = await db.rawUpdate('''
    UPDATE $tableName
    SET $setClause
    WHERE $idField = ?
    ''', values);

      return result;
    } catch (e) {
      print(e);
    }
  }

  /// Delete function column based
  Future<int> deleteFunction(String tName, String cName, String value) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(tName, where: '$cName = ?', whereArgs: [value]);
  }

  /// Delete complete table
  Future<int> deleteTable(String tName) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.delete(tName);
    return result;
  }

  Future<int> commonUpdateForSyncAndStatus(String tableName, String cSync, int cSyncValue, String cStatus, int cStatusValue, String id, String idValue,
      {String? cWorkSpaceId}) async {
    var result;
    Database db = await DatabaseHelper.instance.database;
    try {
      // Build and execute the update query
      result = await db.rawUpdate('''
    UPDATE $tableName
    SET $cSync = ?, $cStatus = ?
    WHERE $id = ? AND $cWorkSpaceId = ?
    ''', [cSyncValue, cStatusValue, idValue,MyApp.activeWorkspace.workspaceId]);

      return result;
    } catch (e) {
      ErrorLogsFunction.insertErrorLogs(e, "commonUpdateForSyncAndStatus - DBFuctionsScript");
    }
    return result;
  }
}
