import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../main.dart';
import '../../model/shop_details.dart';

class ShopDetailsDBScript {
  //SHOP SETTINGS DETAILS INSERT
  Future<int> insertShopDetails(ShopDeatails shopDetails) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(db_shopDetails, {
      db_shop_shopName: shopDetails.name,
      db_shop_shopAddress1: shopDetails.address1,
      db_shop_shopAddress2: shopDetails.address2,
      db_shop_shopAddress3: shopDetails.address3,
      db_shop_shopGstNo: shopDetails.gstNo,
      db_shop_shopFooterText: shopDetails.footerText,
      db_shop_shopFooterText2: shopDetails.footerText2,
      db_workspace_workspaceId: shopDetails.workspaceId,
      db_shop_shopSync: shopDetails.shopSync,
      db_product_rowStatus: 0,
      db_shop_shopImageID: shopDetails.imageID
    });
  }

  Future<List<Map<String, dynamic>>> queryallRowsShopDetails() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_shopDetails,
        where: '$db_workspace_workspaceId=?',
        whereArgs: [MyApp.activeWorkspace.workspaceId]);
  }

  Future<int?> queryShopDetailExists() async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT($db_shop_shopId) FROM $db_shopDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'"));
    return result;
  }

  Future<Map<String, dynamic>?> queryAllNonSyncShopDetails() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.query(db_shopDetails,
        where: '$db_workspace_workspaceId=? AND $db_shop_shopSync=${0}',
        whereArgs: [MyApp.activeWorkspace.workspaceId]);
    if (result.isEmpty) {
      return null;
    }
    return result.first;
  }

  ///Update shop details
  Future<int> updateShopDetails(ShopDeatails shopDetails) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_shopDetails 
    SET $db_shop_shopName = ? ,$db_shop_shopAddress1 = ? ,$db_shop_shopAddress2 = ?,$db_shop_shopAddress3 = ?, $db_shop_shopGstNo = ? ,$db_shop_shopFooterText = ?, $db_shop_shopFooterText2 = ?, $db_product_rowStatus = ?,$db_shop_shopSync = ?, $db_shop_shopImageID = ?
    WHERE $db_workspace_workspaceId = ?
    ''', [
      shopDetails.name,
      shopDetails.address1,
      shopDetails.address2,
      shopDetails.address3,
      shopDetails.gstNo,
      shopDetails.footerText,
      shopDetails.footerText2,
      shopDetails.status,
      shopDetails.shopSync,
      shopDetails.imageID,
      MyApp.activeWorkspace.workspaceId
    ]);
    return result;
  }
}
