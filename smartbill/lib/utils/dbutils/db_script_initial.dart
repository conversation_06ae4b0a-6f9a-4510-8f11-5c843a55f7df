import 'db_strings.dart';

final initialScript = <String>[
  '''
          CREATE TABLE $db_salesDetails (
            $db_sales_salesDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_sales_salesId TEXT NOT NULL,
            $db_sales_invoiceId TEXT NOT NULL,
            $db_sales_salesDate TEXT NOT NULL,
            $db_sales_salesTotalAmount TEXT NOT NULL,
            $db_sales_salesPaymentType INTEGER NOT NULL,
            $db_sales_salesSync INTEGER NOT NULL,
            $db_sales_salesTaxMode INTEGER NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_salesTransactions (
            $db_salesTrans_salesTransactionsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesTrans_transactId TEXT NOT NULL,
            $db_salesTrans_fkSalesId INTEGER NOT NULL,
            $db_salesTrans_salesProductId INTEGER NOT NULL,
            $db_salesTrans_salesProductName TEXT NOT NULL,
            $db_salesTrans_quantity INTEGER NOT NULL,
            $db_salesTrans_price TEXT NOT NULL,
            $db_salesTrans_actualPrice TEXT NULL,
            $db_salesTrans_salesCGst TEXT NULL,
            $db_salesTrans_salesCGstAmount TEXT NULL,
            $db_salesTrans_salesSGst TEXT NULL,
            $db_salesTrans_salesSGstAmount TEXT NULL,
            $db_salesTrans_amount TEXT NOT NULL,
            $db_salesTrans_amountWithoutGst TEXT NULL,
            $db_salesTrans_salesTransactionSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_sales_holdSalesDetails (
            $db_sales_salesDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_sales_salesId TEXT NOT NULL,
            $db_sales_invoiceId TEXT NOT NULL,
            $db_sales_holdSalesName TEXT NULL,
            $db_sales_salesDate TEXT NOT NULL,
            $db_sales_salesTotalAmount TEXT NOT NULL,
            $db_sales_salesPaymentType INTEGER NOT NULL,
            $db_sales_salesSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_salesTrans_holdSalesTransactions (
            $db_salesTrans_salesTransactionsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesTrans_transactId TEXT NOT NULL,
            $db_salesTrans_fkSalesId INTEGER NOT NULL,
            $db_salesTrans_salesProductId INTEGER NOT NULL,
            $db_salesTrans_salesProductName TEXT NOT NULL,
            $db_salesTrans_quantity INTEGER NOT NULL,
            $db_salesTrans_price TEXT NOT NULL,
            $db_salesTrans_actualPrice TEXT NULL,
            $db_salesTrans_salesCGst TEXT NULL,
            $db_salesTrans_salesCGstAmount TEXT NULL,
            $db_salesTrans_salesSGst TEXT NULL,
            $db_salesTrans_salesSGstAmount TEXT NULL,
            $db_salesTrans_amount TEXT NOT NULL,
            $db_salesTrans_amountWithoutGst TEXT NULL,
            $db_salesTrans_salesTransactionSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_shopDetails (
            $db_shop_shopId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_shop_shopName TEXT NOT NULL,
            $db_shop_shopAddress1 TEXT NULL,
            $db_shop_shopAddress2 TEXT NULL,
            $db_shop_shopFooterText TEXT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_shop_shopSync INTEGER NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_productDetails (
            $db_product_productDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_product_productId TEXT NOT NULL,
            $db_product_productSaleId INT NOT NULL,
            $db_product_productName TEXT NOT NULL,
            $db_product_productPrice TEXT NULL,
            $db_product_productMrp TEXT NULL,
            $db_product_productCgst TEXT NULL,
            $db_product_productSgst TEXT NULL,
            $db_product_productImage TEXT NULL,
            $db_product_productSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_product_productDate TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_paymentDetails (
            $db_payment_paymentDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_payment_paymentId INTEGER NOT NULL,
            $db_payment_paymentName TEXT NOT NULL,
            $db_payment_paymentNetworkImage TEXT NULL,
            $db_payment_paymentImage TEXT NULL,
            $db_payment_paymentIsActive INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_workspace (
            $db_workspace_workspaceTableId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_workspace_workspaceName TEXT NOT NULL,
            $db_workspace_userName TEXT NOT NULL,
            $db_workspace_isActive  INTEGER NOT NULL,
            $db_workspace_expiryDate TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_workSpaceSettings (
            $db_workSpacesSettings_settingId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_workspace_userName TEXT NOT NULL,
            $db_workSpacesSettings_settingKey TEXT NOT NULL,
            $db_workSpacesSettings_settingValue TEXT NOT NULL,
            $db_workSpacesSettings_settingSync INT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_workSpacesSettings_deviceSettings (
            $db_workSpacesSettings_settingId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workSpacesSettings_settingKey TEXT NOT NULL,
            $db_workSpacesSettings_settingValue TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_sync (
            $db_sync_syncId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_workspace_userName TEXT NOT NULL,
            $db_sync_commitId TEXT NOT NULL,
            $db_sync_commitDate TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_invoicePrefix (
            $db_invoicePrefix_prefixId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_workspace_userName TEXT NOT NULL,
            $db_invoicePrefix_prefixName TEXT NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_userPermissions(
            $db_userPermissions_permissionId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_userName TEXT NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_userPermissions_permissionName TEXT NOT NULL,
            $db_userPermissions_permissionValue INT NOT NULL
          )
          '''
];
