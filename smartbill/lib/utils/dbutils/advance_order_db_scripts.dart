import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/advance_order_note_model.dart';
import 'db_strings.dart';

class AdvanceOrdersDBScripts {
  Future<int> insertOrderNote(AdvanceOrderNoteModel? noteModel) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_order_notes, {
      db_workspace_workspaceId: noteModel?.workspaceId,
      db_sales_salesId: noteModel?.salesId,
      db_note: noteModel?.note,
      db_createdDate: noteModel?.createdDate,
      db_updated_date: noteModel?.updatedDate,
      db_status: noteModel?.status,
      db_note_sync: noteModel?.sync
    });
      return result;
  }

  Future<int> updateOrderNote(AdvanceOrderNoteModel? noteModel) async {
    Database db = await DatabaseHelper.instance.database;
    int result = await db.update(
      db_order_notes,
      {
        db_workspace_workspaceId: noteModel?.workspaceId,
        db_sales_salesId: noteModel?.salesId,
        db_note: noteModel?.note,
        db_createdDate: noteModel?.createdDate,
        db_updated_date: noteModel?.updatedDate,
        db_status: noteModel?.status,
        db_note_sync: noteModel?.sync
      },
      where: '$db_sales_salesId = ?',
      whereArgs: [noteModel?.salesId],
    );
    return result;
  }

  Future<List<Map<String, dynamic>>> getAdvanceOrderNoteById() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_order_notes WHERE  $db_workspace_workspaceId = ?', [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int> updateNotesStatues(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_order_notes 
    SET $db_status = ${2} 
    WHERE $db_sales_salesId = ?
    ''', [id]);
    return result;
  }

  Future<List<AdvanceOrderNoteModel>> getOrderNotesFromLocal() async {
    var allRows = await getAdvanceOrderNoteById();
    return allRows.map((row) => AdvanceOrderNoteModel.fromJson(row)).toList();
  }
}
