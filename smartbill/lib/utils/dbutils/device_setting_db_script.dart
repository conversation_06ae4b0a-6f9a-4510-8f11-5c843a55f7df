import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../model/workspace_settings.dart';

class DeviceSettingsDBScript{

    Future<int> insertDeviceSettingDetails(WorkspaceSettings setting) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_workSpacesSettings_deviceSettings, {
      db_workSpacesSettings_settingKey: setting.settingKey,
      db_workSpacesSettings_settingValue: setting.settingValue,
    });
    return result;
  }

   Future<List<Map<String, dynamic>>> queryAllGetDeviceSetting() async {
    Database db = await DatabaseHelper.instance.database;
    var result =
        db.rawQuery("SELECT * FROM $db_workSpacesSettings_deviceSettings");
    // var result =
    //     db.rawQuery('SELECT * FROM $db_workspace WHERE $db_workspace_workspaceId=${id}');
    return result;
  }

  Future<int?> queryDeviceWorkspaceCount() async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($db_workSpacesSettings_settingKey) FROM $db_workSpacesSettings_deviceSettings WHERE $db_workSpacesSettings_settingKey=?',
        ["PrinterFormat"]));
    return result;
  }

  Future<int?> queryCustomerSettingWorkspaceCount() async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($db_workSpacesSettings_settingKey) FROM $db_workSpacesSettings_deviceSettings WHERE $db_workSpacesSettings_settingKey=?',
        ["PanelCustomerName"]));
    return result;
  }

  Future<int?> queryMultiPrintSettingWorkspaceCount() async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($db_workSpacesSettings_settingKey) FROM $db_workSpacesSettings_deviceSettings WHERE $db_workSpacesSettings_settingKey=?',
        ["MultiPrint"]));
    return result;
  }

   Future<int> updateDeviceSettings(WorkspaceSettings settingsDetail) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_workSpacesSettings_deviceSettings 
    SET $db_workSpacesSettings_settingValue = ?
    WHERE $db_workSpacesSettings_settingKey=?
    ''', [
      settingsDetail.settingValue,
      settingsDetail.settingKey,
    ]);
    return result;
  }

    Future<int> checkDeviceKeyExists(String keyName) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT(1) FROM $db_workSpacesSettings_deviceSettings WHERE $db_workSpacesSettings_settingKey = '$keyName'"));
    return result;
  }
}