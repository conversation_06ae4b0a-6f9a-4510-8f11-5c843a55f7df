
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/discounts.dart';
import 'database_helper.dart';

class DiscountDB {
  Future<int> insertDiscount(Discounts model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_discount, {
      db_discount_discountID: model.discountID,
      db_discount_couponName: model.couponName,
      db_discount_mode: model.mode,
      db_discount_formula: model.formula,
      db_discount_categoryID: model.categoryID,
      db_discount_createdDate: model.createdDate,
      db_discount_createdBy: model.createdBy,
      db_discount_discountActive: model.discountActive,
      db_discount_discountSync: model.discountSync,
      db_product_rowStatus: model.rowStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<int> updateDiscount(Discounts model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_discount} 
    SET ${db_discount_couponName} = ?, ${db_discount_mode} = ?, ${db_discount_formula} = ?, ${db_discount_categoryID} = ?, ${db_discount_discountActive} = ?, ${db_discount_discountSync} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_discount_discountID} = ?
    ''', [
      model.couponName,
      model.mode,
      model.formula,
      model.categoryID,
      model.discountActive,
      model.discountSync,
      model.rowStatus,
      model.workspaceID,
      model.discountID
    ]);
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllDiscount() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_discount} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_discount_ID}");
    return result;
  }

  Future<List<Map<String, dynamic>>> selectCategoryDiscount(
      String categoryID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_discount} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_product_rowStatus} < 2 AND ${db_discount_categoryID} LIKE '$categoryID%' ORDER BY ${db_discount_ID}");
    return result;
  }
}
