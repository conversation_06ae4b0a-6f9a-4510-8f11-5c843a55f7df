import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../model/invoice_prefix_model.dart';
import 'database_helper.dart';

class InvoicePrefixDBScript {
  Future<int> insertInvoicePrefix(Prefix model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_invoicePrefix, {
      db_workspace_workspaceId: model.workspaceId,
      db_workspace_userName: model.userName,
      db_invoicePrefix_prefixName: model.prefix,
    });
    return result;
  }

  queryAllGetInvoicePrefix(workspaceid, username) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_invoicePrefix_prefixName FROM $db_invoicePrefix WHERE $db_workspace_workspaceId='${workspaceid}' AND $db_workspace_userName='${username}'");
    if (result.length > 0) {
      String prefixName = result.first.values.first.toString();
      return prefixName;
    }
  }
}
