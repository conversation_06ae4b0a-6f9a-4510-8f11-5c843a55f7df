import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/workspace_settings.dart';
import 'database_helper.dart';

class WorkSpaceSettingsDB {

  Future<int> insertSettingDetails(WorkspaceSettings setting, {String table = db_workSpaceSettings}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.insert('$table', {
      db_workspace_workspaceId: setting.workspaceId,
      db_workspace_userName: setting.userName,
      db_workSpacesSettings_settingKey: setting.settingKey,
      db_workSpacesSettings_settingValue: setting.settingValue,
      db_workSpacesSettings_settingSync: setting.sync,
    });
    return result;
  }

  Future<int> updateSettings(WorkspaceSettings settingsDetail, {String table = db_workSpaceSettings}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $table 
    SET $db_workSpacesSettings_settingValue = ?,$db_workSpacesSettings_settingSync=?
    WHERE $db_workspace_workspaceId = ? AND $db_workSpacesSettings_settingKey=?
    ''', [
      settingsDetail.settingValue,
      settingsDetail.sync,
      settingsDetail.workspaceId,
      settingsDetail.settingKey,
    ]);

    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetSetting() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery("SELECT * FROM $db_workSpaceSettings WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  ///Query All get user settings
  Future<List<Map<String, dynamic>>> queryAllGetUserSetting() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_workSpacesSettings_workSpaceUserSettings WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<int> checkWorkspaceKeyExists(String keyName, {String table = db_workSpaceSettings}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT(1) FROM $table WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_workSpacesSettings_settingKey = '$keyName'"));
    return result;
  }
}
