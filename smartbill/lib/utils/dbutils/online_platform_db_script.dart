// ignore_for_file: unnecessary_brace_in_string_interps

import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';

class OnlinePlatformDBScript {
  Future<int> insertOnlinePlatform(OnlinePlatformModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_salesPaymentMapping_onlinePlatform, {
      db_salesPaymentMapping_onlinePlatformId: model.onlinePlatformId,
      db_salesPaymentMapping_onlinePlatformName: model.onlinePlatformName,
      db_common_createdDate: model.createdDate,
      db_product_rowStatus: model.rowStatus,
      db_common_syncStatus: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllOnlinePlatform() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_salesPaymentMapping_onlinePlatform} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_salesPaymentMapping_onlinePlatformAutoId}");
    return result;
  }

  Future<int> updateOnlinePlatform(OnlinePlatformModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesPaymentMapping_onlinePlatform} 
    SET ${db_salesPaymentMapping_onlinePlatformName} = ?, ${db_common_createdDate} = ?, ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesPaymentMapping_onlinePlatformId} = ?
    ''', [
      model.onlinePlatformName,
      model.createdDate,
      model.rowStatus,
      model.syncStatus,
      model.workspaceID,
      model.onlinePlatformId
    ]);
    return result;
  }

  Future<int> deleteWithUpdateArea(OnlinePlatformModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesPaymentMapping_onlinePlatform} 
    SET ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ? WHERE ${db_workspace_workspaceId}= ? AND ${db_salesPaymentMapping_onlinePlatformId} = ?
    ''', [
      model.rowStatus,
      model.syncStatus,
      model.workspaceID,
      model.onlinePlatformId
    ]);
    return result;
  }

  // Future<List<Map<String, dynamic>>> getAllAreasID() async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = db.rawQuery(
  //       "SELECT ${DatabaseHelper.Area_AreaID} FROM ${DatabaseHelper.AREAS} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}'");
  //   return result;
  // }

  // Future<Map<String, dynamic>> getAreaNameByID(id) async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = await db.rawQuery(
  //       "SELECT * FROM ${DatabaseHelper.AREAS} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}' AND ${DatabaseHelper.Area_AreaID}='${id}'");
  //   if (result.isEmpty) {
  //     return null;
  //   }
  //   return result.first;
  // }

  // Future<bool> checkAreaExists(String areaID) async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = Sqflite.firstIntValue(await db.rawQuery(
  //       "SELECT COUNT(${DatabaseHelper.Customer_FK_AreaID}) FROM ${DatabaseHelper.CUSTOMERS} WHERE ${yyy} = '${MyApp.activeWorkspace.workspaceId}' AND ${DatabaseHelper.Customer_FK_AreaID} = '$areaID'  AND ${db_product_rowStatus} < 2"));
  //   if (result != 0) {
  //     return false;
  //   } else {
  //     return true;
  //   }
  // }
}
