import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../model/general_settings.dart';

class GeneralSettingDBScript{
  
    Future<int> insertGeneralSettings(GeneralSetting model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_generalSettings, {
      db_generalSettingName: model.settingKey,
      db_generalSettingValue: model.settingValue,
    });
    return result;
  }

    Future<List<Map<String, dynamic>>> getAllGeneralSettings(String key) async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_generalSettingValue FROM $db_generalSettings WHERE $db_generalSettingName = ?",
        [key]);
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllGeneralSettingName() async {
    Database db = await DatabaseHelper.instance.database;
    var result =
        db.rawQuery("SELECT $db_generalSettingName FROM $db_generalSettings");
    return result;
  }
  
    Future<int> updateGeneralSettings(GeneralSetting model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_generalSettings 
    SET $db_generalSettingValue = ?
    WHERE $db_generalSettingName = ?
    ''', [
      model.settingValue,
      model.settingKey,
    ]);
    return result;
  }
}