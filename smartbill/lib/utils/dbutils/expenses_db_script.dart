import '../../screens/expenses/model/expense_category_model.dart';
import 'package:smartbill/screens/expenses/model/expenses_product_model.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../main.dart';
import 'database_helper.dart';
import 'db_crud_script.dart';

class ExpensesDBScript {
  FnUtilities fnUtilities = FnUtilities();
  DBFuctionsScript dbScript = DBFuctionsScript();

  /// Get expenses category list
  Future<List<Map<String, dynamic>>> queryallRowsExpensesCategoryList() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_expensesCategory WHERE $db_expensesCategory_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_expensesCategory_expCategoryName != '' AND $db_status < 2 ORDER BY $db_expensesCategory_expCatID");
    return result;
  }

  ///Delete category list key
  Future<int> updateProductsKeyDelete(ExpensesCategoryModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_expensesCategory 
    SET $db_expensesCategory_expCategorySync = ? ,$db_expensesCategory_workspaceID=? ,$db_status=?
    WHERE $db_expensesCategory_expCategoryID = ?
    ''', [model.expCategorySync, model.workspaceID, model.status, model.expCategoryID]);
    return result;
  }

  /// Get all expenses from db
  Future<List<Map<String, dynamic>>> queryAllExpenses() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_expensesProduct.*,$db_expensesCategory.$db_expenseCategoryName FROM $db_expensesProduct LEFT JOIN $db_expensesCategory ON $db_expensesProduct.$db_expCategoryID=$db_expensesCategory.$db_expensesCategory_expCategoryID WHERE $db_expensesProduct.$db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_expensesProduct.$db_status < 2");
    return result;
  }

  Future<int> updateExpenseProdDelete(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_expenseTransactions 
    SET $db_status = ${2},
    $db_expTransSync = ${0}
    WHERE $db_expProductID = ?
    ''', [id]);
    return result;
  }

  ///Update with delete expense item
  Future<int> deleteUpdateExpenseItemTable(ExpensesProduct expItem) async {
    var id = expItem.toJson()['$db_expProductID'];
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_expensesProduct 
    SET $db_status = '${2}',
    $db_expProductSync = '${0}'
    WHERE $db_expProductID = '${id}'
    ''');
    await fnUtilities.getAllExpenses();
    return result;
  }

  Future<String> queryGetMaxInvoiceId(prefixName, bool resetSalesNo) async {
    Database db = await DatabaseHelper.instance.database;
    var count;
    if (resetSalesNo) {
      count = Sqflite.firstIntValue(await db.rawQuery(
          "SELECT COUNT($db_expID) FROM $db_expenses WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND date($db_createdDate) = date('${DateTime.now().toString()}') AND $db_invoiceId LIKE '$prefixName%'"));
    } else {
      count = Sqflite.firstIntValue(
          await db.rawQuery("SELECT COUNT($db_expID) FROM $db_expenses WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_invoiceId LIKE '$prefixName%'"));
    }
    if (count == 0) {
      return "0";
    } else {
      var result = await db.rawQuery(
          "SELECT $db_invoiceId FROM $db_expenses WHERE $db_workspaceID='${MyApp.activeWorkspace.workspaceId}' AND $db_invoiceId LIKE '$prefixName%' ORDER BY $id");
      return result.last["invoiceID"].toString();
    }
  }

  upsetExpenseCategory(ExpensesCategoryModel expCategoriesModel, String operations) async {
    var expCategoriesMap;
    if (operations == "insert") {
      expCategoriesMap = {
        db_expensesCategory_expCategoryID: expCategoriesModel.expCategoryID,
        db_expensesCategory_expCategoryName: expCategoriesModel.expCategoryName,
        db_createdDate: expCategoriesModel.createdDate,
        db_expensesCategory_workspaceID: expCategoriesModel.workspaceID,
        db_status: expCategoriesModel.status,
        db_expensesCategory_expCategorySync: 1,
      };
      await dbScript.dbInsert(db_expensesCategory, expCategoriesMap);
    } else {
      expCategoriesMap = {
        db_expensesCategory_expCategoryName: expCategoriesModel.expCategoryName,
        db_createdDate: expCategoriesModel.createdDate,
        db_expensesCategory_workspaceID: expCategoriesModel.workspaceID,
        db_status: expCategoriesModel.status,
        db_expensesCategory_expCategorySync: 1,
      };
      await dbScript.updateModel(db_expensesCategory, expCategoriesMap, db_expensesCategory_expCategoryID, expCategoriesModel.expCategoryID);
    }
  }

  upsetExpenseItem(ExpensesProduct expensesItem, String operations) async {
    var expenseItemMap;
    if (operations == "insert") {
      expenseItemMap = {
        db_expProductID: expensesItem.expProductID,
        db_expProductName: expensesItem.expProductName,
        db_expenseCategoryName: expensesItem.expCategoryName,
        db_createdDate: expensesItem.createdDate,
        db_workspaceID: expensesItem.workspaceID,
        db_expCategoryID: expensesItem.expCategoryID,
        db_expProductSync: expensesItem.sync,
        db_expProductPrice: expensesItem.expProductPrice,
        db_status: expensesItem.status,
      };
      await dbScript.dbInsert(db_expensesProduct, expenseItemMap);
    } else {
      expenseItemMap = {
        db_expProductName: expensesItem.expProductName,
        db_expenseCategoryName: expensesItem.expCategoryName,
        db_createdDate: expensesItem.createdDate,
        db_workspaceID: expensesItem.workspaceID,
        db_expCategoryID: expensesItem.expCategoryID,
        db_expProductSync: expensesItem.sync,
        db_expProductPrice: expensesItem.expProductPrice,
        db_status: expensesItem.status,
      };
      await dbScript.updateModel(db_expensesProduct, expenseItemMap, db_expProductID, expensesItem.expProductID);
    }
  }
}
