import 'package:smartbill/model/hive_model/master_table_model.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';

class MasterTableDBScript {

    ///Insert the master table data in the database
    Future<int> insertMasterTable(MasterTableModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_master_table, {
      db_key: model.key,
      db_value: model.value,
      db_workspaceID: model.workspaceID,
      db_master_table_sync: model.masterTableSync,
    });
    return result;
  }

  ///Update the master data based on key and workspace id
   Future<int> updateMasterTable(MasterTableModel model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_master_table} 
    SET ${db_value} = ?, ${db_master_table_sync} = ? WHERE  ${db_key} = ? AND ${db_workspace_workspaceId} = ? 
    ''', [
      model.value,
      model.masterTableSync,
      model.key,
      model.workspaceID
    ]);
    return result;
  }

  ///Get master table data based on workspace id
  Future<List<Map<String, dynamic>>> getAllMasterTableBasedOnWorkspaceId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_master_table} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}'");
    return result;
  }

  ///Get master table data based on workspace id
  Future<String?> getValueByKeyAndWorkspaceId(String key) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
      '''SELECT $db_value FROM ${db_master_table} WHERE  ${db_key} = ? AND ${db_workspace_workspaceId} = ?''',[key,MyApp.activeWorkspace.workspaceId]);
    if(result.isEmpty){
      return "[]";
    }
    return result.first['value'].toString();
  }


}