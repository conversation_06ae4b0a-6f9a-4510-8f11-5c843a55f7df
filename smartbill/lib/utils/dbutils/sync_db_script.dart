import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/manage_sync.dart';
import 'database_helper.dart';

class SyncDBScript{
  
    Future<int> insertSyncDetails(ManageSync model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_sync, {
      db_workspace_workspaceId: model.workspaceId,
      db_workspace_userName: model.userName,
      db_sync_commitId: model.commitId,
      db_sync_commitDate: model.commitDate,
    });
    return result;
  }

    Future<String> queryGetLastCommitId() async {
    Database db = await DatabaseHelper.instance.database;
    var count =
        await db.rawQuery("SELECT COUNT($db_sync_commitId) FROM $db_sync");
    var result;
    if (count != 0) {
      result = await db.rawQuery(
          "SELECT $db_sync_commitId FROM $db_sync WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' ORDER BY $db_sync_commitDate DESC LIMIT 1");
    }
    return result.last["commitId"];
  }

  Future<int?> getGetLastSyncId() async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(
        await db.rawQuery("SELECT COUNT($db_sync_commitId) FROM $db_sync"));
    var result;
    if (count != 0) {
      result = await db.rawQuery(
          "SELECT $db_sync_syncId FROM$db_sync WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' ORDER BY $db_sync_commitDate DESC LIMIT 1");
    }
    return result.last["syncId"];
  }

  Future<int?> queryGetCommitIdCount() async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT($db_sync_commitId) FROM $db_sync WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'"));
    return count;
  }

    Future<int> updateSyncDetails(ManageSync model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_sync 
    SET $db_sync_commitId = ?,$db_sync_commitDate = ?
    WHERE $db_workspace_workspaceId = ?
    ''', [model.commitId, model.commitDate, model.workspaceId]);
    return result;
  }

    //DELETE ALL RECORDS FROM SYNC TABLE EXCEPT LAST RECORD
  Future<int> deleteAllSyncExceptLast() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawDelete('''
    DELETE FROM$db_sync WHERE $db_sync_syncId< (SELECT MAX($db_sync_syncId) FROM $db_sync) AND $db_workspace_workspaceId = ?
    ''', [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

}