import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';

class SalestransStagingDBScript {
  Future<List<Map<String, dynamic>>> queryAllSalesTransaction({String? tableName}) async {
    Database db = await DatabaseHelper.instance.database;
    return (db.rawQuery('SELECT * FROM $db_salesTrans_salesTransactionsStaging WHERE $db_product_rowStatus < 2 AND $db_workspace_workspaceId = ?',
        [MyApp.activeWorkspace.workspaceId]));
  }

   Future<List<Map<String, dynamic>>> queryAllSalesFromTransaction() async {
    Database db = await DatabaseHelper.instance.database;
    return (db.rawQuery('SELECT * FROM $db_salesTransactions WHERE $db_product_rowStatus < 2 AND $db_workspace_workspaceId = ?',
        [MyApp.activeWorkspace.workspaceId]));
  }

  Future getStagingTotalAmount(String? saleID, {int? saleNum}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (saleID != null) {
      if (saleNum == null) {
        result = await db.rawQuery(
            "SELECT SUM($db_salesTrans_amount) as amount FROM $db_salesTrans_salesTransactionsStaging WHERE $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2",
            [saleID]);
      } else {
        result = await db.rawQuery(
            "SELECT SUM($db_salesTrans_amount) as amount FROM $db_salesTrans_salesTransactionsStaging WHERE $db_salesTrans_fkSalesId = ? AND $db_sales_isActiveSale != ? AND $db_product_rowStatus < 2",
            [saleID, saleNum]);
      }
    }
    return result;
  }

  Future<int?> queryActiveTransactionRowCount(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT MAX($db_sales_isActiveSale) FROM $db_salesTrans_salesTransactionsStaging WHERE $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2',
        [id]));
    return result;
  }

  Future<List<Map<String, dynamic>>> queryActiveTransactionList(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT $db_sales_isActiveSale FROM $db_salesTrans_salesTransactionsStaging WHERE $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2 GROUP BY $db_sales_isActiveSale',
        [id]);
    return result;
  }

  Future<List<Map<String, dynamic>>> advanceOrderTransactionList(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_salesTransactions WHERE $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2', [id]);
    return result;
  }

  Future<int> deleteProductsBasedSalesIdAndProductIdAndToppingIdInStaging(salesId, id, toppingsId, prices, activeSaleNo, fullTopping) async {
    Database db = await DatabaseHelper.instance.database;
    if (toppingsId != null) {
      var result = await db.rawUpdate('''
            UPDATE  $db_salesTrans_salesTransactionsStaging 
            SET $db_product_rowStatus = ?,$db_salesTrans_salesTransactionSync = ? WHERE $db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ? AND$db_toppings_toppingId = ? AND $db_sales_isActiveSale = ? AND $db_salesTrans_price = ?''',
          [2, 0, salesId, id, toppingsId, activeSaleNo, prices]);
      return result;
    } else {
      if (fullTopping == null) {
        var result = await db.rawUpdate('''
            UPDATE  $db_salesTrans_salesTransactionsStaging 
            SET $db_product_rowStatus = ?,$db_salesTrans_salesTransactionSync = ? WHERE $db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ? AND $db_sales_isActiveSale = ? AND $db_salesTrans_price = ?''',
            [2, 0, salesId, id, activeSaleNo, prices]);
        return result;
      } else {
        var result = await db.rawUpdate('''
            UPDATE  $db_salesTrans_salesTransactionsStaging 
            SET $db_product_rowStatus = ?,$db_salesTrans_salesTransactionSync = ? WHERE $db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ? AND $db_sales_isActiveSale = ?''',
            [2, 0, salesId, id, activeSaleNo]);
        return result;
      }
    }
  }

  Future<int> deleteSalesTransactionStaging(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE  $db_salesTrans_salesTransactionsStaging 
    SET $db_product_rowStatus = ?,$db_salesTrans_salesTransactionSync = ? WHERE $db_salesTrans_transactId = ?
    ''', [2, 0, id]);
    return result;
  }

  Future<int> deleteSalesTransactionStagingBySaleNo(String salesID, int activeSaleNo) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE  $db_salesTrans_salesTransactionsStaging 
    SET $db_product_rowStatus = ?,$db_salesTrans_salesTransactionSync = ? WHERE $db_salesTrans_fkSalesId = ? AND $db_sales_isActiveSale = ?
    ''', [2, 0, salesID, activeSaleNo]);
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllStagingSalesById(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        '''SELECT  $db_salesTrans_transactId,$db_salesTrans_fkSalesId,$db_salesTrans_salesProductId,$db_salesTrans_salesProductName,
        SUM($db_salesTrans_quantity) AS $db_salesTrans_quantity,$db_salesTrans_price,$db_salesTrans_actualPrice,$db_salesTrans_salesCGst,
        $db_salesTrans_salesSGst,SUM($db_salesTrans_salesCGstAmount) $db_salesTrans_salesCGstAmount,SUM($db_salesTrans_salesSGstAmount) $db_salesTrans_salesSGstAmount,
        $db_salesTrans_notes,$db_salesTrans_salesTransactionSync,$db_salesTrans_productType,
        SUM($db_salesTrans_amount) AS $db_salesTrans_amount,SUM($db_salesTrans_amountWithoutGst) AS $db_salesTrans_amountWithoutGst,
        $db_workspace_workspaceId,$db_product_rowStatus FROM  $db_salesTrans_salesTransactionsStaging 
        WHERE $db_workspace_workspaceId=? AND $db_salesTrans_fkSalesId = ? AND $db_product_rowStatus < 2 GROUP BY $db_salesTrans_salesProductId,$db_salesTrans_price''',
        [MyApp.activeWorkspace.workspaceId, id]);
    return result;
  }

  Future<int> updateSalesTransactionStaggingSync(String id, int rowStatus) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesTrans_salesTransactionsStaging 
    SET $db_salesTrans_salesTransactionSync = ${0}, $db_product_rowStatus = ${rowStatus}
    WHERE  $db_salesTrans_transactId = ?
    ''', [id]);
    return result;
  }
}
