// ignore_for_file: non_constant_identifier_names, unused_local_variable, sdk_version_ui_as_code

import 'dart:io';

import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:sqflite_migration/sqflite_migration.dart';

import 'db_script_initial.dart';
import 'db_script_migration.dart';

class DatabaseHelper {
  final config = MigrationConfig(initializationScript: initialScript, migrationScripts: migrationScripts);

  DatabaseHelper._privateConstructor();
  static final DatabaseHelper instance = DatabaseHelper._privateConstructor();

  // only have a single app-wide reference to the database
  static Database? _database;
  Future<Database> get database async {
    if (_database != null) {
      return _database!;
    } else {
      // lazily instantiate the db the first time it is accessed
      _database = await _initDatabase();
    }
    return _database!;
  }

  _initDatabase() async {
    try {
      if (Platform.isWindows) {
        /// For Windows
        sqfliteFfiInit();
        var databaseFactory = databaseFactoryFfi;
        final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
        String path = join(appDocumentsDir.path, "databases", db_name);
        Database db = await databaseFactory.openDatabase(
          path,
        );
        return db;
      } else {
        /// For Android & IOS
        String path = join(await getDatabasesPath(), db_name);
        return await openDatabaseWithMigration(path, config);
      }
    } catch (e) {}
  }

  static executeDBScript() async {
    if (Platform.isWindows) {
      /// For Windows
      sqfliteFfiInit();
      var databaseFactory = databaseFactoryFfi;
      final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
      String path = join(appDocumentsDir.path, "databases", db_name);
      Database db = await databaseFactory.openDatabase(
        path,
      );
      _database = db;
    }
    initialScript.addAll(migrationScripts);
    for (String script in initialScript) {
      _database?.execute(script);
    }
    // for (String script in migrationScripts) {
    //   _database.execute(script);
    // }
  }
}
