import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/reports.dart';
import '../../model/transaction_model.dart';

class SalesHoldSalesDetailsDBScript{
    //HOLD SALES INSERT
  Future<int> insertHoldSalesDetails(Sales reports) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_sales_holdSalesDetails, {
      db_sales_holdSalesName: reports.holdSalesName,
      db_sales_salesId: reports.salesId,
      db_sales_invoiceId: reports.invoiceId,
      db_sales_salesDate: reports.date,
      db_sales_salesTotalAmount: reports.totalAmount,
      db_sales_salesPaymentType: reports.paymentType,
      db_sales_salesSync: reports.sync,
      db_workspace_workspaceId: reports.workspaceId,
    });
    return result;
  }
    Future<List<Map<String, dynamic>>> queryallRowsHoldSalesDetails() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_sales_holdSalesDetails,
        where: '$db_workspace_workspaceId=?',
        whereArgs: [MyApp.activeWorkspace.workspaceId]);
  }
    Future<int> deleteHoldSales(id) async {
    await deleteHoldTransactionsBasedSalesId(id);
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_sales_holdSalesDetails,
        where: '$db_sales_salesId = ?', whereArgs: [id]);
  }
  
  Future<int> deleteAllHoldSalesAndTransactions() async {
    Database db = await DatabaseHelper.instance.database;
    await db.delete(db_sales_holdSalesDetails);
    return await db.delete(db_sales_holdSalesDetails);
  }
    Future<int> deleteHoldTransactionsBasedSalesId(id) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_salesTrans_holdSalesTransactions,
        where: '$db_salesTrans_fkSalesId = ?', whereArgs: [id]);
  }

  
  //INSERT HOLD SALES TRANSACTIONS
  Future<int> insertHoldTransactions(Transactions transactions) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_salesTrans_holdSalesTransactions, {
      db_salesTrans_transactId: transactions.transactId,
      db_salesTrans_fkSalesId: transactions.salesId,
      db_salesTrans_salesProductId: transactions.prodId,
      db_salesTrans_salesProductName: transactions.name,
      db_salesTrans_quantity: transactions.qty,
      db_salesTrans_price: transactions.price,
      db_salesTrans_actualPrice: transactions.actualPrice,
      db_salesTrans_salesCGst: transactions.cGst,
      db_salesTrans_salesCGstAmount: transactions.cGstAmount,
      db_salesTrans_salesSGst: transactions.sGst,
      db_salesTrans_salesSGstAmount: transactions.sGstAmount,
      db_salesTrans_amount: transactions.amount,
      db_salesTrans_amountWithoutGst: transactions.amountWithoutGst,
      db_salesTrans_salesTransactionSync: transactions.sync,
      db_workspace_workspaceId: transactions.workspaceId,
    });
    return result;
  }
    Future<List<Map<String, dynamic>>> queryAllHoldTransactionsBasedOnSalesId(
      id) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_salesTrans_holdSalesTransactions,
        where: '$db_salesTrans_fkSalesId = ?', whereArgs: [id]);
  }


  Future<List<Map<String, dynamic>>> queryAllRowsHoldSalesTransactions() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_salesTrans_holdSalesTransactions);
  }
}