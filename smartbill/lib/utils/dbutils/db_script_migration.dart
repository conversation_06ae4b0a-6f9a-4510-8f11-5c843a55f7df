import 'db_strings.dart';

final migrationScripts = <String>[
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_customerName TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_productKeyName TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_priceType INTEGER DEFAULT NULL;
    ''',
  '''
    CREATE TABLE $db_userPermissions_productKeyword(
    $db_userPermissions_keywordId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_userPermissions_productKeyId TEXT NOT NULL,
    $db_userPermissions_keywordName TEXT NOT NULL,
    $db_userPermissions_keywordSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    CREATE TABLE $db_priceTypes(
    $db_priceTypes_priceId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_priceTypes_priceTypeId TEXT NOT NULL,
    $db_product_productId TEXT NOT NULL,
    $db_priceTypes_measurement TEXT NOT NULL,
    $db_priceTypes_measurementPrice TEXT NOT NULL,
    $db_priceTypes_measurementSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    CREATE TABLE $db_toppingGroups(
    $db_toppingGroups_toppingGroupAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_toppingGroups_toppingGroupId TEXT NOT NULL,
    $db_toppingGroups_toppingGroupName TEXT NOT NULL,
    $db_toppingGroups_toppingGroupSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    CREATE TABLE $db_toppings(
    $db_toppings_toppingAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_toppings_toppingId TEXT NOT NULL,
    $db_toppingGroups_toppingGroupId TEXT NOT NULL,
    $db_toppings_toppingName TEXT NOT NULL,
    $db_toppings_toppingPrice TEXT NOT NULL,
    $db_toppings_toppingSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_toppingGroups_toppingGroupId TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_salesTrans_productType TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_toppings_toppingId TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_shopDetails ADD COLUMN $db_shop_shopAddress3 TEXT DEFAULT NULL;
     ''',
  '''
    ALTER TABLE $db_salesTransactions RENAME TO TempSalesTransaction;
    ''',
  '''
          CREATE TABLE $db_salesTransactions (
            $db_salesTrans_salesTransactionsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesTrans_transactId TEXT NOT NULL,
            $db_salesTrans_fkSalesId INTEGER NOT NULL,
            $db_salesTrans_salesProductId INTEGER NOT NULL,
            $db_salesTrans_salesProductName TEXT NOT NULL,
            $db_salesTrans_quantity TEXT NULL,
            $db_salesTrans_price TEXT NULL,
            $db_salesTrans_actualPrice TEXT NULL,
            $db_salesTrans_salesCGst TEXT NULL,
            $db_salesTrans_salesCGstAmount TEXT NULL,
            $db_salesTrans_salesSGst TEXT NULL,
            $db_salesTrans_salesSGstAmount TEXT NULL,
            $db_salesTrans_amount TEXT NOT NULL,
            $db_salesTrans_amountWithoutGst TEXT NULL,
            $db_salesTrans_productType TEXT NULL,
           $db_toppings_toppingId TEXT NULL,
            $db_salesTrans_salesTransactionSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
   INSERT INTO $db_salesTransactions($db_salesTrans_transactId, $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName,
   $db_salesTrans_quantity,$db_salesTrans_price, $db_salesTrans_actualPrice,$db_salesTrans_salesCGst,$db_salesTrans_salesCGstAmount,$db_salesTrans_salesSGst,$db_salesTrans_salesSGstAmount,$db_salesTrans_amount,
   $db_salesTrans_amountWithoutGst,$db_salesTrans_productType,$db_toppings_toppingId,$db_salesTrans_salesTransactionSync,$db_workspace_workspaceId,$db_product_rowStatus)
   SELECT $db_salesTrans_transactId, $db_salesTrans_fkSalesId, $db_salesTrans_salesProductId, $db_salesTrans_salesProductName,
   $db_salesTrans_quantity,$db_salesTrans_price, $db_salesTrans_actualPrice,$db_salesTrans_salesCGst,$db_salesTrans_salesCGstAmount,$db_salesTrans_salesSGst,$db_salesTrans_salesSGstAmount,$db_salesTrans_amount,
   $db_salesTrans_amountWithoutGst,$db_salesTrans_productType,$db_toppings_toppingId,$db_salesTrans_salesTransactionSync,$db_workspace_workspaceId,$db_product_rowStatus FROM TempSalesTransaction;
    ''',
  '''
    DROP TABLE TempSalesTransaction;
    ''',
  '''
    ALTER TABLE $db_priceTypes RENAME TO TempPriceTypes;
    ''',
  '''
    CREATE TABLE $db_priceTypes(
    $db_priceTypes_priceId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_priceTypes_priceTypeId TEXT NOT NULL,
    $db_product_productId TEXT NULL,
    $db_priceTypes_measurement TEXT NULL,
    $db_priceTypes_measurementPrice TEXT NULL,
    $db_priceTypes_measurementSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
   INSERT INTO $db_priceTypes($db_priceTypes_priceTypeId, $db_product_productId, $db_priceTypes_measurement, $db_priceTypes_measurementPrice,
   $db_priceTypes_measurementSync,$db_workspace_workspaceId,$db_product_rowStatus)
   SELECT $db_priceTypes_priceTypeId, $db_product_productId, $db_priceTypes_measurement, $db_priceTypes_measurementPrice,
   $db_priceTypes_measurementSync,$db_workspace_workspaceId,$db_product_rowStatus FROM TempPriceTypes;
    ''',
  '''
    DROP TABLE TempPriceTypes;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_dynamicQuantity INTEGER DEFAULT NULL;
    ''',
  '''
    CREATE TABLE $db_shopTables(
    $db_shopTables_shopTableAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_shopTables_shopTableId TEXT NOT NULL,
    $db_shopTables_shopTableName TEXT NULL,
    $db_shopTables_shopTableSync INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_sales_isActiveSale INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_fkTableID TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_isActiveSale INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails RENAME TO TempSalesDetails;
    ''',
  '''
          CREATE TABLE $db_salesDetails (
            $db_sales_salesDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_sales_salesId TEXT NOT NULL,
            $db_sales_invoiceId TEXT NULL,
            $db_sales_salesDate TEXT NULL,
            $db_sales_salesTotalAmount TEXT NULL,
            $db_sales_salesPaymentType INTEGER NULL,
            $db_sales_salesTaxMode INTEGER NULL,
            $db_sales_fkTableID TEXT NULL,
            $db_sales_customerName TEXT NULL,
            $db_sales_salesSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_sales_isActiveSale INTEGER NULL
          )
          ''',
  '''
   INSERT INTO $db_salesDetails($db_sales_salesId, $db_sales_invoiceId, $db_sales_salesDate, $db_sales_salesTotalAmount,$db_sales_salesPaymentType,$db_sales_salesTaxMode,$db_sales_isActiveSale,$db_sales_fkTableID,$db_sales_customerName,
   $db_sales_salesSync,$db_workspace_workspaceId,$db_product_rowStatus)
   SELECT $db_sales_salesId, $db_sales_invoiceId, $db_sales_salesDate, $db_sales_salesTotalAmount,$db_sales_salesPaymentType,$db_sales_salesTaxMode,$db_sales_isActiveSale,$db_sales_fkTableID,$db_sales_customerName,
   $db_sales_salesSync,$db_workspace_workspaceId,$db_product_rowStatus FROM TempSalesDetails;
    ''',
  '''
    DROP TABLE TempSalesDetails;
    ''',
  '''
          CREATE TABLE $db_salesTrans_salesTransactionsStaging (
            $db_salesTrans_salesTransactionsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesTrans_transactId TEXT NOT NULL,
            $db_salesTrans_fkSalesId INTEGER NOT NULL,
            $db_salesTrans_salesProductId INTEGER NOT NULL,
            $db_salesTrans_salesProductName TEXT NOT NULL,
            $db_salesTrans_quantity TEXT NULL,
            $db_salesTrans_price TEXT NULL,
            $db_salesTrans_actualPrice TEXT NULL,
            $db_salesTrans_salesCGst TEXT NULL,
            $db_salesTrans_salesCGstAmount TEXT NULL,
            $db_salesTrans_salesSGst TEXT NULL,
            $db_salesTrans_salesSGstAmount TEXT NULL,
            $db_salesTrans_amount TEXT NOT NULL,
            $db_salesTrans_amountWithoutGst TEXT NULL,
            $db_salesTrans_productType TEXT NULL,
           $db_toppings_toppingId TEXT NULL,
            $db_salesTrans_salesTransactionSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_sales_isActiveSale INTEGER NULL
          )
          ''',
  '''
          CREATE TABLE $db_paymentTypes (
            $db_paymentTypes_paymentTypeAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_paymentTypes_paymentTypeId TEXT NOT NULL,
            $db_paymentTypes_paymentTypeName TEXT NOT NULL,
            $db_paymentTypes_createdDate TEXT NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_paymentTypes_paymentTypeSync INTEGER NOT NULL
          )
          ''',
  '''
    ALTER TABLE $db_salesDetails RENAME TO TempSalesDetails;
    ''',
  '''
          CREATE TABLE $db_salesDetails (
            $db_sales_salesDetailsId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_sales_salesId TEXT NOT NULL,
            $db_sales_invoiceId TEXT NULL,
            $db_sales_salesDate TEXT NULL,
            $db_sales_salesTotalAmount TEXT NULL,
            $db_sales_salesPaymentType TEXT NULL,
            $db_sales_salesTaxMode INTEGER NULL,
            $db_sales_fkTableID TEXT NULL,
            $db_sales_customerName TEXT NULL,
            $db_sales_salesSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_sales_isActiveSale INTEGER NULL
          )
          ''',
  '''
   INSERT INTO $db_salesDetails($db_sales_salesId, $db_sales_invoiceId, $db_sales_salesDate, $db_sales_salesTotalAmount,$db_sales_salesPaymentType,$db_sales_salesTaxMode,$db_sales_isActiveSale,$db_sales_fkTableID,$db_sales_customerName,
   $db_sales_salesSync,$db_workspace_workspaceId,$db_product_rowStatus)
   SELECT $db_sales_salesId, $db_sales_invoiceId, $db_sales_salesDate, $db_sales_salesTotalAmount,$db_sales_salesPaymentType,$db_sales_salesTaxMode,$db_sales_isActiveSale,$db_sales_fkTableID,$db_sales_customerName,
   $db_sales_salesSync,$db_workspace_workspaceId,$db_product_rowStatus FROM TempSalesDetails;
    ''',
  '''
    DROP TABLE TempSalesDetails;
    ''',
  '''
          CREATE TABLE $db_generalSettings (
            $db_generalSettingAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_generalSettingName TEXT NULL,
            $db_generalSettingValue TEXT NULL
          )
          ''',
  '''
    ALTER TABLE $db_shopDetails ADD COLUMN $db_shop_shopGstNo TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_shopDetails ADD COLUMN $db_shop_shopFooterText2 TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_enableInventory INTEGER DEFAULT NULL;
    ''',
  '''
          CREATE TABLE $db_stocks (
            $db_stocks_stocksAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_stocks_stocksId TEXT NOT NULL,
            $db_stocks_stocks_fkProdId TEXT NOT NULL,
            $db_stocks_stocksSold TEXT NULL,
            $db_stocks_stocksQty TEXT NULL,
            $db_workspace_workspaceId TEXT NULL,
            $db_product_rowStatus INTEGER NULL,
            $db_stocks_stocksSync INTEGER NULL
          )
          ''',
  '''
          CREATE TABLE $db_stockLogs (
            $db_stockLogsAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_stockLogsId TEXT NOT NULL,
            $db_stockLogs_fkStocksId TEXT NOT NULL,
            $db_stockLogs_modifiedStocks TEXT NULL,
            $db_stockLogs_createdDate TEXT NULL,
            $db_stockLogs_createdBy TEXT NULL,
            $db_workspace_workspaceId TEXT NULL,
            $db_product_rowStatus INTEGER NULL,
            $db_stockLogsSync INTEGER NULL
          )
          ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_createdBy TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_shopDetails ADD COLUMN $db_shop_shopImageID TEXT DEFAULT NULL;
    ''',
  '''
          CREATE TABLE $db_discount (
            $db_discount_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_discount_discountID TEXT NOT NULL,
            $db_discount_couponName TEXT NULL,
            $db_discount_mode TEXT NULL,
            $db_discount_formula TEXT NULL,
            $db_discount_categoryID TEXT NULL,
            $db_discount_createdDate TEXT NULL,
            $db_discount_createdBy TEXT NULL,
            $db_discount_discountActive INTEGER NULL,
            $db_discount_discountSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_productKeywordID TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_discountFormula TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_discountPrice TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_sales_discountFormula TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_sales_discountPrice TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTrans_salesTransactionsStaging ADD COLUMN $db_sales_discountFormula TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTrans_salesTransactionsStaging ADD COLUMN $db_sales_discountPrice TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_invoiceNo TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_salesTrans_isIGST INTEGER DEFAULT NULL;
    ''',
  '''
          CREATE TABLE $db_workSpacesSettings_workSpaceUserSettings (
            $db_workSpacesSettings_settingId INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_workspace_workspaceId TEXT NULL,
            $db_workspace_userName TEXT NULL,
            $db_workSpacesSettings_settingKey TEXT NULL,
            $db_workSpacesSettings_settingValue TEXT NULL,
            $db_workSpacesSettings_settingSync INT NULL
            )''',
  '''
          CREATE TABLE $db_areas (
            $db_area_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_area_areaID TEXT NOT NULL,
            $db_area_areaName TEXT NULL,           
            $db_common_createdDate TEXT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_common_syncStatus INTEGER NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_customer (
            $db_customer_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_customer_customerID TEXT NOT NULL,
            $db_customer_firstName TEXT NOT NULL,
            $db_customer_lastName TEXT NULL,
            $db_customer_mobileNo TEXT NULL,
            $db_customer_email TEXT NULL,
            $db_customer_address1  TEXT NOT NULL,
            $db_customer_address2  TEXT NULL,
            $db_common_createdDate TEXT NULL,
            $db_customer_fK_areaID TEXT NULL,
            $db_customer_GST_No TEXT NULL,
            $db_customer_exist_balance TEXT NULL,
            $db_customer_isWhatsApp INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_common_syncStatus INTEGER NOT NULL
          )
          ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_fK_CustomerID TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_sales_MobileNo TEXT DEFAULT NULL;
    ''',
  '''
          CREATE TABLE $db_balance (
            $db_balance_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_balance_FK_CustomerID TEXT NOT NULL,
            $db_balance_customerBalance TEXT NULL,    
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
          CREATE TABLE $db_salesType (
            $db_salesType_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesType_salesTypeID TEXT NOT NULL,
            $db_salesType_FK_SalesID TEXT NULL,
            $db_salesType_Type TEXT NULL,
            $db_salesType_Amount TEXT NULL,
            $db_salesType_FK_CustomerID TEXT NULL,
            $db_salesType_FK_PaymentTypeID TEXT NULL,
            $db_common_createdDate TEXT NULL,   
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_common_syncStatus INTEGER NOT NULL
          )
          ''',
  '''
    ALTER TABLE $db_customer RENAME TO TempCustomersDetails;
    ''',
  '''
          CREATE TABLE $db_customer (
            $db_customer_ID INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_customer_customerID TEXT NOT NULL,
            $db_customer_firstName TEXT NOT NULL,
            $db_customer_lastName TEXT NULL,
            $db_customer_mobileNo TEXT NULL,
            $db_customer_email TEXT NULL,
            $db_customer_address1  TEXT NULL,
            $db_customer_address2  TEXT NULL,
            $db_common_createdDate TEXT NULL,
            $db_customer_fK_areaID TEXT NULL,
            $db_customer_GST_No TEXT NULL,
            $db_customer_exist_balance TEXT NULL,
            $db_customer_isWhatsApp INTEGER NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL,
            $db_common_syncStatus INTEGER NOT NULL
          )
          ''',
  '''
   INSERT INTO $db_customer($db_customer_customerID, $db_customer_firstName, $db_customer_lastName, $db_customer_mobileNo,$db_customer_email,$db_customer_address1 ,$db_customer_address2 ,$db_common_createdDate,$db_customer_fK_areaID,$db_customer_GST_No,$db_customer_exist_balance,$db_customer_isWhatsApp,
   $db_common_syncStatus,$db_workspace_workspaceId,$db_product_rowStatus)
   SELECT $db_customer_customerID, $db_customer_firstName, $db_customer_lastName, $db_customer_mobileNo,$db_customer_email,$db_customer_address1 ,$db_customer_address2 ,$db_common_createdDate,$db_customer_fK_areaID,$db_customer_GST_No,$db_customer_exist_balance,$db_customer_isWhatsApp,
   $db_common_syncStatus,$db_workspace_workspaceId,$db_product_rowStatus FROM TempCustomersDetails;
    ''',
  '''
    DROP TABLE TempCustomersDetails;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_productDetails ADD COLUMN $db_product_productFor TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTrans_salesTransactionsStaging  ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTrans_holdSalesTransactions ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_sales_holdSalesDetails ADD COLUMN $db_product_parcelAmount TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTransactions ADD COLUMN $db_salesTrans_notes TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesTrans_salesTransactionsStaging ADD COLUMN $db_salesTrans_notes TEXT DEFAULT NULL;
    ''',
  '''
          CREATE TABLE $db_salesPaymentMappingDetails (
            $db_salesPaymentMapping_Id INTEGER PRIMARY KEY AUTOINCREMENT,
            $db_salesPaymentMapping_paymentMappingId TEXT NOT NULL,
            $db_payment_paymentId TEXT NOT NULL,
            $db_sales_salesId TEXT NOT NULL,
            $db_salesPaymentMapping_paymentAmount TEXT NOT NULL,
            $db_salesPaymentMapping_paymentSync INTEGER NOT NULL,
            $db_workspace_workspaceId TEXT NOT NULL,
            $db_product_rowStatus INTEGER NOT NULL
          )
          ''',
  '''
    CREATE TABLE $db_salesPaymentMapping_onlinePlatform(
    $db_salesPaymentMapping_onlinePlatformAutoId INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_salesPaymentMapping_onlinePlatformId TEXT NOT NULL,
    $db_salesPaymentMapping_onlinePlatformName TEXT NULL,
    $db_common_createdDate TEXT NULL,
    $db_common_syncStatus INTEGER NOT NULL,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_product_rowStatus INTEGER NOT NULL
    )
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_sales_OrderType INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_workspace ADD COLUMN $db_workspace_roleId INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_sales_OnlinePlatformID TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_sales_OnlineRefNo TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_sales_holdSalesDetails ADD COLUMN $db_sales_sales_OrderType INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_sales_holdSalesDetails ADD COLUMN $db_sales_sales_OnlinePlatformID TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_sales_holdSalesDetails ADD COLUMN $db_sales_sales_OnlineRefNo TEXT DEFAULT NULL;
    ''',
  '''
    CREATE TABLE $db_expensesCategory(
    $db_expensesCategory_expCatID INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_expensesCategory_expCategoryID TEXT NOT NULL,
    $db_expensesCategory_expCategoryName TEXT NULL,
    $db_createdDate TEXT NULL,
    $db_expensesCategory_workspaceID TEXT NULL,
    $db_expensesCategory_expCategorySync INTEGER NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    CREATE TABLE $db_expensesProduct(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_expProductID TEXT NOT NULL,
    $db_expProductName TEXT NULL,
    $db_createdDate TEXT NULL,
    $db_expProductPrice TEXT NULL,
    $db_workspaceID TEXT NULL,
    $db_expProductSync INTEGER NULL,
    $db_expenseCategoryName TEXT NULL,
    $db_expCategoryID TEXT NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    CREATE TABLE $db_expenses(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_expID TEXT NOT NULL,
    $db_totalAmount TEXT NULL,
    $db_createdDate TEXT NULL,
    $db_invoiceId TEXT NULL,
    $db_workspaceID TEXT NULL,
    $db_expSync INTEGER NULL,
    $db_invoiceNo TEXT NULL,
    $db_expenseDate TEXT NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    CREATE TABLE $db_expenseTransactions(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_expTransactionId TEXT NOT NULL,
    $db_expID TEXT NOT NULL,
    $db_expProductID TEXT NOT NULL,
    $db_quantity TEXT NULL,
    $db_amount TEXT NULL,
    $db_description TEXT NULL,
    $db_expProductPrice TEXT NULL,
    $db_createdDate TEXT NULL,
    $db_workspaceID TEXT NULL,
    $db_expTransSync INTEGER NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    CREATE TABLE $db_purchase(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_purchaseID TEXT NOT NULL,
    $db_totalAmount TEXT NULL,
    $db_invoiceId TEXT NULL,
    $db_invoiceNo TEXT NULL,
    $db_purchaseDate TEXT NULL,
    $db_createdDate TEXT NULL, 
    $db_workspaceID TEXT NULL,
    $db_purchaseSync INTEGER NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    CREATE TABLE $db_purchaseTransaction(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_purchaseTransactionID TEXT NOT NULL,
    $db_purchaseID TEXT NOT NULL,
    $db_purchaseProductID TEXT NOT NULL,
    $db_quantity TEXT NULL,
    $db_purchaseProductPrice TEXT NULL,
    $db_amount TEXT NULL,
    $db_description TEXT NULL,
    $db_createdDate TEXT NULL, 
    $db_workspaceID TEXT NULL,
    $db_purchaseTransSync INTEGER NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_status INTEGER DEFAULT 1;
    ''',
  '''
    CREATE TABLE $db_errorLogs(
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_errorLogID TEXT NOT NULL,
    $db_errorLogUserName TEXT NULL,
     $db_errorFrom TEXT NULL,
    $db_errorLogInfo TEXT NULL,
    $db_createdDate TEXT NULL, 
    $db_workspaceID TEXT NULL,
    $db_errorLogSync INTEGER NULL,
    $db_status INTEGER NULL
    )
    ''',
  '''
CREATE TABLE $db_payment_category (
    $id INTEGER PRIMARY KEY AUTOINCREMENT, 
    $db_Payment_category_ID INTEGER NULL,
    $db_payment_category_name TEXT NULL,
    $db_status INTEGER NULL
)
''',
  '''
    ALTER TABLE $db_paymentTypes ADD COLUMN $db_Payment_category_ID INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_payment_Invoice TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_sales_invoice_Extension TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_Payment_category_ID INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_shift_Id INTEGER DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_salesCategoryId INTEGER DEFAULT NULL;
    ''',
  '''
       CREATE TABLE $db_master_table (
    $id INTEGER PRIMARY KEY AUTOINCREMENT, 
    $db_key TEXT NULL,
    $db_value TEXT NULL,
    $db_workspaceID TEXT NULL,
    $db_master_table_sync INTEGER NULL 
)
''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_deleted_date TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_deleted_by TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_deleted_reason TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_deleted_user_name TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesDetails ADD COLUMN $db_delivery_date_time TEXT DEFAULT NULL;
    ''',
  '''
    ALTER TABLE $db_salesPaymentMappingDetails ADD COLUMN $db_createdDate TEXT DEFAULT NULL;
    ''',
  '''
  CREATE TABLE $db_order_notes (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $db_workspace_workspaceId TEXT NOT NULL,
    $db_sales_salesId TEXT DEFAULT NULL,
    $db_note TEXT DEFAULT NULL,
    $db_createdDate TEXT DEFAULT NULL,
    $db_updated_date TEXT DEFAULT NULL,
    $db_status INTEGER DEFAULT NULL,
    $db_note_sync INTEGER DEFAULT NULL
)
'''
];
