import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../main.dart';
import '../../model/workspace.dart';

class CommonDB {
  Future<int?> checkActiveValueExistsOneColumn(String tName, String cName1, String cValue1) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($cName1) FROM $tName WHERE $cName1 = ? AND ${db_workspace_workspaceId} = ? AND rowStatus < 2',
        [cValue1, MyApp.activeWorkspace.workspaceId]));
    return count;
  }

  Future<int> permanentDeleteCommonFunction(String tName, String cName, String cValue) async {
    Database db = await DatabaseHelper.instance.database;
    var result =
        await db.delete(tName, where: '${db_workspace_workspaceId} = ? AND $cName = ?', whereArgs: [MyApp.activeWorkspace.workspaceId, cValue]);
    return result;
  }

  Future<int?> checkNonSyncCommonFunction(String tName, String cName, String? cValue, String syncColumn) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($cName) FROM $tName WHERE $cName = ? AND ${db_workspace_workspaceId} = ? AND $syncColumn = ${0}',
        [cValue, MyApp.activeWorkspace.workspaceId]));
    return count;
  }

  getSettingValueDynamic(String tName, String cName, cValue, scName, {String? workspaceId}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (workspaceId != null) {
      result = await db.rawQuery("SELECT $scName FROM $tName WHERE $cName = ? AND ${db_workspace_workspaceId} = ? ", [cValue, workspaceId]);
    } else {
      result = await db.rawQuery("SELECT $scName FROM $tName WHERE $cName = ?", [cValue]);
    }
    if (result.isNotEmpty) {
      return result.first['$scName'];
    } else {
      return null;
    }
  }

  Future<int?> checkTableisEmpty(String tName) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(
        await db.rawQuery('SELECT COUNT(1) FROM $tName WHERE ${db_workspace_workspaceId} = ?', [MyApp.activeWorkspace.workspaceId]));
    return result;
  }

  Future<int?> checkValueExistsOneColumn(String tName, String cName, String cValue) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db
        .rawQuery('SELECT COUNT(1) FROM $tName WHERE $cName = ? AND ${db_workspace_workspaceId} = ?', [cValue, MyApp.activeWorkspace.workspaceId]));
    return count;
  }

  Future<List<Map<String, dynamic>>> getAllListDataBasedOneColumn(String tName, String cName, cValue, {int limit = 0}) async {
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery = "";
    if (cValue.runtimeType == int) {
      sqlQuery = "SELECT * FROM $tName WHERE $cName = $cValue AND ${db_workspace_workspaceId}=?";
    } else {
      sqlQuery = "SELECT * FROM $tName WHERE $cName = '$cValue' AND ${db_workspace_workspaceId}=?";
    }
    if (limit > 0) {
      sqlQuery += "LIMIT $limit";
    }
    var result = await db.rawQuery(sqlQuery, [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllListDataBasedOnColumnAndSalesId(String tName, String cName, cValue, List<String> salesIds) async {
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery = "";
    if (cValue.runtimeType == int) {
      sqlQuery =
          "SELECT * FROM $tName WHERE $cName = $cValue AND $db_salesTrans_fkSalesId IN ('${salesIds.join("','")}') AND ${db_workspace_workspaceId}=?";
    } else {
      sqlQuery =
          "SELECT * FROM $tName WHERE $cName = '$cValue' AND $db_salesTrans_fkSalesId IN ('${salesIds.join("','")}') AND ${db_workspace_workspaceId}=?";
    }

    var result = await db.rawQuery(sqlQuery, [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<Map<String, dynamic>?> getSingleDataBasedOneColumn(String tName, String cName, cValue) async {
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery = "";
    if (cValue.runtimeType == int) {
      sqlQuery = "SELECT * FROM $tName WHERE $cName = $cValue AND ${db_workspace_workspaceId}=?";
    } else {
      sqlQuery = "SELECT * FROM $tName WHERE $cName = '$cValue' AND ${db_workspace_workspaceId}=?";
    }
    var result = await db.rawQuery(sqlQuery, [MyApp.activeWorkspace.workspaceId]);
    if (result.isEmpty) {
      return null;
    }
    return result.first;
  }

  Future<int> deleteAllSalesBefDays(days) async {
    DateTime startDate = DateTime.parse('2019-01-01');
    DateTime endDate = DateTime.now().subtract(Duration(days: days));
    Database db = await DatabaseHelper.instance.database;
    var transactQuery =
        "DELETE FROM ${db_salesTransactions} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_salesTrans_salesTransactionSync} = 1 AND ${db_salesTrans_fkSalesId} IN (SELECT ${db_sales_salesId} FROM ${db_salesDetails} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_sales_salesSync} = 1 AND (${db_sales_isActiveSale} != ${1} OR ${db_sales_isActiveSale} IS NULL) AND ${db_sales_salesDate} BETWEEN '$startDate' AND '$endDate')";
    var salesPaymentmaoDeleteQuery =
        "DELETE FROM ${db_salesPaymentMappingDetails} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_salesPaymentMapping_paymentSync} = 1 AND ${db_sales_salesId} IN (SELECT ${db_sales_salesId} FROM ${db_salesDetails} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_sales_salesSync} = 1 AND (${db_sales_isActiveSale} != ${1} OR ${db_sales_isActiveSale} IS NULL) AND ${db_sales_salesDate} BETWEEN '$startDate' AND '$endDate')";

    var salesQuery =
        "DELETE FROM ${db_salesDetails} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_sales_salesSync} = 1 AND (${db_sales_isActiveSale} != ${1} OR ${db_sales_isActiveSale} IS NULL) AND ${db_sales_salesDate} BETWEEN '$startDate' AND '$endDate'";
    await db.rawQuery(transactQuery);
    await db.rawQuery(salesPaymentmaoDeleteQuery);
    await db.rawQuery(salesQuery);
    return 1;
  }

  Future<int?> nonSyncShopFunctionCheck(String tName, String syncColumn) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($syncColumn) FROM $tName WHERE $db_workspace_workspaceId = ? AND $syncColumn = ${0}', [MyApp.activeWorkspace.workspaceId]));
    return count;
  }

  Future<int?> checkValueExists(String tName, String cName1, String cValue1, String cName2, String cValue2) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($cName1) FROM $tName WHERE $cName1 = ? AND $db_workspace_workspaceId = ? AND $cName2 != ? AND $db_product_rowStatus < 2',
        [cValue1, MyApp.activeWorkspace.workspaceId, cValue2]));
    return count;
  }

  Future<String> getSingleColumnBasedOnOneColumn(String tName, String cName1, String cValue1, String sName) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawQuery(
        'SELECT $sName FROM $tName WHERE $cName1 = ? AND $db_workspace_workspaceId = ? LIMIT 1', [cValue1, MyApp.activeWorkspace.workspaceId]);
    // result = await db.rawQuery(
    //     "SELECT $db_sync_commitId FROM $Sync WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' ORDER BY $db_sync_commitDate DESC LIMIT 1");
    if (result != null && result.isNotEmpty) {
      return result.first[sName];
    } else {
      return "";
    }
  }

  Future<int?> checkSync(WorkSpace model) async {
    Database db = await DatabaseHelper.instance.database;
    for (var tableInfo in tableSyncInfo) {
      var tableName = tableInfo['table'];
      var syncColumnName = tableInfo['syncColumn'];
      var result = Sqflite.firstIntValue(
          await db.rawQuery("SELECT COUNT($syncColumnName) FROM $tableName WHERE $db_workspaceID ='${model.workspaceId}' AND $syncColumnName==${0}"));
      if (result != 0) {
        return result;
      }
      var listSyncResult = await checkSyncList() == true ? 1 : 0;
      if(listSyncResult != 0){
        return listSyncResult;
      }

    }
    return 0;
  }

  ///If the list of data contains the sync status 0 it can be return true
  ///OrElse it return false
  bool checkSyncList() {
  for (var entry in syncChecks.entries) {
    if (entry.value()) {
      return entry.value();
    }
  }

  return false;
}

  //DELETE ALL RECORDS BASED ON WORKSPACE ID
  Future<int> deleteAllRecordsBasedOnWorkspace(String? id, {reset = false}) async {
    Database db = await DatabaseHelper.instance.database;
    DBFuctionsScript dbScript = DBFuctionsScript();
    var result;

    // Define a list of table names
    final tableNames = [
      db_productDetails,
      db_userPermissions_productKeyword,
      db_priceTypes,
      db_toppingGroups,
      db_toppings,
      db_salesTransactions,
      db_salesDetails,
      db_sync,
      db_shopDetails,
      db_workSpacesSettings_workSpaceUserSettings,
      db_shopTables,
      db_paymentTypes,
      db_stocks,
      db_stockLogs,
      db_discount,
      db_customer,
      db_areas,
      db_salesType,
      db_expensesCategory,
      db_expensesProduct,
      db_expenses,
      db_expenseTransactions,
      db_purchase,
      db_purchaseTransaction,
      db_salesPaymentMappingDetails
    ];
    for (final tableName in tableNames) {
      result = await dbScript.deleteFunction(tableName, db_workspace_workspaceId, id ?? "");
    }

    if (!reset) {
      // Additional delete operations if reset is false
      final additionalTableNames = [
        db_workSpaceSettings,
        db_invoicePrefix,
        db_userPermissions,
      ];

      for (final tableName in additionalTableNames) {
        result = await dbScript.deleteFunction(tableName, db_workspace_workspaceId, id ?? "");
      }
    }
    return result;
  }
}
