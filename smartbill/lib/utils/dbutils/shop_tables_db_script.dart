import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/add_shop_table.dart';

class ShopTablesDBScript {
  Future<int> insertShopTable(ShopTable model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_shopTables, {
      db_shopTables_shopTableId: model.shopTableId,
      db_shopTables_shopTableName: model.shopTableName,
      db_shopTables_shopTableSync: model.shopTableSync,
      db_workspace_workspaceId: model.workspaceId,
      db_product_rowStatus: model.rowStatus,
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetTablesID() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_shopTables_shopTableId FROM $db_shopTables WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncShopTablesData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_shopTables WHERE $db_shopTables_shopTableSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryGetShopTables() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_shopTables WHERE $db_product_rowStatus < 2 AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<bool> queryGetShopTablesBySynced(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT $db_shopTables_shopTableSync FROM $db_shopTables WHERE $db_shopTables_shopTableId=? AND $db_workspace_workspaceId=?',
        [id, MyApp.activeWorkspace.workspaceId]));
    if (result == 0) {
      return true;
    } else {
      return false;
    }
  }

  Future<int> updateShopTable(ShopTable model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_shopTables 
    SET $db_shopTables_shopTableName = ?, $db_shopTables_shopTableSync = ? ,$db_workspace_workspaceId=? ,$db_product_rowStatus=?
    WHERE $db_shopTables_shopTableId = ?
    ''', [
      model.shopTableName,
      model.shopTableSync,
      model.workspaceId,
      model.rowStatus,
      model.shopTableId
    ]);
    return result;
  }

  Future<int> deleteUpdateShopTable(ShopTable model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_shopTables 
    SET $db_shopTables_shopTableSync = ? ,$db_workspace_workspaceId=? ,$db_product_rowStatus=?
    WHERE $db_shopTables_shopTableId = ?
    ''', [
      model.shopTableSync,
      model.workspaceId,
      model.rowStatus,
      model.shopTableId
    ]);
    return result;
  }
}
