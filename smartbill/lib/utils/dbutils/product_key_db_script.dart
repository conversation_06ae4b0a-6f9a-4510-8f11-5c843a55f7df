import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/products.dart';
import 'database_helper.dart';

class ProductKeyDBScript {
  //PRODUCTS  KEYWORD DETAILS INSERT
  Future<int> insertKeyword(ProductKey model) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(db_userPermissions_productKeyword, {
      db_userPermissions_productKeyId: model.productKeyId,
      db_userPermissions_keywordName: model.keywordName,
      db_workspace_workspaceId: model.workspaceId,
      db_userPermissions_keywordSync: model.keywordSync,
      db_product_rowStatus: model.rowStatus,
    });
  }

  Future<List<Map<String, dynamic>>> queryallRowsProductKeyTable() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_userPermissions_productKeyword WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_userPermissions_keywordName != '' AND $db_product_rowStatus < 2 ORDER BY $db_userPermissions_keywordId");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetProductsKeyId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_userPermissions_productKeyId FROM $db_userPermissions_productKeyword WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncProductsKeyData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_userPermissions_productKeyword WHERE $db_userPermissions_keywordSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  getProductKeyID(String keyName) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_userPermissions_productKeyId FROM $db_userPermissions_productKeyword WHERE $db_userPermissions_keywordName = '$keyName' AND ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus < 2");
    if (result.isNotEmpty) {
      return result.first["productKeyId"];
    } else {
      return null;
    }
  }

  getCategoryIDUsingProductID(String productID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_userPermissions_productKeyword INNER JOIN $db_productDetails ON $db_productDetails.$db_product_productKeyName=$db_userPermissions_productKeyword.$db_userPermissions_keywordName WHERE $db_productDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_productDetails.$db_product_rowStatus<2 AND $db_userPermissions_productKeyword.$db_product_rowStatus<2 AND  $db_product_productId=?",
        [productID]);
    if (result.isNotEmpty) {
      return result.first["productKeyId"];
    } else {
      return null;
    }
  }

  Future<int> updateProductsKeyTable(ProductKey model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_userPermissions_productKeyword 
    SET $db_userPermissions_keywordName = ? ,$db_userPermissions_keywordSync = ? ,$db_workspace_workspaceId=? ,$db_product_rowStatus=?
    WHERE $db_userPermissions_productKeyId = ?
    ''', [
      model.keywordName,
      model.keywordSync,
      model.workspaceId,
      model.rowStatus,
      model.productKeyId
    ]);
    return result;
  }

  Future<int> updateProductsKeyDelete(ProductKey model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_userPermissions_productKeyword 
    SET $db_userPermissions_keywordSync = ? ,$db_workspace_workspaceId=? ,$db_product_rowStatus=?
    WHERE $db_userPermissions_productKeyId = ?
    ''', [
      model.keywordSync,
      model.workspaceId,
      model.rowStatus,
      model.productKeyId
    ]);
    return result;
  }
}
