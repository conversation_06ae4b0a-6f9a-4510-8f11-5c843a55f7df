import 'package:smartbill/main.dart';

import '../common_function/list_class.dart';

const db_name = "Poc.db";
const db_version = 1;

///Sales Details
const db_salesDetails = "SalesDetails";
const db_sales_customerName = "customerName";
const db_sales_holdSalesDetails = 'HoldSalesDetails';
const db_sales_holdSalesName = 'holdSalesName';
const db_sales_salesDetailsId = 'salesDetailsId';
const db_sales_salesId = 'salesId';
const db_sales_invoiceId = 'invoiceId';
const db_sales_salesDate = 'date';
const db_sales_salesTotalAmount = 'totalAmount';
const db_sales_salesPaymentType = 'salesPaymentType';
const db_sales_salesSync = 'salesSync';
const db_sales_salesTaxMode = 'salesTaxMode';
const db_sales_fkTableID = 'fkTableID';
const db_sales_isActiveSale = 'isActiveSale';
const db_sales_createdBy = 'createdBy';
const db_sales_discountFormula = 'discountFormula';
const db_sales_discountPrice = 'discountPrice';
const db_sales_invoiceNo = 'invoiceNo';
const db_sales_fK_CustomerID = "FK_CustomerID";
const db_sales_sales_MobileNo = "CustomerMobileNo";
const db_sales_sales_OrderType = "orderType";
const db_sales_sales_OnlinePlatformID = "onlinePlatformID";
const db_sales_sales_OnlineRefNo = "onlineRefNo";
const db_sales_payment_Invoice = "paymentInvoice";
const db_sales_invoice_Extension = "invoiceExtension";
const db_sales_status = "salesStatus";
const db_salesCategoryId = "salesCategoryId";

///Sales Transaction
const db_salesTransactions = 'SalesTransactions';
const db_salesTrans_holdSalesTransactions = 'HoldSalesTransactions';
const db_salesTrans_salesTransactionsStaging = 'SalesTransactionsStaging';
const db_salesTrans_salesTransactionsId = 'salesTransactionsId';
const db_salesTrans_transactId = 'transactId';
const db_salesTrans_fkSalesId = 'fkSalesId';
const db_salesTrans_salesProductId = 'salesProductId';
const db_salesTrans_salesProductName = 'salesProductName';
const db_salesTrans_quantity = 'quantity';
const db_salesTrans_price = 'price';
const db_salesTrans_actualPrice = 'actualPrice';
const db_salesTrans_salesCGst = 'salesCGst';
const db_salesTrans_salesSGst = 'salesSGst';
const db_salesTrans_salesCGstAmount = 'salesCGstAmount';
const db_salesTrans_salesSGstAmount = 'salesSGstAmount';
const db_salesTrans_amount = 'amount';
const db_salesTrans_amountWithoutGst = 'amountWithoutGst';
const db_salesTrans_salesTransactionSync = 'salesTransactionSync';
const db_salesTrans_productType = 'productType';
const db_salesTrans_isIGST = 'isIGST';
const db_salesTrans_notes = 'notes';

///Shop details
const db_shopDetails = 'ShopDetails';
const db_shop_shopId = 'shopId';
const db_shop_shopName = 'shopName';
const db_shop_shopAddress1 = 'shopAddress1';
const db_shop_shopAddress2 = 'shopAddress2';
const db_shop_shopAddress3 = 'shopAddress3';
const db_shop_shopFooterText = 'shopfooterText';
const db_shop_shopFooterText2 = 'shopFooterText2';
const db_shop_shopGstNo = 'shopGstNo';
const db_shop_shopSync = 'shopSync';
const db_shop_shopImageID = 'shopImageID';

///Product details
const db_productDetails = 'ProductDetails';
const db_product_productDetailsId = 'productDetailsId';
const db_product_productId = 'productId';
const db_product_productSaleId = 'productSaleId';
const db_product_productName = 'productName';
const db_product_productPrice = 'productPrice';
const db_product_productMrp = 'productMrp';
const db_product_productKeyName = 'productKeyName';
const db_product_priceType = 'priceType';
const db_product_productCgst = 'productCgst';
const db_product_productSgst = 'productSgst';
const db_product_productImage = 'productImage';
const db_product_productSync = 'productSync';
const db_product_activatedKeysList = 'ActivatedKeysList';
const db_product_keysId = 'keysId';
const db_product_key = 'key';
const db_product_rowStatus = 'rowStatus';
const db_product_productDate = 'productDate';
const db_product_dynamicQuantity = 'dynamicQuantity';
const db_product_enableInventory = 'enableInventory';
const db_product_productKeywordID = 'productKeywordID';
const db_product_parcelAmount = 'parcelAmount';
const db_product_productFor = 'productFor';

///Deleted product details
const db_deletedProducts = 'DeletedProducts';
const db_deletedProd_deletedProductsId = 'id';
const db_deletedProd_removedId = 'removedId';

///Deleted reports details
const db_deletedReports = 'DeletedReports';
const db_deletedReports_deletedReportsId = 'id';
const db_deletedReports_removedReportsId = 'removedReportsId';

///Payment details
const db_paymentDetails = 'PaymentDetails';
const db_payment_paymentDetailsId = 'paymentDetailsId';
const db_payment_paymentId = 'paymentId';
const db_payment_paymentName = 'paymentName';
const db_payment_paymentNetworkImage = 'paymentNetworkImage';
const db_payment_paymentImage = 'paymentImage';
const db_payment_paymentIsActive = 'paymentIsActive';

///Deleted payment
const db_deletedPayments = 'DeletedPayments';
const db_deletedPayments_deletedPaymentsId = 'id';
const db_deletedPayments_removedPaymentsId = 'removedPaymentsId';

///Workspace details
const db_workspace = 'Workspace';
const db_workspace_workspaceTableId = 'id';
const db_workspace_workspaceId = 'workspaceId';
const db_workspace_workspaceName = 'workspaceName';
const db_workspace_userId = 'userId';
const db_workspace_userName = 'userName';
const db_workspace_isActive = 'isActive';
const db_workspace_expiryDate = 'expiryDate';
const db_workspace_roleId = 'roleID';

///Workspace settings
const db_workSpaceSettings = "WorkSpaceSettings";
const db_workSpacesSettings_workSpaceUserSettings = "WorkSpaceUserSettings";
const db_workSpacesSettings_deviceSettings = "DeviceSettings";
const db_workSpacesSettings_settingId = "settingId";
const db_workSpacesSettings_settingKey = "settingKey";
const db_workSpacesSettings_settingValue = "settingValue";
const db_workSpacesSettings_settingSync = "settingSync";

///sync details
const db_sync = "Sync";
const db_sync_syncId = "syncId";
const db_sync_commitId = "commitId";
const db_sync_commitDate = "commitDate";

//PREFIX TABLE VARIABLE
const db_invoicePrefix = "InvoicePrefix";
const db_invoicePrefix_prefixId = "prefixId";
const db_invoicePrefix_prefixName = "prefixName";

//PERMISSION TABLE VARIABLE
const db_userPermissions = "UserPermissions";
const db_userPermissions_permissionId = "permissionId";
const db_userPermissions_permissionName = "permissionName";
const db_userPermissions_permissionValue = "permissionValue";

const db_userPermissions_productKeyword = "ProductKeyword";
const db_userPermissions_productKeyId = "productKeyId";
const db_userPermissions_keywordId = "keywordId";
const db_userPermissions_keywordName = "keywordName";
const db_userPermissions_keywordSync = "keywordSync";

//This Table to mentioned Unit Measurement
const db_priceTypes = "PriceTypes";
const db_priceTypes_priceId = "priceId";
const db_priceTypes_priceTypeId = "priceTypeId";
const db_priceTypes_measurement = "measurement";
const db_priceTypes_measurementPrice = "measurementPrice";
const db_priceTypes_measurementSync = "measurementSync";

//toppings groups
const db_toppingGroups = "ToppingGroups";
const db_toppingGroups_toppingGroupAutoId = "toppingGroupAutoId";
const db_toppingGroups_toppingGroupId = "toppingGroupId";
const db_toppingGroups_toppingGroupName = "toppingGroupName";
const db_toppingGroups_toppingGroupSync = "toppingGroupSync";

//toppings
const db_toppings = "Toppings";
const db_toppings_toppingAutoId = "toppingAutoId";
const db_toppings_toppingId = "toppingId";
const db_toppings_toppingName = "toppingName";
const db_toppings_toppingPrice = "toppingPrice";
const db_toppings_toppingSync = "toppingSync";

///shop tables
const db_shopTables = "ShopTables";
const db_shopTables_shopTableAutoId = "shopTableAutoId";
const db_shopTables_shopTableId = "shopTableId";
const db_shopTables_shopTableName = "shopTableName";
const db_shopTables_shopTableSync = "shopTableSync";

///payment types
const db_paymentTypes = "PaymentTypes";
const db_paymentTypes_paymentTypeAutoId = "paymentTypeAutoId";
const db_paymentTypes_paymentTypeId = "paymentTypeId";
const db_paymentTypes_paymentTypeName = "paymentTypeName";
const db_paymentTypes_paymentTypeSync = "paymentTypeSync";
const db_paymentTypes_createdDate = "createdDate";

///General settings
const db_generalSettings = "GeneralSettings";
const db_generalSettingAutoId = "settingsAutoId";
const db_generalSettingName = "settingKey";
const db_generalSettingValue = "settingValue";

///Stocks
const db_stocks = "Stocks";
const db_stocks_stocksAutoId = "stocksAutoId";
const db_stocks_stocksId = "stocksId";
const db_stocks_stocks_fkProdId = "productId";
const db_stocks_stocksQty = "stocksQty";
const db_stocks_stocksSold = "stocksSold";
const db_stocks_stocksSync = "stocksSync";

///Stocks logs
const db_stockLogs = "StockLogs";
const db_stockLogsAutoId = "stockLogsAutoId";
const db_stockLogsId = "stockLogsId";
const db_stockLogs_fkStocksId = "stocksId";
const db_stockLogs_modifiedStocks = "modifiedStocks";
const db_stockLogs_createdDate = "createdDate";
const db_stockLogs_createdBy = "createdBy";
const db_stockLogsSync = "stockLogsSync";

///Discount
const db_discount = "Discount";
const db_discount_ID = "id";
const db_discount_discountID = "discountID";
const db_discount_couponName = "couponName";
const db_discount_mode = "mode";
const db_discount_formula = "formula";
const db_discount_categoryID = "categoryID";
const db_discount_createdDate = "createdDate";
const db_discount_createdBy = "createdBy";
const db_discount_discountActive = "discountActive";
const db_discount_discountSync = "discountSync";

///customers
const db_customer = 'CUSTOMERS';
const db_customer_ID = 'ID';
const db_customer_customerID = 'CustomerID';
const db_customer_firstName = 'FirstName';
const db_customer_lastName = 'LastName';
const db_customer_mobileNo = 'MobileNo';
const db_customer_email = 'Email';
const db_customer_address1 = 'Address1';
const db_customer_address2 = 'Address2';
const db_customer_fK_areaID = 'FK_AreaID';
const db_customer_isWhatsApp = 'isWhatsApp';
const db_customer_exist_balance = 'Exist_Balance';
const db_customer_GST_No = 'GST_No';

//Area
const db_areas = 'AREAS';
const db_area_ID = 'ID';
const db_area_areaID = 'AreaID';
const db_area_areaName = 'AreaName';

///Balance
const db_balance = 'BALANCE';
const db_balance_ID = 'ID';
const db_balance_FK_CustomerID = 'FK_CustomerID';
const db_balance_customerBalance = 'CustomerBalance';

const db_common_createdDate = "CreatedDate";
const db_common_syncStatus = "SyncStatus";

///Sales type
const db_salesType = 'SALESTYPE'; // Customer Transaction
const db_salesType_ID = 'ID';
const db_salesType_salesTypeID = 'SalesTypeID';
const db_salesType_Type = 'SalesType';
const db_salesType_FK_SalesID = 'FK_SalesID';
const db_salesType_Amount = 'Amount';
const db_salesType_FK_CustomerID = "FK_CustomerID";
const db_salesType_FK_PaymentTypeID = "FK_PaymentTypeID";

///Payment mapping
const db_salesPaymentMappingDetails = "SalesPaymentMappingDetails";
const db_salesPaymentMapping_Id = "id";
const db_salesPaymentMapping_paymentMappingId = "paymentMappingId";
const db_salesPaymentMapping_paymentAmount = "paymentAmount";
const db_salesPaymentMapping_paymentSync = "paymentSync";

///Onlie Platform
const db_salesPaymentMapping_onlinePlatform = "OnlinePlatform";
const db_salesPaymentMapping_onlinePlatformAutoId = "id";
const db_salesPaymentMapping_onlinePlatformId = "onlinePlatformId";
const db_salesPaymentMapping_onlinePlatformName = "onlinePlatformName";

const db_status = "status";
const db_createdDate = "createdDate";

///Expenses Category
const db_expensesCategory = "ExpenseCategory";
const db_expensesCategory_expCatID = "expCatID";
const db_expensesCategory_expCategoryID = "expCategoryID";
const db_expensesCategory_expCategoryName = "expCategoryName";
const db_expensesCategory_workspaceID = "workspaceID";
const db_expensesCategory_expCategorySync = "expCategorySync";

///Expenses Product
const id = "id";
const db_expensesProduct = "ExpensesProduct";
const db_expProductID = "expProductID";
const db_expProductName = "expProductName";
const db_expCategoryID = "expCategoryID";
const db_expProductPrice = "expProductPrice";
const db_expenseCategoryName = "expCategoryName";
const db_expProductSync = "sync";

///Expense Transactions
const db_expenseTransactions = "ExpenseTransactions";
const db_expTransactionId = "expTransactionID";
const db_expId = "expID";
const db_quantity = "quantity";
const db_amount = "amount";
const db_description = "description";
const db_expTransSync = "sync";

///Expenses
const db_expenses = "Expenses";
const db_expID = "expID";
const db_totalAmount = "totalAmount";
const db_invoiceId = "invoiceID";
const db_invoiceNo = "invoiceNo";
const db_workspaceID = "workspaceID";
const db_expenseDate = "expDate";
const db_expSync = "sync";

///Purchases
const db_purchase = "Purchase";
const db_purchaseID = "purchaseID";
const db_purchaseDate = "purchaseDate";
const db_purchaseSync = "sync";

///Purchase Transactions
const db_purchaseTransaction = "PurchaseTransaction";
const db_purchaseTransactionID = "purchaseTransactionID";
const db_purchaseProductID = "purchaseProductID";
const db_purchaseProductPrice = "purchaseProductPrice";
const db_purchaseTransSync = "sync";

///Error Logs
const db_errorLogs = "ErrorLogs";
const db_errorLogID = "errorLogID";
const db_errorLogUserName = "errorLogUserName";
const db_errorFrom = "errorFrom";
const db_errorLogInfo = "errorLogInfo";
const db_errorLogSync = "sync";

/// sales column
const db_deleted_date = "deletedDate";
const db_deleted_reason = "deletedReason";
const db_deleted_by = "deletedBy";
const db_deleted_user_name = "deletedUserName";

/// payment Category
const db_payment_category = "PaymentCategory";
const db_payment_category_name = "paymentCategoryName";
const db_Payment_category_ID = "paymentCategoryID";

/// shift
const db_shift_Id = "shiftId";

///Master Table
const db_key = "key";
const db_value = "value";
const db_master_table = "MasterTable";
const db_master_table_sync = "masterTableSync";

const db_delivery_date_time = "deliveryDateAndTime";
const db_order_notes = "OrderNotes";
const db_note = "note";
const db_updated_date = "updatedDate";
const db_note_sync = "sync";

List<Map<String, String>> tableSyncInfo = [
  {
    'table': db_workSpaceSettings,
    'syncColumn': db_workSpacesSettings_settingSync,
  },
  {
    'table': db_productDetails,
    'syncColumn': db_product_productSync,
  },
  {
    'table': db_shopDetails,
    'syncColumn': db_shop_shopSync,
  },
  {
    'table': db_salesDetails,
    'syncColumn': db_sales_salesSync,
  },
  {
    'table': db_salesTransactions,
    'syncColumn': db_salesTrans_salesTransactionSync,
  },
  {
    'table': db_salesTrans_salesTransactionsStaging,
    'syncColumn': db_salesTrans_salesTransactionSync,
  },
   {
    'table': db_salesPaymentMappingDetails,
    'syncColumn': db_salesPaymentMapping_paymentSync,
  },
  {
    'table': db_userPermissions_productKeyword,
    'syncColumn': db_userPermissions_keywordSync,
  },
  {
    'table': db_priceTypes,
    'syncColumn': db_priceTypes_measurementSync,
  },
  {
    'table': db_toppingGroups,
    'syncColumn': db_toppingGroups_toppingGroupSync,
  },
  {
    'table': db_toppings,
    'syncColumn': db_toppings_toppingSync,
  },
  {
    'table': db_shopTables,
    'syncColumn': db_shopTables_shopTableSync,
  },
  {
    'table': db_paymentTypes,
    'syncColumn': db_paymentTypes_paymentTypeSync,
  },
  {
    'table': db_stocks,
    'syncColumn': db_stocks_stocksSync,
  },
  {
    'table': db_stockLogs,
    'syncColumn': db_stockLogsSync,
  },
  {
    'table': db_discount,
    'syncColumn': db_discount_discountSync,
  },
  {
    'table': db_workSpacesSettings_workSpaceUserSettings,
    'syncColumn': db_workSpacesSettings_settingSync,
  },
  {
    'table': db_customer,
    'syncColumn': db_common_syncStatus,
  },
  {
    'table': db_areas,
    'syncColumn': db_common_syncStatus,
  },
  {
    'table': db_salesType,
    'syncColumn': db_common_syncStatus,
  },
  {
    'table': db_salesPaymentMapping_onlinePlatform,
    'syncColumn': db_common_syncStatus,
  },
  {
    'table': db_expensesCategory,
    'syncColumn': db_expensesCategory_expCategorySync,
  },
  {
    'table': db_expensesProduct,
    'syncColumn': db_expProductSync,
  },
  {
    'table': db_expenses,
    'syncColumn': db_expSync,
  },
  {
    'table': db_expenseTransactions,
    'syncColumn': db_expTransSync,
  },
  {
    'table': db_purchase,
    'syncColumn': db_purchaseSync,
  },
  {
    'table': db_purchaseTransaction,
    'syncColumn': db_purchaseTransSync,
  },
  {
    'table': db_order_notes,
    'syncColumn': db_note_sync,
  },
];

Map<String, Function> syncChecks = {
  'SalesCategoryList': () =>
      ListUtility.salesCategoryList.any((element) => element.sync == 0 && element.workspaceID == MyApp.activeWorkspace.workspaceId),
  'ShiftSalesList': () => ListUtility.shiftSalesList.any((element) => element.sync == 0 && element.workspaceID == MyApp.activeWorkspace.workspaceId),

  // Add more sync check functions for other lists as needed
};
