import 'package:smartbill/screens/expenses/model/expense_transaction_model.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../screens/expenses/model/expenses_model.dart';
import 'database_helper.dart';
import 'db_crud_script.dart';

class ExpensesTransactionDBScript {
  FnUtilities fnUtilities = FnUtilities();
  DBFuctionsScript dbScript = DBFuctionsScript();

  ///Update Expenses with delete function from report
  Future<int> updateExpenseDelete(String? id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_expenses 
    SET $db_status = ${2},
    $db_expSync = ${0}
    WHERE $db_expID = ?
    ''', [id]);
    return result;
  }

  ///Update Expense Transaction with delete function from report
  Future<int> updateExpenseTransactionDelete(String? id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_expenseTransactions 
    SET $db_status = ${2},
    $db_expTransSync = ${0}
    WHERE $db_expID = ?
    ''', [id]);
    return result;
  }

//   Expenses insert and update
  upsetExpense(ExpensesModel expenses, String operations) async {
    final expenseMap = {
      db_totalAmount: expenses.totalAmount,
      db_createdDate: expenses.expDate,
      db_expenseDate: expenses.expDate,
      db_invoiceId: expenses.invoiceID,
      db_workspaceID: expenses.workspaceID,
      db_expSync: expenses.sync,
      db_invoiceNo: expenses.invoiceNo,
      db_status: expenses.status,
    };
    if (operations == "insert") {
      expenseMap[db_expID] = expenses.expID;
      await dbScript.dbInsert(db_expenses, expenseMap);
    } else {
      await dbScript.updateModel(db_expenses, expenseMap, db_expID, expenses.expID);
    }
  }

//  Expenses Transaction insert and update
  upsetExpensesTransaction(ExpenseTransactionModel expensesTransaction, String operations) async {
    var expenseTransactionMap;
    if (operations == "insert") {
      expenseTransactionMap = {
        db_expTransactionId: expensesTransaction.expTransactionID,
        db_expId: expensesTransaction.expID,
        db_expProductID: expensesTransaction.expProductID,
        db_quantity: expensesTransaction.quantity,
        db_amount: expensesTransaction.amount,
        db_description: expensesTransaction.description,
        db_expProductPrice: expensesTransaction.expProductPrice,
        db_createdDate: expensesTransaction.createdDate,
        db_workspaceID: expensesTransaction.workspaceID,
        db_expTransSync: expensesTransaction.sync,
        db_status: expensesTransaction.status,
      };
      await dbScript.dbInsert(db_expenseTransactions, expenseTransactionMap);
    } else {
      expenseTransactionMap = {
        db_expTransactionId: expensesTransaction.expTransactionID,
        db_expId: expensesTransaction.expID,
        db_expProductID: expensesTransaction.expProductID,
        db_quantity: expensesTransaction.quantity,
        db_amount: expensesTransaction.amount,
        db_description: expensesTransaction.description,
        db_expProductPrice: expensesTransaction.expProductPrice,
        db_createdDate: expensesTransaction.createdDate,
        db_workspaceID: expensesTransaction.workspaceID,
        db_expTransSync: expensesTransaction.sync,
        db_status: expensesTransaction.status,
      };
      await dbScript.updateModel(db_expenseTransactions, expenseTransactionMap, db_expTransactionId, expensesTransaction.expTransactionID);
    }
  }
}
