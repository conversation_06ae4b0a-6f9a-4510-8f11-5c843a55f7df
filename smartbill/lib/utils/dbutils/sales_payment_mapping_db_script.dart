import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/sales_payment_mapping.dart';
import 'database_helper.dart';

class SalesPaymentMappingDB {
  //PRODUCTS  DETAILS INSERT
  Future<int> SalesPaymentMapInsert(SalesPaymentMapping paymentMapping) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(db_salesPaymentMappingDetails, {
      db_salesPaymentMapping_paymentMappingId: paymentMapping.paymentMappingId,
      db_payment_paymentId: paymentMapping.paymentId,
      db_salesPaymentMapping_paymentAmount: paymentMapping.amount,
      db_sales_salesId: paymentMapping.salesId,
      db_salesPaymentMapping_paymentSync: paymentMapping.paymentSync,
      db_product_rowStatus: paymentMapping.status,
      db_createdDate: paymentMapping.createdDate,
      db_workspace_workspaceId: paymentMapping.workspaceId
    });
  }

  Future<int> updateSalesPaymentMapping(SalesPaymentMapping salesPaymentMapping) async {
    Database db = await DatabaseHelper.instance.database;
    var result;

    result = await db.rawUpdate('''
    UPDATE ${db_salesPaymentMappingDetails} 
    SET ${db_payment_paymentId} = ?, ${db_salesPaymentMapping_paymentAmount} = ? , ${db_salesPaymentMapping_paymentSync} = ?, ${db_product_rowStatus} = ?, ${db_createdDate} = ?
    WHERE ${db_salesPaymentMapping_paymentMappingId} = ? AND ${db_sales_salesId} = ?
    ''', [
      salesPaymentMapping.paymentId,
      salesPaymentMapping.amount,
      salesPaymentMapping.paymentSync,
      salesPaymentMapping.status,
      salesPaymentMapping.createdDate,
      salesPaymentMapping.paymentMappingId,
      salesPaymentMapping.salesId,
    ]);
    return result;
  }

  ///Guery All Rows based on salesId in SalesPaymentMapping table
  Future<List<Map<String, dynamic>>> queryAllRowsBasedOnPaymentSalesId(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT ${db_salesPaymentMappingDetails}.*,${db_paymentTypes}.${db_paymentTypes_paymentTypeName} as paymentName FROM ${db_salesPaymentMappingDetails} INNER JOIN ${db_paymentTypes} ON ${db_salesPaymentMappingDetails}.${db_payment_paymentId}=${db_paymentTypes}.${db_paymentTypes_paymentTypeId} WHERE ${db_salesPaymentMappingDetails}.${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}' AND ${db_salesPaymentMappingDetails}.${db_product_rowStatus}<2 AND  ${db_salesPaymentMappingDetails}.${db_sales_salesId}=?",
        [id]);
    return result;
  }

  ///Insert Payment Mapping from the data of api
  Future<int> insertSalesPaymentMapping(SalesPaymentMapping model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_salesPaymentMappingDetails, {
      db_salesPaymentMapping_paymentMappingId: model.paymentMappingId,
      db_payment_paymentId: model.paymentId,
      db_sales_salesId: model.salesId,
      db_salesPaymentMapping_paymentAmount: model.amount,
      db_salesPaymentMapping_paymentSync: model.paymentSync,
      db_workspace_workspaceId: model.workspaceId,
      db_product_rowStatus: model.status,
      db_createdDate: model.createdDate
    });
    return result;
  }

  ///Update Payment Mapping from api
  Future<int> updatePaymentMappingSync(SalesPaymentMapping model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesPaymentMappingDetails} 
    SET  ${db_payment_paymentId} = ?, ${db_sales_salesId} = ?, ${db_salesPaymentMapping_paymentAmount} = ?, ${db_salesPaymentMapping_paymentSync} = ?, ${db_product_rowStatus} = ?, ${db_createdDate} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesPaymentMapping_paymentMappingId} = ?
    ''', [
      model.paymentId,
      model.salesId,
      model.amount,
      model.paymentSync,
      model.status,
      model.workspaceId,
      model.paymentMappingId,
      model.createdDate,
    ]);
    return result;
  }
}
