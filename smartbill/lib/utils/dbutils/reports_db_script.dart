import 'dart:developer';

import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';

class ReportDetailsDB {
  static var getActivaSalesFilter = '''
      WHERE s.${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' 
      AND s.${db_product_rowStatus} < 2 
      AND IFNULL(s.${db_sales_isActiveSale}, 1) = 1
      ''';
  static String getCreatedByFilter() {
    var createdByFilter = '''AND ${db_sales_createdBy} = '${MyApp.activeUser.username}' ''';
    return createdByFilter;
  }

  static String getDateFilter(startDate, endDate) {
    var dateFilter = '''AND s.[${db_sales_salesDate}] BETWEEN '$startDate' AND '$endDate' ''';
    return dateFilter;
  }

  static String getPaymentFilter(paymentId) {
    var paymentFilter = '''AND IFNULL(s.${db_sales_salesPaymentType}, '') like '%' || '$paymentId' ||'%' ''';
    return paymentFilter;
  }

  static String getPaymentCategoryFilter(String? paymentCategoryId) {
    var paymentCategoryFilter;
    var categoryId = paymentCategoryId ?? '1,2,3'; // Use 1,2,3,4 if paymentCategoryId is null

    if (paymentCategoryId == "5") {
      paymentCategoryFilter = '''AND (s.${db_delivery_date_time} IS NOT NULL OR s.${db_delivery_date_time} != '')''';
    } else {
      paymentCategoryFilter = '''AND s.${db_Payment_category_ID} IN ($categoryId)''';
    }
    return paymentCategoryFilter;
  }

  static String getCommonFilter(keyname, keyvalue) {
    var paymentFilter = '''AND IFNULL(s.${keyname}, '') like '%' || '$keyvalue' ||'%' ''';
    return paymentFilter;
  }

  static Future<Map<String, dynamic>?> getAllSalesByDateToDateForReport(DateTime startDate, DateTime endDate,
      {String? roleId, String? paymentId, bool? deletedRecord, dynamic paymentCategoryId}) async {
    Database db = await DatabaseHelper.instance.database;
    endDate = endDate.add(Duration(days: 1));
    String rowStatusCondition = deletedRecord ?? false ? "= 2" : "< 2";
    var sumSalesQuery = "";
    var totalQtyQuery = "";
    var getSalesQuery = "";
    getActivaSalesFilter = '''
      WHERE s.${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' 
      AND s.${db_product_rowStatus} $rowStatusCondition
      AND IFNULL(s.${db_sales_isActiveSale}, 0) != 1 AND s.${db_sales_status} = 1
      ''';
    //log(getSalesQuery);
    if (roleId == "3") {
      sumSalesQuery = '''
          SELECT COUNT(*) as totalSales, SUM(CAST(s.${db_sales_salesTotalAmount} as DOUBLE)) as totalAmount,SUM(CAST(s.${db_sales_discountPrice} as DOUBLE)) as totalDiscount 
          FROM ${db_salesDetails} s
          $getActivaSalesFilter
          ${getPaymentFilter(paymentId)}
          ${getDateFilter(startDate, endDate)}
          $getCreatedByFilter
          ${getPaymentCategoryFilter(paymentCategoryId)}
        ''';
      totalQtyQuery = ''' 
          SELECT SUM(CAST(st.${db_salesTrans_quantity} as DOUBLE)) as totalQuantity 
          FROM ${db_salesDetails} as s INNER JOIN ${db_salesTransactions} as st ON s.${db_sales_salesId} = st.${db_salesTrans_fkSalesId} 
         $getActivaSalesFilter
          ${getPaymentFilter(paymentId)}
          ${getDateFilter(startDate, endDate)}
          $getCreatedByFilter
          ${getPaymentCategoryFilter(paymentCategoryId)}
        ''';
      getSalesQuery = '''
           SELECT S.*, X.transactionQty FROM
	      	   (SELECT MIN(s.${db_sales_salesId}) SalesID, SUM(st.${db_salesTrans_quantity}) transactionQty FROM ${db_salesDetails} s
	      	   LEFT JOIN ${db_salesTransactions} st ON s.${db_workspace_workspaceId} = st.${db_workspace_workspaceId} 
             AND s.${db_sales_salesId} = st.${db_salesTrans_fkSalesId}
	      	   $getActivaSalesFilter
             ${getPaymentFilter(paymentId)}
             ${getDateFilter(startDate, endDate)}
             $getCreatedByFilter
             ${getPaymentCategoryFilter(paymentCategoryId)}
	      	   GROUP BY ST.fkSalesId) X
	      	 JOIN ${db_salesDetails} S ON s.salesId = X.salesId
           ORDER BY DateTime(S.${db_sales_salesDate}) DESC
        ''';
    } else {
      sumSalesQuery = '''
          SELECT COUNT(*) as totalSales, SUM(CAST(s.${db_sales_salesTotalAmount} as DOUBLE)) as totalAmount,SUM(CAST(s.${db_sales_discountPrice} as DOUBLE)) as totalDiscount 
          FROM ${db_salesDetails} s
          $getActivaSalesFilter
          ${getPaymentFilter(paymentId)}
          ${getDateFilter(startDate, endDate)}
          ${getPaymentCategoryFilter(paymentCategoryId)}
        ''';
      totalQtyQuery = ''' 
          SELECT 
          SUM(CAST(st.${db_salesTrans_quantity} as DOUBLE)) as totalQuantity, 
          SUM(CASE WHEN s.${db_Payment_category_ID} = 4 THEN st.${db_salesTrans_amountWithoutGst} ELSE st.${db_salesTrans_amount} END) AS totalAmount 
          FROM ${db_salesDetails} as s INNER JOIN ${db_salesTransactions} as st ON s.${db_sales_salesId} = st.${db_salesTrans_fkSalesId} 
          AND st.${db_product_rowStatus} $rowStatusCondition
          $getActivaSalesFilter
          ${getPaymentFilter(paymentId)}
          ${getDateFilter(startDate, endDate)} 
          ${getPaymentCategoryFilter(paymentCategoryId)}
        ''';
      getSalesQuery = '''
           SELECT S.*, X.transactionQty, X.totalAmount FROM
	      	   (SELECT MIN(s.${db_sales_salesId}) SalesID, 
                    SUM(st.${db_salesTrans_quantity}) AS transactionQty,
                    SUM(CASE WHEN s.${db_Payment_category_ID} = 4 THEN st.${db_salesTrans_amountWithoutGst} ELSE st.${db_salesTrans_amount} END) AS totalAmount
                FROM ${db_salesDetails} s
	      	   LEFT JOIN ${db_salesTransactions} st ON s.${db_workspace_workspaceId} = st.${db_workspace_workspaceId} 
             AND s.${db_sales_salesId} = st.${db_salesTrans_fkSalesId}
             AND st.${db_product_rowStatus} $rowStatusCondition
	      	   $getActivaSalesFilter
             ${getPaymentFilter(paymentId)}
             ${getDateFilter(startDate, endDate)}
             ${getPaymentCategoryFilter(paymentCategoryId)}
	      	   GROUP BY ST.fkSalesId) X
	      	 JOIN ${db_salesDetails} S ON s.salesId = X.salesId
           ORDER BY DateTime(S.${db_sales_salesDate}) DESC
        ''';
    }
    List<Map<String, Object?>> sumSales = await db.rawQuery(sumSalesQuery);
    List<Map<String, Object?>> totalQty = await db.rawQuery(totalQtyQuery);
    List<Map<String, Object?>> sales = await db.rawQuery(getSalesQuery);

    Map<String, dynamic>? sample;
    if (sumSales.isNotEmpty && totalQty.isNotEmpty && sales.isNotEmpty)
      sample = {
        "totalSales": sumSales[0]["totalSales"],
        "totalAmount": totalQty[0]["totalAmount"],
        "totalDiscount": sumSales[0]["totalDiscount"],
        "totalQty": totalQty[0]["totalQuantity"],
        "sales": sales
      };
    return sample;
  }

  static getCommonReports(DateTime startDate, DateTime endDate, {String? keyName, String? keyValue, bool? deletedRecord}) async {
    Database db = await DatabaseHelper.instance.database;
    endDate = endDate.add(Duration(days: 1));
    String rowStatusCondition = deletedRecord ?? false ? "= 2" : "< 2";
    var salesQuery = "";
    var sumSalesQuery = "";
    getActivaSalesFilter = '''
      WHERE s.${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' 
      AND s.${db_product_rowStatus} $rowStatusCondition
      AND IFNULL(s.${db_sales_isActiveSale}, 0) != 1 AND s.${db_sales_status} = 1
      ''';

    sumSalesQuery = '''
          SELECT COUNT(*) as totalSales, SUM(CAST(s.${db_sales_salesTotalAmount} as DOUBLE)) as totalAmount
          FROM ${db_salesDetails} s
          $getActivaSalesFilter
          ${getCommonFilter(keyName, keyValue)}
          ${getDateFilter(startDate, endDate)}
        ''';
    salesQuery = '''
          SELECT s.*, s.invoiceId as invoiceID, s.salesId as salesID, s.FK_CustomerID as customerID
          FROM ${db_salesDetails} s
          $getActivaSalesFilter
          ${getCommonFilter(keyName, keyValue)}
          ${getDateFilter(startDate, endDate)}
        ''';

    List<Map<String, Object?>> sumSales = await db.rawQuery(sumSalesQuery);
    List<Map<String, Object?>> salesResult = await db.rawQuery(salesQuery);

    Map<String, dynamic>? sample;
    if (salesResult.isNotEmpty && sumSales.isNotEmpty)
      sample = {
        "totalSales": sumSales[0]["totalSales"],
        "totalAmount": sumSales[0]["totalAmount"],
        "reportList": salesResult,
        "workspaceId": MyApp.activeWorkspace.workspaceId
      };
    return sample;
  }
  // Future<List<Map<String, dynamic>>> queryRowsProductsSalesByDateToDate(
  //     startDate, endDate, id) async {
  //   var result;
  //   Database db = await instance.database;
  //   if (id != "All") {
  //     result = await db.rawQuery(
  //         "SELECT $salesProductId,$salesProductName,$price,SUM(CAST($quantity as DOUBLE)) as $quantity,SUM(CAST($amount as DOUBLE)) as $amount FROM $SalesTransactions INNER JOIN $SalesDetails ON $SalesTransactions.$fkSalesId=$SalesDetails.$salesId WHERE $SalesDetails.$workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $SalesDetails.$rowStatus<2 AND $SalesTransactions.$rowStatus<2 AND  $salesProductId=? AND ($SalesDetails.$isActiveSale != ${1} OR  $SalesDetails.$isActiveSale IS NULL) AND $salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $salesProductId, $price",
  //         [id]);
  //   } else {
  //     result = await db.rawQuery(
  //         "SELECT $salesProductId,$salesProductName,$price,SUM(CAST($quantity as DOUBLE)) as $quantity,SUM(CAST($amount as DOUBLE)) as $amount FROM $SalesTransactions INNER JOIN $SalesDetails ON $SalesTransactions.$fkSalesId=$SalesDetails.$salesId WHERE $SalesDetails.$workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $SalesDetails.$rowStatus<2 AND $SalesTransactions.$rowStatus<2 AND ($SalesDetails.$isActiveSale != ${1} OR  $SalesDetails.$isActiveSale IS NULL) AND $salesDate BETWEEN '$startDate' AND '$endDate' GROUP BY $salesProductId, $price");
  //   }
  //   return result;
  // }
}
