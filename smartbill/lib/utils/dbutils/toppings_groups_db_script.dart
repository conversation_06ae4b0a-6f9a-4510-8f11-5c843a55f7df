import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/toppings_model.dart';

class ToppingGroupsDBScript {
  Future<int> insertToppingsGroupDetails(ToppingsGroup model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_toppingGroups, {
      db_toppingGroups_toppingGroupId: model.toppingGroupId,
      db_toppingGroups_toppingGroupName: model.toppingGroupName,
      db_workspace_workspaceId: model.workspaceId,
      db_toppingGroups_toppingGroupSync: model.toppingGroupSync,
      db_product_rowStatus: model.rowStatus,
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsToppingsDetails() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_toppingGroups WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus < 2 ORDER BY $db_toppingGroups_toppingGroupAutoId");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetToppingsGroupId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_toppingGroups_toppingGroupId FROM $db_toppingGroups WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<int?> queryGetProductToppingsSyncNo(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT $db_toppingGroups_toppingGroupSync FROM $db_toppingGroups WHERE $db_toppingGroups_toppingGroupId=? AND $db_workspace_workspaceId=?',
        [id, MyApp.activeWorkspace.workspaceId]));
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncToppingsGroupData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_toppingGroups WHERE $db_toppingGroups_toppingGroupSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int> updateProductsToppingsGroup(ToppingsGroup model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_toppingGroups 
    SET $db_toppingGroups_toppingGroupName = ? ,$db_toppingGroups_toppingGroupSync = ? ,$db_workspace_workspaceId=? ,$db_product_rowStatus=?
    WHERE $db_toppingGroups_toppingGroupId = ?
    ''', [
      model.toppingGroupName,
      model.toppingGroupSync,
      model.workspaceId,
      model.rowStatus,
      model.toppingGroupId
    ]);
    return result;
  }

  Future<int> deleteUpdateProductsToppingsGroup(ToppingsGroup model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_toppingGroups 
    SET $db_toppingGroups_toppingGroupSync = ?, $db_product_rowStatus=?
    WHERE $db_toppingGroups_toppingGroupId = ?
    ''', [model.toppingGroupSync, model.rowStatus, model.toppingGroupId]);
    return result;
  }
}
