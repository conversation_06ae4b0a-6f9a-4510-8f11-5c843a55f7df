import 'package:smartbill/main.dart';
import 'package:smartbill/model/permissions.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

class UserPermissionsDBScript {
  //PERMISSION DETAILS INSERT
  Future<int> insertPermissionDetails(Permissions permission) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_userPermissions, {
      db_workspace_userName: permission.userName,
      db_workspace_workspaceId: permission.workspaceId,
      db_userPermissions_permissionName: permission.keyName,
      db_userPermissions_permissionValue: permission.permissionLevelId,
    });
    return result;
  }

  //PERMISSIONS VALUE GET BASED ON PERMISSION NAME
  Future<List<Map<String, dynamic>>> queryAllGetPermissions() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_userPermissions WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_workspace_userName='${MyApp.activeUser.username}'");
    return result;
  }

  Future<int> updatePermissions(Permissions model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_userPermissions 
    SET $db_userPermissions_permissionValue = ?
    WHERE $db_workspace_userName = ? AND $db_workspace_workspaceId = ? AND $db_userPermissions_permissionName = ?
    ''', [
      model.permissionLevelId,
      model.userName,
      model.workspaceId,
      model.keyName
    ]);
    return result;
  }

  Future<int> checkPermission(Permissions permission) async {
    Database db = await DatabaseHelper.instance.database;
    var count = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT($db_userPermissions_permissionValue) FROM $db_userPermissions WHERE $db_workspace_userName = ? AND $db_workspace_workspaceId = ? AND $db_userPermissions_permissionName = ?",
        [permission.userName, permission.workspaceId, permission.keyName]));
    return count ?? 0;
  }
}
