// ignore_for_file: prefer_typing_uninitialized_variables

import 'package:smartbill/model/customers.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';

class CustomersDBScript {
  // CommonDBFunction funCommonDB = CommonDBFunction();
  // SalesDBScript salesDB = SalesDBScript();
  Future<int> insertCustomer(Customer model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_customer, {
      db_customer_customerID: model.customerID,
      db_customer_firstName: model.firstName,
      db_customer_lastName: model.lastName,
      db_customer_mobileNo: model.mobileNo,
      db_customer_email: model.email,
      db_customer_address1: model.address1,
      db_customer_address2: model.address2,
      db_customer_isWhatsApp: model.isWhatsApp,
      db_customer_fK_areaID: model.fkAreaID,
      db_customer_GST_No: model.gstNo,
      db_customer_exist_balance: model.exitBalance,
      db_common_createdDate: model.createdDate,
      db_product_rowStatus: model.rowStatus,
      db_common_syncStatus: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllCustomers() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_customer_customerID}");
    return result;
  }

  Future<List<Map<String, dynamic>>> selectCustomersByID(
      String customerID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_customer_customerID} = '$customerID'");
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllCustomersByArea(
      String area) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_customer_fK_areaID} = '$area'  AND ${db_product_rowStatus} < 2 ORDER BY ${db_customer_customerID}");
    return result;
  }

  Future<List<Map<String, dynamic>>> selectCustomerByOptionSearch(
      String option, String search) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (option == "Mobile No" && search != "") {
      result = await db.rawQuery(
          "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_customer_mobileNo} LIKE '%$search%'  AND ${db_product_rowStatus} < 2 ORDER BY ${db_customer_customerID}");
    } else if (option == "Name" && search != "") {
      result = await db.rawQuery(
          "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND (${db_customer_firstName} ||' '|| ${db_customer_lastName}) LIKE '%$search%' AND ${db_product_rowStatus} < 2 ORDER BY ${db_customer_customerID}");
    } else if (option == null && search != "") {
      result = await db.rawQuery(
          "SELECT * FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND (${db_customer_firstName} ||' '|| ${db_customer_lastName}) LIKE '%$search%' AND ${db_product_rowStatus} < 2 ORDER BY ${db_customer_customerID}");
    } else {
      result = await selectAllCustomers();
    }
    return result;
  }

  Future<int> updateCustomer(Customer model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_customer} 
    SET ${db_customer_firstName} = ?, ${db_customer_lastName} = ?, ${db_customer_mobileNo} = ?, ${db_customer_email} = ?, ${db_customer_address1} = ?, ${db_customer_address2} = ?, ${db_customer_isWhatsApp} = ?, ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ?, ${db_customer_fK_areaID} = ?, ${db_customer_GST_No} = ?, ${db_customer_exist_balance} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_customer_customerID} = ?
    ''', [
      model.firstName,
      model.lastName,
      model.mobileNo,
      model.email,
      model.address1,
      model.address2,
      model.isWhatsApp,
      model.rowStatus,
      model.syncStatus,
      model.fkAreaID,
      model.gstNo,
      model.exitBalance,
      model.workspaceID,
      model.customerID
    ]);
    return result;
  }

  Future<int> deleteWithUpdateCustomer(Customer model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_customer} 
    SET ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_customer_customerID} = ?
    ''', [
      model.rowStatus,
      model.syncStatus,
      model.workspaceID,
      model.customerID
    ]);
    return result;
  }

  Future<List<Map<String, dynamic>>> nonSyncCustomerData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_customer} WHERE ${db_common_syncStatus} = ${0} AND ${db_workspace_workspaceId} = ?",
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllCustomersID() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT ${db_customer_customerID} FROM ${db_customer} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }
}
