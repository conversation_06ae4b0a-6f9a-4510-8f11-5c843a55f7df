import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/stocks.dart';
import 'database_helper.dart';

class StocksDBScript {
  Future<int> insertStocks(Stocks model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_stocks, {
      db_stocks_stocksId: model.stocksID,
      db_stocks_stocks_fkProdId: model.productId,
      db_stocks_stocksQty: model.stocksQty,
      db_stocks_stocksSold: model.stocksSold,
      db_product_rowStatus: model.rowStatus,
      db_stocks_stocksSync: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<int> insertStockLogs(StockLogs model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_stockLogs, {
      db_stockLogsId: model.stockLogsID,
      db_stockLogs_fkStocksId: model.stocksID,
      db_stockLogs_modifiedStocks: model.modifiedStocks,
      db_stockLogs_createdDate: model.createdDate,
      db_stockLogs_createdBy: model.createdBy,
      db_product_rowStatus: model.rowStatus,
      db_stockLogsSync: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<int> updateStocks(Stocks model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_stocks} 
    SET ${db_stocks_stocksQty} = ?, ${db_stocks_stocksSold} = ?, ${db_product_rowStatus} = ?, ${db_stocks_stocksSync} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_stocks_stocksId} = ?
    ''', [model.stocksQty, model.stocksSold, model.rowStatus, model.syncStatus, model.workspaceID, model.stocksID]);
    return result;
  }

  Future<int> updateStocksLog(StockLogs model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_stockLogs} 
    SET ${db_stockLogs_modifiedStocks} = ?, ${db_product_rowStatus} = ?, ${db_stockLogsSync} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_stockLogsId} = ?
    ''', [model.modifiedStocks, model.rowStatus, model.syncStatus, model.workspaceID, model.stockLogsID]);
    return result;
  }

  Future<int> updateStocksByProductID(Stocks model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_stocks} 
    SET  ${db_stocks_stocksSold} = ?, ${db_product_rowStatus} = ?, ${db_stocks_stocksSync} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_stocks_stocks_fkProdId} = ?
    ''', [model.stocksSold, model.rowStatus, model.syncStatus, model.workspaceID, model.productId]);
    return result;
  }

  getColumnFromStocksByProductID(String? productID, String selectColumn) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_stocks} WHERE ${db_stocks_stocks_fkProdId} = '$productID' AND ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_stocks_stocksAutoId}");
    if (result.isNotEmpty) {
      return result.first[selectColumn];
    }
    return null;
  }

  Future<List<Map<String, dynamic>>> getAllStocks() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_stocks} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_stocks_stocksAutoId}");
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllStocksId() async {
    Database db = await DatabaseHelper.instance.database;
    var result =
        db.rawQuery("SELECT ${db_stocks_stocksId} FROM ${db_stocks} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllStockLogs() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_stockLogs} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_stockLogsAutoId}");
    return result;
  }

  // Future<List<Map<String, dynamic>>> getAllStockID() async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = db.rawQuery("SELECT $db_stocks_stocksId FROM $db_stocks");
  //   return result;
  // }

  // Future<List<Map<String, dynamic>>> getAllStockLogID() async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = db.rawQuery("SELECT $db_stockLogsId FROM $db_stockLogs");
  //   return result;
  // }

  // Future<List<Map<String, dynamic>>> queryForNonSyncStockData() async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = await db.rawQuery(
  //       'SELECT * FROM $db_stocks WHERE $db_stocks_stocksSync=${0} AND $db_workspace_workspaceId=?',
  //       [MyApp.activeWorkspace.workspaceId]);
  //   return result;
  // }

  // Future<List<Map<String, dynamic>>> queryForNonSyncStockLogData() async {
  //   Database db = await DatabaseHelper.instance.database;
  //   var result = await db.rawQuery(
  //       'SELECT * FROM $db_stockLogs WHERE $db_stockLogsSync=${0} AND $db_workspace_workspaceId=?',
  //       [MyApp.activeWorkspace.workspaceId]);
  //   return result;
  // }
}
