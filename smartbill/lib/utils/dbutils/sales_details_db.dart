import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';
import '../../main.dart';

class SalesDetailsDB {
  Future<Map<String, dynamic>?> getLastSalesForPrinter() async {
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery = "";
    sqlQuery =
        "SELECT * FROM ${db_salesDetails} WHERE ${db_workspace_workspaceId}=? AND (${db_sales_isActiveSale} = ? OR ${db_sales_isActiveSale} IS NULL) AND ${db_product_rowStatus} < ? ORDER BY ${db_sales_salesDetailsId} DESC LIMIT 1";
    var result =
        await db.rawQuery(sqlQuery, [MyApp.activeWorkspace.workspaceId, 0, 2]);
    if (result.isEmpty) {
      return null;
    }
    return result.first;
  }

  Future<int> queryGetSalesSynccount(String? id, String salesType) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (id != null) {
      result = Sqflite.firstIntValue(await db.rawQuery(
          'SELECT $db_common_syncStatus FROM $db_salesType WHERE $db_salesType_FK_SalesID = ? AND $db_workspace_workspaceId = ? AND $db_salesType_Type = ?',
          [id, MyApp.activeWorkspace.workspaceId, salesType]));
    } else {
      result = Sqflite.firstIntValue(await db.rawQuery(
          'SELECT $db_common_syncStatus FROM $db_salesType WHERE $db_workspace_workspaceId = ? AND $db_salesType_Type = ?',
          [MyApp.activeWorkspace.workspaceId, salesType]));
    }
    return result;
  }
    Future<String?> returnCreditAmount(String customerID, String salesID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_salesType_Amount FROM $db_salesType WHERE $db_product_rowStatus < 2 AND $db_workspace_workspaceId = ? AND $db_salesType_FK_CustomerID = ? AND $db_salesType_Type = 'credit' AND $db_salesType_FK_SalesID = ?",
        [MyApp.activeWorkspace.workspaceId, customerID, salesID]);
    if (result.isNotEmpty) {
      return result.first["Amount"].toString();
    }
    return null;
  }
}
