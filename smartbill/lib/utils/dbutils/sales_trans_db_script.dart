import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/transaction_model.dart';

class SalesTransDBScript {
  //SALES TRANSACTIONS INSERT
  Future<int> insertSalesTransactions(Transactions transactions, {String? transactionType}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (transactionType == "staging") {
      result = await db.insert(db_salesTrans_salesTransactionsStaging, {
        db_salesTrans_transactId: transactions.transactId,
        db_salesTrans_fkSalesId: transactions.salesId,
        db_salesTrans_salesProductId: transactions.prodId,
        db_salesTrans_productType: transactions.productType,
        db_toppings_toppingId: transactions.toppingId,
        db_salesTrans_salesProductName: transactions.name,
        db_salesTrans_quantity: transactions.qty,
        db_salesTrans_price: transactions.price,
        db_salesTrans_actualPrice: transactions.actualPrice,
        db_salesTrans_salesCGst: transactions.cGst,
        db_salesTrans_salesCGstAmount: transactions.cGstAmount,
        db_salesTrans_salesSGst: transactions.sGst,
        db_salesTrans_salesSGstAmount: transactions.sGstAmount,
        db_salesTrans_amount: transactions.amount,
        db_salesTrans_amountWithoutGst: transactions.amountWithoutGst,
        db_salesTrans_salesTransactionSync: transactions.sync,
        db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
        db_product_rowStatus: transactions.rowStatus,
        db_sales_isActiveSale: transactions.isActiveSale,
        db_sales_discountFormula: transactions.discountFormula,
        db_sales_discountPrice: transactions.discountPrice,
        db_product_parcelAmount: transactions.parcelAmount,
        db_salesTrans_notes: transactions.notes
      });
    } else {
      result = await db.insert(db_salesTransactions, {
        db_salesTrans_transactId: transactions.transactId,
        db_salesTrans_fkSalesId: transactions.salesId,
        db_salesTrans_salesProductId: transactions.prodId,
        db_salesTrans_productType: transactions.productType,
        db_toppings_toppingId: transactions.toppingId,
        db_salesTrans_salesProductName: transactions.name,
        db_salesTrans_quantity: transactions.qty,
        db_salesTrans_price: transactions.price,
        db_salesTrans_actualPrice: transactions.actualPrice,
        db_salesTrans_salesCGst: transactions.cGst,
        db_salesTrans_salesCGstAmount: transactions.cGstAmount,
        db_salesTrans_salesSGst: transactions.sGst,
        db_salesTrans_salesSGstAmount: transactions.sGstAmount,
        db_salesTrans_amount: transactions.amount,
        db_salesTrans_amountWithoutGst: transactions.amountWithoutGst,
        db_salesTrans_salesTransactionSync: transactions.sync,
        db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
        db_product_rowStatus: transactions.rowStatus,
        db_sales_isActiveSale: transactions.isActiveSale,
        db_sales_discountFormula: transactions.discountFormula,
        db_sales_discountPrice: transactions.discountPrice,
        db_salesTrans_isIGST: transactions.isIGST ?? false ? 1 : 0,
        db_product_parcelAmount: transactions.parcelAmount,
        db_salesTrans_notes: transactions.notes
      });
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllSalesTransactionByCustomer(String customerID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_salesTransactions INNER JOIN $db_salesDetails ON $db_salesTransactions.$db_salesTrans_fkSalesId=$db_salesDetails.$db_sales_salesId WHERE $db_salesDetails.$db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId.toString()}' AND $db_salesDetails.$db_product_rowStatus < 2 AND $db_salesTransactions.$db_product_rowStatus < 2 AND $db_salesDetails.$db_sales_fK_CustomerID = '$customerID'");
    return result;
  }

  Future findQtyBasedOnSalesId(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT SUM(CAST($db_salesTrans_quantity as DOUBLE)) as $db_salesTrans_quantity FROM $db_salesTransactions WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_salesTrans_fkSalesId=?",
        [id]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllRowsBasedOnSalesId(id, {String? tableName, int? isActiveSaleNo, bool? deletedRecord}) async {
    Database db = await DatabaseHelper.instance.database;
    String rowStatusCondition = deletedRecord ?? false ? "= 2" : "< 2";

    var result;
    if (tableName == "staging") {
      if (isActiveSaleNo != null) {
        result = await db.query(db_salesTrans_salesTransactionsStaging,
            where: '$db_salesTrans_fkSalesId = ? and $db_product_rowStatus<2 and $db_workspace_workspaceId = ? and $db_sales_isActiveSale = ?',
            whereArgs: [id, MyApp.activeWorkspace.workspaceId, isActiveSaleNo]);
      } else {
        result = await db.query(db_salesTrans_salesTransactionsStaging,
            where: '$db_salesTrans_fkSalesId = ? and $db_product_rowStatus<2 and $db_workspace_workspaceId = ?',
            whereArgs: [id, MyApp.activeWorkspace.workspaceId]);
      }
    } else {
      result = await db.query(db_salesTransactions,
          where: '$db_salesTrans_fkSalesId = ? and $db_product_rowStatus $rowStatusCondition and $db_workspace_workspaceId = ?',
          whereArgs: [id, MyApp.activeWorkspace.workspaceId]);
    }
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllTransactIdBasedOnSalesId(id, {String? tableName}) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.query(tableName == "staging" ? db_salesTrans_salesTransactionsStaging : db_salesTransactions,
        where: '$db_salesTrans_fkSalesId = ? and $db_product_rowStatus<2', whereArgs: [id]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllRowsSalesTransactions() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_salesTransactions);
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncSalesTransactionData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_salesTransactions WHERE $db_salesTrans_salesTransactionSync=${0}');
    return result;
  }

  Future calculateTotalAmount(String id, {String? tableName}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (tableName == "staging") {
      result = await db.rawQuery(
          "SELECT SUM($db_salesTrans_amount) as amount FROM $db_salesTrans_salesTransactionsStaging WHERE $db_salesTrans_fkSalesId=? AND $db_product_rowStatus<2",
          [id]);
    } else {
      result = await db.rawQuery(
          "SELECT SUM($db_salesTrans_amount) as amount FROM $db_salesTransactions WHERE $db_salesTrans_fkSalesId=? AND $db_product_rowStatus<2",
          [id]);
    }
    return result;
  }

  Future<int?> queryRowCount(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT COUNT($db_salesTrans_fkSalesId) FROM $db_salesTransactions WHERE $db_salesTrans_fkSalesId=? AND $db_product_rowStatus<2', [id]));
    return result;
  }

  Future<int?> queryGetTransactionSyncNo(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        'SELECT $db_salesTrans_salesTransactionSync FROM $db_salesTransactions WHERE $db_salesTrans_transactId=? AND $db_workspace_workspaceId=?',
        [id, MyApp.activeWorkspace.workspaceId]));
    return result;
  }

  getAllQtyByTransactionID(String transactionID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM $db_salesTransactions WHERE  $db_salesTrans_transactId = '$transactionID' AND ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus < 2");
    if (result.isNotEmpty) {
      return result.first["quantity"];
    } else {
      return null;
    }
  }

  Future<int> updateSalesTransactionDelete(String? id, {String? salesType}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    var tableName = salesType == 'staging' ? db_salesTrans_salesTransactionsStaging : db_salesTransactions;
    result = await db.rawUpdate('''
    UPDATE $tableName 
    SET $db_product_rowStatus = ${2},
    $db_salesTrans_salesTransactionSync = ${0}
    WHERE $db_salesTrans_fkSalesId = ?
    ''', [id]);
    return result;
  }

  Future<int> updateSalesTransactionRevert(String? id, {String? salesType}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    var tableName = salesType == 'staging' ? db_salesTrans_salesTransactionsStaging : db_salesTransactions;
    result = await db.rawUpdate('''
    UPDATE $tableName 
    SET $db_product_rowStatus = ${1},
    $db_salesTrans_salesTransactionSync = ${0}
    WHERE $db_salesTrans_fkSalesId = ?
    ''', [id]);
    return result;
  }

  Future<int> updateSalesTransactionSync(String id, int rowStatus) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_salesTrans_salesTransactionSync = ${0}, $db_product_rowStatus = ${rowStatus}
    WHERE  $db_salesTrans_transactId = ?
    ''', [id]);
    return result;
  }

  Future<int> updateActiveTransactions(String saleID, int isActive, int sync) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_sales_isActiveSale = ?, $db_salesTrans_salesTransactionSync = ?
    WHERE $db_salesTrans_fkSalesId=? AND $db_product_rowStatus < 2
    ''', [isActive, sync, saleID]);
    return result;
  }

  Future<int> updateTransactionsTable(Transactions model, {String? transactionType}) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (transactionType == "staging") {
      result = await db.rawUpdate('''
    UPDATE  $db_salesTrans_salesTransactionsStaging 
    SET $db_salesTrans_salesProductName = ?, $db_salesTrans_quantity = ? ,$db_salesTrans_price = ?, $db_salesTrans_actualPrice = ?,$db_salesTrans_amount = ?, $db_salesTrans_salesTransactionSync = ?, $db_salesTrans_salesCGst=?, $db_salesTrans_salesCGstAmount=?, $db_salesTrans_salesSGst=?, $db_salesTrans_salesSGstAmount=?, $db_salesTrans_amountWithoutGst=?, $db_product_rowStatus=?, $db_salesTrans_productType=?,$db_toppings_toppingId=?, $db_sales_isActiveSale = ?, $db_sales_discountFormula = ?, $db_sales_discountPrice = ?, $db_salesTrans_notes = ?
    WHERE  $db_salesTrans_transactId = ? AND $db_salesTrans_salesProductId=?
    ''', [
        model.name,
        model.qty,
        model.price,
        model.actualPrice,
        model.amount,
        model.sync,
        model.cGst,
        model.cGstAmount,
        model.sGst,
        model.sGstAmount,
        model.amountWithoutGst,
        model.rowStatus,
        model.productType,
        model.toppingId,
        model.isActiveSale,
        model.discountFormula,
        model.discountPrice,
        model.notes,
        model.transactId,
        model.prodId
      ]);
    } else {
      result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_salesTrans_salesProductName = ?, $db_salesTrans_quantity = ? ,$db_salesTrans_price = ?, $db_salesTrans_actualPrice = ?,$db_salesTrans_amount = ?, $db_salesTrans_salesTransactionSync = ?, $db_salesTrans_salesCGst=?, $db_salesTrans_salesCGstAmount=?, $db_salesTrans_salesSGst=?, $db_salesTrans_salesSGstAmount=?, $db_salesTrans_amountWithoutGst=?, $db_product_rowStatus=?, $db_salesTrans_productType=?,$db_toppings_toppingId=?, $db_sales_isActiveSale = ?, $db_sales_discountFormula = ?, $db_sales_discountPrice = ?, $db_salesTrans_isIGST = ?, $db_salesTrans_notes = ?
    WHERE  $db_salesTrans_transactId = ? AND $db_salesTrans_salesProductId=?
    ''', [
        model.name,
        model.qty,
        model.price,
        model.actualPrice,
        model.amount,
        model.sync,
        model.cGst,
        model.cGstAmount,
        model.sGst,
        model.sGstAmount,
        model.amountWithoutGst,
        model.rowStatus,
        model.productType,
        model.toppingId,
        model.isActiveSale,
        model.discountFormula,
        model.discountPrice,
        model.isIGST ?? false ? 1 : 0,
        model.notes,
        model.transactId,
        model.prodId,
      ]);
    }

    return result;
  }

  Future<int> updateProductsBasedSalesIdAndProductId(Transactions saleItem, String? fullTopping) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    if (fullTopping == null) {
      result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_salesTrans_salesTransactionSync ='${0}',$db_product_rowStatus='${2}'
    WHERE $db_salesTrans_salesProductId = ? AND $db_salesTrans_fkSalesId = ? AND $db_salesTrans_price = ?
    ''', [saleItem.prodId, saleItem.salesId, saleItem.price]);
    } else {
      result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_salesTrans_salesTransactionSync ='${0}',$db_product_rowStatus='${2}'
    WHERE $db_salesTrans_salesProductId = ? AND $db_salesTrans_fkSalesId = ?
    ''', [saleItem.prodId, saleItem.salesId]);
    }
    return result;
  }

  Future<int> updateProductsBasedSalesIdAndProductIdAndTopping(Transactions saleItem) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_salesTransactions 
    SET $db_salesTrans_salesTransactionSync ='${0}',$db_product_rowStatus='${2}'
    WHERE $db_salesTrans_salesProductId = ? AND $db_salesTrans_fkSalesId=? AND$db_toppings_toppingId=?
    ''', [saleItem.prodId, saleItem.salesId, saleItem.toppingId]);
    return result;
  }

  Future<int> deleteProductsBasedSalesIdAndProductId(Transactions saleItem, String? fullTopping) async {
    Database db = await DatabaseHelper.instance.database;
    if (fullTopping == null) {
      return await db.delete(db_salesTransactions,
          where: '$db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ? AND $db_salesTrans_price = ?',
          whereArgs: [saleItem.salesId, saleItem.prodId, saleItem.price]);
    } else {
      return await db.delete(db_salesTransactions,
          where: '$db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ?', whereArgs: [saleItem.salesId, saleItem.prodId]);
    }
  }

  Future<int> deleteProductsBasedSalesIdAndProductIdAndToppingId(Transactions saleItem) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_salesTransactions,
        where: '$db_salesTrans_fkSalesId = ? AND $db_salesTrans_salesProductId = ? AND$db_toppings_toppingId = ? AND $db_salesTrans_price = ?',
        whereArgs: [saleItem.salesId, saleItem.prodId, saleItem.toppingId, saleItem.price]);
  }
}
