// ignore_for_file: unnecessary_brace_in_string_interps

import 'package:smartbill/model/area.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';


class AreaDBScript {
  Future<int> insertArea(Area model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_areas, {
      db_area_areaID: model.areaID,
     db_area_areaName: model.areaName,
      db_common_createdDate: model.createdDate,
      db_product_rowStatus: model.rowStatus,
      db_common_syncStatus: model.syncStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllArea() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_areas} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_area_ID }");
    return result;
  }

  Future<int> updateArea(Area model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_areas} 
    SET ${db_area_areaName} = ?, ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_area_areaID} = ?
    ''', [
      model.areaName,
      model.rowStatus,
      model.syncStatus,
      model.workspaceID,
      model.areaID
    ]);
    return result;
  }

  Future<int> deleteWithUpdateArea(Area model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_areas} 
    SET ${db_product_rowStatus} = ?, ${db_common_syncStatus} = ? WHERE ${db_workspace_workspaceId}= ? AND ${db_area_areaID} = ?
    ''', [model.rowStatus, model.syncStatus, model.workspaceID, model.areaID]);
    return result;
  }

  Future<List<Map<String, dynamic>>> nonSyncAreaData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_areas} WHERE ${db_common_syncStatus} = ${0} AND ${db_workspace_workspaceId} = ?",
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> getAllAreasID() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT ${db_area_areaID} FROM ${db_areas} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<Map<String, dynamic>?> getAreaNameByID(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_areas} WHERE ${db_workspace_workspaceId}='${MyApp.activeWorkspace.workspaceId}' AND ${db_area_areaID}='${id}'");
    if (result.isEmpty) {
      return null;
    }
    return result.first;
  }

  Future<bool> checkAreaExists(String areaID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db.rawQuery(
        "SELECT COUNT(${db_customer_fK_areaID}) FROM ${db_customer} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId}' AND ${db_customer_fK_areaID} = '$areaID'  AND ${db_product_rowStatus} < 2"));
    if (result != 0) {
      return false;
    } else {
      return true;
    }
  }
}
