import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/products.dart';

class PriceTypeDBScript {
  //PRODUCTS  UNIT DETAILS INSERT
  Future<int> insertUnits(PriceUnitType model) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(db_priceTypes, {
      db_priceTypes_priceTypeId: model.priceTypeId,
      db_product_productId: model.productId,
      db_priceTypes_measurement: model.measurement,
      db_priceTypes_measurementPrice: model.measurementPrice,
      db_workspace_workspaceId: model.workspaceId,
      db_priceTypes_measurementSync: model.measurementSync,
      db_product_rowStatus: model.rowStatus,
    });
  }

  Future<List<Map<String, dynamic>>> queryGetAllForUnit(String product) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_priceTypes WHERE $db_product_productId=? AND $db_workspace_workspaceId=? AND $db_product_rowStatus<2',
        [product, MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetProductsUnitId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_priceTypes WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncProductsUnitData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_priceTypes WHERE $db_priceTypes_measurementSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int> updateUnitTable(PriceUnitType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_priceTypes 
    SET $db_priceTypes_measurement = ?,$db_priceTypes_measurementPrice = ?,$db_priceTypes_measurementSync = ?,$db_product_rowStatus = ?
    WHERE $db_priceTypes_priceTypeId = ?
    ''', [
      model.measurement,
      model.measurementPrice,
      model.measurementSync,
      model.rowStatus,
      model.priceTypeId
    ]);
    return result;
  }
}
