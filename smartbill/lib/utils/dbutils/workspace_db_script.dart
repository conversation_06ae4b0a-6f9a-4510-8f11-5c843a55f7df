import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../model/workspace.dart';
import 'database_helper.dart';

class WorkspaceDBScript {
  Future<int> insertWorkspaceDetails(WorkSpace reports) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_workspace, {
      db_workspace_workspaceId: reports.workspaceId,
      db_workspace_workspaceName: reports.workspaceName,
      db_workspace_userName: reports.userName,
      db_workspace_isActive: reports.isActive ?? false ? 1 : 0,
      db_workspace_expiryDate: reports.expiryDate.toString(),
      db_workspace_roleId : reports.roleId
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetWorkspace() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery('SELECT * FROM $db_workspace');
    return result;
  }

  Future<int> updateExpiryDate(date, id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_workspace 
    SET $db_workspace_expiryDate = ?
    WHERE $db_workspace_workspaceId = ?
    ''', [date, id]);
    return result;
  }

  Future<int> updateWorkspaceName(WorkSpace model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_workspace 
    SET $db_workspace_workspaceName = ? WHERE $db_workspace_workspaceId = ?
    ''', [model.workspaceName, model.workspaceId]);
    return result;
  }

  Future<int> deleteSignedWorkspaceOnId(String? id) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_workspace, where: '$db_workspace_workspaceId = ?', whereArgs: [id]);
  }
}
