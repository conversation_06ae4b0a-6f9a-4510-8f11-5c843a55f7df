// ignore_for_file: unused_local_variable

import 'package:smartbill/model/payment_type.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/invoice_prefix_model.dart';
import 'package:smartbill/model/products.dart';
import 'package:smartbill/model/shop_details.dart';
import 'package:smartbill/model/permissions.dart';
import 'package:smartbill/model/area.dart';
import 'package:smartbill/screens/masters/online_platforms/online_platform_model.dart';
import 'package:smartbill/utils/dbutils/common_db_script.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import 'package:smartbill/utils/dbutils/area_db_script.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:smartbill/utils/dbutils/invoice_prefix_db_script.dart';
import 'package:smartbill/utils/dbutils/online_platform_db_script.dart';
import 'package:smartbill/utils/dbutils/payment_details_db_script.dart';
import 'package:smartbill/utils/dbutils/payment_type_db_script.dart';
import 'package:smartbill/utils/dbutils/sales_details_db_script.dart';
import 'package:smartbill/utils/dbutils/sales_hold_sales_db_script.dart';
import 'package:smartbill/utils/dbutils/sales_trans_db_script.dart';
import 'package:smartbill/utils/dbutils/shop_details_db_script.dart';
import 'package:smartbill/utils/dbutils/user_permission_db_script.dart';
import 'package:smartbill/utils/dbutils/workspace_db_script.dart';
import '../../main.dart';
import 'database_helper.dart';
import 'package:uuid/uuid.dart';
import 'package:smartbill/model/reports.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/utils/common_function/list_class.dart';
import 'package:smartbill/model/payment.dart';
import 'product_details_db_script.dart';

class DBOperations {
  final dbHelper = DatabaseHelper.instance;
  FnUtilities fnUtilities = new FnUtilities();
  CommonDB commonDB = CommonDB();
  AreaDBScript areaDB = AreaDBScript();
  ShopDetailsDBScript shopDetailsDBScript = ShopDetailsDBScript();
  OnlinePlatformDBScript onlinePlatformDBScript = OnlinePlatformDBScript();
  ProductDetailsDBScript productDetailsDBScript = ProductDetailsDBScript();
  PaymentDetailsDBScript paymentDetailsDBScript = PaymentDetailsDBScript();
  WorkspaceDBScript workspaceDBScript = WorkspaceDBScript();
  InvoicePrefixDBScript invoicePrefixDBScript = InvoicePrefixDBScript();
  UserPermissionsDBScript userPermissionsDBScript = UserPermissionsDBScript();
  PaymentTypeDBScript paymentTypeDBScript = PaymentTypeDBScript();
  SalesDetailsDBScript salesDetailsDBScript = SalesDetailsDBScript();
  SalesTransDBScript salesTransDBScript = SalesTransDBScript();
  SalesHoldSalesDetailsDBScript salesHoldSalesDetailsDBScript = SalesHoldSalesDetailsDBScript();

  newSalesInsert(List<Transactions> newTransactionsList) async {
    var uuid = new Uuid();
    String salesId = uuid.v4().toString();
    //insertSalesId = salesId;
    String totalAmount = getTotalAmountFromSalesList(newTransactionsList);
    Map<String, dynamic> row1 = {
      db_sales_salesId: salesId,
      db_sales_salesDate: DateTime.now().toString(),
      db_sales_salesTotalAmount: totalAmount,
      db_sales_salesSync: 0,
      db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
      db_product_rowStatus: 0,
    };
    Sales reports = Sales.fromMap(row1);
    final id2 = await salesDetailsDBScript.insertSalesDetails(reports);
  }

  oldSalesInsert(Sales sales) async {
    Map<String, dynamic> row1 = {
      db_sales_salesId: sales.salesId,
      db_sales_salesDate: sales.date,
      db_sales_salesTotalAmount: sales.totalAmount,
      db_sales_salesSync: 1,
      db_workspace_workspaceId: MyApp.activeWorkspace.workspaceId,
      db_product_rowStatus: 1,
      db_sales_discountFormula: sales.discountFormula,
      db_sales_discountPrice: sales.discountPrice
    };
    Sales reports = Sales.fromMap(row1);
    final id2 = await salesDetailsDBScript.insertSalesDetails(reports);
  }

  String getTotalAmountFromSalesList(List<Transactions> salesList) {
    double total = 0.0;
    for (Transactions t in salesList) {
      total += double.parse(t.amount ?? "0");
    }
    return total.toString();
  }

  oldSalesUpdate(Sales sales1) async {
    Sales sales = Sales(salesId: sales1.salesId, date: sales1.date, totalAmount: sales1.totalAmount, sync: 1);
    var affectedRow = await salesDetailsDBScript.updateSalesTable(sales);
  }

  oldTransactionsUpdate(Transactions transactions) async {
    Transactions transact = Transactions(
        id: transactions.id,
        transactId: transactions.transactId,
        salesId: transactions.salesId,
        prodId: transactions.prodId,
        productType: transactions.productType,
        toppingId: transactions.toppingId,
        name: transactions.name,
        qty: transactions.qty,
        price: transactions.price,
        cGst: transactions.cGst,
        cGstAmount: transactions.cGstAmount,
        sGst: transactions.sGst,
        sGstAmount: transactions.sGstAmount,
        amount: transactions.amount,
        sync: 1,
        isActiveSale: transactions.isActiveSale);
    int affectedRow = await salesTransDBScript.updateTransactionsTable(transact);
    // printerUtility.showToastmessage('Successfully updated');
  }

  // tempinsertValue(Prefix prefix) {
  //   prefix.prefix = "A";
  //   prefix.userName = MyApp.activeUser.username;
  //   prefix.workspaceId = MyApp.activeWorkspace.workspaceId;
  //   insertInvoicePrefix(prefix);
  // }

  //INVOICE PREFIX INSERT
  insertInvoicePrefix(Prefix prefix) async {
    final id = await invoicePrefixDBScript.insertInvoicePrefix(prefix);
  }

//INVOICE PREFIX SELECT FUNCTION

  Future<String> queryPrefixName() async {
    String prefixNameValue = await invoicePrefixDBScript.queryAllGetInvoicePrefix(MyApp.activeWorkspace.workspaceId, MyApp.activeUser.username);
    return prefixNameValue;
  }

  Future<List<Transactions>> queryAllTransactions() async {
    final allRows = await salesTransDBScript.queryAllRowsSalesTransactions();
    List<Transactions> transactList = [];
    allRows.forEach((row) => transactList.add(Transactions.fromMap(row)));
    return transactList;
  }

  Future<List<Sales>> queryAllSales() async {
    final allRows = await salesDetailsDBScript.queryallRowsSalesDetails();
    List<Sales> reportList = [];
    allRows.forEach((row) => reportList.add(Sales.fromMap(row)));
    return reportList;
  }

  void queryAllProductDetails() async {
    final allRows = await productDetailsDBScript.queryallRowsProductDeatails();
    ListUtility.productList.clear();
    allRows.forEach((row) => ListUtility.productList.add(Products.fromMap(row)));
  }

  Future<List> queryAllProductID() async {
    final allRows = await productDetailsDBScript.queryallRowsProductDeatails();
    var productIdList = [];
    allRows.forEach((row) => productIdList.add(row["productId"]));
    return productIdList;
  }

  Future<List<String>> queryAllSalesId() async {
    List<String> salesIdList = [];
    final allRows = await salesDetailsDBScript.queryallRowsSalesDetails();
    allRows.forEach((row) => salesIdList.add(row["salesId"]));
    return salesIdList;
  }

  Future<List<String>> queryAllTransactId() async {
    List<String> transactIdList = [];
    final allRows = await salesTransDBScript.queryAllRowsSalesTransactions();
    allRows.forEach((row) => transactIdList.add(row["transactId"]));
    return transactIdList;
  }

  oldInsertProducts(Products products) async {
    products.sync = 1;
    products.rowStatus = products.isActive ?? false ? 0 : 2;
    products.workspaceId = MyApp.activeWorkspace.workspaceId;
    products.productDate = DateTime.now().toString();
    final id1 = await productDetailsDBScript.insertProductDetails(products);
  }

  oldUpdateProductsDetails(Products product) async {
    Products products = Products(
        productId: product.productId,
        name: product.name,
        toppingGroupId: product.toppingGroupId,
        price: product.price,
        cGst: product.cGst,
        sGst: product.sGst,
        mrp: product.mrp,
        productKeyName: product.productKeyName,
        priceType: product.priceType,
        dynamicQuantity: product.dynamicQuantity,
        image: product.image,
        sync: 1,
        keyID: product.keyID);
    final affectedRow = await productDetailsDBScript.updateProductsTable(products);
  }

  //---------SYNC FROM API------------------//

  Future<List<Payment>> queryAllPaymentDetailsBasedOnActivated() async {
    final allRows = await paymentDetailsDBScript.queryAllGetPaymentsBasedOnActivateId();
    List<Payment> paymentList = [];
    allRows.forEach((row) => paymentList.add(Payment.fromMap(row)));
    return paymentList;
  }

  /*---------HOLD TRANSACTIONS QUERY-------------*/

  Future<List<Transactions>> queryAllHoldTransactions() async {
    final allRows = await salesHoldSalesDetailsDBScript.queryAllRowsHoldSalesTransactions();
    List<Transactions> transactList = [];
    allRows.forEach((row) => transactList.add(Transactions.fromMap(row)));
    return transactList;
  }

  Future<List<Sales>> queryAllHoldReports() async {
    final allRows = await salesHoldSalesDetailsDBScript.queryallRowsHoldSalesDetails();
    List<Sales> holdSalesList = [];
    allRows.forEach((row) => holdSalesList.add(Sales.fromMap(row)));
    return holdSalesList;
  }

  Future<List<Transactions>> queryGetHoldSalesDetails(id) async {
    final allRows = await salesHoldSalesDetailsDBScript.queryAllHoldTransactionsBasedOnSalesId(id);
    List<Transactions> transactList = [];
    allRows.forEach((row) => transactList.add(Transactions.fromMap(row)));
    return transactList;
  }

  deleteHoldSales(id) async {
    DBFuctionsScript dbScript = DBFuctionsScript();
    await dbScript.deleteFunction(db_salesTrans_holdSalesTransactions, db_salesTrans_fkSalesId, id);
    await dbScript.deleteFunction(db_sales_holdSalesDetails, db_sales_salesId, id);
  }

   deleteAllHoldSales() async {
    // Assuming that the number of rows is the id for the last row.
    DBFuctionsScript dbScript = DBFuctionsScript();
    await dbScript.deleteTable(db_sales_holdSalesDetails);
  }

  insertWorkspace(WorkSpace model) async {
    // Map<String, dynamic> row = {
    //   db_workspace_workspaceId: model.workspaceId,
    //   db_workspace_workspaceName: model.workspaceName,
    //   db_workspace_userName: model.userName,
    //   db_workspace_isActive: model.isActive,
    //   db_workspace_expiryDate: model.expiryDate,
    //   db_workspace_roleId: model.roleId,
    // };
    // WorkSpace space = WorkSpace.fromMap(row);
    final id1 = await workspaceDBScript.insertWorkspaceDetails(model);
  }

  //GET WORKSPACE DETAILS
  queryAllGetWorkspace() async {
    final allRows = await workspaceDBScript.queryAllGetWorkspace();
    ListUtility.signedWorkspaceList.clear();
    allRows.forEach((row) => ListUtility.signedWorkspaceList.add(WorkSpace.fromMap(row)));
  }

  // getPermissionDetails(String roleID) async {
  //   String current = fnUtilities.workSpaceUserSetValue(key_roleId);
  //   if ((current == null && roleID != null) || (roleID != current)) {
  //     await getUserPermissions(roleID).then((value) async {
  //       if (value.status == 1) {
  //         await insertPermissionsDetails(value.result, MyApp.activeUser.username, MyApp.activeWorkspace.workspaceId);
  //         await queryAllGetsPermissionDetails();
  //       }
  //     });
  //   }
  // }

  //PERMISSIONS DETAILS INSERT FUNCTION
  // insertPermissionsDetails(List<Permissions> permissionList, String username, String workspaceId) async {
  //   // WorkspaceSettings settings = new WorkspaceSettings();
  //   // settings.settingKey = "RoleId";
  //   // settings.settingValue = permissionList?.first?.roleId?.toString();
  //   // settings.sync = 1;
  //   // await fnUtilities.updateSettingDetails(settings, tableType: "WorkSpaceUserSettings");
  //   if(permissionList != null){
  //     for (Permissions permission in permissionList) {
  //     permission.userName = username;
  //     permission.workspaceId = workspaceId;
  //     await permissionInsertOrUpdate(permission);
  //   }
  //   }
  // }

  // //GET PERMISSIONS DETAILS FUNCTION
  // queryAllGetsPermissionDetails() async {
  //   final allRows = await userPermissionsDBScript.queryAllGetPermissions();
  //   ListUtility.permissionList.clear();
  //   allRows.forEach((row) => ListUtility.permissionList.add(Permissions.fromMap(row)));
  // }

  // //RETURN THE PERMISSION VALUE BASED ON PERMISSION NAME
  // returnPermission(keyname) {
  //   for (Permissions p in ListUtility.permissionList) {
  //     if (p.keyName == keyname) {
  //       return p.permissionLevelId;
  //     }
  //   }
  //   return null;
  // }



   queryAllShopDeatails() async {
    final allRows = await shopDetailsDBScript.queryallRowsShopDetails();
    ListUtility.shopDetailsList.clear();
    allRows.forEach((row) => ListUtility.shopDetailsList.add(ShopDeatails.fromMap(row)));
  }

  //DELETE SALES BEFORE GIVEN DAYS BASED ON WORKSPACE
  deleteDayBeforeRecords() async {
    await salesDetailsDBScript.deleteAllBefOneDayRecords(MyApp.activeWorkspace.workspaceId, 2);
  }

  getAllPaymentTypeFromDB() async {
    List<PaymentType> paymentTypeList = [];
    final allRows = await paymentTypeDBScript.selectAllPaymentType();
    paymentTypeList.clear();
    for (var row in allRows) {
      paymentTypeList.add(PaymentType.fromMap(row));
    }
    return paymentTypeList;
  }

  /// get all online platforms
  getAllOnlinePlatformsFromDB() async {
    List<OnlinePlatformModel> onlinePlatformList = [];
    final allRows = await onlinePlatformDBScript.selectAllOnlinePlatform();
    onlinePlatformList.clear();
    for (var row in allRows) {
      onlinePlatformList.add(OnlinePlatformModel.fromJsonDB(row));
    }
    return onlinePlatformList;
  }

  getAllAreaFromDB() async {
    List<Area> areaList = [];
    final allRows = await areaDB.selectAllArea();
    areaList.clear();
    for (var row in allRows) {
      areaList.add(Area.fromMap(row));
    }
    return areaList;
  }

  getAllTransactionDetailsBySalesID(id) async {
    List<Transactions> transactionList = [];
    final allRows = await salesTransDBScript.queryAllRowsBasedOnSalesId(id, tableName: null);
    allRows.forEach((row) => transactionList.add(Transactions.fromMap(row)));
    return transactionList;
  }

  //GET ALL SALES BY SALES ID
  Future<List> queryAllTransactIdBySalesId(salesId) async {
    final allRows = await salesTransDBScript.queryAllTransactIdBasedOnSalesId(salesId);
    var transactId = [];
    allRows.forEach((row) => transactId.add(row["transactId"]));
    return transactId;
  }

  selectSalesCustomerByID(String customerID) async {
    final allRows = await salesDetailsDBScript.queryRowsByCustomerID(customerID);
    List<Sales> salesList = [];
    allRows.forEach((row) => salesList.add(Sales.fromMap(row)));
    return salesList;
  }

  permissionInsertOrUpdate(Permissions permission) async {
    int count = await userPermissionsDBScript.checkPermission(permission);
    if (count == 0) {
      await userPermissionsDBScript.insertPermissionDetails(permission);
    } else {
      await userPermissionsDBScript.updatePermissions(permission);
    }
  }
}
