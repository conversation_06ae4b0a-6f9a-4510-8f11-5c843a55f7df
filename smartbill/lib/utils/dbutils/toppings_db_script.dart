import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/toppings_model.dart';

class ToppingsDBScript {
  Future<int> insertToppingsDetails(Topping model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_toppings, {
      db_toppings_toppingId: model.toppingId,
      db_toppingGroups_toppingGroupId: model.toppingGroupId,
      db_toppings_toppingName: model.toppingName,
      db_toppings_toppingPrice: model.toppingPrice,
      db_workspace_workspaceId: model.workspaceId,
      db_toppings_toppingSync: model.toppingSync,
      db_product_rowStatus: model.rowStatus,
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryGetAllForToppings(
      String toppingId) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM $db_toppings WHERE $db_toppingGroups_toppingGroupId=? AND $db_workspace_workspaceId=? AND $db_product_rowStatus<2',
        [toppingId, MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetToppings() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_toppings WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncToppingsData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT * FROM$db_toppings WHERE $db_toppings_toppingSync=${0} AND $db_workspace_workspaceId=?',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int> updateProductsToppings(Topping model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_toppings 
    SET $db_toppingGroups_toppingGroupId = ? ,$db_toppings_toppingName = ? ,$db_toppings_toppingPrice=? ,$db_product_rowStatus=?, $db_workspace_workspaceId=?, $db_toppings_toppingSync=?
    WHERE $db_toppings_toppingId = ?
    ''', [
      model.toppingGroupId,
      model.toppingName,
      model.toppingPrice,
      model.rowStatus,
      model.workspaceId,
      model.toppingSync,
      model.toppingId
    ]);
    return result;
  }

  Future<int> deleteUpdateProductsToppings(Topping model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_toppings 
    SET $db_product_rowStatus = ?, $db_toppings_toppingSync = ?
    WHERE $db_toppings_toppingId = ?
    ''', [model.rowStatus, model.toppingSync, model.toppingId]);
    return result;
  }
}
