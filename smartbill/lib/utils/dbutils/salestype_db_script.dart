// ignore_for_file: unnecessary_brace_in_string_interps
import 'package:smartbill/model/sales_type.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import 'database_helper.dart';

class SalesTypeDBScript {
  Future<int> insertSalesType(SalesType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_salesType, {
      db_salesType_salesTypeID: model.salesTypeID,
      db_salesType_FK_SalesID: model.salesID,
      db_salesType_Type: model.salesType,
      db_salesType_Amount: model.amount,
      db_salesType_FK_CustomerID: model.customerID,
      db_salesType_FK_PaymentTypeID: model.paymentTypeID,
      db_common_createdDate: model.createdDate,
      db_common_syncStatus: model.syncStatus,
      db_product_rowStatus: model.rowStatus,
      db_workspace_workspaceId: model.workspaceID
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> selectAllSalesType() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT * FROM ${db_salesType} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}' AND ${db_product_rowStatus} < 2 ORDER BY ${db_salesType_ID}");
    return result;
  }

  Future<List<Map<String, dynamic>>?> selectAllSalesByCustomerID(String? customerID) async {
    if (customerID != null) {
      Database db = await DatabaseHelper.instance.database;
      var result = await db.rawQuery(
          "SELECT * FROM ${db_salesType} WHERE ${db_workspace_workspaceId} = '${MyApp.activeWorkspace.workspaceId.toString()}'AND ${db_product_rowStatus} < 2 AND ${db_sales_fK_CustomerID} = '$customerID' ORDER BY DATE(${db_paymentTypes_createdDate})");
      return result;
    }
  }

  Future<int> updateSalesType(SalesType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesType} 
    SET ${db_salesType_Amount} = ?, ${db_salesType_FK_CustomerID} = ?, ${db_salesType_FK_PaymentTypeID} = ?, ${db_common_createdDate} = ?, ${db_common_syncStatus} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesType_FK_SalesID} = ? AND ${db_salesType_Type} = ?
    ''', [
      model.amount,
      model.customerID,
      model.paymentTypeID,
      model.createdDate,
      model.syncStatus,
      model.rowStatus,
      model.workspaceID,
      model.salesID,
      model.salesType
    ]);
    return result;
  }

  Future<int> updateSalesTypeSync(SalesType model) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesType} 
    SET ${db_salesType_Amount} = ?, ${db_salesType_FK_CustomerID} = ?, ${db_salesType_FK_PaymentTypeID} = ?, ${db_common_createdDate} = ?, ${db_common_syncStatus} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesType_salesTypeID} = ?
    ''', [
      model.amount,
      model.customerID,
      model.paymentTypeID,
      model.createdDate,
      model.syncStatus,
      model.rowStatus,
      model.workspaceID,
      model.salesTypeID
    ]);
    return result;
  }

  Future<int> updateSalesTypeForDeleteBySalesID(String? salesID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesType} 
    SET ${db_common_syncStatus} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesType_FK_SalesID} = ? AND ${db_salesType_Type} = ?
    ''', [0, 2, MyApp.activeWorkspace.workspaceId, salesID, "debit"]);
    return result;
  }

  Future<int> updateSalesTypeForRevertBySalesID(String? salesID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE ${db_salesType} 
    SET ${db_common_syncStatus} = ?, ${db_product_rowStatus} = ? WHERE ${db_workspace_workspaceId} = ? AND ${db_salesType_FK_SalesID} = ? AND ${db_salesType_Type} = ?
    ''', [0, 1, MyApp.activeWorkspace.workspaceId, salesID, "debit"]);
    return result;
  }

  Future<int> permanentDeleteForSalesTypeDebit(String saleID) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.delete(db_salesType, where: '${db_salesType_FK_SalesID} = ? AND ${db_salesType_Type} = ?', whereArgs: [saleID, "debit"]);
  }
}
