import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/payment.dart';

class PaymentDetailsDBScript {
  ///Insert Payment
  Future<int> insertPayment(Payment payment) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.insert(db_paymentDetails, {
      db_payment_paymentId: payment.paymentId,
      db_payment_paymentName: payment.paymentName,
      db_payment_paymentNetworkImage: payment.paymentNetworkImage,
      db_payment_paymentImage: payment.paymentImage,
      db_payment_paymentIsActive: payment.paymentIsActive,
      db_workspace_workspaceId: payment.workspaceId,
    });
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsPaymentDeatails() async {
    Database db = await DatabaseHelper.instance.database;
    return await db.query(db_paymentDetails,
        where: '$db_workspace_workspaceId=?',
        whereArgs: [MyApp.activeWorkspace.workspaceId]);
  }

  Future<List<Map<String, dynamic>>> queryAllGetPaymentsId(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_payment_paymentId FROM $db_paymentDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetPaymentsBasedOnId(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_paymentDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_payment_paymentId='$id'");
    return result;
  }

  Future<String?> queryGetPaymentNameBasedOnId(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_payment_paymentName FROM $db_paymentDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_payment_paymentId='$id'");
    if (result.length > 0) {
      String paymentName = result.first.values.first.toString();
      return paymentName;
    }
  }

  Future<List<Map<String, dynamic>>>
      queryAllGetPaymentsBasedOnActivateId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_paymentDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_payment_paymentIsActive='${1}'");
    return result;
  }

  Future<int> updatePaymentTable(Payment payment) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_paymentDetails 
    SET $db_payment_paymentName = ?,$db_payment_paymentNetworkImage=?,$db_payment_paymentImage = ?
    WHERE $db_payment_paymentId = ?
    ''', [
      payment.paymentName,
      payment.paymentNetworkImage,
      payment.paymentImage,
      payment.paymentId
    ]);
    return result;
  }

  Future<int> updatePaymentActive(id, activeNo) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_paymentDetails 
    SET $db_payment_paymentIsActive = ?
    WHERE $db_payment_paymentId = ?
    ''', [activeNo, id]);
    return result;
  }
}
