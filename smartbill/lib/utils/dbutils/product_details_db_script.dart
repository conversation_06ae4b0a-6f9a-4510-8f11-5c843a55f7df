import 'package:smartbill/Repositories/image_repository.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import '../../main.dart';
import '../../model/products.dart';
import '../../screens/products/add_product_page.dart';

class ProductDetailsDBScript {
  //PRODUCTS  DETAILS INSERT
  Future<int> insertProductDetails(Products products) async {
    Database db = await DatabaseHelper.instance.database;
    return await db.insert(db_productDetails, {
      db_product_productId: products.productId,
      db_product_productSaleId: products.productSaleId,
      db_product_productName: products.name,
      db_product_productPrice: products.price,
      db_product_productMrp: products.mrp,
      db_product_productKeyName: products.productKeyName,
      db_toppingGroups_toppingGroupId: products.toppingGroupId,
      db_product_priceType: products.priceType,
      db_product_productCgst: products.cGst,
      db_product_productSgst: products.sGst,
      db_product_productImage: products.image,
      db_product_productSync: products.sync,
      db_workspace_workspaceId: products.workspaceId,
      db_product_productDate: products.productDate,
      db_product_rowStatus: products.rowStatus,
      db_product_dynamicQuantity: products.dynamicQuantity,
      // db_product_enableInventory: products.enableInventory,
      db_product_productKeywordID: products.keyID,
      db_product_parcelAmount: products.parcelAmount,
      db_product_productFor: products.productFor != null ? productForToString(products.productFor) : null,
    });
  }

  ProductFor? productForFromString(String value) {
    switch (value) {
      case 'purchase':
        return ProductFor.purchase;
      case 'sale':
        return ProductFor.sale;
      case 'both':
        return ProductFor.both;
      default:
        return null;
    }
  }

  productForToString(ProductFor? value) {
    if(value != null){
      return value.toString().split('.').last;
    } 
  }

  Future<Map<String, dynamic>?> getProductDetailsByID(String ProductID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_productDetails WHERE $db_product_productId = ? AND $db_workspace_workspaceId = ? AND $db_product_rowStatus < 2',
        [ProductID, MyApp.activeWorkspace.workspaceId]);
    return result.first;
  }

  Future<List<Map<String, dynamic>>> queryallFavoriteProducts() async {
    Database db = await DatabaseHelper.instance.database;
    var sqlQuery;
    sqlQuery =
        "SELECT P.*, Q.QtyCount FROM $db_productDetails P LEFT JOIN (SELECT $db_salesTrans_salesProductId,COUNT($db_salesTrans_quantity) as QtyCount FROM $db_salesTransactions WHERE $db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' GROUP BY $db_salesTrans_salesProductId) Q ON Q.$db_salesTrans_salesProductId = P.$db_product_productId WHERE P.$db_workspace_workspaceId = '${MyApp.activeWorkspace.workspaceId}' AND P.$db_product_rowStatus < 2 ORDER BY Q.QtyCount DESC";
    var result = db.rawQuery(sqlQuery);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsProductDeatails() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_productDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_rowStatus < 2 ORDER BY $db_product_productSaleId");
    // return await db.query(ProductDetails,
    //     where: '$db_workspace_workspaceId=? and $db_product_rowStatus<2',
    //     whereArgs: [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllRowsProductDetailsByCategory(String categoryID) async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT * FROM $db_productDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_productKeywordID = '$categoryID' AND $db_product_rowStatus < 2 ORDER BY $db_product_productSaleId");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsProductKey() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery(
        "SELECT $db_product_productKeyName FROM $db_productDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' AND $db_product_productKeyName != '' AND $db_product_rowStatus < 2 GROUP BY $db_product_productKeyName");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetProductsId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = db.rawQuery("SELECT $db_product_productId FROM $db_productDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  Future<List<Map<String, dynamic>>> queryAllGetProductsImageId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        'SELECT $db_product_productImage FROM $db_productDetails WHERE $db_workspace_workspaceId=? AND $db_product_rowStatus<2 AND $db_product_productSync = ${0}',
        [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<int?> queryGetProductSyncNo(id) async {
    Database db = await DatabaseHelper.instance.database;
    var result = Sqflite.firstIntValue(await db
        .rawQuery('SELECT $db_product_productSync FROM $db_productDetails WHERE $db_product_productId=? AND $db_workspace_workspaceId=?', [id, MyApp.activeWorkspace.workspaceId]));
    return result;
  }

  Future<List<Map<String, dynamic>>> queryForNonSyncProductsData() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery('SELECT * FROM $db_productDetails WHERE $db_product_productSync=${0} AND $db_workspace_workspaceId=?', [MyApp.activeWorkspace.workspaceId]);
    return result;
  }

  Future<List<Map<String, dynamic>>> queryallRowsProductDeatailsByName(name) async {
    var res;
    Database db = await DatabaseHelper.instance.database;
    if (name == "") {
      res = await db.rawQuery("SELECT * FROM $db_productDetails WHERE $db_workspace_workspaceId=? AND $db_product_rowStatus<2", [MyApp.activeWorkspace.workspaceId]);
    } else {
      res = await db.rawQuery("SELECT * FROM $db_productDetails WHERE $db_product_productName LIKE '%$name%' AND $db_workspace_workspaceId=? AND $db_product_rowStatus<2",
          [MyApp.activeWorkspace.workspaceId]);
    }

    return res;
  }

  queryGetMaxProductSaleId() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery(
        "SELECT $db_product_productSaleId FROM $db_productDetails WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}' ORDER BY $db_product_productSaleId DESC LIMIT 1");
    int productSalesId = result.length != 0 ? int.parse(result.last["productSaleId"].toString()) : 0;
    return productSalesId;
  }

  Future<int> updateProductsTable(Products products) async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_productDetails 
    SET $db_product_productSaleId = ? ,$db_product_productName = ? ,$db_product_productPrice = ?,$db_product_productMrp = ?,$db_product_productKeyName = ?, $db_toppingGroups_toppingGroupId=?, $db_product_priceType = ?,$db_product_productCgst = ?,$db_product_productSgst = ?,$db_product_productImage = ?,$db_product_productDate = ?,$db_product_productSync = ?, $db_product_rowStatus=?, $db_product_dynamicQuantity = ?, $db_product_enableInventory = ?, $db_product_productKeywordID = ?, $db_product_parcelAmount = ?, $db_product_productFor = ?
    WHERE $db_product_productId = ?
    ''', [
      products.productSaleId,
      products.name,
      products.price,
      products.mrp,
      products.productKeyName,
      products.toppingGroupId,
      products.priceType,
      products.cGst,
      products.sGst,
      products.image,
      products.productDate,
      products.sync,
      products.rowStatus,
      products.dynamicQuantity,
      products.enableInventory,
      products.keyID,
      products.parcelAmount,
      productForToString(products.productFor),
      products.productId,
    ]);
    print(result);
    return result;
  }

  Future<int> deleteUpdateProductsTable(Products products) async {
    var id = products.toMap()['$db_product_productId'];
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawUpdate('''
    UPDATE $db_productDetails 
    SET $db_product_rowStatus = '${2}',
    $db_product_productSync = '${0}'
    WHERE $db_product_productId = '${id}'
    ''');
    await fnUtilities.getAllProductDetails();
    return result;
  }

  Future<int> updateProductSync(String id) async {
    Database db = await DatabaseHelper.instance.database;
    var result;
    result = await db.rawUpdate('''
    UPDATE $db_productDetails 
    SET $db_product_productSync = ${1} 
    WHERE $db_product_productId = ?
    ''', [id]);
    return result;
  }
}
