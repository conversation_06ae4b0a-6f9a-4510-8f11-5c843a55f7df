import 'dart:io';

import 'package:smartbill/Repositories/environment.dart';
import 'package:dio/dio.dart';
import 'package:smartbill/utils/error_logs/error_logs_db_script.dart';

import '../../main.dart';

Future<void> uploadErrorLogsToAPI(List<File> files, String errorLogId) async {
  try {
    var dio = Dio();

    var url = "$baseUrl/api/v1/workspaces/uploaderrorlogfile";
    List<MultipartFile> fileList = [];

    for (var file in files) {
      String fileName = file.path.split("/").last;
      fileList.add(await MultipartFile.fromFile(file.path, filename: fileName));
    }

    var formData = FormData.fromMap({
      'ErrorLogFile': fileList,
      'errorLogId': errorLogId,
      'workspaceId': MyApp.activeWorkspace.workspaceId,
    });

    var response = await dio.post(
      url,
      data: formData,
      options: Options(
        headers: {'authorization': '${MyApp.activeUser.token}', 'Content-Type': 'multipart/form-data'},
      ),
    );

    if (response.statusCode == 200) {
      print(response.data);
      ErrorLogsDBScript.deleteErrorLogs();
    }
  } catch (e) {
    throw e;
  }
}

// Commented out unused code
// Future<int> downloadImage(imageId, category, {String imageFor}) async {
//   String baseImageURl = await fnUtilities.getGeneralSetValue("FileManagerUrl");
//   String bucketName = await fnUtilities.getGeneralSetValue("BucketName");
//
//   try {
//     Dio dio = new Dio();
//
//     dynamic url;
//     if (imageFor == applogo) {
//       url =
//           '$baseImageURl/api/filemanager/downloadImage/?category=$category&bucketName=smart-bill&filename=${imageId}.png';
//     } else {
//       url =
//           '$baseImageURl/api/filemanager/download?workspaceID=${MyApp.activeWorkspace.workspaceId}&category=$category&bucketName=$bucketName&filename=${imageId}.png';
//     }
//
//     final String saveDirectory =
//         (await getApplicationDocumentsDirectory()).path;
//
//     String savePath = '$saveDirectory/$imageId.png';
//
//     Response response = await dio.get(
//       url,
//       onReceiveProgress: showDownloadProgress,
//       options: Options(
//         responseType: ResponseType.bytes,
//         followRedirects: true,
//         validateStatus: (status) {
//           return status < 500;
//         },
//       ),
//     );
//
//     if (response.data.length > 0) {
//       File file = File(savePath);
//       var raf = file.openSync(mode: FileMode.write);
//       raf.writeFromSync(response.data);
//       await raf.close();
//       return 1;
//     }
//     return 0;
//   } catch (e) {
//     return 0;
//   }
// }

// Commented out unused code
// void showDownloadProgress(received, total) {
//   if (total != -1) {
//     //print((received / total * 100).toStringAsFixed(0) + "%");
//   }
// }
