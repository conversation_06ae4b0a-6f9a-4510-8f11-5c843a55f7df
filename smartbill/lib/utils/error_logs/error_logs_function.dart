import 'dart:developer';
import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:smartbill/utils/error_logs/error_log_model.dart';
import 'package:smartbill/utils/error_logs/error_logs_db_script.dart';
import 'package:smartbill/utils/error_logs/error_logs_repository.dart';
import 'package:uuid/uuid.dart';

import '../../main.dart';

class ErrorLogsFunction {
  static void insertErrorLogs(dynamic error, String errorFrom) {
    final uuid = Uuid();
    final errorLogID = uuid.v4().toString();
    final errorInfo = error.toString();

    final errorLogs = ErrorLogs(
      errorLogID: errorLogID,
      errorLogUserName: MyApp.activeWorkspace.userName,
      errorFrom: errorFrom,
      errorLogInfo: errorInfo,
      createdDate: DateTime.now().toString(),
      workspaceID: MyApp.activeWorkspace.workspaceId,
      errorLogSync: 1,
      status: 0,
    );

    ErrorLogsDBScript.insertErrorLogsDetails(errorLogs);
  }

  static Future<void> generateErrorFileAndUploadAPI() async {
    // final uuid = Uuid();
    // final errorFileId = uuid.v4().toString();
    String errorFileName = "${MyApp.activeWorkspace.workspaceId}_${DateTime.now().toLocal().toString()}";
    final errorLogList = (await ErrorLogsDBScript.queryAllErrorLogs()).map((row) => ErrorLogs.fromJson(row)).toList();

    if (errorLogList.isNotEmpty) {
      final errorString = errorLogList
          .asMap()
          .map((index, errorLogs) => MapEntry(
                index,
                "Error No : ${index + 1}\n"
                "Error ID   : ${errorLogs.errorLogID}\n"
                "Error Date : ${errorLogs.createdDate}\n"
                "UserName : ${errorLogs.errorLogUserName}  "
                "WorkspaceID: ${errorLogs.workspaceID}  DeviceID: ${MyApp.deviceId}\n"
                "Error From : ${errorLogs.errorFrom}\n"
                "Error Info: \n${errorLogs.errorLogInfo}\n\n\n",
              ))
          .values
          .join();

      final saveDirectory = (await getApplicationDocumentsDirectory()).path;
      final savePath = '$saveDirectory/$errorFileName.txt';

      File(savePath).writeAsStringSync(errorString);

      log('File created and content written successfully!');
      uploadErrorLogsToAPI([File(savePath)], errorFileName);
    }
  }
}
