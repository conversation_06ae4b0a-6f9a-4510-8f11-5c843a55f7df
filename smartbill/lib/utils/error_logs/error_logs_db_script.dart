import 'package:smartbill/main.dart';
import 'package:smartbill/utils/dbutils/database_helper.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite/sqflite.dart';

import 'error_log_model.dart';

class ErrorLogsDBScript {
  // INSERT ERROR LOGS DETAILS
  static Future<int> insertErrorLogsDetails(ErrorLogs permission) async {
    try {
      Database db = await DatabaseHelper.instance.database;
      var result = await db.insert(db_errorLogs, permission.toJson());
      return result;
    } catch (e) {
      print('Error inserting error logs: $e');
      return 0; // or handle the error in an appropriate way
    }
  }

  // GET ALL ERROR LOGS BASED ON WORKSPACE ID
  static Future<List<Map<String, dynamic>>> queryAllErrorLogs() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.rawQuery("SELECT * FROM $db_errorLogs WHERE $db_workspace_workspaceId='${MyApp.activeWorkspace.workspaceId}'");
    return result;
  }

  // DELETE ERROR LOGS TABLE
  static Future<int> deleteErrorLogs() async {
    Database db = await DatabaseHelper.instance.database;
    var result = await db.delete(db_errorLogs);
    return result;
  }
}
