import 'dart:convert';

GetErrorLogs getErrorLogsFromJson(String str) => GetErrorLogs.fromJson(json.decode(str));

String getErrorLogsToJson(GetErrorLogs data) => json.encode(data.toJson());

class GetErrorLogs {
  GetErrorLogs({
    this.status,
    this.message,
    this.result,
  });

  int? status;
  String? message;
  List<ErrorLogs>? result;

  factory GetErrorLogs.fromJson(Map<String, dynamic> json) => GetErrorLogs(
        status: json["status"] == null ? null : json["status"],
        message: json["message"] == null ? null : json["message"],
        result: json["result"] == null ? null : List<ErrorLogs>.from(json["result"].map((x) => ErrorLogs.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "status": status == null ? null : status,
        "message": message == null ? null : message,
        "result": result == null ? null : List<dynamic>.from(result!.map((x) => x.toJson())),
      };
}

class ErrorLogs {
  ErrorLogs(
      {this.id,
      this.errorLogID,
      this.errorLogUserName,
      this.errorFrom,
      this.errorLogInfo,
      this.createdDate,
      this.workspaceID,
      this.errorLogSync,
      this.status});

  int? id;
  String? errorLogID;
  String? errorLogUserName;
  String? errorFrom;
  String? errorLogInfo;
  String? createdDate;
  String? workspaceID;
  int? errorLogSync;
  int? status;

  factory ErrorLogs.fromJson(Map<String, dynamic> json) => ErrorLogs(
        id: json["id"] == null ? null : json["id"],
        errorLogID: json["errorLogID"] == null ? null : json["errorLogID"],
        errorLogUserName: json["errorLogUserName"] == null ? null : json["errorLogUserName"],
        errorFrom: json["errorFrom"] == null ? null : json["errorFrom"],
        errorLogInfo: json["errorLogInfo"] == null ? null : json["errorLogInfo"],
        createdDate: json["createdDate"] == null ? null : json["createdDate"],
        workspaceID: json["workspaceID"] == null ? null : json["workspaceID"],
        errorLogSync: json["sync"] == null ? null : json["sync"],
        status: json["status"] == null ? null : json["status"],
      );

  Map<String, dynamic> toJson() => {
        "id": id == null ? null : id,
        "errorLogID": errorLogID == null ? null : errorLogID,
        "errorLogUserName": errorLogUserName == null ? null : errorLogUserName,
        "errorFrom": errorFrom == null ? null : errorFrom,
        "errorLogInfo": errorLogInfo == null ? null : errorLogInfo,
        "createdDate": createdDate == null ? null : createdDate,
        "workspaceID": workspaceID == null ? null : workspaceID,
        "sync": errorLogSync == null ? null : errorLogSync,
        "status": status == null ? null : status,
      };
}
