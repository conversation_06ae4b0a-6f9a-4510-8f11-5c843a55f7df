import 'dart:io';

import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';

import '../../main.dart';
import '../../model/app_info/app_info_request.dart';
import 'firebase_mesage_service.dart';

class NotificationFunctions {
  updateFireBaseToken(String token) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    prefs.setString('fireBaseToken', token ?? '');
  }

  Future<bool> checkFireBaseTokenisNotExists(String token) async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    String fireBaseToken = prefs.getString('fireBaseToken') ?? "";
    if (fireBaseToken != token) {
      return true;
    }
    return false;
  }

  Future<AppInfoRequest> generateAppInfoReqModel() async {
    String firebaseToken =   "";
    AppInfoRequest appInfoRequest = AppInfoRequest(
      mobileId: MyApp.deviceId,
      appVersion: MyApp.versionCode,
      androidVersion: MyApp.androidVersionCode,
      iosVersion: MyApp.iosVersionCode,
      appName: appName.toLowerCase(),
      fireBaseToken: firebaseToken,
      isFireBaseToken: await checkFireBaseTokenisNotExists(firebaseToken),
      userType: 1,
    );
    return appInfoRequest;
  }
}
