import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:smartbill/utils/common_function/sync_utility.dart';
import 'package:smartbill/utils/local_notifications/local_notification_service.dart';
import '../local_notifications/awesome_notification_service.dart';

@pragma('vm:entry-point')
Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  if (message != null) {
    bool isAlertAlaram = message.data["showAlaram"] == "true";
    String title = message.data["title"];
    String body = message.data["body"];
    if (isAlertAlaram) {
      AwesomeNotificationService.showAlertNotification(title, body, "payload");
    }
  }
}

dynamic terNotifyPayload;

class FCM {
  static bool isForegroundNotificationHandled = false;

  static Future<void> initFireBase() async {
    await Firebase.initializeApp();

    if (Platform.isIOS) {
      FirebaseMessaging messaging = FirebaseMessaging.instance;

      NotificationSettings settings = await messaging.requestPermission(
        alert: true,
        announcement: false,
        badge: true,
        carPlay: false,
        criticalAlert: false,
        provisional: false,
        sound: true,
      );

      if (settings.authorizationStatus == AuthorizationStatus.authorized) {
        print('User granted permission');
      } else if (settings.authorizationStatus == AuthorizationStatus.provisional) {
        print('User granted provisional permission');
      } else {
        print('User declined or has not accepted permission');
      }
    }

    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // handle when app in active state
    forgroundNotification();

    // handle when app running in background state
    backgroundNotification();

    // handle when app completely closed by the user
    terminateNotification();

    /// on refresh token it will call the function
    // getFireBaseRefreshToken();
  }

  static forgroundNotification() {
    FirebaseMessaging.onMessage.listen(
      (RemoteMessage message) async {
        RemoteNotification? notification = message.notification;
        AndroidNotification? android = message.notification?.android;
        if (notification != null) {
          if (android != null) {
            var payload = await buidNotificationPayload(message.data, notifyType: "Local");
            // AwesomeNotificationService.showNormalNotification(notification.title, notification.body, payload);
            RemoteNotification notification1 = RemoteNotification(title: notification.title ?? "", body: notification.body);
            LocalNotification.showNotification(notification1, payload);
          } else if (Platform.isIOS) {
            RemoteNotification notification1 = RemoteNotification(title: notification.title ?? "", body: notification.body);
            LocalNotification.showNotification(notification1, "payload");
            // AwesomeNotificationService.showNormalNotification(notification.title, notification.body, "payload");
          }
        } else {
          if (!isForegroundNotificationHandled) {
            // showToast("Sync Started");
            triggerAlertAlaramFromFirebase(message);
            isForegroundNotificationHandled = true;
            // Reset the flag after a delay (e.g., 2 seconds)
            Timer(Duration(seconds: 2), () {
              isForegroundNotificationHandled = false;
            });
          }
        }
      },
    );
  }

  static backgroundNotification() {
    FirebaseMessaging.onMessageOpenedApp.listen(
      (RemoteMessage message) async {},
    );
  }

  static terminateNotification() async {
    RemoteMessage? message = await FirebaseMessaging.instance.getInitialMessage();
    if (message != null) {}
  }

  static Future<String> getFireBaseToken() async {
    // With this token you can test it easily on your phone
    try {
      String token = Platform.isIOS ? "" : await FirebaseMessaging.instance.getToken() ?? "";
      print(token);
      return token;
    } catch (e) {
      return "";
    }
  }

  static buidNotificationPayload(data, {String notifyType = "FIREBASE"}) {
    dynamic payload = {};
    if (notifyType.toUpperCase() == "LOCAL") {
      var path = data["path"];

      if (path == "filedownload") {
        var location = data["filelocation"];
        payload = {"path": "filedownload", "filelocation": location};
      }

      payload = jsonEncode(payload);
    }
    return payload;
  }

  /// show alert alaram
  static triggerAlertAlaramFromFirebase(RemoteMessage message) {
    if (message != null) {
      bool isAlertAlaram = message.data["showAlaram"] == "true";
      bool isTriggerSync = message.data["Sync"] == "true";
      String title = message.data["title"];
      String body = message.data["body"];
      if (isAlertAlaram) {
        AwesomeNotificationService.showAlertNotification(title, body, "payload");
      }
      if (isTriggerSync) {
        SyncUtility syncUtility = SyncUtility();
        // pushNotifyKey.currentState?.getAllSyncData();
        syncUtility.triggerManualSync();
      }
    }
  }
}
