import 'package:smartbill/model/get_transactions_fromapi.dart'; 
import 'package:smartbill/utils/common_widgets/widgets.dart';

import '../../Repositories/reports_repository.dart';

class ReportsUtils {
  static Future<GetTransactions> getTransactionListFromApi(fromDate, toDate, {paymentId}) async {
    GetTransactions getTransactions = new GetTransactions();
    await getTransactionsByDatToDate(fromDate, toDate, paymentId: paymentId).then((value) {
      if (value.status == 1) {
        if (value.result != null) {
          getTransactions = value.result!;
        }
      } else {
        showToast(value.message ?? "");
        getTransactions = new GetTransactions();
      }
    });
    return getTransactions;
  }
}
