import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:smartbill/main.dart';
import 'package:smartbill/utils/common_widgets/advance_order_widget.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';

import '../../Repositories/image_repository.dart';
import '../../model/printer_devices.dart';
import '../../model/reports.dart';
import '../../model/sales_payment_mapping.dart';
import '../../model/transaction_model.dart';
import '../common_function/common_sales_function.dart';
import '../common_function/printer_utilities.dart';
import 'printer_commands.dart';

class SalesPrint {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = PrinterUtilities();

  //REPORT VIEW PAGE PRINTER DEVICE FUNCTION FOR KOT
  startKotReportPrint(BuildContext context, Sales? sales, List<Transactions> transactionList) async {
    if (await printerUtilities.checkPrinterConnection(context)) {
      await printerUtilities.getPrinters();
      await printerUtilities.getPrinterDetails();
      if (printerUtilities.printerForKOT?.printerFormat == "Default") {
        if (printerUtilities.printerForKOT?.paperSize == 58) {
          await salesPrintKOT(
            sales,
            sales?.tableName,
            printerUtilities.printerForKOT,
            transactionList,
          );
        } else {
          await salesPrintKOT80MM(sales, sales?.tableName, printerUtilities.printerForKOT, transactionList);
        }
      } else {
        if (printerUtilities.printerForKOT?.paperSize == 58) {
          await salesPrintKOTTVS(sales, sales?.tableName, printerUtilities.printerForKOT, transactionList);
        } else {
          await salesPrintKOTTvs80MM(sales, sales?.tableName, printerUtilities.printerForKOT, transactionList);
        }
      }
    }
  }

  // SALES DEFAULT PRINT PAPER
  salesPrint(Sales sale, String? saleType, List<Transactions> printSaleList, List<SalesPaymentMapping> paymentMappingList,
      {SalesStatus? salesStatus, bool isComplementory = false, bool isAdvanceOrder = false}) async {
    List<int> printBytes = [];

    await printerUtilities.getPrinters();
    await printerUtilities.getPrinterDetails();
    await printerUtilities.commonOrderTitleFunction(sale, saleType: saleType);

    if (printerUtilities.printerForBill != null) {
      double totalAmount = 0.0;
      double totalCGstAmount = 0.0;
      double totalSGstAmount = 0.0;
      double grantTotalAmount = 0.0;
      double totalQty = 0.0;
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerUtilities.printerForBill);
      }
      //printBytes += await printerUtilities.printShopImage();

      if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
        printBytes += printerUtilities.commonShopName(printerUtilities.shopName, 16);
      } else {
        printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
      }
      printBytes += printerUtilities.commonShopAddress();
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom("Advance Order", 1, 1);
      }
      printBytes += printNewLine();
      if (printerUtilities.orderTitle != null) {
        printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
      }
      printBytes += printCustom("Ref No: ${sale.invoiceId}".padRight(22) + '${printerUtilities.currentDateTime()}'.padLeft(20), 0, 0);
      printBytes += printerUtilities.printOnlineRefNo(sale);
      if (sale.customerName != null && sale.customerName != "") {
        printBytes += printCustom("Customer Name: ${sale.customerName}", 0, 0);
      }
      printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom(
            "${AdvanceOrderWidget.convertDateTime(sale.deliveryDateAndTime == null ? "" : sale.deliveryDateAndTime ?? "")}".padRight(22), 0, 0);
      }
      if (sale.fkTableID != null) {
        printBytes += printCustom("Table: ${sale.tableName == null ? "" : sale.tableName}", 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      printBytes += printCustom("Product     Qty Rate  Amount(RS)", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      for (Transactions p in printSaleList) {
        totalAmount += double.parse(p.amountWithoutGst ?? '0');
        if (!isComplementory) {
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
        }
        totalQty += double.parse(p.qty ?? '0');
        var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 12);
        for (int i = 0; i < slittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${slittedProduct[i]}".padRight(13) +
                    "  " +
                    "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                    "  " +
                    '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                    "     " +
                    "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                0,
                0);
          } else {
            printBytes +=
                printCustom("${slittedProduct[i]}".padRight(13) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + "".padLeft(8), 0, 0);
          }
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      printBytes += printCustom(
          "Total Item: ${printSaleList.length}".padRight(20) + "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(22),
          0,
          0);
      printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
      if (sale.discountPrice != null && sale.discountPrice != "0.0") {
        printBytes += printCustom("Discount RS." + "${double.parse(sale.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
      }
      if (!isComplementory) {
        if (sale.isIGST ?? false) {
          if (totalCGstAmount != 0 && totalSGstAmount != 0) {
            double totalIGST = totalCGstAmount + totalSGstAmount;
            printBytes += printCustom("IGST RS." + "${totalIGST.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sale.discountPrice != null) {
          printBytes += printCustom("Grand Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
      }

      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);

      if (salesStatus != SalesStatus.processbill) {
        printBytes += await printerUtilities.printPaymentDetails(paymentMappingList, sale.totalAmount, paperSize: 58);
      }

      printBytes += printerUtilities.commonShopFooterText(true);
      if (printerUtilities.kotPrint) {
        printBytes += await printerUtilities.printDevelopedBy();
        printBytes += printNewLine();
        printBytes += printNewLine();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        if (!(printerUtilities.isIndividualKOT ?? false) &&
            printerUtilities.kotPrint &&
            (saleType != "T" && saleType != "SO") &&
            printerUtilities.printerForKOT != null) {
          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            await printerUtilities.disconnect();
            await Future.delayed(Duration(milliseconds: 500));
          }
          await salesPrintKOT(sale, null, printerUtilities.printerForKOT, printSaleList);
        }
      } else {
        printBytes += await printerUtilities.printDevelopedBy();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
      }
      print(printBytes.length);
      //Uint8List printBytesContent = Uint8List.fromList(printBytes);
      await bluetooth.writeBytes(printBytes);
      if (printerUtilities.isMultiPrint) {
        await Future.delayed(Duration(milliseconds: 500));
        printerUtilities.disconnect();
      }
    }
  }

  /// SALES DEFAULT KOT PRINTER
  salesPrintKOT(Sales? sales, String? tableName, PrinterDevices? printerInfo, List<Transactions>? printSaleList) async {
    List<int> printBytes = [];
    await printerUtilities.getPrinterDetails();
    if (printerInfo != null) {
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerInfo);
      }
      if (sales?.invoiceId != null) {
        printBytes += printCustom("NO: ${sales?.invoiceId}", 2, 1);
      }
      printBytes += printerUtilities.printOnlineRefNo(sales, isKOT: true);
      if (tableName != null) {
        printBytes += printCustom("$tableName", 2, 1);
      }

      printBytes += printCustom('${printerUtilities.currentDateTime()}', 0, 1);
      if (await fnUtilities.workSpaceSetValues(showEmployeeNamesInKOT, table: "WorkSpaceSettings") == "1") {
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      printBytes += printCustom("Product                    Qty  ", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        var splittedProduct = printerUtilities.splitStringByLength(p.name ?? "", 24);
        for (int i = 0; i < splittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${splittedProduct[i]}".padRight(24) + "  " + "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(5), 1, 0);
          } else {
            printBytes += printCustom("${splittedProduct[i]}".padRight(24) + "  " + " ".padLeft(5), 1, 0);
          }
        }
        printBytes += printerUtilities.printKOTNotes(p.notes);
        printBytes += printNewLine();
      }
      for (int i = 0; i < printerInfo.feedSize!; i++) {
        printBytes += printNewLine();
      }
      if (printerInfo.paperCut ?? false) {
        printBytes += paperCut();
      }
      await bluetooth.writeBytes(printBytes);
      if (printerUtilities.isMultiPrint) {
        await Future.delayed(Duration(milliseconds: 500));
        printerUtilities.disconnect();
      }
    }
  }

  /// SALES PRINT KOT TVS
  salesPrintKOTTVS(Sales? sales, String? tableName, PrinterDevices? printerInfo, List<Transactions>? printSaleList) async {
    List<int> printBytes = [];
    if (printerInfo != null) {
      await printerUtilities.initPrint(printerInfo);
      if (sales?.invoiceId != null) {
        printBytes += printCustom("NO: ${sales?.invoiceId}", 2, 1);
      }
      printBytes += printerUtilities.printOnlineRefNo(sales, isKOT: true);
      if (tableName != null) {
        printBytes += printCustom("$tableName", 2, 1);
      }

      printBytes += printCustom('${printerUtilities.currentDateTime()}', 0, 1);
      if (await fnUtilities.workSpaceSetValues(showEmployeeNamesInKOT, table: "WorkSpaceSettings") == "1") {
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
      printBytes += printCustom("Product                    Qty  ", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        var splittedProduct = printerUtilities.splitStringByLength(p.name!, 24);
        for (int i = 0; i < splittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${splittedProduct[i]}".padRight(24) + "  " + "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(5), 1, 0);
          } else {
            printBytes += printCustom("${splittedProduct[i]}".padRight(24) + "  " + " ".padLeft(5), 1, 0);
          }
        }
        printBytes += printerUtilities.printKOTNotes(p.notes!);
        printBytes += printNewLine();
      }
      for (int i = 0; i < printerInfo.feedSize!; i++) {
        printBytes += printNewLine();
      }
      if (printerInfo.paperCut ?? false) {
        printBytes += paperCut();
      }
      await bluetooth.writeBytes(printBytes);
    }
  }

  // SALES TVS(FORMAT 1) PRINT PAPER
  salesPrintTvs(Sales sale, String? saleType, List<Transactions>? printSaleList, List<SalesPaymentMapping>? paymentMappingList,
      {SalesStatus? salesStatus, bool isComplementory = false, bool isAdvanceOrder = false}) async {
    printerUtilities.getPrinterInfo();
    await printerUtilities.getPrinterDetails();
    await printerUtilities.commonOrderTitleFunction(sale, saleType: saleType);
    List<int> printBytes = [];

    if (printerUtilities.printerForBill != null) {
      double totalAmount = 0.0;
      double totalCGstAmount = 0.0;
      double totalSGstAmount = 0.0;
      double grantTotalAmount = 0.0;
      double totalQty = 0.0;

      /// connect printer
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerUtilities.printerForBill);
      }
      // printBytes += await printerUtilities.printShopImage();
      printBytes += printerUtilities.shopNameWidget();
      printBytes += printerUtilities.commonShopAddress();
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom("Advance Order", 1, 1);
      }
      printBytes += printNewLine();
      if (printerUtilities.orderTitle != null) {
        printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
      }
      printBytes += printCustom("Ref No: ${sale.invoiceId}".padRight(10) + '${printerUtilities.currentDateTime()}'.padLeft(19), 0, 0);
      printBytes += printerUtilities.printOnlineRefNo(sale);
      if (sale.customerName != null && sale.customerName != "") {
        printBytes += printCustom("Customer Name: ${sale.customerName}", 0, 0);
      }
      printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom(
            "${AdvanceOrderWidget.convertDateTime(sale.deliveryDateAndTime == null ? "" : sale.deliveryDateAndTime ?? "")}".padRight(22), 0, 0);
      }
      if (sale.fkTableID != null) {
        printBytes += printCustom("Table: ${sale.tableName == null ? "" : sale.tableName}", 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
      printBytes += printCustom("Product     Qty Rate  Amount(RS)", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        totalAmount += double.parse(p.amountWithoutGst ?? '0');
        if (!isComplementory) {
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
        }
        totalQty += double.parse(p.qty ?? '0');
        var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 10);
        for (int i = 0; i < slittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${slittedProduct[i]}".padRight(11) +
                    "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(3) +
                    '${double.parse(p.price ?? '0').toStringAsFixed(2)}'.padLeft(6) +
                    "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(10),
                0,
                0);
          } else {
            printBytes += printCustom("${slittedProduct[i]}".padRight(11) + " ".padLeft(3) + ' '.padLeft(6) + " ".padLeft(10), 0, 0);
          }
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      printBytes += printCustom(
          "Total Item: ${printSaleList?.length}".padRight(15) + "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(10),
          0,
          0);
      printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
      if (sale.discountPrice != null && sale.discountPrice != "0.0") {
        printBytes += printCustom("Discount RS." + "${double.parse(sale.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
      }
      if (!isComplementory) {
        if (sale.isIGST ?? false) {
          if (totalCGstAmount != 0 && totalSGstAmount != 0) {
            double totalIGST = totalCGstAmount + totalSGstAmount;
            printBytes += printCustom("IGST RS." + "${totalIGST.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sale.discountPrice != null) {
          printBytes += printCustom("Grand Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
      if (salesStatus != SalesStatus.processbill) {
        printBytes += printerUtilities.printPaymentDetails(paymentMappingList, sale.totalAmount, paperSize: 58);
      }
      printBytes += printerUtilities.commonShopFooterText(true);
      if (printerUtilities.kotPrint) {
        printBytes += await printerUtilities.printDevelopedBy();
        printBytes += printNewLine();
        printBytes += printNewLine();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        await bluetooth.writeBytes(printBytes);

        if (!(printerUtilities.isIndividualKOT ?? false) &&
            printerUtilities.kotPrint &&
            (saleType != "T" && saleType != "SO") &&
            printerUtilities.printerForKOT != null) {
          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            await printerUtilities.disconnect();
            await Future.delayed(Duration(milliseconds: 500));
          }
          await salesPrintKOTTVS(sale, null, printerUtilities.printerForKOT, printSaleList);

          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            printerUtilities.disconnect();
          }
        }
      } else {
        printBytes += await printerUtilities.printDevelopedBy();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        await bluetooth.writeBytes(printBytes);
      }
    }
  }

  // SALES DEFAULT PRINT PAPER
  salesPrint80MM(Sales sale, String? saleType, List<Transactions> printSaleList, List<SalesPaymentMapping> paymentMappingList,
      {SalesStatus? salesStatus, bool isComplementory = false, bool isAdvanceOrder = false}) async {
    await printerUtilities.getPrinters();
    await printerUtilities.getPrinterDetails();
    await printerUtilities.commonOrderTitleFunction(sale, saleType: saleType);
    List<int> printBytes = [];

    if (printerUtilities.printerForBill != null) {
      double totalAmount = 0.0;
      double totalCGstAmount = 0.0;
      double totalSGstAmount = 0.0;
      double grantTotalAmount = 0.0;
      double totalQty = 0.0;

      /// connect printer
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerUtilities.printerForBill);
      }
    //  printBytes += await printerUtilities.printShopImage();
      if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
        printBytes += printerUtilities.commonShopName(printerUtilities.shopName, 24);
      } else {
        printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
      }
      printBytes += printerUtilities.commonShopAddress();
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom("Advance Order", 1, 1);
      }
      printBytes += printNewLine();
      if (printerUtilities.orderTitle != null) {
        printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
      }
      printBytes += printCustom("Ref No: ${sale.invoiceId}".padRight(28) + '${printerUtilities.currentDateTime()}'.padLeft(35), 0, 0);
      printBytes += printerUtilities.printOnlineRefNo(sale);
      if (sale.customerName != null && sale.customerName != "") {
        printBytes += printCustom("Customer Name: ${sale.customerName}", 0, 0);
      }
      printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom(
            "${AdvanceOrderWidget.convertDateTime(sale.deliveryDateAndTime == null ? "" : sale.deliveryDateAndTime ?? "")}".padRight(22), 0, 0);
      }
      if (sale.fkTableID != null) {
        printBytes += printCustom("Table: ${sale.tableName == null ? "" : sale.tableName}", 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom("Product                  Qty   Rate   Amount(RS)", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      for (Transactions p in printSaleList) {
        totalAmount += double.parse(p.amountWithoutGst ?? '0');
        if (!isComplementory) {
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
        }

        totalQty += double.parse(p.qty ?? '0');
        var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 21);
        for (int i = 0; i < slittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${slittedProduct[i]}".padRight(21) +
                    "  " +
                    "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                    "  " +
                    '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                    "     " +
                    "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                1,
                0);
          } else {
            printBytes +=
                printCustom("${slittedProduct[i]}".padRight(35) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + "".padLeft(8), 1, 0);
          }
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom(
          "Total Item: ${printSaleList.length}".padRight(32) + "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(32),
          0,
          0);
      printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
      if (!isComplementory) {
        if (sale.discountPrice != null && sale.discountPrice != "0.0") {
          printBytes += printCustom("Discount RS." + "${double.parse(sale.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        if (sale.isIGST ?? false) {
          if (totalCGstAmount != 0 && totalSGstAmount != 0) {
            double totalIGST = totalCGstAmount + totalSGstAmount;
            printBytes += printCustom("IGST RS." + "${totalIGST.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sale.discountPrice != null) {
          printBytes += printCustom("Grand Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);

      if (salesStatus != SalesStatus.processbill) {
        printBytes += printerUtilities.printPaymentDetails(paymentMappingList, sale.totalAmount, paperSize: 80);
      }
      printBytes += printNewLine();
      printBytes += printerUtilities.commonShopFooterText(false);
      if (printerUtilities.kotPrint) {
        printBytes += await printerUtilities.printDevelopedBy(is80MM: true);
        printBytes += printNewLine();
        printBytes += printNewLine();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        await bluetooth.writeBytes(printBytes);
        
        if (!(printerUtilities.isIndividualKOT ?? false) &&
            printerUtilities.kotPrint &&
            (saleType != "T" && saleType != "SO") &&
            printerUtilities.printerForKOT != null) {
          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            await printerUtilities.disconnect();
            await Future.delayed(Duration(milliseconds: 500));
          }
          await salesPrintKOT80MM(sale, null, printerUtilities.printerForKOT, printSaleList);
        }
      } else {
        printBytes += await printerUtilities.printDevelopedBy(is80MM: true);
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
          await bluetooth.writeBytes(printBytes);
        }
      }
      
      if (printerUtilities.isMultiPrint) {
        await Future.delayed(Duration(milliseconds: 500));
        printerUtilities.disconnect();
      }
    }
  }

  salesPrintKOT80MM(Sales? sales, String? tableName, PrinterDevices? printerInfo, List<Transactions>? printSaleList) async {
    await printerUtilities.getPrinterDetails();
    List<int> printBytes = [];
    if (printerInfo != null) {
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerInfo);
      }

      if (sales?.invoiceId != null) {
        printBytes += printCustom("NO: ${sales?.invoiceId}", 2, 1);
      }
      printBytes += printerUtilities.printOnlineRefNo(sales, isKOT: true);
      if (tableName != null) {
        printBytes += printCustom("$tableName", 2, 1);
      }
      printBytes += printCustom('${printerUtilities.currentDateTime()}', 0, 1);
      if (await fnUtilities.workSpaceSetValues(showEmployeeNamesInKOT, table: "WorkSpaceSettings") == "1") {
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom("Product                              Qty  ", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        var splittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 30);
        for (int i = 0; i < (splittedProduct.length); i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${splittedProduct[i]}".padRight(30) + "  " + "${printerUtilities.checkQtyDoubleValue(p.qty ?? '0'.toString())}".padLeft(7), 1, 0);
          } else {
            printBytes += printCustom("${splittedProduct[i]}".padRight(24) + "  " + " ".padLeft(5), 1, 0);
          }
        }
        printBytes += printerUtilities.printKOTNotes(p.notes);
        printBytes += printNewLine();
      }
      for (int i = 0; i < (printerInfo.feedSize ?? 0); i++) {
        printBytes += printNewLine();
      }
      if (printerInfo.paperCut ?? false) {
        printBytes += paperCut();
      }
      await bluetooth.writeBytes(printBytes);
      if (printerUtilities.isMultiPrint) {
        await Future.delayed(Duration(milliseconds: 500));
        await printerUtilities.disconnect();
      }
    }
  }

  // SALES DEFAULT PRINT PAPER
  salesPrintTvs80MM(Sales sale, String? saleType, List<Transactions>? printSaleList, List<SalesPaymentMapping>? paymentMappingList,
      {SalesStatus? salesStatus, bool isComplementory = false, bool isAdvanceOrder = false}) async {
    await printerUtilities.getPrinters();
    await printerUtilities.getPrinterDetails();
    await printerUtilities.commonOrderTitleFunction(sale, saleType: saleType);
    List<int> printBytes = [];

    if (printerUtilities.isMultiPrint != null) {
      double totalAmount = 0.0;
      double totalCGstAmount = 0.0;
      double totalSGstAmount = 0.0;
      double grantTotalAmount = 0.0;
      double totalQty = 0.0;
      // printBytes += await printerUtilities.printShopImage();
      printBytes += await printerUtilities.shopNameWidget();
      // if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
      //   bluetooth.printCustom("${printerUtilities.shopName.toUpperCase()}", 2, 1);
      // } else {
      //   bluetooth.printCustom("${appName.toUpperCase()}", 2, 1);
      // }
      printBytes += await printerUtilities.commonShopAddress();
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom("Advance Order", 1, 1);
      }
      printBytes += printNewLine();
      if (printerUtilities.orderTitle != null) {
        printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
      }
      printBytes += printCustom("Ref No: ${sale.invoiceId}".padRight(28) + '${printerUtilities.currentDateTime()}'.padLeft(35), 0, 0);
      printBytes += printerUtilities.printOnlineRefNo(sale);
      if (sale.customerName != null && sale.customerName != "") {
        printBytes += printCustom("Customer Name: ${sale.customerName}", 0, 0);
      }
      printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      if (isAdvanceOrder && salesStatus == SalesStatus.advanceOrder) {
        printBytes += printCustom(
            "${AdvanceOrderWidget.convertDateTime(sale.deliveryDateAndTime == null ? "" : sale.deliveryDateAndTime ?? "")}".padRight(22), 0, 0);
      }
      if (sale.fkTableID != null) {
        printBytes += printCustom("Table: ${sale.tableName == null ? "" : sale.tableName}", 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom("Product                  Qty   Rate   Amount(RS)", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        totalAmount += double.parse(p.amountWithoutGst ?? '0');
        if (!isComplementory) {
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
        }
        totalQty += double.parse(p.qty ?? '0');
        var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 21);
        for (int i = 0; i < (slittedProduct.length); i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${slittedProduct[i]}".padRight(21) +
                    "  " +
                    "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                    "  " +
                    '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                    "     " +
                    "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                1,
                0);
          } else {
            printBytes +=
                printCustom("${slittedProduct[i]}".padRight(35) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + "".padLeft(8), 1, 0);
          }
        }
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom(
          "Total Item: ${printSaleList?.length}".padRight(32) + "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(32),
          0,
          0);
      printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
      if (sale.discountPrice != null && sale.discountPrice != "0.0") {
        printBytes += printCustom("Discount RS." + "${double.parse(sale.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
      }
      if (!isComplementory) {
        if (sale.isIGST ?? false) {
          if (totalCGstAmount != 0 && totalSGstAmount != 0) {
            double totalIGST = totalCGstAmount + totalSGstAmount;
            printBytes += printCustom("IGST RS." + "${totalIGST.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sale.discountPrice != null) {
          printBytes += printCustom("Grand Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
      }

      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);

      if (salesStatus != SalesStatus.processbill) {
        printBytes += await printerUtilities.printPaymentDetails(paymentMappingList, sale.totalAmount, paperSize: 80);
      }

      printBytes += printNewLine();
      printBytes += printerUtilities.commonShopFooterText(false);
      if (printerUtilities.kotPrint) {
        printBytes += await printerUtilities.printDevelopedBy(is80MM: true);
        printBytes += printNewLine();
        printBytes += printNewLine();
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        if (!(printerUtilities.isIndividualKOT ?? false) &&
            printerUtilities.kotPrint &&
            (saleType != "T" && saleType != "SO") &&
            printerUtilities.printerForKOT != null) {
          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            await printerUtilities.disconnect();
            await Future.delayed(Duration(milliseconds: 500));
          }
          await salesPrintKOT80MM(sale, null, printerUtilities.printerForKOT, printSaleList);
          await bluetooth.writeBytes(printBytes);
          if (printerUtilities.isMultiPrint) {
            await Future.delayed(Duration(milliseconds: 500));
            printerUtilities.disconnect();
          }
        }
      } else {
        printBytes += await printerUtilities.printDevelopedBy(is80MM: true);
        for (int i = 0; i < (printerUtilities.printerForBill?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerUtilities.printerForBill?.paperCut ?? false) {
          printBytes += paperCut();
        }
        await bluetooth.writeBytes(printBytes);
      }
    }
  }

  salesPrintKOTTvs80MM(Sales? sales, String? tableName, PrinterDevices? printerInfo, List<Transactions>? printSaleList) async {
    List<int> printBytes = [];
    await printerUtilities.getPrinterDetails();
    if (printerInfo != null) {
      /// connect printer
      if (printerUtilities.isMultiPrint) {
        await printerUtilities.initPrint(printerInfo);
      }
      if (sales?.invoiceId != null) {
        printBytes += printCustom("NO: ${sales?.invoiceId}", 2, 1);
      }
      printBytes += printerUtilities.printOnlineRefNo(sales, isKOT: true);
      if (tableName != null) {
        printBytes += printCustom("$tableName", 2, 1);
      }
      printBytes += printCustom('${printerUtilities.currentDateTime()}', 0, 1);
      if (await fnUtilities.workSpaceSetValues(showEmployeeNamesInKOT, table: "WorkSpaceSettings") == "1") {
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
      }
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      printBytes += printCustom("Product                              Qty  ", 1, 0);
      printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
      for (Transactions p in printSaleList ?? []) {
        var splittedProduct = printerUtilities.splitStringByLength(p.name!, 30);
        for (int i = 0; i < splittedProduct.length; i++) {
          if (i == 0) {
            printBytes += printCustom(
                "${splittedProduct[i]}".padRight(30) + "  " + "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(7), 1, 0);
          } else {
            printBytes += printCustom("${splittedProduct[i]}".padRight(24) + "  " + " ".padLeft(5), 1, 0);
          }
        }
        printBytes += printerUtilities.printKOTNotes(p.notes!);
        printBytes += printNewLine();
      }
      for (int i = 0; i < printerInfo.feedSize!; i++) {
        printBytes += printNewLine();
      }
      if (printerInfo.paperCut ?? false) {
        printBytes += paperCut();
      }
      await bluetooth.writeBytes(printBytes);
    }
  }
}
