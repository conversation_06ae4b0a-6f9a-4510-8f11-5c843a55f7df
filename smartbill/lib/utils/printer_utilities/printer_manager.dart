import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter_esc_pos_utils/flutter_esc_pos_utils.dart';
import 'package:flutter/services.dart';
import 'package:image/image.dart';

import 'printer_commands.dart';

class PrinterManager {
  static List<int> printCustomBytes(String message, int size, int align, String? charset) {
    List<int> cc = [0x1B, 0x21, 0x03]; // 0- normal size text
    // List<int> cc1 = [0x1B, 0x21, 0x00]; // 0- normal size text
    List<int> bb = [0x1B, 0x21, 0x08]; // 1- only bold text
    List<int> bb2 = [0x1B, 0x21, 0x20]; // 2- bold with medium text
    List<int> bb3 = [0x1B, 0x21, 0x10]; // 3- bold with large text
    List<int> bb4 = [0x1B, 0x21, 0x30]; // 4- strong text

    List<int> commandBytes = [];

    try {
      switch (size) {
        case 0:
          commandBytes.addAll(cc);
          break;
        case 1:
          commandBytes.addAll(bb);
          break;
        case 2:
          commandBytes.addAll(bb2);
          break;
        case 3:
          commandBytes.addAll(bb3);
          break;
        case 4:
          commandBytes.addAll(bb4);
          break;
      }

      switch (align) {
        case 0:
          // left align
          commandBytes.addAll(PrinterCommands.ESC_ALIGN_LEFT);
          break;
        case 1:
          // center align
          commandBytes.addAll(PrinterCommands.ESC_ALIGN_CENTER);
          break;
        case 2:
          // right align
          commandBytes.addAll(PrinterCommands.ESC_ALIGN_RIGHT);
          break;
      }

      if (charset != null) {
        commandBytes.addAll(utf8.encode(message));
      } else {
        commandBytes.addAll(utf8.encode(message));
      }

      commandBytes.addAll(PrinterCommands.FEED_LINE);
    } catch (e, stackTrace) {
      print("Error: $e");
      print(stackTrace);
    }
    return commandBytes;
  }

  static Future<List<int>> printImage(String pathImage, {int paperSize = 58}) async {
    List<int> commandBytes = [];
    try {
      final profile = await CapabilityProfile.load();
      final generator = Generator(paperSize == 58 ? PaperSize.mm80 : PaperSize.mm58, profile);
      File imageFile = File(pathImage);
      Uint8List imageBytes = imageFile.readAsBytesSync();
      var sizeFactor = 1.5;
      var sizeHeight = 1.5;
      final Image image = decodeImage(imageBytes)!;
      dynamic resizedImage = copyResize(image, width: (image.width * sizeFactor).toInt(), height: (image.height * sizeHeight).toInt());

      List<int> imageData = generator.imageRaster(resizedImage);
      commandBytes.addAll(imageData);
    } catch (e, stackTrace) {
      print("Error: $e");
      print(stackTrace);
    }
    return commandBytes;
  }
}
