import 'dart:io';
import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:collection/collection.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:pdf/pdf.dart';
import 'package:share/share.dart';
import 'package:smartbill/utils/constants/strings_keys.dart';

import '../../Repositories/image_repository.dart';
import 'package:pdf/widgets.dart' as pw;

import '../../main.dart';
import '../../model/reports.dart';
import '../../model/transaction_model.dart';
import '../common_function/printer_utilities.dart';
import '../common_widgets/snack_bar_widget.dart';
import '../common_widgets/widgets.dart';
import '../local_notifications/local_notification_service.dart';
import '../push_notifications/firebase_mesage_service.dart';

class SharePDF {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = PrinterUtilities();

  //PDF
  pw.Widget pdfHeader() {
    return pw.Header(
        level: 0,
        child: pw.Row(
            mainAxisAlignment: !printerUtilities.shopImageID.isEmptyOrNull && printerUtilities.imageFile!.existsSync()
                ? pw.MainAxisAlignment.start
                : pw.MainAxisAlignment.center,
            children: [
              printerUtilities.showImage(),
              pw.SizedBox(
                  width: !printerUtilities.shopImageID.isEmptyOrNull && printerUtilities.imageFile!.existsSync() ? 400 : 500,
                  child: pw.Center(
                      child: pw.Column(
                    mainAxisAlignment: pw.MainAxisAlignment.center,
                    children: [
                      pw.Text(
                          printerUtilities.shopName != "" && printerUtilities.shopName != null
                              ? printerUtilities.shopName ?? ''.toUpperCase()
                              : appName.toUpperCase(),
                          style: pw.TextStyle(fontSize: 23, fontWeight: pw.FontWeight.bold)),
                      printerUtilities.shopAddress1 != "" && printerUtilities.shopAddress1 != null
                          ? pw.Text(printerUtilities.shopAddress1 ?? '', style: const pw.TextStyle(fontSize: 18))
                          : pw.Container(),
                      printerUtilities.shopAddress2 != "" && printerUtilities.shopAddress2 != null
                          ? pw.Text(printerUtilities.shopAddress2 ?? '', style: const pw.TextStyle(fontSize: 18))
                          : pw.Container(),
                      printerUtilities.shopAddress3 != "" && printerUtilities.shopAddress3 != null
                          ? pw.Text(printerUtilities.shopAddress3 ?? '', style: const pw.TextStyle(fontSize: 18))
                          : pw.Container(),
                      printerUtilities.shopGstNo != "" && printerUtilities.shopGstNo != null
                          ? pw.Text("GSTIN: " + (printerUtilities.shopGstNo?.toUpperCase() ?? ""), style: const pw.TextStyle(fontSize: 18))
                          : pw.Container(),
                    ],
                  )))
            ]));
  }

  pw.Widget pdfFooter() {
    return pw.Row(mainAxisAlignment: pw.MainAxisAlignment.center, children: [
      pw.Column(children: [
        printerUtilities.shopFooterText != "" && printerUtilities.shopFooterText != null
            ? pw.Padding(
                padding: const pw.EdgeInsets.only(top: 10),
                child: pw.Text(printerUtilities.shopFooterText ?? '', style: const pw.TextStyle(fontSize: 18)))
            : pw.Container(),
        printerUtilities.shopFooterText2 != "" && printerUtilities.shopFooterText2 != null
            ? pw.Padding(
                padding: const pw.EdgeInsets.only(top: 5),
                child: pw.Text(printerUtilities.shopFooterText2 ?? '', style: const pw.TextStyle(fontSize: 18)))
            : pw.Container(),
      ])
    ]);
  }

  pw.Widget pdfSaleTitle() {
    return pw.Row(mainAxisAlignment: pw.MainAxisAlignment.center, children: [
      pw.Column(children: [
        printerUtilities.orderTitle != "" && printerUtilities.orderTitle != null
            ? pw.Text("${printerUtilities.orderTitle}", style: pw.TextStyle(fontSize: 20, fontWeight: pw.FontWeight.bold))
            : pw.Container(),
      ])
    ]);
  }

  pw.Widget pdfDevelopedBy() {
    if ((fnUtilities.workSpaceSetValues(key_printerAdEnable, table: "WorkSpaceSettings") == "1")) {
      return pw.Padding(
          padding: const pw.EdgeInsets.only(top: 5),
          child: pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Text("", style: const pw.TextStyle(fontSize: 10)),
            pw.Text("by $appName", style: const pw.TextStyle(fontSize: 10)),
            pw.Text("contact:  +91 9487674499", style: const pw.TextStyle(fontSize: 10))
          ]));
    } else {
      return pw.SizedBox();
    }
  }

  showSimpleNotification(String fileName, dynamic path) async {
    // var androidDetails =
    //     const AndroidNotificationDetails('id', 'channel', priority: Priority.high, importance: Importance.max, icon: "@mipmap/ic_launcher");
    // var iOSDetails = const IOSNotificationDetails();
    // var platformDetails = NotificationDetails(android: androidDetails, iOS: iOSDetails);
    // await flutterLocalNotificationsPlugin.show(0, "Downloaded Successfully", '', platformDetails, payload: path);
    var payload = FCM.buidNotificationPayload({"path": "filedownload", "filelocation": path}, notifyType: "LOCAL");
    RemoteNotification notification = RemoteNotification(title: "Downloaded Successfully", body: payload);
    LocalNotification.showNotification(notification, payload);
  }

  Future<void> savePDFFormat(pw.Widget bodyWidget, String fileName, String? currentDate, {BuildContext? context}) async {
    await printerUtilities.getShopDetails();
    await printerUtilities.getShopImage();
    final pdf = pw.Document(pageMode: PdfPageMode.outlines);
    pdf.addPage(
      pw.MultiPage(
        maxPages: 100,
        orientation: pw.PageOrientation.portrait,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        header: (pw.Context context) {
          if (context.pageNumber == 1) {
            return pdfHeader();
          }
          return pw.SizedBox();
        },
        build: (pw.Context context) => <pw.Widget>[
          fileName == "Sales_Report" ? pdfSaleTitle() : pw.Container(),
          bodyWidget,
          fileName == "Sales_Report" ? pdfFooter() : pw.Container(),
          fileName == "Sales_Report" ? pdfDevelopedBy() : pw.Container()
        ], //
      ),
    );
    await savePDF(pdf, fileName, currentDate, context: context);
  }

  pw.Widget pdfCommonSalesReportBodyBuildFunction(Sales? reports, List<Transactions>? transactionList) {
    int sNo = 0;
    var totalAmount = 0.0;
    var totalDiscount = 0.0;
    var totalQuantity = 0.0;
    var totalSGST = 0.0;
    var totalCGST = 0.0;
    var dateFormat = DateFormat("dd-MM-yyyy hh:mm a").format(DateTime.parse("${reports?.date}"));
    for (Transactions p in transactionList ?? []) {
      totalAmount += double.parse(p.amount ?? '0');
      totalQuantity += double.parse(p.qty ?? '0');
      if (p.sGstAmount != null) {
        totalSGST += double.parse(p.sGstAmount ?? '0');
      }
      if (p.cGstAmount != null) {
        totalCGST += double.parse(p.cGstAmount ?? '0');
      }
    }
    if (reports?.discountPrice != null) {
      totalDiscount = double.parse(reports?.discountPrice ?? '0');
    }
    //totalAmount = totalAmount - totalDiscount;

    var groupList = transactionList?.groupListsBy((element) => element.prodId);
    var invoiceExtension = reports?.invoiceExtension != null ? reports?.invoiceExtension ?? '' : "";
    return pw.Container(
        width: 500,
        child: pw.Column(children: [
          // pw.SizedBox(height: 10),
          // pw.Row(
          //   mainAxisAlignment: pw.MainAxisAlignment.center,
          //   children: [
          //     pw.Text("Sales",
          //         style: pw.TextStyle(
          //             fontSize: 20, fontWeight: pw.FontWeight.bold))
          //   ],
          // ),
          pw.SizedBox(height: 10),
          // pw.Row(
          //   mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
          //   children: [
          //     reports.invoiceNo != null && !printerUtilities.isResetInvoice
          //         ? pw.Text("Bill No: ${reports.invoiceNo}", style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold))
          //         : pw.Container(),
          //     pw.Text("Ref No: ${reports.invoiceId}", style: pw.TextStyle(fontSize: 13, fontWeight: pw.FontWeight.normal)),
          //   ],
          // ),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              reports?.paymentInvoice != null && !printerUtilities.isResetInvoice
                  ? pw.Text("Bill No: ${reports?.paymentInvoice ?? '' + invoiceExtension}",
                      style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold))
                  : pw.Container(),
              pw.Text("Ref No: ${reports?.invoiceId}", style: pw.TextStyle(fontSize: 13, fontWeight: pw.FontWeight.normal)),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Row(
            mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
            children: [
              reports?.onlineRefNo != null && reports?.onlineRefNo != ""
                  ? pw.Text("Online No: ${reports?.onlineRefNo}", style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold))
                  : pw.Container(),
            ],
          ),
          pw.SizedBox(height: 10),
          pw.Container(
              width: 500,
              child: pw.Column(
                crossAxisAlignment: pw.CrossAxisAlignment.start,
                children: [
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("Date ", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + dateFormat.toString(), style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  reports?.customerName != null
                      ? pw.Row(children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("Customer Name", style: const pw.TextStyle(fontSize: 15)),
                          ),
                          pw.Container(
                            width: 300,
                            child: pw.Text(": ${reports?.customerName}", style: const pw.TextStyle(fontSize: 15)),
                          )
                        ])
                      : pw.Container(),
                  pw.Row(children: [
                    pw.Container(
                      width: 120,
                      child: pw.Text("Served by", style: const pw.TextStyle(fontSize: 15)),
                    ),
                    pw.Container(
                      width: 300,
                      child: pw.Text(": " + (MyApp.activeUser.firstName ?? '') + " " + (MyApp.activeUser.lastName ?? ''),
                          style: const pw.TextStyle(fontSize: 15)),
                    )
                  ]),
                  reports?.shiftName != null
                      ? pw.Row(children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("Shift Name  ", style: const pw.TextStyle(fontSize: 15)),
                          ),
                          pw.Container(
                            width: 300,
                            child: pw.Text(": ${reports?.shiftName != null ? reports?.shiftName : ' '}", style: const pw.TextStyle(fontSize: 15)),
                          )
                        ])
                      : pw.SizedBox.shrink(),
                  reports?.fkTableID != null
                      ? pw.Row(children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("Table", style: const pw.TextStyle(fontSize: 15)),
                          ),
                          pw.Container(
                            width: 300,
                            child: pw.Text(": ${reports?.tableName}", style: const pw.TextStyle(fontSize: 15)),
                          )
                        ])
                      : pw.Container(),
                  reports?.deletedBy != null && reports?.deletedDate != null && reports?.deletedReason != null && reports?.deletedUserName != null
                      ? pw.Column(children: [
                          pw.Row(children: [
                            pw.Container(
                              width: 120,
                              child: pw.Text("Deleted by  ", style: const pw.TextStyle(fontSize: 15)),
                            ),
                            pw.Container(
                              width: 300,
                              child: pw.Text(": ${reports?.deletedUserName != null ? reports?.deletedUserName : ' '}",
                                  style: const pw.TextStyle(fontSize: 15)),
                            )
                          ]),
                          pw.Row(children: [
                            pw.Container(
                              width: 120,
                              child: pw.Text("Deleted date ", style: const pw.TextStyle(fontSize: 15)),
                            ),
                            pw.Container(
                              width: 300,
                              child: pw.Text(": ${reports?.deletedDate != null ? printerUtilities.formatDate(reports?.deletedDate ?? '') : ' '} ",
                                  style: const pw.TextStyle(fontSize: 15)),
                            )
                          ]),
                          pw.Row(children: [
                            pw.Container(
                              width: 120,
                              child: pw.Text("Deleted reason", style: const pw.TextStyle(fontSize: 15)),
                            ),
                            pw.Container(
                              width: 300,
                              child: pw.Text(": ${reports?.deletedReason != null ? reports?.deletedReason : ' '} ",
                                  style: const pw.TextStyle(fontSize: 15)),
                            )
                          ]),
                          // pw.Text("Deleted By :${reports.deletedUserName != null ? reports.deletedUserName : ' '}",
                          //     style: pw.TextStyle(fontSize: 15)),
                          // pw.Text("Deleted Date :${reports.deletedDate != null ? printerUtilities.formatDate(reports.deletedDate) : ' '} ",
                          //     style: pw.TextStyle(fontSize: 15)),
                          // pw.Text("Deleted Reason:${reports.deletedReason != null ? reports.deletedReason : ' '} ",
                          //     style: pw.TextStyle(fontSize: 15)),
                        ])
                      : pw.SizedBox.shrink(),
                ],
              )),
          pw.SizedBox(height: 10),
          pw.Divider(thickness: 2),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
            pw.Container(width: 40, child: pw.Text("S.No", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 130, child: pw.Text("Product", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.left)),
            pw.Container(width: 80, child: pw.Text("Qty", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.center)),
            pw.Container(width: 90, child: pw.Text("Price", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right)),
            pw.Container(width: 95, child: pw.Text("Amount", style: const pw.TextStyle(fontSize: 15), textAlign: pw.TextAlign.right))
          ]),
          pw.Divider(thickness: 2),
          // ignore: sdk_version_ui_as_code
          if (groupList != null || groupList?.length != 0)
            for (var i in groupList!.keys)
              pw.Column(children: [
                for (int list = 0; list < groupList[i]!.length; list++)
                  pw.Row(mainAxisAlignment: pw.MainAxisAlignment.spaceBetween, children: [
                    pw.Container(
                        width: 40, child: pw.Text((sNo += 1).toString(), style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                    pw.Container(
                        width: 130,
                        child: pw.Text(groupList[i]?[list].name ?? ''.toString(),
                            style: const pw.TextStyle(fontSize: 10), textAlign: pw.TextAlign.left)),
                    pw.Container(
                        width: 80,
                        child: pw.Text(fnUtilities.checkQtyDoubleValue(groupList[i]?[list].qty ?? '0'.toString()),
                            textAlign: pw.TextAlign.center, style: const pw.TextStyle(fontSize: 10))),
                    pw.Container(
                        width: 90,
                        child: pw.Text(double.parse(groupList[i]?[list].price ?? '0'.toString()).toStringAsFixed(1),
                            textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10))),
                    pw.Container(
                        width: 95,
                        child: pw.Text(double.parse(groupList[i]?[list].amountWithoutGst ?? '0'.toString()).toStringAsFixed(1),
                            textAlign: pw.TextAlign.right, style: const pw.TextStyle(fontSize: 10)))
                  ]),
              ]),
          pw.Divider(thickness: 2),
          reports?.discountPrice != null
              ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                  pw.Container(
                    width: 120,
                    child:
                        pw.Text("Total Discount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  ),
                  pw.Container(
                    width: 150,
                    child: pw.Text(double.parse(reports?.discountPrice ?? '0').toStringAsFixed(2),
                        textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                  )
                ])
              : pw.Container(),
          reports?.isIGST == null || !(reports?.isIGST ?? false)
              ? pw.Column(children: [
                  totalSGST != null && totalSGST != 0.0
                      ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("SGST (Rs) :",
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          ),
                          pw.Container(
                            width: 150,
                            child: pw.Text(totalSGST.toStringAsFixed(2),
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          )
                        ])
                      : pw.Container(),
                  totalCGST != null && totalCGST != 0.0
                      ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("CGST (Rs) :",
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          ),
                          pw.Container(
                            width: 150,
                            child: pw.Text(totalCGST.toStringAsFixed(2),
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          )
                        ])
                      : pw.Container(),
                ])
              : pw.Column(children: [
                  totalCGST != null && totalCGST != 0.0
                      ? pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
                          pw.Container(
                            width: 120,
                            child: pw.Text("IGST (Rs) :",
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          ),
                          pw.Container(
                            width: 150,
                            child: pw.Text((totalSGST + totalCGST).toStringAsFixed(2),
                                textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
                          )
                        ])
                      : pw.Container(),
                ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Items :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text((transactionList?.length ?? 0).toString(),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Qty :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(fnUtilities.checkQtyDoubleValue(totalQuantity.toString()),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.Row(mainAxisAlignment: pw.MainAxisAlignment.end, children: [
            pw.Container(
              width: 120,
              child: pw.Text("Total Amount :", textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            ),
            pw.Container(
              width: 150,
              child: pw.Text(totalAmount.toStringAsFixed(2),
                  textAlign: pw.TextAlign.right, style: pw.TextStyle(fontSize: 15, fontWeight: pw.FontWeight.bold)),
            )
          ]),
          pw.SizedBox(height: 10)
        ]));
  }

  printShopImage() async {
    if (await fnUtilities.workSpaceSetValues(key_imagePrint, table: "WorkSpaceSettings") == "1") {
      if (!printerUtilities.shopImageID.isEmptyOrNull) {
        var filepath = await fnUtilities.readFileFromLocal(printerUtilities.shopImageID);
        if (filepath != null && filepath.path != "") {
          await bluetooth.printImage(filepath.path);
          bluetooth.printNewLine();
        }
      }
    }
  }

  printLogoImage() async {
    var filepath = await fnUtilities.readFileFromLocal(applogo);
    if (filepath != null && filepath.path != "") {
      await bluetooth.printNewLine();
      await bluetooth.printImage(filepath.path);
      await bluetooth.printNewLine();
    }
  }

  buildSalesPDFFormat(pw.Document pdf, pw.Widget bodyWidget, String fileName, String currentDate) async {
    await printerUtilities.getShopDetails();
    await printerUtilities.getShopImage();
    pdf.addPage(
      pw.MultiPage(
        orientation: pw.PageOrientation.portrait,
        crossAxisAlignment: pw.CrossAxisAlignment.start,
        header: (pw.Context context) {
          return pdfHeader();
        },
        build: (pw.Context context) => <pw.Widget>[
          fileName == "Sales_Report" ? pdfSaleTitle() : pw.Container(),
          bodyWidget,
          fileName == "Sales_Report" ? pdfFooter() : pw.Container(),
          fileName == "Sales_Report" ? pdfDevelopedBy() : pw.Container()
        ], //
      ),
    );
  }

  savePDF(pw.Document pdf, String fileName, String? currentDate, {BuildContext? context}) async {
    var output;
    if (currentDate != null && currentDate != "") {
      currentDate = DateFormat("ddMMyyyyhhmmss").format(DateTime.parse(currentDate));
      if (Platform.isAndroid) {
        Directory dir = Directory('/storage/emulated/0/Download');
        output = dir;
      } else {
        output = await getApplicationDocumentsDirectory();
      }

      fileName = fileName + currentDate + '.pdf';
    } else {
      if (Platform.isAndroid) {
        output = await getExternalStorageDirectory();
      } else {
        output = await getApplicationDocumentsDirectory();
      }

      fileName = fileName + '.pdf';
    }
    final file = File("${output.path}/$fileName");
    await file.writeAsBytes(await pdf.save());
    if (currentDate != null && currentDate != "") {
      if (file.existsSync() && Platform.isAndroid) {
        showSimpleNotification(fileName, file.path);
      }
      // showToast("Saved to downloads");
      if (context != null)
        showCommonSnackBar(context, () {
          OpenFilex.open(file.path);
        });
    } else {
      Share.shareFiles(['${output.path}/$fileName'], text: appName);
    }
  }
}
