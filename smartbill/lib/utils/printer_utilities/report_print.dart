import 'package:blue_thermal_printer/blue_thermal_printer.dart';
import 'package:flutter/material.dart';
import 'package:smartbill/utils/printer_utilities/printer_commands.dart';

import '../../main.dart';
import '../../model/printer_devices.dart';
import '../../model/reports.dart';
import '../../model/sales_reports_printer.dart';
import '../../model/transaction_model.dart';
import '../common_function/printer_utilities.dart';
import '../constants/strings_keys.dart';

class ReportPrint {
  BlueThermalPrinter bluetooth = BlueThermalPrinter.instance;
  PrinterUtilities printerUtilities = PrinterUtilities();

  //REPORT VIEW PAGE PRINTER DEVICE FUNCTION
  startReportPrint(BuildContext context, Sales? sales, List<Transactions> transactionList) async {
    if (await printerUtilities.checkPrinterConnection(context)) {
      await printerUtilities.getReportPrinter();
      if (printerUtilities.reportPrinter?.printerFormat == "Default") {
        if (printerUtilities.reportPrinter?.paperSize == 58) {
          await individualReportPrint(sales, transactionList, printerUtilities.reportPrinter);
        } else {
          await individualReportPrint80MM(sales, transactionList, printerUtilities.reportPrinter);
        }
      } else {
        if (printerUtilities.reportPrinter?.paperSize == 58) {
          await individualReportPrintTvs(sales, transactionList, printerUtilities.reportPrinter);
        } else {
          await individualReportPrintTvs80MM(sales, transactionList, printerUtilities.reportPrinter);
        }
      }
    }
  }

  //REPORT LONG PRINTER DEVICE FUNCTION
  startFullReportPrint(BuildContext context, SalesReportsPrinter? reports, {bool isPaymentReport = false}) async {
    if (await printerUtilities.checkPrinterConnection(context)) {
      await printerUtilities.getReportPrinter();
      await printerUtilities.getPrinterDetails();
      if (printerUtilities.reportPrinter?.printerFormat == "Default") {
        if (printerUtilities.reportPrinter?.paperSize == 58) {
          await fullReportsPrint(reports, printerUtilities.reportPrinter, isPaymentReport: isPaymentReport);
        } else {
          await fullReportsPrint80MM(reports, printerUtilities.reportPrinter, isPaymentReport: isPaymentReport);
        }
      } else {
        if (printerUtilities.reportPrinter?.paperSize == 58) {
          await fullReportsPrintTvs(reports, printerUtilities.reportPrinter, isPaymentReport: isPaymentReport);
        } else {
          await fullReportsPrintTVS80MM(reports, printerUtilities.reportPrinter, isPaymentReport: isPaymentReport);
        }
      }
    }
  }

  // INDIVIDUAL SALES DEFAULT PRINT PAPER
  individualReportPrint(Sales? sales, List<Transactions> transactionList, PrinterDevices? printerInfo) async {
    await printerUtilities.getReportPrinter();
    await printerUtilities.commonOrderTitleFunction(sales);
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        double totalCGstAmount = 0.0;
        double totalSGstAmount = 0.0;
        double grantTotalAmount = 0.0;
        double totalQty = 0.0;
        await printerUtilities.printShopImage();
    List<int> printBytes = [];

        if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
          await printerUtilities.commonShopName(printerUtilities.shopName, 16);
        } else {
          printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
        }
        await printerUtilities.commonShopAddress();
       printBytes += printNewLine();
        if (printerUtilities.orderTitle != null) {
          printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
        }
        // if (sales?.invoiceNo != null && !printerUtilities.isResetInvoice) {
        //   bluetooth.printCustom("Bill No: ${sales.invoiceNo}".padRight(22), 0, 0);
        // }
        if (sales?.paymentInvoice != null && !printerUtilities.isResetInvoice) {
          var invoiceExtenction = sales?.invoiceExtension != null ? sales?.invoiceExtension : "";
          printBytes += printCustom("Bill No: ${sales?.paymentInvoice}${invoiceExtenction}".padRight(22), 0, 0);
        }

        printBytes += printCustom("Ref No: ${sales?.invoiceId}".padRight(22) + '${printerUtilities.formatDate(sales?.date ?? '')}'.padLeft(20), 0, 0);
        await printerUtilities.printOnlineRefNo(sales);
        if (sales?.customerName != null && sales?.customerName != "") {
          printBytes += printCustom("Customer Name: ${sales?.customerName}".padRight(22), 0, 0);
        }
        printBytes +=printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
        if (sales?.fkTableID != null) {
          printBytes += printCustom("Table: ${sales?.tableName == null ? "" : sales?.tableName}".padRight(22), 0, 0);
        }
        if (sales?.shiftName != null) {
         printBytes += printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}".padRight(22), 0, 0);
        }
        if (sales?.deletedBy != null && sales?.deletedDate != null && sales?.deletedReason != null && sales?.deletedUserName != null) {
          printBytes += printCustom("Deleted By: ${sales?.deletedUserName == null ? "" : sales?.deletedUserName}".padRight(22), 0, 0);
          printBytes += printCustom(
              "Deleted Date: ${sales?.deletedDate == null ? "" : printerUtilities.formatDate(sales?.deletedDate ?? '')}".padRight(22), 0, 0);
          printBytes += printCustom("Deleted Reason: ${sales?.deletedReason == null ? "" : sales?.deletedReason}".padRight(22), 0, 0);
        }
        printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
        printBytes += printCustom("Product     Qty Rate  Amount(RS)", 1, 0);
        printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
        for (Transactions p in transactionList) {
          totalAmount += double.parse(p.amountWithoutGst ?? '0');
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
          totalQty += double.parse(p.qty ?? '0');
          var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 12);
          for (int i = 0; i < slittedProduct.length; i++) {
            if (i == 0) {
              printBytes += printCustom(
                  "${slittedProduct[i]}".padRight(13) +
                      "  " +
                      "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                      "  " +
                      '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                      "     " +
                      "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                  0,
                  0);
            } else {
              printBytes += printCustom(
                  "${slittedProduct[i]}".padRight(13) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + " ".padLeft(8), 0, 0);
            }
          }
        }

       printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
        printBytes += printCustom(
            "Total Item: ${transactionList.length}".padRight(20) +
                "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(22),
            0,
            0);
        printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        if (sales?.discountPrice != null) {
          printBytes += printCustom("Discount RS." + "${double.parse(sales?.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        if (sales?.isIGST == null || !(sales?.isIGST ?? false)) {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalCGstAmount != 0) {
            double igst = totalCGstAmount + totalSGstAmount;
            printBytes += printCustom("IGST RS." + "${igst.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sales?.discountPrice != null) {
          printBytes += printCustom("Grant Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        printBytes += printNewLine();
        await printerUtilities.commonShopFooterText(false);
        await printerUtilities.printDevelopedBy();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes += paperCut();
        }
        bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  // LONG SALES DEFAULT PRINT PAPER
  fullReportsPrint(SalesReportsPrinter? reports, PrinterDevices? printerInfo, {bool isPaymentReport = false}) async {
    List<int> printBytes = [];
    await printerUtilities.getShopDetails();
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
          await printerUtilities.commonShopName(printerUtilities.shopName, 16);
        } else {
          printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
        }
        printBytes +=printCustom("Sales Reports", 1, 1);
        await printerUtilities.commonShopAddress();
        if (DateTime.parse(reports?.fromDate ?? '').difference(DateTime.parse(reports?.toDate ?? '')).inDays != 0) {
          printBytes +=printCustom("Form Date :".padRight(22) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(20), 0, 0);
          printBytes +=printCustom("To Date :".padRight(22) + '${printerUtilities.formatDate(reports?.toDate ?? '')}'.padLeft(20), 0, 0);
        } else {
          printBytes +=printCustom("Date :".padRight(22) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(20), 0, 0);
        }

        printBytes +=printCustom(printerUtilities.returnDashedLines(42), 0, 0);

        if (reports?.isShort ?? false) {
        } else {
          if (isPaymentReport) {
           printBytes +=printCustom("No   ID     PaidBy    Amount(RS)", 1, 0);
          } else {
            printBytes +=printCustom("No   ID        Qty    Amount(RS)", 1, 0);
          }
          printBytes +=printCustom(printerUtilities.returnDashedLines(42), 0, 0);
          int sno = 0;
          for (Sales p in reports?.SalesReportsPrinterList ?? []) {
            sno++;
            totalAmount += double.parse(p.totalAmount ?? '0');
            String? invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
            if (isPaymentReport) {
              printBytes += printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(7) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(7) +
                      "  " +
                      "${p.paymentTypeName.toString()}".padLeft(6) +
                      "       " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(9),
                  0,
                  0);
            } else {
              var invoiceExtenction = p.invoiceExtension != null ? '\n${p.invoiceExtension}' : "";

              printBytes += printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtenction : '-' : sno}"
                          .padRight(7) +
                      " ${p.invoiceId != null ? p.invoiceId : '-'}".padRight(7) +
                      "   " +
                      "${printerUtilities.checkQtyDoubleValue(p.transactionQty.toString())}".padLeft(6) +
                      "   " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(9),
                  0,
                  0);
            }
          }
          printBytes += printCustom(printerUtilities.returnDashedLines(42), 0, 0);
        }
        if (!isPaymentReport) {
          printBytes += printCustom("Total Sales:".padLeft(15) + "${reports?.SalesReportsPrinterList?.length}".padLeft(15), 1, 2);
          printBytes += printCustom("Total Qty:".padLeft(15) + "${reports?.totQty}".padLeft(15), 1, 2);
        }
        printBytes += printCustom("Total Amount:".padLeft(15) + "RS ${double.parse(reports?.totAmount ?? '0').toStringAsFixed(2)}".padLeft(15), 1, 2);
        printBytes += printNewLine();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes += paperCut();
        }
         bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  individualReportPrintTvs(Sales? sales, List<Transactions> transactionList, PrinterDevices? printerInfo) async {
    await printerUtilities.commonOrderTitleFunction(sales);
    await bluetooth.isConnected.then((isConnected) async {
      List<int> printBytes = [];
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        double totalCGstAmount = 0.0;
        double totalSGstAmount = 0.0;
        double grantTotalAmount = 0.0;
        double totalQty = 0.0;
        await printerUtilities.printShopImage();
        await printerUtilities.shopNameWidget();
        await printerUtilities.commonShopAddress();
        printBytes += printNewLine();
        if (printerUtilities.orderTitle != null) {
          printBytes += printCustom("${printerUtilities.orderTitle}", 1, 1);
        }
        printBytes += printCustom("Ref No: ${sales?.invoiceId}".padRight(10) + '${printerUtilities.currentDateTime()}'.padLeft(19), 0, 0);
        await printerUtilities.printOnlineRefNo(sales);
        if (sales?.customerName != null && sales?.customerName != "") {
          printBytes += printCustom("Customer Name: ${sales?.customerName}", 0, 0);
        }
        printBytes +=printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
        if (sales?.shiftName != null) {
          printBytes += printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}", 0, 0);
        }
        if (sales?.fkTableID != null) {
          printBytes += printCustom("Table: ${sales?.tableName == null ? "" : sales?.tableName}", 0, 0);
        }
        if (sales?.shiftName != null) {
          printBytes += printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}".padRight(22), 0, 0);
        }
        if (sales?.deletedBy != null && sales?.deletedDate != null && sales?.deletedReason != null && sales?.deletedUserName != null) {
          printBytes += printCustom("Deleted By: ${sales?.deletedUserName == null ? "" : sales?.deletedUserName}".padRight(22), 0, 0);
          printBytes += printCustom(
              "Deleted Date: ${sales?.deletedDate == null ? "" : printerUtilities.formatDate(sales?.deletedDate ?? '')}".padRight(22), 0, 0);
          printBytes += printCustom("Deleted Reason: ${sales?.deletedReason == null ? "" : sales?.deletedReason}".padRight(22), 0, 0);
        }
       printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
       printBytes += printCustom("Product     Qty Rate  Amount(RS)", 1, 0);
        printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
        for (Transactions p in transactionList) {
          totalAmount += double.parse(p.amountWithoutGst ?? '0');
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
          totalQty += double.parse(p.qty ?? '0');
          var slittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 10);
          for (int i = 0; i < slittedProduct.length; i++) {
            if (i == 0) {
             printBytes += printCustom(
                  "${slittedProduct[i]}".padRight(11) +
                      "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(3) +
                      '${p.price}'.padLeft(6) +
                      "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(10),
                  0,
                  0);
            } else {
              printBytes += printCustom("${slittedProduct[i]}".padRight(11) + " ".padLeft(3) + ' '.padLeft(6) + " ".padLeft(10), 0, 0);
            }
          }
        }

        printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
        printBytes += printCustom(
            "Total Item: ${transactionList.length}".padRight(15) +
                "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(10),
            0,
            0);
       printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        if (sales?.discountPrice != null) {
          printBytes += printCustom("Discount RS." + "${double.parse(sales?.discountPrice ?? '').toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        if (sales?.isIGST == null || !(sales?.isIGST ?? false)) {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalSGstAmount != 0) {
            double igst = totalSGstAmount + totalCGstAmount;
           printBytes += printCustom("IGST RS." + "${igst.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sales?.discountPrice != null) {
          printBytes += printCustom("Grant Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        printBytes += printNewLine();
        await printerUtilities.commonShopFooterText(false);
        await printerUtilities.printDevelopedBy();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes += paperCut();
        }
         bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  // LONG SALES TVS(FORMAT 1) PRINT PAPER
  fullReportsPrintTvs(SalesReportsPrinter? reports, PrinterDevices? printerInfo, {bool isPaymentReport = false}) async {
    List<int> printBytes = [];
    await printerUtilities.getShopDetails();
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        await printerUtilities.shopNameWidget();
        printBytes += printCustom("Sales Reports", 1, 1);
        await printerUtilities.commonShopAddress();
        if (DateTime.parse(reports?.fromDate ?? '').difference(DateTime.parse(reports?.toDate ?? '')).inDays != 0) {
          printBytes += printCustom("Form Date :".padRight(10) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(19), 0, 0);
          printBytes += printCustom("To Date :".padRight(10) + '${printerUtilities.formatDate(reports?.toDate ?? '')}'.padLeft(19), 0, 0);
        } else {
          printBytes += printCustom("Date :".padRight(10) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(19), 0, 0);
        }

        printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);

        if (reports?.isShort ?? false) {
        } else {
          if (isPaymentReport) {
            printBytes += printCustom("No   ID     PaidBy    Amount(RS)", 1, 0);
          } else {
            printBytes += printCustom("No   ID        Qty    Amount(RS)", 1, 0);
          }
          printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
          int sno = 0;
          for (Sales p in reports?.SalesReportsPrinterList ?? []) {
            sno++;
            totalAmount += double.parse(p.totalAmount ?? '0');
            if (isPaymentReport) {
              var invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";

              printBytes += printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(7) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(7) +
                      "  " +
                      "${p.paymentTypeName.toString()}".padLeft(6) +
                      "       " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(9),
                  0,
                  0);
            } else {
              var invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
              printBytes += printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? "" + invoiceExtension! : '-' : sno}"
                          .padRight(7) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(7) +
                      "     " +
                      "${printerUtilities.checkQtyDoubleValue(p.transactionQty.toString())}".padLeft(6) +
                      "       " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(9),
                  0,
                  0);
            }
          }
          printBytes += printCustom(printerUtilities.returnDashedLines(31), 0, 0);
        }

        if (!isPaymentReport) {
          printBytes += printCustom("Total Sales:".padLeft(15) + "${reports?.SalesReportsPrinterList?.length}".padLeft(15), 1, 2);
          printBytes += printCustom("Total Qty:".padLeft(15) + "${reports?.totQty}".padLeft(15), 1, 2);
        }
        printBytes += printCustom("Total Amount:".padLeft(15) + "RS ${double.parse(reports?.totAmount ?? '0').toStringAsFixed(2)}".padLeft(15), 1, 2);
        printBytes += printNewLine();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes += printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes += paperCut();
        }
        bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  individualReportPrint80MM(Sales? sales, List<Transactions> transactionList, PrinterDevices? printerInfo) async {
    List<int> printBytes = [];
    await printerUtilities.commonOrderTitleFunction(sales);
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        double totalCGstAmount = 0.0;
        double totalSGstAmount = 0.0;
        double grantTotalAmount = 0.0;
        double totalQty = 0.0;
        await printerUtilities.printShopImage();
        if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
          await printerUtilities.commonShopName(printerUtilities.shopName, 24);
        } else {
          printBytes += printCustom("${appName.toUpperCase()}", 2, 1);
        }
        await printerUtilities.commonShopAddress();
        if (sales?.paymentInvoice != null && !printerUtilities.isResetInvoice) {
          var invoiceExtension = sales?.invoiceExtension != null ? sales?.invoiceExtension : "";
          printBytes += printCustom("Bill No: ${sales?.paymentInvoice ?? ''}${invoiceExtension ?? ''}".padRight(22), 0, 0);
        }
        printBytes += printCustom("Ref No: ${sales?.invoiceId}".padRight(28) + '${printerUtilities.formatDate(sales?.date ?? '')}'.padLeft(35), 0, 0);
        await printerUtilities.printOnlineRefNo(sales);
        if (sales?.customerName != null && sales?.customerName != '') {
          printBytes += printCustom("Customer Name: ${sales?.customerName}".padRight(35), 0, 0);
        }
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
        if (sales?.shiftName != null) {
          printBytes += printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}".padRight(22), 0, 0);
        }
        if (sales?.fkTableID != null) {
          printBytes += printCustom("Table: ${sales?.tableName == null ? "" : sales?.tableName}".padRight(22), 0, 0);
        }
        if (sales?.shiftName != null) {
         printBytes += printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}".padRight(22), 0, 0);
        }
        if (sales?.deletedBy != null && sales?.deletedDate != null && sales?.deletedReason != null && sales?.deletedUserName != null) {
          printBytes += printCustom("Deleted By: ${sales?.deletedUserName == null ? "" : sales?.deletedUserName}".padRight(22), 0, 0);
          printBytes += printCustom(
              "Deleted Date: ${sales?.deletedDate == null ? "" : printerUtilities.formatDate(sales?.deletedDate ?? '')}".padRight(22), 0, 0);
          printBytes += printCustom("Deleted Reason: ${sales?.deletedReason == null ? "" : sales?.deletedReason}".padRight(22), 0, 0);
        }
        printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        printBytes += printCustom("Product                  Qty   Rate   Amount(RS)", 1, 0);
        printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        for (Transactions p in transactionList) {
          totalAmount += double.parse(p.amountWithoutGst ?? '0');
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
          totalQty += double.parse(p.qty ?? '0');
          var splittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 21);
          for (int i = 0; i < splittedProduct.length; i++) {
            if (i == 0) {
              printBytes += printCustom(
                  "${splittedProduct[i]}".padRight(21) +
                      "  " +
                      "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                      "  " +
                      '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                      "     " +
                      "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                  1,
                  0);
            } else {
              printBytes += printCustom(
                  "${splittedProduct[i]}".padRight(35) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + "".padLeft(8), 1, 0);
            }
          }
        }

        printBytes += printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        printBytes += printCustom(
            "Total Item: ${transactionList.length}".padRight(32) +
                "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(32),
            0,
            0);
        printBytes += printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        if (sales?.discountPrice != null) {
          printBytes += printCustom("Discount RS." + "${double.parse(sales?.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        if (sales?.isIGST == null || !(sales?.isIGST ?? false)) {
          if (totalCGstAmount != 0) {
            printBytes += printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
            printBytes += printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalSGstAmount != 0) {
            double igst = totalSGstAmount + totalCGstAmount;
            printBytes += printCustom("IGST RS." + "${igst.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sales?.discountPrice != null) {
          printBytes += printCustom("Grant Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
       printBytes += printNewLine();
        await printerUtilities.commonShopFooterText(false);
        await printerUtilities.printDevelopedBy(is80MM: true);
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
         printBytes += printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes += paperCut();
        }
        bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  individualReportPrintTvs80MM(Sales? sales, List<Transactions> transactionList, PrinterDevices? printerInfo) async {
     List<int> printBytes = [];
    await printerUtilities.commonOrderTitleFunction(sales);
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        double totalCGstAmount = 0.0;
        double totalSGstAmount = 0.0;
        double grantTotalAmount = 0.0;
        double totalQty = 0.0;
        await printerUtilities.printShopImage();
        await printerUtilities.shopNameWidget();
        await printerUtilities.commonShopAddress();
        List<int> printBytes = [];
         printBytes += printCustom(
            "Ref No: ${sales?.invoiceId}".padRight(28) + '${printerUtilities.formatDate(sales?.date ?? DateTime.now().toString())}'.padLeft(35),
            0,
            0);
        await printerUtilities.printOnlineRefNo(sales);
        if (sales?.customerName != null && sales?.customerName != '') {
         printBytes +=printCustom("Customer Name: ${sales?.customerName}".padRight(35), 0, 0);
        }
        printBytes += printCustom("Served By: ${MyApp.activeUser.firstName} ${MyApp.activeUser.lastName}".padRight(22), 0, 0);
        if (sales?.shiftName != null) {
         printBytes +=printCustom("Shift Name: ${sales?.shiftName == null ? "" : sales?.shiftName}".padRight(22), 0, 0);
        }
        if (sales?.fkTableID != null) {
         printBytes +=printCustom("Table: ${sales?.tableName == null ? "" : sales?.tableName}".padRight(22), 0, 0);
        }
        if (sales?.deletedBy != null && sales?.deletedDate != null && sales?.deletedReason != null && sales?.deletedUserName != null) {
         printBytes +=printCustom("Deleted By: ${sales?.deletedUserName == null ? "" : sales?.deletedUserName}".padRight(22), 0, 0);
         printBytes +=printCustom(
              "Deleted Date: ${sales?.deletedDate == null ? "" : printerUtilities.formatDate(sales?.deletedDate ?? '')}".padRight(22), 0, 0);
         printBytes +=printCustom("Deleted Reason: ${sales?.deletedReason == null ? "" : sales?.deletedReason}".padRight(22), 0, 0);
        }
        printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        printBytes +=printCustom("Product                  Qty   Rate   Amount(RS)", 1, 0);
       printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        for (Transactions p in transactionList) {
          totalAmount += double.parse(p.amountWithoutGst ?? '0');
          totalCGstAmount += double.parse(p.cGstAmount ?? '0');
          totalSGstAmount += double.parse(p.sGstAmount ?? '0');
          grantTotalAmount += double.parse(p.amount ?? '0');
          totalQty += double.parse(p.qty ?? '0');
          var splittedProduct = printerUtilities.splitStringByLength(p.name ?? '', 21);
          for (int i = 0; i < splittedProduct.length; i++) {
            if (i == 0) {
             printBytes +=printCustom(
                  "${splittedProduct[i]}".padRight(21) +
                      "  " +
                      "${printerUtilities.checkQtyDoubleValue(p.qty.toString())}".padLeft(4) +
                      "  " +
                      '${double.parse(p.price ?? '0').toStringAsFixed(1)}'.padLeft(6) +
                      "     " +
                      "${double.parse(p.amountWithoutGst ?? '0').toStringAsFixed(2)}".padLeft(8),
                  1,
                  0);
            } else {
              printBytes +=printCustom(
                  "${splittedProduct[i]}".padRight(35) + "  " + " ".padLeft(4) + "  " + ' '.padLeft(6) + "     " + "".padLeft(8), 1, 0);
            }
          }
        }

        printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
       printBytes +=printCustom(
            "Total Item: ${transactionList.length}".padRight(32) +
                "Total Qty: ${printerUtilities.checkQtyDoubleValue(totalQty.toString())}".padLeft(32),
            0,
            0);
        printBytes +=printCustom("Total RS." + "${totalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        if (sales?.discountPrice != null) {
          printBytes +=printCustom("Discount RS." + "${double.parse(sales?.discountPrice ?? '0').toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        if (sales?.isIGST == null || !(sales?.isIGST ?? false)) {
          if (totalCGstAmount != 0) {
            printBytes +=printCustom("SGST RS." + "${totalCGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
          if (totalSGstAmount != 0) {
           printBytes +=printCustom("CGST RS." + "${totalSGstAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        } else {
          if (totalSGstAmount != 0) {
            double igst = totalSGstAmount + totalCGstAmount;
            printBytes +=printCustom("IGST RS." + "${igst.toStringAsFixed(2)}".padLeft(10), 1, 2);
          }
        }
        if (totalSGstAmount != 0 || sales?.discountPrice != null) {
          printBytes +=printCustom("Grant Total RS." + "${grantTotalAmount.toStringAsFixed(2)}".padLeft(10), 1, 2);
        }
        printBytes +=printNewLine();
        await printerUtilities.commonShopFooterText(false);
        await printerUtilities.printDevelopedBy(is80MM: true);
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes +=printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes +=paperCut();
        }
         bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  fullReportsPrint80MM(SalesReportsPrinter? reports, PrinterDevices? printerInfo, {bool isPaymentReport = false}) async {
    List<int> printBytes = [];
    await printerUtilities.getShopDetails();
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        if (printerUtilities.shopName != "" && printerUtilities.shopName != null) {
          await printerUtilities.commonShopName(printerUtilities.shopName, 24);
        } else {
          printBytes +=printCustom("${appName.toUpperCase()}", 2, 1);
        }
        printBytes +=printCustom("Sales Reports", 1, 1);
        await printerUtilities.commonShopAddress();
        if (DateTime.parse(reports?.fromDate ?? '').difference(DateTime.parse(reports?.toDate ?? '')).inDays != 0) {
          printBytes +=printCustom("Form Date :".padRight(28) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(35), 0, 0);
          printBytes +=printCustom("To Date :".padRight(28) + '${printerUtilities.formatDate(reports?.toDate ?? '')}'.padLeft(35), 0, 0);
        } else {
          printBytes +=printCustom("Date :".padRight(28) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(35), 0, 0);
        }

       printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);

        if (reports?.isShort ?? false) {
        } else {
          if (isPaymentReport) {
            printBytes +=printCustom("No         ID           PaidBy      Amount(RS)", 1, 0);
          } else {
            printBytes +=printCustom("No         ID            Qty         Amount(RS)", 1, 0);
          }
          printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
          int sno = 0;
          for (Sales p in reports?.SalesReportsPrinterList ?? []) {
            sno++;
            totalAmount += double.parse(p.totalAmount ?? '0');
            if (isPaymentReport) {
              var invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
              printBytes +=printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(15) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(8) +
                      "      " +
                      "${p.paymentTypeName}".padLeft(8) +
                      "       " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(14),
                  0,
                  0);
            } else {
              var invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
             printBytes +=printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(15) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(8) +
                      "       " +
                      "${printerUtilities.checkQtyDoubleValue(p.transactionQty.toString())}".padLeft(8) +
                      "         " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(14),
                  0,
                  0);
              if (p.shiftName != null) {
                printBytes +=printCustom("${p.shiftName != null ? p.shiftName : '-'}".padRight(8), 0, 0);
              }
            }
          }
          printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        }

        if (!isPaymentReport) {
          printBytes +=printCustom("Total Sales:".padLeft(15) + "${reports?.SalesReportsPrinterList?.length}".padLeft(15), 1, 2);
          printBytes +=printCustom("Total Qty:".padLeft(15) + "${printerUtilities.checkQtyDoubleValue(reports?.totQty ?? '0')}".padLeft(15), 1, 2);
        }

        printBytes +=printCustom("Total Amount:".padLeft(15) + "RS ${double.parse(reports?.totAmount ?? '0').toStringAsFixed(2)}".padLeft(15), 1, 2);
        printBytes +=printNewLine();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes +=printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
         printBytes +=paperCut();
        }
        bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }

  fullReportsPrintTVS80MM(SalesReportsPrinter? reports, PrinterDevices? printerInfo, {bool isPaymentReport = false}) async {
    List<int> printBytes = [];
    await printerUtilities.getShopDetails();
    await bluetooth.isConnected.then((isConnected) async {
      if (isConnected ?? false) {
        double totalAmount = 0.0;
        await printerUtilities.shopNameWidget();
        printBytes +=printCustom("Sales Reports", 1, 1);
        await printerUtilities.commonShopAddress();
        if (DateTime.parse(reports?.fromDate ?? '').difference(DateTime.parse(reports?.toDate ?? '')).inDays != 0) {
          printBytes +=printCustom("Form Date :".padRight(28) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(35), 0, 0);
          printBytes +=printCustom("To Date :".padRight(28) + '${printerUtilities.formatDate(reports?.toDate ?? '')}'.padLeft(35), 0, 0);
        } else {
          printBytes +=printCustom("Date :".padRight(28) + '${printerUtilities.formatDate(reports?.fromDate ?? '')}'.padLeft(35), 0, 0);
        }
        printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);

        if (reports?.isShort ?? false) {
        } else {
          if (isPaymentReport) {
           printBytes +=printCustom("No         ID           PaidBy      Amount(RS)", 1, 0);
          } else {
           printBytes +=printCustom("No         ID            Qty         Amount(RS)", 1, 0);
          }
          printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
          int sno = 0;
          for (Sales p in reports?.SalesReportsPrinterList ?? []) {
            sno++;
            totalAmount += double.parse(p.totalAmount ?? '0');

            if (isPaymentReport) {
              String? invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
              printBytes +=printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(15) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(8) +
                      "      " +
                      "${p.paymentTypeName}".padLeft(8) +
                      "       " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(14),
                  0,
                  0);
            } else {
              var invoiceExtension = p.invoiceExtension != null ? p.invoiceExtension : "";
              printBytes +=printCustom(
                  "${!printerUtilities.isResetInvoice ? p.paymentInvoice != null ? p.paymentInvoice ?? '' + invoiceExtension! : '-' : sno}"
                          .padRight(15) +
                      "${p.invoiceId != null ? p.invoiceId : '-'}".padRight(8) +
                      "       " +
                      "${printerUtilities.checkQtyDoubleValue(p.transactionQty.toString())}".padLeft(8) +
                      "         " +
                      "${double.parse(p.totalAmount ?? '0').toStringAsFixed(2)}".padLeft(14),
                  0,
                  0);
              if (p.shiftName != null) {
                printBytes +=printCustom("${p.shiftName != null ? p.shiftName : ''}".padRight(8), 0, 0);
              }
            }
          }
         printBytes +=printCustom(printerUtilities.returnDashedLines(64), 0, 0);
        }

        if (!isPaymentReport) {
          printBytes +=printCustom("Total Sales:".padLeft(15) + "${reports?.SalesReportsPrinterList?.length}".padLeft(15), 1, 2);
          printBytes +=printCustom("Total Qty:".padLeft(15) + "${printerUtilities.checkQtyDoubleValue(reports?.totQty ?? '0')}".padLeft(15), 1, 2);
        }
        printBytes +=printCustom("Total Amount:".padLeft(15) + "RS ${double.parse(reports?.totAmount ?? '0').toStringAsFixed(2)}".padLeft(15), 1, 2);
        printBytes +=printNewLine();
        for (int i = 0; i < (printerInfo?.feedSize ?? 0); i++) {
          printBytes +=printNewLine();
        }
        if (printerInfo?.paperCut ?? false) {
          printBytes +=paperCut();
        }
        bluetooth.writeBytes(printBytes);
        if (printerUtilities.isMultiPrint) {
          await Future.delayed(Duration(milliseconds: 500));
          printerUtilities.disconnect();
        }
      }
    });
  }
}
