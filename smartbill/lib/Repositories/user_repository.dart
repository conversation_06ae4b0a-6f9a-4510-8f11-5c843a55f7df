import 'dart:convert';
// import 'dart:io'; // COMMENTED OUT FOR LOCAL MODE
import 'package:smartbill/Repositories/helper/api_helper.dart';
import 'package:smartbill/model/activation.dart';
import 'package:smartbill/model/change_password.dart';
import 'package:smartbill/model/forgot_password_model.dart';
import 'package:smartbill/model/login_request_model.dart';
import 'package:smartbill/model/register_request.dart';
import 'package:smartbill/model/register_response.dart';
import 'package:smartbill/model/user_model.dart';
// import 'package:http/http.dart' as http; // COMMENTED OUT FOR LOCAL MODE
import 'package:smartbill/model/validity.dart';
// import '../main.dart'; // COMMENTED OUT FOR LOCAL MODE
import '../model/app_info/app_info_request.dart';
import '../model/app_info/app_info_response.dart';
import '../model/common_api_response.dart';
import '../model/forget_password.dart';
// import 'environment.dart'; // COMMENTED OUT FOR LOCAL MODE

Future<RegisterResponse> register(RegisterRequest registerRequest) async {
  RegisterResponse registerResponse = new RegisterResponse();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/registration/",
      requestBody: registerRequest.toMap());

  if (response != null && response['status'] == 1) {
    registerResponse = RegisterResponse.fromJson(response);
  } else {
    registerResponse.status = 0;
    registerResponse.message = response?['message'] ?? "Failed to register";
  }
  return registerResponse;
}

Future<LoginResponse> login(LoginRequestModel user, String flag) async {
  LoginResponse responseModel = new LoginResponse();

  // LOCAL MODE: Use API helper which now handles local data
  final String endpoint = flag == "Normal" ? "/api/v1/users/authenticateusers" : "/api/v1/users/authenticateuser";
  final response = await apiHelper.commonPost(endpoint, requestBody: user.toMap());

  if (response != null && response['status'] == 1) {
    responseModel = LoginResponse.fromJson(response);
  } else {
    responseModel.message = response?['message'] ?? "Login Failed";
  }
  return responseModel;
}

Future<Validity> addDevice(Validity validity) async {
  Validity valid = new Validity();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/adddevice",
      requestBody: validity.toMap());

  if (response != null && response['status'] == 1) {
    valid.message = response['message'] ?? "Device added successfully";
  } else {
    valid.message = response?['message'] ?? "Failed to add device";
  }
  return valid;
}

Future<ChangePassword> changePassword(ChangePassword passwords) async {
  ChangePassword password = new ChangePassword();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/changepassword",
      requestBody: passwords.toMap());

  if (response != null && response['status'] == 1) {
    password = ChangePassword.fromJson(response);
  } else {
    password.message = response?['message'] ?? "Failed to change password";
  }
  return password;
}

Future<Activation1> activationByCoupenCode(Activation1 activation) async {
  Activation1 activation1 = new Activation1();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/extendvaliditybycode",
      requestBody: activation.toMap());

  if (response != null && response['status'] == 1) {
    activation1 = Activation1.fromJson(response);
  } else {
    activation1.response = new ActivationResponse();
    activation1.response?.message = response?['message'] ?? "Activation failed";
    activation1.response?.status = 0;
  }
  return activation1;
}

Future<ForgotPasswordModel> forgotPassword(ForgotPasswordModel model) async {
  ForgotPasswordModel forgotPasswordModel = ForgotPasswordModel();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/forgotpassworduser",
      requestBody: model.toJson());

  if (response != null && response['status'] == 1) {
    forgotPasswordModel = ForgotPasswordModel.fromJson(response);
  } else {
    forgotPasswordModel.message = response?['message'] ?? "Forgot password Failed";
  }
  return forgotPasswordModel;
}

Future<ProfileUpdateResponse> updateUserProfile(User requestModel) async {
  ProfileUpdateResponse responseModel = new ProfileUpdateResponse();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPut("/api/v1/users/updateuser",
      requestBody: requestModel.toJson());

  if (response != null && response['status'] == 1) {
    responseModel = ProfileUpdateResponse.fromJson(response);
  } else {
    responseModel.message = response?['message'] ?? "Failed to update profile";
  }
  return responseModel;
}

/// Send Otp api
Future<ForgotPasswordResult> sendOTPApi(String mobile) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response = await apiHelper.commonPost(
    "/api/v1/users/sendotp?mobile=$mobile",
  );
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

///Verify otp api
Future<ForgotPasswordResult> verifyOTPApi(VerifyOTP model) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response =
      await apiHelper.commonPost("/api/v1/users/forgotpasswordotpverify?mobile=${model.mobile}&otp=${model.otp}", requestBody: model.toJson());
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

///Reset password
Future<ForgotPasswordResult> resetPasswordApi(ResetPassword model) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response = await apiHelper.commonPost("/api/v1/users/forgotpassword?encryptedText=${model.encryptedText}&newPassword=${model.newPassword}",
      requestBody: model.toJson());
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

/// Verify Log in otp
Future<LoginResponse> verifyLogInOTPApi(VerifyLoginOTP model) async {
  LoginResponse responseModel = LoginResponse();
  final response = await apiHelper.commonPost("/api/v1/users/verifyloginotp", requestBody: model.toJson());
  if (response != null) {
    responseModel = LoginResponse.fromJson(response);
  }
  return responseModel;
}

Future<CommonAPIResponse> deleteAccountAPI(String username) async {
  CommonAPIResponse responseModel = CommonAPIResponse();
  final response = await apiHelper.commonPost("/api/v1/users/deleteuserbymobilenumber?mobile=$username", requestBody: {});
  if (response != null) {
    responseModel = CommonAPIResponse.fromJson(response);
  }
  return responseModel;
}

Future<GetAppInfoResponse> getAppInfoApi(AppInfoRequest model) async {
  GetAppInfoResponse responseModel = GetAppInfoResponse();
  print(json.encode(model.toJson()));
  final response = await apiHelper.commonPost("/api/v1/generalsettings/updateversion", requestBody: model.toJson());
  if (response != null) {
    responseModel = GetAppInfoResponse.fromJson(response);
  }
  return responseModel;
}

//DELETE Account
Future<CommonAPIResponse> deleteAccountByPassword(String password) async {
  CommonAPIResponse responseModel = new CommonAPIResponse();

  // LOCAL MODE: Use API helper which now handles local data
  final response = await apiHelper.commonPost("/api/v1/users/deleteaccount",
      queryParams: {"password": password});

  if (response != null && response['status'] == 1) {
    responseModel = CommonAPIResponse.fromJson(response);
  } else {
    responseModel.message = response?['message'] ?? "Failed to delete account";
  }
  return responseModel;
}

