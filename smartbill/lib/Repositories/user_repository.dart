import 'dart:convert';
import 'dart:io';
import 'package:smartbill/Repositories/helper/api_helper.dart';
import 'package:smartbill/model/activation.dart';
import 'package:smartbill/model/change_password.dart';
import 'package:smartbill/model/forgot_password_model.dart';
import 'package:smartbill/model/login_request_model.dart';
import 'package:smartbill/model/register_request.dart';
import 'package:smartbill/model/register_response.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:http/http.dart' as http;
import 'package:smartbill/model/validity.dart';
import '../main.dart';
import '../model/app_info/app_info_request.dart';
import '../model/app_info/app_info_response.dart';
import '../model/common_api_response.dart';
import '../model/forget_password.dart';
import 'environment.dart';

Future<RegisterResponse> register(RegisterRequest registerRequest) async {
  RegisterResponse registerResponse = new RegisterResponse();
  final String url = "$baseUrl/api/v1/users/registration/";
  final response = await http.post(
    Uri.parse(url),
    headers: <String, String>{
      'Content-Type': 'application/json; charset=UTF-8',
    },
    body: json.encode(registerRequest.toMap()),
  );
  if (response.statusCode == 200) {
    registerResponse = RegisterResponse.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    registerResponse.status = 0;
    registerResponse.message = "Failed to register";
  } else {
    registerResponse.message = json.decode(response.body)['message'];
  }
  return registerResponse;
}

Future<LoginResponse> login(LoginRequestModel user, String flag) async {
  //User users = new User();
  LoginResponse responseModel = new LoginResponse();
  final String url = flag == "Normal" ? "$baseUrl/api/v1/users/authenticateusers" : "$baseUrl/api/v1/users/authenticateuser";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {HttpHeaders.contentTypeHeader: 'application/json'},
    body: json.encode(user.toMap()),
  );
  if (response.statusCode == 200) {
    responseModel = LoginResponse.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Login Failed";
  }
  return responseModel;
}

Future<Validity> addDevice(Validity validity) async {
  Validity valid = new Validity();
  final String url = "$baseUrl/api/v1/users/adddevice";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "Bearer ${validity.token}",
    },
    body: json.encode(validity.toMap()),
  );
  if (response.statusCode == 200) {
    valid.message = json.decode(response.body)["message"];
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    valid.message = "Failed to add device";
  } else {
    valid.message = jsonDecode(response.body)['message'];
  }
  return valid;
}

Future<ChangePassword> changePassword(ChangePassword passwords) async {
  ChangePassword password = new ChangePassword();
  final String url = "$baseUrl/api/v1/users/changepassword";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(passwords.toMap()),
  );
  if (response.statusCode == 200) {
    password = ChangePassword.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    password.message = "Failed to change password";
  } else {
    password.message = jsonDecode(response.body)['message'];
  }
  return password;
}

Future<Activation1> activationByCoupenCode(Activation1 activation) async {
  Activation1 activation1 = new Activation1();
  final String url = "$baseUrl/api/v1/users/extendvaliditybycode";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(activation.toMap()),
  );
  if (response.statusCode == 200) {
    activation1 = Activation1.fromJson(json.decode(response.body));
  } else {
    activation1.response = new ActivationResponse();
    activation1.response?.message = "Activation failed";
    activation1.response?.status = 0;
  }
  return activation1;
}

Future<ForgotPasswordModel> forgotPassword(ForgotPasswordModel model) async {
  ForgotPasswordModel forgotPasswordModel = ForgotPasswordModel();
  final String url = "$baseUrl/api/v1/users/forgotpassworduser";
  final client = new http.Client();
  final response = await client.post(
    Uri.parse(url),
    headers: {
      HttpHeaders.contentTypeHeader: 'application/json',
    },
    body: json.encode(
      model.toJson(),
    ),
  );
  if (response.statusCode == 200) {
    forgotPasswordModel = ForgotPasswordModel.fromJson(json.decode(response.body));
  } else {
    forgotPasswordModel.message = "Forgot password Failed";
  }
  return forgotPasswordModel;
}

Future<ProfileUpdateResponse> updateUserProfile(User requestModel) async {
  ProfileUpdateResponse responseModel = new ProfileUpdateResponse();
  final String url = "$baseUrl/api/v1/users/updateuser";
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = ProfileUpdateResponse.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to update profile";
  }
  return responseModel;
}

/// Send Otp api
Future<ForgotPasswordResult> sendOTPApi(String mobile) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response = await apiHelper.commonPost(
    "/api/v1/users/sendotp?mobile=$mobile",
  );
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

///Verify otp api
Future<ForgotPasswordResult> verifyOTPApi(VerifyOTP model) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response =
      await apiHelper.commonPost("/api/v1/users/forgotpasswordotpverify?mobile=${model.mobile}&otp=${model.otp}", requestBody: model.toJson());
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

///Reset password
Future<ForgotPasswordResult> resetPasswordApi(ResetPassword model) async {
  ForgotPasswordResult responseModel = ForgotPasswordResult();
  final response = await apiHelper.commonPost("/api/v1/users/forgotpassword?encryptedText=${model.encryptedText}&newPassword=${model.newPassword}",
      requestBody: model.toJson());
  if (response != null) {
    responseModel = ForgotPasswordResult.fromJson(response);
  }
  return responseModel;
}

/// Verify Log in otp
Future<LoginResponse> verifyLogInOTPApi(VerifyLoginOTP model) async {
  LoginResponse responseModel = LoginResponse();
  final response = await apiHelper.commonPost("/api/v1/users/verifyloginotp", requestBody: model.toJson());
  if (response != null) {
    responseModel = LoginResponse.fromJson(response);
  }
  return responseModel;
}

Future<CommonAPIResponse> deleteAccountAPI(String username) async {
  CommonAPIResponse responseModel = CommonAPIResponse();
  final response = await apiHelper.commonPost("/api/v1/users/deleteuserbymobilenumber?mobile=$username", requestBody: {});
  if (response != null) {
    responseModel = CommonAPIResponse.fromJson(response);
  }
  return responseModel;
}

Future<GetAppInfoResponse> getAppInfoApi(AppInfoRequest model) async {
  GetAppInfoResponse responseModel = GetAppInfoResponse();
  print(json.encode(model.toJson()));
  final response = await apiHelper.commonPost("/api/v1/generalsettings/updateversion", requestBody: model.toJson());
  if (response != null) {
    responseModel = GetAppInfoResponse.fromJson(response);
  }
  return responseModel;
}

//DELETE Account
Future<CommonAPIResponse> deleteAccountByPassword(String password) async {
  CommonAPIResponse responseModel = new CommonAPIResponse();
  final String url = "$baseUrl/api/v1/users/deleteaccount?password=$password";
  final response = await http.delete(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = CommonAPIResponse.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to delete member";
  }
  return responseModel;
}

