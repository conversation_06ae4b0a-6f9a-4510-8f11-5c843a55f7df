import 'dart:convert';
import 'dart:io';
import 'package:smartbill/Repositories/helper/api_helper.dart';
import 'package:smartbill/model/add_workspace.dart';
import 'package:smartbill/model/add_workspace_member.dart';
import 'package:smartbill/model/check_user_password.dart';
import 'package:smartbill/model/extend_validity.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/model/invoice_prefix_model.dart';
import 'package:http/http.dart' as http;
import 'package:smartbill/model/workspace_by_id.dart';
import 'package:smartbill/model/workspace_member.dart';  
import '../main.dart';
import '../model/get_shop_type.dart';
import 'environment.dart';

Future<AddWorkspace> addWorkspace(AddWorkspace requestModel) async {
  AddWorkspace responseModel = new AddWorkspace();
  final String url = "$baseUrl/api/v3/workspaces/addworkspace";
  final response = await http.post(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = AddWorkspace.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to add shops";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to add shops";
  }
  return responseModel;
}

Future<AddWorkSpaceMember> addWorkspaceMember(AddWorkSpaceMember requestModel) async {
  AddWorkSpaceMember responseModel = new AddWorkSpaceMember();
  final String url = "$baseUrl/api/v1/workspaces/addworkspacemember";
  final response = await http.post(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = AddWorkSpaceMember.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to add member";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to add member";
  }
  return responseModel;
}

Future<GetWorkspaceList> getWorkspaceListByUser() async {
  GetWorkspaceList responseModel = new GetWorkspaceList();
  final String url = "$baseUrl/api/v1/workspaces/getworkspacesbyuser";
  final client = new http.Client();
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = GetWorkspaceList.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to get shops";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to get shops";
  }
  return responseModel;
}

Future<GetWorkspaceList> getWorkspaceListByAdmin() async {
  GetWorkspaceList responseModel = new GetWorkspaceList();
  final String url = "$baseUrl/api/v1/workspaces/getworkspacesbyadminuser";
  final client = new http.Client();
  final response = await client.get(
    Uri.parse(url),
    headers: {
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = GetWorkspaceList.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to get shops";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to get shops";
  }
  return responseModel;
}

Future<CheckUserPassword> checkUserPassword(CheckUserPassword requestModel) async {
  CheckUserPassword responseModel = new CheckUserPassword();
  final String url = "$baseUrl/api/v1/users/checkpassword";
  final response = await http.post(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = CheckUserPassword.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to sign in";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to sign in";
  }
  return responseModel;
}

Future<ExtendValidity> extendValidity(ExtendValidity requestModel) async {
  ExtendValidity responseModel = new ExtendValidity();
  final String url = "$baseUrl/api/v1/users/extendvaliditybycode";
  final response = await http.post(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = ExtendValidity.fromJson(json.decode(response.body));
  } else {
    responseModel.response?.message = "Activation failed";
  }
  return responseModel;
}

Future<WorkspaceById> getWorkspaceById(id) async {
  WorkspaceById responseModel = new WorkspaceById();
  final String url = "$baseUrl/api/v1/workspaces/getworkspacesbyid?workspaceId=${id}";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = WorkspaceById.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to get shops";
  }
  return responseModel;
}

Future<Prefix> getPrefixById(id) async {
  Prefix responseModel = new Prefix();
  final String url = "$baseUrl/api/v1/workspaces/getinvoiceprefix?workspaceID=${id}";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = Prefix.fromJson(json.decode(response.body)["result"]);
  } else {
    responseModel.message = "Failed to get prefix id.";
  }
  return responseModel;
}

//API CALL FOR MEMBER LIST
Future<GetWorkspaceMember> getWorkspaceMember(id) async {
  GetWorkspaceMember responseModel = new GetWorkspaceMember();
  final String url = "$baseUrl/api/v1/workspaces/getworkspacemembersbyid?workspaceId=${id}";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = GetWorkspaceMember.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to get members.";
  }
  return responseModel;
}

//UPDATE WORKSPACE
Future<WorkSpace> updateWorkspaceName(WorkSpace requestModel) async {
  WorkSpace responseModel = WorkSpace();
  final String url = "$baseUrl/api/v1/workspaces/updateworkspace";
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = WorkSpace.fromJson(json.decode(response.body));
  } else if (response.statusCode == 400 || response.statusCode == 401) {
    responseModel.status = 0;
    responseModel.message = "Failed to update shop";
  } else {
    responseModel.status = 0;
    responseModel.message = "Failed to update shop";
  }
  return responseModel;
}

//DELETE WORKSPACE
Future<WorkSpace> deleteWorkspace(String workspaceID) async {
  WorkSpace responseModel = WorkSpace();
  final String url = "$baseUrl/api/v1/workspaces/deleteworkspacesbyid?workspaceID=$workspaceID";
  final response = await http.delete(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = WorkSpace.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to delete shop";
  }
  return responseModel;
}

//DELETE WORKSPACE ALL RECORDS
Future<WorkSpace> resetWorkspace(String workspaceID) async {
  WorkSpace responseModel = WorkSpace();
  final String url = "$baseUrl/api/v1/workspaces/deleteworkspace?WorkspaceID=$workspaceID";
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = WorkSpace.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to delete shop records";
  }
  return responseModel;
}

//DELETE WORKSPACE SALES AND TRANSACTION
Future<WorkSpace> resetSalesByWorkspace(String workspaceID) async {
  WorkSpace responseModel = WorkSpace();
  final String url = "$baseUrl/api/v1/workspaces/deletesales?WorkspaceID=$workspaceID";
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
  );
  if (response.statusCode == 200) {
    responseModel = WorkSpace.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to delete sales";
  }
  return responseModel;
}

///Get shop type Api
Future<GetShopType> getShopTypeAPI() async {
  GetShopType responseModel = GetShopType();
  final response = await apiHelper.commonPost("/api/v1/sync/getshoptypes");
  if (response != null) {
    responseModel = GetShopType.fromJson(response);
  }
  return responseModel;
}
