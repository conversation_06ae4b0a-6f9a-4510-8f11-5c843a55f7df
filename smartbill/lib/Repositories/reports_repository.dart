import 'package:smartbill/model/get_transactions_fromapi.dart';
import 'package:smartbill/model/reports/get_payment_reports.dart';
import 'package:smartbill/model/reports/get_product_reports.dart';
import 'package:smartbill/model/reports/get_reports.dart';
import 'package:smartbill/model/reports/get_sales_category_report.dart';
import 'package:smartbill/model/reports/get_table_reports.dart';
import 'package:smartbill/model/reports/get_tax_reports.dart';
import 'package:smartbill/model/reports/get_user_reports.dart';
import 'package:smartbill/model/transaction_model.dart';
import 'package:smartbill/screens/expenses/model/get_expense_transaction_byId.dart';
import '../main.dart';
import '../model/expense_dashboard_model.dart';
import '../model/payment_category_model.dart';
import '../model/reports/get_discount_reports.dart';
import '../model/reports/get_expenses_report.dart';
import '../model/reports/get_purchase_reports.dart';
import '../model/reports/get_stock_reports.dart';
import '../screens/expenses/model/get_purchase_transaction_byId.dart';
import 'helper/api_helper.dart';

Future<GetReportsFromApi> getSalesReportByDatToDate(fromDate, toDate, {String? paymentCategoryID}) async {
  GetReportsFromApi responseModel = GetReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'toDate': toDate,
    'paymentCategoryId': paymentCategoryID
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v3/sales/getsalesbyworksapceid?", queryParams: params);
  if (response != null) {
    responseModel = GetReportsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<GetReportsFromApi> getDeltedSalesReportByDatToDate(fromDate, toDate, {String? paymentCategoryID}) async {
  GetReportsFromApi responseModel = GetReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'paymentCategoryID': paymentCategoryID,
    'fromDate': fromDate,
    'toDate': toDate
  }.map((key, value) => MapEntry(key, value.toString()));

  final response = await apiHelper.commonGet("/api/v2/sales/getdeletesalesbyworksapceid?", queryParams: params);
  if (response != null) {
    responseModel = GetReportsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<GetTransactionsFromApi> getTransactionsByDatToDate(fromDate, toDate, {paymentId = ""}) async {
  GetTransactionsFromApi responseModel = GetTransactionsFromApi();
  final response = await apiHelper
      .commonGet("/api/v1/sales/gettransactionsbyworkspaceid?workspaceID=${MyApp.activeWorkspace.workspaceId}&fromDate=$fromDate&ToDate=$toDate");
  if (response != null) {
    responseModel = GetTransactionsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<GetTransactionsFromApi> getTransactionDetailsBySalesID(String salesID) async {
  GetTransactionsFromApi responseModel = GetTransactionsFromApi();
  final response = await apiHelper.commonGet("/api/v1/sales/getsalestransactionbysalesid?SalesID=$salesID");
  if (response != null) {
    responseModel = GetTransactionsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<TransactionGetModel> getTransactionBySalesID(String salesID) async {
  TransactionGetModel responseModel = TransactionGetModel();
  final response =
      await apiHelper.commonGet("/api/v9/sync/getsalestransactionbysalesid?workspaceID=${MyApp.activeWorkspace.workspaceId}&salesID=$salesID");
  if (response != null) {
    responseModel = TransactionGetModel.fromJson(response);
  }
  return responseModel;
}

Future<GetProductReportsFromApi> getProductsReportByDatToDate(fromDate, toDate, {productId}) async {
  GetProductReportsFromApi responseModel = GetProductReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'productId': productId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getallproductdetailsbyproductid?", queryParams: params);
  if (response != null) {
    responseModel = GetProductReportsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<GetPaymentReportsFromApi> getPaymentReportByDatToDate(fromDate, toDate, {paymentId = ""}) async {
  GetPaymentReportsFromApi responseModel = GetPaymentReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'paymentType': paymentId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v2/sales/getallpaymentsalesbypaymentid?", queryParams: params);
  if (response != null) {
    responseModel = GetPaymentReportsFromApi.fromJson(response);
  }
  return responseModel;
}

///Get Sales category reports from api
Future<GetSalesCategoryReportsFromAPI> getSalesCategoryReportByDateToDate(fromDate, toDate, {salesCategoryId = ""}) async {
  GetSalesCategoryReportsFromAPI responseModel = GetSalesCategoryReportsFromAPI();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'salesCategoryID': salesCategoryId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getsalesbysalescategoryid?", queryParams: params);
  if (response != null) {
    responseModel = GetSalesCategoryReportsFromAPI.fromJson(response);
  }
  return responseModel;
}

Future<GetUserReportsFromApi> getUserReportByDatToDate(fromDate, toDate, {userId}) async {
  GetUserReportsFromApi responseModel = GetUserReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'createdBy': userId ?? '',
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getsalesandtransactionsbycreatedby?", queryParams: params);
  if (response != null) {
    responseModel = GetUserReportsFromApi.fromJson(response);
  }
  return responseModel;
}

/// Get table reports
Future<GetTableReportsFromApi> getTableReportsById(fromDate, toDate, {tableId}) async {
  GetTableReportsFromApi responseModel = GetTableReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'tableID': tableId ?? '',
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getproductsdetailsbytableid", queryParams: params);
  if (response != null) {
    responseModel = GetTableReportsFromApi.fromJson(response);
  }
  return responseModel;
}

/// Get tax reports
Future<GetTaxReportsFromApi> getTaxReportsByDate(
  fromDate,
  toDate,
) async {
  GetTaxReportsFromApi responseModel = GetTaxReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/gettaxesbyworkspaceid", queryParams: params);
  if (response != null) {
    responseModel = GetTaxReportsFromApi.fromJson(response);
  }
  return responseModel;
}

/// Get stock reports
Future<GetStockReportFromAPI> getStockReports({productId}) async {
  GetStockReportFromAPI responseModel = GetStockReportFromAPI();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    //'productId':productId
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getallstocksbyworkspaceid", queryParams: params);
  if (response != null) {
    responseModel = GetStockReportFromAPI.fromJson(response);
  }
  return responseModel;
}

/// Get discount reports
Future<GetDiscountReports> getDiscountReports(
  fromDate,
  toDate,
) async {
  GetDiscountReports responseModel = GetDiscountReports();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getdiscountbyworkspaceid", queryParams: params);
  if (response != null) {
    responseModel = GetDiscountReports.fromJson(response);
  }
  return responseModel;
}

///To get purchase reports
Future<GetPurchaseReports> getPurchaseReportByDatToDate(fromDate, toDate, {purchaseProductId = ""}) async {
  GetPurchaseReports responseModel = GetPurchaseReports();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getallpurchasereport?", queryParams: params);
  if (response != null) {
    responseModel = GetPurchaseReports.fromJson(response);
  }
  return responseModel;
}

// get reports for expenses
Future<GetExpenseReportFromApi> getExpenseReportByDatToDate(fromDate, toDate) async {
  GetExpenseReportFromApi responseModel = GetExpenseReportFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getallexpensesreport?", queryParams: params);
  if (response != null) {
    responseModel = GetExpenseReportFromApi.fromJson(response);
  }
  return responseModel;
}

///To get purchase reports
Future<ExpenseDashboardModel> getExpenseDashboardReport(fromDate, toDate) async {
  ExpenseDashboardModel responseModel = ExpenseDashboardModel();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getexpensedashboardreport?", queryParams: params);
  if (response != null) {
    responseModel = ExpenseDashboardModel.fromJson(response);
  }
  return responseModel;
}

Future<GetExpenseTransactionById> getExpenseTransDetailsByExpId(String expId) async {
  GetExpenseTransactionById responseModel = GetExpenseTransactionById();
  final response = await apiHelper.commonGet("/api/v1/sales/getexpensetransactionbyexpenseid?expenseID=$expId");
  if (response != null) {
    responseModel = GetExpenseTransactionById.fromJson(response);
  }
  return responseModel;
}

Future<GetPurchaseTransactionById> getPurchaseTransDetailsByExpId(String PurchaseId) async {
  GetPurchaseTransactionById responseModel = GetPurchaseTransactionById();
  final response = await apiHelper.commonGet("/api/v1/sales/getpurchasetransactionbypurchaseid?purchaseID=$PurchaseId");
  if (response != null) {
    responseModel = GetPurchaseTransactionById.fromJson(response);
  }
  return responseModel;
}

Future<PaymentCategoryModel> getAllPaymentCategoryAPI() async {
  PaymentCategoryModel responseModel = PaymentCategoryModel();
  final response = await apiHelper.commonGet("/api/v1/paymentmethods/getallpaymentcategories");

  if (response != null) {
    // responseModel = PaymentCategoryModel.fromJson(response);
    responseModel = PaymentCategoryModel.fromJson(response);
  }
  return responseModel;
}

Future<GetPaymentReportsFromApi> getShiftReportByDatToDate(fromDate, toDate, {shiftId = ""}) async {
  GetPaymentReportsFromApi responseModel = GetPaymentReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'shiftTypeID': shiftId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonGet("/api/v1/sales/getallshiftwisesales?", queryParams: params);
  if (response != null) {
    responseModel = GetPaymentReportsFromApi.fromJson(response);
  }
  return responseModel;
}

Future<GetPaymentReportsFromApi> getCustomerReportByDatToDate(fromDate, toDate, {customerId = ""}) async {
  GetPaymentReportsFromApi responseModel = GetPaymentReportsFromApi();
  Map<String, String> params = {
    'workspaceID': MyApp.activeWorkspace.workspaceId,
    'CustomerID': customerId,
    'fromDate': fromDate,
    'ToDate': toDate,
  }.map((key, value) => MapEntry(key, value.toString()));
  final response = await apiHelper.commonPost("/api/v1/sales/getcustomersalesreport?", queryParams: params);
  if (response != null) {
    responseModel = GetPaymentReportsFromApi.fromJson(response);
  }
  return responseModel;
}
