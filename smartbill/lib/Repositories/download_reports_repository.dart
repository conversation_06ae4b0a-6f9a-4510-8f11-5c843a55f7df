import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:open_filex/open_filex.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share/share.dart';
import 'package:smartbill/utils/common_function/printer_utilities.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../main.dart';
import '../model/reports/download_reports_request.dart';
import '../utils/common_widgets/snack_bar_widget.dart';
import '../utils/constants/strings_keys.dart';
import 'environment.dart';

double width = 0.0;
Future downloadReportsAPI(
  DownloadReportsRequest requestModel,
  String downloadUrl,
  String fileName,
  BuildContext context, {
  bool isSharePDF = false,
}) async {
  try {
    width = MediaQuery.of(context).size.width;
    Dio dio = Dio();
    var url = '$baseUrl$downloadUrl';

    Response response = await dio.post(url,
        data: requestModel,
        onReceiveProgress: showDownloadProgress,
        //Received data with List<int>
        options: Options(
            responseType: ResponseType.bytes,
            followRedirects: true,
            validateStatus: (status) {
              return status! < 500;
            },
            headers: <String, String>{
              HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
            }));
    try {
      Directory? dir;
      //      if (Platform.isWindows) {
      //   return await getDownloadsDirectory();
      // } else if (Platform.isAndroid) {
      //   final dir = Directory('/storage/emulated/0/Download/');
      //   return await dir.exists() ? dir : await getExternalStorageDirectory();
      // } else if (Platform.isIOS) {
      //   return await getApplicationDocumentsDirectory();
      // }
      if (Platform.isWindows) {
        dir = await getDownloadsDirectory();
      } else if (Platform.isAndroid) {
        dir = Directory('/storage/emulated/0/Download');
        if (!await dir.exists()) {
          dir = await getExternalStorageDirectory();
        }
      } else if (Platform.isIOS) {
        dir = await getApplicationDocumentsDirectory();
      }

      var currentDate = DateTime.now().toString().replaceAll(".", "").replaceAll(" ", "").replaceAll(":", "");
      String fileFullName = '${fileName}_$currentDate.pdf';
      String savePath = dir!.path + '/' + fileFullName;

      if (response.statusCode == 200 && response.data.length > 0) {
        File file = await File(savePath).create(recursive: true);
        var raf = file.openSync(mode: FileMode.write);
        raf.writeFromSync(response.data);
        await raf.close();
        if (file.existsSync()) {
          if (isSharePDF) {
            Share.shareFiles([file.path], text: appName);
          } else {
            if (Platform.isAndroid) {
              PrinterUtilities printerUtilities = PrinterUtilities();
              printerUtilities.showSimpleNotification(fileName, file.path);
            }
            // showToast("Saved to downloads");
            showCommonSnackBar(context, () {
              OpenFilex.open(file.path);
            });
          }
        }
      } else {
        showToast(response.statusMessage ?? "Failed to download reports");
      }
    } catch (e) {
      showToast("Failed to download reports");
    }
  } on DioError catch (e) {
    showToast("Please turn on network or wifi");
  }
}

void showDownloadProgress(received, total) {
  if (total != -1) {
    //print((received / total * 100).toStringAsFixed(0) + "%");
  }
}
