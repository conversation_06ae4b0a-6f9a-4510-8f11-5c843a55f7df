import 'dart:convert';
import 'dart:io';

import 'package:smartbill/model/delete_member_model.dart';
import 'package:smartbill/model/permissions.dart';
import 'package:smartbill/model/update_permission_model.dart';
import 'package:smartbill/model/user_roles.dart';

import '../main.dart';
import 'environment.dart';
import 'package:http/http.dart' as http;

Future<GetRoles> getUserRoles() async {
  GetRoles responseModel = new GetRoles();
  final String url = "$baseUrl/api/v1/roles/getallroles";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = GetRoles.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to get roles.";
  }
  return responseModel;
}

//GET ALL USER PERMISSIONS BY ROLE ID
Future<GetPermissions> getUserPermissions(String roleId) async {
  GetPermissions responseModel = new GetPermissions();
  final String url = "$baseUrl/api/v1/roles/getpermissionbyroleid?roleId=${roleId}";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = GetPermissions.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to get permissions.";
  }
  return responseModel;
}

//GET ALL USER PERMISSIONS
Future<GetPermissions> getAllUserPermissions() async {
  GetPermissions responseModel = new GetPermissions();
  final String url = "$baseUrl/api/v1/roles/getallpermission";
  final response = await http.get(Uri.parse(url), headers: <String, String>{
    HttpHeaders.contentTypeHeader: 'application/json',
    HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
  });
  if (response.statusCode == 200) {
    responseModel = GetPermissions.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to get permissions.";
  }
  return responseModel;
}

//UPDATE MEMBER ROLE API
Future<UpdateMember> updateWorkspaceMember(int memberId, UpdateMember requestModel) async {
  UpdateMember responseModel = new UpdateMember();
  final String url = "$baseUrl/api/v1/workspaces/updateworkspacememberbymemberid?memberId=${memberId}";
  final response = await http.put(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = UpdateMember.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to update member";
  }
  return responseModel;
}

//DELETE MEMBER
Future<DeleteMember> deleteWorkspaceMember(DeleteMember requestModel) async {
  DeleteMember responseModel = new DeleteMember();
  final String url = "$baseUrl/api/v1/workspaces/deleteworkspacemembersbyid";
  final response = await http.delete(
    Uri.parse(url),
    headers: <String, String>{
      HttpHeaders.contentTypeHeader: 'application/json',
      HttpHeaders.authorizationHeader: "${MyApp.activeUser.token}",
    },
    body: json.encode(requestModel.toJson()),
  );
  if (response.statusCode == 200) {
    responseModel = DeleteMember.fromJson(json.decode(response.body));
  } else {
    responseModel.message = "Failed to delete member";
  }
  return responseModel;
}
