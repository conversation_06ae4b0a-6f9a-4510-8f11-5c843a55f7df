import 'dart:convert';
import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

/// Local Data Service for managing JSON data files
/// This service replaces API calls with local JSON data operations
class LocalDataService {
  static final LocalDataService _instance = LocalDataService._internal();
  factory LocalDataService() => _instance;
  LocalDataService._internal();

  // Cache for loaded JSON data
  final Map<String, dynamic> _dataCache = {};
  
  /// Initialize the local data service
  Future<void> initialize() async {
    await _copyAssetsToLocal();
  }

  /// Copy JSON assets to local storage for read/write operations
  Future<void> _copyAssetsToLocal() async {
    final directory = await getApplicationDocumentsDirectory();
    final localDataDir = Directory('${directory.path}/local_data');
    
    if (!await localDataDir.exists()) {
      await localDataDir.create(recursive: true);
    }

    // List of JSON files to copy from assets
    final jsonFiles = [
      'users.json',
      'workspaces.json', 
      'products.json',
      'customers.json',
      'sales.json',
      'transactions.json',
      'reports.json',
      'payment_types.json',
      'areas.json',
      'discounts.json',
      'stocks.json',
      'settings.json',
      'subscriptions.json',
      'permissions.json',
      'roles.json',
    ];

    for (String fileName in jsonFiles) {
      final localFile = File('${localDataDir.path}/$fileName');
      if (!await localFile.exists()) {
        try {
          final assetData = await rootBundle.loadString('assets/local_data/$fileName');
          await localFile.writeAsString(assetData);
        } catch (e) {
          // If asset doesn't exist, create empty structure
          await _createEmptyJsonFile(localFile, fileName);
        }
      }
    }
  }

  /// Create empty JSON file with basic structure
  Future<void> _createEmptyJsonFile(File file, String fileName) async {
    Map<String, dynamic> emptyStructure = {};
    
    switch (fileName) {
      case 'users.json':
        emptyStructure = {
          'status': 1,
          'message': 'Success',
          'users': []
        };
        break;
      case 'workspaces.json':
        emptyStructure = {
          'status': 1,
          'message': 'Success',
          'workspaces': []
        };
        break;
      default:
        emptyStructure = {
          'status': 1,
          'message': 'Success',
          'data': []
        };
    }
    
    await file.writeAsString(json.encode(emptyStructure));
  }

  /// Load JSON data from local file
  Future<Map<String, dynamic>> loadJsonData(String fileName) async {
    if (_dataCache.containsKey(fileName)) {
      return _dataCache[fileName];
    }

    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/local_data/$fileName');
    
    if (await file.exists()) {
      final jsonString = await file.readAsString();
      final data = json.decode(jsonString);
      _dataCache[fileName] = data;
      return data;
    }
    
    return {'status': 0, 'message': 'File not found', 'data': []};
  }

  /// Save JSON data to local file
  Future<void> saveJsonData(String fileName, Map<String, dynamic> data) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/local_data/$fileName');
    
    await file.writeAsString(json.encode(data));
    _dataCache[fileName] = data; // Update cache
  }

  /// Add item to JSON array
  Future<void> addToJsonArray(String fileName, String arrayKey, Map<String, dynamic> item) async {
    final data = await loadJsonData(fileName);
    if (data[arrayKey] is List) {
      (data[arrayKey] as List).add(item);
      await saveJsonData(fileName, data);
    }
  }

  /// Update item in JSON array by ID
  Future<void> updateInJsonArray(String fileName, String arrayKey, String idKey, dynamic id, Map<String, dynamic> updatedItem) async {
    final data = await loadJsonData(fileName);
    if (data[arrayKey] is List) {
      final list = data[arrayKey] as List;
      final index = list.indexWhere((item) => item[idKey] == id);
      if (index != -1) {
        list[index] = updatedItem;
        await saveJsonData(fileName, data);
      }
    }
  }

  /// Delete item from JSON array by ID
  Future<void> deleteFromJsonArray(String fileName, String arrayKey, String idKey, dynamic id) async {
    final data = await loadJsonData(fileName);
    if (data[arrayKey] is List) {
      final list = data[arrayKey] as List;
      list.removeWhere((item) => item[idKey] == id);
      await saveJsonData(fileName, data);
    }
  }

  /// Find item in JSON array by ID
  Future<Map<String, dynamic>?> findInJsonArray(String fileName, String arrayKey, String idKey, dynamic id) async {
    final data = await loadJsonData(fileName);
    if (data[arrayKey] is List) {
      final list = data[arrayKey] as List;
      try {
        return list.firstWhere((item) => item[idKey] == id);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// Filter items in JSON array
  Future<List<dynamic>> filterJsonArray(String fileName, String arrayKey, bool Function(dynamic) predicate) async {
    final data = await loadJsonData(fileName);
    if (data[arrayKey] is List) {
      final list = data[arrayKey] as List;
      return list.where(predicate).toList();
    }
    return [];
  }

  /// Clear cache
  void clearCache() {
    _dataCache.clear();
  }

  /// Generate unique ID
  String generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  /// Get current timestamp
  String getCurrentTimestamp() {
    return DateTime.now().toIso8601String();
  }
}
