import 'dart:convert';
import 'dart:developer';
import 'package:smartbill/model/sync_request_model.dart';
import 'package:smartbill/model/sync_response_model.dart';
import '../main.dart';
import 'helper/api_helper.dart';

Future<GetSyncResponse> postSyncApi(String syncType, SyncRequest model, {int pageNo = 0}) async {
  GetSyncResponse responseModel = GetSyncResponse();
  final queryParameters = {
    'SyncType': syncType,
    'pageNo': pageNo.toString(),
  };

  var test = json.encode(model.toJson());
  log(test);
  print(MyApp.activeUser.token);
  final response = await apiHelper.commonPost("/api/v22/sync/sync", requestBody: model.toJson(), queryParams: queryParameters);
  if (response != null) {
    responseModel = GetSyncResponse.fromJson(response);
  }
  return responseModel;
}   

Future<GetSyncResponse> getSyncApi(String filter, String? syncType, String? lastCommitID, {int pageNo = 0}) async {
  GetSyncResponse responseModel = GetSyncResponse();
  {
    Map<String, String>? queryParameters = {
    'workspaceID': MyApp.activeWorkspace.workspaceId ?? "",
    'filter': filter,
    'SyncType': syncType ?? "",
    'lastCommitID': lastCommitID ?? "",
    'pageNo': pageNo.toString(),
  };
  final response = await apiHelper.commonGet("/api/v19/sync/getsync", queryParams: queryParameters);
  if (response != null) {
    responseModel = GetSyncResponse.fromJson(response);
  }

  return responseModel;
}
}
