import 'package:smartbill/model/subscriptions/get_all_subscriptions.dart';
import 'package:smartbill/utils/razorpay/model/generate_order_id.dart';
import 'package:smartbill/utils/razorpay/model/payment_success_callback.dart';
import 'package:smartbill/utils/razorpay/model/payment_success_callback_response.dart';
import 'helper/api_helper.dart';

Future<GetSubscriptionPlans> getAllSubscriptionPlans() async {
  GetSubscriptionPlans responseModel = GetSubscriptionPlans();
  final response = await apiHelper.commonGet("/api/v1/plans/getallplans");
  if (response != null) {
    responseModel = GetSubscriptionPlans.fromJson(response);
  }
  return responseModel;
}

Future<GenerateOrderIDResponse> generateOrderId(GenerateOrderID model) async {
  GenerateOrderIDResponse responseModel = GenerateOrderIDResponse();
  final response = await apiHelper.commonPost("/api/v1/orders/generateorderid",
      requestBody: model.toJson());
  if (response != null) {
    responseModel = GenerateOrderIDResponse.fromJson(response);
  }
  return responseModel;
}

Future<PaymentSuccessCallBackRes> paymentSuccessCallBack(
    PaymentSuccessCallBack model) async {
  PaymentSuccessCallBackRes responseModel = PaymentSuccessCallBackRes();
  final response = await apiHelper.commonPost(
      "/api/v1/orders/paymentsuccesscallback",
      requestBody: model.toJson());
  if (response != null) {
    responseModel = PaymentSuccessCallBackRes.fromJson(response);
  }
  return responseModel;
}
