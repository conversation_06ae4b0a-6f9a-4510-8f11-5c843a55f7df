// COMMENTED OUT FOR LOCAL DEVELOPMENT - NO EXTERNAL API CALLS
// Original API helper code has been replaced with local data service

// LOCAL DEVELOPMENT IMPORTS
import 'dart:convert';
import 'package:nb_utils/nb_utils.dart';
import 'package:smartbill/utils/common_widgets/widgets.dart';
import '../local_data/local_data_service.dart';
import '../environment.dart';

// Local API Helper for offline development
ApiBaseHelper apiHelper = ApiBaseHelper();

class ApiBaseHelper {
  final LocalDataService _localDataService = LocalDataService();

  /// Simulate GET request with local data
  Future<dynamic> commonGet(String subUrl, {Map<String, String>? queryParams}) async {
    try {
      // Check if we're in local mode
      if (baseUrl == "LOCAL_MODE") {
        return await _handleLocalGet(subUrl, queryParams);
      }

      // For non-local mode, return error (since we've disabled external APIs)
      return {
        'status': 0,
        'message': 'External API calls are disabled in local mode'
      };
    } catch (e) {
      log('Local GET error: $e');
      return {
        'status': 0,
        'message': 'Local data error: ${e.toString()}'
      };
    }
  }

  /// Simulate POST request with local data
  Future<dynamic> commonPost(String subUrl,
      {Map<String, dynamic>? requestBody,
      Map<String, String>? queryParams,
      List<Map<String, dynamic>>? arrayRequestBody,
      isArrayRequest = false}) async {
    try {
      // Check if we're in local mode
      if (baseUrl == "LOCAL_MODE") {
        return await _handleLocalPost(subUrl, requestBody, queryParams, arrayRequestBody, isArrayRequest);
      }

      // For non-local mode, return error (since we've disabled external APIs)
      return {
        'status': 0,
        'message': 'External API calls are disabled in local mode'
      };
    } catch (e) {
      log('Local POST error: $e');
      return {
        'status': 0,
        'message': 'Local data error: ${e.toString()}'
      };
    }
  }




  /// Simulate PUT request with local data
  Future<dynamic> commonPut(String subUrl,
      {Map<String, dynamic>? requestBody,
      Map<String, String>? queryParams}) async {
    try {
      // Check if we're in local mode
      if (baseUrl == "LOCAL_MODE") {
        return await _handleLocalPut(subUrl, requestBody, queryParams);
      }

      // For non-local mode, return error (since we've disabled external APIs)
      return {
        'status': 0,
        'message': 'External API calls are disabled in local mode'
      };
    } catch (e) {
      log('Local PUT error: $e');
      return {
        'status': 0,
        'message': 'Local data error: ${e.toString()}'
      };
    }
  }

  /// Handle local GET requests by routing to appropriate JSON files
  Future<dynamic> _handleLocalGet(String subUrl, Map<String, String>? queryParams) async {
    await _localDataService.initialize();

    // Route based on URL patterns
    if (subUrl.contains('/api/v1/users/authenticateuser')) {
      return await _handleLogin(queryParams);
    } else if (subUrl.contains('/sync/sync')) {
      return await _handleSync(queryParams);
    } else if (subUrl.contains('/sales/getsalesbyworksapceid')) {
      return await _handleGetSales(queryParams);
    } else if (subUrl.contains('/getallplans')) {
      return await _handleGetSubscriptionPlans();
    } else if (subUrl.contains('/getallroles')) {
      return await _handleGetRoles();
    } else if (subUrl.contains('/getallpermission')) {
      return await _handleGetPermissions();
    } else if (subUrl.contains('/getallpaymentcategories')) {
      return await _handleGetPaymentCategories();
    }

    // Default response for unhandled endpoints
    return {
      'status': 1,
      'message': 'Success',
      'data': []
    };
  }

  /// Handle local POST requests
  Future<dynamic> _handleLocalPost(String subUrl, Map<String, dynamic>? requestBody,
      Map<String, String>? queryParams, List<Map<String, dynamic>>? arrayRequestBody, bool isArrayRequest) async {
    await _localDataService.initialize();

    // Route based on URL patterns
    if (subUrl.contains('/sync/sync')) {
      return await _handleSyncPost(requestBody, queryParams);
    } else if (subUrl.contains('/users/registration')) {
      return await _handleUserRegistration(requestBody);
    } else if (subUrl.contains('/addworkspace')) {
      return await _handleAddWorkspace(requestBody);
    }

    // Default success response for POST
    return {
      'status': 1,
      'message': 'Success',
      'data': requestBody ?? {}
    };
  }

  /// Handle local PUT requests
  Future<dynamic> _handleLocalPut(String subUrl, Map<String, dynamic>? requestBody, Map<String, String>? queryParams) async {
    await _localDataService.initialize();

    // Default success response for PUT
    return {
      'status': 1,
      'message': 'Updated successfully',
      'data': requestBody ?? {}
    };
  }

  /// Handle login authentication
  Future<dynamic> _handleLogin(Map<String, String>? queryParams) async {
    final usersData = await _localDataService.loadJsonData('users.json');
    final users = usersData['users'] as List;

    // For demo purposes, return the first user as authenticated
    if (users.isNotEmpty) {
      return {
        'status': 1,
        'message': 'Login successful',
        'response': users[0]
      };
    }

    return {
      'status': 0,
      'message': 'Login failed'
    };
  }

  /// Handle sync requests
  Future<dynamic> _handleSync(Map<String, String>? queryParams) async {
    final syncData = await _localDataService.loadJsonData('workspaces.json');
    return {
      'status': 1,
      'message': 'Sync successful',
      'result': syncData
    };
  }

  /// Handle sync POST requests
  Future<dynamic> _handleSyncPost(Map<String, dynamic>? requestBody, Map<String, String>? queryParams) async {
    // For sync POST, return success with empty commits
    return {
      'status': 1,
      'message': 'Sync completed',
      'result': {
        'commits': [],
        'pageCount': 0
      }
    };
  }

  /// Handle get sales requests
  Future<dynamic> _handleGetSales(Map<String, String>? queryParams) async {
    final salesData = await _localDataService.loadJsonData('sales.json');
    return {
      'status': 1,
      'message': 'Success',
      'result': salesData['sales']
    };
  }

  /// Handle get subscription plans
  Future<dynamic> _handleGetSubscriptionPlans() async {
    final subscriptionData = await _localDataService.loadJsonData('subscriptions.json');
    return {
      'status': 1,
      'message': 'Success',
      'result': subscriptionData['subscriptionPlans']
    };
  }

  /// Handle get roles
  Future<dynamic> _handleGetRoles() async {
    final rolesData = await _localDataService.loadJsonData('roles.json');
    return {
      'status': 1,
      'message': 'Success',
      'result': rolesData['roles']
    };
  }

  /// Handle get permissions
  Future<dynamic> _handleGetPermissions() async {
    final permissionsData = await _localDataService.loadJsonData('permissions.json');
    return {
      'status': 1,
      'message': 'Success',
      'result': permissionsData['permissions']
    };
  }

  /// Handle get payment categories
  Future<dynamic> _handleGetPaymentCategories() async {
    final paymentData = await _localDataService.loadJsonData('payment_types.json');
    return {
      'status': 1,
      'message': 'Success',
      'result': paymentData['paymentCategories']
    };
  }

  /// Handle user registration
  Future<dynamic> _handleUserRegistration(Map<String, dynamic>? requestBody) async {
    return {
      'status': 1,
      'message': 'Registration successful',
      'response': {
        'username': requestBody?['username'] ?? '<EMAIL>',
        'status': 1,
        'message': 'User registered successfully'
      }
    };
  }

  /// Handle add workspace
  Future<dynamic> _handleAddWorkspace(Map<String, dynamic>? requestBody) async {
    return {
      'status': 1,
      'message': 'Workspace added successfully',
      'response': requestBody
    };
  }
}
