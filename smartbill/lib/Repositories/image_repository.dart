// ignore_for_file: deprecated_member_use, unnecessary_brace_in_string_interps

import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/utils/common_function/fn_utilities.dart';
import '../main.dart';
import 'package:dio/dio.dart';
import '../utils/constants/strings_keys.dart';

FnUtilities fnUtilities = new FnUtilities();

Future uploadMultipleImages(List images, String category) async {
  try {
    var dio = Dio();
    String baseImageURl =
        await fnUtilities.getGeneralSetValue("FileManagerUrl");
    String bucketName = await fnUtilities.getGeneralSetValue("BucketName");
    // var url =
    //     "$baseUrl/api/v4/imageupload/uploadimages?workspaceID=${MyApp.activeWorkspace.workspaceId}&category=$category";
    var url = "$baseImageURl/api/filemanager/upload/";
    List<MultipartFile> fileList = [];

    for (var file in images) {
      String fileName = file.path.split("/").last;
      fileList.add(await MultipartFile.fromFile(file.path, filename: fileName));
    }

    var formData = FormData.fromMap({
      'files': fileList,
      'workspaceID': MyApp.activeWorkspace.workspaceId,
      'category': category,
      'bucketName': bucketName
    });
    var response = await dio.post(
      url,
      data: formData,
      options: Options(
        headers: {
          'authorization': '${MyApp.activeUser.token}',
          'Content-Type': 'multipart/form-data'
        },
      ),
    );
    // if (response.statusCode == 200) {
    //   print(response.data);
    // }
  } on Exception catch (e) {
    throw e;
  }
}

Future<int> downloadImage(imageId, category, {String? imageFor}) async {
  String baseImageURl = await fnUtilities.getGeneralSetValue("FileManagerUrl");
  String bucketName = await fnUtilities.getGeneralSetValue("BucketName");

  try {
    Dio dio = new Dio();
    // var url =
    //     '$baseUrl/assets/workspaces/${MyApp.activeWorkspace.workspaceId}/$category/$imageId.png';
    dynamic url;
    if (imageFor == applogo) {
      url =
          '$baseImageURl/api/filemanager/downloadImage/?category=$category&bucketName=smart-bill&filename=${imageId}.png';
    } else {
      url =
          '$baseImageURl/api/filemanager/download?workspaceID=${MyApp.activeWorkspace.workspaceId}&category=$category&bucketName=$bucketName&filename=${imageId}.png';
    }
    final String saveDirectory =
        (await getApplicationDocumentsDirectory()).path;

    String savePath = '${saveDirectory}/${imageId}.png';

    Response response = await dio.get(
      url,
      onReceiveProgress: showDownloadProgress,
      //Received data with List<int>
      options: Options(
          responseType: ResponseType.bytes,
          followRedirects: true,
          validateStatus: (status) {
            return status! < 500;
          }),
    );
    if (response.data.length > 0) {
      File file = File(savePath);
      var raf = file.openSync(mode: FileMode.write);
      raf.writeFromSync(response.data);
      await raf.close();
      return 1;
    }
    return 0;
  } catch (e) {
    //print(e);
    return 0;
  }
}

void showDownloadProgress(received, total) {
  if (total != -1) {
    //print((received / total * 100).toStringAsFixed(0) + "%");
  }
}
