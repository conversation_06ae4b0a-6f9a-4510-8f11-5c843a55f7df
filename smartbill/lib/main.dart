import 'dart:async';
import 'dart:io';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:month_year_picker/month_year_picker.dart';
import 'package:nb_utils/nb_utils.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'package:smartbill/model/user_model.dart';
import 'package:smartbill/model/workspace.dart';
import 'package:smartbill/screens/navigators/bill_dashboard.dart';
import 'package:smartbill/screens/navigators/bottom_bar.dart';
import 'package:smartbill/screens/navigators/home_settings.dart';
import 'package:smartbill/utils/common_function/background_sync.dart';
import 'package:smartbill/utils/dbutils/db_crud_script.dart';
import 'package:smartbill/utils/dbutils/db_strings.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'screens/users/profile.dart';
import 'screens/users/sign_in.dart';
import 'screens/users/splash_screen.dart';
import 'utils/dbutils/common_db_script.dart';
import 'utils/common_function/fn_utilities.dart';
import 'utils/dbutils/database_helper.dart';
import 'utils/error_logs/error_logs_function.dart';
import 'utils/local_notifications/local_notification_service.dart';
import 'utils/push_notifications/firebase_mesage_service.dart';

final GlobalKey<ScaffoldMessengerState> snackbarKey = GlobalKey<ScaffoldMessengerState>();
final GlobalKey<NavigatorState> navigatorKey1 = GlobalKey<NavigatorState>();

typedef ParentFunctionCallback = void Function(int value);
typedef FunctionCallback2Param = void Function(dynamic value1, dynamic value2);
typedef FunctionCallbackParam = void Function(
  dynamic value,
);
typedef FunctionCallback4Param = void Function(
  dynamic valueOne,
  dynamic valueTwo,
  dynamic valueThree,
  dynamic valueFour,
);
typedef FunctionCallback5Param = void Function(
  dynamic valueOne,
  dynamic valueTwo,
  dynamic valueThree,
  dynamic valueFour,
  dynamic valueFive,
);
typedef FunctionCallback3Param = void Function(
  dynamic valueOne,
  dynamic valueTwo,
  dynamic valueThree,
);
void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();
    if (!Platform.isWindows) {
      // FCM.initFireBase();
      // await Firebase.initializeApp();
    }
    LocalNotification.initLocalNotification();

    await databaseInitialization();
    runApp(MyApp());
  } on Exception catch (ex) {
    ErrorLogsFunction.insertErrorLogs(ex, "Main Function");
  }
}

class MyApp extends StatefulWidget {
  static int shopCode = 1;
  static DateTime? expiryDate;
  static String? deviceId;
  static String? appTempToken;
  static bool isNumberPanel = false;
  static bool isNamePanel = false;
  static bool isFilterEnable = false;
  static bool isListViewEnable = false;
  static int versionCode = 20;
  static String androidVersionCode = "1.2.0";
  static String iosVersionCode = "1.1.8";
  static String windowsVersionCode = "1.0.8";

  static WorkSpace activeWorkspace = WorkSpace();
  static User activeUser = User();
  static String workspaceSyncTime = "1";
  static bool? isDemoAccount;

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Timer? timer;

  @override
  void initState() {
    startBackSync();
    // notificationFunction();
    deleteOldSales();
    super.initState();
  }

  // notificationFunction() async {
  // WidgetsFlutterBinding.ensureInitialized();
  //Initialization Settings for Android
  // final AndroidInitializationSettings initializationSettingsAndroid = AndroidInitializationSettings('@mipmap/ic_launcher');

  //   //Initialization Settings for iOS
  //   final IOSInitializationSettings initializationSettingsIOS = IOSInitializationSettings(
  //     requestSoundPermission: false,
  //     requestBadgePermission: false,
  //     requestAlertPermission: false,
  //   );
  //   Future<dynamic> onSelectNotification(payload) async {
  //     await OpenFilex.open(payload);
  //   }

  // //InitializationSettings for initializing settings for both platforms (Android & iOS)
  //   final InitializationSettings initializationSettings =
  //       InitializationSettings(android: initializationSettingsAndroid, iOS: initializationSettingsIOS);

  //   await flutterLocalNotificationsPlugin.initialize(initializationSettings, onSelectNotification: onSelectNotification);
  // }

  initSyncTimer() async {
    int syncTime = 1;
    FnUtilities fnUtilities = FnUtilities();
    MyApp.workspaceSyncTime = await fnUtilities.getWorkspaceSetValue("SyncTime") ?? "";

    if (!MyApp.workspaceSyncTime.isEmptyOrNull) {
      syncTime = int.tryParse(MyApp.workspaceSyncTime) ?? 1;
    }
    timer = Timer.periodic(Duration(minutes: syncTime), (Timer t) => checkTimer());
  }

  checkTimer() {
    if (!isSyncProgress) {
      BackGroundSync.startBackTasks('com.cloudstier.autosync', isPullSync: true);
    }
  }

  startBackSync() async {
    await initSyncTimer();
    await BackGroundSync.initTasks();
    BackGroundSync.enableorDisableBackTask(true);
  }

  deleteOldSales() {
    CommonDB commonDB = new CommonDB();
    commonDB.deleteAllSalesBefDays(2);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'NembooBill',
      theme: ThemeData(
        useMaterial3: false,
        primarySwatch: Colors.deepPurple,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      debugShowCheckedModeBanner: false,
      scaffoldMessengerKey: snackbarKey,
      navigatorKey: navigatorKey1,
      //home: Dashboard(),
      initialRoute: 'splash',
      localizationsDelegates: [
        // GlobalMaterialLocalizations.delegate,
        MonthYearPickerLocalizations.delegate,
      ],
      routes: {
        // When navigating to the "/" route, build the FirstScreen widget.
        // '/home': MaterialPageRoute(builder: (context) => Dashboard()),
        // When navigating to the "/second" route, build the SecondScreen widget.
        'bottombar': (context) => BottomBar(),
        // 'home': (context) => BottomBar(),
        'billdashboard': (context) => BillDashBoard(),
        'profile': (context) => ProfilePage(),
        'signIn': (context) => SignInPage(),
        'splash': (context) => SplashScreen(),
        'settings': (context) => HomeSettings(),
      },
    );
  }
}

databaseInitialization() async {
  // DatabaseHelper dbHelper = DatabaseHelper.instance;
  if (Platform.isWindows) {
    /// For Windows
    sqfliteFfiInit();
    var databaseFactory = databaseFactoryFfi;
    final Directory appDocumentsDir = await getApplicationDocumentsDirectory();
    String path = join(appDocumentsDir.path, "databases", db_name);
    Database db = await databaseFactory.openDatabase(
      path,
    );
    var isExists;
    await databaseFactory.databaseExists(db.path).then((value) {
      isExists = value;
    });
    //if (!isExists) {
    await DatabaseHelper.executeDBScript();
    //}
    return db;
  }
}
