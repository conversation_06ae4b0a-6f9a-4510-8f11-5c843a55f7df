buildscript {
    ext.kotlin_version = '1.7.10'
    ext{
        compileSdkVersion = 34
        targetSdkVersion = 33
        supportLibVersion = "1.6.1"
    }
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:7.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
        // classpath 'com.google.protobuf:protobuf-gradle-plugin:0.8.12'
    }
}

allprojects {
    repositories {
        google()
        maven {
            // [required] background_fetch
            url "${project(':background_fetch').projectDir}/libs"
        }
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}

configurations.all {
    resolutionStrategy {
        force 'androidx.core:core-ktx:1.6.0'
    }
}
