# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
/build/
pubspec.lock

# Web related
lib/generated_plugin_registrant.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# ios/XCode related
**/ios/**/Pods/
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Runner/GeneratedPluginRegistrant.*
**/ios/Flutter/flutter_export_environment.sh
**/ios/Flutter/Generated.xcconfig
**/ios/Podfile.lock
**/ios/Runner.xcodeproj/project.pbxproj
**/ios/Runner.xcworkspace/contents.xcworkspacedata

# FVM Version Cache
.fvm/